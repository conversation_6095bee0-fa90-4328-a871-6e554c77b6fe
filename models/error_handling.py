"""
Models for error handling feature
"""
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from api.utils.validators import CommonValidators

class ErrorHandlingBase(BaseModel):
    """Base error handling model"""
    name: str = Field(..., description="Name of the error handler")
    description: Optional[str] = Field(None, description="Description of the error handler")
    error_code: Optional[str] = Field(None, description="Error code for identifying the error")
    error_type: str = Field(..., description="Type of error (validation, authorization, etc.)")
    error_message: str = Field(..., description="Error message to display")
    is_user_facing: bool = Field(True, description="Whether this error should be shown to users")
    http_status_code: Optional[int] = Field(None, description="HTTP status code for this error")
    severity: Optional[str] = Field(None, description="Severity level of the error (low, medium, high, critical)")

class ErrorHandlingCreate(ErrorHandlingBase):
    """Error handling creation model"""
    pass

class ErrorHandlingUpdate(BaseModel):
    """Error handling update model"""
    name: Optional[str] = Field(None, description="Name of the error handler")
    description: Optional[str] = Field(None, description="Description of the error handler")
    error_code: Optional[str] = Field(None, description="Error code for identifying the error")
    error_type: Optional[str] = Field(None, description="Type of error (validation, authorization, etc.)")
    error_message: Optional[str] = Field(None, description="Error message to display")
    is_user_facing: Optional[bool] = Field(None, description="Whether this error should be shown to users")
    http_status_code: Optional[int] = Field(None, description="HTTP status code for this error")
    severity: Optional[str] = Field(None, description="Severity level of the error (low, medium, high, critical)")

class ErrorHandlingModel(ErrorHandlingBase):
    """Error handling response model"""
    id: int = Field(..., description="Unique identifier for the error handler")
    created_at: datetime = Field(..., description="Timestamp when the error handler was created")
    updated_at: Optional[datetime] = Field(None, description="Timestamp when the error handler was last updated")
    
    class Config:
        orm_mode = True
