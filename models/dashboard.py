"""
Models for dashboard feature
"""
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from api.utils.validators import CommonValidators

class DashboardBase(BaseModel):
    """Base dashboard model"""
    name: str = Field(..., description="Name of the dashboard")
    description: Optional[str] = Field(None, description="Description of the dashboard")

class DashboardCreate(DashboardBase, CommonValidators):
    """Dashboard creation model"""
    pass

class DashboardUpdate(DashboardBase):
    """Dashboard update model"""
    name: Optional[str] = Field(None, description="Name of the dashboard")

class DashboardModel(DashboardBase):
    """Dashboard response model"""
    id: int = Field(..., description="Unique identifier for the dashboard")
    created_at: datetime = Field(..., description="Timestamp when the dashboard was created")
    updated_at: Optional[datetime] = Field(None, description="Timestamp when the dashboard was last updated")
    
    class Config:
        orm_mode = True
