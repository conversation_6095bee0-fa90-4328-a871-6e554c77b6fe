"""
Pydantic models for the comprehensive tagging system API.

This module defines the request and response models for the tagging system API endpoints.
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
import re

# Tag Category Models
class TagCategoryBase(BaseModel):
    """Base model for tag categories"""
    name: str = Field(..., description="Name of the category", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="Description of the category")
    color: Optional[str] = Field(None, description="Color code for UI display (hex or named color)")
    icon: Optional[str] = Field(None, description="Icon name for UI display")

    @validator('color')
    def validate_color(cls, v):
        """Validate that color is a valid hex code or named color"""
        if v is None:
            return v
        # Check if it's a hex color code
        if re.match(r'^#(?:[0-9a-fA-F]{3}){1,2}$', v):
            return v
        # Check if it's a named color (simplified validation)
        if re.match(r'^[a-zA-Z]+(?:-[a-zA-Z]+)*$', v):
            return v
        raise ValueError("Color must be a valid hex code or named color")


class TagCategoryCreate(TagCategoryBase):
    """Model for creating a tag category"""
    pass


class TagCategoryUpdate(BaseModel):
    """Model for updating a tag category"""
    name: Optional[str] = Field(None, description="Name of the category", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="Description of the category")
    color: Optional[str] = Field(None, description="Color code for UI display (hex or named color)")
    icon: Optional[str] = Field(None, description="Icon name for UI display")

    @validator('color')
    def validate_color(cls, v):
        """Validate that color is a valid hex code or named color"""
        if v is None:
            return v
        # Check if it's a hex color code
        if re.match(r'^#(?:[0-9a-fA-F]{3}){1,2}$', v):
            return v
        # Check if it's a named color (simplified validation)
        if re.match(r'^[a-zA-Z]+(?:-[a-zA-Z]+)*$', v):
            return v
        raise ValueError("Color must be a valid hex code or named color")


class TagCategoryResponse(TagCategoryBase):
    """Response model for tag categories"""
    id: int = Field(..., description="Unique identifier for the category")
    is_system: bool = Field(..., description="Whether this is a system-defined category")
    created_at: datetime = Field(..., description="Timestamp when the category was created")
    updated_at: Optional[datetime] = Field(None, description="Timestamp when the category was last updated")
    tag_count: Optional[int] = Field(None, description="Number of tags in this category")

    class Config:
        from_attributes = True


# Tag Models
class TagBase(BaseModel):
    """Base model for tags"""
    name: str = Field(..., description="Name of the tag", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="Description of the tag")
    category_id: Optional[int] = Field(None, description="ID of the category this tag belongs to")

    @validator('name')
    def validate_name(cls, v):
        """Validate that name doesn't contain special characters"""
        if not re.match(r'^[a-zA-Z0-9\s\-_]+$', v):
            raise ValueError("Tag name can only contain alphanumeric characters, spaces, hyphens, and underscores")
        return v


class TagCreate(TagBase):
    """Model for creating a tag"""
    pass


class TagUpdate(BaseModel):
    """Model for updating a tag"""
    name: Optional[str] = Field(None, description="Name of the tag", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="Description of the tag")
    category_id: Optional[int] = Field(None, description="ID of the category this tag belongs to")

    @validator('name')
    def validate_name(cls, v):
        """Validate that name doesn't contain special characters"""
        if v is None:
            return v
        if not re.match(r'^[a-zA-Z0-9\s\-_]+$', v):
            raise ValueError("Tag name can only contain alphanumeric characters, spaces, hyphens, and underscores")
        return v


class TagResponse(BaseModel):
    """Response model for tags"""
    id: int = Field(..., description="Unique identifier for the tag")
    name: str = Field(..., description="Name of the tag")
    description: Optional[str] = Field(None, description="Description of the tag")
    color: str = Field(..., description="Color code for UI display")
    category_id: Optional[int] = Field(None, description="ID of the category this tag belongs to")
    created_at: datetime = Field(..., description="When the tag was created")
    updated_at: Optional[datetime] = Field(None, description="When the tag was last updated")
    created_by_id: Optional[int] = Field(None, description="ID of the user who created the tag")
    updated_by_id: Optional[int] = Field(None, description="ID of the user who last updated the tag")
    
    class Config:
        orm_mode = True


# Tag Assignment Models
class TagAssignmentBase(BaseModel):
    """Base model for tag assignments"""
    tag_id: int = Field(..., description="ID of the tag to assign")
    resource_type: str = Field(..., description="Type of resource to tag (e.g., 'campaign', 'test_case')")
    resource_id: int = Field(..., description="ID of the resource to tag")


class TagAssignmentCreate(TagAssignmentBase):
    """Model for creating a tag assignment"""
    pass


class TagAssignmentResponse(TagAssignmentBase):
    """Response model for tag assignments"""
    tag: TagResponse = Field(..., description="The assigned tag")
    created_by: Optional[int] = Field(None, description="ID of the user who created the assignment")
    created_at: datetime = Field(..., description="Timestamp when the assignment was created")

    class Config:
        from_attributes = True


# Tag Relation Models
class TagRelationBase(BaseModel):
    """Base model for tag relations"""
    tag_id: int = Field(..., description="ID of the primary tag")
    related_tag_id: int = Field(..., description="ID of the related tag")
    relation_type: Optional[str] = Field(None, description="Type of relation (e.g., 'similar', 'opposite')")


class TagRelationCreate(BaseModel):
    """Model for creating a tag relation"""
    tag_id: int = Field(..., description="ID of the first tag")
    related_tag_id: int = Field(..., description="ID of the related tag")
    relation_type: Optional[str] = Field(None, description="Type of relation between the tags")


class TagRelationResponse(BaseModel):
    """Response model for tag relations"""
    tag_id: int = Field(..., description="ID of the first tag")
    tag_name: str = Field(..., description="Name of the first tag")
    related_tag_id: int = Field(..., description="ID of the related tag")
    related_tag_name: str = Field(..., description="Name of the related tag")
    relation_type: Optional[str] = Field(None, description="Type of relation between the tags")
    created_at: datetime = Field(..., description="When the relation was created")
    
    class Config:
        orm_mode = True


# Tag Hierarchy Models
class TagHierarchyCreate(BaseModel):
    """Model for creating a tag hierarchy (parent-child relationship)"""
    parent_id: int = Field(..., description="ID of the parent tag")
    child_id: int = Field(..., description="ID of the child tag")


class TagHierarchyResponse(BaseModel):
    """Response model for tag hierarchies"""
    parent_id: int = Field(..., description="ID of the parent tag")
    parent_name: str = Field(..., description="Name of the parent tag")
    child_id: int = Field(..., description="ID of the child tag")
    child_name: str = Field(..., description="Name of the child tag")
    
    class Config:
        orm_mode = True


# Tag Audit Log Models
class TagAuditLogResponse(BaseModel):
    """Response model for tag audit logs"""
    id: int = Field(..., description="Unique identifier for the audit log")
    user_id: int = Field(..., description="ID of the user who performed the action")
    action: str = Field(..., description="Type of action performed")
    tag_id: Optional[int] = Field(None, description="ID of the tag affected")
    resource_type: Optional[str] = Field(None, description="Type of resource affected")
    resource_id: Optional[int] = Field(None, description="ID of the resource affected")
    details: Optional[str] = Field(None, description="Additional details about the action")
    created_at: datetime = Field(..., description="Timestamp when the action occurred")
    tag: Optional[TagResponse] = Field(None, description="The affected tag")
    user_name: Optional[str] = Field(None, description="Name of the user who performed the action")

    class Config:
        from_attributes = True


# Bulk Operations Models
class BulkTagAssignment(BaseModel):
    """Model for bulk tag assignments"""
    tag_ids: List[int] = Field(..., description="IDs of the tags to assign")
    resource_type: str = Field(..., description="Type of resource to tag")
    resource_ids: List[int] = Field(..., description="IDs of the resources to tag")


class BulkTagRemoval(BaseModel):
    """Model for bulk tag removals"""
    tag_ids: List[int] = Field(..., description="IDs of the tags to remove")
    resource_type: str = Field(..., description="Type of resource to untag")
    resource_ids: List[int] = Field(..., description="IDs of the resources to untag")


# Search and Filter Models
class TagSearchParams(BaseModel):
    """Model for tag search parameters"""
    query: Optional[str] = Field(None, description="Search query for tag name or description")
    category_id: Optional[int] = Field(None, description="Filter by category ID")
    created_by: Optional[int] = Field(None, description="Filter by creator user ID")
    is_system: Optional[bool] = Field(None, description="Filter by system tag status")
    resource_type: Optional[str] = Field(None, description="Filter by resource type")
    resource_id: Optional[int] = Field(None, description="Filter by resource ID")


# Statistics Models
class TagStatistics(BaseModel):
    """Model for tag usage statistics"""
    total_tags: int = Field(..., description="Total number of tags")
    total_categories: int = Field(..., description="Total number of categories")
    most_used_tags: List[Dict[str, Any]] = Field(..., description="Most frequently used tags")
    recent_tags: List[Dict[str, Any]] = Field(..., description="Recently created tags")
    tag_counts_by_category: Dict[str, int] = Field(..., description="Tag counts by category")
    tag_counts_by_resource_type: Dict[str, int] = Field(..., description="Tag usage by resource type")


# Tag Resource Association Models
class ResourceIdentifier(BaseModel):
    """Model for identifying a resource"""
    resource_type: str = Field(..., description="Type of the resource (e.g., 'test_case', 'campaign')")
    resource_id: int = Field(..., description="ID of the resource")


class TagResourceAssociation(BaseModel):
    """Model for associating a tag with a resource"""
    tag_id: int = Field(..., description="ID of the tag")
    resource_type: str = Field(..., description="Type of the resource (e.g., 'test_case', 'campaign')")
    resource_id: int = Field(..., description="ID of the resource")


class TagResourceResponse(BaseModel):
    """Response model for tag-resource associations"""
    tag_id: int = Field(..., description="ID of the tag")
    tag_name: str = Field(..., description="Name of the tag")
    tag_color: str = Field(..., description="Color of the tag")
    resource_type: str = Field(..., description="Type of the resource")
    resource_id: int = Field(..., description="ID of the resource")
    created_at: datetime = Field(..., description="When the association was created")
    created_by: Optional[int] = Field(None, description="ID of the user who created the association")
    
    class Config:
        orm_mode = True


# Bulk Operation Models
class TagBulkOperation(BaseModel):
    """Model for bulk operations on tags"""
    operation_type: str = Field(..., description="Type of operation ('associate' or 'dissociate')")
    tag_ids: List[int] = Field(..., description="List of tag IDs to operate on")
    resources: List[ResourceIdentifier] = Field(..., description="List of resources to operate on")

    @validator('operation_type')
    def validate_operation_type(cls, v):
        """Validate that operation_type is supported"""
        if v not in ['associate', 'dissociate']:
            raise ValueError("Operation type must be 'associate' or 'dissociate'")
        return v


# Tag Analytics Models
class TagUsage(BaseModel):
    """Model for tag usage statistics"""
    id: int = Field(..., description="Tag ID")
    name: str = Field(..., description="Tag name")
    usage_count: int = Field(..., description="Number of resources using this tag")


class ResourceTypeStats(BaseModel):
    """Model for resource type statistics"""
    resource_type: str = Field(..., description="Type of resource")
    resource_count: int = Field(..., description="Number of resources of this type")
    tag_count: int = Field(..., description="Number of tag associations for this resource type")


class TagAnalytics(BaseModel):
    """Model for tag analytics data"""
    total_tags: int = Field(..., description="Total number of tags in the system")
    tags_by_category: Dict[str, int] = Field(..., description="Number of tags by category")
    top_tags: List[TagUsage] = Field(..., description="Top used tags")
    resource_types: List[ResourceTypeStats] = Field(..., description="Statistics by resource type")


# Tag Propagation Models
class TagPropagationRule(BaseModel):
    """Model for tag propagation rules"""
    source_type: str = Field(..., description="Source resource type")
    target_type: str = Field(..., description="Target resource type")
    relation_field: str = Field(..., description="Field that relates source to target")
    is_active: bool = Field(True, description="Whether this rule is active")
    description: Optional[str] = Field(None, description="Description of the rule")


class TagPropagationRuleCreate(TagPropagationRule):
    """Model for creating a tag propagation rule"""
    pass


class TagPropagationRuleResponse(TagPropagationRule):
    """Response model for tag propagation rules"""
    id: int = Field(..., description="Unique identifier for the rule")
    created_at: datetime = Field(..., description="When the rule was created")
    updated_at: Optional[datetime] = Field(None, description="When the rule was last updated")
    created_by_id: Optional[int] = Field(None, description="ID of the user who created the rule")
    
    class Config:
        orm_mode = True 