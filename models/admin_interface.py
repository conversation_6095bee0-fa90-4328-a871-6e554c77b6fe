"""
Models for admin interface feature
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from api.utils.validators import CommonValidators

# Admin Settings Models
class AdminSettingBase(BaseModel):
    """Base admin setting model"""
    key: str = Field(..., description="Unique key for the setting")
    value: Optional[str] = Field(None, description="Value of the setting")
    description: Optional[str] = Field(None, description="Description of the setting")
    category: str = Field(..., description="Category of the setting (e.g., 'security', 'display', 'notification')")
    is_editable: bool = Field(True, description="Whether the setting can be edited by admins")

class AdminSettingCreate(AdminSettingBase):
    """Admin setting creation model"""
    pass

class AdminSettingUpdate(BaseModel):
    """Admin setting update model"""
    value: Optional[str] = Field(None, description="Value of the setting")
    description: Optional[str] = Field(None, description="Description of the setting")
    category: Optional[str] = Field(None, description="Category of the setting")
    is_editable: Optional[bool] = Field(None, description="Whether the setting can be edited by admins")

class AdminSettingModel(AdminSettingBase):
    """Admin setting response model"""
    id: int = Field(..., description="Unique identifier for the setting")
    created_at: datetime = Field(..., description="Timestamp when the setting was created")
    updated_at: Optional[datetime] = Field(None, description="Timestamp when the setting was last updated")
    
    class Config:
        orm_mode = True

# Admin Audit Log Models
class AdminAuditLogBase(BaseModel):
    """Base admin audit log model"""
    user_id: int = Field(..., description="ID of the user who performed the action")
    action: str = Field(..., description="Action performed (e.g., 'create', 'update', 'delete')")
    resource_type: str = Field(..., description="Type of resource affected (e.g., 'user', 'setting')")
    resource_id: Optional[str] = Field(None, description="ID of the resource affected")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional details about the action")
    ip_address: Optional[str] = Field(None, description="IP address of the user")
    user_agent: Optional[str] = Field(None, description="User agent of the user's browser")

class AdminAuditLogCreate(AdminAuditLogBase):
    """Admin audit log creation model"""
    pass

class AdminAuditLogModel(AdminAuditLogBase):
    """Admin audit log response model"""
    id: int = Field(..., description="Unique identifier for the audit log")
    created_at: datetime = Field(..., description="Timestamp when the audit log was created")
    
    class Config:
        orm_mode = True

# For backward compatibility with existing code
AuditLogBase = AdminAuditLogBase
AuditLogCreate = AdminAuditLogCreate
AuditLogModel = AdminAuditLogModel

# System Configuration Models
class SystemConfigurationBase(BaseModel):
    """Base system configuration model"""
    name: str = Field(..., description="Name of the configuration")
    value: Optional[str] = Field(None, description="Value of the configuration")
    description: Optional[str] = Field(None, description="Description of the configuration")
    is_encrypted: bool = Field(False, description="Whether the value is encrypted")

class SystemConfigurationCreate(SystemConfigurationBase):
    """System configuration creation model"""
    pass

class SystemConfigurationUpdate(BaseModel):
    """System configuration update model"""
    value: Optional[str] = Field(None, description="Value of the configuration")
    description: Optional[str] = Field(None, description="Description of the configuration")
    is_encrypted: Optional[bool] = Field(None, description="Whether the value is encrypted")

class SystemConfigurationModel(SystemConfigurationBase):
    """System configuration response model"""
    id: int = Field(..., description="Unique identifier for the configuration")
    created_at: datetime = Field(..., description="Timestamp when the configuration was created")
    updated_at: Optional[datetime] = Field(None, description="Timestamp when the configuration was last updated")
    
    class Config:
        orm_mode = True

# Admin Dashboard Widget Models
class AdminDashboardWidgetBase(BaseModel):
    """Base admin dashboard widget model"""
    name: str = Field(..., description="Name of the widget")
    widget_type: str = Field(..., description="Type of widget (e.g., 'chart', 'table', 'stats')")
    position: int = Field(..., description="Position of the widget on the dashboard")
    size: str = Field("medium", description="Size of the widget (small, medium, large)")
    config: Optional[Dict[str, Any]] = Field(None, description="Configuration for the widget")
    is_enabled: bool = Field(True, description="Whether the widget is enabled")

    @validator('size')
    def validate_size(cls, v):
        """Validate that size is one of the allowed values"""
        allowed_sizes = ['small', 'medium', 'large']
        if v not in allowed_sizes:
            raise ValueError(f"Size must be one of {allowed_sizes}")
        return v

    @validator('widget_type')
    def validate_widget_type(cls, v):
        """Validate that widget_type is one of the allowed values"""
        allowed_types = ['chart', 'table', 'stats', 'list', 'map', 'custom']
        if v not in allowed_types:
            raise ValueError(f"Widget type must be one of {allowed_types}")
        return v

class AdminDashboardWidgetCreate(AdminDashboardWidgetBase):
    """Admin dashboard widget creation model"""
    pass

class AdminDashboardWidgetUpdate(BaseModel):
    """Admin dashboard widget update model"""
    name: Optional[str] = Field(None, description="Name of the widget")
    widget_type: Optional[str] = Field(None, description="Type of widget")
    position: Optional[int] = Field(None, description="Position of the widget on the dashboard")
    size: Optional[str] = Field(None, description="Size of the widget")
    config: Optional[Dict[str, Any]] = Field(None, description="Configuration for the widget")
    is_enabled: Optional[bool] = Field(None, description="Whether the widget is enabled")

    @validator('size')
    def validate_size(cls, v):
        """Validate that size is one of the allowed values"""
        if v is None:
            return v
        allowed_sizes = ['small', 'medium', 'large']
        if v not in allowed_sizes:
            raise ValueError(f"Size must be one of {allowed_sizes}")
        return v

    @validator('widget_type')
    def validate_widget_type(cls, v):
        """Validate that widget_type is one of the allowed values"""
        if v is None:
            return v
        allowed_types = ['chart', 'table', 'stats', 'list', 'map', 'custom']
        if v not in allowed_types:
            raise ValueError(f"Widget type must be one of {allowed_types}")
        return v

class AdminDashboardWidgetModel(AdminDashboardWidgetBase):
    """Admin dashboard widget response model"""
    id: int = Field(..., description="Unique identifier for the widget")
    created_at: datetime = Field(..., description="Timestamp when the widget was created")
    updated_at: Optional[datetime] = Field(None, description="Timestamp when the widget was last updated")
    
    class Config:
        orm_mode = True

# Admin Notification Models
class AdminNotificationBase(BaseModel):
    """Base admin notification model"""
    title: str = Field(..., description="Title of the notification")
    message: str = Field(..., description="Message content of the notification")
    severity: str = Field("info", description="Severity level of the notification (info, warning, error, critical)")
    user_id: Optional[int] = Field(None, description="ID of the user the notification is for (null for all admins)")

    @validator('severity')
    def validate_severity(cls, v):
        """Validate that severity is one of the allowed values"""
        allowed_severities = ['info', 'warning', 'error', 'critical']
        if v not in allowed_severities:
            raise ValueError(f"Severity must be one of {allowed_severities}")
        return v

class AdminNotificationCreate(AdminNotificationBase):
    """Admin notification creation model"""
    pass

class AdminNotificationUpdate(BaseModel):
    """Admin notification update model"""
    is_read: Optional[bool] = Field(None, description="Whether the notification has been read")

class AdminNotificationModel(AdminNotificationBase):
    """Admin notification response model"""
    id: int = Field(..., description="Unique identifier for the notification")
    is_read: bool = Field(..., description="Whether the notification has been read")
    created_at: datetime = Field(..., description="Timestamp when the notification was created")
    
    class Config:
        orm_mode = True

# Admin Dashboard Models
class AdminDashboardStats(BaseModel):
    """Admin dashboard statistics model"""
    total_users: int = Field(..., description="Total number of users")
    active_users: int = Field(..., description="Number of active users")
    total_sessions: int = Field(..., description="Total number of user sessions")
    active_sessions: int = Field(..., description="Number of active user sessions")
    system_uptime: str = Field(..., description="System uptime in human-readable format")
    database_size: str = Field(..., description="Database size in human-readable format")
    error_count_24h: int = Field(..., description="Number of errors in the last 24 hours")
    api_requests_24h: int = Field(..., description="Number of API requests in the last 24 hours")

class AdminDashboardModel(BaseModel):
    """Admin dashboard response model"""
    stats: AdminDashboardStats = Field(..., description="Dashboard statistics")
    widgets: List[AdminDashboardWidgetModel] = Field(..., description="Dashboard widgets")
    notifications: List[AdminNotificationModel] = Field(..., description="Admin notifications")
    recent_audit_logs: List[AdminAuditLogModel] = Field(..., description="Recent audit logs")
