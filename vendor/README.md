# Vendor Submodules

This directory contains third-party code that is included as Git submodules.

## MITRE ATT&CK Navigator

The [MITRE ATT&CK Navigator](https://github.com/mitre-attack/attack-navigator) is a web-based tool for annotating and exploring ATT&CK matrices. It's included as a submodule in the `attack-navigator` directory.

### Usage

The Navigator is integrated into the Advanced Execution Framework and can be accessed through the frontend dashboard at `/mitre-navigator`. The integration allows you to:

1. Visualize test case coverage of MITRE ATT&CK techniques
2. Visualize campaign coverage of MITRE ATT&CK techniques
3. Compare coverage between different campaigns
4. View coverage across all test cases in the system

### Updating the Submodule

To update the MITRE ATT&CK Navigator to the latest version:

```bash
cd vendor/attack-navigator
git fetch
git checkout <desired-tag-or-branch>
cd ../..
git add vendor/attack-navigator
git commit -m "Update MITRE ATT&CK Navigator to <version>"
```

### API Integration

The Advanced Execution Framework provides the following API endpoints for generating layer files:

- `/api/v1/execution-framework/mitre/navigator/layers/test-case/{id}`: Generate a layer file for a specific test case
- `/api/v1/execution-framework/mitre/navigator/layers/campaign/{id}`: Generate a layer file for a specific campaign
- `/api/v1/execution-framework/mitre/navigator/layers/all`: Generate a layer file for all test cases
- `/api/v1/execution-framework/mitre/navigator/layers/compare`: Generate a comparison layer file for two campaigns

These endpoints return JSON layer files that can be loaded directly into the MITRE ATT&CK Navigator.
