#!/usr/bin/env python
"""
<PERSON>ript to patch the API's database.py file to use Alembic for migrations.
"""
import os
import sys
import re
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def update_database_file():
    """Update the database.py file to use Alembic for migrations."""
    try:
        # First, find the database.py file in the container
        api_database_path = None
        for path in Path("/app").rglob("database.py"):
            if "api" in str(path):
                api_database_path = path
                break
                
        if not api_database_path:
            logger.error("Could not find API database module")
            return False
            
        logger.info(f"Found API database module at {api_database_path}")
        
        # Read the current content
        with open(api_database_path, "r") as f:
            content = f.read()
        
        # Replace the init_db function with one that uses Alembic
        modified_content = re.sub(
            r'def init_db\([^)]*\)[^{]*:[^}]*Base\.metadata\.create_all\([^)]*\)[^}]*',
            """def init_db(*, clean: bool = False) -> None:
    \"\"\"Initialize database with Alembic migrations.

    Args:
        clean: If True, drops all tables before recreating them
    \"\"\"
    try:
        logger.info("Initializing database with Alembic migrations...")

        # Test database connection
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            result.scalar()
            logger.info("Database connection test successful")

        # Run migrations using Alembic
        import subprocess
        import os
        
        alembic_ini = os.path.join(os.path.dirname(__file__), "..", "alembic.ini")
        if os.path.exists(alembic_ini):
            logger.info("Running Alembic migrations...")
            result = subprocess.run(
                ["alembic", "upgrade", "head"],
                capture_output=True,
                text=True,
                check=False
            )
            
            if result.returncode != 0:
                logger.error(f"Alembic migration failed: {result.stderr}")
                # Fall back to SQLAlchemy create_all
                logger.info("Falling back to SQLAlchemy create_all...")
                Base.metadata.create_all(bind=engine, checkfirst=True)
            else:
                logger.info("Alembic migrations completed successfully")
        else:
            logger.warning("Alembic configuration not found, using SQLAlchemy create_all")
            # Fall back to SQLAlchemy create_all
            Base.metadata.create_all(bind=engine, checkfirst=True)
            
        logger.info("Database initialization completed successfully")
    except Exception as e:
        logger.error(f"Database initialization error: {e}")
        logger.error("Stack trace:", exc_info=True)
        raise""",
            content
        )
        
        # Add imports if needed
        if 'import subprocess' not in modified_content:
            modified_content = modified_content.replace('import sys', 'import sys\nimport subprocess')
        
        # Write the modified content back
        with open(api_database_path, "w") as f:
            f.write(modified_content)
            
        logger.info(f"Updated {api_database_path} to use Alembic for migrations")
        return True
    except Exception as e:
        logger.error(f"Error updating database file: {e}")
        return False

if __name__ == "__main__":
    success = update_database_file()
    sys.exit(0 if success else 1) 