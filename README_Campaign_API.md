# Campaign Management API

## Overview

The Campaign Management API is a core component of the RegressionRigor platform, providing functionality for creating, managing, and tracking security testing campaigns. This API enables users to organize security tests and assessments in a structured manner, facilitating comprehensive security testing activities.

## Features

- **Campaign CRUD Operations**: Create, read, update, and delete security testing campaigns
- **Test Case Assignment**: Assign test cases to campaigns and manage these assignments
- **Campaign Summaries**: Generate summaries of campaign progress and results
- **Role-Based Access Control**: Ensure proper authorization for campaign operations
- **Soft Deletion**: Support for soft deletion and restoration of campaigns

## Implementation Details

The Campaign Management API is implemented using FastAPI and follows a layered architecture:

1. **Database Models**: Defined in `api/models/campaign.py`
2. **Pydantic Schemas**: Defined in `api/models/schemas/campaign.py`
3. **Service Layer**: Implemented in `api/services/campaign.py`
4. **API Routes**: Defined in `api/routes/campaign.py`
5. **Database Migrations**: Created in `migrations/versions/20240315_add_campaign_tables.py`

## API Documentation

Detailed API documentation is available in the [Campaign API Documentation](docs/campaign_api.md) file, which includes:

- Endpoint descriptions and examples
- Request and response formats
- Authentication and authorization requirements
- Error handling information

## Testing

The Campaign Management API includes comprehensive test coverage in the `tests/test_campaign_api.py` file, which tests all API endpoints and functionality.

## Usage Examples

### Creating a Campaign

```python
import requests

# Authentication
token = "your_jwt_token"
headers = {"Authorization": f"Bearer {token}"}

# Campaign data
campaign_data = {
    "name": "Q2 Security Assessment",
    "description": "Comprehensive security assessment for Q2",
    "status": "draft",
    "start_date": "2024-04-01T00:00:00",
    "end_date": "2024-06-30T23:59:59"
}

# Create campaign
response = requests.post(
    "https://api.regressionrigor.com/api/v1/campaigns/",
    json=campaign_data,
    headers=headers
)

# Check response
if response.status_code == 201:
    campaign = response.json()
    print(f"Campaign created with ID: {campaign['id']}")
else:
    print(f"Error: {response.json()['detail']}")
```

### Assigning Test Cases to a Campaign

```python
import requests

# Authentication
token = "your_jwt_token"
headers = {"Authorization": f"Bearer {token}"}

# Campaign ID
campaign_id = 1

# Test case assignment data
assignment_data = {
    "test_case_ids": [1, 2, 3]
}

# Assign test cases
response = requests.post(
    f"https://api.regressionrigor.com/api/v1/campaigns/{campaign_id}/test-cases",
    json=assignment_data,
    headers=headers
)

# Check response
if response.status_code == 204:
    print("Test cases assigned successfully")
else:
    print(f"Error: {response.json()['detail']}")
```

## Integration with Other Components

The Campaign Management API integrates with other components of the RegressionRigor platform:

- **Test Case Management**: Campaigns can be associated with test cases
- **Assessment Management**: Assessments can be linked to campaigns
- **User Authentication**: All API endpoints require proper authentication and authorization

## Future Enhancements

Planned enhancements for the Campaign Management API include:

1. **Campaign Templates**: Support for creating campaigns from templates
2. **Advanced Filtering**: Enhanced filtering options for campaign listing
3. **Bulk Operations**: Support for bulk assignment and removal of test cases
4. **Campaign Cloning**: Ability to clone existing campaigns
5. **Reporting Enhancements**: Advanced reporting capabilities for campaigns 