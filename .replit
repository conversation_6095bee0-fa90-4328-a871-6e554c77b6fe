modules = ["python-3.11", "nodejs-20", "python3", "postgresql-16"]

[nix]
channel = "stable-24_05"

[deployment]
deploymentTarget = "autoscale"
run = ["gunicorn", "--bind", "0.0.0.0:5000", "main:app"]

[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Test Runner"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Coverage Report"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "FastAPI Backend"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Flask UI"

[[workflows.workflow]]
name = "Test Runner"
author = "agent"

[workflows.workflow.metadata]
agentRequireRestartOnSave = false

[[workflows.workflow.tasks]]
task = "packager.installForAll"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python -m pytest tests/test_api/test_user_management.py -v"

[[workflows.workflow]]
name = "Coverage Report"
author = "agent"

[workflows.workflow.metadata]
agentRequireRestartOnSave = false

[[workflows.workflow.tasks]]
task = "packager.installForAll"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python -m pytest --cov=api --cov-report=term-missing tests/test_api/test_threat_defense_mapper.py -v"

[[workflows.workflow]]
name = "FastAPI Backend"
author = "agent"

[workflows.workflow.metadata]
agentRequireRestartOnSave = false

[[workflows.workflow.tasks]]
task = "packager.installForAll"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python -m uvicorn api.main:app --host 0.0.0.0 --port 5001 --reload"
waitForPort = 5001

[[workflows.workflow]]
name = "Flask UI"
author = "agent"

[workflows.workflow.metadata]
agentRequireRestartOnSave = false

[[workflows.workflow.tasks]]
task = "packager.installForAll"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python flask_app.py"
waitForPort = 5000

[[ports]]
localPort = 5000
externalPort = 80

[[ports]]
localPort = 5001
externalPort = 3001

[[ports]]
localPort = 8501
externalPort = 3000
