import unittest
from unittest.mock import MagicMock, patch
import json
from datetime import datetime

from api.services.logger import Logger
from api.models.logging import LogEntry, PerformanceMetric

class TestLogger(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create a mock database session
        self.db_session = MagicMock()
        
        # Initialize the logger with the mock session
        self.logger = Logger(session=self.db_session)
        
        # Set some properties for testing
        self.logger._correlation_id = "test-correlation-id"
        self.logger._session_id = "test-session-id"
        self.logger._user_id = 1
        
    def test_initialization(self):
        """Test logger initialization and default values."""
        logger = Logger()
        self.assertIsNotNone(logger.correlation_id)
        self.assertIsNotNone(logger.session_id)
        self.assertIsNone(logger.user_id)
        self.assertIsNotNone(logger.config)
        
    def test_sanitize_data(self):
        """Test that sensitive data is sanitized."""
        # Define test data with sensitive fields
        test_data = {
            "username": "test_user",
            "password": "secret_password",
            "credit_card": "1234-5678-9012-3456",
            "nested": {
                "password": "nested_secret",
                "token": "sensitive_token"
            },
            "safe_field": "public information"
        }
        
        # Configure logger to sanitize specific fields
        self.logger.config.sensitive_fields = ["password", "credit_card", "token"]
        
        # Sanitize the data
        sanitized = self.logger._sanitize_data(test_data)
        
        # Check that sensitive fields are redacted
        self.assertEqual(sanitized["username"], "test_user")
        self.assertEqual(sanitized["password"], "[REDACTED]")
        self.assertEqual(sanitized["credit_card"], "[REDACTED]")
        self.assertEqual(sanitized["nested"]["password"], "[REDACTED]")
        self.assertEqual(sanitized["nested"]["token"], "[REDACTED]")
        self.assertEqual(sanitized["safe_field"], "public information")
        
    def test_log_to_console(self):
        """Test that logs are written to console with appropriate level."""
        with patch("logging.getLogger") as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            # Test different log levels
            self.logger._log_to_console("debug", "Debug message")
            mock_logger.debug.assert_called_with("Debug message")
            
            self.logger._log_to_console("info", "Info message")
            mock_logger.info.assert_called_with("Info message")
            
            self.logger._log_to_console("warning", "Warning message")
            mock_logger.warning.assert_called_with("Warning message")
            
            self.logger._log_to_console("error", "Error message")
            mock_logger.error.assert_called_with("Error message")
            
            self.logger._log_to_console("critical", "Critical message")
            mock_logger.critical.assert_called_with("Critical message")
            
    def test_create_log_entry(self):
        """Test creating a log entry in the database."""
        # Call the method to create a log entry
        self.logger._create_log_entry(
            level="info",
            message="Test log message",
            source="test_source",
            component="test_component",
            metadata={"test_key": "test_value"}
        )
        
        # Check that add was called on the session
        self.db_session.add.assert_called_once()
        
        # Get the LogEntry object that was added
        log_entry = self.db_session.add.call_args[0][0]
        
        # Verify the log entry properties
        self.assertIsInstance(log_entry, LogEntry)
        self.assertEqual(log_entry.log_level, "info")
        self.assertEqual(log_entry.message, "Test log message")
        self.assertEqual(log_entry.source, "test_source")
        self.assertEqual(log_entry.component, "test_component")
        self.assertEqual(log_entry.correlation_id, "test-correlation-id")
        self.assertEqual(log_entry.session_id, "test-session-id")
        self.assertEqual(log_entry.user_id, 1)
        self.assertEqual(log_entry.metadata, {"test_key": "test_value"})
        
    def test_should_log(self):
        """Test log level filtering logic."""
        # Test with console level set to info
        self.logger.config.console_level = "info"
        
        # These should be logged
        self.assertTrue(self.logger._should_log("info", "console"))
        self.assertTrue(self.logger._should_log("warning", "console"))
        self.assertTrue(self.logger._should_log("error", "console"))
        self.assertTrue(self.logger._should_log("critical", "console"))
        
        # This should not be logged
        self.assertFalse(self.logger._should_log("debug", "console"))
        
        # Test with DB level set to error
        self.logger.config.db_level = "error"
        
        # These should be logged
        self.assertTrue(self.logger._should_log("error", "db"))
        self.assertTrue(self.logger._should_log("critical", "db"))
        
        # These should not be logged
        self.assertFalse(self.logger._should_log("debug", "db"))
        self.assertFalse(self.logger._should_log("info", "db"))
        self.assertFalse(self.logger._should_log("warning", "db"))
        
    def test_convenience_methods(self):
        """Test that convenience logging methods call the main log method."""
        with patch.object(self.logger, 'log') as mock_log:
            # Test debug method
            self.logger.debug("Debug message", "test_component", {"key": "value"})
            mock_log.assert_called_with("debug", "Debug message", "test_component", {"key": "value"})
            
            # Test info method
            self.logger.info("Info message", "test_component", {"key": "value"})
            mock_log.assert_called_with("info", "Info message", "test_component", {"key": "value"})
            
            # Test warning method
            self.logger.warning("Warning message", "test_component", {"key": "value"})
            mock_log.assert_called_with("warning", "Warning message", "test_component", {"key": "value"})
            
            # Test error method
            self.logger.error("Error message", "test_component", {"key": "value"})
            mock_log.assert_called_with("error", "Error message", "test_component", {"key": "value"})
            
            # Test critical method
            self.logger.critical("Critical message", "test_component", {"key": "value"})
            mock_log.assert_called_with("critical", "Critical message", "test_component", {"key": "value"})
    
    def test_log_exception(self):
        """Test logging an exception with stack trace."""
        with patch.object(self.logger, 'error') as mock_error:
            try:
                # Generate an exception
                raise ValueError("Test exception")
            except Exception as e:
                # Log the exception
                self.logger.log_exception(e, "test_component", {"extra": "info"})
                
                # Check that error was called with the message and metadata containing the stack
                args, kwargs = mock_error.call_args
                self.assertEqual(args[0], "Test exception")
                self.assertEqual(args[1], "test_component")
                self.assertIn("extra", args[2])
                self.assertEqual(args[2]["extra"], "info")
                self.assertIn("stack", args[2])
                self.assertIn("ValueError: Test exception", args[2]["stack"])
    
    def test_record_metric(self):
        """Test recording a performance metric."""
        # Record a metric
        self.logger.record_metric(
            metric_type="response_time",
            value=123.45,
            component="api_endpoint",
            unit="ms"
        )
        
        # Check that add was called on the session
        self.db_session.add.assert_called_once()
        
        # Get the PerformanceMetric object that was added
        metric = self.db_session.add.call_args[0][0]
        
        # Verify the metric properties
        self.assertIsInstance(metric, PerformanceMetric)
        self.assertEqual(metric.metric_type, "response_time")
        self.assertEqual(metric.value, 123.45)
        self.assertEqual(metric.component, "api_endpoint")
        self.assertEqual(metric.unit, "ms")
        self.assertEqual(metric.correlation_id, "test-correlation-id")
        self.assertEqual(metric.session_id, "test-session-id")
    
    def test_track_time_decorator(self):
        """Test the track_time decorator for measuring function execution time."""
        # Define a test function to be decorated
        @self.logger.track_time(metric_type="test_function", component="tests")
        def test_function():
            return "result"
        
        # Mock the record_metric method
        with patch.object(self.logger, 'record_metric') as mock_record_metric:
            # Call the decorated function
            result = test_function()
            
            # Check that the function returned the correct result
            self.assertEqual(result, "result")
            
            # Check that record_metric was called
            mock_record_metric.assert_called_once()
            
            # Check the arguments
            args, kwargs = mock_record_metric.call_args
            self.assertEqual(kwargs['metric_type'], "test_function")
            self.assertEqual(kwargs['component'], "tests")
            self.assertEqual(kwargs['unit'], "ms")
            self.assertGreaterEqual(kwargs['value'], 0)

if __name__ == '__main__':
    unittest.main() 