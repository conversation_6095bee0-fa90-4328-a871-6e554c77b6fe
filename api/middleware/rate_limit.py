"""Rate limiting middleware for FastAPI applications.

This module provides a configurable rate limiting middleware that can be used
to protect API endpoints from excessive requests. It implements a sliding window
algorithm to track and limit requests per client IP address.

Example:
    >>> app = FastAPI()
    >>> rate_limiter = RateLimiter(times=100, minutes=1)
    >>> app.middleware("http")(rate_limiter)
"""

from fastapi import HTT<PERSON>Exception, Request, Response
from collections import defaultdict
import time
from typing import DefaultDict, List, Optional, Dict, Callable, Awaitable, Union
import logging
import re

logger = logging.getLogger(__name__)

class RateLimiter:
    """Rate limiting middleware implementation.

    This class implements a sliding window rate limiter that tracks requests
    per client IP address and enforces configurable rate limits.

    Attributes:
        times (int): Maximum number of requests allowed within the time window.
        window (int): Time window in seconds within which to track requests.
        requests (DefaultDict[str, List[float]]): Dictionary tracking request timestamps
            per client IP.
        path_limits (Dict[str, Dict]): Dictionary mapping path patterns to custom rate limits.
        whitelist_ips (List[str]): List of IP addresses exempt from rate limiting.
        blacklist_ips (List[str]): List of IP addresses that are always blocked.
    """

    def __init__(
        self, 
        times: int = 100, 
        minutes: int = 1,
        path_limits: Optional[Dict[str, Dict[str, Union[int, int]]]] = None,
        whitelist_ips: Optional[List[str]] = None,
        blacklist_ips: Optional[List[str]] = None
    ) -> None:
        """Initialize the rate limiter.

        Args:
            times: Maximum number of requests allowed within the time window.
                Defaults to 100 requests.
            minutes: Size of the sliding window in minutes. Defaults to 1 minute.
            path_limits: Dictionary mapping path patterns to custom rate limits.
                Example: {"/api/v1/auth/.*": {"times": 5, "minutes": 1}}
            whitelist_ips: List of IP addresses exempt from rate limiting.
            blacklist_ips: List of IP addresses that are always blocked.
        """
        self.times = times
        self.window = minutes * 60
        self.requests: DefaultDict[str, List[float]] = defaultdict(list)
        self.path_limits = path_limits or {}
        self.whitelist_ips = whitelist_ips or []
        self.blacklist_ips = blacklist_ips or []
        
        # Compile regex patterns for path matching
        self.compiled_patterns = [(re.compile(pattern), limits) for pattern, limits in self.path_limits.items()]
        
        logger.info(f"Rate limiter initialized with default limit of {times} requests per {minutes} minute(s)")
        if path_limits:
            for path, limits in path_limits.items():
                logger.info(f"Custom rate limit for {path}: {limits['times']} requests per {limits['minutes']} minute(s)")

    def _get_client_ip(self, request: Request) -> str:
        """Extract the client IP address from the request.
        
        Handles X-Forwarded-For headers for clients behind proxies.
        
        Args:
            request: FastAPI request object.
            
        Returns:
            str: The client's IP address.
        """
        forwarded = request.headers.get("X-Forwarded-For")
        if forwarded:
            # Get the first IP in the chain (client's real IP)
            return forwarded.split(",")[0].strip()
        return request.client.host

    def _get_rate_limit_for_path(self, path: str) -> Dict[str, int]:
        """Get the rate limit configuration for a specific path.
        
        Args:
            path: The request path to check.
            
        Returns:
            Dict with 'times' and 'window' keys for the matching path,
            or the default values if no match is found.
        """
        for pattern, limits in self.compiled_patterns:
            if pattern.match(path):
                window = limits.get("minutes", 1) * 60
                return {"times": limits.get("times", self.times), "window": window}
        
        return {"times": self.times, "window": self.window}

    async def __call__(self, request: Request, call_next: Callable[[Request], Awaitable[Response]]) -> Response:
        """Process each request and enforce rate limits.

        Args:
            request: FastAPI request object containing client information.
            call_next: Function to call the next middleware or route handler.

        Returns:
            Response: The response from the next middleware or route handler.

        Raises:
            HTTPException: If the client has exceeded their rate limit, raises
                a 429 Too Many Requests error.
        """
        client_ip = self._get_client_ip(request)
        path = request.url.path
        
        # Check blacklist
        if client_ip in self.blacklist_ips:
            logger.warning(f"Blocked request from blacklisted IP: {client_ip}")
            return Response(
                content='{"detail":"IP address blocked"}',
                status_code=403,
                media_type="application/json"
            )
        
        # Skip rate limiting for whitelisted IPs
        if client_ip in self.whitelist_ips:
            return await call_next(request)
        
        # Get rate limit for this path
        limit_config = self._get_rate_limit_for_path(path)
        times = limit_config["times"]
        window = limit_config["window"]
        
        # Create a unique key for this client and path pattern
        client_key = f"{client_ip}:{path}"
        now = time.time()
        
        # Remove old requests outside the window
        self.requests[client_key] = [
            req_time for req_time in self.requests[client_key] 
            if now - req_time < window
        ]
        
        # Check if rate limit is exceeded
        if len(self.requests[client_key]) >= times:
            logger.warning(f"Rate limit exceeded for {client_ip} on path {path}")
            retry_after = int(window - (now - min(self.requests[client_key])))
            return Response(
                content=f'{{"detail":"Too many requests","retry_after":{retry_after}}}',
                status_code=429,
                headers={"Retry-After": str(retry_after)},
                media_type="application/json"
            )
        
        # Add current request timestamp
        self.requests[client_key].append(now)
        
        # Process the request
        return await call_next(request)

# Default rate limiter instance with tiered rate limits
default_rate_limiter = RateLimiter(
    times=100,  # Default: 100 requests per minute
    minutes=1,
    path_limits={
        # Authentication endpoints: 5 requests per minute
        r"/api/v1/auth/.*": {"times": 5, "minutes": 1},
        
        # Admin endpoints: 20 requests per minute
        r"/api/v1/admin/.*": {"times": 20, "minutes": 1},
        
        # MITRE ATT&CK data modification endpoints: 20 requests per minute
        r"/api/v1/mitre/.*/(?:create|update|delete|restore)": {"times": 20, "minutes": 1},
    }
)