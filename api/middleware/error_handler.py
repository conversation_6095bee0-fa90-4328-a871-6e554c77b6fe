"""
Error handling middleware for the FastAPI application.
"""
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from sqlalchemy.exc import SQLAlchemyError
from typing import Callable, Dict, Any
import logging
import traceback
import json
from api.models.database.error_handling import ErrorHandling
from sqlalchemy.orm import Session
from api.database import get_db

logger = logging.getLogger(__name__)

class ErrorHandlerMiddleware:
    """Middleware for handling errors in the FastAPI application."""
    
    def __init__(self, app: FastAPI):
        """Initialize the middleware with the FastAPI app."""
        self.app = app
        
    async def __call__(self, scope: Dict[str, Any], receive: Callable, send: Callable):
        """Process the request and handle any errors."""
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
            
        async def send_wrapper(message: Dict[str, Any]):
            """Wrap the send function to handle errors."""
            if message["type"] == "http.response.start" and message["status"] >= 400:
                # Log the error
                logger.error(f"HTTP Error {message['status']}: {scope['path']}")
                
                # We can't read the response body here, so just log what we know
                logger.error(f"Error status: {message['status']}")
                
                # Continue with the original send
                await send(message)
            else:
                # Pass through other messages
                await send(message)
        
        try:
            await self.app(scope, receive, send_wrapper)
        except Exception as exc:
            logger.error(f"Unhandled exception: {str(exc)}")
            logger.error(traceback.format_exc())
            
            # Store the error in the database
            try:
                db = next(get_db())
                error_handler = ErrorHandling(
                    name="Unhandled Exception",
                    description=f"Unhandled exception at {scope['path']}",
                    error_type="exception",
                    error_message=str(exc),
                    is_user_facing=False,
                    severity="high"
                )
                db.add(error_handler)
                db.commit()
            except Exception as e:
                logger.error(f"Failed to store error in database: {str(e)}")
            
            # Return a 500 Internal Server Error response
            response = JSONResponse(
                status_code=500,
                content={"detail": "Internal Server Error"}
            )
            await response(scope, receive, send)

def register_error_handlers(app: FastAPI):
    """Register error handlers for the FastAPI application."""
    
    @app.exception_handler(StarletteHTTPException)
    async def http_exception_handler(request: Request, exc: StarletteHTTPException):
        """Handle HTTP exceptions."""
        logger.error(f"HTTP Exception: {exc.detail}")
        
        # Store the error in the database
        try:
            db = next(get_db())
            error_handler = ErrorHandling(
                name=f"HTTP {exc.status_code}",
                description=f"HTTP exception at {request.url.path}",
                error_type="http",
                error_message=str(exc.detail),
                http_status_code=exc.status_code,
                is_user_facing=True,
                severity="medium"
            )
            db.add(error_handler)
            db.commit()
        except Exception as e:
            logger.error(f"Failed to store error in database: {str(e)}")
        
        return JSONResponse(
            status_code=exc.status_code,
            content={"detail": exc.detail}
        )
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        """Handle validation errors."""
        error_detail = str(exc)
        logger.error(f"Validation Error: {error_detail}")
        
        # Store the error in the database
        try:
            db = next(get_db())
            error_handler = ErrorHandling(
                name="Validation Error",
                description=f"Validation error at {request.url.path}",
                error_type="validation",
                error_message=error_detail,
                http_status_code=422,
                is_user_facing=True,
                severity="low"
            )
            db.add(error_handler)
            db.commit()
        except Exception as e:
            logger.error(f"Failed to store error in database: {str(e)}")
        
        return JSONResponse(
            status_code=422,
            content={"detail": exc.errors()}
        )
    
    @app.exception_handler(SQLAlchemyError)
    async def sqlalchemy_exception_handler(request: Request, exc: SQLAlchemyError):
        """Handle SQLAlchemy errors."""
        error_detail = str(exc)
        logger.error(f"Database Error: {error_detail}")
        
        # Store the error in the database
        try:
            db = next(get_db())
            error_handler = ErrorHandling(
                name="Database Error",
                description=f"Database error at {request.url.path}",
                error_type="database",
                error_message=error_detail,
                http_status_code=500,
                is_user_facing=False,
                severity="high"
            )
            db.add(error_handler)
            db.commit()
        except Exception as e:
            logger.error(f"Failed to store error in database: {str(e)}")
        
        return JSONResponse(
            status_code=500,
            content={"detail": "Database error occurred"}
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle general exceptions."""
        error_detail = str(exc)
        logger.error(f"Unhandled Exception: {error_detail}")
        logger.error(traceback.format_exc())
        
        # Store the error in the database
        try:
            db = next(get_db())
            error_handler = ErrorHandling(
                name="Unhandled Exception",
                description=f"Unhandled exception at {request.url.path}",
                error_type="exception",
                error_message=error_detail,
                http_status_code=500,
                is_user_facing=False,
                severity="high"
            )
            db.add(error_handler)
            db.commit()
        except Exception as e:
            logger.error(f"Failed to store error in database: {str(e)}")
        
        return JSONResponse(
            status_code=500,
            content={"detail": "Internal server error"}
        )