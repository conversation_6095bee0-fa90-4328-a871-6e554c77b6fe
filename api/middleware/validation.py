"""Middleware for input validation."""

from fastapi import Request, Response, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
import json
import logging
from typing import Dict, Any, List, Optional, Callable
import re
from datetime import datetime

from api.utils.validators import (
    validate_username,
    validate_email,
    validate_password,
    validate_mitre_technique_id,
    validate_pagination_params,
    validate_date_range,
    validate_json_data
)

logger = logging.getLogger(__name__)

class ValidationMiddleware(BaseHTTPMiddleware):
    """Middleware for validating request inputs."""
    
    def __init__(
        self,
        app: ASGIApp,
        exclude_paths: Optional[List[str]] = None,
        path_validators: Optional[Dict[str, Dict[str, Callable]]] = None
    ):
        """
        Initialize the validation middleware.
        
        Args:
            app: The ASGI application
            exclude_paths: List of paths to exclude from validation
            path_validators: Dictionary mapping paths to validator dictionaries
        """
        super().__init__(app)
        self.exclude_paths = exclude_paths or []
        self.path_validators = path_validators or {}
        
        # Default validators for common paths
        self.default_validators = {
            r"^/api/v1/auth/register": {
                "username": validate_username,
                "email": validate_email,
                "password": validate_password
            },
            r"^/api/v1/auth/token": {
                "username": validate_username,
                "password": validate_password
            },
            r"^/api/v1/users": {
                "username": validate_username,
                "email": validate_email
            },
            r"^/api/v1/mitre/techniques/T\d+": {
                "technique_id": validate_mitre_technique_id
            }
        }
        
        # Merge default validators with custom validators
        for path, validators in self.default_validators.items():
            if path not in self.path_validators:
                self.path_validators[path] = validators
            else:
                self.path_validators[path].update(validators)
    
    async def dispatch(self, request: Request, call_next):
        """
        Process the request through the middleware.
        
        Args:
            request: The incoming request
            call_next: The next middleware or endpoint handler
            
        Returns:
            The response from the next handler or a validation error response
        """
        # Skip validation for excluded paths
        path = request.url.path
        if any(path.startswith(exclude) for exclude in self.exclude_paths):
            return await call_next(request)
        
        # Check if we have validators for this path
        validators = self._get_validators_for_path(path)
        if not validators:
            return await call_next(request)
        
        # Validate request data
        validation_errors = await self._validate_request(request, validators)
        if validation_errors:
            return JSONResponse(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                content={
                    "detail": "Validation error",
                    "errors": validation_errors,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
        
        # Continue processing the request
        return await call_next(request)
    
    def _get_validators_for_path(self, path: str) -> Dict[str, Callable]:
        """
        Get validators for a specific path.
        
        Args:
            path: The request path
            
        Returns:
            Dictionary of validators for the path
        """
        for pattern, validators in self.path_validators.items():
            if re.match(pattern, path):
                return validators
        return {}
    
    async def _validate_request(self, request: Request, validators: Dict[str, Callable]) -> List[str]:
        """
        Validate the request data.
        
        Args:
            request: The incoming request
            validators: Dictionary of validators to apply
            
        Returns:
            List of validation error messages
        """
        errors = []
        
        # Validate query parameters
        query_params = dict(request.query_params)
        for param, validator in validators.items():
            if param in query_params:
                is_valid, message = validator(query_params[param])
                if not is_valid:
                    errors.append(f"Query parameter '{param}': {message}")
        
        # Validate request body for POST/PUT/PATCH requests
        if request.method in ["POST", "PUT", "PATCH"]:
            try:
                body = await request.json()
                if isinstance(body, dict):
                    for field, validator in validators.items():
                        if field in body:
                            is_valid, message = validator(body[field])
                            if not is_valid:
                                errors.append(f"Field '{field}': {message}")
            except json.JSONDecodeError:
                # If the body is not JSON, we'll skip body validation
                pass
        
        return errors

def get_validation_middleware(
    exclude_paths: Optional[List[str]] = None,
    path_validators: Optional[Dict[str, Dict[str, Callable]]] = None
) -> ValidationMiddleware:
    """
    Create a configured validation middleware instance.
    
    Args:
        exclude_paths: List of paths to exclude from validation
        path_validators: Dictionary mapping paths to validator dictionaries
        
    Returns:
        Configured ValidationMiddleware instance
    """
    return ValidationMiddleware(
        app=None,  # This will be set when the middleware is added to the app
        exclude_paths=exclude_paths,
        path_validators=path_validators
    ) 