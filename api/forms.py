from flask_wtf import <PERSON>laskForm
from wtforms import <PERSON><PERSON><PERSON>, Password<PERSON>ield, <PERSON>ail<PERSON>ield
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError
from api.models.user import User
from api.database import db

class LoginForm(FlaskForm):
    """Form for admin login."""
    username = StringField('Username', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])

class RegistrationForm(FlaskForm):
    """Form for user registration."""
    username = StringField('Username', validators=[
        DataRequired(),
        Length(min=3, max=64, message="Username must be between 3 and 64 characters")
    ])
    email = EmailField('Email', validators=[
        DataRequired(),
        Email(),
        Length(max=120)
    ])
    password = PasswordField('Password', validators=[
        DataRequired(),
        Length(min=8, message="Password must be at least 8 characters long")
    ])
    confirm_password = PasswordField('Confirm Password', validators=[
        DataRequired(),
        EqualTo('password', message='Passwords must match')
    ])

    def validate_username(self, field):
        """Check if username is already taken."""
        user = db.session.query(User).filter_by(username=field.data).first()
        if user:
            raise ValidationError('Username already taken.')

    def validate_email(self, field):
        """Check if email is already registered."""
        user = db.session.query(User).filter_by(email=field.data).first()
        if user:
            raise ValidationError('Email already registered.')