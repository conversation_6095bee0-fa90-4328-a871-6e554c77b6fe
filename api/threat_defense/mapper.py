"""ThreatDefenseMapper class for analyzing attack paths and defense coverage."""
import logging
import re
from typing import Dict, List, Optional, Union, Any, TypedDict
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy.sql import text
from sqlalchemy.exc import SQLAlchemyError

from api.models.mitre import MitreTechnique
from api.models.stix import StixDB
from api.models.stix_mappings import STIXd3fendMapping

logger = logging.getLogger(__name__)

class CountermeasureDict(TypedDict):
    """Type definition for countermeasure dictionary."""
    name: str
    effectiveness: float 
    description: str
    d3fend_id: str

class DefenseGapDict(TypedDict):
    """Type definition for defense gap dictionary."""
    technique_id: str
    technique_name: str
    coverage: float
    recommendation: str

class CoverageDict(TypedDict):
    """Type definition for coverage dictionary."""
    techniques: Dict[str, float]
    overall_coverage: float
    average_coverage: float

class ThreatDefenseMapper:
    """Maps attack techniques to defense capabilities and analyzes coverage."""

    # Class constants
    TECHNIQUE_ID_PATTERN: re.Pattern = re.compile(r'^T\d{4}(\.\d{3})?$')
    MAX_TECHNIQUES: int = 50  # Maximum number of techniques to process at once
    COVERAGE_PER_COUNTERMEASURE: float = 0.2  # Each countermeasure provides 20% coverage
    MAX_COVERAGE: float = 1.0  # Maximum possible coverage is 100%
    DEFAULT_MIN_COVERAGE: float = 0.7  # Default minimum coverage threshold

    def __init__(self, db: Session) -> None:
        """Initialize mapper with database session.

        Args:
            db: SQLAlchemy database session
        """
        self.db = db

    def _validate_technique_id(self, technique_id: str) -> bool:
        """Validate format of MITRE ATT&CK technique ID.

        Args:
            technique_id: ID to validate (e.g., "T1566")

        Returns:
            bool: True if valid format, False otherwise
        """
        return bool(self.TECHNIQUE_ID_PATTERN.match(technique_id))

    def _sanitize_technique_ids(self, technique_sequence: List[str]) -> List[str]:
        """Sanitize and validate a sequence of technique IDs.

        Args:
            technique_sequence: Raw list of technique IDs

        Returns:
            List[str]: Validated technique IDs

        Raises:
            ValueError: If any technique ID is invalid or sequence is too long
        """
        if len(technique_sequence) > self.MAX_TECHNIQUES:
            raise ValueError(f"Too many techniques. Maximum allowed is {self.MAX_TECHNIQUES}")

        valid_ids = []
        for technique_id in technique_sequence:
            if not isinstance(technique_id, str):
                raise ValueError(f"Invalid technique ID type: {type(technique_id)}")

            if not self._validate_technique_id(technique_id):
                raise ValueError(f"Invalid technique ID format: {technique_id}")

            valid_ids.append(technique_id)

        return valid_ids

    def find_d3fend_countermeasures(
        self,
        technique_sequence: List[str]
    ) -> Dict[str, List[CountermeasureDict]]:
        """Find relevant D3FEND countermeasures for each technique.

        Args:
            technique_sequence: List of technique IDs representing an attack path

        Returns:
            Dict mapping technique IDs to lists of countermeasure details

        Raises:
            ValueError: If technique IDs are invalid
            SQLAlchemyError: If database query fails
        """
        try:
            # Validate input
            technique_sequence = self._sanitize_technique_ids(technique_sequence)
            logger.debug(f"Finding countermeasures for techniques: {technique_sequence}")

            countermeasures: Dict[str, List[CountermeasureDict]] = {}

            for technique_id in technique_sequence:
                try:
                    # Get D3FEND mappings and associated countermeasures
                    mappings = self.db.query(STIXd3fendMapping).filter(
                        STIXd3fendMapping.technique_id == technique_id
                    ).all()
                    logger.debug(f"Found {len(mappings)} mappings for {technique_id}")

                    countermeasures[technique_id] = []
                    for mapping in mappings:
                        countermeasures[technique_id].append({
                            "name": mapping.countermeasure_name,
                            "effectiveness": mapping.effectiveness_score or 0.5,
                            "description": mapping.description or "",
                            "d3fend_id": mapping.d3fend_id
                        })

                except SQLAlchemyError as e:
                    logger.warning(f"Error querying mappings for {technique_id}: {e}")
                    countermeasures[technique_id] = []

        except SQLAlchemyError as e:
            logger.error("Database error in find_d3fend_countermeasures: %s", str(e))
            raise
        except Exception as e:
            logger.error("Error in find_d3fend_countermeasures: %s", str(e))
            raise

        return countermeasures

    def get_attack_path_coverage(
        self,
        technique_sequence: List[str]
    ) -> CoverageDict:
        """Calculate defense coverage for an attack path.

        Args:
            technique_sequence: List of technique IDs in attack path order
                Each ID should match a valid MITRE ATT&CK technique (e.g., ["T1566", "T1078"])

        Returns:
            Dict containing coverage metrics:
                - techniques: Dict mapping technique IDs to coverage scores (0.0-1.0)
                - overall_coverage: Minimum coverage across all techniques
                - average_coverage: Average coverage across all techniques

        Raises:
            ValueError: If technique IDs are invalid
            SQLAlchemyError: If database query fails
        """
        coverage_data: CoverageDict = {
            "techniques": {},
            "overall_coverage": 0.0,
            "average_coverage": 0.0
        }

        try:
            # Validate input
            technique_sequence = self._sanitize_technique_ids(technique_sequence)
            logger.debug(f"Calculating coverage for techniques: {technique_sequence}")

            total_coverage = 0.0

            for technique_id in technique_sequence:
                try:
                    # Get D3FEND mappings for this technique
                    mappings = self.db.query(STIXd3fendMapping).filter(
                        STIXd3fendMapping.technique_id == technique_id
                    ).all()
                    logger.debug(f"Found {len(mappings)} countermeasures for {technique_id}")

                    # Calculate coverage based on number and effectiveness of controls
                    technique_coverage = min(
                        len(mappings) * self.COVERAGE_PER_COUNTERMEASURE,
                        self.MAX_COVERAGE
                    )
                    coverage_data["techniques"][technique_id] = technique_coverage
                    total_coverage += technique_coverage

                except SQLAlchemyError as e:
                    logger.warning(f"Error querying mappings for {technique_id}: {e}")
                    coverage_data["techniques"][technique_id] = 0.0

            if technique_sequence:
                coverage_data["average_coverage"] = total_coverage / len(technique_sequence)
                # Overall coverage considers sequential dependencies
                coverage_data["overall_coverage"] = min(coverage_data["techniques"].values())

        except SQLAlchemyError as e:
            logger.error("Database error in get_attack_path_coverage: %s", str(e))
            raise
        except Exception as e:
            logger.error("Error in get_attack_path_coverage: %s", str(e))
            raise

        return coverage_data

    def analyze_defense_gaps(
        self,
        technique_sequence: List[str]
    ) -> List[DefenseGapDict]:
        """Identify gaps in defense coverage for an attack path.

        Args:
            technique_sequence: List of technique IDs representing attack path

        Returns:
            List of identified defense gaps, each containing:
                - technique_id: ID of the technique with insufficient coverage
                - technique_name: Name of the technique
                - coverage: Current coverage score (0.0-1.0)
                - recommendation: Suggested improvement action

        Raises:
            ValueError: If technique IDs are invalid
            SQLAlchemyError: If database query fails
        """
        try:
            # Validate input
            technique_sequence = self._sanitize_technique_ids(technique_sequence)

            gaps: List[DefenseGapDict] = []
            coverage = self.get_attack_path_coverage(technique_sequence)

            for technique_id, coverage_score in coverage["techniques"].items():
                if coverage_score < self.DEFAULT_MIN_COVERAGE:
                    technique = self.db.query(MitreTechnique).filter(
                        MitreTechnique.technique_id == technique_id
                    ).first()

                    if technique:
                        gaps.append({
                            "technique_id": technique_id,
                            "technique_name": technique.name,
                            "coverage": coverage_score,
                            "recommendation": f"Implement additional controls for {technique.name}"
                        })

        except SQLAlchemyError as e:
            logger.error("Database error in analyze_defense_gaps: %s", str(e))
            raise
        except Exception as e:
            logger.error("Error in analyze_defense_gaps: %s", str(e))
            raise

        return gaps

    def recommend_defense_improvements(
        self,
        technique_ids: List[str],
        min_coverage: float = DEFAULT_MIN_COVERAGE
    ) -> Dict[str, List[CountermeasureDict]]:
        """Recommend D3FEND controls to improve coverage.

        Args:
            technique_ids: List of ATT&CK technique IDs
            min_coverage: Minimum desired coverage threshold

        Returns:
            Dict mapping technique IDs to recommended D3FEND controls
        """
        # Validate input
        technique_ids = self._sanitize_technique_ids(technique_ids)

        # Get current coverage
        current_coverage = self.get_attack_path_coverage(technique_ids)

        # Find techniques below threshold
        below_threshold = [
            tid for tid, score in current_coverage["techniques"].items()
            if score < min_coverage
        ]

        return self.find_d3fend_countermeasures(below_threshold)