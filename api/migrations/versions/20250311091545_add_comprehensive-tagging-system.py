"""
Add comprehensive tagging system tables

Revision ID: 20250311091545
Revises: 
Create Date: 2025-03-11 09:15:45.369
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import func

# revision identifiers, used by Alembic
revision = '20250311091545'
down_revision = None  # TODO: Update this to the previous migration
branch_labels = None
depends_on = None

def upgrade():
    """Upgrade database schema"""
    # Create tags table
    op.create_table(
        'tags',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('color', sa.String(length=7), nullable=False, server_default="#3498db"),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_by_id', sa.Integer(), sa.<PERSON>('users.id'), nullable=True),
        sa.Primary<PERSON>eyConstraint('id'),
        sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_tags_id'), 'tags', ['id'], unique=False)
    op.create_index(op.f('ix_tags_name'), 'tags', ['name'], unique=True)
    
    # Create tag_associations table
    op.create_table(
        'tag_associations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('tag_id', sa.Integer(), sa.ForeignKey('tags.id', ondelete='CASCADE'), nullable=False),
        sa.Column('entity_type', sa.String(length=50), nullable=False),
        sa.Column('entity_id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=False),
        sa.Column('created_by_id', sa.Integer(), sa.ForeignKey('users.id'), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tag_associations_id'), 'tag_associations', ['id'], unique=False)
    op.create_index(op.f('ix_tag_associations_tag_id'), 'tag_associations', ['tag_id'], unique=False)
    op.create_index(op.f('ix_tag_associations_entity_type'), 'tag_associations', ['entity_type'], unique=False)
    op.create_index(op.f('ix_tag_associations_entity_id'), 'tag_associations', ['entity_id'], unique=False)
    
    # Create composite index for faster lookups
    op.create_index(
        'ix_tag_associations_entity',
        'tag_associations',
        ['entity_type', 'entity_id'],
        unique=False
    )

def downgrade():
    """Downgrade database schema"""
    op.drop_index(op.f('ix_tag_associations_entity'), table_name='tag_associations')
    op.drop_index(op.f('ix_tag_associations_entity_id'), table_name='tag_associations')
    op.drop_index(op.f('ix_tag_associations_entity_type'), table_name='tag_associations')
    op.drop_index(op.f('ix_tag_associations_tag_id'), table_name='tag_associations')
    op.drop_index(op.f('ix_tag_associations_id'), table_name='tag_associations')
    op.drop_table('tag_associations')
    
    op.drop_index(op.f('ix_tags_name'), table_name='tags')
    op.drop_index(op.f('ix_tags_id'), table_name='tags')
    op.drop_table('tags')
