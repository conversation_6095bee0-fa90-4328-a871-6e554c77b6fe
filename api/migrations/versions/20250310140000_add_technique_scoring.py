"""Add technique scoring

Revision ID: 20250310140000
Revises: 20250310132018
Create Date: 2025-03-10 14:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20250310140000'
down_revision = '20250310132018'
branch_labels = None
depends_on = None


def upgrade():
    # Create enum type for score categories
    score_category = sa.Enum('impact', 'likelihood', 'detectability', 'exploitability', 'custom', name='scorecategory')
    score_category.create(op.get_bind())
    
    # Create technique_scores table
    op.create_table('technique_scores',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('technique_id', sa.Integer(), nullable=False),
        sa.Column('category', score_category, nullable=False),
        sa.Column('score', sa.Float(), nullable=False),
        sa.Column('weight', sa.Float(), server_default='1.0', nullable=False),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['technique_id'], ['mitre_techniques.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('technique_id', 'category', name='uix_technique_score_category')
    )
    
    # Create index for faster lookups
    op.create_index(op.f('ix_technique_scores_technique_id'), 'technique_scores', ['technique_id'], unique=False)
    op.create_index(op.f('ix_technique_scores_category'), 'technique_scores', ['category'], unique=False)


def downgrade():
    # Drop indexes
    op.drop_index(op.f('ix_technique_scores_category'), table_name='technique_scores')
    op.drop_index(op.f('ix_technique_scores_technique_id'), table_name='technique_scores')
    
    # Drop table
    op.drop_table('technique_scores')
    
    # Drop enum type
    sa.Enum(name='scorecategory').drop(op.get_bind(), checkfirst=False) 