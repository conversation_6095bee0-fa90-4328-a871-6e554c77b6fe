"""Add two-factor authentication fields to User model.

Revision ID: 2fa_fields_20250310
Revises: previous_revision_id
Create Date: 2025-03-10 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '2fa_fields_20250310'
down_revision = None  # Set this to the previous migration ID
branch_labels = None
depends_on = None

def upgrade():
    """Add two-factor authentication fields to the users table."""
    op.add_column('users', sa.Column('two_factor_enabled', sa.<PERSON>(), nullable=False, server_default='false'))
    op.add_column('users', sa.Column('two_factor_secret', sa.String(length=32), nullable=True))
    op.add_column('users', sa.Column('backup_codes', sa.String(length=512), nullable=True))

def downgrade():
    """Remove two-factor authentication fields from the users table."""
    op.drop_column('users', 'backup_codes')
    op.drop_column('users', 'two_factor_secret')
    op.drop_column('users', 'two_factor_enabled') 