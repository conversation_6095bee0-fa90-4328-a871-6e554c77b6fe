"""
Add tag propagation rules table

Revision ID: 20250311_tag_propagation_rules
Revises: 20250311091545_add_comprehensive-tagging-system
Create Date: 2025-03-11 14:30:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '20250311_tag_propagation_rules'
down_revision = '20250311091545_add_comprehensive-tagging-system'
branch_labels = None
depends_on = None


def upgrade():
    # Create tag_propagation_rules table
    op.create_table(
        'tag_propagation_rules',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('source_type', sa.String(length=50), nullable=False),
        sa.Column('target_type', sa.String(length=50), nullable=False),
        sa.Column('relation_field', sa.String(length=100), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_by_id', sa.Integer(), sa.ForeignKey('users.id'), nullable=True),
        sa.Column('updated_by_id', sa.Integer(), sa.ForeignKey('users.id'), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('source_type', 'target_type', 'relation_field', name='unique_tag_propagation_rule')
    )
    
    # Create index for faster lookups
    op.create_index(
        'ix_tag_propagation_rules_source_type',
        'tag_propagation_rules',
        ['source_type'],
        unique=False
    )
    op.create_index(
        'ix_tag_propagation_rules_target_type',
        'tag_propagation_rules',
        ['target_type'],
        unique=False
    )
    op.create_index(
        'ix_tag_propagation_rules_is_active',
        'tag_propagation_rules',
        ['is_active'],
        unique=False
    )


def downgrade():
    # Drop indexes
    op.drop_index('ix_tag_propagation_rules_is_active', table_name='tag_propagation_rules')
    op.drop_index('ix_tag_propagation_rules_target_type', table_name='tag_propagation_rules')
    op.drop_index('ix_tag_propagation_rules_source_type', table_name='tag_propagation_rules')
    
    # Drop table
    op.drop_table('tag_propagation_rules') 