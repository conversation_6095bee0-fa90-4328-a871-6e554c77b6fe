"""
Add error-handling table

Revision ID: 20250310131232
Revises: 
Create Date: 2025-03-10 13:12:32.569
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import func

# revision identifiers, used by Alembic
revision = '20250310131232'
down_revision = None  # TODO: Update this to the previous migration
branch_labels = None
depends_on = None

def upgrade():
    """Upgrade database schema"""
    op.create_table(
        'error-handlings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_error-handlings_id'), 'error-handlings', ['id'], unique=False)
    
    # TODO: Add additional columns and indexes as needed

def downgrade():
    """Downgrade database schema"""
    op.drop_index(op.f('ix_error-handlings_id'), table_name='error-handlings')
    op.drop_table('error-handlings')
