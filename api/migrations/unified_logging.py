from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSON, TIMESTAMP

# revision identifiers
revision = 'unified_logging_001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # Create log_entries table
    op.create_table(
        'log_entries',
        sa.Column('id', sa.Integer, primary_key=True),
        sa.Column('timestamp', sa.DateTime(timezone=True), nullable=False, index=True),
        sa.Column('correlation_id', sa.String(64), nullable=False, index=True),
        sa.Column('user_id', sa.Integer, sa.<PERSON>ey('users.id'), nullable=True),
        sa.Column('session_id', sa.String(64), nullable=True, index=True),
        sa.Column('log_level', sa.String(20), nullable=False, index=True),
        sa.Column('source', sa.String(20), nullable=False, index=True),
        sa.Column('component', sa.String(100), nullable=False, index=True),
        sa.Column('message', sa.Text, nullable=False),
        sa.Column('metadata', JSON, nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False)
    )

    # Create performance_metrics table
    op.create_table(
        'performance_metrics',
        sa.Column('id', sa.Integer, primary_key=True),
        sa.Column('timestamp', sa.DateTime(timezone=True), nullable=False, index=True),
        sa.Column('metric_type', sa.String(50), nullable=False, index=True),
        sa.Column('component', sa.String(100), nullable=False, index=True),
        sa.Column('value', sa.Float, nullable=False),
        sa.Column('unit', sa.String(20), nullable=False),
        sa.Column('correlation_id', sa.String(64), nullable=True, index=True),
        sa.Column('session_id', sa.String(64), nullable=True, index=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False)
    )

    # Create test_scenarios table
    op.create_table(
        'test_scenarios',
        sa.Column('id', sa.Integer, primary_key=True),
        sa.Column('name', sa.String(100), nullable=False, unique=True),
        sa.Column('description', sa.Text, nullable=True),
        sa.Column('creator_id', sa.Integer, sa.ForeignKey('users.id'), nullable=False),
        sa.Column('scenario_data', JSON, nullable=False),
        sa.Column('last_run', sa.DateTime(timezone=True), nullable=True),
        sa.Column('status', sa.String(20), nullable=False, server_default='active'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False)
    )

    # Create test_results table
    op.create_table(
        'test_results',
        sa.Column('id', sa.Integer, primary_key=True),
        sa.Column('test_scenario_id', sa.Integer, sa.ForeignKey('test_scenarios.id'), nullable=False),
        sa.Column('run_timestamp', sa.DateTime(timezone=True), nullable=False),
        sa.Column('duration', sa.Float, nullable=False),  # Duration in seconds
        sa.Column('status', sa.String(20), nullable=False),
        sa.Column('result_data', JSON, nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False)
    )

    # Create indexes for frequent queries
    op.create_index('idx_log_entries_timestamp_level', 'log_entries', ['timestamp', 'log_level'])
    op.create_index('idx_log_entries_correlation_timestamp', 'log_entries', ['correlation_id', 'timestamp'])
    op.create_index('idx_metrics_timestamp_type', 'performance_metrics', ['timestamp', 'metric_type'])
    op.create_index('idx_test_results_scenario_timestamp', 'test_results', ['test_scenario_id', 'run_timestamp'])


def downgrade():
    # Drop tables in reverse order to avoid foreign key constraints
    op.drop_table('test_results')
    op.drop_table('test_scenarios')
    op.drop_table('performance_metrics')
    op.drop_table('log_entries') 