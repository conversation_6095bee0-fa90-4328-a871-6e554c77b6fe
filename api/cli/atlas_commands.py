"""CLI commands for managing ATLAS data."""
import click
import json
import logging
import os
import sys
import traceback
from api.database import SessionLocal
from api.utils.atlas_importer import AtlasImporter, ImportError

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@click.group()
def atlas():
    """Manage ATLAS data."""
    pass

@atlas.command()
@click.option(
    '--matrix-file',
    default="3rdparty/atlas-navigator-data/dist/default-navigator-layers/atlas_layer_matrix.json",
    help="Path to ATLAS matrix JSON file"
)
@click.option(
    '--force-update',
    is_flag=True,
    default=False,
    help="Force update if version already exists"
)
def import_data(matrix_file, force_update):
    """Import ATLAS data from matrix file."""
    try:
        logger.info(f"Starting ATLAS data import from {matrix_file}")

        if not os.path.exists(matrix_file):
            logger.error(f"Error: Matrix file not found at {matrix_file}")
            sys.exit(1)

        db = SessionLocal()
        try:
            # Load data from file
            with open(matrix_file, 'r') as f:
                data = json.load(f)

            # Create importer instance and process data
            importer = AtlasImporter(db)
            results = importer.process_import(data, force_update=force_update)

            logger.info(f"Successfully imported ATLAS data: {results}")

        except ImportError as e:
            logger.error(f"Error importing ATLAS data: {str(e)}")
            sys.exit(1)
        except Exception as e:
            logger.error(f"Unexpected error importing ATLAS data: {str(e)}\n{traceback.format_exc()}")
            sys.exit(1)
        finally:
            db.close()
    except Exception as e:
        logger.error(f"Fatal error in import_data command: {str(e)}\n{traceback.format_exc()}")
        sys.exit(1)

if __name__ == '__main__':
    atlas()