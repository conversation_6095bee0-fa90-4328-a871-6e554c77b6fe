"""
Database models for assessments and test executions.

This module contains the SQLAlchemy models for assessments and test executions,
which are used to track security testing activities and their outcomes.
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func

from api.database import Base
from api.models.base import VersionMixin
from api.models.mixins import SoftDeleteMixin


class Assessment(Base, SoftDeleteMixin):
    """Database model for security assessments."""
    __tablename__ = "assessments"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    target_system = Column(String, nullable=False)
    assessment_type = Column(String, nullable=False)  # vulnerability, penetration, code_review, red_team, compliance, custom
    status = Column(String, default="pending")  # pending, in_progress, completed, reviewed, cancelled
    start_date = Column(DateTime, nullable=True)
    end_date = Column(DateTime, nullable=True)
    environment_id = Column(Integer, ForeignKey("environments.id"), nullable=True)
    created_by = Column(Integer, ForeignKey("users.id"))
    created_on = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    environment = relationship("Environment", back_populates="assessments")
    creator = relationship("User", foreign_keys=[created_by], back_populates="created_assessments")
    test_executions = relationship("TestExecution", back_populates="assessment", cascade="all, delete-orphan")
    campaigns = relationship("Campaign", back_populates="assessment", foreign_keys="Campaign.assessment_id")
    technique_results = relationship("AttackTechniqueResult", back_populates="assessment", cascade="all, delete-orphan")
    heatmap_snapshots = relationship("HeatMapSnapshot", back_populates="assessment", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Assessment id={self.id} name={self.name} status={self.status}>"


class TestExecution(Base):
    """Database model for test case executions within assessments."""
    __tablename__ = "test_executions"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    test_case_id = Column(Integer, ForeignKey("test_cases.id"))
    assessment_id = Column(Integer, ForeignKey("assessments.id"))
    result = Column(String, default="pending")  # pending, pass, fail, partial, blocked, not_applicable
    notes = Column(Text, nullable=True)
    evidence = Column(JSON, nullable=True)  # JSON data for evidence (links, screenshots, etc.)
    executed_by = Column(Integer, ForeignKey("users.id"))
    executed_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    test_case = relationship("TestCase", back_populates="executions")
    assessment = relationship("Assessment", back_populates="test_executions")
    executor = relationship("User", foreign_keys=[executed_by], back_populates="executed_tests")

    def __repr__(self):
        return f"<TestExecution id={self.id} test_case_id={self.test_case_id} result={self.result}>" 