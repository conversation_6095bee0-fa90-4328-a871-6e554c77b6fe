"""Models package for the cybersecurity data platform.
Contains all database models used in the application.
"""

# Import base models first
from api.models.admin_notification import AdminNotification
from api.models.assessment import Assessment, TestExecution

# Import ATLAS models last since they don't have circular dependencies
from api.models.atlas import (
    AtlasMatrix,
    AtlasMatrixItem,
    AtlasTactic,
    AtlasTechnique,
    AtlasVersion,
)
from api.models.audit_log import AuditLog
from api.models.campaign import Campaign
from api.models.database.test_case import TestCase

# Import hierarchical data models
from api.models.environment import Environment

# Import logging models
from api.models.error_log import ErrorLog

# Import MITRE models
from api.models.mitre import (
    MitreGroup,
    MitreMitigation,
    MitreSoftware,
    MitreTactic,
    MitreTechnique,
    MitreVersion,
    TechnologyDomain,
)
from api.models.session import UserSession
from api.models.soft_deletion import (
    SoftDeletionAudit,
    SoftDeletionNotification,
    SoftDeletionPolicy,
    SoftDeletionSchedule,
)
from api.models.testcase_chain import (
    ChainExecution,
    NodeExecution,
    <PERSON>caseChain,
    TestcaseChainEdge,
    TestcaseChainNode,
    TestcaseCondition,
)
from api.models.user import User
from api.models.user_preferences import UserPreference

# Export all models that are currently in use
__all__ = [
    "AdminNotification",
    "Assessment",
    "AtlasMatrix",
    "AtlasMatrixItem",
    "AtlasTactic",
    "AtlasTechnique",
    "AtlasVersion",
    "AuditLog",
    "Campaign",
    "ChainExecution",
    "Environment",
    "ErrorLog",
    "MitreGroup",
    "MitreMitigation",
    "MitreSoftware",
    "MitreTactic",
    "MitreTechnique",
    "MitreVersion",
    "NodeExecution",
    "SoftDeletionAudit",
    "SoftDeletionNotification",
    "SoftDeletionPolicy",
    "SoftDeletionSchedule",
    "TechnologyDomain",
    "TestCase",
    "TestcaseChain",
    "TestcaseChainEdge",
    "TestcaseChainNode",
    "TestcaseCondition",
    "TestExecution",
    "User",
    "UserPreference",
    "UserSession",
]
