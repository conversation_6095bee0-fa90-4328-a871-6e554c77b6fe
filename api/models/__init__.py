"""
Models package for the cybersecurity data platform.
Contains all database models used in the application.
"""

# Import base models first
from api.models.user import User
from api.models.session import UserSession
from api.models.user_preferences import UserPreference

# Import logging models
from api.models.error_log import ErrorLog
from api.models.audit_log import AuditLog
from api.models.admin_notification import AdminNotification

# Import hierarchical data models
from api.models.environment import Environment
from api.models.assessment import Assessment, TestExecution
from api.models.campaign import Campaign
from api.models.database.test_case import TestCase

# Import MITRE models
from api.models.mitre import (
    MitreVersion,
    MitreTechnique,
    MitreTactic,
    MitreGroup,
    MitreSoftware,
    MitreMitigation,
    TechnologyDomain
)

# Import ATLAS models last since they don't have circular dependencies
from api.models.atlas import (
    AtlasVersion,
    AtlasTactic, 
    AtlasTechnique,
    AtlasMatrix,
    AtlasMatrixItem
)

# Export all models that are currently in use
__all__ = [
    "User",
    "UserSession",
    "UserPreference",
    "ErrorLog",
    "AuditLog",
    "AdminNotification",
    "Environment",
    "Assessment",
    "TestExecution",
    "Campaign",
    "TestCase",
    "MitreVersion",
    "MitreTechnique",
    "MitreTactic", 
    "MitreGroup",
    "MitreSoftware",
    "MitreMitigation",
    "TechnologyDomain",
    "AtlasVersion",
    "AtlasTactic",
    "AtlasTechnique", 
    "AtlasMatrix",
    "AtlasMatrixItem"
]