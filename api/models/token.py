"""Token models for tracking revoked tokens."""

from datetime import datetime
from sqlalchemy import Column, String, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from api.database import Base

class TokenBlacklist(Base):
    """Model for tracking revoked tokens."""

    __tablename__ = "token_blacklist"

    id = Column(String, primary_key=True)
    token = Column(String, unique=True, nullable=False)
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    revoked_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationship to user
    user = relationship("User", back_populates="blacklisted_tokens") 