"""
Database models for Enhanced Testcase Chaining & Sequencing.

This module defines the SQLAlchemy models for testcase chains, nodes, edges,
executions, and conditions that enable complex workflow management.
"""

from datetime import datetime
from typing import List, Optional

from sqlalchemy import (
    <PERSON><PERSON>an,
    CheckConstraint,
    Column,
    DateTime,
    Enum,
    Float,
    ForeignKey,
    Integer,
    JSON,
    String,
    Text,
    UniqueConstraint,
)
from sqlalchemy.orm import relationship

from api.database import Base
from api.models.database.mixins import SoftDeleteMixin, VersionMixin


class TestcaseChain(Base, SoftDeleteMixin, VersionMixin):
    """
    Database model for testcase chains.
    
    A testcase chain represents a sequence of related testcases that form
    a complete attack scenario or testing workflow.
    """
    
    __tablename__ = "testcase_chains"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    status = Column(
        Enum(
            "draft", "active", "completed", "archived", "paused",
            name="chain_status_enum"
        ),
        default="draft",
        nullable=False
    )
    
    # Chain metadata
    chain_type = Column(String(50), default="sequential", nullable=False)  # sequential, parallel, conditional
    max_execution_time_minutes = Column(Integer, default=60, nullable=False)
    retry_on_failure = Column(Boolean, default=False, nullable=False)
    auto_cleanup = Column(Boolean, default=True, nullable=False)
    
    # User tracking
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Constraints
    __table_args__ = (
        CheckConstraint(
            "max_execution_time_minutes > 0",
            name="ck_max_execution_time_positive"
        ),
    )
    
    # Relationships
    creator = relationship("User", foreign_keys=[created_by])
    chain_nodes = relationship(
        "TestcaseChainNode",
        back_populates="chain",
        cascade="all, delete-orphan",
        order_by="TestcaseChainNode.execution_order"
    )
    executions = relationship(
        "ChainExecution",
        back_populates="chain",
        cascade="all, delete-orphan"
    )


class TestcaseChainNode(Base, SoftDeleteMixin, VersionMixin):
    """
    Database model for nodes in a testcase chain.
    
    Each node represents a testcase within the chain and its position
    in the execution flow.
    """
    
    __tablename__ = "testcase_chain_nodes"
    
    id = Column(Integer, primary_key=True, index=True)
    chain_id = Column(Integer, ForeignKey("testcase_chains.id", ondelete="CASCADE"), nullable=False)
    testcase_id = Column(Integer, ForeignKey("test_cases.id", ondelete="CASCADE"), nullable=False)
    
    # Node configuration
    node_type = Column(
        Enum(
            "start", "standard", "conditional", "parallel", "end",
            name="node_type_enum"
        ),
        default="standard",
        nullable=False
    )
    execution_order = Column(Integer, default=0, nullable=False)
    
    # UI positioning
    position_x = Column(Float, default=0.0, nullable=False)
    position_y = Column(Float, default=0.0, nullable=False)
    
    # Conditional logic
    condition_expression = Column(Text, nullable=True)  # For conditional nodes
    timeout_minutes = Column(Integer, default=30, nullable=False)
    retry_count = Column(Integer, default=0, nullable=False)
    
    # Execution behavior
    continue_on_failure = Column(Boolean, default=False, nullable=False)
    required_for_completion = Column(Boolean, default=True, nullable=False)
    
    # Constraints
    __table_args__ = (
        CheckConstraint(
            "timeout_minutes > 0",
            name="ck_timeout_minutes_positive"
        ),
        CheckConstraint(
            "retry_count >= 0",
            name="ck_retry_count_non_negative"
        ),
        UniqueConstraint("chain_id", "testcase_id", name="uq_chain_testcase")
    )
    
    # Relationships
    chain = relationship("TestcaseChain", back_populates="chain_nodes")
    testcase = relationship("TestCase")
    outgoing_edges = relationship(
        "TestcaseChainEdge",
        foreign_keys="[TestcaseChainEdge.source_node_id]",
        back_populates="source_node",
        cascade="all, delete-orphan"
    )
    incoming_edges = relationship(
        "TestcaseChainEdge",
        foreign_keys="[TestcaseChainEdge.target_node_id]",
        back_populates="target_node",
        cascade="all, delete-orphan"
    )
    node_executions = relationship(
        "NodeExecution",
        back_populates="node",
        cascade="all, delete-orphan"
    )


class TestcaseChainEdge(Base, SoftDeleteMixin, VersionMixin):
    """
    Database model for edges connecting testcase chain nodes.
    
    Edges define the flow between nodes and can include conditions
    for when the edge should be traversed.
    """
    
    __tablename__ = "testcase_chain_edges"
    
    id = Column(Integer, primary_key=True, index=True)
    source_node_id = Column(Integer, ForeignKey("testcase_chain_nodes.id", ondelete="CASCADE"), nullable=False)
    target_node_id = Column(Integer, ForeignKey("testcase_chain_nodes.id", ondelete="CASCADE"), nullable=False)
    
    # Edge configuration
    edge_type = Column(
        Enum(
            "standard", "success_path", "failure_path", "conditional", "parallel",
            name="edge_type_enum"
        ),
        default="standard",
        nullable=False
    )
    condition = Column(Text, nullable=True)  # Condition for taking this edge
    weight = Column(Integer, default=1, nullable=False)  # For prioritization
    
    # Edge metadata
    label = Column(String(100), nullable=True)
    description = Column(Text, nullable=True)
    
    # Constraints
    __table_args__ = (
        CheckConstraint(
            "source_node_id != target_node_id",
            name="ck_no_self_loops"
        ),
        CheckConstraint(
            "weight > 0",
            name="ck_weight_positive"
        ),
        UniqueConstraint("source_node_id", "target_node_id", name="uq_source_target_edge")
    )
    
    # Relationships
    source_node = relationship(
        "TestcaseChainNode",
        foreign_keys=[source_node_id],
        back_populates="outgoing_edges"
    )
    target_node = relationship(
        "TestcaseChainNode",
        foreign_keys=[target_node_id],
        back_populates="incoming_edges"
    )


class ChainExecution(Base, SoftDeleteMixin, VersionMixin):
    """
    Database model for testcase chain executions.
    
    Tracks the execution of an entire chain including timing,
    status, and results.
    """
    
    __tablename__ = "chain_executions"
    
    id = Column(Integer, primary_key=True, index=True)
    chain_id = Column(Integer, ForeignKey("testcase_chains.id", ondelete="CASCADE"), nullable=False)
    
    # Execution tracking
    started_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    start_time = Column(DateTime, default=datetime.utcnow, nullable=False)
    end_time = Column(DateTime, nullable=True)
    
    # Status and results
    status = Column(
        Enum(
            "pending", "running", "completed", "failed", "aborted", "timeout",
            name="execution_status_enum"
        ),
        default="pending",
        nullable=False
    )
    
    # Execution metadata
    execution_context = Column(JSON, nullable=True)  # Environment, parameters, etc.
    result_summary = Column(JSON, nullable=True)  # Overall results
    error_message = Column(Text, nullable=True)
    
    # Performance metrics
    total_nodes = Column(Integer, default=0, nullable=False)
    completed_nodes = Column(Integer, default=0, nullable=False)
    failed_nodes = Column(Integer, default=0, nullable=False)
    skipped_nodes = Column(Integer, default=0, nullable=False)
    
    # Constraints
    __table_args__ = (
        CheckConstraint(
            "total_nodes >= 0",
            name="ck_total_nodes_non_negative"
        ),
        CheckConstraint(
            "completed_nodes >= 0",
            name="ck_completed_nodes_non_negative"
        ),
        CheckConstraint(
            "failed_nodes >= 0",
            name="ck_failed_nodes_non_negative"
        ),
        CheckConstraint(
            "skipped_nodes >= 0",
            name="ck_skipped_nodes_non_negative"
        ),
    )
    
    # Relationships
    chain = relationship("TestcaseChain", back_populates="executions")
    starter = relationship("User", foreign_keys=[started_by])
    node_executions = relationship(
        "NodeExecution",
        back_populates="chain_execution",
        cascade="all, delete-orphan"
    )


class NodeExecution(Base, SoftDeleteMixin, VersionMixin):
    """
    Database model for individual node executions within a chain.
    
    Tracks the execution of each testcase node including timing,
    status, and detailed results.
    """
    
    __tablename__ = "node_executions"
    
    id = Column(Integer, primary_key=True, index=True)
    chain_execution_id = Column(Integer, ForeignKey("chain_executions.id", ondelete="CASCADE"), nullable=False)
    node_id = Column(Integer, ForeignKey("testcase_chain_nodes.id", ondelete="CASCADE"), nullable=False)
    
    # Execution timing
    start_time = Column(DateTime, nullable=True)
    end_time = Column(DateTime, nullable=True)
    
    # Status and results
    status = Column(
        Enum(
            "pending", "running", "completed", "failed", "skipped", "timeout", "aborted",
            name="node_execution_status_enum"
        ),
        default="pending",
        nullable=False
    )
    
    # Execution data
    result_data = Column(JSON, nullable=True)
    error_message = Column(Text, nullable=True)
    output_logs = Column(Text, nullable=True)
    
    # Retry tracking
    attempt_number = Column(Integer, default=1, nullable=False)
    max_attempts = Column(Integer, default=1, nullable=False)
    
    # Constraints
    __table_args__ = (
        CheckConstraint(
            "attempt_number > 0",
            name="ck_attempt_number_positive"
        ),
        CheckConstraint(
            "max_attempts > 0",
            name="ck_max_attempts_positive"
        ),
        CheckConstraint(
            "attempt_number <= max_attempts",
            name="ck_attempt_within_max"
        ),
    )
    
    # Relationships
    chain_execution = relationship("ChainExecution", back_populates="node_executions")
    node = relationship("TestcaseChainNode", back_populates="node_executions")


class TestcaseCondition(Base, SoftDeleteMixin, VersionMixin):
    """
    Database model for testcase preconditions and postconditions.
    
    Defines conditions that must be met before or after testcase execution.
    """
    
    __tablename__ = "testcase_conditions"
    
    id = Column(Integer, primary_key=True, index=True)
    testcase_id = Column(Integer, ForeignKey("test_cases.id", ondelete="CASCADE"), nullable=False)
    
    # Condition configuration
    condition_type = Column(
        Enum(
            "precondition", "postcondition", "cleanup",
            name="condition_type_enum"
        ),
        nullable=False
    )
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Validation
    validation_script = Column(Text, nullable=True)  # Script to validate the condition
    validation_type = Column(String(50), default="script", nullable=False)  # script, api, manual
    required = Column(Boolean, default=True, nullable=False)  # Is this condition required?
    
    # Execution order for multiple conditions
    execution_order = Column(Integer, default=0, nullable=False)
    timeout_seconds = Column(Integer, default=30, nullable=False)
    
    # Constraints
    __table_args__ = (
        CheckConstraint(
            "timeout_seconds > 0",
            name="ck_timeout_seconds_positive"
        ),
    )
    
    # Relationships
    testcase = relationship("TestCase", back_populates="conditions")
