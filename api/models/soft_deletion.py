"""
Advanced Soft Deletion Framework Models.

This module defines the database models for the advanced soft deletion framework,
including retention policies, audit trails, and enhanced soft deletion capabilities.
"""

from datetime import datetime
from typing import Optional

from sqlalchemy import (
    Boolean,
    CheckConstraint,
    Column,
    DateTime,
    Enum,
    ForeignKey,
    Integer,
    JSON,
    String,
    Text,
    UniqueConstraint,
)
from sqlalchemy.orm import relationship

from api.database import Base


class SoftDeletionPolicy(Base):
    """
    Configurable retention policies for soft-deleted entities.
    
    This model defines how long different types of entities should be retained
    after soft deletion before being permanently purged.
    """
    
    __tablename__ = "soft_deletion_policies"
    
    id = Column(Integer, primary_key=True)
    entity_type = Column(String(100), nullable=False, unique=True)
    retention_period_days = Column(Integer, nullable=False)
    auto_purge_enabled = Column(Boolean, default=True, nullable=False)
    cascade_deletion = Column(Boolean, default=True, nullable=False)
    notification_enabled = Column(Boolean, default=True, nullable=False)
    notification_days_before = Column(Integer, default=7, nullable=False)
    
    # Policy metadata
    description = Column(Text, nullable=True)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Constraints
    __table_args__ = (
        CheckConstraint(
            "retention_period_days > 0",
            name="ck_retention_period_positive"
        ),
        CheckConstraint(
            "notification_days_before >= 0",
            name="ck_notification_days_non_negative"
        ),
        UniqueConstraint("entity_type", name="uq_entity_type_policy")
    )
    
    # Relationships
    creator = relationship("User", foreign_keys=[created_by])
    audit_logs = relationship("SoftDeletionAudit", back_populates="policy")


class SoftDeletionAudit(Base):
    """
    Audit trail for soft deletion operations.
    
    This model tracks all soft deletion, recovery, and purge operations
    for compliance and debugging purposes.
    """
    
    __tablename__ = "soft_deletion_audits"
    
    id = Column(Integer, primary_key=True)
    entity_type = Column(String(100), nullable=False)
    entity_id = Column(Integer, nullable=False)
    operation_type = Column(
        Enum(
            "soft_delete",
            "restore",
            "permanent_delete",
            "policy_change",
            "cascade_delete",
            "cascade_restore",
            name="operation_type_enum"
        ),
        nullable=False
    )
    
    # Operation details
    performed_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    reason = Column(Text, nullable=True)
    cascade_triggered = Column(Boolean, default=False, nullable=False)
    affected_entities_count = Column(Integer, default=1, nullable=False)
    
    # Policy information at time of operation
    policy_id = Column(Integer, ForeignKey("soft_deletion_policies.id"), nullable=True)
    retention_period_used = Column(Integer, nullable=True)
    
    # Additional metadata
    metadata = Column(JSON, nullable=True)
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    user_agent = Column(String(500), nullable=True)
    
    # Timestamps
    operation_time = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Constraints
    __table_args__ = (
        CheckConstraint(
            "affected_entities_count > 0",
            name="ck_affected_entities_positive"
        ),
    )
    
    # Relationships
    performer = relationship("User", foreign_keys=[performed_by])
    policy = relationship("SoftDeletionPolicy", back_populates="audit_logs")


class SoftDeletionSchedule(Base):
    """
    Scheduled operations for soft deletion management.
    
    This model tracks scheduled purge operations and their execution status.
    """
    
    __tablename__ = "soft_deletion_schedules"
    
    id = Column(Integer, primary_key=True)
    entity_type = Column(String(100), nullable=False)
    entity_id = Column(Integer, nullable=False)
    operation_type = Column(
        Enum(
            "purge",
            "notification",
            "policy_check",
            name="schedule_operation_type_enum"
        ),
        nullable=False
    )
    
    # Scheduling information
    scheduled_for = Column(DateTime, nullable=False)
    policy_id = Column(Integer, ForeignKey("soft_deletion_policies.id"), nullable=False)
    
    # Execution tracking
    status = Column(
        Enum(
            "pending",
            "in_progress",
            "completed",
            "failed",
            "cancelled",
            name="schedule_status_enum"
        ),
        default="pending",
        nullable=False
    )
    executed_at = Column(DateTime, nullable=True)
    execution_result = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0, nullable=False)
    max_retries = Column(Integer, default=3, nullable=False)
    
    # Metadata
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    notes = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Constraints
    __table_args__ = (
        CheckConstraint(
            "retry_count >= 0",
            name="ck_retry_count_non_negative"
        ),
        CheckConstraint(
            "max_retries >= 0",
            name="ck_max_retries_non_negative"
        ),
        CheckConstraint(
            "retry_count <= max_retries",
            name="ck_retry_count_within_max"
        ),
    )
    
    # Relationships
    policy = relationship("SoftDeletionPolicy")
    creator = relationship("User", foreign_keys=[created_by])


class SoftDeletionNotification(Base):
    """
    Notifications for upcoming purge operations.
    
    This model tracks notifications sent to users about upcoming
    permanent deletions of their soft-deleted entities.
    """
    
    __tablename__ = "soft_deletion_notifications"
    
    id = Column(Integer, primary_key=True)
    entity_type = Column(String(100), nullable=False)
    entity_id = Column(Integer, nullable=False)
    recipient_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Notification details
    notification_type = Column(
        Enum(
            "purge_warning",
            "purge_reminder",
            "purge_final_notice",
            "purge_completed",
            name="notification_type_enum"
        ),
        nullable=False
    )
    scheduled_purge_date = Column(DateTime, nullable=False)
    days_until_purge = Column(Integer, nullable=False)
    
    # Delivery tracking
    sent_at = Column(DateTime, nullable=True)
    delivery_status = Column(
        Enum(
            "pending",
            "sent",
            "delivered",
            "failed",
            "cancelled",
            name="delivery_status_enum"
        ),
        default="pending",
        nullable=False
    )
    delivery_method = Column(String(50), nullable=True)  # email, in_app, etc.
    
    # Content
    subject = Column(String(255), nullable=True)
    message = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Constraints
    __table_args__ = (
        CheckConstraint(
            "days_until_purge >= 0",
            name="ck_days_until_purge_non_negative"
        ),
    )
    
    # Relationships
    recipient = relationship("User")
