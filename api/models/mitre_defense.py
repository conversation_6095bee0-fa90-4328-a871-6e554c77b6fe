"""Models for MITRE Defense data."""
from sqlalchemy import <PERSON>umn, Integer, String, Text, ForeignKey, DateTime, Boolean
from sqlalchemy.orm import relationship
from api.models.base import Base
from datetime import datetime

class MitreDefenseVersion(Base):
    """Model for tracking MITRE Defense data versions."""
    __tablename__ = 'mitre_defense_versions'

    id = Column(Integer, primary_key=True)
    version = Column(String(50), unique=True, nullable=False)
    import_date = Column(DateTime, default=datetime.utcnow)
    is_current = Column(Boolean, default=False)

    controls = relationship("MitreControl", back_populates="version", cascade="all, delete-orphan")

class MitreControl(Base):
    """Model for MITRE Defense Controls."""
    __tablename__ = 'mitre_controls'

    id = Column(Integer, primary_key=True)
    external_id = Column(String(50), nullable=False)  # e.g., M1001
    name = Column(String(255), nullable=False)
    description = Column(Text)
    version_id = Column(Integer, ForeignKey('mitre_defense_versions.id', ondelete='CASCADE'), nullable=False)
    created = Column(DateTime)
    modified = Column(DateTime)
    revoked = Column(Boolean, default=False)
    deprecated = Column(Boolean, default=False)

    version = relationship("MitreDefenseVersion", back_populates="controls")
    relationships = relationship("MitreControlRelationship", back_populates="control", cascade="all, delete-orphan")

class MitreControlRelationship(Base):
    """Model for relationships between MITRE Defense Controls and other entities."""
    __tablename__ = 'mitre_control_relationships'

    id = Column(Integer, primary_key=True)
    control_id = Column(Integer, ForeignKey('mitre_controls.id', ondelete='CASCADE'), nullable=False)
    target_type = Column(String(50), nullable=False)  # e.g., 'technique', 'tactic'
    target_id = Column(String(255), nullable=False)  # External ID of the target
    relationship_type = Column(String(50), nullable=False)  # e.g., 'mitigates', 'relates-to'
    description = Column(Text)
    
    control = relationship("MitreControl", back_populates="relationships")
