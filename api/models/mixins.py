"""Mixins for model functionality."""
from sqlalchemy import <PERSON>umn, DateTime, Boolean, String, Integer
from datetime import datetime
from sqlalchemy.orm import Session
from typing import Optional
from sqlalchemy import select, update

class SoftDeleteMixin:
    """Mixin for soft delete functionality with CRUD operations."""
    created_on = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    is_deprecated = Column(Boolean, default=False)
    is_revoked = Column(Boolean, default=False)
    revoked_by_id = Column(Integer, nullable=True)  # ID of the object that replaces this one
    version = Column(String(10))  # Format: major.minor

    @classmethod
    def get_all(cls, db: Session, skip_deleted: bool = True):
        """Get all records, optionally including deleted ones."""
        query = select(cls)
        if skip_deleted:
            query = query.where(cls.deleted_at.is_(None))
        return db.execute(query).scalars().all()

    @classmethod
    def get_by_id(cls, db: Session, id: int, skip_deleted: bool = True) -> Optional["SoftDeleteMixin"]:
        """Get a record by ID, optionally including deleted ones."""
        query = select(cls).where(cls.id == id)
        if skip_deleted:
            query = query.where(cls.deleted_at.is_(None))
        return db.execute(query).scalar_one_or_none()

    def soft_delete(self, db: Session) -> None:
        """Soft delete a record."""
        self.deleted_at = datetime.utcnow()
        db.add(self)
        db.commit()
        db.refresh(self)

    def restore(self, db: Session) -> None:
        """Restore a soft-deleted record."""
        self.deleted_at = None
        db.add(self)
        db.commit()
        db.refresh(self)

    def update(self, db: Session, **kwargs) -> None:
        """Update record attributes."""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.updated_at = datetime.utcnow()
        db.add(self)
        db.commit()
        db.refresh(self)

    @classmethod
    def create(cls, db: Session, **kwargs) -> "SoftDeleteMixin":
        """Create a new record."""
        instance = cls(**kwargs)
        db.add(instance)
        db.commit()
        db.refresh(instance)
        return instance

    def mark_deprecated(self, db: Session, replacement_id: Optional[int] = None) -> None:
        """Mark record as deprecated with optional replacement."""
        self.is_deprecated = True
        if replacement_id:
            self.revoked_by_id = replacement_id
        self.updated_at = datetime.utcnow()
        db.add(self)
        db.commit()
        db.refresh(self)