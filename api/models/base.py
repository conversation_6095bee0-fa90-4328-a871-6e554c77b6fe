"""Base models for the application.

This module provides the foundational database models and mixins for the cybersecurity
data platform. It establishes common functionality like versioning and soft deletion
that can be used across all models in the application.

The module uses SQLAlchemy 2.0 style ORM patterns for all database interactions.
"""
from datetime import datetime
from typing import Optional, List, Any
from sqlalchemy import text, <PERSON><PERSON>n, Integer, String, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship, Session
from sqlalchemy.orm import DeclarativeBase, declared_attr
from api.database import Base
from sqlalchemy.sql import func

# Remove the circular import
# from api.models.testcase_chaining import TestcaseConditionDB

class VersionMixin:
    """Mixin to add versioning fields to models using SQLAlchemy 2.0 style."""

    @declared_attr
    def created_time(cls) -> Mapped[datetime]:
        """Track creation timestamp."""
        return mapped_column(
            nullable=False,
            server_default=text('CURRENT_TIMESTAMP')
        )

    @declared_attr
    def updated_time(cls) -> Mapped[datetime]:
        """Track last update timestamp."""
        return mapped_column(
            nullable=False,
            server_default=text('CURRENT_TIMESTAMP'),
            server_onupdate=text('CURRENT_TIMESTAMP')
        )

    @declared_attr
    def deleted_time(cls) -> Mapped[Optional[datetime]]:
        """Track soft deletion timestamp."""
        return mapped_column(nullable=True)

    def soft_delete(self, session: Session) -> None:
        """Mark the record as deleted without removing it from the database."""
        self.deleted_time = datetime.utcnow()
        session.add(self)
        session.commit()

    @classmethod
    def not_deleted(cls) -> Any:
        """Query filter to exclude soft-deleted records."""
        return cls.deleted_time.is_(None)

class CampaignDB(Base, VersionMixin):
    """Database model for security testing campaigns."""
    __tablename__ = "cl_campaign"
    __table_args__ = {'extend_existing': True}

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(nullable=False)
    description: Mapped[Optional[str]]
    status: Mapped[str] = mapped_column(server_default="active")

    # Relationships
    test_cases: Mapped[List["TestCaseDB"]] = relationship(
        back_populates="campaign",
        cascade="all, delete-orphan"
    )
    organizations: Mapped[List["OrganizationDB"]] = relationship(
        secondary="cl_campaign_to_organization",
        back_populates="campaigns"
    )
    assessments: Mapped[List["AssessmentDB"]] = relationship(
        secondary="assessment_to_campaign",
        back_populates="campaigns"
    )

class OrganizationDB(Base, VersionMixin):
    """Database model for organizations."""
    __tablename__ = "cl_organization"
    __table_args__ = {'extend_existing': True}

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(nullable=False)
    description: Mapped[Optional[str]]

    # Relationships
    campaigns: Mapped[List["CampaignDB"]] = relationship(
        secondary="cl_campaign_to_organization",
        back_populates="organizations"
    )

class CampaignToOrganizationDB(Base):
    """Association table for campaign-organization many-to-many relationship."""
    __tablename__ = "cl_campaign_to_organization"
    __table_args__ = {'extend_existing': True}

    campaign_id: Mapped[int] = mapped_column(
        ForeignKey("cl_campaign.id", ondelete="CASCADE"),
        primary_key=True
    )
    organization_id: Mapped[int] = mapped_column(
        ForeignKey("cl_organization.id", ondelete="CASCADE"),
        primary_key=True
    )

class TestCaseDB(Base, VersionMixin):
    """Database model for individual security test cases."""
    __tablename__ = "test_cases"
    __table_args__ = {'extend_existing': True}

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(nullable=False)
    description: Mapped[Optional[str]]
    campaign_id: Mapped[int] = mapped_column(
        ForeignKey("cl_campaign.id", ondelete="CASCADE")
    )
    expected_result: Mapped[str] = mapped_column(nullable=False)
    actual_result: Mapped[Optional[str]]
    status: Mapped[str] = mapped_column(server_default="pending")

    # Relationships
    campaign: Mapped["CampaignDB"] = relationship(back_populates="test_cases")
    # Use string type annotation to avoid circular import
    conditions: Mapped[List["TestcaseConditionDB"]] = relationship(
        back_populates="testcase",
        cascade="all, delete-orphan"
    )

class AssessmentDB(Base, VersionMixin):
    """Database model for security assessments."""
    __tablename__ = "assessments"
    __table_args__ = {'extend_existing': True}

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(nullable=False)
    description: Mapped[Optional[str]]
    target_system: Mapped[str] = mapped_column(nullable=False)
    assessment_type: Mapped[str] = mapped_column(nullable=False)  # e.g., "vulnerability", "penetration", "code review"
    start_date: Mapped[datetime] = mapped_column(default=func.now())
    end_date: Mapped[Optional[datetime]]
    status: Mapped[str] = mapped_column(server_default="planned")  # planned, in-progress, completed, cancelled
    
    # User who created the assessment
    created_by: Mapped[int] = mapped_column(ForeignKey("users.id"))
    
    # Relationships
    campaigns: Mapped[List["CampaignDB"]] = relationship(
        secondary="assessment_to_campaign",
        back_populates="assessments"
    )
    creator: Mapped["User"] = relationship(foreign_keys=[created_by])

class AssessmentToCampaignDB(Base):
    """Association table for assessment-campaign many-to-many relationship."""
    __tablename__ = "assessment_to_campaign"
    __table_args__ = {'extend_existing': True}

    assessment_id: Mapped[int] = mapped_column(
        ForeignKey("assessments.id", ondelete="CASCADE"),
        primary_key=True
    )
    campaign_id: Mapped[int] = mapped_column(
        ForeignKey("cl_campaign.id", ondelete="CASCADE"),
        primary_key=True
    )