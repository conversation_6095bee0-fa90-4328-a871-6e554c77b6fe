"""User model definition for the FastAPI cybersecurity data platform."""

from datetime import datetime, timedelta, timezone
from typing import Optional
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Enum as SQLEnum, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from passlib.context import Crypt<PERSON>ontext
from flask_login import UserMixin
import enum
import pyotp
import uuid
import json
import qrcode
import io
import base64
import secrets

from api.database import Base

# Configure password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class UserRole(str, enum.Enum):
    """Enumeration of available user roles."""
    ADMIN = "admin"
    ANALYST = "analyst"
    VIEWER = "viewer"

class User(UserMixin, Base):
    """User model for authentication and authorization."""

    __tablename__ = "users"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    username = Column(String, unique=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String(length=256), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    email_verified = Column(Boolean, default=False)

    # Enhanced user profile fields
    role = Column(SQLEnum(UserRole), nullable=False, default=UserRole.VIEWER)
    full_name = Column(String(length=100))
    bio = Column(Text, nullable=True)
    last_login = Column(DateTime(timezone=True), nullable=True)
    failed_login_attempts = Column(Integer, default=0)
    password_changed_at = Column(DateTime(timezone=True), nullable=True)

    # Security related fields
    account_locked = Column(Boolean, default=False)
    account_locked_at = Column(DateTime(timezone=True), nullable=True)
    last_failed_login = Column(DateTime(timezone=True), nullable=True)
    password_reset_token = Column(String(256), nullable=True)
    password_reset_expires = Column(DateTime(timezone=True), nullable=True)
    
    # Two-factor authentication fields
    two_factor_enabled = Column(Boolean, default=False)
    two_factor_secret = Column(String(32), nullable=True)
    backup_codes = Column(String(512), nullable=True)  # Comma-separated list of hashed backup codes

    # Session management relationship - Use string for late binding
    sessions = relationship(
        "UserSession",
        back_populates="user",
        cascade="all, delete-orphan",
        lazy="selectin"
    )

    # Relationship to preferences - Use string for late binding
    preferences = relationship(
        "UserPreference",
        back_populates="user",
        uselist=False,
        cascade="all, delete-orphan",
        lazy="selectin"
    )
    
    # Admin interface relationships
    audit_logs = relationship(
        "AuditLog",
        back_populates="user",
        cascade="all, delete-orphan",
        lazy="dynamic"
    )
    
    # Admin audit logs relationship
    admin_audit_logs = relationship(
        "AdminAuditLog",
        back_populates="user",
        cascade="all, delete-orphan",
        lazy="dynamic"
    )
    
    # Error logs relationship
    error_logs = relationship(
        "ErrorLog",
        back_populates="user",
        cascade="all, delete-orphan",
        lazy="dynamic"
    )
    
    admin_notifications = relationship(
        "AdminNotification",
        back_populates="user",
        cascade="all, delete-orphan",
        lazy="dynamic"
    )
    
    # Environment relationships
    created_environments = relationship(
        "Environment",
        foreign_keys="Environment.created_by",
        back_populates="creator",
        lazy="dynamic"
    )
    
    revoked_environments = relationship(
        "Environment",
        foreign_keys="Environment.revoked_by_id",
        back_populates="revoker",
        lazy="dynamic"
    )
    
    # Assessment relationships
    created_assessments = relationship(
        "Assessment",
        foreign_keys="Assessment.created_by",
        back_populates="creator",
        lazy="dynamic"
    )
    
    # Campaign relationships
    created_campaigns = relationship(
        "Campaign",
        foreign_keys="Campaign.created_by",
        back_populates="creator",
        lazy="dynamic"
    )
    
    # TestCase relationships
    created_test_cases = relationship(
        "TestCase",
        foreign_keys="TestCase.created_by",
        back_populates="creator",
        lazy="dynamic"
    )
    
    revoked_test_cases = relationship(
        "TestCase",
        foreign_keys="TestCase.revoked_by_id",
        back_populates="revoked_by",
        lazy="dynamic"
    )
    
    # TestExecution relationships
    executed_tests = relationship(
        "TestExecution",
        foreign_keys="TestExecution.executed_by",
        back_populates="executor",
        lazy="dynamic"
    )

    # Logging and Testing relationships
    log_entries = relationship("LogEntry", back_populates="user")
    test_scenarios = relationship("TestScenario", back_populates="creator")

    # New relationship for blacklisted tokens
    blacklisted_tokens = relationship("TokenBlacklist", back_populates="user")

    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        return pwd_context.verify(plain_password, hashed_password)

    @staticmethod
    def get_password_hash(password: str) -> str:
        """Generate a password hash."""
        return pwd_context.hash(password)

    def set_password(self, password: str) -> None:
        """Set user's password, converting it to a hash first."""
        self.hashed_password = self.get_password_hash(password)
        self.password_changed_at = datetime.now(tz=timezone.utc)
        # Reset security-related fields
        self.failed_login_attempts = 0
        self.account_locked = False
        self.account_locked_at = None
        self.password_reset_token = None
        self.password_reset_expires = None

    def check_password(self, password: str) -> bool:
        """Check if the provided password matches the user's password."""
        if self.account_locked:
            return False

        valid = self.verify_password(password, self.hashed_password)
        if valid:
            self.last_login = datetime.now(tz=timezone.utc)
            self.failed_login_attempts = 0
            self.last_failed_login = None
        else:
            self.failed_login_attempts += 1
            self.last_failed_login = datetime.now(tz=timezone.utc)

            # Lock account after 5 failed attempts
            if self.failed_login_attempts >= 5:
                self.account_locked = True
                self.account_locked_at = datetime.now(tz=timezone.utc)

        return valid
        
    # Two-factor authentication methods
    def generate_two_factor_secret(self) -> str:
        """Generate a new 2FA secret key."""
        self.two_factor_secret = pyotp.random_base32()
        return self.two_factor_secret
        
    def get_totp_uri(self) -> str:
        """Get the TOTP URI for QR code generation."""
        if not self.two_factor_secret:
            self.generate_two_factor_secret()
        return pyotp.totp.TOTP(self.two_factor_secret).provisioning_uri(
            name=self.email,
            issuer_name="RegrigorRigor"
        )
        
    def verify_totp(self, token: str) -> bool:
        """Verify a TOTP token."""
        if not self.two_factor_secret:
            return False
        totp = pyotp.TOTP(self.two_factor_secret)
        return totp.verify(token)
        
    def generate_backup_codes(self, count: int = 10) -> list[str]:
        """Generate backup codes for 2FA recovery."""
        import secrets
        import string
        
        # Generate random backup codes
        codes = []
        for _ in range(count):
            code = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(10))
            codes.append(code)
            
        # Store hashed backup codes
        hashed_codes = [self.get_password_hash(code) for code in codes]
        self.backup_codes = ','.join(hashed_codes)
        
        return codes
        
    def verify_backup_code(self, code: str) -> bool:
        """Verify a backup code and remove it if valid."""
        if not self.backup_codes:
            return False
            
        hashed_codes = self.backup_codes.split(',')
        for i, hashed_code in enumerate(hashed_codes):
            if self.verify_password(code, hashed_code):
                # Remove the used backup code
                hashed_codes.pop(i)
                self.backup_codes = ','.join(hashed_codes)
                return True
                
        return False
        
    def enable_two_factor(self) -> None:
        """Enable two-factor authentication."""
        if not self.two_factor_secret:
            self.generate_two_factor_secret()
        self.two_factor_enabled = True
        
    def disable_two_factor(self) -> None:
        """Disable two-factor authentication."""
        self.two_factor_enabled = False
        self.two_factor_secret = None
        self.backup_codes = None

    def has_role(self, role: UserRole) -> bool:
        """Check if user has a specific role."""
        return self.role == role

    def is_admin(self) -> bool:
        """Check if user has admin role."""
        return self.role == UserRole.ADMIN

    def is_analyst(self) -> bool:
        """Check if user has analyst role."""
        return self.role == UserRole.ANALYST

    def can_access_resource(self, required_roles: list[UserRole]) -> bool:
        """Check if user has access to a resource based on their role."""
        return self.role in required_roles or self.is_admin()

    def verify_email(self) -> None:
        """Verify user's email address."""
        self.email_verified = True

    def update_profile(self, full_name: Optional[str] = None, bio: Optional[str] = None) -> None:
        """Update user profile information."""
        if full_name is not None:
            self.full_name = full_name
        if bio is not None:
            self.bio = bio

    def deactivate(self) -> None:
        """Deactivate user account."""
        self.is_active = False

    def activate(self) -> None:
        """Activate user account."""
        self.is_active = True

    def unlock_account(self) -> None:
        """Unlock a locked account."""
        self.account_locked = False
        self.account_locked_at = None
        self.failed_login_attempts = 0
        self.last_failed_login = None

    def set_password_reset_token(self, token: str, expires_in_hours: int = 24) -> None:
        """Set password reset token with expiration."""
        self.password_reset_token = token
        self.password_reset_expires = datetime.now(tz=timezone.utc) + timedelta(hours=expires_in_hours)

    def clear_password_reset_token(self) -> None:
        """Clear password reset token and expiration."""
        self.password_reset_token = None
        self.password_reset_expires = None

    def password_reset_token_valid(self) -> bool:
        """Check if password reset token is valid and not expired."""
        if not self.password_reset_token or not self.password_reset_expires:
            return False
        return datetime.now(tz=timezone.utc) < self.password_reset_expires

    def get_active_sessions(self) -> list["UserSession"]:
        """Get all active sessions for the user."""
        return [session for session in self.sessions if session.is_active]

    def terminate_other_sessions(self, current_session_id: str) -> None:
        """Terminate all sessions except the current one."""
        for session in self.sessions:
            if session.id != current_session_id and session.is_active:
                session.terminate()

    def generate_totp_secret(self):
        """Generate a new TOTP secret for two-factor authentication.
        
        Returns:
            Tuple of (secret, uri, backup_codes)
        """
        # Generate a new secret
        secret = pyotp.random_base32()
        
        # Generate the URI for QR code
        totp = pyotp.TOTP(secret)
        uri = totp.provisioning_uri(name=self.email, issuer_name="Security Testing Platform")
        
        # Generate backup codes
        backup_codes = []
        hashed_backup_codes = []
        
        for _ in range(10):
            code = secrets.token_hex(5)  # 10 characters
            backup_codes.append(code)
            hashed_backup_codes.append(pwd_context.hash(code))
        
        # Store hashed backup codes
        self.backup_codes = json.dumps(hashed_backup_codes)
        
        return secret, uri, backup_codes
    
    def regenerate_backup_codes(self):
        """Regenerate backup codes for two-factor authentication.
        
        Returns:
            List of new backup codes
        """
        backup_codes = []
        hashed_backup_codes = []
        
        for _ in range(10):
            code = secrets.token_hex(5)  # 10 characters
            backup_codes.append(code)
            hashed_backup_codes.append(pwd_context.hash(code))
        
        # Store hashed backup codes
        self.backup_codes = json.dumps(hashed_backup_codes)
        
        return backup_codes

    def __repr__(self):
        return f"<User(id={self.id}, username={self.username})>"

class UserSession(Base):
    """User session model for tracking active sessions."""
    __tablename__ = "user_sessions"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id", ondelete="CASCADE"))
    session_token = Column(String, unique=True, index=True)
    expires_at = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)
    ip_address = Column(String, nullable=True)
    user_agent = Column(String, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="sessions")

class UserPreference(Base):
    """User preferences model for storing user-specific settings."""
    __tablename__ = "user_preferences"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id", ondelete="CASCADE"), unique=True)
    theme = Column(String, default="light")
    notifications_enabled = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="preferences")