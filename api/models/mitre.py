"""Models for MITRE ATT&CK Framework and campaign tracking data."""

from datetime import datetime
from enum import Enum as PyEnum

from sqlalchemy import (
    JSON,
    Boolean,
    CheckConstraint,
    Column,
    DateTime,
    Enum,
    Float,
    <PERSON><PERSON>ey,
    Integer,
    String,
    Table,
    Text,
    UniqueConstraint,
)
from sqlalchemy.orm import relationship

from api.database import Base
from api.models.mixins import SoftDeleteMixin
from api.models.relationships import (
    group_technique_association,
    mitigation_technique_association,
    software_technique_association,
)


# Technology domain enumeration
class TechnologyDomain(str, PyEnum):
    """MITRE ATT&CK Technology Domains."""

    ENTERPRISE = "enterprise"
    MOBILE = "mobile"
    ICS = "ics"


# Scoring category enumeration
class ScoreCategory(str, PyEnum):
    """Categories for technique scoring."""

    IMPACT = "impact"
    LIKELIHOOD = "likelihood"
    DETECTABILITY = "detectability"
    EXPLOITABILITY = "exploitability"
    CUSTOM = "custom"


# Association tables
technique_tactic_association = Table(
    "technique_tactic_association",
    Base.metadata,
    Column(
        "technique_id", Inte<PERSON>, <PERSON><PERSON><PERSON>("mitre_techniques.id", ondelete="CASCADE")
    ),
    Column("tactic_id", Integer, ForeignKey("mitre_tactics.id", ondelete="CASCADE"))
)


class MitreVersion(Base, SoftDeleteMixin):
    """Track MITRE ATT&CK Framework versions."""

    __tablename__ = "mitre_versions"

    id = Column(Integer, primary_key=True)
    version = Column(String, nullable=False, unique=True)
    import_date = Column(DateTime, default=datetime.utcnow)
    is_current = Column(Boolean, default=False)
    technology_domain = Column(
        Enum(TechnologyDomain, values_callable=lambda obj: [e.value for e in obj]),
        nullable=False,
        default=TechnologyDomain.ENTERPRISE,
    )

    # Relationships with cascade delete
    techniques = relationship(
        "MitreTechnique", back_populates="version", cascade="all, delete-orphan"
    )
    tactics = relationship(
        "MitreTactic", back_populates="version", cascade="all, delete-orphan"
    )
    groups = relationship(
        "MitreGroup", back_populates="version", cascade="all, delete-orphan"
    )
    software = relationship(
        "MitreSoftware", back_populates="version", cascade="all, delete-orphan"
    )
    mitigations = relationship(
        "MitreMitigation", back_populates="version", cascade="all, delete-orphan"
    )


class MitreTechnique(Base, SoftDeleteMixin):
    """MITRE ATT&CK Technique."""

    __tablename__ = "mitre_techniques"

    id = Column(Integer, primary_key=True)
    technique_id = Column(String(50), nullable=False)  # e.g., T1566
    name = Column(String(255), nullable=False)
    description = Column(String(2000))
    version_id = Column(Integer, ForeignKey("mitre_versions.id", ondelete="CASCADE"))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    extra_data = Column(JSON)  # Additional MITRE data
    detection = Column(String(2000))  # Detection strategies
    platforms = Column(JSON)  # Applicable platforms

    # Unique constraint for technique_id within a version
    __table_args__ = (
        UniqueConstraint("technique_id", "version_id", name="uix_technique_version"),
    )

    # Relationships
    version = relationship("MitreVersion", back_populates="techniques")
    tactics = relationship(
        "MitreTactic",
        secondary=technique_tactic_association,
        back_populates="techniques",
    )
    groups = relationship(
        "MitreGroup",
        secondary=group_technique_association,
        back_populates="techniques",
    )
    software = relationship(
        "MitreSoftware",
        secondary=software_technique_association,
        back_populates="techniques",
    )
    mitigations = relationship(
        "MitreMitigation",
        secondary=mitigation_technique_association,
        back_populates="techniques",
    )
    scores = relationship(
        "TechniqueScore", back_populates="technique", cascade="all, delete-orphan"
    )


class MitreTactic(Base, SoftDeleteMixin):
    """MITRE ATT&CK Tactic."""

    __tablename__ = "mitre_tactics"

    id = Column(Integer, primary_key=True)
    tactic_id = Column(String, nullable=False)  # MITRE ID (e.g., TA0001)
    name = Column(String, nullable=False)
    description = Column(String)
    version_id = Column(Integer, ForeignKey("mitre_versions.id", ondelete="CASCADE"))
    data = Column(JSON)  # Store additional MITRE data
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Composite unique constraint
    __table_args__ = (
        UniqueConstraint("tactic_id", "version_id", name="uix_tactic_version"),
    )

    # Relationships
    version = relationship("MitreVersion", back_populates="tactics")
    techniques = relationship(
        "MitreTechnique",
        secondary=technique_tactic_association,
        back_populates="tactics",
    )


class MitreGroup(Base, SoftDeleteMixin):
    """MITRE ATT&CK Group (Threat Actor)."""

    __tablename__ = "mitre_groups"

    id = Column(Integer, primary_key=True)
    group_id = Column(String(50), nullable=False)  # e.g., G0001
    name = Column(String(255), nullable=False)
    description = Column(String(2000))
    version_id = Column(Integer, ForeignKey("mitre_versions.id", ondelete="CASCADE"))
    aliases = Column(JSON)  # Alternative names
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    extra_data = Column(JSON)  # Additional MITRE data

    # Unique constraint for group_id within a version
    __table_args__ = (
        UniqueConstraint("group_id", "version_id", name="uix_group_version"),
    )

    # Relationships
    version = relationship("MitreVersion", back_populates="groups")
    techniques = relationship(
        "MitreTechnique",
        secondary=group_technique_association,
        back_populates="groups",
    )


class MitreSoftware(Base, SoftDeleteMixin):
    """MITRE ATT&CK Software."""

    __tablename__ = "mitre_software"

    id = Column(Integer, primary_key=True)
    software_id = Column(String(50), nullable=False)  # e.g., S0001
    name = Column(String(255), nullable=False)
    description = Column(String(2000))
    version_id = Column(Integer, ForeignKey("mitre_versions.id", ondelete="CASCADE"))
    software_type = Column(String(50))  # malware, tool, etc.
    platforms = Column(JSON)  # Operating systems/platforms
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    extra_data = Column(JSON)  # Additional MITRE data

    # Unique constraint for software_id within a version
    __table_args__ = (
        UniqueConstraint("software_id", "version_id", name="uix_software_version"),
    )

    # Relationships
    version = relationship("MitreVersion", back_populates="software")
    techniques = relationship(
        "MitreTechnique",
        secondary=software_technique_association,
        back_populates="software",
    )


class MitreMitigation(Base, SoftDeleteMixin):
    """MITRE ATT&CK Mitigation."""

    __tablename__ = "mitre_mitigations"

    id = Column(Integer, primary_key=True)
    mitigation_id = Column(String(50), nullable=False)  # e.g., M1001
    name = Column(String(255), nullable=False)
    description = Column(String(2000))
    version_id = Column(Integer, ForeignKey("mitre_versions.id", ondelete="CASCADE"))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    extra_data = Column(JSON)  # Additional MITRE data

    # Unique constraint for mitigation_id within a version
    __table_args__ = (
        UniqueConstraint("mitigation_id", "version_id", name="uix_mitigation_version"),
    )

    # Relationships
    version = relationship("MitreVersion", back_populates="mitigations")
    techniques = relationship(
        "MitreTechnique",
        secondary=mitigation_technique_association,
        back_populates="mitigations",
    )


class TechniqueScore(Base):
    """Scoring for MITRE ATT&CK Techniques."""

    __tablename__ = "technique_scores"

    id = Column(Integer, primary_key=True)
    technique_id = Column(
        Integer, ForeignKey("mitre_techniques.id", ondelete="CASCADE"), nullable=False
    )
    category = Column(
        Enum(ScoreCategory, values_callable=lambda obj: [e.value for e in obj]),
        nullable=False,
        default=ScoreCategory.IMPACT,
    )
    score = Column(Float, nullable=False)  # Score value (0-10)
    weight = Column(Float, default=1.0)  # Weight for this score category (0-1)
    notes = Column(Text)  # Optional notes explaining the score
    created_by = Column(Integer, ForeignKey("users.id"))  # User who created the score
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Unique constraint for technique_id and category
    __table_args__ = (
        UniqueConstraint(
            "technique_id", "category", name="uix_technique_score_category"
        ),
    )

    # Relationships
    technique = relationship("MitreTechnique", back_populates="scores")


class AttackTechniqueResult(Base):
    """
    Attack technique execution results for heat map generation.

    This model stores the results of executing MITRE ATT&CK techniques
    during campaigns or assessments, including effectiveness metrics
    and execution statistics.
    """

    __tablename__ = "attack_technique_results"

    id = Column(Integer, primary_key=True)
    technique_id = Column(String(50), nullable=False)  # MITRE technique ID (e.g., T1566)
    campaign_id = Column(Integer, ForeignKey("campaigns.id", ondelete="CASCADE"), nullable=False)
    assessment_id = Column(Integer, ForeignKey("assessments.id", ondelete="CASCADE"), nullable=True)

    # Effectiveness metrics (0-1 scale)
    effectiveness_score = Column(Float, nullable=True, default=0.0)
    detection_rate = Column(Float, nullable=True, default=0.0)
    prevention_rate = Column(Float, nullable=True, default=0.0)

    # Execution statistics
    execution_count = Column(Integer, nullable=True, default=0)
    success_count = Column(Integer, nullable=True, default=0)
    detection_count = Column(Integer, nullable=True, default=0)
    prevention_count = Column(Integer, nullable=True, default=0)

    # Metadata
    last_tested = Column(DateTime, nullable=True)
    test_environment = Column(String(100), nullable=True)
    notes = Column(Text, nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)  # Soft delete timestamp

    # Constraints
    __table_args__ = (
        UniqueConstraint(
            "technique_id", "campaign_id",
            name="uix_technique_campaign_result"
        ),
        CheckConstraint(
            "effectiveness_score >= 0 AND effectiveness_score <= 1",
            name="ck_effectiveness_score_range"
        ),
        CheckConstraint(
            "detection_rate >= 0 AND detection_rate <= 1",
            name="ck_detection_rate_range"
        ),
        CheckConstraint(
            "prevention_rate >= 0 AND prevention_rate <= 1",
            name="ck_prevention_rate_range"
        ),
        CheckConstraint(
            "execution_count >= 0",
            name="ck_execution_count_positive"
        ),
        CheckConstraint(
            "success_count >= 0 AND success_count <= execution_count",
            name="ck_success_count_valid"
        ),
        CheckConstraint(
            "detection_count >= 0 AND detection_count <= execution_count",
            name="ck_detection_count_valid"
        ),
        CheckConstraint(
            "prevention_count >= 0 AND prevention_count <= execution_count",
            name="ck_prevention_count_valid"
        )
    )

    # Relationships
    campaign = relationship("Campaign", back_populates="technique_results")
    assessment = relationship("Assessment", back_populates="technique_results")


class HeatMapSnapshot(Base):
    """
    Heat map snapshots for historical comparison and analysis.

    This model stores aggregated heat map data at specific points in time,
    enabling historical comparison and trend analysis.
    """

    __tablename__ = "heatmap_snapshots"

    id = Column(Integer, primary_key=True)
    campaign_id = Column(Integer, ForeignKey("campaigns.id", ondelete="CASCADE"), nullable=True)
    assessment_id = Column(Integer, ForeignKey("assessments.id", ondelete="CASCADE"), nullable=True)

    # Snapshot metadata
    snapshot_date = Column(DateTime, default=datetime.utcnow)
    snapshot_type = Column(String(50), nullable=True, default="campaign")
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)

    # Aggregated data (stored as JSON for flexibility)
    technique_coverage = Column(JSON, nullable=True)  # technique_id -> coverage_score
    tactic_effectiveness = Column(JSON, nullable=True)  # tactic_id -> effectiveness_score
    overall_metrics = Column(JSON, nullable=True)  # overall statistics

    # Comparison data
    baseline_snapshot_id = Column(Integer, ForeignKey("heatmap_snapshots.id"), nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)  # Soft delete timestamp

    # Constraints
    __table_args__ = (
        CheckConstraint(
            "snapshot_type IN ('campaign', 'assessment', 'organization', 'comparison')",
            name="ck_snapshot_type_valid"
        ),
        CheckConstraint(
            "campaign_id IS NOT NULL OR assessment_id IS NOT NULL",
            name="ck_campaign_or_assessment_required"
        )
    )

    # Relationships
    campaign = relationship("Campaign", back_populates="heat_map_snapshots")
    assessment = relationship("Assessment", back_populates="heat_map_snapshots")
    baseline_snapshot = relationship("HeatMapSnapshot", remote_side=[id])
