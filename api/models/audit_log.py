"""Audit log model for tracking user actions."""

from datetime import datetime
from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from api.database import Base

class AuditLog(Base):
    """Model for tracking user actions in the system.
    
    Attributes:
        id: Primary key
        action: Type of action performed (e.g., login, create, update, delete)
        resource_type: Type of resource affected (e.g., user, technique, campaign)
        resource_id: ID of the resource affected
        details: Additional details about the action as JSON
        user_id: ID of the user who performed the action
        ip_address: IP address of the user
        user_agent: User agent of the user's browser
        created_at: Timestamp when the action was performed
    """
    __tablename__ = "audit_logs"

    id = Column(Integer, primary_key=True, index=True)
    action = Column(String(50), nullable=False)
    resource_type = Column(String(50), nullable=False)
    resource_id = Column(String(50), nullable=True)
    details = Column(JSON, nullable=True)
    user_id = Column(String, ForeignKey("users.id"), nullable=True)
    ip_address = Column(String(50), nullable=True)
    user_agent = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    user = relationship("User", back_populates="audit_logs")

    def __repr__(self):
        """String representation of the audit log."""
        return f"<AuditLog(id={self.id}, action='{self.action}', resource_type='{self.resource_type}')>" 