"""
Environment model for the RegressionRigor platform.

This module defines the Environment class, which represents a testing environment
in which security assessments are conducted.
"""

from datetime import datetime
from typing import Dict, List, Optional
from sqlalchemy import Column, String, DateTime, Integer, <PERSON><PERSON>an, <PERSON><PERSON><PERSON>, <PERSON>um, Text
from sqlalchemy.orm import relationship, Session
import enum

from api.database import Base
from api.models.user import User


class EnvironmentType(str, enum.Enum):
    """Enumeration of possible environment types."""
    PRODUCTION = "production"
    STAGING = "staging"
    DEVELOPMENT = "development"
    TEST = "test"
    OTHER = "other"


class EnvironmentStatus(str, enum.Enum):
    """Enumeration of possible environment statuses."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DEPRECATED = "deprecated"


class Environment(Base):
    """
    Environment model representing a testing environment.
    
    Attributes:
        id: Unique identifier for the environment
        name: Name of the environment
        description: Detailed description of the environment
        type: Type of environment (production, staging, development, test, other)
        status: Status of the environment (active, inactive, deprecated)
        created_by: ID of the user who created the environment
        created_at: Timestamp when the environment was created
        updated_at: Timestamp when the environment was last updated
        deleted_at: Timestamp when the environment was soft-deleted, or None if not deleted
        is_deprecated: Flag indicating if the environment is deprecated
        is_revoked: Flag indicating if the environment is revoked
        revoked_by_id: ID of the user who revoked the environment, or None if not revoked
        version: Version number for optimistic locking
    """
    __tablename__ = "environments"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    type = Column(Enum(EnvironmentType), nullable=False, default=EnvironmentType.TEST)
    status = Column(Enum(EnvironmentStatus), nullable=False, default=EnvironmentStatus.ACTIVE)
    
    created_by = Column(String, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    is_deprecated = Column(Boolean, nullable=False, default=False)
    is_revoked = Column(Boolean, nullable=False, default=False)
    revoked_by_id = Column(String, ForeignKey("users.id"), nullable=True)
    
    version = Column(Integer, nullable=False, default=1)
    
    # Relationships
    creator = relationship("User", foreign_keys=[created_by], back_populates="created_environments")
    revoker = relationship("User", foreign_keys=[revoked_by_id], back_populates="revoked_environments")
    assessments = relationship("Assessment", back_populates="environment", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        """Return string representation of the environment."""
        return f"<Environment(id={self.id}, name='{self.name}', type='{self.type}', status='{self.status}')>"
    
    def to_dict(self) -> Dict:
        """Convert the environment to a dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "type": self.type,
            "status": self.status,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "deleted_at": self.deleted_at.isoformat() if self.deleted_at else None,
            "is_deprecated": self.is_deprecated,
            "is_revoked": self.is_revoked,
            "revoked_by_id": self.revoked_by_id,
            "version": self.version
        }
    
    def soft_delete(self, db: Session) -> None:
        """
        Soft delete the environment by setting deleted_at timestamp.
        
        Args:
            db: Database session
        """
        self.deleted_at = datetime.utcnow()
        db.add(self)
        db.commit()
        db.refresh(self)
    
    def restore(self, db: Session) -> None:
        """
        Restore a soft-deleted environment by clearing deleted_at timestamp.
        
        Args:
            db: Database session
        """
        self.deleted_at = None
        db.add(self)
        db.commit()
        db.refresh(self) 