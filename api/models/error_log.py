"""Error log model for storing application errors."""

from datetime import datetime
from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from api.database import Base

class ErrorLog(Base):
    """Model for storing application errors.
    
    Attributes:
        id: Primary key
        error_type: Type of error (e.g., SQLAlchemyError, ValueError)
        error_message: Error message
        stack_trace: Full stack trace
        user_id: ID of the user who triggered the error (if authenticated)
        endpoint: API endpoint or route where the error occurred
        request_method: HTTP method (GET, POST, etc.)
        request_data: Request data as JSO<PERSON>
        created_at: Timestamp when the error was logged
    """
    __tablename__ = "error_logs"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    error_type = Column(String(255), nullable=False)
    error_message = Column(Text, nullable=False)
    stack_trace = Column(Text, nullable=True)
    user_id = Column(String, ForeignKey("users.id"), nullable=True)
    endpoint = Column(String(255), nullable=True)
    request_method = Column(String(10), nullable=True)
    request_data = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    user = relationship("User", back_populates="error_logs")

    def __repr__(self):
        """String representation of the error log."""
        return f"<ErrorLog(id={self.id}, error_type='{self.error_type}', endpoint='{self.endpoint}')>" 