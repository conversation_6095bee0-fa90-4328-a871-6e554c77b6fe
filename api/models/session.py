"""User session and device tracking models."""
from datetime import datetime, timedelta
import uuid
from sqlalchemy import Column, Integer, String, DateTime, Boolean, ForeignKey, Table
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func

from api.database import Base

class DeviceInfo(Base):
    """Store device fingerprint information."""
    __tablename__ = 'device_info'
    __table_args__ = {'extend_existing': True}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_agent = Column(String, nullable=False)
    ip_address = Column(String, nullable=False)
    device_type = Column(String)  # mobile, desktop, tablet
    os_info = Column(String)
    browser_info = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_seen = Column(DateTime, default=datetime.utcnow)

    # Relationships
    sessions = relationship("UserSession", back_populates="device")

class UserSession(Base):
    """Model for tracking user sessions."""

    __tablename__ = "user_sessions"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    token_hash = Column(String(256), nullable=False)
    ip_address = Column(String(45), nullable=False)
    user_agent = Column(String(255), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_activity = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=False)
    is_active = Column(Boolean, default=True)
    revoked_at = Column(DateTime(timezone=True), nullable=True)

    # Relationship to user
    user = relationship("User", back_populates="sessions")

    def update_activity(self) -> None:
        """Update the last activity timestamp."""
        self.last_activity = datetime.utcnow()

    def is_expired(self) -> bool:
        """Check if the session has expired."""
        return datetime.utcnow() > self.expires_at

    def revoke(self) -> None:
        """Revoke the session."""
        self.is_active = False
        self.revoked_at = datetime.utcnow()

    @staticmethod
    def create_session(
        user_id: str,
        token_hash: str,
        ip_address: str,
        user_agent: str,
        expires_in_hours: int = 24
    ) -> "UserSession":
        """Create a new session.
        
        Args:
            user_id: ID of the user
            token_hash: Hash of the session token
            ip_address: IP address of the client
            user_agent: User agent string
            expires_in_hours: Number of hours until session expires
            
        Returns:
            New UserSession instance
        """
        return UserSession(
            user_id=user_id,
            token_hash=token_hash,
            ip_address=ip_address,
            user_agent=user_agent,
            expires_at=datetime.utcnow() + timedelta(hours=expires_in_hours)
        )
