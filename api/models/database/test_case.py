"""
Database models for test cases.

This module defines the SQLAlchemy models for test cases and related entities.
"""
from datetime import datetime
from typing import List, Optional, Dict, Any

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, Enum, Table
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship, Mapped
from sqlalchemy.sql import func

from api.database import Base
from api.models.database.mixins import SoftDeleteMixin, VersionMixin, DeprecationMixin, RevocationMixin


# Association table for many-to-many relationship between test cases and campaigns
test_case_campaign = Table(
    "campaign_test_cases",
    Base.metadata,
    Column("test_case_id", Integer, ForeignKey("test_cases.id", ondelete="CASCADE"), primary_key=True),
    Column("campaign_id", Integer, Foreign<PERSON>ey("campaigns.id", ondelete="CASCADE"), primary_key=True),
    extend_existing=True
)


class TestCase(Base, SoftDeleteMixin, VersionMixin, DeprecationMixin, RevocationMixin):
    """
    Database model for test cases.
    
    Attributes:
        id: Unique identifier for the test case
        name: Name of the test case
        description: Detailed description of the test case
        type: Type of test case (manual, automated, hybrid)
        status: Status of the test case (draft, active, deprecated, archived)
        priority: Priority of the test case (low, medium, high, critical)
        complexity: Complexity of the test case (simple, moderate, complex)
        prerequisites: Prerequisites for running the test case
        steps: Steps to execute the test case
        expected_result: Expected result of the test case
        tags: Tags associated with the test case
        mitre_techniques: MITRE ATT&CK techniques associated with the test case
        created_by: ID of the user who created the test case
        created_at: Timestamp when the test case was created
        updated_at: Timestamp of the last test case update
    """
    __tablename__ = "test_cases"
    __table_args__ = {'extend_existing': True}
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    type = Column(Enum("manual", "automated", "hybrid", name="testcasetype"), nullable=False, default="manual")
    status = Column(Enum("draft", "active", "deprecated", "archived", name="testcasestatus"), nullable=False, default="draft")
    priority = Column(Enum("low", "medium", "high", "critical", name="testcasepriority"), nullable=False, default="medium")
    complexity = Column(Enum("simple", "moderate", "complex", name="testcasecomplexity"), nullable=False, default="moderate")
    prerequisites = Column(Text, nullable=True)
    steps = Column(JSONB, nullable=True)
    expected_result = Column(Text, nullable=False)
    actual_result = Column(Text, nullable=True)
    tags = Column(JSONB, nullable=True)
    mitre_techniques = Column(JSONB, nullable=True)
    
    # Foreign keys
    created_by = Column(String, ForeignKey("users.id"), nullable=True)
    revoked_by_id = Column(String, ForeignKey("users.id"), nullable=True)
    
    # Relationships
    campaigns = relationship("Campaign", secondary=test_case_campaign, back_populates="test_cases")
    creator = relationship("User", foreign_keys=[created_by], back_populates="created_test_cases")
    revoked_by = relationship("User", foreign_keys=[revoked_by_id], back_populates="revoked_test_cases")
    executions = relationship("TestExecution", back_populates="test_case")
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the test case to a dictionary.
        
        Returns:
            Dict[str, Any]: Dictionary representation of the test case
        """
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "type": self.type,
            "status": self.status,
            "priority": self.priority,
            "complexity": self.complexity,
            "prerequisites": self.prerequisites,
            "steps": self.steps or [],
            "expected_result": self.expected_result,
            "actual_result": self.actual_result,
            "tags": self.tags or [],
            "mitre_techniques": self.mitre_techniques or [],
            "created_by": self.created_by,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "deleted_at": self.deleted_at,
            "is_deprecated": self.is_deprecated,
            "is_revoked": self.is_revoked,
            "revoked_by_id": self.revoked_by_id,
            "version": self.version
        } 