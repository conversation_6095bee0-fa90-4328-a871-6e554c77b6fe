"""
Database models for comprehensive tagging system feature
"""
from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, Boolean, ForeignKey, Table
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from api.database import Base

# Association table for many-to-many relationships between tags and various entities
class TagAssociation(Base):
    """Association table for tags and their related entities"""
    __tablename__ = "tag_associations"
    
    id = Column(Integer, primary_key=True, index=True)
    tag_id = Column(Integer, ForeignKey("tags.id", ondelete="CASCADE"), nullable=False)
    entity_type = Column(String(50), nullable=False, index=True)  # e.g., 'test_case', 'campaign', 'assessment'
    entity_id = Column(Integer, nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Relationships
    tag = relationship("Tag", back_populates="associations")
    created_by = relationship("User", foreign_keys=[created_by_id])
    
    __table_args__ = (
        # Composite index for faster lookups
        {'sqlite_autoincrement': True},
    )
    
    def __repr__(self):
        return f"<TagAssociation tag_id={self.tag_id} entity_type={self.entity_type} entity_id={self.entity_id}>"


class Tag(Base):
    """Tag database model"""
    __tablename__ = "tags"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True, index=True)
    description = Column(Text, nullable=True)
    color = Column(String(7), nullable=False, default="#3498db")  # Hex color code
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Relationships
    associations = relationship("TagAssociation", back_populates="tag", cascade="all, delete-orphan")
    created_by = relationship("User", foreign_keys=[created_by_id])
    
    __table_args__ = (
        # Ensure tag names are unique
        {'sqlite_autoincrement': True},
    )
    
    def __repr__(self):
        return f"<Tag id={self.id} name={self.name}>"
    
    @property
    def usage_count(self):
        """Return the number of entities this tag is associated with"""
        return len(self.associations)
