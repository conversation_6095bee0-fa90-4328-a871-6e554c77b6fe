"""
Database model for error handling feature
"""
from sqlalchemy import <PERSON><PERSON>n, Integer, String, Text, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from api.database import Base

class ErrorHandling(Base):
    """Error handling database model"""
    __tablename__ = "error_handlings"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    error_code = Column(String(50), nullable=True)
    error_type = Column(String(100), nullable=False)
    error_message = Column(Text, nullable=False)
    is_user_facing = Column(Boolean, default=True)
    http_status_code = Column(Integer, nullable=True)
    severity = Column(String(50), nullable=True)
    
    # TODO: Add additional fields and relationships as needed
    
    def __repr__(self):
        return f"<ErrorHandling id={self.id} name={self.name}>"
