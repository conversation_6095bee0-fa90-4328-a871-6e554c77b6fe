"""
Database model for admin interface feature
"""
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from api.database import Base

class AdminSetting(Base):
    """Admin settings database model"""
    __tablename__ = "admin_settings"
    
    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(100), nullable=False, unique=True)
    value = Column(Text, nullable=True)
    description = Column(Text, nullable=True)
    category = Column(String(50), nullable=False)
    is_editable = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<AdminSetting id={self.id} key={self.key}>"

class AdminAuditLog(Base):
    """Audit log database model for tracking admin actions"""
    __tablename__ = "admin_audit_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    action = Column(String(100), nullable=False)
    resource_type = Column(String(50), nullable=False)
    resource_id = Column(String(50), nullable=True)
    details = Column(JSON, nullable=True)
    ip_address = Column(String(50), nullable=True)
    user_agent = Column(String(255), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationship to user
    user = relationship("User", back_populates="admin_audit_logs")
    
    def __repr__(self):
        return f"<AdminAuditLog id={self.id} action={self.action} user_id={self.user_id}>"

class SystemConfiguration(Base):
    """System configuration database model"""
    __tablename__ = "system_configurations"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True)
    value = Column(Text, nullable=True)
    description = Column(Text, nullable=True)
    is_encrypted = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<SystemConfiguration id={self.id} name={self.name}>"

class AdminDashboardWidget(Base):
    """Admin dashboard widget configuration"""
    __tablename__ = "admin_dashboard_widgets"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    widget_type = Column(String(50), nullable=False)
    position = Column(Integer, nullable=False)
    size = Column(String(20), nullable=False, default="medium")  # small, medium, large
    config = Column(JSON, nullable=True)
    is_enabled = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<AdminDashboardWidget id={self.id} name={self.name}>"

class AdminNotification(Base):
    """Admin notification database model"""
    __tablename__ = "admin_notifications"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    severity = Column(String(20), nullable=False, default="info")  # info, warning, error, critical
    is_read = Column(Boolean, default=False)
    user_id = Column(String, ForeignKey("users.id"), nullable=True)  # If null, notification is for all admins
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationship to user
    user = relationship("User", back_populates="admin_notifications")
    
    def __repr__(self):
        return f"<AdminNotification id={self.id} title={self.title}>"
