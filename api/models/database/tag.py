"""
Database models for the comprehensive tagging system.

This module defines the models for tags, tag categories, and tag relationships
that allow for a flexible and powerful tagging system across the application.
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, Table, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from api.database import Base

# Association table for many-to-many relationship between tags and resources
tag_resource = Table(
    'tag_resource',
    Base.metadata,
    Column('tag_id', Integer, ForeignKey('tags.id', ondelete='CASCADE'), primary_key=True),
    Column('resource_type', String(50), primary_key=True),
    Column('resource_id', Integer, primary_key=True),
    Column('created_by', Integer, ForeignKey('users.id')),
    Column('created_at', DateTime(timezone=True), server_default=func.now()),
)

# Association table for tag hierarchies (parent-child relationships)
tag_hierarchy = Table(
    'tag_hierarchy',
    Base.metadata,
    Column('parent_id', <PERSON><PERSON><PERSON>, <PERSON><PERSON>ey('tags.id', ondelete='CASCADE'), primary_key=True),
    <PERSON>umn('child_id', Integer, ForeignKey('tags.id', ondelete='CASCADE'), primary_key=True),
    UniqueConstraint('parent_id', 'child_id', name='unique_tag_hierarchy'),
)

# Association table for related tags
tag_relation = Table(
    'tag_relation',
    Base.metadata,
    Column('tag_id', Integer, ForeignKey('tags.id', ondelete='CASCADE'), primary_key=True),
    Column('related_tag_id', Integer, ForeignKey('tags.id', ondelete='CASCADE'), primary_key=True),
    Column('relation_type', String(50), nullable=True),
    Column('created_at', DateTime(timezone=True), server_default=func.now()),
    UniqueConstraint('tag_id', 'related_tag_id', name='unique_tag_relation'),
)


class TagCategory(Base):
    """
    Model for tag categories that help organize tags into logical groups.
    
    Attributes:
        id: Primary key
        name: Name of the category
        description: Optional description of the category
        color: Optional color code for UI display
        icon: Optional icon name for UI display
        is_system: Whether this is a system-defined category
        created_at: Timestamp when the category was created
        updated_at: Timestamp when the category was last updated
    """
    __tablename__ = "tag_categories"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text, nullable=True)
    color = Column(String(20), nullable=True)
    icon = Column(String(50), nullable=True)
    is_system = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tags = relationship("Tag", back_populates="category")
    
    def __repr__(self):
        return f"<TagCategory id={self.id} name={self.name}>"


class Tag(Base):
    """
    Model for tags that can be applied to various resources.
    
    Attributes:
        id: Primary key
        name: Name of the tag
        slug: URL-friendly version of the name
        description: Optional description of the tag
        category_id: Foreign key to the tag category
        is_system: Whether this is a system-defined tag
        created_by: User ID who created the tag
        created_at: Timestamp when the tag was created
        updated_at: Timestamp when the tag was last updated
    """
    __tablename__ = "tags"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    slug = Column(String(100), nullable=False, unique=True)
    description = Column(Text, nullable=True)
    category_id = Column(Integer, ForeignKey("tag_categories.id"), nullable=True)
    is_system = Column(Boolean, default=False)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Ensure name is unique within a category
    __table_args__ = (
        UniqueConstraint('name', 'category_id', name='unique_tag_name_per_category'),
    )
    
    # Relationships
    category = relationship("TagCategory", back_populates="tags")
    creator = relationship("User", foreign_keys=[created_by])
    
    # Tag hierarchies
    children = relationship(
        "Tag",
        secondary=tag_hierarchy,
        primaryjoin=(id == tag_hierarchy.c.parent_id),
        secondaryjoin=(id == tag_hierarchy.c.child_id),
        backref="parents"
    )
    
    # Related tags
    related_tags = relationship(
        "Tag",
        secondary=tag_relation,
        primaryjoin=(id == tag_relation.c.tag_id),
        secondaryjoin=(id == tag_relation.c.related_tag_id),
        backref="related_to"
    )
    
    def __repr__(self):
        return f"<Tag id={self.id} name={self.name}>"


class TagAuditLog(Base):
    """
    Model for tracking changes to tags and tag assignments.
    
    Attributes:
        id: Primary key
        user_id: User who made the change
        action: Type of action (create, update, delete, assign, unassign)
        tag_id: ID of the tag affected
        resource_type: Type of resource affected (if applicable)
        resource_id: ID of the resource affected (if applicable)
        details: Additional details about the action
        created_at: Timestamp when the action occurred
    """
    __tablename__ = "tag_audit_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    action = Column(String(50), nullable=False)
    tag_id = Column(Integer, ForeignKey("tags.id"), nullable=True)
    resource_type = Column(String(50), nullable=True)
    resource_id = Column(Integer, nullable=True)
    details = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User")
    tag = relationship("Tag")
    
    def __repr__(self):
        return f"<TagAuditLog id={self.id} action={self.action}>"


class TagPropagationRule(Base):
    """
    Model for tag propagation rules that define how tags should be propagated
    between related resources.
    
    Attributes:
        id: Primary key
        source_type: The type of resource that is the source of the tag
        target_type: The type of resource that should receive the tag
        relation_field: The field that relates the source to the target
        is_active: Whether this rule is currently active
        description: Optional description of the rule
        created_at: When the rule was created
        updated_at: When the rule was last updated
        created_by_id: User who created the rule
        updated_by_id: User who last updated the rule
    """
    __tablename__ = "tag_propagation_rules"
    
    id = Column(Integer, primary_key=True)
    source_type = Column(String(50), nullable=False)
    target_type = Column(String(50), nullable=False)
    relation_field = Column(String(100), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    updated_by_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Relationships
    created_by = relationship("User", foreign_keys=[created_by_id])
    updated_by = relationship("User", foreign_keys=[updated_by_id])
    
    __table_args__ = (
        # Ensure unique combination of source_type, target_type, and relation_field
        UniqueConstraint('source_type', 'target_type', 'relation_field', name='unique_tag_propagation_rule'),
        {'sqlite_autoincrement': True},
    )
    
    def __repr__(self):
        return f"<TagPropagationRule id={self.id} source={self.source_type} target={self.target_type}>" 