"""
Mixins for database models.

This module defines mixins that can be used to add common functionality to database models.
"""
from datetime import datetime
from typing import Optional, Any

from sqlalchemy import <PERSON><PERSON><PERSON>, Date<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, String
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import Session
from sqlalchemy.sql import func


class SoftDeleteMixin:
    """
    Mixin for soft deletion functionality.
    
    Adds a deleted_at column to the model and methods for soft deletion.
    """
    deleted_at = Column(DateTime, nullable=True)
    
    def soft_delete(self, session: Session) -> None:
        """
        Mark the record as deleted by setting the deleted_at timestamp.
        
        Args:
            session: SQLAlchemy session
        """
        self.deleted_at = datetime.utcnow()
        session.add(self)
        session.commit()
    
    def restore(self, session: Session) -> None:
        """
        Restore a soft-deleted record by clearing the deleted_at timestamp.
        
        Args:
            session: SQLAlchemy session
        """
        self.deleted_at = None
        session.add(self)
        session.commit()
    
    @classmethod
    def not_deleted(cls) -> Any:
        """
        Query filter to exclude soft-deleted records.
        
        Returns:
            SQLAlchemy filter expression
        """
        return cls.deleted_at == None  # noqa: E711


class VersionMixin:
    """
    Mixin for versioning functionality.
    
    Adds created_at, updated_at columns and version tracking.
    """
    created_at = Column(DateTime, server_default=func.now(), nullable=False)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False)
    version = Column(String(10), nullable=True)
    
    def increment_version(self, session: Session) -> None:
        """
        Increment the version number.
        
        Args:
            session: SQLAlchemy session
        """
        if self.version is None:
            self.version = "1.0.0"
        else:
            major, minor, patch = map(int, self.version.split('.'))
            patch += 1
            self.version = f"{major}.{minor}.{patch}"
        
        session.add(self)
        session.commit()


class DeprecationMixin:
    """
    Mixin for deprecation functionality.
    
    Adds is_deprecated column and methods for deprecation.
    """
    is_deprecated = Column(Boolean, default=False, nullable=False)
    
    def deprecate(self, session: Session) -> None:
        """
        Mark the record as deprecated.
        
        Args:
            session: SQLAlchemy session
        """
        self.is_deprecated = True
        session.add(self)
        session.commit()
    
    def undeprecate(self, session: Session) -> None:
        """
        Remove the deprecated status from the record.
        
        Args:
            session: SQLAlchemy session
        """
        self.is_deprecated = False
        session.add(self)
        session.commit()
    
    @classmethod
    def not_deprecated(cls) -> Any:
        """
        Query filter to exclude deprecated records.
        
        Returns:
            SQLAlchemy filter expression
        """
        return cls.is_deprecated == False  # noqa: E712


class RevocationMixin:
    """
    Mixin for revocation functionality.
    
    Adds is_revoked and revoked_by_id columns and methods for revocation.
    """
    is_revoked = Column(Boolean, default=False, nullable=False)
    revoked_by_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    def revoke(self, session: Session, user_id: int) -> None:
        """
        Mark the record as revoked.
        
        Args:
            session: SQLAlchemy session
            user_id: ID of the user who revoked the record
        """
        self.is_revoked = True
        self.revoked_by_id = user_id
        session.add(self)
        session.commit()
    
    @classmethod
    def not_revoked(cls) -> Any:
        """
        Query filter to exclude revoked records.
        
        Returns:
            SQLAlchemy filter expression
        """
        return cls.is_revoked == False  # noqa: E712 