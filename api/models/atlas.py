"""SQLAlchemy models for ATLAS (Adversarial Threat Landscape for Artificial-Intelligence Systems)."""
from datetime import datetime
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Foreign<PERSON>ey, Integer, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship
from typing import Optional, List
from api.database import Base

class AtlasVersion(Base):
    """ATLAS version tracking."""
    __tablename__ = 'atlas_versions'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    version: Mapped[str] = mapped_column(String(50), nullable=False, unique=True)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    import_date: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    is_current: Mapped[bool] = mapped_column(Boolean, default=False)

    # Relationships
    tactics: Mapped[List["AtlasTactic"]] = relationship(
        "AtlasTactic", back_populates="version", cascade="all, delete-orphan"
    )
    techniques: Mapped[List["AtlasTechnique"]] = relationship(
        "AtlasTechnique", back_populates="version", cascade="all, delete-orphan"
    )
    matrices: Mapped[List["AtlasMatrix"]] = relationship(
        "AtlasMatrix", back_populates="version", cascade="all, delete-orphan"
    )

class AtlasTactic(Base):
    """ATLAS tactics."""
    __tablename__ = 'atlas_tactics'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    external_id: Mapped[str] = mapped_column(String(50), nullable=False)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    version_id: Mapped[int] = mapped_column(
        Integer, ForeignKey('atlas_versions.id', ondelete='CASCADE'), nullable=False
    )
    created: Mapped[Optional[datetime]] = mapped_column(DateTime)
    modified: Mapped[Optional[datetime]] = mapped_column(DateTime)
    revoked: Mapped[bool] = mapped_column(Boolean, default=False)
    deprecated: Mapped[bool] = mapped_column(Boolean, default=False)

    # Relationships
    version: Mapped["AtlasVersion"] = relationship("AtlasVersion", back_populates="tactics")
    techniques: Mapped[List["AtlasTechnique"]] = relationship("AtlasTechnique", back_populates="tactic")
    matrix_items: Mapped[List["AtlasMatrixItem"]] = relationship("AtlasMatrixItem", back_populates="tactic")

class AtlasTechnique(Base):
    """ATLAS techniques."""
    __tablename__ = 'atlas_techniques'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    external_id: Mapped[str] = mapped_column(String(50), nullable=False)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    version_id: Mapped[int] = mapped_column(
        Integer, ForeignKey('atlas_versions.id', ondelete='CASCADE'), nullable=False
    )
    tactic_id: Mapped[Optional[int]] = mapped_column(
        Integer, ForeignKey('atlas_tactics.id', ondelete='SET NULL')
    )
    created: Mapped[Optional[datetime]] = mapped_column(DateTime)
    modified: Mapped[Optional[datetime]] = mapped_column(DateTime)
    revoked: Mapped[bool] = mapped_column(Boolean, default=False)
    deprecated: Mapped[bool] = mapped_column(Boolean, default=False)
    is_subtechnique: Mapped[bool] = mapped_column(Boolean, default=False)
    parent_technique_id: Mapped[Optional[int]] = mapped_column(
        Integer, ForeignKey('atlas_techniques.id', ondelete='SET NULL')
    )

    # Relationships
    version: Mapped["AtlasVersion"] = relationship("AtlasVersion", back_populates="techniques")
    tactic: Mapped[Optional["AtlasTactic"]] = relationship("AtlasTactic", back_populates="techniques")
    parent_technique: Mapped[Optional["AtlasTechnique"]] = relationship(
        "AtlasTechnique", remote_side=[id], backref="subtechniques"
    )
    matrix_items: Mapped[List["AtlasMatrixItem"]] = relationship(
        "AtlasMatrixItem", back_populates="technique"
    )
    outgoing_relationships: Mapped[List["AtlasRelationship"]] = relationship(
        "AtlasRelationship",
        foreign_keys="AtlasRelationship.source_id",
        back_populates="source",
        cascade="all, delete-orphan"
    )
    incoming_relationships: Mapped[List["AtlasRelationship"]] = relationship(
        "AtlasRelationship",
        foreign_keys="AtlasRelationship.target_id",
        back_populates="target",
        cascade="all, delete-orphan"
    )

class AtlasMatrix(Base):
    """ATLAS matrices."""
    __tablename__ = 'atlas_matrices'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    version_id: Mapped[int] = mapped_column(
        Integer, ForeignKey('atlas_versions.id', ondelete='CASCADE'), nullable=False
    )

    # Relationships
    version: Mapped["AtlasVersion"] = relationship("AtlasVersion", back_populates="matrices")
    matrix_items: Mapped[List["AtlasMatrixItem"]] = relationship(
        "AtlasMatrixItem", back_populates="matrix", cascade="all, delete-orphan"
    )

class AtlasMatrixItem(Base):
    """ATLAS matrix items for technique-tactic mappings."""
    __tablename__ = 'atlas_matrix_items'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    matrix_id: Mapped[int] = mapped_column(
        Integer, ForeignKey('atlas_matrices.id', ondelete='CASCADE'), nullable=False
    )
    technique_id: Mapped[int] = mapped_column(
        Integer, ForeignKey('atlas_techniques.id', ondelete='CASCADE'), nullable=False
    )
    tactic_id: Mapped[int] = mapped_column(
        Integer, ForeignKey('atlas_tactics.id', ondelete='CASCADE'), nullable=False
    )
    color: Mapped[Optional[str]] = mapped_column(String(50))
    show_subtechniques: Mapped[bool] = mapped_column(Boolean, default=True)

    # Relationships
    matrix: Mapped["AtlasMatrix"] = relationship("AtlasMatrix", back_populates="matrix_items")
    technique: Mapped["AtlasTechnique"] = relationship("AtlasTechnique", back_populates="matrix_items")
    tactic: Mapped["AtlasTactic"] = relationship("AtlasTactic", back_populates="matrix_items")

class AtlasRelationship(Base):
    """ATLAS Relationships between techniques."""
    __tablename__ = "atlas_relationships"

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    source_id: Mapped[int] = mapped_column(
        Integer, ForeignKey('atlas_techniques.id', ondelete="CASCADE"), nullable=False
    )
    target_id: Mapped[int] = mapped_column(
        Integer, ForeignKey('atlas_techniques.id', ondelete="CASCADE"), nullable=False
    )
    type: Mapped[str] = mapped_column(String(50), nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow
    )

    # Relationships
    source: Mapped["AtlasTechnique"] = relationship(
        "AtlasTechnique",
        foreign_keys=[source_id],
        back_populates="outgoing_relationships"
    )
    target: Mapped["AtlasTechnique"] = relationship(
        "AtlasTechnique",
        foreign_keys=[target_id],
        back_populates="incoming_relationships"
    )