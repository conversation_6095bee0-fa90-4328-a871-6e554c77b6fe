"""User preferences model for storing user-specific settings."""

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, JSON
from sqlalchemy.orm import relationship

from api.database import Base

class UserPreference(Base):
    """User preferences model for storing customizable user settings."""

    __tablename__ = "user_preferences"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True)
    user_id = Column(String, ForeignKey('users.id', ondelete='CASCADE'), nullable=False, unique=True)
    theme = Column(String(20), default="light")
    notification_settings = Column(JSON, default={})
    dashboard_layout = Column(JSON, default={})
    timezone = Column(String(50), default="UTC")
    language = Column(String(10), default="en")
    email_notifications = Column(Boolean, default=True)
    two_factor_enabled = Column(Boolean, default=False)

    # Relationship back to user
    user = relationship("User", back_populates="preferences")

    def to_dict(self):
        """Convert preference object to dictionary."""
        return {
            "theme": self.theme,
            "notification_settings": self.notification_settings,
            "dashboard_layout": self.dashboard_layout,
            "timezone": self.timezone,
            "language": self.language,
            "email_notifications": self.email_notifications,
            "two_factor_enabled": self.two_factor_enabled
        }