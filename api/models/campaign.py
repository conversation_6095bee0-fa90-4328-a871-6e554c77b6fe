"""Campaign model for the RegressionRigor platform.

This module defines the Campaign model and related database models for
managing security testing campaigns.
"""

from datetime import datetime

from sqlalchemy import Column, DateTime, Foreign<PERSON>ey, Integer, String, Table, Text
from sqlalchemy.orm import Session, relationship

from api.database import Base
from api.models.mixins import SoftDeleteMixin

# Association table for campaign-test case many-to-many relationship
campaign_test_cases = Table(
    "campaign_test_cases",
    Base.metadata,
    Column(
        "campaign_id",
        Integer,
        ForeignKey("campaigns.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "test_case_id",
        Integer,
        ForeignKey("test_cases.id", ondelete="CASCADE"),
        primary_key=True,
    ),
)


class Campaign(Base, SoftDeleteMixin):
    """Campaign model for security testing campaigns.

    A campaign represents a collection of related security tests that are executed
    together to assess a specific target or system.

    Attributes:
        id: Unique identifier for the campaign
        name: Name of the campaign
        description: Detailed description of the campaign
        status: Current status of the campaign (draft, active, completed, archived)
        start_date: Planned start date for the campaign
        end_date: Planned end date for the campaign
        assessment_id: ID of the assessment this campaign belongs to
        created_by: ID of the user who created the campaign
        created_at: Timestamp when the campaign was created
        updated_at: Timestamp when the campaign was last updated
        deleted_at: Timestamp when the campaign was soft-deleted (if applicable)
    """

    __tablename__ = "campaigns"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    status = Column(String(20), default="draft")  # draft, active, completed, archived
    start_date = Column(DateTime, nullable=True)
    end_date = Column(DateTime, nullable=True)
    assessment_id = Column(Integer, ForeignKey("assessments.id"), nullable=True)
    created_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    creator = relationship(
        "User", foreign_keys=[created_by], back_populates="created_campaigns"
    )
    test_cases = relationship(
        "TestCase", secondary=campaign_test_cases, back_populates="campaigns"
    )
    assessment = relationship(
        "Assessment", back_populates="campaigns", foreign_keys=[assessment_id]
    )
    technique_results = relationship(
        "AttackTechniqueResult", back_populates="campaign", cascade="all, delete-orphan"
    )
    heat_map_snapshots = relationship(
        "HeatMapSnapshot", back_populates="campaign", cascade="all, delete-orphan"
    )

    def __repr__(self):
        """Return string representation of the campaign."""
        return f"<Campaign(id={self.id}, name='{self.name}', status='{self.status}')>"

    def to_dict(self):
        """Convert campaign to dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "status": self.status,
            "start_date": self.start_date.isoformat() if self.start_date else None,
            "end_date": self.end_date.isoformat() if self.end_date else None,
            "assessment_id": self.assessment_id,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "deleted_at": self.deleted_at.isoformat() if self.deleted_at else None,
        }

    def soft_delete(self, db: Session):
        """Soft delete the campaign.

        Args:
            db: Database session
        """
        self.deleted_at = datetime.utcnow()
        db.commit()
        return self
