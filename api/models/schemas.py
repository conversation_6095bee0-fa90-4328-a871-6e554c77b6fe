"""Pydantic models for the API.

This module defines the Pydantic models used for request/response validation and
serialization in the API. It includes models for campaigns, test cases, and
MITRE ATT&CK framework data.

The models enforce type safety and validation rules while providing clear
documentation of the expected data structures for API interactions.

Example:
    >>> campaign = Campaign(
    >>>     id=1,
    >>>     name="Web App Security Testing",
    >>>     description="Testing OWASP Top 10 vulnerabilities",
    >>>     status="active",
    >>>     created_at=datetime.now(),
    >>>     updated_at=datetime.now()
    >>> )
"""
from typing import Optional, Dict, Any, Generic, TypeVar, List, Union, Literal
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict, EmailStr, validator, root_validator
from enum import Enum

# Define the score category enum to match the SQLAlchemy model
class ScoreCategory(str, Enum):
    """Categories for technique scoring."""
    IMPACT = "impact"
    LIKELIHOOD = "likelihood"
    DETECTABILITY = "detectability"
    EXPLOITABILITY = "exploitability"
    CUSTOM = "custom"

T = TypeVar('T')

class CampaignBase(BaseModel):
    """Base model for campaign data validation.

    Contains the common attributes shared between campaign creation
    and response models.

    Attributes:
        name: Name of the security testing campaign
        description: Optional detailed description of the campaign's purpose
        status: Current campaign status (active/inactive/completed)
    """
    name: str = Field(..., description="Name of the campaign")
    description: Optional[str] = Field(None, description="Campaign description")
    status: str = Field(default="active", pattern="^(active|inactive|completed)$")

class CampaignCreate(CampaignBase):
    """Model for campaign creation requests.

    Inherits from CampaignBase and doesn't add additional fields, but
    is kept separate to maintain API flexibility for future extensions.
    """
    pass

class Campaign(CampaignBase):
    """Complete campaign model including database-generated fields.

    Extends CampaignBase to include the id and timestamps that are
    generated when the campaign is stored in the database.

    Attributes:
        id: Unique identifier for the campaign
        created_at: Timestamp when the campaign was created
        updated_at: Timestamp of the last campaign update
    """
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class AssessmentBase(BaseModel):
    """Base model for assessment data validation.

    Contains the common attributes shared between assessment creation
    and response models.

    Attributes:
        name: Name of the security assessment
        description: Optional detailed description of the assessment
        target_system: The system being assessed
        assessment_type: Type of assessment (e.g., vulnerability, penetration, code review)
        start_date: When the assessment starts
        end_date: Optional end date for the assessment
        status: Current assessment status (planned/in-progress/completed/cancelled)
    """
    name: str = Field(..., description="Name of the assessment")
    description: Optional[str] = Field(None, description="Assessment description")
    target_system: str = Field(..., description="System being assessed")
    assessment_type: str = Field(..., description="Type of assessment")
    start_date: Optional[datetime] = Field(None, description="Assessment start date")
    end_date: Optional[datetime] = Field(None, description="Assessment end date")
    status: str = Field(default="planned", description="Assessment status")

class AssessmentCreate(AssessmentBase):
    """Schema for creating a new assessment.

    Extends AssessmentBase to include campaign IDs for associating
    campaigns with the assessment.

    Attributes:
        campaign_ids: Optional list of campaign IDs to associate with the assessment
    """
    campaign_ids: Optional[List[int]] = Field(None, description="Campaign IDs to associate")

class Assessment(AssessmentBase):
    """Complete assessment model including database-generated fields.

    Extends AssessmentBase to include the id, created_by, and timestamps that are
    generated when the assessment is stored in the database.

    Attributes:
        id: Unique identifier for the assessment
        created_by: ID of the user who created the assessment
        created_at: Timestamp when the assessment was created
        updated_at: Timestamp of the last assessment update
        campaigns: List of associated campaigns
    """
    id: int
    created_by: int
    created_at: datetime
    updated_at: datetime
    campaigns: Optional[List[Campaign]] = []

    class Config:
        from_attributes = True

class TestCaseBase(BaseModel):
    """Base model for test case data validation.

    Contains the common attributes shared between test case creation
    and response models.

    Attributes:
        name: Name of the test case
        description: Optional detailed description of the test
        campaign_id: ID of the parent campaign
        expected_result: Expected outcome of the test
        actual_result: Optional actual result after execution
        status: Current test status (pending/running/passed/failed)
    """
    name: str = Field(..., description="Name of the test case")
    description: Optional[str] = Field(None, description="Test case description")
    campaign_id: int = Field(..., description="ID of the parent campaign")
    expected_result: str = Field(..., description="Expected test result")
    actual_result: Optional[str] = Field(None, description="Actual test result")
    status: str = Field(default="pending", pattern="^(pending|running|passed|failed)$")

class TestCaseCreate(TestCaseBase):
    """Model for test case creation requests.

    Inherits from TestCaseBase and doesn't add additional fields, but
    is kept separate to maintain API flexibility for future extensions.
    """
    pass

class TestCase(TestCaseBase):
    """Complete test case model including database-generated fields.

    Extends TestCaseBase to include the id and timestamps that are
    generated when the test case is stored in the database.

    Attributes:
        id: Unique identifier for the test case
        created_at: Timestamp when the test case was created
        updated_at: Timestamp of the last test case update
    """
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class TechniqueCreate(BaseModel):
    """Schema for creating MITRE ATT&CK techniques.

    Defines the required fields for creating a new technique entry
    in the database.

    Attributes:
        technique_id: MITRE ATT&CK technique ID (e.g., T1234)
        name: Human-readable name of the technique
        description: Optional detailed description
        version_id: ID of the MITRE version this technique belongs to
    """
    technique_id: str = Field(..., description="MITRE ATT&CK technique ID")
    name: str = Field(..., description="Name of the technique")
    description: Optional[str] = Field(None, description="Description of the technique")
    version_id: int = Field(..., description="ID of the MITRE version")

class TechniqueResponse(BaseModel):
    """Schema for technique responses.

    Complete model for technique data including database-generated fields
    and timestamps.

    Attributes:
        id: Internal database ID
        technique_id: MITRE ATT&CK technique ID
        name: Name of the technique
        description: Optional detailed description
        version_id: ID of the MITRE version
        created_at: Creation timestamp
        updated_at: Last update timestamp
    """
    id: int
    technique_id: str
    name: str
    description: Optional[str]
    version_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class TechniqueScoreCreate(BaseModel):
    """Schema for creating technique scores.

    Defines the required fields for scoring a MITRE ATT&CK technique.

    Attributes:
        technique_id: ID of the technique to score
        category: Category of the score (impact, likelihood, etc.)
        score: Numeric score value (0-10)
        weight: Optional weight for this score category (0-1)
        notes: Optional notes explaining the score rationale
    """
    technique_id: int = Field(..., description="ID of the technique to score")
    category: ScoreCategory = Field(..., description="Category of the score")
    score: float = Field(..., ge=0, le=10, description="Score value (0-10)")
    weight: float = Field(1.0, ge=0, le=1, description="Weight for this score category (0-1)")
    notes: Optional[str] = Field(None, description="Notes explaining the score")

    @validator('score')
    def validate_score(cls, v):
        """Validate that score is between 0 and 10."""
        if v < 0 or v > 10:
            raise ValueError("Score must be between 0 and 10")
        return v

    @validator('weight')
    def validate_weight(cls, v):
        """Validate that weight is between 0 and 1."""
        if v < 0 or v > 1:
            raise ValueError("Weight must be between 0 and 1")
        return v

class TechniqueScoreResponse(BaseModel):
    """Schema for technique score responses.

    Complete model for technique score data including database-generated fields
    and timestamps.

    Attributes:
        id: Internal database ID
        technique_id: ID of the scored technique
        category: Category of the score
        score: Numeric score value (0-10)
        weight: Weight for this score category (0-1)
        notes: Optional notes explaining the score
        created_by: ID of the user who created the score
        created_at: Creation timestamp
        updated_at: Last update timestamp
    """
    id: int
    technique_id: int
    category: ScoreCategory
    score: float
    weight: float
    notes: Optional[str]
    created_by: Optional[int]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class TechniqueWithScores(TechniqueResponse):
    """Schema for technique responses with scores.

    Extends TechniqueResponse to include scoring information.

    Attributes:
        scores: List of scores for this technique
        overall_score: Calculated overall score based on weighted average
    """
    scores: List[TechniqueScoreResponse] = []
    overall_score: Optional[float] = None

    class Config:
        from_attributes = True

class TechniqueScoreBulkCreate(BaseModel):
    """Schema for bulk creating technique scores.

    Allows creating multiple scores for different techniques at once.

    Attributes:
        scores: List of technique scores to create
    """
    scores: List[TechniqueScoreCreate]

class TechniqueRelationshipCreate(BaseModel):
    """Schema for creating technique relationships.

    Defines the structure for establishing relationships between
    different MITRE ATT&CK techniques.

    Attributes:
        source_technique_id: ID of the source technique
        target_technique_id: ID of the target technique
        relationship_type: Type of relationship between techniques
    """
    source_technique_id: str
    target_technique_id: str
    relationship_type: str

class TechniqueRelationship(BaseModel):
    """Schema for technique relationship responses.

    Complete model for technique relationships including database-generated
    fields and timestamps.

    Attributes:
        id: Internal database ID
        source_technique_id: ID of the source technique
        target_technique_id: ID of the target technique
        relationship_type: Type of relationship
        created_at: Creation timestamp
        updated_at: Last update timestamp
    """
    id: int
    source_technique_id: str
    target_technique_id: str
    relationship_type: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

"""Pydantic models for API request/response validation.

This module defines the Pydantic models used for data validation and serialization
in the API endpoints. These schemas are separate from SQLAlchemy models to maintain
a clear separation between API contracts and database models.
"""

from typing import Optional, Dict, Any, Generic, TypeVar, List
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict, EmailStr

class PaginatedResponse(BaseModel, Generic[T]):
    """Generic schema for paginated responses."""
    items: List[T]
    total: int = Field(..., description="Total number of items")
    page: int = Field(..., ge=1, description="Current page number")
    size: int = Field(..., ge=1, description="Number of items per page")

    class Config:
        """Pydantic configuration."""
        from_attributes = True

class UserBase(BaseModel):
    """Base schema for user data validation."""
    username: str = Field(..., description="Username for login")
    email: EmailStr = Field(..., description="User's email address")
    full_name: Optional[str] = Field(None, description="User's full name")
    bio: Optional[str] = Field(None, description="User's bio or description")
    role: str = Field(default="user", description="User's role (user/admin/analyst)")

class UserCreate(UserBase):
    """Schema for user creation requests."""
    password: str = Field(..., min_length=8, description="User's password")

class UserUpdate(UserBase):
    """Schema for user profile updates."""
    password: Optional[str] = None

class UserResponse(UserBase):
    """Schema for user responses, excluding sensitive data."""
    id: int
    username: str
    email: EmailStr
    full_name: Optional[str] = None
    bio: Optional[str] = None
    role: str
    is_active: bool
    created_at: datetime
    last_login: Optional[datetime] = None
    two_factor_enabled: bool = False

    class Config:
        """Pydantic configuration."""
        from_attributes = True

class UserPreferenceBase(BaseModel):
    """Base schema for user preferences."""
    theme: str = Field(default="light", description="UI theme preference")
    language: str = Field(default="en", description="Interface language")
    notifications_enabled: bool = Field(default=True, description="Enable notifications")
    dashboard_layout: Dict[str, Any] = Field(default_factory=dict, description="Dashboard widget layout")

class UserPreferenceCreate(UserPreferenceBase):
    """Schema for creating user preferences."""
    user_id: int = Field(..., description="ID of the user these preferences belong to")

class UserPreferenceResponse(UserPreferenceBase):
    """Schema for user preference responses."""
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        """Pydantic configuration."""
        from_attributes = True

class PasswordResetRequest(BaseModel):
    """Schema for password reset request."""
    email: EmailStr = Field(..., description="Email address for password reset")

class PasswordReset(BaseModel):
    """Schema for password reset confirmation."""
    token: str = Field(..., description="Password reset token")
    new_password: str = Field(..., min_length=8, description="New password")

class UnlockAccountRequest(BaseModel):
    """Schema for account unlock request."""
    email: EmailStr = Field(..., description="Email address of locked account")

# Two-factor authentication schemas
class TwoFactorSetup(BaseModel):
    """Schema for 2FA setup response."""
    secret: str = Field(..., description="TOTP secret key")
    qr_code_uri: str = Field(..., description="URI for QR code generation")
    backup_codes: List[str] = Field(..., description="Backup codes for account recovery")

class TwoFactorEnable(BaseModel):
    """Schema for enabling 2FA."""
    token: str = Field(..., description="TOTP token for verification")

class TwoFactorVerify(BaseModel):
    """Schema for verifying 2FA during login."""
    token: str = Field(..., description="TOTP token or backup code")
    is_backup_code: bool = Field(default=False, description="Whether the token is a backup code")

class TwoFactorDisable(BaseModel):
    """Schema for disabling 2FA."""
    password: str = Field(..., description="Current password for verification")

class TwoFactorStatus(BaseModel):
    """Schema for 2FA status response."""
    enabled: bool = Field(..., description="Whether 2FA is enabled")
    backup_codes_remaining: int = Field(..., description="Number of backup codes remaining")

# Enhanced login response
class LoginResponse(BaseModel):
    """Schema for login response."""
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field(..., description="Token type (bearer)")
    username: str = Field(..., description="Username")
    role: str = Field(..., description="User role")
    requires_two_factor: bool = Field(default=False, description="Whether 2FA verification is required")
    two_factor_session_id: Optional[str] = Field(None, description="Temporary session ID for 2FA verification")

class EmailVerificationRequest(BaseModel):
    """Model for email verification requests."""
    token: str