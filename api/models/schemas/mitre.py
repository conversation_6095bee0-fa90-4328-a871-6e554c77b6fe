"""
Schemas for MITRE ATT&CK data.

This module contains Pydantic models for MITRE ATT&CK techniques, tactics, groups, etc.
"""

from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime

class TechniqueBase(BaseModel):
    """Base schema for MITRE ATT&CK techniques."""
    technique_id: str = Field(..., description="The MITRE ATT&CK ID (e.g., T1234)")
    name: str = Field(..., description="The name of the technique")
    description: str = Field(..., description="Detailed description of the technique")
    detection: Optional[str] = Field(None, description="Detection methods for the technique")
    platforms: List[str] = Field(default_factory=list, description="Platforms affected by this technique")
    permissions_required: List[str] = Field(default_factory=list, description="Permissions required to execute this technique")
    data_sources: List[str] = Field(default_factory=list, description="Data sources that can be used to identify this technique")
    version: str = Field(..., description="MITRE ATT&CK version")

class TechniqueCreate(TechniqueBase):
    """Schema for creating a new MITRE ATT&CK technique."""
    pass

class TechniqueResponse(TechniqueBase):
    """Schema for returning a MITRE ATT&CK technique."""
    id: int = Field(..., description="Database ID")
    created_at: datetime = Field(..., description="When the record was created")
    updated_at: Optional[datetime] = Field(None, description="When the record was last updated")
    deleted_at: Optional[datetime] = Field(None, description="When the record was soft deleted")
    
    class Config:
        orm_mode = True 