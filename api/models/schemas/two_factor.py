"""
Schemas for two-factor authentication.

This module contains Pydantic models for two-factor authentication.
"""

from pydantic import BaseModel, Field
from typing import List, Optional

class TwoFactorSetup(BaseModel):
    """Schema for two-factor authentication setup response."""
    secret: str = Field(..., description="TOTP secret key")
    qr_code: str = Field(..., description="Base64 encoded QR code image")
    backup_codes: List[str] = Field(..., description="Backup codes for account recovery")

class TwoFactorEnable(BaseModel):
    """Schema for enabling two-factor authentication."""
    code: str = Field(..., description="TOTP code for verification")

class TwoFactorVerify(BaseModel):
    """Schema for verifying two-factor authentication."""
    code: str = Field(..., description="TOTP code or backup code for verification")
    remember_device: bool = Field(False, description="Whether to remember this device")

class TwoFactorDisable(BaseModel):
    """Schema for disabling two-factor authentication."""
    code: str = Field(..., description="TOTP code or backup code for verification")
    password: str = Field(..., description="User's password for additional security")

class TwoFactorStatus(BaseModel):
    """Schema for two-factor authentication status."""
    enabled: bool = Field(..., description="Whether 2FA is enabled")
    backup_codes_remaining: Optional[int] = Field(None, description="Number of backup codes remaining")
    remembered_devices: Optional[int] = Field(None, description="Number of remembered devices") 