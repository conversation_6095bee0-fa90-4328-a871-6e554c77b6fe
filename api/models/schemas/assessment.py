"""
Pydantic schemas for the Assessment API.

This module contains the Pydantic models for validating and serializing data
related to assessments and test executions.
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator
from enum import Enum


class AssessmentStatus(str, Enum):
    """Enumeration of possible assessment statuses."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    REVIEWED = "reviewed"
    CANCELLED = "cancelled"


class AssessmentType(str, Enum):
    """Enumeration of possible assessment types."""
    VULNERABILITY = "vulnerability"
    PENETRATION = "penetration"
    CODE_REVIEW = "code_review"
    RED_TEAM = "red_team"
    COMPLIANCE = "compliance"
    CUSTOM = "custom"


class TestExecutionResult(str, Enum):
    """Enumeration of possible test execution results."""
    PENDING = "pending"
    PASS = "pass"
    FAIL = "fail"
    PARTIAL = "partial"
    BLOCKED = "blocked"
    NOT_APPLICABLE = "not_applicable"


class AssessmentBase(BaseModel):
    """Base model for assessment data validation."""
    name: str = Field(..., min_length=3, max_length=100, description="Name of the assessment")
    description: Optional[str] = Field(None, max_length=5000, description="Detailed description of the assessment")
    target_system: str = Field(..., min_length=1, max_length=200, description="System being assessed")
    assessment_type: AssessmentType = Field(..., description="Type of assessment")
    status: AssessmentStatus = Field(default=AssessmentStatus.PENDING, description="Current status of the assessment")
    start_date: Optional[datetime] = Field(None, description="Planned or actual start date")
    end_date: Optional[datetime] = Field(None, description="Planned or actual end date")
    environment_id: Optional[int] = Field(None, description="ID of the associated environment")

    @validator('end_date')
    def end_date_after_start_date(cls, v, values):
        """Validate that end_date is after start_date if both are provided."""
        if v and 'start_date' in values and values['start_date'] and v < values['start_date']:
            raise ValueError('End date must be after start date')
        return v


class AssessmentCreate(AssessmentBase):
    """Model for assessment creation requests."""
    pass


class AssessmentUpdate(BaseModel):
    """Model for assessment update requests."""
    name: Optional[str] = Field(None, min_length=3, max_length=100, description="Name of the assessment")
    description: Optional[str] = Field(None, max_length=5000, description="Detailed description of the assessment")
    target_system: Optional[str] = Field(None, min_length=1, max_length=200, description="System being assessed")
    assessment_type: Optional[AssessmentType] = Field(None, description="Type of assessment")
    status: Optional[AssessmentStatus] = Field(None, description="Current status of the assessment")
    start_date: Optional[datetime] = Field(None, description="Planned or actual start date")
    end_date: Optional[datetime] = Field(None, description="Planned or actual end date")
    environment_id: Optional[int] = Field(None, description="ID of the associated environment")

    @validator('end_date')
    def end_date_after_start_date(cls, v, values):
        """Validate that end_date is after start_date if both are provided."""
        if v and 'start_date' in values and values['start_date'] and v < values['start_date']:
            raise ValueError('End date must be after start date')
        return v


class Assessment(AssessmentBase):
    """Complete assessment model including database-generated fields."""
    id: int
    created_by: int
    created_time: datetime
    updated_time: datetime
    deleted_time: Optional[datetime] = None

    class Config:
        from_attributes = True


class TestExecutionBase(BaseModel):
    """Base model for test execution data validation."""
    test_case_id: int = Field(..., description="ID of the test case being executed")
    assessment_id: int = Field(..., description="ID of the assessment this execution belongs to")
    result: TestExecutionResult = Field(default=TestExecutionResult.PENDING, description="Result of the test execution")
    notes: Optional[str] = Field(None, max_length=5000, description="Notes about the test execution")
    evidence: Optional[Dict[str, Any]] = Field(None, description="Evidence supporting the test result")


class TestExecutionCreate(TestExecutionBase):
    """Model for test execution creation requests."""
    pass


class TestExecutionUpdate(BaseModel):
    """Model for test execution update requests."""
    result: Optional[TestExecutionResult] = Field(None, description="Result of the test execution")
    notes: Optional[str] = Field(None, max_length=5000, description="Notes about the test execution")
    evidence: Optional[Dict[str, Any]] = Field(None, description="Evidence supporting the test result")


class TestExecution(TestExecutionBase):
    """Complete test execution model including database-generated fields."""
    id: int
    executed_by: int
    executed_at: datetime

    class Config:
        from_attributes = True


class AssessmentWithExecutions(Assessment):
    """Assessment model with test executions included."""
    test_executions: List[TestExecution] = []


class AssessmentWithCampaigns(Assessment):
    """Assessment model with campaigns included."""
    campaigns: List["CampaignBase"] = []

    class Config:
        from_attributes = True


class AssessmentSummary(BaseModel):
    """Summary of assessment results."""
    total_tests: int
    passed: int
    failed: int
    partial: int
    pending: int
    blocked: int
    not_applicable: int
    completion_percentage: float
    pass_rate: float


class AssessmentReport(BaseModel):
    """Comprehensive assessment report."""
    assessment: Assessment
    summary: AssessmentSummary
    test_executions: List[TestExecution]
    campaigns: Optional[List["CampaignBase"]] = None
    mitre_coverage: Optional[Dict[str, Any]] = None
    recommendations: Optional[List[str]] = None


# Import at the end to avoid circular imports
from api.models.schemas.campaign import CampaignBase
AssessmentWithCampaigns.update_forward_refs()
AssessmentReport.update_forward_refs() 