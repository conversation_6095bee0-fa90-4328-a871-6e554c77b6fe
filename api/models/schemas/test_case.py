"""
Pydantic schemas for Test Case Management API.

This module defines the data validation models for the Test Case Management API.
These models are used for validating request and response data.
"""
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any

from pydantic import BaseModel, Field, validator


class TestCaseStatus(str, Enum):
    """Enumeration of possible test case statuses."""
    DRAFT = "draft"
    ACTIVE = "active"
    DEPRECATED = "deprecated"
    ARCHIVED = "archived"


class TestCaseType(str, Enum):
    """Enumeration of possible test case types."""
    MANUAL = "manual"
    AUTOMATED = "automated"
    HYBRID = "hybrid"


class TestCasePriority(str, Enum):
    """Enumeration of possible test case priorities."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class TestCaseComplexity(str, Enum):
    """Enumeration of possible test case complexity levels."""
    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"


class TestCaseBase(BaseModel):
    """Base model for test case data validation.
    
    Contains the common attributes shared between test case creation
    and response models.
    """
    name: str = Field(..., description="Name of the test case")
    description: Optional[str] = Field(None, description="Detailed description of the test case")
    type: TestCaseType = Field(default=TestCaseType.MANUAL, description="Type of test case")
    status: TestCaseStatus = Field(default=TestCaseStatus.DRAFT, description="Status of the test case")
    priority: TestCasePriority = Field(default=TestCasePriority.MEDIUM, description="Priority of the test case")
    complexity: TestCaseComplexity = Field(default=TestCaseComplexity.MODERATE, description="Complexity of the test case")
    prerequisites: Optional[str] = Field(None, description="Prerequisites for running the test case")
    steps: List[str] = Field(default=[], description="Steps to execute the test case")
    expected_result: str = Field(..., description="Expected result of the test case")
    tags: List[str] = Field(default=[], description="Tags associated with the test case")
    mitre_techniques: List[str] = Field(default=[], description="MITRE ATT&CK techniques associated with the test case")


class TestCaseCreate(TestCaseBase):
    """Model for test case creation requests."""
    pass


class TestCaseUpdate(BaseModel):
    """Model for test case update requests."""
    name: Optional[str] = Field(None, description="Name of the test case")
    description: Optional[str] = Field(None, description="Detailed description of the test case")
    type: Optional[TestCaseType] = Field(None, description="Type of test case")
    status: Optional[TestCaseStatus] = Field(None, description="Status of the test case")
    priority: Optional[TestCasePriority] = Field(None, description="Priority of the test case")
    complexity: Optional[TestCaseComplexity] = Field(None, description="Complexity of the test case")
    prerequisites: Optional[str] = Field(None, description="Prerequisites for running the test case")
    steps: Optional[List[str]] = Field(None, description="Steps to execute the test case")
    expected_result: Optional[str] = Field(None, description="Expected result of the test case")
    tags: Optional[List[str]] = Field(None, description="Tags associated with the test case")
    mitre_techniques: Optional[List[str]] = Field(None, description="MITRE ATT&CK techniques associated with the test case")


class TestCase(TestCaseBase):
    """Complete test case model including database-generated fields."""
    id: int = Field(..., description="Unique identifier for the test case")
    created_by: int = Field(..., description="ID of the user who created the test case")
    created_at: datetime = Field(..., description="Timestamp when the test case was created")
    updated_at: datetime = Field(..., description="Timestamp of the last test case update")
    deleted_at: Optional[datetime] = Field(None, description="Timestamp when the test case was deleted")
    is_deprecated: bool = Field(default=False, description="Whether the test case is deprecated")
    is_revoked: bool = Field(default=False, description="Whether the test case is revoked")
    revoked_by_id: Optional[int] = Field(None, description="ID of the user who revoked the test case")
    version: Optional[str] = Field(None, description="Version of the test case")

    class Config:
        """Pydantic configuration."""
        from_attributes = True


class TestCaseFilter(BaseModel):
    """Model for filtering test cases."""
    status: Optional[TestCaseStatus] = Field(None, description="Filter by test case status")
    type: Optional[TestCaseType] = Field(None, description="Filter by test case type")
    priority: Optional[TestCasePriority] = Field(None, description="Filter by test case priority")
    complexity: Optional[TestCaseComplexity] = Field(None, description="Filter by test case complexity")
    tags: Optional[List[str]] = Field(None, description="Filter by tags")
    mitre_techniques: Optional[List[str]] = Field(None, description="Filter by MITRE ATT&CK techniques")
    created_by: Optional[int] = Field(None, description="Filter by creator ID")
    search: Optional[str] = Field(None, description="Search term for name and description")


class TestCaseBulkCreate(BaseModel):
    """Model for bulk creating test cases."""
    test_cases: List[TestCaseCreate] = Field(..., description="List of test cases to create")


class TestCaseBulkResponse(BaseModel):
    """Model for bulk test case creation response."""
    test_cases: List[TestCase] = Field(..., description="List of created test cases")
    total_created: int = Field(..., description="Total number of test cases created")
    failed_entries: List[Dict[str, Any]] = Field(default=[], description="List of failed entries with errors")


class TestCaseWithCampaigns(TestCase):
    """Test case model with associated campaigns."""
    campaigns: List[Dict[str, Any]] = Field(default=[], description="List of campaigns associated with the test case")


class TestCaseDeprecate(BaseModel):
    """Model for deprecating a test case."""
    reason: Optional[str] = Field(None, description="Reason for deprecating the test case")


class TestCaseRevoke(BaseModel):
    """Model for revoking a test case."""
    reason: str = Field(..., description="Reason for revoking the test case")


class TestCaseStats(BaseModel):
    """Model for test case statistics."""
    total: int = Field(..., description="Total number of test cases")
    by_status: Dict[str, int] = Field(..., description="Count of test cases by status")
    by_type: Dict[str, int] = Field(..., description="Count of test cases by type")
    by_priority: Dict[str, int] = Field(..., description="Count of test cases by priority")
    by_complexity: Dict[str, int] = Field(..., description="Count of test cases by complexity")
    
    class Config:
        """Pydantic configuration."""
        schema_extra = {
            "example": {
                "total": 100,
                "by_status": {
                    "draft": 20,
                    "active": 60,
                    "deprecated": 15,
                    "archived": 5
                },
                "by_type": {
                    "manual": 50,
                    "automated": 30,
                    "hybrid": 20
                },
                "by_priority": {
                    "low": 10,
                    "medium": 40,
                    "high": 30,
                    "critical": 20
                },
                "by_complexity": {
                    "simple": 30,
                    "moderate": 50,
                    "complex": 20
                }
            }
        } 