"""
Pydantic schemas for Environment data validation.

This module contains Pydantic models for validating and serializing
environment-related data in the RegressionRigor platform.
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, validator
from enum import Enum


class EnvironmentType(str, Enum):
    """Enumeration of possible environment types."""
    PRODUCTION = "production"
    STAGING = "staging"
    DEVELOPMENT = "development"
    TEST = "test"
    OTHER = "other"


class EnvironmentStatus(str, Enum):
    """Enumeration of possible environment statuses."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DEPRECATED = "deprecated"


class EnvironmentBase(BaseModel):
    """Base model for environment data validation."""
    name: str = Field(..., min_length=1, max_length=255, description="Name of the environment")
    description: Optional[str] = Field(None, description="Detailed description of the environment")
    type: EnvironmentType = Field(EnvironmentType.TEST, description="Type of environment")
    status: EnvironmentStatus = Field(EnvironmentStatus.ACTIVE, description="Status of the environment")


class EnvironmentCreate(EnvironmentBase):
    """Model for environment creation requests."""
    pass


class EnvironmentUpdate(BaseModel):
    """Model for environment update requests."""
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Name of the environment")
    description: Optional[str] = Field(None, description="Detailed description of the environment")
    type: Optional[EnvironmentType] = Field(None, description="Type of environment")
    status: Optional[EnvironmentStatus] = Field(None, description="Status of the environment")


class Environment(EnvironmentBase):
    """Complete environment model including database-generated fields."""
    id: int
    created_by: int
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime] = None
    is_deprecated: bool = False
    is_revoked: bool = False
    revoked_by_id: Optional[int] = None
    version: int = 1

    class Config:
        """Pydantic configuration."""
        orm_mode = True


class EnvironmentWithAssessments(Environment):
    """Environment model including associated assessments."""
    assessments: List["AssessmentBase"] = []

    class Config:
        """Pydantic configuration."""
        orm_mode = True


# Import at the end to avoid circular imports
from api.models.schemas.assessment import AssessmentBase
EnvironmentWithAssessments.update_forward_refs() 