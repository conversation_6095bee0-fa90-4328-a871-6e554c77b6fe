"""
Pydantic schema models for the Enhanced Testcase Chaining & Sequencing feature.

This module contains the Pydantic models for validating and serializing data
related to testcase chains, nodes, edges, executions, and conditions.
"""
from datetime import datetime
from typing import List, Optional, Dict, Any, Literal
from pydantic import BaseModel, Field, validator


class TestcaseConditionBase(BaseModel):
    """Base model for testcase condition data validation."""
    testcase_id: int = Field(..., description="ID of the associated testcase")
    condition_type: str = Field(..., description="Type of condition (precondition, postcondition)")
    name: str = Field(..., description="Name of the condition")
    description: Optional[str] = Field(None, description="Detailed description of the condition")
    validation_script: Optional[str] = Field(None, description="Script to validate the condition")
    required: bool = Field(True, description="Whether this condition is required for execution")

    @validator('condition_type')
    def validate_condition_type(cls, v):
        """Validate that condition_type is either 'precondition' or 'postcondition'."""
        if v not in ['precondition', 'postcondition']:
            raise ValueError("condition_type must be either 'precondition' or 'postcondition'")
        return v


class TestcaseConditionCreate(TestcaseConditionBase):
    """Model for testcase condition creation requests."""
    pass


class TestcaseCondition(TestcaseConditionBase):
    """Complete testcase condition model including database-generated fields."""
    id: int
    created_time: datetime
    updated_time: datetime

    class Config:
        from_attributes = True


class TestcaseChainNodeBase(BaseModel):
    """Base model for testcase chain node data validation."""
    chain_id: int = Field(..., description="ID of the parent chain")
    testcase_id: int = Field(..., description="ID of the associated testcase")
    node_type: str = Field("standard", description="Type of node (standard, conditional, start, end)")
    position_x: float = Field(0, description="X-coordinate for UI positioning")
    position_y: float = Field(0, description="Y-coordinate for UI positioning")
    execution_order: int = Field(0, description="Order of execution within the chain")
    condition_expression: Optional[str] = Field(None, description="Condition expression for conditional nodes")

    @validator('node_type')
    def validate_node_type(cls, v):
        """Validate that node_type is one of the allowed values."""
        if v not in ['standard', 'conditional', 'start', 'end']:
            raise ValueError("node_type must be one of: standard, conditional, start, end")
        return v


class TestcaseChainNodeCreate(TestcaseChainNodeBase):
    """Model for testcase chain node creation requests."""
    pass


class TestcaseChainNode(TestcaseChainNodeBase):
    """Complete testcase chain node model including database-generated fields."""
    id: int
    created_time: datetime
    updated_time: datetime

    class Config:
        from_attributes = True


class TestcaseChainEdgeBase(BaseModel):
    """Base model for testcase chain edge data validation."""
    source_node_id: int = Field(..., description="ID of the source node")
    target_node_id: int = Field(..., description="ID of the target node")
    edge_type: str = Field("standard", description="Type of edge (standard, success_path, failure_path)")
    condition: Optional[str] = Field(None, description="Condition for taking this edge")

    @validator('edge_type')
    def validate_edge_type(cls, v):
        """Validate that edge_type is one of the allowed values."""
        if v not in ['standard', 'success_path', 'failure_path']:
            raise ValueError("edge_type must be one of: standard, success_path, failure_path")
        return v


class TestcaseChainEdgeCreate(TestcaseChainEdgeBase):
    """Model for testcase chain edge creation requests."""
    pass


class TestcaseChainEdge(TestcaseChainEdgeBase):
    """Complete testcase chain edge model including database-generated fields."""
    id: int
    created_time: datetime
    updated_time: datetime

    class Config:
        from_attributes = True


class TestcaseChainBase(BaseModel):
    """Base model for testcase chain data validation."""
    name: str = Field(..., description="Name of the testcase chain")
    description: Optional[str] = Field(None, description="Detailed description of the chain")
    created_by: int = Field(..., description="ID of the user who created the chain")
    status: str = Field("draft", description="Status of the chain (draft, active, completed, archived)")

    @validator('status')
    def validate_status(cls, v):
        """Validate that status is one of the allowed values."""
        if v not in ['draft', 'active', 'completed', 'archived']:
            raise ValueError("status must be one of: draft, active, completed, archived")
        return v


class TestcaseChainCreate(TestcaseChainBase):
    """Model for testcase chain creation requests."""
    pass


class TestcaseChain(TestcaseChainBase):
    """Complete testcase chain model including database-generated fields."""
    id: int
    created_time: datetime
    updated_time: datetime
    chain_nodes: Optional[List[TestcaseChainNode]] = None

    class Config:
        from_attributes = True


class ChainExecutionBase(BaseModel):
    """Base model for chain execution data validation."""
    chain_id: int = Field(..., description="ID of the chain being executed")
    started_by: int = Field(..., description="ID of the user who started the execution")
    status: str = Field("running", description="Status of the execution (running, completed, failed, aborted)")

    @validator('status')
    def validate_status(cls, v):
        """Validate that status is one of the allowed values."""
        if v not in ['running', 'completed', 'failed', 'aborted']:
            raise ValueError("status must be one of: running, completed, failed, aborted")
        return v


class ChainExecutionCreate(ChainExecutionBase):
    """Model for chain execution creation requests."""
    pass


class NodeExecutionBase(BaseModel):
    """Base model for node execution data validation."""
    chain_execution_id: int = Field(..., description="ID of the parent chain execution")
    node_id: int = Field(..., description="ID of the node being executed")
    status: str = Field("pending", description="Status of the node execution (pending, running, completed, failed, skipped)")
    result_data: Optional[Dict[str, Any]] = Field(None, description="Result data from the node execution")

    @validator('status')
    def validate_status(cls, v):
        """Validate that status is one of the allowed values."""
        if v not in ['pending', 'running', 'completed', 'failed', 'skipped']:
            raise ValueError("status must be one of: pending, running, completed, failed, skipped")
        return v


class NodeExecutionCreate(NodeExecutionBase):
    """Model for node execution creation requests."""
    pass


class NodeExecution(NodeExecutionBase):
    """Complete node execution model including database-generated fields."""
    id: int
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    created_time: datetime
    updated_time: datetime

    class Config:
        from_attributes = True


class ChainExecution(ChainExecutionBase):
    """Complete chain execution model including database-generated fields."""
    id: int
    start_time: datetime
    end_time: Optional[datetime] = None
    created_time: datetime
    updated_time: datetime
    node_executions: Optional[List[NodeExecution]] = None

    class Config:
        from_attributes = True 