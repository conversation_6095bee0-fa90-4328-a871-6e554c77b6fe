from sqlalchemy import Column, Integer, String, Float, Text, DateTime, ForeignKey, Index
from sqlalchemy.dialects.postgresql import JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from api.database import Base


class LogEntry(Base):
    """Model for storing log entries from both API and UI."""
    __tablename__ = 'log_entries'

    id = Column(Integer, primary_key=True)
    timestamp = Column(DateTime(timezone=True), nullable=False, index=True)
    correlation_id = Column(String(64), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    session_id = Column(String(64), nullable=True, index=True)
    log_level = Column(String(20), nullable=False, index=True)  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    source = Column(String(20), nullable=False, index=True)  # API, UI, SYSTEM
    component = Column(String(100), nullable=False, index=True)
    message = Column(Text, nullable=False)
    metadata = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)

    # Relationships
    user = relationship("User", back_populates="log_entries")

    # Create indexes for common queries
    __table_args__ = (
        Index('idx_log_entries_timestamp_level', timestamp, log_level),
        Index('idx_log_entries_correlation_timestamp', correlation_id, timestamp),
    )

    def __repr__(self):
        return f"<LogEntry(id={self.id}, timestamp={self.timestamp}, level={self.log_level}, source={self.source})>"


class PerformanceMetric(Base):
    """Model for storing performance metrics."""
    __tablename__ = 'performance_metrics'

    id = Column(Integer, primary_key=True)
    timestamp = Column(DateTime(timezone=True), nullable=False, index=True)
    metric_type = Column(String(50), nullable=False, index=True)  # RESPONSE_TIME, CPU_USAGE, MEMORY_USAGE, etc.
    component = Column(String(100), nullable=False, index=True)
    value = Column(Float, nullable=False)
    unit = Column(String(20), nullable=False)  # ms, %, MB, etc.
    correlation_id = Column(String(64), nullable=True, index=True)
    session_id = Column(String(64), nullable=True, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)

    # Create indexes for common queries
    __table_args__ = (
        Index('idx_metrics_timestamp_type', timestamp, metric_type),
    )

    def __repr__(self):
        return f"<PerformanceMetric(id={self.id}, type={self.metric_type}, value={self.value}{self.unit})>"


class TestScenario(Base):
    """Model for storing test scenarios."""
    __tablename__ = 'test_scenarios'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text, nullable=True)
    creator_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    scenario_data = Column(JSON, nullable=False)  # JSON structure with test steps, parameters, assertions
    last_run = Column(DateTime(timezone=True), nullable=True)
    status = Column(String(20), nullable=False, server_default='active')  # active, archived, deprecated
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    creator = relationship("User", back_populates="test_scenarios")
    test_results = relationship("TestResult", back_populates="test_scenario", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<TestScenario(id={self.id}, name={self.name})>"


class TestResult(Base):
    """Model for storing test execution results."""
    __tablename__ = 'test_results'

    id = Column(Integer, primary_key=True)
    test_scenario_id = Column(Integer, ForeignKey('test_scenarios.id'), nullable=False)
    run_timestamp = Column(DateTime(timezone=True), nullable=False)
    duration = Column(Float, nullable=False)  # Duration in seconds
    status = Column(String(20), nullable=False)  # passed, failed, error, skipped
    result_data = Column(JSON, nullable=False)  # Detailed results including step-by-step outcomes
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)

    # Relationships
    test_scenario = relationship("TestScenario", back_populates="test_results")

    # Create indexes for common queries
    __table_args__ = (
        Index('idx_test_results_scenario_timestamp', test_scenario_id, run_timestamp),
    )

    def __repr__(self):
        return f"<TestResult(id={self.id}, scenario={self.test_scenario_id}, status={self.status})>" 