"""Models for D3FEND ontology data and technique mappings."""
from sqlalchemy import (
    Column, Integer, String, Text, Float, Foreign<PERSON>ey, DateTime, Boolean, Table
)
from sqlalchemy.orm import Mapped, mapped_column, relationship
from api.database import Base
from datetime import datetime
from typing import Optional, List

# Association table for many-to-many relationships between D3FEND concepts
d3fend_relationships = Table(
    'd3fend_relationships',
    Base.metadata,
    Column('source_id', Integer, Foreign<PERSON>ey('d3fend_concepts.id', ondelete='CASCADE'), primary_key=True),
    <PERSON>umn('target_id', Integer, Foreign<PERSON>ey('d3fend_concepts.id', ondelete='CASCADE'), primary_key=True),
    <PERSON>umn('relationship_type', String(50), nullable=False)
)

# Association table for many-to-many relationships between D3FEND classes
d3fend_class_hierarchy = Table(
    'd3f_class_hierarchy',
    Base.metadata,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('subclass_id', <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>('d3f_classes.id', ondelete='CASCADE')),
    <PERSON><PERSON><PERSON>('superclass_id', Inte<PERSON>, <PERSON><PERSON><PERSON>('d3f_classes.id', ondelete='CASCADE'))
)

class D3FENDVersion(Base):
    """Model for tracking D3FEND ontology versions."""
    __tablename__ = 'd3fend_versions'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    version: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    import_date: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    is_current: Mapped[bool] = mapped_column(Boolean, default=False)

    # One-to-many relationship with concepts
    concepts: Mapped[List["D3FENDConcept"]] = relationship(
        "D3FENDConcept", 
        back_populates="version",
        cascade="all, delete-orphan"
    )

class D3FENDConcept(Base):
    """Model for D3FEND concepts."""
    __tablename__ = 'd3fend_concepts'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    uri: Mapped[str] = mapped_column(String(255), nullable=False, unique=True)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    type: Mapped[str] = mapped_column(String(50), nullable=False)
    definition: Mapped[Optional[str]] = mapped_column(Text)
    version_id: Mapped[int] = mapped_column(Integer, ForeignKey('d3fend_versions.id', ondelete='CASCADE'), nullable=False)

    # Additional metadata
    created: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    modified: Mapped[datetime] = mapped_column(DateTime, onupdate=datetime.utcnow)
    is_deprecated: Mapped[bool] = mapped_column(Boolean, default=False)

    # Relationships
    version: Mapped["D3FENDVersion"] = relationship("D3FENDVersion", back_populates="concepts")
    related_to: Mapped[List["D3FENDConcept"]] = relationship(
        "D3FENDConcept",
        secondary=d3fend_relationships,
        primaryjoin=id == d3fend_relationships.c.source_id,
        secondaryjoin=id == d3fend_relationships.c.target_id,
        backref="related_from"
    )

    # Additional metadata fields
    external_references: Mapped[Optional[str]] = mapped_column(Text)
    notes: Mapped[Optional[str]] = mapped_column(Text)

    def __repr__(self):
        """String representation."""
        return f"<D3FENDConcept {self.name}>"

class D3FENDClass(Base):
    """Model for D3FEND classes from the ontology."""
    __tablename__ = 'd3f_classes'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    uri: Mapped[str] = mapped_column(String(255), nullable=False, unique=True)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)

    # Relationships for class hierarchy
    subclasses: Mapped[List["D3FENDClass"]] = relationship(
        "D3FENDClass",
        secondary=d3fend_class_hierarchy,
        primaryjoin=id == d3fend_class_hierarchy.c.superclass_id,
        secondaryjoin=id == d3fend_class_hierarchy.c.subclass_id,
        backref="superclasses"
    )

    def __repr__(self):
        """String representation."""
        return f"<D3FENDClass {self.name}>"

class D3FENDProperty(Base):
    """Model for D3FEND properties from the ontology."""
    __tablename__ = 'd3f_properties'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    uri: Mapped[str] = mapped_column(String(255), nullable=False, unique=True)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    property_type: Mapped[str] = mapped_column(String(50), nullable=False)  # 'object' or 'datatype'
    description: Mapped[Optional[str]] = mapped_column(Text)

    def __repr__(self):
        """String representation."""
        return f"<D3FENDProperty {self.name}>"

# Association table for class-property relationships
d3fend_class_property_relationships = Table(
    'd3f_class_property_relationships',
    Base.metadata,
    Column('id', Integer, primary_key=True),
    Column('source_class_id', Integer, ForeignKey('d3f_classes.id', ondelete='CASCADE')),
    Column('property_id', Integer, ForeignKey('d3f_properties.id', ondelete='CASCADE')),
    Column('target_class_id', Integer, ForeignKey('d3f_classes.id', ondelete='CASCADE'))
)

class D3FENDDigitalArtifact(Base):
    """Model for D3FEND digital artifacts."""
    __tablename__ = 'd3f_digital_artifacts'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    class_id: Mapped[int] = mapped_column(Integer, ForeignKey('d3f_classes.id', ondelete='CASCADE'), nullable=False)
    artifact_name: Mapped[str] = mapped_column(String(255), nullable=False)

    # Relationship to the associated class
    d3fend_class: Mapped[D3FENDClass] = relationship("D3FENDClass")

    def __repr__(self):
        """String representation."""
        return f"<D3FENDDigitalArtifact {self.artifact_name}>"

class D3FENDCountermeasure(Base):
    """Model for D3FEND countermeasures."""
    __tablename__ = 'd3f_countermeasures'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    class_id: Mapped[int] = mapped_column(Integer, ForeignKey('d3f_classes.id', ondelete='CASCADE'), nullable=False)
    countermeasure_name: Mapped[str] = mapped_column(String(255), nullable=False)
    implementation_level: Mapped[Optional[str]] = mapped_column(String(50))

    # Relationship to the associated class
    d3fend_class: Mapped[D3FENDClass] = relationship("D3FENDClass")

    def __repr__(self):
        """String representation."""
        return f"<D3FENDCountermeasure {self.countermeasure_name}>"

class D3fendMappingDB(Base):
    """Model for mapping MITRE ATT&CK techniques to D3FEND defensive controls.

    This model tracks relationships between offensive techniques and their defensive
    countermeasures, including effectiveness scores and implementation details.

    Attributes:
        id: Primary key
        technique_id: MITRE ATT&CK technique identifier (e.g., T1566)
        name: Name of the defensive control
        effectiveness_score: Score between 0 and 1 indicating control effectiveness
        description: Detailed description of how the control mitigates the technique
    """
    __tablename__ = "d3fend_mappings"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    technique_id: Mapped[str] = mapped_column(String, index=True, nullable=False)
    name: Mapped[str] = mapped_column(String, nullable=False)
    effectiveness_score: Mapped[float] = mapped_column(Float, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    created_at: Mapped[datetime] = mapped_column(
        DateTime, nullable=False, default=datetime.utcnow
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow
    )

    def __repr__(self) -> str:
        """String representation of the mapping."""
        return f"<D3fendMapping {self.technique_id}: {self.name}>"