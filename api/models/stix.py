"""STIX 2.1 models and utilities for the cybersecurity data platform."""
from typing import Dict, List, Optional, Union, Literal
from datetime import datetime
import stix2
from pydantic import BaseModel, Field, validator
from sqlalchemy import Column, String, JSON
from .base import Base, VersionMixin
import json
import logging

logger = logging.getLogger(__name__)

class StixObject(BaseModel):
    """Base class for STIX objects in our platform."""
    type: str = Field(..., description="STIX object type")
    id: str = Field(..., description="STIX identifier")
    created: datetime = Field(default_factory=datetime.utcnow)
    modified: datetime = Field(default_factory=datetime.utcnow)

    def to_stix(self) -> stix2.v20.base._STIXBase20:
        """Convert the object to a STIX object."""
        try:
            # Use the appropriate STIX2 class based on type
            if self.type == "attack-pattern":
                return stix2.v20.AttackPattern(**self.dict())
            elif self.type == "malware":
                return stix2.v20.Malware(**self.dict())
            elif self.type == "tool":
                return stix2.v20.Tool(**self.dict())
            else:
                # Generic STIX object
                return stix2.v20.CustomObject(**self.dict())
        except Exception as e:
            logger.error(f"Error converting to STIX: {str(e)}")
            raise ValueError(f"Error converting to STIX: {str(e)}")

    @classmethod
    def from_stix(cls, stix_obj: Union[dict, stix2.v20.base._STIXBase20]) -> "StixObject":
        """Create an instance from a STIX object."""
        try:
            if isinstance(stix_obj, stix2.v20.base._STIXBase20):
                stix_dict = dict(stix_obj)
            else:
                stix_dict = stix_obj
            return cls(**stix_dict)
        except Exception as e:
            logger.error(f"Error creating from STIX: {str(e)}")
            raise ValueError(f"Error creating from STIX: {str(e)}")

class StixBundle(BaseModel):
    """Model for STIX2 bundles."""
    type: Literal["bundle"] = Field("bundle", description="Must be 'bundle'")
    id: str = Field(..., description="Bundle identifier")
    objects: List[StixObject] = Field(default_factory=list)

    def to_stix(self) -> stix2.v20.Bundle:
        """Convert to STIX bundle."""
        try:
            stix_objects = [obj.to_stix() for obj in self.objects]
            return stix2.v20.Bundle(objects=stix_objects)
        except Exception as e:
            logger.error(f"Error converting bundle to STIX: {str(e)}")
            raise ValueError(f"Error converting bundle to STIX: {str(e)}")

    @classmethod
    def from_stix(cls, bundle: Union[dict, stix2.v20.Bundle]) -> "StixBundle":
        """Create from STIX bundle."""
        try:
            if isinstance(bundle, stix2.v20.Bundle):
                bundle_dict = dict(bundle)
            else:
                bundle_dict = bundle
            return cls(
                id=bundle_dict["id"],
                objects=[StixObject.from_stix(obj) for obj in bundle_dict["objects"]]
            )
        except Exception as e:
            logger.error(f"Error creating bundle from STIX: {str(e)}")
            raise ValueError(f"Error creating bundle from STIX: {str(e)}")

class StixDB(Base, VersionMixin):
    """Database model for storing STIX objects."""
    __tablename__ = 'stix_objects'

    stix_id = Column(String, primary_key=True, 
                     doc="STIX identifier (e.g. attack-pattern--123)")
    type = Column(String, nullable=False, index=True,
                 doc="STIX object type (e.g. attack-pattern)")
    data = Column(JSON, nullable=False,
                 doc="Complete STIX object as JSON")

    def to_stix(self) -> stix2.v20.base._STIXBase20:
        """Convert the database record to a STIX object."""
        try:
            return StixObject.from_stix(self.data).to_stix()
        except Exception as e:
            logger.error(f"Error converting database record to STIX: {str(e)}")
            raise ValueError(f"Error converting database record to STIX: {str(e)}")

    @classmethod
    def from_stix(cls, obj: Union[dict, stix2.v20.base._STIXBase20]) -> "StixDB":
        """Create a database record from a STIX object."""
        try:
            # Convert STIX object to dictionary format
            if isinstance(obj, stix2.v20.base._STIXBase20):
                stix_dict = dict(obj)
            else:
                stix_obj = StixObject.from_stix(obj).to_stix()
                stix_dict = dict(stix_obj)

            # Custom JSON serializer to handle special STIX types
            def stix_json_serializer(obj):
                if hasattr(obj, 'isoformat'):  # Handle datetime objects
                    return obj.isoformat()
                if isinstance(obj, stix2.v20.base._STIXBase20):  # Handle nested STIX objects
                    return dict(obj)
                if hasattr(obj, '__dict__'):  # Handle other custom objects
                    return dict(obj)
                return str(obj)  # Fallback for other non-serializable types

            # Serialize to JSON-compatible format
            data_serialized = json.dumps(stix_dict, default=stix_json_serializer)
            data_dict = json.loads(data_serialized)

            return cls(
                stix_id=data_dict['id'],
                type=data_dict['type'],
                data=data_dict
            )
        except Exception as e:
            logger.error(f"Failed to create STIX database record: {str(e)}")
            raise ValueError(f"Failed to create STIX database record: {str(e)}") from e