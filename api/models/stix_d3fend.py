"""STIX 2.1 to D3FEND mapping models and utilities."""
from typing import Dict, List, Optional, Union
from datetime import datetime
import stix2
from pydantic import BaseModel, Field
from sqlalchemy import Column, String, JSON, ForeignKey, Float, Text
from sqlalchemy.orm import relationship
from .stix import StixObject, StixDB
from .base import Base, VersionMixin

class D3fendMapping(BaseModel):
    """Mapping between STIX and D3FEND objects."""
    stix_id: str = Field(..., description="STIX object identifier")
    d3fend_id: str = Field(..., description="D3FEND concept identifier")
    mapping_type: str = Field(..., description="Type of mapping relationship")
    confidence: float = Field(
        default=1.0,
        ge=0.0,
        le=1.0,
        description="Confidence score for the mapping"
    )
    mapping_metadata: Dict = Field(
        default_factory=dict,
        description="Additional mapping metadata"
    )

class D3fendMappingDB(Base, VersionMixin):
    """Database model for storing STIX to D3FEND mappings."""
    __tablename__ = 'stix_d3fend_mappings'

    id = Column(String, primary_key=True)
    stix_id = Column(String, ForeignKey('stix_objects.stix_id'), nullable=False)
    d3fend_id = Column(String, nullable=False)
    technique_id = Column(String, nullable=False)  # Added technique_id field
    mapping_type = Column(String, nullable=False)
    confidence = Column(Float, nullable=False)
    countermeasure_name = Column(String)  # Added for visualization
    description = Column(Text)  # Added for visualization
    effectiveness_score = Column(Float, default=0.5)  # Added for coverage calculation
    mapping_metadata = Column(JSON, default={})

    stix_object = relationship("StixDB")

    def __repr__(self):
        return f"<D3fendMapping(id={self.id}, technique_id={self.technique_id}, d3fend_id={self.d3fend_id})>"

def map_attack_pattern(stix_obj: Union[dict, stix2.AttackPattern]) -> List[D3fendMapping]:
    """Map a STIX Attack Pattern to D3FEND techniques.

    Args:
        stix_obj: STIX Attack Pattern object

    Returns:
        List[D3fendMapping]: List of D3FEND mappings for this object
    """
    if isinstance(stix_obj, dict):
        stix_obj = stix2.parse_object(stix_obj)

    mappings = []

    # Use external references to find D3FEND mappings
    for ref in getattr(stix_obj, 'external_references', []):
        if ref.get('source_name') == 'd3fend':
            mappings.append(D3fendMapping(
                stix_id=stix_obj.id,
                d3fend_id=ref['external_id'],
                mapping_type='attack-pattern',
                confidence=1.0,
                mapping_metadata={
                    'stix_type': 'attack-pattern',
                    'name': stix_obj.name,
                    'description': stix_obj.description
                }
            ))

    return mappings

def map_course_of_action(stix_obj: Union[dict, stix2.CourseOfAction]) -> List[D3fendMapping]:
    """Map a STIX Course of Action to D3FEND countermeasures.

    Args:
        stix_obj: STIX Course of Action object

    Returns:
        List[D3fendMapping]: List of D3FEND mappings for this object
    """
    if isinstance(stix_obj, dict):
        stix_obj = stix2.parse_object(stix_obj)

    mappings = []

    # Use external references to find D3FEND mappings
    for ref in getattr(stix_obj, 'external_references', []):
        if ref.get('source_name') == 'd3fend':
            mappings.append(D3fendMapping(
                stix_id=stix_obj.id,
                d3fend_id=ref['external_id'],
                mapping_type='course-of-action',
                confidence=1.0,
                mapping_metadata={
                    'stix_type': 'course-of-action',
                    'name': stix_obj.name,
                    'description': stix_obj.description
                }
            ))

    return mappings

def map_relationship(stix_obj: Union[dict, stix2.Relationship]) -> Optional[D3fendMapping]:
    """Map a STIX Relationship to D3FEND relationships.

    Args:
        stix_obj: STIX Relationship object

    Returns:
        Optional[D3fendMapping]: D3FEND mapping if applicable
    """
    if isinstance(stix_obj, dict):
        stix_obj = stix2.parse_object(stix_obj)

    # Only map relationships between objects that have D3FEND mappings
    if (stix_obj.relationship_type == 'mitigates' and
        stix_obj.source_ref.startswith('course-of-action') and
        stix_obj.target_ref.startswith('attack-pattern')):

        return D3fendMapping(
            stix_id=stix_obj.id,
            d3fend_id=f"rel-{stix_obj.source_ref}-{stix_obj.target_ref}",
            mapping_type='relationship',
            confidence=1.0,
            mapping_metadata={
                'stix_type': 'relationship',
                'relationship_type': stix_obj.relationship_type,
                'source_ref': stix_obj.source_ref,
                'target_ref': stix_obj.target_ref
            }
        )

    return None