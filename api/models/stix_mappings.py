"""SQLAlchemy models for STIX-D3FEND mappings."""
from sqlalchemy import Column, Integer, String, Float, JSON, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime
from api.database import Base

class STIXd3fendMapping(Base):
    """Mapping between STIX techniques and D3FEND countermeasures."""
    __tablename__ = "stix_d3fend_mappings"

    id = Column(Integer, primary_key=True)
    stix_id = Column(String(50), nullable=False)
    d3fend_id = Column(String(50), nullable=False)
    technique_id = Column(String(50), nullable=False, index=True)  # Added index for better query performance
    mapping_type = Column(String(50), nullable=False)
    confidence = Column(Float, default=0.5)
    countermeasure_name = Column(String(255), nullable=False)
    description = Column(String(1000))
    effectiveness_score = Column(Float, default=0.5)
    mapping_metadata = Column(JSON)
    created_time = Column(DateTime, default=datetime.utcnow)
    updated_time = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_time = Column(DateTime)

    def __repr__(self):
        """String representation."""
        return f"<STIXd3fendMapping {self.technique_id}: {self.countermeasure_name}>"