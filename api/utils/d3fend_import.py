"""D3FEND ontology import utilities."""
import logging
from pathlib import Path
from datetime import datetime
import os
from rdflib import Graph, OWL, RDF, RDFS, URIRef, Literal
from sqlalchemy.orm import Session
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

from api.models.d3fend import (
    D3FENDVersion,
    D3FENDClass,
    D3FENDProperty,
    D3FENDCountermeasure,
    d3fend_class_hierarchy,
    d3fend_class_property_relationships
)

# Configure logging
logger = logging.getLogger(__name__)

def extract_local_name(uri: str) -> str:
    """Extract the local name from a URI."""
    return uri.split('#')[-1] if '#' in uri else uri.split('/')[-1]

def cleanup_d3fend_data(db: Session) -> None:
    """Clean up existing D3FEND data in test environment."""
    try:
        if os.environ.get("TESTING") == "true":
            logger.info("Cleaning up existing D3FEND data for testing")
            # Delete in correct order to respect foreign key constraints
            db.query(D3FENDCountermeasure).delete()
            db.execute(text("DELETE FROM d3f_class_property_relationships"))
            db.execute(text("DELETE FROM d3f_class_hierarchy"))
            db.query(D3FENDProperty).delete()
            db.query(D3FENDClass).delete()
            db.commit()
            logger.info("Successfully cleaned up D3FEND data")
    except Exception as e:
        logger.error(f"Error cleaning up D3FEND data: {e}", exc_info=True)
        db.rollback()
        raise

def create_class(db: Session, uri: str, name: str, description: str) -> D3FENDClass:
    """Create a D3FEND class with strict validation."""
    if not uri or not isinstance(uri, str):
        raise ValueError("URI is required and must be a string")
    if not name or not isinstance(name, str):
        raise ValueError("Name is required and must be a string")
    description = description or ""  # Convert None to empty string

    class_obj = D3FENDClass(
        uri=uri,
        name=name,
        description=description
    )
    db.add(class_obj)
    return class_obj

def create_property(db: Session, uri: str, name: str, property_type: str, description: str) -> D3FENDProperty:
    """Create a D3FEND property."""
    if property_type not in ['object', 'datatype']:
        raise ValueError("Property type must be either 'object' or 'datatype'")
    description = description or ""  # Convert None to empty string

    prop = D3FENDProperty(
        uri=uri,
        name=name,
        property_type=property_type,
        description=description
    )
    db.add(prop)
    return prop

def import_d3fend_ontology_from_owl(db: Session, owl_path: str) -> None:
    """Import D3FEND ontology from OWL file."""
    logger.info(f"Starting D3FEND ontology import from OWL file: {owl_path}")

    try:
        # Clean up existing data in test environment
        cleanup_d3fend_data(db)

        # Load OWL file
        g = Graph()
        g.parse(owl_path, format="xml")
        logger.info("Successfully loaded OWL file")

        # Track created objects
        uri_to_class = {}
        uri_to_property = {}

        # First pass: Create all classes
        for subject in g.subjects(RDF.type, OWL.Class):
            if not isinstance(subject, URIRef):
                continue

            uri = str(subject)
            name = extract_local_name(uri)
            label = next((str(o) for o in g.objects(subject, RDFS.label)), name)
            description = next((str(o) for o in g.objects(subject, RDFS.comment)), "")

            try:
                class_obj = create_class(db, uri, label, description)
                uri_to_class[uri] = class_obj
                logger.debug(f"Created class: {label}")
            except ValueError as e:
                logger.error(f"Invalid class data: {e}")
                continue

        # Second pass: Create properties
        for pred in g.subjects(RDF.type, OWL.ObjectProperty):
            if not isinstance(pred, URIRef):
                continue

            uri = str(pred)
            name = extract_local_name(uri)
            label = next((str(o) for o in g.objects(pred, RDFS.label)), name)
            description = next((str(o) for o in g.objects(pred, RDFS.comment)), "")

            try:
                prop = create_property(db, uri, label, 'object', description)
                uri_to_property[uri] = prop
                logger.debug(f"Created object property: {label}")
            except ValueError as e:
                logger.error(f"Invalid property data: {e}")
                continue

        db.flush()

        # Third pass: Create class hierarchy
        for subject in g.subjects(RDF.type, OWL.Class):
            subject_uri = str(subject)
            if subject_uri not in uri_to_class:
                continue

            subclass = uri_to_class[subject_uri]

            for parent in g.objects(subject, RDFS.subClassOf):
                if not isinstance(parent, URIRef):
                    continue

                parent_uri = str(parent)
                if parent_uri not in uri_to_class:
                    continue

                superclass = uri_to_class[parent_uri]

                # Add to hierarchy table
                db.execute(
                    d3fend_class_hierarchy.insert().values(
                        subclass_id=subclass.id,
                        superclass_id=superclass.id
                    )
                )
                logger.debug(f"Created hierarchy: {subclass.name} -> {superclass.name}")

        # Fourth pass: Create property relationships and identify countermeasures
        for subject in g.subjects(RDF.type, OWL.Class):
            subject_uri = str(subject)
            if subject_uri not in uri_to_class:
                continue

            source_class = uri_to_class[subject_uri]

            # Check if this is a countermeasure class
            if "Countermeasure" in source_class.name:
                # Create countermeasure record
                countermeasure = D3FENDCountermeasure(
                    class_id=source_class.id,
                    countermeasure_name=source_class.name,
                    implementation_level=None  # Default to None if not specified
                )
                db.add(countermeasure)
                logger.debug(f"Created countermeasure: {source_class.name}")

            # Handle property relationships
            for pred, obj in g.predicate_objects(subject):
                pred_uri = str(pred)
                if pred_uri not in uri_to_property:
                    continue

                property_obj = uri_to_property[pred_uri]

                if isinstance(obj, URIRef) and str(obj) in uri_to_class:
                    target_class = uri_to_class[str(obj)]

                    # Add to relationships table
                    db.execute(
                        d3fend_class_property_relationships.insert().values(
                            source_class_id=source_class.id,
                            property_id=property_obj.id,
                            target_class_id=target_class.id
                        )
                    )
                    logger.debug(f"Created property relationship: {source_class.name} -> {property_obj.name} -> {target_class.name}")

        db.commit()
        logger.info("Successfully imported D3FEND ontology")

    except Exception as e:
        db.rollback()
        logger.error(f"Failed to import D3FEND ontology: {e}", exc_info=True)
        raise

def import_d3fend_ontology(db: Session, file_path: str) -> None:
    """Import D3FEND ontology."""
    if file_path.endswith('.owl'):
        import_d3fend_ontology_from_owl(db, file_path)
    else:
        raise ValueError("Only OWL file format is currently supported")

# Add file handler for persistent logging
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)
file_handler = logging.FileHandler(log_dir / "d3fend_import.log")
file_handler.setFormatter(
    logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
)
logger.addHandler(file_handler)