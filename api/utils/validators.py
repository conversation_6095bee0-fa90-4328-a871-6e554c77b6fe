"""Validation utilities for API input validation."""

import re
from typing import <PERSON><PERSON>, Dict, Any, Optional, List
from pydantic import BaseModel, validator, Field, EmailStr
from fastapi import HTTPException, status
import logging

logger = logging.getLogger(__name__)

# Regular expressions for validation
USERNAME_PATTERN = r"^[a-zA-Z0-9_-]{3,32}$"
PASSWORD_PATTERN = r"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$"
NAME_PATTERN = r"^[a-zA-Z0-9\s\-']{2,100}$"
MITRE_ID_PATTERN = r"^T\d{4}(\.\d{3})?$"

def validate_password(password: str) -> Tuple[bool, str]:
    """
    Validate a password against security requirements.
    
    Requirements:
    - At least 8 characters
    - At least one uppercase letter
    - At least one lowercase letter
    - At least one digit
    - At least one special character
    
    Args:
        password: The password to validate
        
    Returns:
        Tuple of (is_valid, message)
    """
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    
    if not re.search(r"[A-Z]", password):
        return False, "Password must contain at least one uppercase letter"
    
    if not re.search(r"[a-z]", password):
        return False, "Password must contain at least one lowercase letter"
    
    if not re.search(r"\d", password):
        return False, "Password must contain at least one digit"
    
    if not re.search(r"[@$!%*?&]", password):
        return False, "Password must contain at least one special character (@$!%*?&)"
    
    return True, "Password is valid"

def validate_username(username: str) -> Tuple[bool, str]:
    """
    Validate a username.
    
    Requirements:
    - 3-32 characters
    - Alphanumeric characters, underscores, and hyphens only
    
    Args:
        username: The username to validate
        
    Returns:
        Tuple of (is_valid, message)
    """
    if not re.match(USERNAME_PATTERN, username):
        return False, "Username must be 3-32 characters and contain only letters, numbers, underscores, and hyphens"
    
    return True, "Username is valid"

def validate_email(email: str) -> Tuple[bool, str]:
    """
    Validate an email address.
    
    Args:
        email: The email to validate
        
    Returns:
        Tuple of (is_valid, message)
    """
    # Basic email validation pattern
    email_pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    
    if not re.match(email_pattern, email):
        return False, "Invalid email format"
    
    return True, "Email is valid"

def validate_mitre_technique_id(technique_id: str) -> Tuple[bool, str]:
    """
    Validate a MITRE ATT&CK technique ID.
    
    Requirements:
    - Format: TXXXX or TXXXX.XXX
    
    Args:
        technique_id: The technique ID to validate
        
    Returns:
        Tuple of (is_valid, message)
    """
    if not re.match(MITRE_ID_PATTERN, technique_id):
        return False, "Invalid MITRE technique ID format. Must be TXXXX or TXXXX.XXX"
    
    return True, "MITRE technique ID is valid"

def validate_pagination_params(skip: int, limit: int, max_limit: int = 100) -> Tuple[bool, str]:
    """
    Validate pagination parameters.
    
    Args:
        skip: Number of items to skip
        limit: Maximum number of items to return
        max_limit: Maximum allowed limit
        
    Returns:
        Tuple of (is_valid, message)
    """
    if skip < 0:
        return False, "Skip parameter must be non-negative"
    
    if limit < 1:
        return False, "Limit parameter must be positive"
    
    if limit > max_limit:
        return False, f"Limit parameter must not exceed {max_limit}"
    
    return True, "Pagination parameters are valid"

def validate_date_range(start_date: Optional[str], end_date: Optional[str]) -> Tuple[bool, str]:
    """
    Validate a date range.
    
    Args:
        start_date: Start date in ISO format (YYYY-MM-DD)
        end_date: End date in ISO format (YYYY-MM-DD)
        
    Returns:
        Tuple of (is_valid, message)
    """
    if start_date and end_date:
        try:
            from datetime import datetime
            start = datetime.fromisoformat(start_date)
            end = datetime.fromisoformat(end_date)
            
            if start > end:
                return False, "Start date must be before end date"
        except ValueError:
            return False, "Invalid date format. Use ISO format (YYYY-MM-DD)"
    
    return True, "Date range is valid"

def validate_json_data(data: Dict[str, Any], required_fields: List[str]) -> Tuple[bool, str]:
    """
    Validate JSON data against required fields.
    
    Args:
        data: The JSON data to validate
        required_fields: List of required field names
        
    Returns:
        Tuple of (is_valid, message)
    """
    missing_fields = [field for field in required_fields if field not in data]
    
    if missing_fields:
        return False, f"Missing required fields: {', '.join(missing_fields)}"
    
    return True, "JSON data is valid"

def validate_request_data(data: Dict[str, Any], validators: Dict[str, callable]) -> List[str]:
    """
    Validate request data using multiple validators.
    
    Args:
        data: The request data to validate
        validators: Dictionary mapping field names to validator functions
        
    Returns:
        List of error messages, empty if validation passes
    """
    errors = []
    
    for field, validator_func in validators.items():
        if field in data:
            is_valid, message = validator_func(data[field])
            if not is_valid:
                errors.append(message)
    
    return errors

# Pydantic validators for common fields
class CommonValidators:
    @validator('username')
    def username_must_be_valid(cls, v):
        is_valid, message = validate_username(v)
        if not is_valid:
            raise ValueError(message)
        return v
    
    @validator('password')
    def password_must_be_valid(cls, v):
        is_valid, message = validate_password(v)
        if not is_valid:
            raise ValueError(message)
        return v
    
    @validator('email')
    def email_must_be_valid(cls, v):
        is_valid, message = validate_email(v)
        if not is_valid:
            raise ValueError(message)
        return v 