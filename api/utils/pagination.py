"""Pagination utility for database queries."""
from typing import Any, Dict, List
from sqlalchemy.orm import Query
from flask import request

def paginate(query: Query, page: int = 1, per_page: int = 10) -> Dict[str, Any]:
    """Paginate a SQLAlchemy query.
    
    Args:
        query: The query to paginate
        page: The current page number (1-indexed)
        per_page: Number of items per page
        
    Returns:
        Dict containing pagination information and items
    """
    if page < 1:
        page = 1
    
    total = query.count()
    items = query.offset((page - 1) * per_page).limit(per_page).all()
    
    total_pages = (total + per_page - 1) // per_page
    
    return {
        'items': items,
        'total': total,
        'page': page,
        'per_page': per_page,
        'total_pages': total_pages,
        'has_next': page < total_pages,
        'has_prev': page > 1,
        'next_page': page + 1 if page < total_pages else None,
        'prev_page': page - 1 if page > 1 else None
    }
