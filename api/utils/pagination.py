"""
Pagination utilities for API endpoints.

This module provides utilities for implementing consistent pagination
across API endpoints, following REST API best practices.
"""

from typing import List, Optional, TypeVar, Generic, Any, Dict
from pydantic import BaseModel, Field
from sqlalchemy.orm import Query
from fastapi import Query as FastAPIQuery

# Type variable for paginated items
T = TypeVar('T')


class PaginationParams(BaseModel):
    """
    Pagination parameters for API requests.

    This class defines standard pagination parameters that can be used
    across all API endpoints requiring pagination.
    """

    page: int = Field(
        default=1,
        description="Page number (1-based)",
        ge=1,
        le=10000
    )
    page_size: int = Field(
        default=20,
        description="Number of items per page",
        ge=1,
        le=100
    )

    @property
    def offset(self) -> int:
        """Calculate offset for database queries."""
        return (self.page - 1) * self.page_size

    @property
    def limit(self) -> int:
        """Get limit for database queries."""
        return self.page_size


def paginate_query(
    query: Query,
    pagination: PaginationParams
) -> List:
    """
    Apply pagination to a SQLAlchemy query.

    Args:
        query: SQLAlchemy query object
        pagination: Pagination parameters

    Returns:
        List of paginated results
    """
    return query.offset(pagination.offset).limit(pagination.limit).all()


def paginate(query: Query, page: int = 1, per_page: int = 10) -> Dict[str, Any]:
    """
    Legacy pagination function for backward compatibility.

    Args:
        query: The query to paginate
        page: The current page number (1-indexed)
        per_page: Number of items per page

    Returns:
        Dict containing pagination information and items
    """
    if page < 1:
        page = 1

    total = query.count()
    items = query.offset((page - 1) * per_page).limit(per_page).all()

    total_pages = (total + per_page - 1) // per_page

    return {
        'items': items,
        'total': total,
        'page': page,
        'per_page': per_page,
        'total_pages': total_pages,
        'has_next': page < total_pages,
        'has_prev': page > 1,
        'next_page': page + 1 if page < total_pages else None,
        'prev_page': page - 1 if page > 1 else None
    }
