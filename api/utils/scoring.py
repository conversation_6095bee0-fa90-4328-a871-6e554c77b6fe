
"""Utility functions for scoring MITRE techniques."""
from typing import Dict, Any
from sqlalchemy.orm import Session
from api.models.mitre import MitreTechnique

def calculate_technique_score(technique: MitreTechnique, params: Dict[str, Any] = None) -> float:
    """Calculate a score for a MITRE technique based on various parameters."""
    base_score = 1.0
    if not params:
        return base_score
        
    # Add scoring logic based on parameters
    if params.get("severity"):
        base_score *= float(params["severity"])
    
    return min(base_score, 10.0)  # Cap at 10.0
