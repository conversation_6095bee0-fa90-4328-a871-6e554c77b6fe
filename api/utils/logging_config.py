"""Centralized logging configuration for the application."""
import logging
import os
from logging.handlers import RotatingFileHandler
from pathlib import Path

def setup_logging(app_name: str = "api") -> None:
    """Configure logging with both file and console handlers.
    
    Args:
        app_name: The name of the application, used for the logger name
    """
    # Create logs directory if it doesn't exist
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG if os.environ.get("TESTING") == "true" else logging.INFO)
    
    # Clear any existing handlers
    root_logger.handlers.clear()
    
    # Create formatters
    verbose_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Console Handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(verbose_formatter)
    console_handler.setLevel(logging.DEBUG if os.environ.get("TESTING") == "true" else logging.INFO)
    
    # File Handler (with rotation)
    file_handler = RotatingFileHandler(
        filename=log_dir / f"{app_name}.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setFormatter(verbose_formatter)
    file_handler.setLevel(logging.DEBUG)
    
    # Add handlers to root logger
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    
    # Create application logger
    app_logger = logging.getLogger(app_name)
    app_logger.setLevel(logging.DEBUG if os.environ.get("TESTING") == "true" else logging.INFO)
    
    logging.info(f"Logging configured for {app_name}")
