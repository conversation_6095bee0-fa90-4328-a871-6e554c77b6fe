"""Utility functions for STIX to D3FEND mappings.

This module provides helper functions for working with STIX to D3FEND mappings,
including:
- Mapping STIX bundles to D3FEND concepts
- Validating mapping relationships
- Managing mapping persistence
"""
from typing import List, Dict, Optional, Union
import stix2
import logging
from sqlalchemy.orm import Session
from api.models.stix import StixDB
from api.models.stix_d3fend import (
    D3fendMapping, D3fendMappingDB,
    map_attack_pattern, map_course_of_action, map_relationship
)

logger = logging.getLogger(__name__)

def map_stix_bundle(bundle: Union[stix2.Bundle, dict]) -> List[D3fendMapping]:
    """Map all objects in a STIX bundle to their D3FEND equivalents.

    Args:
        bundle: STIX bundle to map

    Returns:
        List[D3fendMapping]: List of all D3FEND mappings found
    """
    if isinstance(bundle, dict):
        bundle = stix2.parse(bundle, allow_custom=True)

    mappings = []

    for obj in bundle.objects:
        if isinstance(obj, stix2.AttackPattern):
            mappings.extend(map_attack_pattern(obj))
        elif isinstance(obj, stix2.CourseOfAction):
            mappings.extend(map_course_of_action(obj))
        elif isinstance(obj, stix2.Relationship):
            mapping = map_relationship(obj)
            if mapping:
                mappings.append(mapping)

    return mappings

def import_stix_mappings(db: Session, bundle: Union[stix2.Bundle, dict]) -> List[D3fendMappingDB]:
    """Import STIX to D3FEND mappings into the database.

    Args:
        db: Database session
        bundle: STIX bundle containing objects to map

    Returns:
        List[D3fendMappingDB]: List of imported mapping records
    """
    if isinstance(bundle, dict):
        bundle = stix2.parse(bundle, allow_custom=True)

    # First, ensure all STIX objects are stored
    for obj in bundle.objects:
        try:
            stix_obj = StixDB.from_stix(obj)
            existing = db.query(StixDB).filter(StixDB.stix_id == stix_obj.stix_id).first()
            if not existing:
                logger.info(f"Storing STIX object: {stix_obj.stix_id}")
                db.add(stix_obj)
        except Exception as e:
            logger.error(f"Failed to store STIX object {obj.id}: {e}")
            raise

    try:
        db.flush()  # Ensure STIX objects are stored before creating mappings
    except Exception as e:
        logger.error(f"Failed to store STIX objects: {e}")
        db.rollback()
        raise

    # Now create the mappings
    mappings = map_stix_bundle(bundle)
    db_mappings = []

    for mapping in mappings:
        try:
            db_mapping = D3fendMappingDB(
                id=f"mapping--{mapping.stix_id}-{mapping.d3fend_id}",
                stix_id=mapping.stix_id,
                d3fend_id=mapping.d3fend_id,
                mapping_type=mapping.mapping_type,
                confidence=mapping.confidence,
                mapping_metadata=mapping.mapping_metadata
            )
            db.add(db_mapping)
            db_mappings.append(db_mapping)
        except Exception as e:
            logger.error(f"Failed to create mapping for {mapping.stix_id}: {e}")
            raise

    try:
        db.commit()
        logger.info(f"Successfully imported {len(db_mappings)} D3FEND mappings")
    except Exception as e:
        logger.error(f"Failed to commit mappings: {e}")
        db.rollback()
        raise

    return db_mappings

def get_d3fend_mappings(db: Session, stix_id: str) -> List[D3fendMappingDB]:
    """Get all D3FEND mappings for a STIX object.

    Args:
        db: Database session
        stix_id: STIX object identifier

    Returns:
        List[D3fendMappingDB]: List of mappings for the object
    """
    return db.query(D3fendMappingDB).filter(
        D3fendMappingDB.stix_id == stix_id
    ).all()

def get_mapped_d3fend_objects(db: Session, stix_id: str) -> List[Dict]:
    """Get D3FEND objects mapped to a STIX object.

    Args:
        db: Database session
        stix_id: STIX object identifier

    Returns:
        List[Dict]: List of mapped D3FEND objects with their metadata
    """
    mappings = get_d3fend_mappings(db, stix_id)
    return [
        {
            'd3fend_id': mapping.d3fend_id,
            'mapping_type': mapping.mapping_type,
            'confidence': mapping.confidence,
            **mapping.mapping_metadata
        }
        for mapping in mappings
    ]