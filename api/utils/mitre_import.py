"""Utility for importing MITRE ATT&CK data with robust validation and logging."""
import json
import os
import time
import functools
import logging
from typing import Dict, List, Optional, Union, Any
from sqlalchemy.orm import Session
from datetime import datetime
from mitreattack.stix20 import MitreAttackData

from api.models import MitreVersion, MitreTechnique, MitreTactic
from api.database import SessionLocal
from api.utils.i18n import _

logger = logging.getLogger(__name__)

MITRE_DATA_PATH = os.path.join(
    os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
    "3rdparty",
    "mitre-cti",
    "enterprise-attack",
    "enterprise-attack.json"
)

def validate_mitre_data(data: Dict) -> bool:
    """Validate MITRE data structure before import."""
    required_keys = ["objects"]
    if not all(key in data for key in required_keys):
        logger.error("Missing required keys in MITRE data")
        return False

    # Validate structure of objects
    valid_objects = True
    for obj in data.get("objects", []):
        if "type" not in obj:
            logger.error(f"Missing type in object: {obj}")
            valid_objects = False
            break

    return valid_objects

def import_with_diagnostics(func):
    """Decorator to add diagnostics to import functions."""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        success = False
        stats = {"objects_processed": 0, "errors": []}

        try:
            result = func(*args, stats=stats, **kwargs)
            success = True
            return result
        finally:
            elapsed = time.time() - start_time
            logger.info(f"Import {'succeeded' if success else 'failed'} in {elapsed:.2f}s")
            logger.info(f"Statistics: {stats}")

    return wrapper

def get_object_value(obj: Union[Dict, Any], key: str, default: Any = None) -> Any:
    """Safe getter for both dictionary and object attributes."""
    if obj is None or key is None:
        return default

    try:
        if isinstance(obj, dict):
            return obj.get(key, default)
        if hasattr(obj, key):
            return getattr(obj, key)
        return default
    except Exception:
        return default

def extract_version_from_data(data: Union[MitreAttackData, Dict]) -> str:
    """Extract version information from MITRE data."""
    try:
        objects = data.get("objects", []) if isinstance(data, dict) else []

        for obj in objects:
            if get_object_value(obj, 'type') == "x-mitre-matrix":
                modified = get_object_value(obj, 'modified')
                if modified:
                    if isinstance(modified, str):
                        modified = datetime.fromisoformat(modified.replace('Z', '+00:00'))
                    return modified.strftime("%Y.%m")

                created = get_object_value(obj, 'created')
                if created:
                    if isinstance(created, str):
                        created = datetime.fromisoformat(created.replace('Z', '+00:00'))
                    return created.strftime("%Y.%m")

        return datetime.utcnow().strftime("%Y.%m")
    except Exception as e:
        logger.error(f"Error extracting version: {e}")
        return "unknown"

def extract_external_id(refs: Union[List[Dict], List[Any], Dict, Any]) -> Optional[str]:
    """Safely extract external ID from references."""
    if not refs:
        return None

    ref = refs[0] if isinstance(refs, list) and refs else refs
    return get_object_value(ref, 'external_id')

@import_with_diagnostics
def import_mitre_data(
    db: Session,
    version: Optional[str] = None,
    set_as_current: bool = True,
    stats: Dict = None
) -> MitreVersion:
    """Import MITRE ATT&CK data with robust transaction handling."""
    if stats is None:
        stats = {"objects_processed": 0, "errors": []}

    outer_transaction = db.begin_nested()  # Create savepoint
    try:
        # Load and validate data
        with open(MITRE_DATA_PATH, 'r') as f:
            raw_data = json.load(f)

        if not validate_mitre_data(raw_data):
            raise ValueError("Invalid MITRE data structure")

        # Initialize MITRE ATT&CK data
        attack_data = raw_data if os.environ.get("TESTING") == "true" else MitreAttackData(MITRE_DATA_PATH)
        version_str = version or extract_version_from_data(attack_data)

        # Create version record with savepoint
        version_savepoint = db.begin_nested()
        try:
            if set_as_current:
                db.query(MitreVersion).update({"is_current": False})

            mitre_version = MitreVersion(
                version=version_str,
                import_date=datetime.utcnow(),
                is_current=set_as_current
            )
            db.add(mitre_version)
            version_savepoint.commit()
        except Exception as e:
            version_savepoint.rollback()
            logger.error(f"Version creation failed: {e}")
            raise

        # Import tactics with savepoint
        tactics_savepoint = db.begin_nested()
        tactics_map = {}
        try:
            tactics = (
                raw_data.get("objects", []) if isinstance(attack_data, dict)
                else attack_data.get_tactics()
            )

            for tactic in tactics:
                if isinstance(attack_data, dict):
                    if get_object_value(tactic, 'type') != 'x-mitre-tactic':
                        continue

                external_id = extract_external_id(get_object_value(tactic, 'external_references', []))
                if not external_id:
                    continue

                db_tactic = MitreTactic(
                    tactic_id=external_id,
                    name=get_object_value(tactic, 'name', ''),
                    description=get_object_value(tactic, 'description', ''),
                    version_id=mitre_version.id,
                    data=tactic if isinstance(tactic, dict) else tactic.serialize()
                )
                db.add(db_tactic)
                tactics_map[get_object_value(tactic, 'id', '')] = db_tactic
                stats["objects_processed"] += 1

            tactics_savepoint.commit()
        except Exception as e:
            tactics_savepoint.rollback()
            logger.error(f"Tactics import failed: {e}")
            stats["errors"].append(f"Tactics import: {str(e)}")
            raise

        # Import techniques with savepoint
        techniques_savepoint = db.begin_nested()
        try:
            techniques = (
                [obj for obj in raw_data.get("objects", []) 
                 if get_object_value(obj, 'type') == 'attack-pattern']
                if isinstance(attack_data, dict)
                else attack_data.get_techniques()
            )

            for technique in techniques:
                external_id = extract_external_id(get_object_value(technique, 'external_references', []))
                if not external_id:
                    continue

                db_technique = MitreTechnique(
                    technique_id=external_id,
                    name=get_object_value(technique, 'name', ''),
                    description=get_object_value(technique, 'description', ''),
                    version_id=mitre_version.id,
                    data=technique if isinstance(technique, dict) else technique.serialize()
                )

                # Link to tactics using kill chain phases
                kill_chain_phases = get_object_value(technique, 'kill_chain_phases', [])
                for phase in kill_chain_phases:
                    phase_name = get_object_value(phase, 'phase_name', '')
                    if phase_name in tactics_map:
                        db_technique.tactics.append(tactics_map[phase_name])

                db.add(db_technique)
                stats["objects_processed"] += 1

            techniques_savepoint.commit()
        except Exception as e:
            techniques_savepoint.rollback()
            logger.error(f"Techniques import failed: {e}")
            stats["errors"].append(f"Techniques import: {str(e)}")
            raise

        outer_transaction.commit()
        logger.info(f"Successfully imported MITRE version {version_str}")
        return mitre_version

    except Exception as e:
        outer_transaction.rollback()
        error_msg = f"MITRE import failed: {e}"
        logger.error(error_msg)
        stats["errors"].append(error_msg)
        raise Exception(error_msg)

def get_current_version(db: Session) -> Optional[MitreVersion]:
    """Get the current MITRE version."""
    return db.query(MitreVersion).filter(MitreVersion.is_current == True).first()