import logging
import traceback
import uuid
from functools import wraps
from flask import jsonify, current_app, request, session
from flask_login import current_user
from sqlalchemy.exc import SQLAlchemyError, InvalidRequestError
from api.database import db
from api.models.error_log import ErrorLog

# Configure logger
logger = logging.getLogger(__name__)
handler = logging.FileHandler('error.log')
handler.setFormatter(logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
))
logger.addHandler(handler)
logger.setLevel(logging.ERROR)

def log_error(error, endpoint=None, request_data=None):
    """
    Log detailed error information to the server log and database
    
    Args:
        error: The exception that was raised
        endpoint: The API endpoint where the error occurred
        request_data: The request data that caused the error
    
    Returns:
        str: The unique error ID
    """
    error_type = type(error).__name__
    error_message = str(error)
    stack_trace = traceback.format_exc()
    error_id = str(uuid.uuid4())
    
    # Create structured log entry
    log_entry = {
        "error_id": error_id,
        "error_type": error_type,
        "error_message": error_message,
        "stack_trace": stack_trace,
        "endpoint": endpoint or request.path if request else None,
        "method": request.method if request else None,
        "request_data": request_data or (request.json if request and request.is_json else None)
    }
    
    # Log the error to file
    logger.error(f"Application Error: {error_type} - {error_message}")
    logger.error(f"Error ID: {error_id}")
    logger.error(f"Stack Trace: {stack_trace}")
    logger.error(f"Endpoint: {log_entry['endpoint']}")
    logger.error(f"Method: {log_entry['method']}")
    logger.error(f"Request Data: {log_entry['request_data']}")
    
    # If in development mode, also print to console
    if current_app and current_app.config.get('DEBUG', False):
        print(f"ERROR: {error_type} - {error_message}")
        print(f"Error ID: {error_id}")
        print(f"Stack Trace: {stack_trace}")
    
    # Store in database if possible
    try:
        # Only attempt to store in database if we're in an application context
        if current_app:
            # Get user ID if user is logged in
            user_id = current_user.id if current_user and hasattr(current_user, 'id') else None
            
            # Create error log entry
            error_log = ErrorLog(
                error_type=error_type,
                error_message=error_message,
                stack_trace=stack_trace,
                user_id=user_id,
                endpoint=log_entry['endpoint'],
                request_method=log_entry['method'],
                request_data=log_entry['request_data']
            )
            
            # Add to database
            db.session.add(error_log)
            db.session.commit()
    except Exception as e:
        # If we can't store in database, just log the failure
        logger.error(f"Failed to store error in database: {str(e)}")
        if current_app and current_app.config.get('DEBUG', False):
            print(f"Failed to store error in database: {str(e)}")
        # Make sure to rollback any failed transaction
        if 'db' in locals() and hasattr(db, 'session'):
            db.session.rollback()
    
    return error_id

def handle_error_response(error, status_code=500):
    """
    Create a user-friendly error response
    
    Args:
        error: The exception that was raised
        status_code: HTTP status code to return
    
    Returns:
        A Flask response object with a user-friendly error message
    """
    # Log the error and get error ID
    error_id = log_error(error)
    
    # Create user-friendly response
    if isinstance(error, SQLAlchemyError):
        # Don't expose database errors to the user
        message = "A database error occurred. Our team has been notified."
    else:
        # Generic error message
        message = "An unexpected error occurred. Our team has been notified."
    
    response = {
        "success": False,
        "message": message,
        "error": "internal_server_error",
        "error_id": error_id
    }
    
    return jsonify(response), status_code

def error_handler(f):
    """
    Decorator to handle exceptions in routes
    
    Args:
        f: The function to wrap
    
    Returns:
        Wrapped function that catches and handles exceptions
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except SQLAlchemyError as e:
            return handle_error_response(e, 500)
        except Exception as e:
            return handle_error_response(e, 500)
    return decorated_function 