"""MITRE framework data importer implementation."""
from datetime import datetime
from typing import Any, Dict, Optional, List
from sqlalchemy.orm import Session

from .import_base import BaseFrameworkImporter, ImportError
from api.models.mitre import MitreTactic, MitreTechnique
from api.models.relationships import MitreRelationship

class MITREImporter(BaseFrameworkImporter):
    """MITRE ATT&CK framework data importer."""

    def extract_version(self, data: Any) -> str:
        """Extract version from MITRE data.

        Args:
            data: MITRE ATT&CK bundle

        Returns:
            str: Version string in YYYY.MM format
        """
        try:
            # Extract version from matrix modified date
            bundle = data.get_bundle()
            for obj in bundle.objects:
                if obj.type == "x-mitre-matrix":
                    # Use modified date if available, otherwise created date
                    date = obj.modified if hasattr(obj, "modified") else obj.created
                    if isinstance(date, str):
                        date = datetime.strptime(date, "%Y-%m-%dT%H:%M:%S.%fZ")
                    return date.strftime("%Y.%m")

            # Fallback to current date if no matrix found
            self.logger.warning("No matrix object found, using current date")
            return datetime.utcnow().strftime("%Y.%m")

        except Exception as e:
            self.logger.error(f"Error extracting version: {e}")
            return "unknown"

    def validate_data(self, data: Any) -> bool:
        """Validate MITRE data structure.

        Args:
            data: MITRE ATT&CK bundle

        Returns:
            bool: True if valid
        """
        try:
            bundle = data.get_bundle()

            # Check for required object types
            required_types = {"attack-pattern", "x-mitre-tactic"}
            found_types = {obj.type for obj in bundle.objects}

            if not required_types.issubset(found_types):
                self.logger.error(f"Missing required object types. Found: {found_types}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"Validation error: {e}")
            return False

    def import_data(self, data: Any, version: Optional[str] = None, **kwargs) -> Dict:
        """Import MITRE ATT&CK data.

        Args:
            data: MITRE ATT&CK bundle
            version: Optional version string
            **kwargs: Additional options

        Returns:
            dict: Import statistics
        """
        stats = {
            "tactics": 0,
            "techniques": 0,
            "relationships": 0
        }

        try:
            bundle = data.get_bundle()

            # Import tactics first
            tactics = {}
            for obj in bundle.objects:
                if obj.type == "x-mitre-tactic":
                    tactic = MitreTactic(
                        external_id=obj.external_references[0].external_id,
                        name=obj.name,
                        description=obj.description,
                        version=version
                    )
                    self.db.add(tactic)
                    tactics[obj.id] = tactic
                    stats["tactics"] += 1

            # Import techniques and relationships
            for obj in bundle.objects:
                if obj.type == "attack-pattern":
                    technique = MitreTechnique(
                        external_id=obj.external_references[0].external_id,
                        name=obj.name,
                        description=obj.description,
                        version=version
                    )

                    # Link to tactics
                    if hasattr(obj, "kill_chain_phases"):
                        for phase in obj.kill_chain_phases:
                            if phase.phase_name in tactics:
                                technique.tactics.append(tactics[phase.phase_name])
                                stats["relationships"] += 1

                    self.db.add(technique)
                    stats["techniques"] += 1

            return stats

        except Exception as e:
            raise ImportError(f"MITRE import failed: {str(e)}")