"""Utility functions for working with STIX 2.1 data.

This module provides helper functions for:
- Converting between platform and STIX formats
- Validating STIX bundles and objects
- Managing relationships between STIX objects
"""
from typing import List, Dict, Union, Optional
import stix2
from datetime import datetime
from api.models.stix import StixObject, StixDB
from sqlalchemy.orm import Session

def validate_stix_bundle(data: Union[str, dict, bytes]) -> stix2.Bundle:
    """Validate and parse a STIX bundle.
    
    Args:
        data: STIX bundle data as JSON string, dict, or bytes
        
    Returns:
        stix2.Bundle: Parsed STIX bundle
        
    Raises:
        stix2.exceptions.STIXError: If the bundle is invalid
    """
    try:
        return stix2.parse(data, allow_custom=True)
    except stix2.exceptions.STIXError as e:
        raise ValueError(f"Invalid STIX bundle: {str(e)}")

def import_stix_bundle(db: Session, bundle: Union[stix2.Bundle, dict]) -> List[StixDB]:
    """Import a STIX bundle into the database.
    
    Args:
        db: Database session
        bundle: STIX bundle to import
        
    Returns:
        List[StixDB]: List of imported STIX objects
        
    Raises:
        ValueError: If the bundle is invalid
    """
    if isinstance(bundle, dict):
        bundle = validate_stix_bundle(bundle)
        
    objects = []
    for obj in bundle.objects:
        db_obj = StixDB.from_stix(obj)
        db.add(db_obj)
        objects.append(db_obj)
        
    db.commit()
    return objects

def create_stix_bundle(objects: List[Union[StixObject, StixDB, dict]]) -> stix2.Bundle:
    """Create a STIX bundle from a list of objects.
    
    Args:
        objects: List of STIX objects to include in the bundle
        
    Returns:
        stix2.Bundle: STIX bundle containing the objects
    """
    stix_objects = []
    
    for obj in objects:
        if isinstance(obj, StixObject):
            stix_objects.append(obj.to_stix())
        elif isinstance(obj, StixDB):
            stix_objects.append(obj.to_stix())
        elif isinstance(obj, dict):
            stix_objects.append(stix2.parse_object(obj))
        else:
            raise TypeError(f"Unsupported object type: {type(obj)}")
            
    return stix2.Bundle(objects=stix_objects)
