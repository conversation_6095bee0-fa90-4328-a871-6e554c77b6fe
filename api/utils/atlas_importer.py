"""ATLAS framework data importer implementation."""
from datetime import datetime
from typing import Any, Dict, Optional
from sqlalchemy.orm import Session

from .import_base import BaseFrameworkImporter, ImportError
from api.models.atlas import AtlasMatrix, AtlasTechnique, AtlasTactic
from api.models.relationships import AtlasRelationship

class AtlasImporter(BaseFrameworkImporter):
    """ATLAS framework data importer."""

    def extract_version(self, data: Any) -> str:
        """Extract version from ATLAS data.

        Args:
            data: ATLAS matrix data

        Returns:
            str: Version string in YYYY.MM format
        """
        try:
            # Get version from matrix metadata if available
            if isinstance(data, dict) and "version" in data:
                version = data["version"]
                if isinstance(version, str):
                    # Try to parse version string to ensure valid format
                    try:
                        datetime.strptime(version, "%Y.%m")
                        return version
                    except ValueError:
                        self.logger.warning(f"Invalid version format: {version}")

            # Fallback to current date
            self.logger.warning("No valid version found in data, using current date")
            return datetime.utcnow().strftime("%Y.%m")

        except Exception as e:
            self.logger.error(f"Error extracting version: {e}")
            return "unknown"

    def validate_data(self, data: Any) -> bool:
        """Validate ATLAS data structure.

        Args:
            data: ATLAS matrix data

        Returns:
            bool: True if valid
        """
        try:
            # Basic structure validation
            if not isinstance(data, dict):
                self.logger.error("Data must be a dictionary")
                return False

            # Check required fields
            required_fields = {"name", "tactics", "techniques"}
            if not all(field in data for field in required_fields):
                self.logger.error(f"Missing required fields. Found: {set(data.keys())}")
                return False

            # Validate tactics and techniques
            if not isinstance(data["tactics"], list):
                self.logger.error("Tactics must be a list")
                return False

            if not isinstance(data["techniques"], list):
                self.logger.error("Techniques must be a list")
                return False

            return True

        except Exception as e:
            self.logger.error(f"Validation error: {e}")
            return False

    def import_data(self, data: Any, version: Optional[str] = None, **kwargs) -> Dict:
        """Import ATLAS data.

        Args:
            data: ATLAS matrix data
            version: Optional version string
            **kwargs: Additional options

        Returns:
            dict: Import statistics
        """
        stats = {
            "matrices": 0,
            "techniques": 0, 
            "relationships": 0
        }

        try:
            # Create matrix entry
            matrix = AtlasMatrix(
                name=data["name"],
                description=data.get("description", ""),
                version=version
            )
            self.db.add(matrix)
            stats["matrices"] += 1

            # Import techniques with relationships
            technique_map = {}
            for tech_data in data["techniques"]:
                tech = AtlasTechnique(
                    name=tech_data["name"],
                    description=tech_data.get("description", ""),
                    external_id=tech_data.get("id"),
                    matrix_id=matrix.id,
                    version=version
                )
                self.db.add(tech)
                technique_map[tech_data["id"]] = tech
                stats["techniques"] += 1

                # Add relationships
                if "relationships" in tech_data:
                    for rel in tech_data["relationships"]:
                        relationship = AtlasRelationship(
                            source_id=tech.id,
                            target_id=technique_map[rel["target"]].id,
                            type=rel["type"]
                        )
                        self.db.add(relationship)
                        stats["relationships"] += 1

            return stats

        except Exception as e:
            raise ImportError(f"ATLAS import failed: {str(e)}")