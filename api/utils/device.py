"""Device fingerprinting and information extraction utilities."""
from typing import Dict, Optional
from datetime import datetime, timezone
from user_agents import parse
from fastapi import Request
from sqlalchemy.orm import Session

from api.models.session import DeviceInfo

def extract_device_info(request: Request) -> Dict[str, str]:
    """Extract device information from request headers."""
    user_agent_string = request.headers.get("user-agent", "")
    user_agent = parse(user_agent_string)
    
    return {
        "user_agent": user_agent_string[:500],  # Truncate to fit db column
        "ip_address": request.client.host if request.client else None,
        "browser": f"{user_agent.browser.family} {user_agent.browser.version_string}",
        "os": f"{user_agent.os.family} {user_agent.os.version_string}",
        "device_type": ("mobile" if user_agent.is_mobile else 
                       "tablet" if user_agent.is_tablet else
                       "bot" if user_agent.is_bot else "desktop")
    }

def create_or_update_device_info(
    db: Session,
    device_data: Dict[str, str]
) -> DeviceInfo:
    """Create or update device info record."""
    # Try to find existing device with same fingerprint
    device = (
        db.query(DeviceInfo)
        .filter(
            DeviceInfo.user_agent == device_data["user_agent"],
            DeviceInfo.ip_address == device_data["ip_address"],
            DeviceInfo.browser == device_data["browser"],
            DeviceInfo.os == device_data["os"]
        )
        .first()
    )
    
    if device:
        # Update last seen timestamp
        device.last_seen = datetime.now(timezone.utc)
    else:
        # Create new device record
        device = DeviceInfo(**device_data)
        db.add(device)
    
    db.commit()
    return device
