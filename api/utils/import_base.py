"""Base classes for framework data importers."""
from abc import ABC, abstractmethod
import logging
from typing import Any, Dict, Optional
from sqlalchemy.orm import Session

# Configure logging
logger = logging.getLogger(__name__)

class ImportError(Exception):
    """Base exception for import errors."""
    pass

class BaseFrameworkImporter(ABC):
    """Abstract base class for framework data importers."""

    def __init__(self, db: Session):
        """Initialize importer with database session."""
        self.db = db
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    @abstractmethod
    def extract_version(self, data: Any) -> str:
        """Extract version information from data.
        
        Args:
            data: The source data to extract version from
            
        Returns:
            str: Version string
            
        Raises:
            ImportError: If version cannot be extracted
        """
        pass

    @abstractmethod
    def validate_data(self, data: Any) -> bool:
        """Validate the data before import.
        
        Args:
            data: The data to validate
            
        Returns:
            bool: True if valid, False otherwise
            
        Raises:
            ImportError: If validation fails critically
        """
        pass

    @abstractmethod
    def import_data(self, data: Any, version: Optional[str] = None, **kwargs) -> Dict:
        """Import the framework data.
        
        Args:
            data: The data to import
            version: Optional version override
            **kwargs: Additional importer-specific options
            
        Returns:
            dict: Import statistics/results
            
        Raises:
            ImportError: If import fails
        """
        pass

    def _begin_transaction(self):
        """Start database transaction."""
        self.logger.debug("Beginning database transaction")

    def _commit_transaction(self):
        """Commit database transaction."""
        try:
            self.db.commit()
            self.logger.debug("Transaction committed")
        except Exception as e:
            self.logger.error(f"Error committing transaction: {e}")
            self.db.rollback()
            raise ImportError(f"Failed to commit transaction: {str(e)}")

    def _rollback_transaction(self):
        """Rollback database transaction."""
        self.logger.debug("Rolling back transaction")
        self.db.rollback()

    def process_import(self, data: Any, **kwargs) -> Dict:
        """Main import processing flow with error handling.
        
        Args:
            data: The data to import
            **kwargs: Additional options passed to import_data
            
        Returns:
            dict: Import results
            
        Raises:
            ImportError: If import fails
        """
        try:
            self.logger.info("Starting import process")
            
            # Validate
            if not self.validate_data(data):
                raise ImportError("Data validation failed")
                
            # Extract version if not provided
            version = kwargs.get('version')
            if not version:
                version = self.extract_version(data)
                kwargs['version'] = version
            
            # Begin transaction
            self._begin_transaction()
            
            # Perform import
            results = self.import_data(data, **kwargs)
            
            # Commit if successful
            self._commit_transaction()
            
            self.logger.info(f"Import completed successfully: {results}")
            return results
            
        except Exception as e:
            self.logger.error(f"Import failed: {str(e)}", exc_info=True)
            self._rollback_transaction()
            raise ImportError(f"Import failed: {str(e)}")
