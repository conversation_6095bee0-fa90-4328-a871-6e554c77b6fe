"""Rate limiting utilities for the FastAPI application."""

import os
from typing import Callable, Optional
import redis.asyncio as redis
from fastapi import Request, Response
from fastapi_limiter import FastAPILimiter
from fastapi_limiter.depends import RateLimiter
import logging

logger = logging.getLogger(__name__)

async def init_rate_limiter():
    """Initialize the rate limiter with Redis."""
    try:
        redis_url = os.environ.get("REDIS_URL", "redis://localhost:6379/0")
        logger.info(f"Initializing rate limiter with Redis URL: {redis_url}")
        
        redis_client = redis.from_url(redis_url, encoding="utf-8", decode_responses=True)
        await FastAPILimiter.init(redis_client)
        
        logger.info("Rate limiter initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize rate limiter: {str(e)}")
        raise

def rate_limit(
    times: int = 10,
    seconds: int = 60,
    identifier: Optional[Callable] = None
):
    """Create a rate limiter dependency with custom settings.
    
    Args:
        times: Maximum number of requests allowed within the time window
        seconds: Time window in seconds
        identifier: Optional function to generate a custom identifier
        
    Returns:
        RateLimiter: A rate limiter dependency that can be used in FastAPI routes
    """
    if identifier is None:
        # Default identifier uses the client's IP address
        async def default_identifier(request: Request) -> str:
            forwarded = request.headers.get("X-Forwarded-For")
            if forwarded:
                ip = forwarded.split(",")[0].strip()
            else:
                ip = request.client.host
            return f"rate-limit:{ip}"
        
        identifier = default_identifier
    
    return RateLimiter(
        times=times,
        seconds=seconds,
        identifier=identifier
    )

# Common rate limiters
standard_rate_limit = rate_limit(times=100, seconds=60)  # 100 requests per minute
strict_rate_limit = rate_limit(times=20, seconds=60)     # 20 requests per minute
auth_rate_limit = rate_limit(times=5, seconds=60)        # 5 requests per minute for auth endpoints

# Custom identifier for user-based rate limiting
async def user_identifier(request: Request) -> str:
    """Generate a rate limit identifier based on the authenticated user."""
    # Try to get user from request state (set by auth middleware)
    user_id = getattr(request.state, "user_id", None)
    
    # Fall back to IP if user is not authenticated
    if user_id is None:
        forwarded = request.headers.get("X-Forwarded-For")
        if forwarded:
            ip = forwarded.split(",")[0].strip()
        else:
            ip = request.client.host
        return f"rate-limit:anonymous:{ip}"
    
    return f"rate-limit:user:{user_id}"

# User-based rate limiters
user_rate_limit = rate_limit(times=200, seconds=60, identifier=user_identifier)  # 200 requests per minute per user 