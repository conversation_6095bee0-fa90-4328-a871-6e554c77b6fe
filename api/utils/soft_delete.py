"""
Soft delete utilities for database operations.

This module provides utilities for implementing soft delete functionality
across database models, following best practices for data retention and
audit trails.
"""

from datetime import datetime, timezone
from typing import Type, TypeVar
from sqlalchemy.orm import Query
from sqlalchemy.ext.declarative import DeclarativeMeta

# Type variable for SQLAlchemy models
ModelType = TypeVar('ModelType', bound=DeclarativeMeta)


def apply_soft_delete_filter(query: Query, model: Type[ModelType]) -> Query:
    """
    Apply soft delete filter to exclude deleted records.
    
    Args:
        query: SQLAlchemy query object
        model: SQLAlchemy model class
        
    Returns:
        Filtered query excluding soft-deleted records
        
    Raises:
        AttributeError: If model doesn't have deleted_at column
    """
    if not hasattr(model, 'deleted_at'):
        # If model doesn't support soft delete, return query unchanged
        return query
    
    return query.filter(model.deleted_at.is_(None))


def soft_delete_record(record: ModelType) -> None:
    """
    Soft delete a database record by setting deleted_at timestamp.
    
    Args:
        record: Database record to soft delete
        
    Raises:
        AttributeError: If record doesn't have deleted_at attribute
    """
    if not hasattr(record, 'deleted_at'):
        raise AttributeError(
            f"Model {type(record).__name__} doesn't support soft delete"
        )
    
    record.deleted_at = datetime.now(timezone.utc)


def restore_record(record: ModelType) -> None:
    """
    Restore a soft-deleted record by clearing deleted_at timestamp.
    
    Args:
        record: Database record to restore
        
    Raises:
        AttributeError: If record doesn't have deleted_at attribute
    """
    if not hasattr(record, 'deleted_at'):
        raise AttributeError(
            f"Model {type(record).__name__} doesn't support soft delete"
        )
    
    record.deleted_at = None


def is_soft_deleted(record: ModelType) -> bool:
    """
    Check if a record is soft deleted.
    
    Args:
        record: Database record to check
        
    Returns:
        True if record is soft deleted, False otherwise
        
    Raises:
        AttributeError: If record doesn't have deleted_at attribute
    """
    if not hasattr(record, 'deleted_at'):
        return False
    
    return record.deleted_at is not None


def include_deleted_filter(query: Query, include_deleted: bool = False) -> Query:
    """
    Conditionally include or exclude deleted records.
    
    Args:
        query: SQLAlchemy query object
        include_deleted: Whether to include deleted records
        
    Returns:
        Filtered or unfiltered query based on include_deleted flag
    """
    if include_deleted:
        return query
    
    # Extract model from query
    model = query.column_descriptions[0]['type']
    return apply_soft_delete_filter(query, model)


class SoftDeleteMixin:
    """
    Mixin class to add soft delete functionality to SQLAlchemy models.

    This mixin adds the deleted_at column and helper methods for soft delete
    operations. Models using this mixin should inherit from it.
    """

    deleted_at = None  # Should be overridden in actual models

    def soft_delete(self) -> None:
        """Soft delete this record."""
        self.deleted_at = datetime.now(timezone.utc)

    def restore(self) -> None:
        """Restore this soft-deleted record."""
        self.deleted_at = None

    @property
    def is_deleted(self) -> bool:
        """Check if this record is soft deleted."""
        return self.deleted_at is not None

    @classmethod
    def active_records(cls, query: Query) -> Query:
        """
        Filter query to only include active (non-deleted) records.

        Args:
            query: SQLAlchemy query object

        Returns:
            Filtered query excluding soft-deleted records
        """
        return query.filter(cls.deleted_at.is_(None))

    @classmethod
    def deleted_records(cls, query: Query) -> Query:
        """
        Filter query to only include deleted records.

        Args:
            query: SQLAlchemy query object

        Returns:
            Filtered query including only soft-deleted records
        """
        return query.filter(cls.deleted_at.is_not(None))

    @classmethod
    def all_records(cls, query: Query) -> Query:
        """
        Return query including both active and deleted records.

        Args:
            query: SQLAlchemy query object

        Returns:
            Unfiltered query including all records
        """
        return query


class AdvancedSoftDeleteMixin(SoftDeleteMixin):
    """
    Enhanced soft delete mixin with advanced features.

    This mixin extends the basic soft delete functionality with:
    - User tracking for deletions
    - Deletion reasons
    - Cascade control
    - Policy integration
    """

    deleted_at = None  # Should be overridden in actual models
    deleted_by = None  # Should be overridden in actual models
    deletion_reason = None  # Should be overridden in actual models
    cascade_strategy = None  # Should be overridden in actual models

    def advanced_soft_delete(
        self,
        user_id: Optional[int] = None,
        reason: Optional[str] = None,
        cascade: bool = True
    ) -> None:
        """
        Advanced soft delete with user tracking and reason.

        Args:
            user_id: ID of user performing the deletion
            reason: Reason for deletion
            cascade: Whether to cascade delete related entities
        """
        self.deleted_at = datetime.now(timezone.utc)
        if hasattr(self, 'deleted_by') and user_id is not None:
            self.deleted_by = user_id
        if hasattr(self, 'deletion_reason') and reason is not None:
            self.deletion_reason = reason
        if hasattr(self, 'cascade_strategy'):
            self.cascade_strategy = "cascade" if cascade else "none"

    def advanced_restore(
        self,
        user_id: Optional[int] = None,
        reason: Optional[str] = None
    ) -> None:
        """
        Advanced restore with user tracking.

        Args:
            user_id: ID of user performing the restoration
            reason: Reason for restoration
        """
        self.deleted_at = None
        if hasattr(self, 'deleted_by'):
            self.deleted_by = None
        if hasattr(self, 'deletion_reason'):
            self.deletion_reason = None
        if hasattr(self, 'cascade_strategy'):
            self.cascade_strategy = None

    @property
    def deletion_info(self) -> dict:
        """
        Get comprehensive deletion information.

        Returns:
            Dictionary with deletion details
        """
        return {
            "is_deleted": self.is_deleted,
            "deleted_at": self.deleted_at,
            "deleted_by": getattr(self, 'deleted_by', None),
            "deletion_reason": getattr(self, 'deletion_reason', None),
            "cascade_strategy": getattr(self, 'cascade_strategy', None)
        }

    @classmethod
    def deleted_by_user(cls, query: Query, user_id: int) -> Query:
        """
        Filter query to only include records deleted by specific user.

        Args:
            query: SQLAlchemy query object
            user_id: ID of user who deleted the records

        Returns:
            Filtered query for records deleted by the user
        """
        if hasattr(cls, 'deleted_by'):
            return query.filter(cls.deleted_by == user_id)
        return query

    @classmethod
    def deleted_with_reason(cls, query: Query, reason_pattern: str) -> Query:
        """
        Filter query to include records with specific deletion reason.

        Args:
            query: SQLAlchemy query object
            reason_pattern: Pattern to match in deletion reason

        Returns:
            Filtered query for records with matching reason
        """
        if hasattr(cls, 'deletion_reason'):
            return query.filter(cls.deletion_reason.like(f"%{reason_pattern}%"))
        return query
