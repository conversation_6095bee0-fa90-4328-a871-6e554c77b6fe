"""Rate limiting configuration settings."""

from typing import Dict, Any, Optional
from pydantic import BaseSettings, Field

class RateLimitSettings(BaseSettings):
    """Rate limiting configuration settings."""
    
    # Global Settings
    ENABLED: bool = Field(
        default=True,
        description="Whether rate limiting is enabled"
    )
    DEFAULT_LIMIT: int = Field(
        default=100,
        description="Default number of requests allowed per window"
    )
    DEFAULT_WINDOW: int = Field(
        default=60,
        description="Default time window in seconds"
    )
    
    # Endpoint-specific Settings
    ENDPOINT_LIMITS: Dict[str, Dict[str, Any]] = Field(
        default={
            "/api/auth/login": {
                "limit": 5,
                "window": 300  # 5 minutes
            },
            "/api/auth/register": {
                "limit": 3,
                "window": 3600  # 1 hour
            }
        },
        description="Rate limits for specific endpoints"
    )
    
    # Storage Settings
    STORAGE_TYPE: str = Field(
        default="memory",
        description="Storage backend type (memory or redis)"
    )
    REDIS_URL: Optional[str] = Field(
        default=None,
        description="Redis connection URL"
    )
    
    class Config:
        """Pydantic config."""
        env_prefix = "RATE_LIMIT_"
        case_sensitive = True

# Create global settings instance
rate_limit_settings = RateLimitSettings() 