"""Token configuration settings."""

from pydantic_settings import BaseSettings
from typing import Optional

class TokenSettings(BaseSettings):
    """Token configuration settings."""
    
    # JWT Settings
    SECRET_KEY: str = "your-secret-key-here"  # TODO: Load from environment
    ALGORITHM: str = "HS256"
    
    # Token Expiration
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 15
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # Token Blacklist
    BLACKLIST_ENABLED: bool = True
    BLACKLIST_CLEANUP_INTERVAL_HOURS: int = 24
    
    class Config:
        env_prefix = "TOKEN_"
        case_sensitive = True

# Create global settings instance
token_settings = TokenSettings() 