"""Service for importing MITRE ATLAS data into the database."""
import json
import logging
import sys
import traceback
from datetime import datetime
from pathlib import Path
from sqlalchemy.orm import Session
from typing import Any, Dict, List, Optional
from sqlalchemy.exc import IntegrityError

from api.models.atlas import (
    AtlasVersion,
    AtlasTactic, 
    AtlasTechnique,
    AtlasMatrix,
    AtlasMatrixItem
)

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)  # Set debug level for more verbose output

class AtlasImportError(Exception):
    """Custom exception for ATLAS import errors."""
    pass

def _get_existing_version(db: Session, version: str) -> Optional[AtlasVersion]:
    """Get existing version from database if it exists."""
    try:
        logger.debug(f"Checking for existing version: {version}")
        return db.query(AtlasVersion).filter(AtlasVersion.version == version).first()
    except Exception as e:
        logger.error(f"Error checking existing version: {str(e)}")
        raise AtlasImportError(f"Failed to check existing version: {str(e)}")

def _extract_version_info(matrix_data: Dict[str, Any]) -> Dict[str, Any]:
    """Extract version information from matrix data."""
    try:
        logger.debug("Extracting version info from matrix data")
        metadata = {item["name"]: item["value"] 
                   for item in matrix_data.get("metadata", [])
                   if "name" in item and "value" in item}

        version_info = {
            "version": metadata.get("atlas_data_version", "unknown"),
            "name": matrix_data.get("name", "ATLAS Matrix"),
            "description": matrix_data.get("description", "")
        }
        logger.debug(f"Extracted version info: {version_info}")
        return version_info
    except Exception as e:
        logger.error(f"Error extracting version info: {str(e)}")
        raise AtlasImportError(f"Failed to extract version info: {str(e)}")

def _is_valid_subtechnique_id(technique_id: str) -> bool:
    """Check if a technique ID follows the subtechnique format (parent.sub)."""
    if not technique_id or '.' not in technique_id:
        return False

    parts = technique_id.split('.')
    if len(parts) != 3:  # Must be AML.T####.### for subtechniques
        return False

    # Ensure parent ID is valid (e.g., "AML.T0001")
    base_id = f"{parts[0]}.{parts[1]}"
    return base_id.startswith("AML.T") and parts[1].startswith("T")

def _parse_technique_data(technique_data: Dict[str, Any]) -> Dict[str, Any]:
    """Parse technique data from matrix entry."""
    try:
        logger.debug(f"Parsing technique data: {technique_data}")
        # Normalize technique ID and validate
        technique_id = (technique_data.get("techniqueID", "") or "").strip()
        if not technique_id:
            raise AtlasImportError("Technique ID is required")

        # Determine if this is a subtechnique and get parent ID
        is_subtechnique = _is_valid_subtechnique_id(technique_id)
        parent_id = '.'.join(technique_id.split('.')[:2]) if is_subtechnique else None

        # Get name and normalize
        name = (technique_data.get("name", "") or "").strip()
        if not name:
            name = technique_id
            logger.info(f"Using ID as name for technique {technique_id}")

        # Get and normalize description
        description = (technique_data.get("description", "") or "").strip()

        # Process URL if available
        url = (technique_data.get("url", "") or "").strip()
        if url:
            if description:
                description = f"{description}\n\nReference: {url}"
            else:
                description = f"Reference: {url}"

        # Build technique data with all required fields
        result = {
            "external_id": technique_id,
            "name": name,
            "description": description,
            "is_subtechnique": is_subtechnique,
            "parent_technique_id": parent_id,
            "color": technique_data.get("color"),
            "show_subtechniques": technique_data.get("showSubtechniques", True),
            "needs_name": name == technique_id,  # Track if we used ID as name
            "created": None,  # Add timestamp fields
            "modified": None,
            "revoked": False,
            "deprecated": False
        }
        logger.debug(f"Parsed technique data: {result}")
        return result
    except Exception as e:
        logger.error(f"Error parsing technique data: {str(e)}\n{traceback.format_exc()}")
        raise AtlasImportError(f"Failed to parse technique data: {str(e)}")

def import_atlas_data(db: Session, matrix_file_path: str, force_update: bool = False) -> None:
    """Import ATLAS data from matrix JSON file into database."""
    try:
        logger.info(f"Starting ATLAS data import from {matrix_file_path}")

        # Initialize counters
        stats = {
            "techniques_total": 0,
            "subtechniques": 0,
            "tactics": 0,
            "linked_subtechniques": 0,
            "unlinked_subtechniques": 0,
            "duplicates_skipped": 0,
            "fallback_names": 0
        }

        # Validate file path
        path = Path(matrix_file_path)
        if not path.exists():
            raise AtlasImportError(f"Matrix file not found: {matrix_file_path}")
        if not path.is_file():
            raise AtlasImportError(f"Path is not a file: {matrix_file_path}")

        # Read matrix data
        try:
            with path.open('r', encoding='utf-8') as f:
                matrix_data = json.load(f)
            logger.debug("Successfully loaded matrix data")
        except json.JSONDecodeError as e:
            raise AtlasImportError(f"Invalid JSON format: {str(e)}")
        except Exception as e:
            raise AtlasImportError(f"Error reading matrix file: {str(e)}")

        # Extract version info and handle version existence
        version_info = _extract_version_info(matrix_data)
        logger.info(f"Processing ATLAS version {version_info['version']}")

        existing_version = _get_existing_version(db, version_info["version"])
        if existing_version and not force_update:
            logger.warning(f"Version {version_info['version']} already exists. Use force_update=True to update it.")
            return

        # Set all versions as not current first
        db.query(AtlasVersion).update({"is_current": False})

        # Create or update version
        if existing_version and force_update:
            logger.info(f"Updating existing version {version_info['version']}")
            existing_version.name = version_info["name"]
            existing_version.description = version_info["description"]
            existing_version.import_date = datetime.utcnow()
            existing_version.is_current = True
            version = existing_version
            db.flush()  # Ensure the version update is flushed
        else:
            version = AtlasVersion(
                version=version_info["version"],
                name=version_info["name"],
                description=version_info["description"],
                import_date=datetime.utcnow(),
                is_current=True
            )
            db.add(version)
            db.flush()  # Get version ID

        # Delete existing matrix entries if updating
        if existing_version and force_update:
            logger.info("Cleaning up existing matrix entries")
            db.query(AtlasMatrixItem).filter(
                AtlasMatrixItem.matrix_id.in_(
                    db.query(AtlasMatrix.id).filter_by(version_id=version.id)
                )
            ).delete(synchronize_session=False)
            db.query(AtlasMatrix).filter_by(version_id=version.id).delete()
            db.query(AtlasTechnique).filter_by(version_id=version.id).delete()
            db.query(AtlasTactic).filter_by(version_id=version.id).delete()
            db.flush()  # Ensure deletions are flushed

        # Create matrix record
        matrix = AtlasMatrix(
            name=matrix_data.get("name", "ATLAS Matrix"),
            description=matrix_data.get("description", ""),
            version_id=version.id
        )
        db.add(matrix)
        db.flush()

        # First pass: Create all techniques
        technique_map = {}  # Map technique IDs to DB records
        tactic_map = {}     # Map tactic names to DB records
        subtechniques = []  # Store subtechniques for second pass
        processed_techniques = set()  # Track processed technique IDs

        logger.info("Processing techniques and tactics")
        for technique_entry in matrix_data.get("techniques", []):
            tech_data = _parse_technique_data(technique_entry)
            stats["techniques_total"] += 1

            if tech_data.get("needs_name", False):
                stats["fallback_names"] += 1

            # Skip if we've already processed this technique
            if tech_data["external_id"] in processed_techniques:
                stats["duplicates_skipped"] += 1
                logger.debug(f"Skipping duplicate technique {tech_data['external_id']}")
                continue

            processed_techniques.add(tech_data["external_id"])

            # Get or create tactic if specified
            tactic = None
            if "tactic" in technique_entry:
                tactic_name = technique_entry["tactic"]
                if tactic_name not in tactic_map:
                    tactic = AtlasTactic(
                        external_id=f"TA{len(tactic_map):04d}",
                        name=tactic_name,
                        version_id=version.id
                    )
                    db.add(tactic)
                    tactic_map[tactic_name] = tactic
                    stats["tactics"] += 1
                else:
                    tactic = tactic_map[tactic_name]

            # Create technique
            technique = AtlasTechnique(
                external_id=tech_data["external_id"],
                name=tech_data["name"],
                description=tech_data["description"],
                version_id=version.id,
                tactic_id=tactic.id if tactic else None,
                is_subtechnique=tech_data["is_subtechnique"],
                created=datetime.utcnow(),  #Setting created and modified timestamps
                modified=datetime.utcnow(),
                revoked=tech_data["revoked"],
                deprecated=tech_data["deprecated"]
            )

            db.add(technique)
            db.flush()  # Get technique ID
            technique_map[tech_data["external_id"]] = technique

            # Store subtechnique information for second pass
            if tech_data["is_subtechnique"] and tech_data["parent_technique_id"]:
                subtechniques.append((technique, tech_data["parent_technique_id"]))
                stats["subtechniques"] += 1

            # Create matrix item if tactic exists
            if tactic:
                matrix_item = AtlasMatrixItem(
                    matrix_id=matrix.id,
                    technique_id=technique.id,
                    tactic_id=tactic.id,
                    color=tech_data.get("color"),
                    show_subtechniques=tech_data.get("show_subtechniques", True)
                )
                db.add(matrix_item)

        # Second pass: Update parent-child relationships
        logger.info("Updating parent-child technique relationships")
        for subtechnique, parent_id in subtechniques:
            if parent_id in technique_map:
                subtechnique.parent_technique_id = technique_map[parent_id].id
                stats["linked_subtechniques"] += 1
                logger.debug(f"Linked subtechnique {subtechnique.external_id} to parent {parent_id}")
            else:
                stats["unlinked_subtechniques"] += 1
                logger.warning(f"Unable to link subtechnique {subtechnique.external_id} - parent {parent_id} not found")

        # Final commit
        logger.info("Committing changes to database")
        db.commit()

        # Refresh the version record to ensure we have the latest data
        if existing_version:
            db.refresh(existing_version)

        logger.info(
            f"Successfully imported ATLAS data version {version_info['version']}\n"
            f"Summary:\n"
            f"- Total techniques: {stats['techniques_total']}\n"
            f"- Tactics: {stats['tactics']}\n"
            f"- Subtechniques: {stats['subtechniques']}\n"
            f"- Linked subtechniques: {stats['linked_subtechniques']}\n"
            f"- Unlinked subtechniques: {stats['unlinked_subtechniques']}\n"
            f"- Duplicates skipped: {stats['duplicates_skipped']}\n"
            f"- Techniques using fallback names: {stats['fallback_names']}"
        )

    except Exception as e:
        db.rollback()
        logger.error(f"Error importing ATLAS data: {str(e)}\n{traceback.format_exc()}")
        raise AtlasImportError(f"Failed to import ATLAS data: {str(e)}")