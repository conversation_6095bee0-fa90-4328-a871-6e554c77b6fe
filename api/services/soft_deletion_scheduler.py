"""
Soft Deletion Scheduler Service.

This service handles scheduled operations for the advanced soft deletion framework,
including automatic purging, notifications, and policy enforcement.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional

from sqlalchemy import and_, or_
from sqlalchemy.orm import Session

from api.database import get_db
from api.models.soft_deletion import (
    SoftDeletionNotification,
    SoftDeletionPolicy,
    SoftDeletionSchedule,
)
from api.services.soft_deletion_service import SoftDeletionService

logger = logging.getLogger(__name__)


class SoftDeletionScheduler:
    """Service for managing scheduled soft deletion operations."""
    
    def __init__(self, db: Session):
        """
        Initialize the scheduler service.
        
        Args:
            db: Database session
        """
        self.db = db
        self.soft_deletion_service = SoftDeletionService(db)
    
    def process_scheduled_operations(self, batch_size: int = 100) -> dict:
        """
        Process pending scheduled operations.
        
        Args:
            batch_size: Maximum number of operations to process in one batch
            
        Returns:
            Dictionary with processing statistics
        """
        stats = {
            "processed": 0,
            "successful": 0,
            "failed": 0,
            "errors": []
        }
        
        # Get pending operations that are due
        now = datetime.utcnow()
        pending_operations = self.db.query(SoftDeletionSchedule).filter(
            and_(
                SoftDeletionSchedule.status == "pending",
                SoftDeletionSchedule.scheduled_for <= now
            )
        ).limit(batch_size).all()
        
        for operation in pending_operations:
            stats["processed"] += 1
            
            try:
                self._process_single_operation(operation)
                stats["successful"] += 1
                logger.info(f"Successfully processed scheduled operation {operation.id}")
                
            except Exception as e:
                stats["failed"] += 1
                error_msg = f"Failed to process operation {operation.id}: {str(e)}"
                stats["errors"].append(error_msg)
                logger.error(error_msg, exc_info=True)
                
                self._handle_operation_failure(operation, str(e))
        
        if stats["processed"] > 0:
            logger.info(f"Processed {stats['processed']} scheduled operations: "
                       f"{stats['successful']} successful, {stats['failed']} failed")
        
        return stats
    
    def _process_single_operation(self, operation: SoftDeletionSchedule) -> None:
        """
        Process a single scheduled operation.
        
        Args:
            operation: Scheduled operation to process
        """
        operation.status = "in_progress"
        operation.executed_at = datetime.utcnow()
        self.db.add(operation)
        self.db.commit()
        
        try:
            if operation.operation_type == "purge":
                self._process_purge_operation(operation)
            elif operation.operation_type == "notification":
                self._process_notification_operation(operation)
            elif operation.operation_type == "policy_check":
                self._process_policy_check_operation(operation)
            else:
                raise ValueError(f"Unknown operation type: {operation.operation_type}")
            
            operation.status = "completed"
            operation.execution_result = "Operation completed successfully"
            
        except Exception as e:
            operation.status = "failed"
            operation.execution_result = str(e)
            raise
        
        finally:
            self.db.add(operation)
            self.db.commit()
    
    def _process_purge_operation(self, operation: SoftDeletionSchedule) -> None:
        """
        Process a purge operation.
        
        Args:
            operation: Purge operation to process
        """
        # This is a placeholder - actual implementation would depend on the specific entity type
        # For now, we'll just log the operation
        logger.info(f"Processing purge operation for {operation.entity_type} {operation.entity_id}")
        
        # In a real implementation, you would:
        # 1. Load the entity from the appropriate table
        # 2. Verify it's still soft-deleted and eligible for purging
        # 3. Perform the permanent deletion
        # 4. Create audit records
        
        # Example pseudo-code:
        # entity = get_entity_by_type_and_id(operation.entity_type, operation.entity_id)
        # if entity and entity.is_deleted:
        #     self.soft_deletion_service.permanently_delete_entity(
        #         entity, operation.entity_type, user_id=None, reason="Scheduled purge"
        #     )
    
    def _process_notification_operation(self, operation: SoftDeletionSchedule) -> None:
        """
        Process a notification operation.
        
        Args:
            operation: Notification operation to process
        """
        logger.info(f"Processing notification operation for {operation.entity_type} {operation.entity_id}")
        
        # Create and send notification
        # This would integrate with your notification system (email, in-app, etc.)
        
        # Example implementation:
        # notification = SoftDeletionNotification(
        #     entity_type=operation.entity_type,
        #     entity_id=operation.entity_id,
        #     recipient_id=...,  # Would need to determine recipient
        #     notification_type="purge_reminder",
        #     scheduled_purge_date=...,  # Calculate from policy
        #     days_until_purge=...,
        #     subject="Upcoming Data Purge Notification",
        #     message="Your data will be permanently deleted soon..."
        # )
        # self.db.add(notification)
        # self.db.commit()
    
    def _process_policy_check_operation(self, operation: SoftDeletionSchedule) -> None:
        """
        Process a policy check operation.
        
        Args:
            operation: Policy check operation to process
        """
        logger.info(f"Processing policy check for {operation.entity_type} {operation.entity_id}")
        
        # Verify that the entity still complies with current policies
        # This could involve checking if policies have changed and rescheduling operations
        
        policy = self.db.query(SoftDeletionPolicy).filter(
            SoftDeletionPolicy.id == operation.policy_id
        ).first()
        
        if not policy:
            raise ValueError(f"Policy {operation.policy_id} not found")
        
        # Check if policy has been updated and reschedule if necessary
        # This is a simplified check - real implementation would be more sophisticated
        logger.info(f"Policy check completed for {operation.entity_type} {operation.entity_id}")
    
    def _handle_operation_failure(self, operation: SoftDeletionSchedule, error_message: str) -> None:
        """
        Handle a failed operation, including retry logic.
        
        Args:
            operation: Failed operation
            error_message: Error message from the failure
        """
        operation.retry_count += 1
        
        if operation.retry_count < operation.max_retries:
            # Schedule retry
            retry_delay = min(2 ** operation.retry_count, 60)  # Exponential backoff, max 60 minutes
            operation.scheduled_for = datetime.utcnow() + timedelta(minutes=retry_delay)
            operation.status = "pending"
            operation.execution_result = f"Retry {operation.retry_count}/{operation.max_retries}: {error_message}"
            
            logger.warning(f"Scheduling retry {operation.retry_count} for operation {operation.id} "
                          f"in {retry_delay} minutes")
        else:
            # Max retries reached
            operation.status = "failed"
            operation.execution_result = f"Max retries reached: {error_message}"
            
            logger.error(f"Operation {operation.id} failed permanently after {operation.retry_count} retries")
        
        self.db.add(operation)
        self.db.commit()
    
    def cleanup_completed_operations(self, retention_days: int = 30) -> int:
        """
        Clean up old completed operations.
        
        Args:
            retention_days: Number of days to retain completed operations
            
        Returns:
            Number of operations cleaned up
        """
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
        
        completed_operations = self.db.query(SoftDeletionSchedule).filter(
            and_(
                SoftDeletionSchedule.status.in_(["completed", "failed", "cancelled"]),
                SoftDeletionSchedule.updated_at < cutoff_date
            )
        ).all()
        
        count = len(completed_operations)
        
        for operation in completed_operations:
            self.db.delete(operation)
        
        self.db.commit()
        
        logger.info(f"Cleaned up {count} old scheduled operations")
        return count
    
    def get_operation_statistics(self) -> dict:
        """
        Get statistics about scheduled operations.
        
        Returns:
            Dictionary with operation statistics
        """
        stats = {}
        
        # Count by status
        for status in ["pending", "in_progress", "completed", "failed", "cancelled"]:
            count = self.db.query(SoftDeletionSchedule).filter(
                SoftDeletionSchedule.status == status
            ).count()
            stats[f"{status}_count"] = count
        
        # Count by operation type
        for op_type in ["purge", "notification", "policy_check"]:
            count = self.db.query(SoftDeletionSchedule).filter(
                SoftDeletionSchedule.operation_type == op_type
            ).count()
            stats[f"{op_type}_count"] = count
        
        # Overdue operations
        now = datetime.utcnow()
        overdue_count = self.db.query(SoftDeletionSchedule).filter(
            and_(
                SoftDeletionSchedule.status == "pending",
                SoftDeletionSchedule.scheduled_for < now
            )
        ).count()
        stats["overdue_count"] = overdue_count
        
        return stats
    
    def cancel_operations_for_entity(self, entity_type: str, entity_id: int) -> int:
        """
        Cancel all pending operations for a specific entity.
        
        Args:
            entity_type: Type of entity
            entity_id: ID of entity
            
        Returns:
            Number of operations cancelled
        """
        operations = self.db.query(SoftDeletionSchedule).filter(
            and_(
                SoftDeletionSchedule.entity_type == entity_type,
                SoftDeletionSchedule.entity_id == entity_id,
                SoftDeletionSchedule.status == "pending"
            )
        ).all()
        
        count = len(operations)
        
        for operation in operations:
            operation.status = "cancelled"
            operation.execution_result = "Cancelled due to entity restoration or policy change"
            self.db.add(operation)
        
        self.db.commit()
        
        logger.info(f"Cancelled {count} scheduled operations for {entity_type} {entity_id}")
        return count


def run_scheduled_operations():
    """
    Entry point for running scheduled operations.
    
    This function can be called by a cron job or task scheduler.
    """
    db = next(get_db())
    try:
        scheduler = SoftDeletionScheduler(db)
        stats = scheduler.process_scheduled_operations()
        
        # Also cleanup old operations
        cleanup_count = scheduler.cleanup_completed_operations()
        stats["cleanup_count"] = cleanup_count
        
        return stats
    finally:
        db.close()
