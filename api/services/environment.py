"""
Environment service module.

This module provides service functions for managing environments in the RegressionRigor platform.
These functions handle the business logic for environment operations, acting as an intermediary
between the API routes and the database models.
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_

from api.models.environment import Environment, EnvironmentType, EnvironmentStatus
from api.models.schemas.environment import EnvironmentCreate, EnvironmentUpdate


def get_environments(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    search: Optional[str] = None,
    created_by: Optional[int] = None,
    include_deleted: bool = False
) -> List[Environment]:
    """
    Retrieve a list of environments with optional filtering.
    
    Args:
        db: Database session
        skip: Number of records to skip for pagination
        limit: Maximum number of records to return
        status: Filter by environment status
        search: Search term to filter environments by name or description
        created_by: Filter by creator user ID
        include_deleted: Whether to include soft-deleted environments
        
    Returns:
        List of Environment objects
    """
    query = db.query(Environment)
    
    # Apply filters
    if not include_deleted:
        query = query.filter(Environment.deleted_at == None)
    
    if status:
        query = query.filter(Environment.status == status)
    
    if search:
        query = query.filter(
            or_(
                Environment.name.ilike(f"%{search}%"),
                Environment.description.ilike(f"%{search}%")
            )
        )
    
    if created_by:
        query = query.filter(Environment.created_by == created_by)
    
    # Apply pagination
    query = query.offset(skip).limit(limit)
    
    return query.all()


def get_environment_by_id(
    db: Session,
    environment_id: int,
    include_deleted: bool = False
) -> Optional[Environment]:
    """
    Retrieve an environment by its ID.
    
    Args:
        db: Database session
        environment_id: ID of the environment to retrieve
        include_deleted: Whether to include soft-deleted environments
        
    Returns:
        Environment object if found, None otherwise
    """
    query = db.query(Environment).filter(Environment.id == environment_id)
    
    if not include_deleted:
        query = query.filter(Environment.deleted_at == None)
    
    return query.first()


def create_environment(
    db: Session,
    environment_data: EnvironmentCreate,
    user_id: int
) -> Environment:
    """
    Create a new environment.
    
    Args:
        db: Database session
        environment_data: Data for the new environment
        user_id: ID of the user creating the environment
        
    Returns:
        Created Environment object
    """
    environment = Environment(
        name=environment_data.name,
        description=environment_data.description,
        type=environment_data.type,
        status=environment_data.status,
        created_by=user_id,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        is_deprecated=False,
        is_revoked=False,
        version=1
    )
    
    db.add(environment)
    db.commit()
    db.refresh(environment)
    
    return environment


def update_environment(
    db: Session,
    environment_id: int,
    environment_data: EnvironmentUpdate
) -> Optional[Environment]:
    """
    Update an existing environment.
    
    Args:
        db: Database session
        environment_id: ID of the environment to update
        environment_data: Data to update the environment with
        
    Returns:
        Updated Environment object if found, None otherwise
    """
    environment = get_environment_by_id(db, environment_id)
    
    if not environment:
        return None
    
    # Update fields if provided
    if environment_data.name is not None:
        environment.name = environment_data.name
    
    if environment_data.description is not None:
        environment.description = environment_data.description
    
    if environment_data.type is not None:
        environment.type = environment_data.type
    
    if environment_data.status is not None:
        environment.status = environment_data.status
    
    environment.updated_at = datetime.utcnow()
    environment.version += 1
    
    db.add(environment)
    db.commit()
    db.refresh(environment)
    
    return environment


def delete_environment(
    db: Session,
    environment_id: int
) -> Optional[Environment]:
    """
    Soft-delete an environment.
    
    Args:
        db: Database session
        environment_id: ID of the environment to delete
        
    Returns:
        Deleted Environment object if found, None otherwise
    """
    environment = get_environment_by_id(db, environment_id)
    
    if not environment:
        return None
    
    environment.soft_delete(db)
    
    return environment


def restore_environment(
    db: Session,
    environment_id: int
) -> Optional[Environment]:
    """
    Restore a soft-deleted environment.
    
    Args:
        db: Database session
        environment_id: ID of the environment to restore
        
    Returns:
        Restored Environment object if found, None otherwise
    """
    environment = get_environment_by_id(db, environment_id, include_deleted=True)
    
    if not environment or not environment.deleted_at:
        return None
    
    environment.restore(db)
    
    return environment


def get_environment_assessments(
    db: Session,
    environment_id: int,
    skip: int = 0,
    limit: int = 100
) -> List[Any]:
    """
    Retrieve assessments associated with an environment.
    
    Args:
        db: Database session
        environment_id: ID of the environment
        skip: Number of records to skip for pagination
        limit: Maximum number of records to return
        
    Returns:
        List of Assessment objects
    """
    environment = get_environment_by_id(db, environment_id)
    
    if not environment:
        return []
    
    return environment.assessments[skip:skip+limit]


def deprecate_environment(
    db: Session,
    environment_id: int,
    user_id: int
) -> Optional[Environment]:
    """
    Mark an environment as deprecated.
    
    Args:
        db: Database session
        environment_id: ID of the environment to deprecate
        user_id: ID of the user deprecating the environment
        
    Returns:
        Deprecated Environment object if found, None otherwise
    """
    environment = get_environment_by_id(db, environment_id)
    
    if not environment:
        return None
    
    environment.is_deprecated = True
    environment.updated_at = datetime.utcnow()
    environment.version += 1
    
    db.add(environment)
    db.commit()
    db.refresh(environment)
    
    return environment


def revoke_environment(
    db: Session,
    environment_id: int,
    user_id: int
) -> Optional[Environment]:
    """
    Revoke an environment.
    
    Args:
        db: Database session
        environment_id: ID of the environment to revoke
        user_id: ID of the user revoking the environment
        
    Returns:
        Revoked Environment object if found, None otherwise
    """
    environment = get_environment_by_id(db, environment_id)
    
    if not environment:
        return None
    
    environment.is_revoked = True
    environment.revoked_by_id = user_id
    environment.updated_at = datetime.utcnow()
    environment.version += 1
    
    db.add(environment)
    db.commit()
    db.refresh(environment)
    
    return environment 