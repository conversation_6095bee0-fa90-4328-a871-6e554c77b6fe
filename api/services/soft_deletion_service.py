"""
Advanced Soft Deletion Service.

This service provides comprehensive soft deletion management including
policy enforcement, audit logging, cascade operations, and scheduled purging.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Type, Union

from sqlalchemy import and_, or_
from sqlalchemy.orm import Session

from api.models.soft_deletion import (
    SoftDeletionAudit,
    SoftDeletionNotification,
    SoftDeletionPolicy,
    SoftDeletionSchedule,
)
from api.utils.soft_delete import AdvancedSoftDeleteMixin

logger = logging.getLogger(__name__)


class SoftDeletionService:
    """Service for managing advanced soft deletion operations."""
    
    def __init__(self, db: Session):
        """
        Initialize the soft deletion service.
        
        Args:
            db: Database session
        """
        self.db = db
    
    def get_policy(self, entity_type: str) -> Optional[SoftDeletionPolicy]:
        """
        Get the soft deletion policy for an entity type.
        
        Args:
            entity_type: Type of entity (e.g., 'campaign', 'testcase')
            
        Returns:
            Soft deletion policy or None if not found
        """
        return self.db.query(SoftDeletionPolicy).filter(
            SoftDeletionPolicy.entity_type == entity_type
        ).first()
    
    def create_policy(
        self,
        entity_type: str,
        retention_period_days: int,
        auto_purge_enabled: bool = True,
        cascade_deletion: bool = True,
        notification_enabled: bool = True,
        notification_days_before: int = 7,
        description: Optional[str] = None,
        created_by: Optional[int] = None
    ) -> SoftDeletionPolicy:
        """
        Create a new soft deletion policy.
        
        Args:
            entity_type: Type of entity
            retention_period_days: Days to retain before purging
            auto_purge_enabled: Whether to automatically purge expired records
            cascade_deletion: Whether to cascade deletions to related entities
            notification_enabled: Whether to send notifications before purging
            notification_days_before: Days before purge to send notifications
            description: Optional description of the policy
            created_by: ID of user creating the policy
            
        Returns:
            Created soft deletion policy
        """
        policy = SoftDeletionPolicy(
            entity_type=entity_type,
            retention_period_days=retention_period_days,
            auto_purge_enabled=auto_purge_enabled,
            cascade_deletion=cascade_deletion,
            notification_enabled=notification_enabled,
            notification_days_before=notification_days_before,
            description=description,
            created_by=created_by
        )
        
        self.db.add(policy)
        self.db.commit()
        self.db.refresh(policy)
        
        logger.info(f"Created soft deletion policy for {entity_type}")
        return policy
    
    def soft_delete_entity(
        self,
        entity: AdvancedSoftDeleteMixin,
        entity_type: str,
        user_id: Optional[int] = None,
        reason: Optional[str] = None,
        cascade: bool = True,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> SoftDeletionAudit:
        """
        Soft delete an entity with full audit logging.
        
        Args:
            entity: Entity to soft delete
            entity_type: Type of entity
            user_id: ID of user performing deletion
            reason: Reason for deletion
            cascade: Whether to cascade delete related entities
            ip_address: IP address of user
            user_agent: User agent string
            
        Returns:
            Audit record for the operation
        """
        if entity.is_deleted:
            raise ValueError(f"Entity {entity_type} is already deleted")
        
        # Get policy for scheduling
        policy = self.get_policy(entity_type)
        
        # Perform soft deletion
        entity.advanced_soft_delete(user_id=user_id, reason=reason, cascade=cascade)
        self.db.add(entity)
        
        # Create audit record
        audit = SoftDeletionAudit(
            entity_type=entity_type,
            entity_id=entity.id,
            operation_type="soft_delete",
            performed_by=user_id,
            reason=reason,
            cascade_triggered=cascade,
            affected_entities_count=1,  # Will be updated if cascade occurs
            policy_id=policy.id if policy else None,
            retention_period_used=policy.retention_period_days if policy else None,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        self.db.add(audit)
        self.db.commit()
        
        # Schedule purge if policy exists and auto-purge is enabled
        if policy and policy.auto_purge_enabled:
            self._schedule_purge(entity, entity_type, policy)
        
        # Schedule notifications if enabled
        if policy and policy.notification_enabled:
            self._schedule_notifications(entity, entity_type, policy, user_id)
        
        logger.info(f"Soft deleted {entity_type} {entity.id} by user {user_id}")
        return audit
    
    def restore_entity(
        self,
        entity: AdvancedSoftDeleteMixin,
        entity_type: str,
        user_id: Optional[int] = None,
        reason: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> SoftDeletionAudit:
        """
        Restore a soft-deleted entity.
        
        Args:
            entity: Entity to restore
            entity_type: Type of entity
            user_id: ID of user performing restoration
            reason: Reason for restoration
            ip_address: IP address of user
            user_agent: User agent string
            
        Returns:
            Audit record for the operation
        """
        if not entity.is_deleted:
            raise ValueError(f"Entity {entity_type} is not deleted")
        
        # Restore the entity
        entity.advanced_restore(user_id=user_id, reason=reason)
        self.db.add(entity)
        
        # Create audit record
        audit = SoftDeletionAudit(
            entity_type=entity_type,
            entity_id=entity.id,
            operation_type="restore",
            performed_by=user_id,
            reason=reason,
            cascade_triggered=False,
            affected_entities_count=1,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        self.db.add(audit)
        
        # Cancel any scheduled operations for this entity
        self._cancel_scheduled_operations(entity_type, entity.id)
        
        self.db.commit()
        
        logger.info(f"Restored {entity_type} {entity.id} by user {user_id}")
        return audit
    
    def permanently_delete_entity(
        self,
        entity: AdvancedSoftDeleteMixin,
        entity_type: str,
        user_id: Optional[int] = None,
        reason: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> SoftDeletionAudit:
        """
        Permanently delete a soft-deleted entity.
        
        Args:
            entity: Entity to permanently delete
            entity_type: Type of entity
            user_id: ID of user performing deletion
            reason: Reason for permanent deletion
            ip_address: IP address of user
            user_agent: User agent string
            
        Returns:
            Audit record for the operation
        """
        if not entity.is_deleted:
            raise ValueError(f"Entity {entity_type} must be soft-deleted before permanent deletion")
        
        entity_id = entity.id
        
        # Create audit record before deletion
        audit = SoftDeletionAudit(
            entity_type=entity_type,
            entity_id=entity_id,
            operation_type="permanent_delete",
            performed_by=user_id,
            reason=reason,
            cascade_triggered=False,
            affected_entities_count=1,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        self.db.add(audit)
        
        # Cancel any scheduled operations
        self._cancel_scheduled_operations(entity_type, entity_id)
        
        # Permanently delete the entity
        self.db.delete(entity)
        self.db.commit()
        
        logger.info(f"Permanently deleted {entity_type} {entity_id} by user {user_id}")
        return audit
    
    def _schedule_purge(
        self,
        entity: AdvancedSoftDeleteMixin,
        entity_type: str,
        policy: SoftDeletionPolicy
    ) -> None:
        """Schedule a purge operation for the entity."""
        purge_date = datetime.utcnow() + timedelta(days=policy.retention_period_days)
        
        schedule = SoftDeletionSchedule(
            entity_type=entity_type,
            entity_id=entity.id,
            operation_type="purge",
            scheduled_for=purge_date,
            policy_id=policy.id
        )
        
        self.db.add(schedule)
        logger.debug(f"Scheduled purge for {entity_type} {entity.id} on {purge_date}")
    
    def _schedule_notifications(
        self,
        entity: AdvancedSoftDeleteMixin,
        entity_type: str,
        policy: SoftDeletionPolicy,
        user_id: Optional[int]
    ) -> None:
        """Schedule notifications for upcoming purge."""
        if not user_id:
            return
        
        purge_date = datetime.utcnow() + timedelta(days=policy.retention_period_days)
        notification_date = purge_date - timedelta(days=policy.notification_days_before)
        
        notification = SoftDeletionNotification(
            entity_type=entity_type,
            entity_id=entity.id,
            recipient_id=user_id,
            notification_type="purge_warning",
            scheduled_purge_date=purge_date,
            days_until_purge=policy.notification_days_before
        )
        
        self.db.add(notification)
        logger.debug(f"Scheduled notification for {entity_type} {entity.id}")
    
    def _cancel_scheduled_operations(self, entity_type: str, entity_id: int) -> None:
        """Cancel all scheduled operations for an entity."""
        schedules = self.db.query(SoftDeletionSchedule).filter(
            and_(
                SoftDeletionSchedule.entity_type == entity_type,
                SoftDeletionSchedule.entity_id == entity_id,
                SoftDeletionSchedule.status == "pending"
            )
        ).all()
        
        for schedule in schedules:
            schedule.status = "cancelled"
            self.db.add(schedule)
        
        logger.debug(f"Cancelled {len(schedules)} scheduled operations for {entity_type} {entity_id}")
