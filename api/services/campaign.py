"""
Service layer for campaign operations.

This module contains the business logic for managing campaigns.
It acts as an intermediary between the API routes and the database models.
"""
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

from api.models.campaign import Campaign
from api.models.schemas.campaign import (
    CampaignCreate, CampaignUpdate, 
    CampaignSummary, TestCaseAssignment
)


def get_campaigns(
    db: Session, 
    skip: int = 0, 
    limit: int = 100, 
    status: Optional[str] = None,
    search: Optional[str] = None,
    created_by: Optional[int] = None,
    include_deleted: bool = False
) -> List[Campaign]:
    """
    Get a list of campaigns with optional filtering.
    
    Args:
        db: Database session
        skip: Number of records to skip (for pagination)
        limit: Maximum number of records to return
        status: Filter by campaign status
        search: Search term for name or description
        created_by: Filter by creator user ID
        include_deleted: Whether to include soft-deleted campaigns
        
    Returns:
        List of campaign objects
    """
    query = db.query(Campaign)
    
    # Apply filters
    if not include_deleted:
        query = query.filter(Campaign.deleted_at.is_(None))
    
    if status:
        query = query.filter(Campaign.status == status)
    
    if created_by:
        query = query.filter(Campaign.created_by == created_by)
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                Campaign.name.ilike(search_term),
                Campaign.description.ilike(search_term)
            )
        )
    
    # Apply pagination
    query = query.order_by(Campaign.created_at.desc()).offset(skip).limit(limit)
    
    return query.all()


def get_campaign_by_id(db: Session, campaign_id: int, include_deleted: bool = False) -> Optional[Campaign]:
    """
    Get a campaign by its ID.
    
    Args:
        db: Database session
        campaign_id: ID of the campaign to retrieve
        include_deleted: Whether to include soft-deleted campaigns
        
    Returns:
        Campaign object if found, None otherwise
    """
    query = db.query(Campaign).filter(Campaign.id == campaign_id)
    
    if not include_deleted:
        query = query.filter(Campaign.deleted_at.is_(None))
    
    return query.first()


def create_campaign(db: Session, campaign_data: CampaignCreate, user_id: int) -> Campaign:
    """
    Create a new campaign.
    
    Args:
        db: Database session
        campaign_data: Campaign data
        user_id: ID of the user creating the campaign
        
    Returns:
        Created campaign object
    """
    campaign = Campaign(
        name=campaign_data.name,
        description=campaign_data.description,
        status=campaign_data.status.value,
        start_date=campaign_data.start_date,
        end_date=campaign_data.end_date,
        created_by=user_id
    )
    
    db.add(campaign)
    db.commit()
    db.refresh(campaign)
    
    return campaign


def update_campaign(
    db: Session, 
    campaign_id: int, 
    campaign_data: CampaignUpdate
) -> Optional[Campaign]:
    """
    Update an existing campaign.
    
    Args:
        db: Database session
        campaign_id: ID of the campaign to update
        campaign_data: Updated campaign data
        
    Returns:
        Updated campaign object if found, None otherwise
    """
    campaign = get_campaign_by_id(db, campaign_id)
    
    if not campaign:
        return None
    
    # Update fields if provided
    if campaign_data.name is not None:
        campaign.name = campaign_data.name
    
    if campaign_data.description is not None:
        campaign.description = campaign_data.description
    
    if campaign_data.status is not None:
        campaign.status = campaign_data.status.value
    
    if campaign_data.start_date is not None:
        campaign.start_date = campaign_data.start_date
    
    if campaign_data.end_date is not None:
        campaign.end_date = campaign_data.end_date
    
    campaign.updated_at = datetime.utcnow()
    
    db.commit()
    db.refresh(campaign)
    
    return campaign


def delete_campaign(db: Session, campaign_id: int) -> bool:
    """
    Soft-delete a campaign.
    
    Args:
        db: Database session
        campaign_id: ID of the campaign to delete
        
    Returns:
        True if the campaign was deleted, False otherwise
    """
    campaign = get_campaign_by_id(db, campaign_id)
    
    if not campaign:
        return False
    
    campaign.soft_delete(db)
    
    return True


def restore_campaign(db: Session, campaign_id: int) -> bool:
    """
    Restore a soft-deleted campaign.
    
    Args:
        db: Database session
        campaign_id: ID of the campaign to restore
        
    Returns:
        True if the campaign was restored, False otherwise
    """
    campaign = get_campaign_by_id(db, campaign_id, include_deleted=True)
    
    if not campaign or not campaign.deleted_at:
        return False
    
    campaign.deleted_at = None
    db.commit()
    
    return True


def get_campaign_test_cases(db: Session, campaign_id: int, skip: int = 0, limit: int = 100) -> List[Any]:
    """
    Get test cases associated with a campaign.
    
    Args:
        db: Database session
        campaign_id: ID of the campaign
        skip: Number of records to skip (for pagination)
        limit: Maximum number of records to return
        
    Returns:
        List of test case objects
    """
    campaign = get_campaign_by_id(db, campaign_id)
    
    if not campaign:
        return []
    
    return campaign.test_cases[skip:skip+limit]


def assign_test_cases(db: Session, campaign_id: int, assignment: TestCaseAssignment) -> bool:
    """
    Assign test cases to a campaign.
    
    Args:
        db: Database session
        campaign_id: ID of the campaign
        assignment: Test case assignment data
        
    Returns:
        True if the test cases were assigned, False otherwise
    """
    from api.models.test_case import TestCase  # Import here to avoid circular imports
    
    campaign = get_campaign_by_id(db, campaign_id)
    
    if not campaign:
        return False
    
    # Get test cases by IDs
    test_cases = db.query(TestCase).filter(
        TestCase.id.in_(assignment.test_case_ids),
        TestCase.deleted_at.is_(None)
    ).all()
    
    # Add test cases to campaign
    for test_case in test_cases:
        if test_case not in campaign.test_cases:
            campaign.test_cases.append(test_case)
    
    db.commit()
    
    return True


def remove_test_case(db: Session, campaign_id: int, test_case_id: int) -> bool:
    """
    Remove a test case from a campaign.
    
    Args:
        db: Database session
        campaign_id: ID of the campaign
        test_case_id: ID of the test case to remove
        
    Returns:
        True if the test case was removed, False otherwise
    """
    from api.models.test_case import TestCase  # Import here to avoid circular imports
    
    campaign = get_campaign_by_id(db, campaign_id)
    
    if not campaign:
        return False
    
    # Get test case by ID
    test_case = db.query(TestCase).filter(TestCase.id == test_case_id).first()
    
    if not test_case or test_case not in campaign.test_cases:
        return False
    
    # Remove test case from campaign
    campaign.test_cases.remove(test_case)
    db.commit()
    
    return True


def get_campaign_summary(db: Session, campaign_id: int) -> Optional[CampaignSummary]:
    """
    Get a summary of a campaign's test cases and assessments.
    
    Args:
        db: Database session
        campaign_id: ID of the campaign
        
    Returns:
        Campaign summary object if the campaign exists, None otherwise
    """
    campaign = get_campaign_by_id(db, campaign_id)
    
    if not campaign:
        return None
    
    # Count test cases by status
    test_case_status = {}
    for test_case in campaign.test_cases:
        status = test_case.status
        test_case_status[status] = test_case_status.get(status, 0) + 1
    
    # Count assessments by status
    assessment_status = {}
    for assessment in campaign.assessments:
        status = assessment.status
        assessment_status[status] = assessment_status.get(status, 0) + 1
    
    # Calculate completion percentage
    total_assessments = len(campaign.assessments)
    completed_assessments = assessment_status.get("completed", 0) + assessment_status.get("reviewed", 0)
    completion_percentage = (completed_assessments / total_assessments * 100) if total_assessments > 0 else 0
    
    return CampaignSummary(
        total_test_cases=len(campaign.test_cases),
        total_assessments=total_assessments,
        test_case_status=test_case_status,
        assessment_status=assessment_status,
        completion_percentage=completion_percentage
    )


def get_campaigns_by_assessment(
    db: Session, 
    assessment_id: int, 
    skip: int = 0, 
    limit: int = 100,
    include_deleted: bool = False
) -> List[Campaign]:
    """
    Get campaigns for a specific assessment.
    
    Args:
        db: Database session
        assessment_id: ID of the assessment
        skip: Number of records to skip (for pagination)
        limit: Maximum number of records to return
        include_deleted: Whether to include soft-deleted campaigns
        
    Returns:
        List of campaign objects
    """
    query = db.query(Campaign).filter(Campaign.assessment_id == assessment_id)
    
    if not include_deleted:
        query = query.filter(Campaign.deleted_at.is_(None))
    
    return query.order_by(Campaign.created_at.desc()).offset(skip).limit(limit).all() 