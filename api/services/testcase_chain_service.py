"""
Testcase Chain Service.

This service provides comprehensive testcase chain management including
chain creation, validation, execution orchestration, and dependency resolution.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Tuple

from sqlalchemy import and_, or_
from sqlalchemy.orm import Session

from api.models.testcase_chain import (
    ChainExecution,
    NodeExecution,
    TestcaseChain,
    TestcaseChainEdge,
    TestcaseChainNode,
    TestcaseCondition,
)
from api.schemas.testcase_chain import (
    ChainValidationResult,
    ChainExecutionStats,
    ExecutionStatus,
    NodeExecutionStatus,
)

logger = logging.getLogger(__name__)


class TestcaseChainService:
    """Service for managing testcase chains and their execution."""
    
    def __init__(self, db: Session):
        """
        Initialize the testcase chain service.
        
        Args:
            db: Database session
        """
        self.db = db
    
    def create_chain(
        self,
        name: str,
        description: Optional[str] = None,
        chain_type: str = "sequential",
        max_execution_time_minutes: int = 60,
        retry_on_failure: bool = False,
        auto_cleanup: bool = True,
        created_by: int = None
    ) -> TestcaseChain:
        """
        Create a new testcase chain.
        
        Args:
            name: Name of the chain
            description: Optional description
            chain_type: Type of chain execution
            max_execution_time_minutes: Maximum execution time
            retry_on_failure: Whether to retry on failure
            auto_cleanup: Whether to auto-cleanup
            created_by: ID of user creating the chain
            
        Returns:
            Created testcase chain
        """
        chain = TestcaseChain(
            name=name,
            description=description,
            chain_type=chain_type,
            max_execution_time_minutes=max_execution_time_minutes,
            retry_on_failure=retry_on_failure,
            auto_cleanup=auto_cleanup,
            created_by=created_by
        )
        
        self.db.add(chain)
        self.db.commit()
        self.db.refresh(chain)
        
        logger.info(f"Created testcase chain '{name}' with ID {chain.id}")
        return chain
    
    def add_node_to_chain(
        self,
        chain_id: int,
        testcase_id: int,
        node_type: str = "standard",
        execution_order: int = 0,
        position_x: float = 0.0,
        position_y: float = 0.0,
        condition_expression: Optional[str] = None,
        timeout_minutes: int = 30,
        retry_count: int = 0,
        continue_on_failure: bool = False,
        required_for_completion: bool = True
    ) -> TestcaseChainNode:
        """
        Add a node to a testcase chain.
        
        Args:
            chain_id: ID of the chain
            testcase_id: ID of the testcase
            node_type: Type of node
            execution_order: Order of execution
            position_x: X position for UI
            position_y: Y position for UI
            condition_expression: Condition for conditional nodes
            timeout_minutes: Timeout for execution
            retry_count: Number of retries
            continue_on_failure: Whether to continue on failure
            required_for_completion: Whether required for completion
            
        Returns:
            Created chain node
        """
        # Check if chain exists
        chain = self.db.query(TestcaseChain).filter(TestcaseChain.id == chain_id).first()
        if not chain:
            raise ValueError(f"Chain with ID {chain_id} not found")
        
        # Check for duplicate testcase in chain
        existing_node = self.db.query(TestcaseChainNode).filter(
            and_(
                TestcaseChainNode.chain_id == chain_id,
                TestcaseChainNode.testcase_id == testcase_id
            )
        ).first()
        
        if existing_node:
            raise ValueError(f"Testcase {testcase_id} already exists in chain {chain_id}")
        
        node = TestcaseChainNode(
            chain_id=chain_id,
            testcase_id=testcase_id,
            node_type=node_type,
            execution_order=execution_order,
            position_x=position_x,
            position_y=position_y,
            condition_expression=condition_expression,
            timeout_minutes=timeout_minutes,
            retry_count=retry_count,
            continue_on_failure=continue_on_failure,
            required_for_completion=required_for_completion
        )
        
        self.db.add(node)
        self.db.commit()
        self.db.refresh(node)
        
        logger.info(f"Added node {node.id} to chain {chain_id}")
        return node
    
    def add_edge_to_chain(
        self,
        source_node_id: int,
        target_node_id: int,
        edge_type: str = "standard",
        condition: Optional[str] = None,
        weight: int = 1,
        label: Optional[str] = None,
        description: Optional[str] = None
    ) -> TestcaseChainEdge:
        """
        Add an edge between two nodes in a chain.
        
        Args:
            source_node_id: ID of source node
            target_node_id: ID of target node
            edge_type: Type of edge
            condition: Condition for traversing edge
            weight: Weight for prioritization
            label: Label for the edge
            description: Description of the edge
            
        Returns:
            Created chain edge
        """
        if source_node_id == target_node_id:
            raise ValueError("Source and target nodes cannot be the same")
        
        # Check if nodes exist and are in the same chain
        source_node = self.db.query(TestcaseChainNode).filter(
            TestcaseChainNode.id == source_node_id
        ).first()
        target_node = self.db.query(TestcaseChainNode).filter(
            TestcaseChainNode.id == target_node_id
        ).first()
        
        if not source_node or not target_node:
            raise ValueError("Source or target node not found")
        
        if source_node.chain_id != target_node.chain_id:
            raise ValueError("Source and target nodes must be in the same chain")
        
        # Check for duplicate edge
        existing_edge = self.db.query(TestcaseChainEdge).filter(
            and_(
                TestcaseChainEdge.source_node_id == source_node_id,
                TestcaseChainEdge.target_node_id == target_node_id
            )
        ).first()
        
        if existing_edge:
            raise ValueError(f"Edge already exists between nodes {source_node_id} and {target_node_id}")
        
        edge = TestcaseChainEdge(
            source_node_id=source_node_id,
            target_node_id=target_node_id,
            edge_type=edge_type,
            condition=condition,
            weight=weight,
            label=label,
            description=description
        )
        
        self.db.add(edge)
        self.db.commit()
        self.db.refresh(edge)
        
        logger.info(f"Added edge {edge.id} from node {source_node_id} to {target_node_id}")
        return edge
    
    def validate_chain(self, chain_id: int) -> ChainValidationResult:
        """
        Validate a testcase chain for structural integrity.
        
        Args:
            chain_id: ID of the chain to validate
            
        Returns:
            Validation result with errors and warnings
        """
        errors = []
        warnings = []
        
        # Get chain and its components
        chain = self.db.query(TestcaseChain).filter(TestcaseChain.id == chain_id).first()
        if not chain:
            return ChainValidationResult(
                is_valid=False,
                errors=[f"Chain with ID {chain_id} not found"],
                warnings=[],
                cycle_detected=False,
                unreachable_nodes=[]
            )
        
        nodes = self.db.query(TestcaseChainNode).filter(
            TestcaseChainNode.chain_id == chain_id
        ).all()
        
        edges = self.db.query(TestcaseChainEdge).join(
            TestcaseChainNode, TestcaseChainEdge.source_node_id == TestcaseChainNode.id
        ).filter(TestcaseChainNode.chain_id == chain_id).all()
        
        if not nodes:
            errors.append("Chain has no nodes")
            return ChainValidationResult(
                is_valid=False,
                errors=errors,
                warnings=warnings,
                cycle_detected=False,
                unreachable_nodes=[]
            )
        
        # Check for start and end nodes
        start_nodes = [n for n in nodes if n.node_type == "start"]
        end_nodes = [n for n in nodes if n.node_type == "end"]
        
        if not start_nodes:
            warnings.append("Chain has no start node")
        elif len(start_nodes) > 1:
            warnings.append("Chain has multiple start nodes")
        
        if not end_nodes:
            warnings.append("Chain has no end node")
        
        # Check for cycles
        cycle_detected = self._detect_cycles(nodes, edges)
        if cycle_detected:
            errors.append("Chain contains cycles")
        
        # Check for unreachable nodes
        unreachable_nodes = self._find_unreachable_nodes(nodes, edges)
        if unreachable_nodes:
            warnings.append(f"Found {len(unreachable_nodes)} unreachable nodes")
        
        # Check for orphaned nodes (no incoming or outgoing edges)
        node_ids = {n.id for n in nodes}
        connected_nodes = set()
        for edge in edges:
            connected_nodes.add(edge.source_node_id)
            connected_nodes.add(edge.target_node_id)
        
        orphaned_nodes = node_ids - connected_nodes
        if orphaned_nodes and len(nodes) > 1:
            warnings.append(f"Found {len(orphaned_nodes)} orphaned nodes")
        
        is_valid = len(errors) == 0
        
        return ChainValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            cycle_detected=cycle_detected,
            unreachable_nodes=list(unreachable_nodes)
        )
    
    def _detect_cycles(self, nodes: List[TestcaseChainNode], edges: List[TestcaseChainEdge]) -> bool:
        """
        Detect cycles in the chain using DFS.
        
        Args:
            nodes: List of chain nodes
            edges: List of chain edges
            
        Returns:
            True if cycles are detected
        """
        # Build adjacency list
        graph = {node.id: [] for node in nodes}
        for edge in edges:
            graph[edge.source_node_id].append(edge.target_node_id)
        
        # DFS cycle detection
        visited = set()
        rec_stack = set()
        
        def dfs(node_id):
            visited.add(node_id)
            rec_stack.add(node_id)
            
            for neighbor in graph.get(node_id, []):
                if neighbor not in visited:
                    if dfs(neighbor):
                        return True
                elif neighbor in rec_stack:
                    return True
            
            rec_stack.remove(node_id)
            return False
        
        for node in nodes:
            if node.id not in visited:
                if dfs(node.id):
                    return True
        
        return False
    
    def _find_unreachable_nodes(self, nodes: List[TestcaseChainNode], edges: List[TestcaseChainEdge]) -> List[int]:
        """
        Find nodes that are unreachable from start nodes.
        
        Args:
            nodes: List of chain nodes
            edges: List of chain edges
            
        Returns:
            List of unreachable node IDs
        """
        # Build adjacency list
        graph = {node.id: [] for node in nodes}
        for edge in edges:
            graph[edge.source_node_id].append(edge.target_node_id)
        
        # Find start nodes
        start_nodes = [n.id for n in nodes if n.node_type == "start"]
        if not start_nodes:
            # If no start nodes, consider first node as start
            start_nodes = [nodes[0].id] if nodes else []
        
        # BFS from start nodes to find reachable nodes
        reachable = set()
        queue = start_nodes.copy()
        reachable.update(start_nodes)
        
        while queue:
            current = queue.pop(0)
            for neighbor in graph.get(current, []):
                if neighbor not in reachable:
                    reachable.add(neighbor)
                    queue.append(neighbor)
        
        # Find unreachable nodes
        all_nodes = {node.id for node in nodes}
        unreachable = all_nodes - reachable
        
        return list(unreachable)

    def start_chain_execution(
        self,
        chain_id: int,
        started_by: int,
        execution_context: Optional[Dict] = None
    ) -> ChainExecution:
        """
        Start execution of a testcase chain.

        Args:
            chain_id: ID of the chain to execute
            started_by: ID of user starting the execution
            execution_context: Optional execution context

        Returns:
            Created chain execution
        """
        # Validate chain before execution
        validation_result = self.validate_chain(chain_id)
        if not validation_result.is_valid:
            raise ValueError(f"Chain validation failed: {', '.join(validation_result.errors)}")

        # Get chain and nodes
        chain = self.db.query(TestcaseChain).filter(TestcaseChain.id == chain_id).first()
        if not chain:
            raise ValueError(f"Chain with ID {chain_id} not found")

        nodes = self.db.query(TestcaseChainNode).filter(
            TestcaseChainNode.chain_id == chain_id
        ).all()

        # Create chain execution
        execution = ChainExecution(
            chain_id=chain_id,
            started_by=started_by,
            start_time=datetime.utcnow(),
            status="running",
            execution_context=execution_context or {},
            total_nodes=len(nodes),
            completed_nodes=0,
            failed_nodes=0,
            skipped_nodes=0
        )

        self.db.add(execution)
        self.db.commit()
        self.db.refresh(execution)

        # Create node executions for all nodes
        for node in nodes:
            node_execution = NodeExecution(
                chain_execution_id=execution.id,
                node_id=node.id,
                status="pending",
                attempt_number=1,
                max_attempts=max(1, node.retry_count + 1)
            )
            self.db.add(node_execution)

        self.db.commit()

        logger.info(f"Started execution {execution.id} for chain {chain_id}")
        return execution

    def get_next_executable_nodes(self, execution_id: int) -> List[TestcaseChainNode]:
        """
        Get the next nodes that can be executed in a chain.

        Args:
            execution_id: ID of the chain execution

        Returns:
            List of nodes ready for execution
        """
        execution = self.db.query(ChainExecution).filter(
            ChainExecution.id == execution_id
        ).first()

        if not execution:
            raise ValueError(f"Execution with ID {execution_id} not found")

        # Get all nodes and their execution status
        node_executions = self.db.query(NodeExecution).filter(
            NodeExecution.chain_execution_id == execution_id
        ).all()

        # Create status map
        node_status = {ne.node_id: ne.status for ne in node_executions}

        # Get all nodes and edges for the chain
        nodes = self.db.query(TestcaseChainNode).filter(
            TestcaseChainNode.chain_id == execution.chain_id
        ).all()

        edges = self.db.query(TestcaseChainEdge).join(
            TestcaseChainNode, TestcaseChainEdge.source_node_id == TestcaseChainNode.id
        ).filter(TestcaseChainNode.chain_id == execution.chain_id).all()

        # Build dependency map
        dependencies = {node.id: [] for node in nodes}
        for edge in edges:
            dependencies[edge.target_node_id].append(edge.source_node_id)

        # Find executable nodes
        executable_nodes = []

        for node in nodes:
            current_status = node_status.get(node.id, "pending")

            # Skip if already completed, running, or failed
            if current_status in ["completed", "running", "failed", "aborted"]:
                continue

            # Check if all dependencies are satisfied
            dependencies_satisfied = True
            for dep_node_id in dependencies[node.id]:
                dep_status = node_status.get(dep_node_id, "pending")
                if dep_status != "completed":
                    dependencies_satisfied = False
                    break

            if dependencies_satisfied:
                executable_nodes.append(node)

        return executable_nodes

    def update_node_execution_status(
        self,
        node_execution_id: int,
        status: str,
        result_data: Optional[Dict] = None,
        error_message: Optional[str] = None,
        output_logs: Optional[str] = None
    ) -> NodeExecution:
        """
        Update the status of a node execution.

        Args:
            node_execution_id: ID of the node execution
            status: New status
            result_data: Optional result data
            error_message: Optional error message
            output_logs: Optional output logs

        Returns:
            Updated node execution
        """
        node_execution = self.db.query(NodeExecution).filter(
            NodeExecution.id == node_execution_id
        ).first()

        if not node_execution:
            raise ValueError(f"Node execution with ID {node_execution_id} not found")

        # Update node execution
        node_execution.status = status
        if result_data is not None:
            node_execution.result_data = result_data
        if error_message is not None:
            node_execution.error_message = error_message
        if output_logs is not None:
            node_execution.output_logs = output_logs

        # Set timing
        if status == "running" and not node_execution.start_time:
            node_execution.start_time = datetime.utcnow()
        elif status in ["completed", "failed", "aborted", "timeout"]:
            if not node_execution.end_time:
                node_execution.end_time = datetime.utcnow()

        self.db.add(node_execution)

        # Update chain execution statistics
        self._update_chain_execution_stats(node_execution.chain_execution_id)

        self.db.commit()
        self.db.refresh(node_execution)

        logger.info(f"Updated node execution {node_execution_id} status to {status}")
        return node_execution

    def _update_chain_execution_stats(self, chain_execution_id: int) -> None:
        """
        Update chain execution statistics based on node execution statuses.

        Args:
            chain_execution_id: ID of the chain execution
        """
        execution = self.db.query(ChainExecution).filter(
            ChainExecution.id == chain_execution_id
        ).first()

        if not execution:
            return

        # Get all node executions
        node_executions = self.db.query(NodeExecution).filter(
            NodeExecution.chain_execution_id == chain_execution_id
        ).all()

        # Count statuses
        completed_count = sum(1 for ne in node_executions if ne.status == "completed")
        failed_count = sum(1 for ne in node_executions if ne.status in ["failed", "aborted", "timeout"])
        skipped_count = sum(1 for ne in node_executions if ne.status == "skipped")
        running_count = sum(1 for ne in node_executions if ne.status == "running")

        # Update execution
        execution.completed_nodes = completed_count
        execution.failed_nodes = failed_count
        execution.skipped_nodes = skipped_count

        # Determine overall status
        if running_count > 0:
            execution.status = "running"
        elif completed_count == execution.total_nodes:
            execution.status = "completed"
            execution.end_time = datetime.utcnow()
        elif failed_count > 0 and not execution.retry_on_failure:
            execution.status = "failed"
            execution.end_time = datetime.utcnow()
        elif completed_count + failed_count + skipped_count == execution.total_nodes:
            # All nodes processed
            if failed_count > 0:
                execution.status = "failed"
            else:
                execution.status = "completed"
            execution.end_time = datetime.utcnow()

        self.db.add(execution)
