import json
import logging
import time
import uuid
from datetime import datetime
from functools import wraps
from typing import Any, Dict, List, Optional, Union

from sqlalchemy.orm import Session
from fastapi import Request, Response

from api.models.logging import LogEntry, PerformanceMetric
from api.database import get_db

# Standard Python logger for console output
std_logger = logging.getLogger(__name__)

# Log levels with their numeric values
LOG_LEVELS = {
    "DEBUG": 10,
    "INFO": 20,
    "WARNING": 30,
    "ERROR": 40,
    "CRITICAL": 50
}


class Logger:
    """
    Unified logging service for both API and UI components.
    This class provides methods for logging messages and metrics at different levels,
    with automatic correlation ID management and context capturing.
    """

    def __init__(self, db_session: Optional[Session] = None):
        """Initialize the logger with an optional database session."""
        self.db_session = db_session
        self._correlation_id = None
        self._session_id = None
        self._user_id = None
        
        # Default configuration (can be overridden)
        self.config = {
            "console_level": "INFO",
            "db_level": "INFO",
            "capture_request_body": False,
            "capture_response_body": False,
            "sensitive_fields": ["password", "token", "secret", "credit_card", "ssn"],
            "async_logging": False,
            "max_log_age_days": 30
        }

    @property
    def correlation_id(self) -> str:
        """Get the current correlation ID or generate a new one."""
        if not self._correlation_id:
            self._correlation_id = str(uuid.uuid4())
        return self._correlation_id

    @correlation_id.setter
    def correlation_id(self, value: str) -> None:
        """Set the correlation ID."""
        self._correlation_id = value

    @property
    def session_id(self) -> Optional[str]:
        """Get the current session ID."""
        return self._session_id

    @session_id.setter
    def session_id(self, value: str) -> None:
        """Set the session ID."""
        self._session_id = value

    @property
    def user_id(self) -> Optional[int]:
        """Get the current user ID."""
        return self._user_id

    @user_id.setter
    def user_id(self, value: int) -> None:
        """Set the user ID."""
        self._user_id = value

    def _get_db_session(self) -> Session:
        """Get a database session, using the provided one or creating a new one."""
        if self.db_session:
            return self.db_session
        return next(get_db())

    def _should_log(self, level: str, threshold: str) -> bool:
        """Check if a message at the given level should be logged."""
        return LOG_LEVELS.get(level, 0) >= LOG_LEVELS.get(threshold, 0)

    def _sanitize_data(self, data: Any) -> Any:
        """Remove sensitive information from data."""
        if not data:
            return data

        if isinstance(data, dict):
            sanitized = {}
            for key, value in data.items():
                if any(sensitive in key.lower() for sensitive in self.config["sensitive_fields"]):
                    sanitized[key] = "********"
                else:
                    sanitized[key] = self._sanitize_data(value)
            return sanitized
        elif isinstance(data, list):
            return [self._sanitize_data(item) for item in data]
        else:
            return data

    def _create_log_entry(
        self,
        level: str,
        message: str,
        source: str,
        component: str,
        metadata: Optional[Dict] = None
    ) -> None:
        """Create a log entry in the database."""
        try:
            # Don't log to DB if level is below configured threshold
            if not self._should_log(level, self.config["db_level"]):
                return

            metadata_safe = self._sanitize_data(metadata) if metadata else None
            
            db = self._get_db_session()
            log_entry = LogEntry(
                timestamp=datetime.now(),
                correlation_id=self.correlation_id,
                user_id=self.user_id,
                session_id=self.session_id,
                log_level=level,
                source=source,
                component=component,
                message=message,
                metadata=metadata_safe
            )
            db.add(log_entry)
            db.commit()
        except Exception as e:
            std_logger.exception(f"Failed to create log entry: {e}")

    def _log_to_console(
        self,
        level: str,
        message: str,
        source: str,
        component: str,
        metadata: Optional[Dict] = None
    ) -> None:
        """Log a message to the console."""
        if not self._should_log(level, self.config["console_level"]):
            return

        log_method = getattr(std_logger, level.lower(), std_logger.info)
        metadata_str = json.dumps(self._sanitize_data(metadata)) if metadata else "{}"
        
        log_method(
            f"[{level}] [{source}] [{component}] [{self.correlation_id}] {message} {metadata_str}"
        )

    def log(
        self,
        level: str,
        message: str,
        source: str,
        component: str,
        metadata: Optional[Dict] = None
    ) -> None:
        """
        Log a message with the specified level, source, and component.
        
        Args:
            level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            message: The message to log
            source: The source of the log (API, UI, SYSTEM)
            component: The component generating the log
            metadata: Additional contextual data (will be sanitized)
        """
        level = level.upper()
        if level not in LOG_LEVELS:
            level = "INFO"
            
        # Always log to console (subject to level filtering)
        self._log_to_console(level, message, source, component, metadata)
        
        # Log to database (subject to level filtering)
        self._create_log_entry(level, message, source, component, metadata)

    def debug(self, message: str, component: str, metadata: Optional[Dict] = None) -> None:
        """Log a DEBUG message."""
        self.log("DEBUG", message, "API", component, metadata)

    def info(self, message: str, component: str, metadata: Optional[Dict] = None) -> None:
        """Log an INFO message."""
        self.log("INFO", message, "API", component, metadata)

    def warning(self, message: str, component: str, metadata: Optional[Dict] = None) -> None:
        """Log a WARNING message."""
        self.log("WARNING", message, "API", component, metadata)

    def error(self, message: str, component: str, metadata: Optional[Dict] = None) -> None:
        """Log an ERROR message."""
        self.log("ERROR", message, "API", component, metadata)

    def critical(self, message: str, component: str, metadata: Optional[Dict] = None) -> None:
        """Log a CRITICAL message."""
        self.log("CRITICAL", message, "API", component, metadata)

    def record_metric(
        self,
        metric_type: str,
        component: str,
        value: float,
        unit: str,
        metadata: Optional[Dict] = None
    ) -> None:
        """
        Record a performance metric.
        
        Args:
            metric_type: Type of metric (e.g., RESPONSE_TIME, CPU_USAGE)
            component: Component being measured
            value: Numeric value of the metric
            unit: Unit of measurement (e.g., ms, %, MB)
            metadata: Additional contextual data
        """
        try:
            db = self._get_db_session()
            metric = PerformanceMetric(
                timestamp=datetime.now(),
                metric_type=metric_type,
                component=component,
                value=value,
                unit=unit,
                correlation_id=self.correlation_id,
                session_id=self.session_id
            )
            db.add(metric)
            db.commit()
            
            # Also log to console as a debug message
            self._log_to_console(
                "DEBUG",
                f"Metric recorded: {metric_type}={value}{unit}",
                "API",
                component,
                metadata
            )
        except Exception as e:
            std_logger.exception(f"Failed to record metric: {e}")

    def log_exception(
        self,
        exc: Exception,
        component: str,
        metadata: Optional[Dict] = None
    ) -> None:
        """
        Log an exception with detailed stack trace.
        
        Args:
            exc: The exception to log
            component: Component where the exception occurred
            metadata: Additional contextual data
        """
        import traceback
        
        # Generate stack trace
        stack_trace = traceback.format_exc()
        
        # Create metadata with exception details
        exc_metadata = metadata or {}
        exc_metadata.update({
            "exception_type": type(exc).__name__,
            "exception_message": str(exc),
            "stack_trace": stack_trace
        })
        
        # Log the exception
        self.log("ERROR", f"Exception: {str(exc)}", "API", component, exc_metadata)

    def track_time(self, component: str, operation: str = "function_execution"):
        """
        Decorator to track the execution time of a function.
        
        Args:
            component: Component being timed
            operation: Description of the operation being timed
        
        Returns:
            Decorated function that logs execution time
        """
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                result = func(*args, **kwargs)
                execution_time = (time.time() - start_time) * 1000  # Convert to ms
                
                # Record the metric
                self.record_metric(
                    metric_type="EXECUTION_TIME",
                    component=component,
                    value=execution_time,
                    unit="ms",
                    metadata={"operation": operation, "function": func.__name__}
                )
                
                return result
            return wrapper
        return decorator


# Create middleware for FastAPI
async def logging_middleware(request: Request, call_next):
    """
    Middleware to log API requests and responses.
    
    Args:
        request: The incoming request
        call_next: The next middleware in the chain
    
    Returns:
        The response from downstream middleware/route handlers
    """
    # Create a logger instance
    logger = Logger()
    
    # Extract correlation ID from headers if present
    correlation_id = request.headers.get("X-Correlation-ID")
    if correlation_id:
        logger.correlation_id = correlation_id
    
    # Extract session ID from headers or cookies if present
    session_id = request.headers.get("X-Session-ID") or request.cookies.get("session_id")
    if session_id:
        logger.session_id = session_id
    
    # Log the request
    start_time = time.time()
    request_id = str(uuid.uuid4())
    
    # Prepare request metadata
    metadata = {
        "request_id": request_id,
        "method": request.method,
        "url": str(request.url),
        "client_host": request.client.host if request.client else "unknown",
        "headers": dict(request.headers)
    }
    
    # Log the request (without body for security reasons unless explicitly enabled)
    logger.info(f"Request started: {request.method} {request.url.path}", "api.request", metadata)
    
    # Process the request and capture response
    try:
        response = await call_next(request)
        
        # Calculate execution time
        execution_time = (time.time() - start_time) * 1000  # Convert to ms
        
        # Record the metric
        logger.record_metric(
            metric_type="RESPONSE_TIME",
            component=f"api.endpoint.{request.url.path}",
            value=execution_time,
            unit="ms",
            metadata={"request_id": request_id, "status_code": response.status_code}
        )
        
        # Log the response
        response_metadata = {
            "request_id": request_id,
            "status_code": response.status_code,
            "execution_time_ms": execution_time
        }
        
        log_level = "INFO" if response.status_code < 400 else "ERROR"
        logger.log(
            level=log_level,
            message=f"Request completed: {request.method} {request.url.path} - {response.status_code}",
            source="API",
            component="api.response",
            metadata=response_metadata
        )
        
        # Add correlation ID to response headers
        response.headers["X-Correlation-ID"] = logger.correlation_id
        
        return response
    except Exception as exc:
        # Log any unhandled exceptions
        logger.log_exception(exc, "api.middleware")
        
        # Re-raise the exception to be handled by FastAPI
        raise 