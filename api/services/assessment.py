"""
Service layer for assessment operations.

This module contains the business logic for managing assessments and test executions.
It acts as an intermediary between the API routes and the database models.
"""
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

from api.models.assessment import Assessment, TestExecution
from api.models.schemas.assessment import (
    AssessmentCreate, AssessmentUpdate, 
    TestExecutionCreate, TestExecutionUpdate,
    AssessmentSummary, AssessmentReport
)


def get_assessments(
    db: Session, 
    skip: int = 0, 
    limit: int = 100, 
    status: Optional[str] = None,
    created_by: Optional[int] = None,
    search: Optional[str] = None,
    include_deleted: bool = False
) -> List[Assessment]:
    """
    Get a list of assessments with optional filtering.
    
    Args:
        db: Database session
        skip: Number of records to skip (for pagination)
        limit: Maximum number of records to return
        status: Filter by assessment status
        created_by: Filter by creator user ID
        search: Search term for name or description
        include_deleted: Whether to include soft-deleted assessments
        
    Returns:
        List of assessment objects
    """
    query = db.query(Assessment)
    
    # Apply filters
    if not include_deleted:
        query = query.filter(Assessment.deleted_at.is_(None))
    
    if status:
        query = query.filter(Assessment.status == status)
    
    if created_by:
        query = query.filter(Assessment.created_by == created_by)
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                Assessment.name.ilike(search_term),
                Assessment.description.ilike(search_term)
            )
        )
    
    # Apply pagination
    query = query.order_by(Assessment.created_on.desc()).offset(skip).limit(limit)
    
    return query.all()


def get_assessment_by_id(db: Session, assessment_id: int, include_deleted: bool = False) -> Optional[Assessment]:
    """
    Get an assessment by its ID.
    
    Args:
        db: Database session
        assessment_id: ID of the assessment to retrieve
        include_deleted: Whether to include soft-deleted assessments
        
    Returns:
        Assessment object if found, None otherwise
    """
    query = db.query(Assessment).filter(Assessment.id == assessment_id)
    
    if not include_deleted:
        query = query.filter(Assessment.deleted_at.is_(None))
    
    return query.first()


def create_assessment(db: Session, assessment_data: AssessmentCreate, user_id: int) -> Assessment:
    """
    Create a new assessment.
    
    Args:
        db: Database session
        assessment_data: Assessment data
        user_id: ID of the user creating the assessment
        
    Returns:
        Created assessment object
    """
    assessment = Assessment(
        name=assessment_data.name,
        description=assessment_data.description,
        target_system=assessment_data.target_system,
        assessment_type=assessment_data.assessment_type,
        status=assessment_data.status,
        start_date=assessment_data.start_date,
        end_date=assessment_data.end_date,
        environment_id=assessment_data.environment_id,
        created_by=user_id
    )
    
    db.add(assessment)
    db.commit()
    db.refresh(assessment)
    
    return assessment


def update_assessment(
    db: Session, 
    assessment_id: int, 
    assessment_data: AssessmentUpdate
) -> Optional[Assessment]:
    """
    Update an existing assessment.
    
    Args:
        db: Database session
        assessment_id: ID of the assessment to update
        assessment_data: Updated assessment data
        
    Returns:
        Updated assessment object if found, None otherwise
    """
    assessment = get_assessment_by_id(db, assessment_id)
    
    if not assessment:
        return None
    
    # Update fields if provided
    if assessment_data.name is not None:
        assessment.name = assessment_data.name
    
    if assessment_data.description is not None:
        assessment.description = assessment_data.description
    
    if assessment_data.target_system is not None:
        assessment.target_system = assessment_data.target_system
    
    if assessment_data.assessment_type is not None:
        assessment.assessment_type = assessment_data.assessment_type
    
    if assessment_data.status is not None:
        assessment.status = assessment_data.status
    
    if assessment_data.start_date is not None:
        assessment.start_date = assessment_data.start_date
    
    if assessment_data.end_date is not None:
        assessment.end_date = assessment_data.end_date
    
    if assessment_data.environment_id is not None:
        assessment.environment_id = assessment_data.environment_id
    
    assessment.updated_at = datetime.utcnow()
    
    db.commit()
    db.refresh(assessment)
    
    return assessment


def delete_assessment(db: Session, assessment_id: int) -> bool:
    """
    Soft-delete an assessment.
    
    Args:
        db: Database session
        assessment_id: ID of the assessment to delete
        
    Returns:
        True if the assessment was deleted, False otherwise
    """
    assessment = get_assessment_by_id(db, assessment_id)
    
    if not assessment:
        return False
    
    assessment.soft_delete(db)
    
    return True


def restore_assessment(db: Session, assessment_id: int) -> bool:
    """
    Restore a soft-deleted assessment.
    
    Args:
        db: Database session
        assessment_id: ID of the assessment to restore
        
    Returns:
        True if the assessment was restored, False otherwise
    """
    assessment = get_assessment_by_id(db, assessment_id, include_deleted=True)
    
    if not assessment or not assessment.deleted_at:
        return False
    
    assessment.deleted_at = None
    db.commit()
    
    return True


def get_test_executions(
    db: Session, 
    assessment_id: int, 
    skip: int = 0, 
    limit: int = 100,
    result: Optional[str] = None
) -> List[TestExecution]:
    """
    Get test executions for an assessment.
    
    Args:
        db: Database session
        assessment_id: ID of the assessment
        skip: Number of records to skip (for pagination)
        limit: Maximum number of records to return
        result: Filter by execution result
        
    Returns:
        List of test execution objects
    """
    query = db.query(TestExecution).filter(TestExecution.assessment_id == assessment_id)
    
    if result:
        query = query.filter(TestExecution.result == result)
    
    query = query.order_by(TestExecution.executed_at.desc()).offset(skip).limit(limit)
    
    return query.all()


def get_test_execution_by_id(db: Session, execution_id: int) -> Optional[TestExecution]:
    """
    Get a test execution by its ID.
    
    Args:
        db: Database session
        execution_id: ID of the test execution to retrieve
        
    Returns:
        Test execution object if found, None otherwise
    """
    return db.query(TestExecution).filter(TestExecution.id == execution_id).first()


def create_test_execution(
    db: Session, 
    execution_data: TestExecutionCreate, 
    user_id: int
) -> TestExecution:
    """
    Create a new test execution.
    
    Args:
        db: Database session
        execution_data: Test execution data
        user_id: ID of the user creating the execution
        
    Returns:
        Created test execution object
    """
    execution = TestExecution(
        test_case_id=execution_data.test_case_id,
        assessment_id=execution_data.assessment_id,
        result=execution_data.result,
        notes=execution_data.notes,
        evidence=execution_data.evidence,
        executed_by=user_id,
        executed_at=datetime.utcnow()
    )
    
    db.add(execution)
    db.commit()
    db.refresh(execution)
    
    return execution


def update_test_execution(
    db: Session, 
    execution_id: int, 
    execution_data: TestExecutionUpdate
) -> Optional[TestExecution]:
    """
    Update an existing test execution.
    
    Args:
        db: Database session
        execution_id: ID of the test execution to update
        execution_data: Updated test execution data
        
    Returns:
        Updated test execution object if found, None otherwise
    """
    execution = get_test_execution_by_id(db, execution_id)
    
    if not execution:
        return None
    
    # Update fields if provided
    if execution_data.result is not None:
        execution.result = execution_data.result
    
    if execution_data.notes is not None:
        execution.notes = execution_data.notes
    
    if execution_data.evidence is not None:
        execution.evidence = execution_data.evidence
    
    db.commit()
    db.refresh(execution)
    
    return execution


def delete_test_execution(db: Session, execution_id: int) -> bool:
    """
    Delete a test execution.
    
    Args:
        db: Database session
        execution_id: ID of the test execution to delete
        
    Returns:
        True if the test execution was deleted, False otherwise
    """
    execution = get_test_execution_by_id(db, execution_id)
    
    if not execution:
        return False
    
    db.delete(execution)
    db.commit()
    
    return True


def get_assessment_summary(db: Session, assessment_id: int) -> Optional[AssessmentSummary]:
    """
    Get a summary of test execution results for an assessment.
    
    Args:
        db: Database session
        assessment_id: ID of the assessment
        
    Returns:
        Assessment summary object if the assessment exists, None otherwise
    """
    assessment = get_assessment_by_id(db, assessment_id)
    
    if not assessment:
        return None
    
    # Count executions by result
    result_counts = (
        db.query(
            TestExecution.result,
            func.count(TestExecution.id).label('count')
        )
        .filter(TestExecution.assessment_id == assessment_id)
        .group_by(TestExecution.result)
        .all()
    )
    
    # Initialize counters
    total = 0
    passed = 0
    failed = 0
    partial = 0
    pending = 0
    blocked = 0
    not_applicable = 0
    
    # Process result counts
    for result, count in result_counts:
        total += count
        if result == 'pass':
            passed = count
        elif result == 'fail':
            failed = count
        elif result == 'partial':
            partial = count
        elif result == 'pending':
            pending = count
        elif result == 'blocked':
            blocked = count
        elif result == 'not_applicable':
            not_applicable = count
    
    # Calculate metrics
    completion_percentage = 0
    pass_rate = 0
    
    if total > 0:
        completion_percentage = ((total - pending) / total) * 100
        completed_tests = total - pending - not_applicable
        if completed_tests > 0:
            pass_rate = ((passed + (partial * 0.5)) / completed_tests) * 100
    
    return AssessmentSummary(
        total_tests=total,
        passed=passed,
        failed=failed,
        partial=partial,
        pending=pending,
        blocked=blocked,
        not_applicable=not_applicable,
        completion_percentage=completion_percentage,
        pass_rate=pass_rate
    )


def generate_assessment_report(db: Session, assessment_id: int) -> Optional[AssessmentReport]:
    """
    Generate a comprehensive report for an assessment.
    
    Args:
        db: Database session
        assessment_id: ID of the assessment
        
    Returns:
        Assessment report object if the assessment exists, None otherwise
    """
    assessment = get_assessment_by_id(db, assessment_id)
    
    if not assessment:
        return None
    
    # Get summary
    summary = get_assessment_summary(db, assessment_id)
    
    # Get all test executions
    executions = get_test_executions(db, assessment_id, limit=1000)
    
    # TODO: Add MITRE coverage analysis
    mitre_coverage = {}
    
    # Generate recommendations based on results
    recommendations = []
    
    if summary.failed > 0:
        recommendations.append("Address failed test cases to improve security posture")
    
    if summary.blocked > 0:
        recommendations.append("Resolve blocked test cases to complete assessment")
    
    if summary.pending > 0:
        recommendations.append("Complete pending test cases for a comprehensive assessment")
    
    return AssessmentReport(
        assessment=assessment,
        summary=summary,
        test_executions=executions,
        mitre_coverage=mitre_coverage,
        recommendations=recommendations
    ) 