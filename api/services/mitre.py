"""CRUD services for MITRE ATT&CK models."""
from api.services.crud import CRUDService
from api.models.mitre import MitreTechnique, MitreTactic, MitreGroup, MitreSoftware, MitreMitigation
from sqlalchemy.orm import Session
from sqlalchemy import select
from typing import List, Optional, Dict, Any

class MitreTechniqueService(CRUDService[MitreTechnique]):
    """CRUD service for MITRE ATT&CK techniques."""
    
    def get_by_technique_id(
        self, 
        db: Session, 
        technique_id: str,
        skip_deleted: bool = True
    ) -> Optional[MitreTechnique]:
        """Get technique by MITRE technique ID."""
        query = select(self.model).where(self.model.technique_id == technique_id)
        if skip_deleted:
            query = query.where(self.model.deleted_at.is_(None))
        return db.execute(query).scalar_one_or_none()

    def get_by_tactic(
        self, 
        db: Session, 
        tactic_id: int,
        skip_deleted: bool = True
    ) -> List[MitreTechnique]:
        """Get all techniques associated with a tactic."""
        query = (
            select(self.model)
            .join(self.model.tactics)
            .where(MitreTactic.id == tactic_id)
        )
        if skip_deleted:
            query = query.where(self.model.deleted_at.is_(None))
        return db.execute(query).scalars().all()

    def create_with_relationships(
        self,
        db: Session,
        data: Dict[str, Any],
        tactic_ids: List[int] = None,
        group_ids: List[int] = None,
        software_ids: List[int] = None,
        mitigation_ids: List[int] = None
    ) -> MitreTechnique:
        """Create a technique with its relationships."""
        # Create the technique
        technique = super().create(db, data)

        # Add relationships if provided
        if tactic_ids:
            tactics = db.query(MitreTactic).filter(MitreTactic.id.in_(tactic_ids)).all()
            technique.tactics.extend(tactics)

        if group_ids:
            groups = db.query(MitreGroup).filter(MitreGroup.id.in_(group_ids)).all()
            technique.groups.extend(groups)

        if software_ids:
            software = db.query(MitreSoftware).filter(MitreSoftware.id.in_(software_ids)).all()
            technique.software.extend(software)

        if mitigation_ids:
            mitigations = db.query(MitreMitigation).filter(MitreMitigation.id.in_(mitigation_ids)).all()
            technique.mitigations.extend(mitigations)

        db.commit()
        db.refresh(technique)
        return technique

# Create service instances
technique_service = MitreTechniqueService(MitreTechnique)
