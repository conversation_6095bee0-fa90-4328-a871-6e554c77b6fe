"""
Service layer for test case management.

This module provides functions for managing test cases, including CRUD operations
and business logic.
"""
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple

from fastapi import HTTPException
from sqlalchemy import or_, and_, func
from sqlalchemy.orm import Session

from api.models.database.test_case import TestCase
from api.models.schemas.test_case import (
    TestCaseCreate, TestCaseUpdate, TestCaseFilter,
    TestCaseStatus, TestCaseType, TestCasePriority, TestCaseComplexity
)


def get_test_cases(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    filter_params: Optional[TestCaseFilter] = None,
    include_deleted: bool = False,
    user_id: Optional[int] = None,
    is_admin: bool = False
) -> List[TestCase]:
    """
    Get a list of test cases with optional filtering.
    
    Args:
        db: Database session
        skip: Number of records to skip (for pagination)
        limit: Maximum number of records to return
        filter_params: Filter parameters
        include_deleted: Whether to include soft-deleted test cases
        user_id: ID of the current user
        is_admin: Whether the current user is an admin
        
    Returns:
        List of test cases
    """
    query = db.query(TestCase)
    
    # Apply filters
    if filter_params:
        if filter_params.status:
            query = query.filter(TestCase.status == filter_params.status)
        
        if filter_params.type:
            query = query.filter(TestCase.type == filter_params.type)
        
        if filter_params.priority:
            query = query.filter(TestCase.priority == filter_params.priority)
        
        if filter_params.complexity:
            query = query.filter(TestCase.complexity == filter_params.complexity)
        
        if filter_params.tags:
            for tag in filter_params.tags:
                query = query.filter(TestCase.tags.contains([tag]))
        
        if filter_params.mitre_techniques:
            for technique in filter_params.mitre_techniques:
                query = query.filter(TestCase.mitre_techniques.contains([technique]))
        
        if filter_params.created_by:
            query = query.filter(TestCase.created_by == filter_params.created_by)
        
        if filter_params.search:
            search_term = f"%{filter_params.search}%"
            query = query.filter(
                or_(
                    TestCase.name.ilike(search_term),
                    TestCase.description.ilike(search_term)
                )
            )
    
    # Filter by user permissions
    if not is_admin and user_id:
        query = query.filter(TestCase.created_by == user_id)
    
    # Filter deleted records
    if not include_deleted:
        query = query.filter(TestCase.not_deleted())
    
    # Apply pagination
    return query.order_by(TestCase.id).offset(skip).limit(limit).all()


def get_test_case_by_id(
    db: Session,
    test_case_id: int,
    include_deleted: bool = False
) -> Optional[TestCase]:
    """
    Get a test case by ID.
    
    Args:
        db: Database session
        test_case_id: ID of the test case to retrieve
        include_deleted: Whether to include soft-deleted test cases
        
    Returns:
        Test case if found, None otherwise
    """
    query = db.query(TestCase).filter(TestCase.id == test_case_id)
    
    if not include_deleted:
        query = query.filter(TestCase.not_deleted())
    
    return query.first()


def create_test_case(
    db: Session,
    test_case_data: TestCaseCreate,
    user_id: int
) -> TestCase:
    """
    Create a new test case.
    
    Args:
        db: Database session
        test_case_data: Data for the new test case
        user_id: ID of the user creating the test case
        
    Returns:
        Created test case
    """
    # Convert steps, tags, and mitre_techniques to JSON
    steps = test_case_data.steps if test_case_data.steps else []
    tags = test_case_data.tags if test_case_data.tags else []
    mitre_techniques = test_case_data.mitre_techniques if test_case_data.mitre_techniques else []
    
    # Create the test case
    db_test_case = TestCase(
        name=test_case_data.name,
        description=test_case_data.description,
        type=test_case_data.type,
        status=test_case_data.status,
        priority=test_case_data.priority,
        complexity=test_case_data.complexity,
        prerequisites=test_case_data.prerequisites,
        steps=steps,
        expected_result=test_case_data.expected_result,
        tags=tags,
        mitre_techniques=mitre_techniques,
        created_by=user_id,
        version="1.0.0"
    )
    
    db.add(db_test_case)
    db.commit()
    db.refresh(db_test_case)
    
    return db_test_case


def bulk_create_test_cases(
    db: Session,
    test_cases_data: List[TestCaseCreate],
    user_id: int
) -> Tuple[List[TestCase], List[Dict[str, Any]]]:
    """
    Create multiple test cases in bulk.
    
    Args:
        db: Database session
        test_cases_data: List of test case data
        user_id: ID of the user creating the test cases
        
    Returns:
        Tuple of (created test cases, failed entries)
    """
    created_test_cases = []
    failed_entries = []
    
    for index, test_case_data in enumerate(test_cases_data):
        try:
            test_case = create_test_case(db, test_case_data, user_id)
            created_test_cases.append(test_case)
        except Exception as e:
            failed_entries.append({
                "index": index,
                "data": test_case_data.dict(),
                "error": str(e)
            })
    
    return created_test_cases, failed_entries


def update_test_case(
    db: Session,
    test_case_id: int,
    test_case_data: TestCaseUpdate,
    user_id: int,
    is_admin: bool = False
) -> Optional[TestCase]:
    """
    Update an existing test case.
    
    Args:
        db: Database session
        test_case_id: ID of the test case to update
        test_case_data: Updated data for the test case
        user_id: ID of the user updating the test case
        is_admin: Whether the current user is an admin
        
    Returns:
        Updated test case if successful, None otherwise
    """
    db_test_case = get_test_case_by_id(db, test_case_id)
    
    if not db_test_case:
        return None
    
    # Check permissions
    if not is_admin and db_test_case.created_by != user_id:
        raise HTTPException(status_code=403, detail="Not authorized to update this test case")
    
    # Update fields if provided
    update_data = test_case_data.dict(exclude_unset=True)
    
    for key, value in update_data.items():
        if key in ["steps", "tags", "mitre_techniques"] and value is not None:
            # Convert lists to JSON
            setattr(db_test_case, key, value)
        elif value is not None:
            setattr(db_test_case, key, value)
    
    # Increment version
    if db_test_case.version:
        major, minor, patch = map(int, db_test_case.version.split('.'))
        patch += 1
        db_test_case.version = f"{major}.{minor}.{patch}"
    else:
        db_test_case.version = "1.0.0"
    
    db_test_case.updated_at = datetime.utcnow()
    
    db.add(db_test_case)
    db.commit()
    db.refresh(db_test_case)
    
    return db_test_case


def delete_test_case(
    db: Session,
    test_case_id: int,
    user_id: int,
    is_admin: bool = False
) -> bool:
    """
    Soft-delete a test case.
    
    Args:
        db: Database session
        test_case_id: ID of the test case to delete
        user_id: ID of the user deleting the test case
        is_admin: Whether the current user is an admin
        
    Returns:
        True if successful, False otherwise
    """
    db_test_case = get_test_case_by_id(db, test_case_id)
    
    if not db_test_case:
        return False
    
    # Check permissions
    if not is_admin and db_test_case.created_by != user_id:
        raise HTTPException(status_code=403, detail="Not authorized to delete this test case")
    
    db_test_case.soft_delete(db)
    
    return True


def restore_test_case(
    db: Session,
    test_case_id: int,
    user_id: int,
    is_admin: bool = False
) -> Optional[TestCase]:
    """
    Restore a soft-deleted test case.
    
    Args:
        db: Database session
        test_case_id: ID of the test case to restore
        user_id: ID of the user restoring the test case
        is_admin: Whether the current user is an admin
        
    Returns:
        Restored test case if successful, None otherwise
    """
    db_test_case = get_test_case_by_id(db, test_case_id, include_deleted=True)
    
    if not db_test_case or not db_test_case.deleted_at:
        return None
    
    # Check permissions
    if not is_admin:
        raise HTTPException(status_code=403, detail="Only admins can restore test cases")
    
    db_test_case.restore(db)
    
    return db_test_case


def deprecate_test_case(
    db: Session,
    test_case_id: int,
    user_id: int,
    is_admin: bool = False
) -> Optional[TestCase]:
    """
    Mark a test case as deprecated.
    
    Args:
        db: Database session
        test_case_id: ID of the test case to deprecate
        user_id: ID of the user deprecating the test case
        is_admin: Whether the current user is an admin
        
    Returns:
        Deprecated test case if successful, None otherwise
    """
    db_test_case = get_test_case_by_id(db, test_case_id)
    
    if not db_test_case:
        return None
    
    # Check permissions
    if not is_admin and db_test_case.created_by != user_id:
        raise HTTPException(status_code=403, detail="Not authorized to deprecate this test case")
    
    db_test_case.deprecate(db)
    db_test_case.status = TestCaseStatus.DEPRECATED.value
    
    db.add(db_test_case)
    db.commit()
    db.refresh(db_test_case)
    
    return db_test_case


def revoke_test_case(
    db: Session,
    test_case_id: int,
    user_id: int,
    is_admin: bool = False
) -> Optional[TestCase]:
    """
    Revoke a test case.
    
    Args:
        db: Database session
        test_case_id: ID of the test case to revoke
        user_id: ID of the user revoking the test case
        is_admin: Whether the current user is an admin
        
    Returns:
        Revoked test case if successful, None otherwise
    """
    db_test_case = get_test_case_by_id(db, test_case_id)
    
    if not db_test_case:
        return None
    
    # Check permissions
    if not is_admin:
        raise HTTPException(status_code=403, detail="Only admins can revoke test cases")
    
    db_test_case.revoke(db, user_id)
    
    return db_test_case


def get_test_case_stats(
    db: Session,
    user_id: Optional[int] = None,
    is_admin: bool = False
) -> Dict[str, Any]:
    """
    Get statistics about test cases.
    
    Args:
        db: Database session
        user_id: ID of the current user
        is_admin: Whether the current user is an admin
        
    Returns:
        Dictionary with test case statistics
    """
    # Base query
    query = db.query(TestCase).filter(TestCase.not_deleted())
    
    # Filter by user permissions
    if not is_admin and user_id:
        query = query.filter(TestCase.created_by == user_id)
    
    # Total count
    total = query.count()
    
    # Count by status
    status_counts = {
        status.value: query.filter(TestCase.status == status.value).count()
        for status in TestCaseStatus
    }
    
    # Count by type
    type_counts = {
        type_.value: query.filter(TestCase.type == type_.value).count()
        for type_ in TestCaseType
    }
    
    # Count by priority
    priority_counts = {
        priority.value: query.filter(TestCase.priority == priority.value).count()
        for priority in TestCasePriority
    }
    
    # Count by complexity
    complexity_counts = {
        complexity.value: query.filter(TestCase.complexity == complexity.value).count()
        for complexity in TestCaseComplexity
    }
    
    return {
        "total": total,
        "by_status": status_counts,
        "by_type": type_counts,
        "by_priority": priority_counts,
        "by_complexity": complexity_counts
    } 