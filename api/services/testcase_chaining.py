"""
Service layer for the Enhanced Testcase Chaining & Sequencing feature.

This module contains the business logic for managing testcase chains, nodes,
edges, executions, and conditions.
"""
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, not_

from api.models.testcase_chaining import (
    TestcaseChainDB, TestcaseChainNodeDB, TestcaseChainEdgeDB,
    ChainExecutionDB, NodeExecutionDB, TestcaseConditionDB
)
from api.models.base import TestCaseDB
from api.models.schemas.testcase_chaining import (
    TestcaseChainCreate, TestcaseChainNodeCreate, TestcaseChainEdgeCreate,
    TestcaseConditionCreate, ChainExecutionCreate, NodeExecutionCreate
)


class TestcaseChainService:
    """Service for managing testcase chains."""

    @staticmethod
    def create_chain(db: Session, chain_data: TestcaseChainCreate) -> TestcaseChainDB:
        """Create a new testcase chain."""
        chain = TestcaseChainDB(**chain_data.model_dump())
        db.add(chain)
        db.commit()
        db.refresh(chain)
        return chain

    @staticmethod
    def get_chain(db: Session, chain_id: int) -> Optional[TestcaseChainDB]:
        """Get a testcase chain by ID."""
        return db.query(TestcaseChainDB).filter(
            and_(
                TestcaseChainDB.id == chain_id,
                TestcaseChainDB.deleted_time.is_(None)
            )
        ).first()

    @staticmethod
    def get_chains(db: Session, skip: int = 0, limit: int = 100) -> List[TestcaseChainDB]:
        """Get all testcase chains."""
        return db.query(TestcaseChainDB).filter(
            TestcaseChainDB.deleted_time.is_(None)
        ).offset(skip).limit(limit).all()

    @staticmethod
    def update_chain(db: Session, chain_id: int, chain_data: Dict[str, Any]) -> Optional[TestcaseChainDB]:
        """Update a testcase chain."""
        chain = TestcaseChainService.get_chain(db, chain_id)
        if not chain:
            return None
        
        for key, value in chain_data.items():
            setattr(chain, key, value)
        
        db.commit()
        db.refresh(chain)
        return chain

    @staticmethod
    def delete_chain(db: Session, chain_id: int) -> bool:
        """Delete a testcase chain."""
        chain = TestcaseChainService.get_chain(db, chain_id)
        if not chain:
            return False
        
        chain.soft_delete(db)
        return True


class TestcaseChainNodeService:
    """Service for managing testcase chain nodes."""

    @staticmethod
    def create_node(db: Session, node_data: TestcaseChainNodeCreate) -> TestcaseChainNodeDB:
        """Create a new testcase chain node."""
        node = TestcaseChainNodeDB(**node_data.model_dump())
        db.add(node)
        db.commit()
        db.refresh(node)
        return node

    @staticmethod
    def get_node(db: Session, node_id: int) -> Optional[TestcaseChainNodeDB]:
        """Get a testcase chain node by ID."""
        return db.query(TestcaseChainNodeDB).filter(
            and_(
                TestcaseChainNodeDB.id == node_id,
                TestcaseChainNodeDB.deleted_time.is_(None)
            )
        ).first()

    @staticmethod
    def get_nodes_by_chain(db: Session, chain_id: int) -> List[TestcaseChainNodeDB]:
        """Get all nodes for a specific chain."""
        return db.query(TestcaseChainNodeDB).filter(
            and_(
                TestcaseChainNodeDB.chain_id == chain_id,
                TestcaseChainNodeDB.deleted_time.is_(None)
            )
        ).all()

    @staticmethod
    def update_node(db: Session, node_id: int, node_data: Dict[str, Any]) -> Optional[TestcaseChainNodeDB]:
        """Update a testcase chain node."""
        node = TestcaseChainNodeService.get_node(db, node_id)
        if not node:
            return None
        
        for key, value in node_data.items():
            setattr(node, key, value)
        
        db.commit()
        db.refresh(node)
        return node

    @staticmethod
    def delete_node(db: Session, node_id: int) -> bool:
        """Delete a testcase chain node."""
        node = TestcaseChainNodeService.get_node(db, node_id)
        if not node:
            return False
        
        node.soft_delete(db)
        return True


class TestcaseChainEdgeService:
    """Service for managing testcase chain edges."""

    @staticmethod
    def create_edge(db: Session, edge_data: TestcaseChainEdgeCreate) -> TestcaseChainEdgeDB:
        """Create a new testcase chain edge."""
        edge = TestcaseChainEdgeDB(**edge_data.model_dump())
        db.add(edge)
        db.commit()
        db.refresh(edge)
        return edge

    @staticmethod
    def get_edge(db: Session, edge_id: int) -> Optional[TestcaseChainEdgeDB]:
        """Get a testcase chain edge by ID."""
        return db.query(TestcaseChainEdgeDB).filter(
            and_(
                TestcaseChainEdgeDB.id == edge_id,
                TestcaseChainEdgeDB.deleted_time.is_(None)
            )
        ).first()

    @staticmethod
    def get_edges_by_node(db: Session, node_id: int, direction: str = "both") -> List[TestcaseChainEdgeDB]:
        """Get all edges connected to a specific node.
        
        Args:
            db: Database session
            node_id: ID of the node
            direction: 'outgoing', 'incoming', or 'both'
        """
        if direction == "outgoing":
            return db.query(TestcaseChainEdgeDB).filter(
                and_(
                    TestcaseChainEdgeDB.source_node_id == node_id,
                    TestcaseChainEdgeDB.deleted_time.is_(None)
                )
            ).all()
        elif direction == "incoming":
            return db.query(TestcaseChainEdgeDB).filter(
                and_(
                    TestcaseChainEdgeDB.target_node_id == node_id,
                    TestcaseChainEdgeDB.deleted_time.is_(None)
                )
            ).all()
        else:  # both
            return db.query(TestcaseChainEdgeDB).filter(
                and_(
                    or_(
                        TestcaseChainEdgeDB.source_node_id == node_id,
                        TestcaseChainEdgeDB.target_node_id == node_id
                    ),
                    TestcaseChainEdgeDB.deleted_time.is_(None)
                )
            ).all()

    @staticmethod
    def update_edge(db: Session, edge_id: int, edge_data: Dict[str, Any]) -> Optional[TestcaseChainEdgeDB]:
        """Update a testcase chain edge."""
        edge = TestcaseChainEdgeService.get_edge(db, edge_id)
        if not edge:
            return None
        
        for key, value in edge_data.items():
            setattr(edge, key, value)
        
        db.commit()
        db.refresh(edge)
        return edge

    @staticmethod
    def delete_edge(db: Session, edge_id: int) -> bool:
        """Delete a testcase chain edge."""
        edge = TestcaseChainEdgeService.get_edge(db, edge_id)
        if not edge:
            return False
        
        edge.soft_delete(db)
        return True


class TestcaseConditionService:
    """Service for managing testcase conditions."""

    @staticmethod
    def create_condition(db: Session, condition_data: TestcaseConditionCreate) -> TestcaseConditionDB:
        """Create a new testcase condition."""
        condition = TestcaseConditionDB(**condition_data.model_dump())
        db.add(condition)
        db.commit()
        db.refresh(condition)
        return condition

    @staticmethod
    def get_condition(db: Session, condition_id: int) -> Optional[TestcaseConditionDB]:
        """Get a testcase condition by ID."""
        return db.query(TestcaseConditionDB).filter(
            and_(
                TestcaseConditionDB.id == condition_id,
                TestcaseConditionDB.deleted_time.is_(None)
            )
        ).first()

    @staticmethod
    def get_conditions_by_testcase(
        db: Session, testcase_id: int, condition_type: Optional[str] = None
    ) -> List[TestcaseConditionDB]:
        """Get all conditions for a specific testcase."""
        query = db.query(TestcaseConditionDB).filter(
            and_(
                TestcaseConditionDB.testcase_id == testcase_id,
                TestcaseConditionDB.deleted_time.is_(None)
            )
        )
        
        if condition_type:
            query = query.filter(TestcaseConditionDB.condition_type == condition_type)
            
        return query.all()

    @staticmethod
    def update_condition(
        db: Session, condition_id: int, condition_data: Dict[str, Any]
    ) -> Optional[TestcaseConditionDB]:
        """Update a testcase condition."""
        condition = TestcaseConditionService.get_condition(db, condition_id)
        if not condition:
            return None
        
        for key, value in condition_data.items():
            setattr(condition, key, value)
        
        db.commit()
        db.refresh(condition)
        return condition

    @staticmethod
    def delete_condition(db: Session, condition_id: int) -> bool:
        """Delete a testcase condition."""
        condition = TestcaseConditionService.get_condition(db, condition_id)
        if not condition:
            return False
        
        condition.soft_delete(db)
        return True


class ChainExecutionService:
    """Service for managing chain executions."""

    @staticmethod
    def create_execution(db: Session, execution_data: ChainExecutionCreate) -> ChainExecutionDB:
        """Create a new chain execution."""
        execution = ChainExecutionDB(**execution_data.model_dump())
        db.add(execution)
        db.commit()
        db.refresh(execution)
        return execution

    @staticmethod
    def get_execution(db: Session, execution_id: int) -> Optional[ChainExecutionDB]:
        """Get a chain execution by ID."""
        return db.query(ChainExecutionDB).filter(
            and_(
                ChainExecutionDB.id == execution_id,
                ChainExecutionDB.deleted_time.is_(None)
            )
        ).first()

    @staticmethod
    def get_executions_by_chain(db: Session, chain_id: int) -> List[ChainExecutionDB]:
        """Get all executions for a specific chain."""
        return db.query(ChainExecutionDB).filter(
            and_(
                ChainExecutionDB.chain_id == chain_id,
                ChainExecutionDB.deleted_time.is_(None)
            )
        ).all()

    @staticmethod
    def update_execution(
        db: Session, execution_id: int, execution_data: Dict[str, Any]
    ) -> Optional[ChainExecutionDB]:
        """Update a chain execution."""
        execution = ChainExecutionService.get_execution(db, execution_id)
        if not execution:
            return None
        
        for key, value in execution_data.items():
            setattr(execution, key, value)
        
        db.commit()
        db.refresh(execution)
        return execution

    @staticmethod
    def complete_execution(db: Session, execution_id: int, status: str) -> Optional[ChainExecutionDB]:
        """Mark a chain execution as complete."""
        execution = ChainExecutionService.get_execution(db, execution_id)
        if not execution:
            return None
        
        execution.status = status
        execution.end_time = datetime.now()
        
        db.commit()
        db.refresh(execution)
        return execution


class NodeExecutionService:
    """Service for managing node executions."""

    @staticmethod
    def create_execution(db: Session, execution_data: NodeExecutionCreate) -> NodeExecutionDB:
        """Create a new node execution."""
        execution = NodeExecutionDB(**execution_data.model_dump())
        db.add(execution)
        db.commit()
        db.refresh(execution)
        return execution

    @staticmethod
    def get_execution(db: Session, execution_id: int) -> Optional[NodeExecutionDB]:
        """Get a node execution by ID."""
        return db.query(NodeExecutionDB).filter(
            and_(
                NodeExecutionDB.id == execution_id,
                NodeExecutionDB.deleted_time.is_(None)
            )
        ).first()

    @staticmethod
    def get_executions_by_chain_execution(
        db: Session, chain_execution_id: int
    ) -> List[NodeExecutionDB]:
        """Get all node executions for a specific chain execution."""
        return db.query(NodeExecutionDB).filter(
            and_(
                NodeExecutionDB.chain_execution_id == chain_execution_id,
                NodeExecutionDB.deleted_time.is_(None)
            )
        ).all()

    @staticmethod
    def update_execution(
        db: Session, execution_id: int, execution_data: Dict[str, Any]
    ) -> Optional[NodeExecutionDB]:
        """Update a node execution."""
        execution = NodeExecutionService.get_execution(db, execution_id)
        if not execution:
            return None
        
        for key, value in execution_data.items():
            setattr(execution, key, value)
        
        db.commit()
        db.refresh(execution)
        return execution

    @staticmethod
    def start_execution(db: Session, execution_id: int) -> Optional[NodeExecutionDB]:
        """Mark a node execution as started."""
        execution = NodeExecutionService.get_execution(db, execution_id)
        if not execution:
            return None
        
        execution.status = "running"
        execution.start_time = datetime.now()
        
        db.commit()
        db.refresh(execution)
        return execution

    @staticmethod
    def complete_execution(
        db: Session, execution_id: int, status: str, result_data: Optional[Dict[str, Any]] = None
    ) -> Optional[NodeExecutionDB]:
        """Mark a node execution as complete."""
        execution = NodeExecutionService.get_execution(db, execution_id)
        if not execution:
            return None
        
        execution.status = status
        execution.end_time = datetime.now()
        if result_data:
            execution.result_data = result_data
        
        db.commit()
        db.refresh(execution)
        return execution


class ChainExecutionEngine:
    """Engine for executing testcase chains."""

    @staticmethod
    def initialize_chain_execution(
        db: Session, chain_id: int, user_id: int
    ) -> Optional[ChainExecutionDB]:
        """Initialize a new chain execution."""
        # Check if chain exists
        chain = TestcaseChainService.get_chain(db, chain_id)
        if not chain:
            return None
        
        # Create chain execution
        chain_execution = ChainExecutionService.create_execution(
            db,
            ChainExecutionCreate(
                chain_id=chain_id,
                started_by=user_id,
                status="running"
            )
        )
        
        # Get all nodes in the chain
        nodes = TestcaseChainNodeService.get_nodes_by_chain(db, chain_id)
        
        # Create node executions for each node
        for node in nodes:
            NodeExecutionService.create_execution(
                db,
                NodeExecutionCreate(
                    chain_execution_id=chain_execution.id,
                    node_id=node.id,
                    status="pending"
                )
            )
        
        return chain_execution

    @staticmethod
    def get_next_nodes_to_execute(
        db: Session, chain_execution_id: int
    ) -> List[Tuple[NodeExecutionDB, TestcaseChainNodeDB]]:
        """Get the next nodes to execute in a chain execution."""
        # Get chain execution
        chain_execution = ChainExecutionService.get_execution(db, chain_execution_id)
        if not chain_execution or chain_execution.status != "running":
            return []
        
        # Get all node executions for this chain execution
        node_executions = NodeExecutionService.get_executions_by_chain_execution(
            db, chain_execution_id
        )
        
        # Find nodes that are ready to execute
        ready_nodes = []
        for node_execution in node_executions:
            if node_execution.status != "pending":
                continue
            
            # Get the node
            node = TestcaseChainNodeService.get_node(db, node_execution.node_id)
            if not node:
                continue
            
            # Check if this node has any incoming edges
            incoming_edges = TestcaseChainEdgeService.get_edges_by_node(
                db, node.id, direction="incoming"
            )
            
            # If no incoming edges, this node is ready to execute
            if not incoming_edges:
                ready_nodes.append((node_execution, node))
                continue
            
            # Check if all source nodes have completed successfully
            all_sources_completed = True
            for edge in incoming_edges:
                # Find the execution for the source node
                source_node_execution = next(
                    (ne for ne in node_executions if ne.node_id == edge.source_node_id),
                    None
                )
                
                # If source node execution doesn't exist or isn't completed, this node isn't ready
                if not source_node_execution or source_node_execution.status != "completed":
                    all_sources_completed = False
                    break
                
                # If edge has a condition, check if it's satisfied
                if edge.condition:
                    # TODO: Implement condition evaluation
                    pass
            
            if all_sources_completed:
                ready_nodes.append((node_execution, node))
        
        return ready_nodes

    @staticmethod
    def execute_node(
        db: Session, node_execution_id: int
    ) -> Optional[NodeExecutionDB]:
        """Execute a node in a chain execution."""
        # Get node execution
        node_execution = NodeExecutionService.get_execution(db, node_execution_id)
        if not node_execution or node_execution.status != "pending":
            return None
        
        # Mark node execution as running
        node_execution = NodeExecutionService.start_execution(db, node_execution_id)
        
        # Get the node
        node = TestcaseChainNodeService.get_node(db, node_execution.node_id)
        if not node:
            NodeExecutionService.complete_execution(db, node_execution_id, "failed")
            return node_execution
        
        # Get the testcase
        testcase = db.query(TestCaseDB).filter(
            and_(
                TestCaseDB.id == node.testcase_id,
                TestCaseDB.deleted_time.is_(None)
            )
        ).first()
        if not testcase:
            NodeExecutionService.complete_execution(db, node_execution_id, "failed")
            return node_execution
        
        # Check preconditions
        preconditions = TestcaseConditionService.get_conditions_by_testcase(
            db, testcase.id, condition_type="precondition"
        )
        for precondition in preconditions:
            if precondition.required:
                # TODO: Validate precondition
                pass
        
        # Execute the testcase
        # TODO: Implement testcase execution
        
        # Check postconditions
        postconditions = TestcaseConditionService.get_conditions_by_testcase(
            db, testcase.id, condition_type="postcondition"
        )
        for postcondition in postconditions:
            if postcondition.required:
                # TODO: Validate postcondition
                pass
        
        # Mark node execution as completed
        node_execution = NodeExecutionService.complete_execution(
            db, node_execution_id, "completed", {"result": "success"}
        )
        
        return node_execution

    @staticmethod
    def process_chain_execution(
        db: Session, chain_execution_id: int
    ) -> Optional[ChainExecutionDB]:
        """Process a chain execution, executing nodes as they become ready."""
        # Get chain execution
        chain_execution = ChainExecutionService.get_execution(db, chain_execution_id)
        if not chain_execution or chain_execution.status != "running":
            return chain_execution
        
        # Get next nodes to execute
        next_nodes = ChainExecutionEngine.get_next_nodes_to_execute(db, chain_execution_id)
        
        # If no more nodes to execute, check if all nodes are completed
        if not next_nodes:
            node_executions = NodeExecutionService.get_executions_by_chain_execution(
                db, chain_execution_id
            )
            
            all_completed = all(ne.status in ["completed", "skipped"] for ne in node_executions)
            any_failed = any(ne.status == "failed" for ne in node_executions)
            
            if all_completed:
                status = "failed" if any_failed else "completed"
                chain_execution = ChainExecutionService.complete_execution(
                    db, chain_execution_id, status
                )
            
            return chain_execution
        
        # Execute each ready node
        for node_execution, _ in next_nodes:
            ChainExecutionEngine.execute_node(db, node_execution.id)
        
        # Recursively process the chain execution again
        return ChainExecutionEngine.process_chain_execution(db, chain_execution_id) 