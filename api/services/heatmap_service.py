"""
Heat map service for MITRE ATT&CK visualization and analysis.

This module provides business logic for generating, analyzing, and managing
MITRE ATT&CK heat maps. Follows service layer pattern with comprehensive
error handling and validation.
"""

from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
import logging

from api.models import (
    AttackTechniqueResult,
    HeatMapSnapshot,
    Campaign,
    Assessment,
    MitreTechnique,
    MitreTactic,
    TestCase
)
from api.schemas.heatmap import (
    HeatMapResponse,
    HeatMapType,
    TechniqueHeatMapData,
    TacticHeatMapData,
    HeatMapMetrics,
    TechniqueStatus,
    HeatMapSnapshotCreate,
    HeatMapSnapshotResponse
)

# Configure logging
logger = logging.getLogger(__name__)


class HeatMapService:
    """Service class for heat map operations."""
    
    def __init__(self, db: Session) -> None:
        """
        Initialize heat map service.
        
        Args:
            db: Database session
        """
        self.db = db
    
    async def generate_campaign_heatmap(
        self,
        campaign_id: int,
        save_snapshot: bool = False,
        snapshot_name: Optional[str] = None
    ) -> HeatMapResponse:
        """
        Generate heat map for a specific campaign.
        
        Args:
            campaign_id: Campaign ID
            save_snapshot: Whether to save as snapshot
            snapshot_name: Snapshot name (required if saving)
            
        Returns:
            Generated heat map data
            
        Raises:
            ValueError: If validation fails
        """
        logger.info(f"Generating heat map for campaign {campaign_id}")
        
        # Get campaign
        campaign = self.db.query(Campaign).filter(
            Campaign.id == campaign_id
        ).first()
        
        if not campaign:
            raise ValueError(f"Campaign {campaign_id} not found")
        
        # Get technique results for campaign
        technique_results = self.db.query(AttackTechniqueResult).filter(
            AttackTechniqueResult.campaign_id == campaign_id,
            AttackTechniqueResult.deleted_at.is_(None)
        ).all()
        
        # Generate heat map data
        techniques_data = await self._generate_technique_data(technique_results)
        tactics_data = await self._generate_tactic_data(techniques_data)
        metrics = await self._calculate_metrics(techniques_data)
        
        # Create heat map response
        heatmap_response = HeatMapResponse(
            name=f"Campaign {campaign.name} Heat Map",
            description=f"Heat map for campaign: {campaign.description or campaign.name}",
            heat_map_type=HeatMapType.CAMPAIGN,
            campaign_id=campaign_id,
            assessment_id=None,
            techniques=techniques_data,
            tactics=tactics_data,
            metrics=metrics,
            generated_at=datetime.now(timezone.utc)
        )
        
        # Save snapshot if requested
        if save_snapshot and snapshot_name:
            snapshot = await self._save_snapshot(
                heatmap_response=heatmap_response,
                snapshot_name=snapshot_name,
                campaign_id=campaign_id
            )
            heatmap_response.id = snapshot.id
        
        logger.info(f"Generated heat map for campaign {campaign_id}")
        return heatmap_response
    
    async def generate_assessment_heatmap(
        self,
        assessment_id: int,
        save_snapshot: bool = False,
        snapshot_name: Optional[str] = None
    ) -> HeatMapResponse:
        """
        Generate heat map for a specific assessment.
        
        Args:
            assessment_id: Assessment ID
            save_snapshot: Whether to save as snapshot
            snapshot_name: Snapshot name (required if saving)
            
        Returns:
            Generated heat map data
            
        Raises:
            ValueError: If validation fails
        """
        logger.info(f"Generating heat map for assessment {assessment_id}")
        
        # Get assessment
        assessment = self.db.query(Assessment).filter(
            Assessment.id == assessment_id
        ).first()
        
        if not assessment:
            raise ValueError(f"Assessment {assessment_id} not found")
        
        # Get technique results for assessment
        technique_results = self.db.query(AttackTechniqueResult).filter(
            AttackTechniqueResult.assessment_id == assessment_id,
            AttackTechniqueResult.deleted_at.is_(None)
        ).all()
        
        # Generate heat map data
        techniques_data = await self._generate_technique_data(technique_results)
        tactics_data = await self._generate_tactic_data(techniques_data)
        metrics = await self._calculate_metrics(techniques_data)
        
        # Create heat map response
        heatmap_response = HeatMapResponse(
            name=f"Assessment {assessment.name} Heat Map",
            description=f"Heat map for assessment: {assessment.description or assessment.name}",
            heat_map_type=HeatMapType.ASSESSMENT,
            campaign_id=None,
            assessment_id=assessment_id,
            techniques=techniques_data,
            tactics=tactics_data,
            metrics=metrics,
            generated_at=datetime.now(timezone.utc)
        )
        
        # Save snapshot if requested
        if save_snapshot and snapshot_name:
            snapshot = await self._save_snapshot(
                heatmap_response=heatmap_response,
                snapshot_name=snapshot_name,
                assessment_id=assessment_id
            )
            heatmap_response.id = snapshot.id
        
        logger.info(f"Generated heat map for assessment {assessment_id}")
        return heatmap_response
    
    async def _generate_technique_data(
        self,
        technique_results: List[AttackTechniqueResult]
    ) -> List[TechniqueHeatMapData]:
        """
        Generate technique-level heat map data.
        
        Args:
            technique_results: List of technique results
            
        Returns:
            List of technique heat map data
        """
        technique_data = []
        
        # Group results by technique
        technique_groups = {}
        for result in technique_results:
            if result.technique_id not in technique_groups:
                technique_groups[result.technique_id] = []
            technique_groups[result.technique_id].append(result)
        
        # Process each technique
        for technique_id, results in technique_groups.items():
            # Get technique details
            technique = self.db.query(MitreTechnique).filter(
                MitreTechnique.technique_id == technique_id
            ).first()
            
            if not technique:
                logger.warning(f"Technique {technique_id} not found in database")
                continue
            
            # Get tactic for technique
            tactic = self.db.query(MitreTactic).filter(
                MitreTactic.id == technique.tactic_id
            ).first()
            
            # Calculate aggregated metrics
            total_executions = sum(r.execution_count for r in results)
            avg_effectiveness = sum(r.effectiveness_score for r in results) / len(results)
            avg_detection = sum(r.detection_rate for r in results) / len(results)
            avg_prevention = sum(r.prevention_rate for r in results) / len(results)
            
            # Determine status
            status = self._determine_technique_status(avg_effectiveness)
            
            # Get test cases count
            test_cases_count = self.db.query(TestCase).filter(
                TestCase.mitre_techniques.contains([technique_id]),
                TestCase.deleted_at.is_(None)
            ).count()
            
            # Get latest test date
            latest_result = max(results, key=lambda r: r.last_tested or datetime.min)
            
            technique_data.append(TechniqueHeatMapData(
                technique_id=technique_id,
                technique_name=technique.name,
                tactic_id=tactic.tactic_id if tactic else "Unknown",
                tactic_name=tactic.name if tactic else "Unknown",
                status=status,
                effectiveness_score=avg_effectiveness,
                detection_rate=avg_detection,
                prevention_rate=avg_prevention,
                execution_count=total_executions,
                last_tested=latest_result.last_tested,
                test_cases_count=test_cases_count
            ))
        
        return technique_data
    
    async def _generate_tactic_data(
        self,
        techniques_data: List[TechniqueHeatMapData]
    ) -> List[TacticHeatMapData]:
        """
        Generate tactic-level heat map data.
        
        Args:
            techniques_data: List of technique heat map data
            
        Returns:
            List of tactic heat map data
        """
        tactic_data = []
        
        # Group techniques by tactic
        tactic_groups = {}
        for technique in techniques_data:
            if technique.tactic_id not in tactic_groups:
                tactic_groups[technique.tactic_id] = []
            tactic_groups[technique.tactic_id].append(technique)
        
        # Process each tactic
        for tactic_id, techniques in tactic_groups.items():
            # Count by status
            covered_count = len([t for t in techniques if t.status == TechniqueStatus.COVERED])
            partial_count = len([t for t in techniques if t.status == TechniqueStatus.PARTIAL])
            not_covered_count = len([t for t in techniques if t.status == TechniqueStatus.NOT_COVERED])
            
            # Calculate averages
            avg_effectiveness = sum(t.effectiveness_score for t in techniques) / len(techniques)
            coverage_percentage = ((covered_count + partial_count * 0.5) / len(techniques)) * 100
            
            tactic_data.append(TacticHeatMapData(
                tactic_id=tactic_id,
                tactic_name=techniques[0].tactic_name,
                techniques_count=len(techniques),
                covered_count=covered_count,
                partial_count=partial_count,
                not_covered_count=not_covered_count,
                average_effectiveness=avg_effectiveness,
                coverage_percentage=coverage_percentage
            ))
        
        return tactic_data
    
    async def _calculate_metrics(
        self,
        techniques_data: List[TechniqueHeatMapData]
    ) -> HeatMapMetrics:
        """
        Calculate overall heat map metrics.
        
        Args:
            techniques_data: List of technique heat map data
            
        Returns:
            Overall heat map metrics
        """
        if not techniques_data:
            return HeatMapMetrics(
                total_techniques=0,
                covered_techniques=0,
                partial_techniques=0,
                not_covered_techniques=0,
                overall_coverage_percentage=0.0,
                average_effectiveness=0.0,
                average_detection_rate=0.0,
                average_prevention_rate=0.0,
                total_executions=0,
                last_updated=datetime.now(timezone.utc)
            )
        
        # Count by status
        covered_count = len([t for t in techniques_data if t.status == TechniqueStatus.COVERED])
        partial_count = len([t for t in techniques_data if t.status == TechniqueStatus.PARTIAL])
        not_covered_count = len([t for t in techniques_data if t.status == TechniqueStatus.NOT_COVERED])
        
        # Calculate averages
        avg_effectiveness = sum(t.effectiveness_score for t in techniques_data) / len(techniques_data)
        avg_detection = sum(t.detection_rate for t in techniques_data) / len(techniques_data)
        avg_prevention = sum(t.prevention_rate for t in techniques_data) / len(techniques_data)
        
        # Calculate coverage percentage
        coverage_percentage = ((covered_count + partial_count * 0.5) / len(techniques_data)) * 100
        
        # Calculate total executions
        total_executions = sum(t.execution_count for t in techniques_data)
        
        # Get latest update
        latest_tested = max(
            (t.last_tested for t in techniques_data if t.last_tested),
            default=datetime.now(timezone.utc)
        )
        
        return HeatMapMetrics(
            total_techniques=len(techniques_data),
            covered_techniques=covered_count,
            partial_techniques=partial_count,
            not_covered_techniques=not_covered_count,
            overall_coverage_percentage=coverage_percentage,
            average_effectiveness=avg_effectiveness,
            average_detection_rate=avg_detection,
            average_prevention_rate=avg_prevention,
            total_executions=total_executions,
            last_updated=latest_tested
        )

    def _determine_technique_status(self, effectiveness_score: float) -> TechniqueStatus:
        """
        Determine technique status based on effectiveness score.

        Args:
            effectiveness_score: Effectiveness score (0-1)

        Returns:
            Technique status
        """
        if effectiveness_score >= 0.8:
            return TechniqueStatus.COVERED
        elif effectiveness_score >= 0.4:
            return TechniqueStatus.PARTIAL
        else:
            return TechniqueStatus.NOT_COVERED

    async def _save_snapshot(
        self,
        heatmap_response: HeatMapResponse,
        snapshot_name: str,
        campaign_id: Optional[int] = None,
        assessment_id: Optional[int] = None
    ) -> HeatMapSnapshot:
        """
        Save heat map as snapshot.

        Args:
            heatmap_response: Heat map response data
            snapshot_name: Snapshot name
            campaign_id: Optional campaign ID
            assessment_id: Optional assessment ID

        Returns:
            Created snapshot
        """
        # Prepare technique coverage data
        technique_coverage = {
            t.technique_id: t.effectiveness_score
            for t in heatmap_response.techniques
        }

        # Prepare tactic effectiveness data
        tactic_effectiveness = {
            t.tactic_id: t.average_effectiveness
            for t in heatmap_response.tactics
        }

        # Prepare overall metrics
        metrics = heatmap_response.metrics
        overall_metrics = {
            "total_techniques": metrics.total_techniques,
            "covered_techniques": metrics.covered_techniques,
            "partial_techniques": metrics.partial_techniques,
            "not_covered_techniques": metrics.not_covered_techniques,
            "overall_coverage_percentage": metrics.overall_coverage_percentage,
            "average_effectiveness": metrics.average_effectiveness,
            "average_detection_rate": metrics.average_detection_rate,
            "average_prevention_rate": metrics.average_prevention_rate,
            "total_executions": metrics.total_executions
        }

        # Create snapshot
        snapshot = HeatMapSnapshot(
            campaign_id=campaign_id,
            assessment_id=assessment_id,
            name=snapshot_name,
            description=heatmap_response.description,
            snapshot_type=heatmap_response.heat_map_type.value,
            technique_coverage=technique_coverage,
            tactic_effectiveness=tactic_effectiveness,
            overall_metrics=overall_metrics,
            snapshot_date=datetime.now(timezone.utc)
        )

        self.db.add(snapshot)
        self.db.commit()
        self.db.refresh(snapshot)

        logger.info(f"Saved heat map snapshot {snapshot.id}: {snapshot_name}")
        return snapshot

    async def get_snapshot(self, snapshot_id: int) -> Optional[HeatMapSnapshotResponse]:
        """
        Get heat map snapshot by ID.

        Args:
            snapshot_id: Snapshot ID

        Returns:
            Snapshot data or None if not found
        """
        snapshot = self.db.query(HeatMapSnapshot).filter(
            HeatMapSnapshot.id == snapshot_id,
            HeatMapSnapshot.deleted_at.is_(None)
        ).first()

        if not snapshot:
            return None

        return HeatMapSnapshotResponse.from_orm(snapshot)

    async def list_snapshots(
        self,
        campaign_id: Optional[int] = None,
        assessment_id: Optional[int] = None,
        snapshot_type: Optional[HeatMapType] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[HeatMapSnapshotResponse]:
        """
        List heat map snapshots with optional filtering.

        Args:
            campaign_id: Optional campaign ID filter
            assessment_id: Optional assessment ID filter
            snapshot_type: Optional snapshot type filter
            limit: Maximum number of results
            offset: Number of results to skip

        Returns:
            List of snapshots
        """
        query = self.db.query(HeatMapSnapshot).filter(
            HeatMapSnapshot.deleted_at.is_(None)
        )

        if campaign_id:
            query = query.filter(HeatMapSnapshot.campaign_id == campaign_id)

        if assessment_id:
            query = query.filter(HeatMapSnapshot.assessment_id == assessment_id)

        if snapshot_type:
            query = query.filter(HeatMapSnapshot.snapshot_type == snapshot_type.value)

        snapshots = query.order_by(
            HeatMapSnapshot.snapshot_date.desc()
        ).offset(offset).limit(limit).all()

        return [HeatMapSnapshotResponse.from_orm(s) for s in snapshots]

    async def delete_snapshot(self, snapshot_id: int) -> bool:
        """
        Soft delete a heat map snapshot.

        Args:
            snapshot_id: Snapshot ID

        Returns:
            True if deleted, False if not found
        """
        snapshot = self.db.query(HeatMapSnapshot).filter(
            HeatMapSnapshot.id == snapshot_id,
            HeatMapSnapshot.deleted_at.is_(None)
        ).first()

        if not snapshot:
            return False

        snapshot.deleted_at = datetime.now(timezone.utc)
        self.db.commit()

        logger.info(f"Soft deleted heat map snapshot {snapshot_id}")
        return True
