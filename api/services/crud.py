"""Base CRUD service for database operations."""
from typing import TypeVar, Generic, Type, Optional, List, Any, Dict
from sqlalchemy.orm import Session
from sqlalchemy import select
from api.models.mixins import SoftDeleteMixin
from api.database import Base
from fastapi import HTTPException
from datetime import datetime

ModelType = TypeVar("ModelType", bound=Base)

class CRUDService(Generic[ModelType]):
    """Base class for CRUD operations."""

    def __init__(self, model: Type[ModelType]):
        """Initialize with the model class."""
        self.model = model

    def get_all(
        self, 
        db: Session, 
        skip_deleted: bool = True,
        skip: int = 0,
        limit: int = 100
    ) -> List[ModelType]:
        """Get all records with pagination."""
        query = select(self.model)
        if skip_deleted and issubclass(self.model, SoftDeleteMixin):
            query = query.where(self.model.deleted_at.is_(None))
        query = query.offset(skip).limit(limit)
        return db.execute(query).scalars().all()

    def get_by_id(
        self, 
        db: Session, 
        id: int, 
        skip_deleted: bool = True
    ) -> Optional[ModelType]:
        """Get a record by ID."""
        query = select(self.model).where(self.model.id == id)
        if skip_deleted and issubclass(self.model, SoftDeleteMixin):
            query = query.where(self.model.deleted_at.is_(None))
        return db.execute(query).scalar_one_or_none()

    def create(self, db: Session, data: Dict[str, Any]) -> ModelType:
        """Create a new record."""
        instance = self.model(**data)
        db.add(instance)
        db.commit()
        db.refresh(instance)
        return instance

    def update(
        self, 
        db: Session,
        instance: ModelType,
        data: Dict[str, Any]
    ) -> ModelType:
        """Update a record."""
        for key, value in data.items():
            if hasattr(instance, key):
                setattr(instance, key, value)
        
        if isinstance(instance, SoftDeleteMixin):
            instance.updated_at = datetime.utcnow()
        
        db.add(instance)
        db.commit()
        db.refresh(instance)
        return instance

    def delete(self, db: Session, instance: ModelType) -> None:
        """Delete a record (soft-delete if supported)."""
        if isinstance(instance, SoftDeleteMixin):
            instance.soft_delete(db)
        else:
            db.delete(instance)
            db.commit()

    def restore(self, db: Session, instance: ModelType) -> ModelType:
        """Restore a soft-deleted record."""
        if not isinstance(instance, SoftDeleteMixin):
            raise HTTPException(
                status_code=400,
                detail="Model does not support soft-delete"
            )
        instance.restore(db)
        return instance

    def mark_deprecated(
        self, 
        db: Session,
        instance: ModelType,
        replacement_id: Optional[int] = None
    ) -> ModelType:
        """Mark a record as deprecated."""
        if not isinstance(instance, SoftDeleteMixin):
            raise HTTPException(
                status_code=400,
                detail="Model does not support deprecation"
            )
        instance.mark_deprecated(db, replacement_id)
        return instance
