"""Authentication routes for the Flask application."""

from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import Lo<PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required
from api.forms import LoginForm
from api.models.user import User
from api.database import db
from api.utils.error_handler import error_handler, log_error
import logging

# Configure logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# Create blueprint
auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

# Initialize LoginManager
login_manager = LoginManager()
login_manager.login_view = 'auth.login'
login_manager.login_message_category = 'info'

@login_manager.user_loader
def load_user(user_id):
    """Load user by ID."""
    try:
        return User.query.get(int(user_id))
    except Exception as e:
        log_error(e, endpoint='login_manager.user_loader', request_data={'user_id': user_id})
        return None

@auth_bp.route('/login', methods=['GET', 'POST'])
@error_handler
def login():
    """Handle user login."""
    if request.method == 'GET':
        return render_template('login.html')
        
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data):
            login_user(user)
            logger.info(f"User {user.username} logged in successfully")
            flash('Logged in successfully.', 'success')
            next_page = request.args.get('next')
            return redirect(next_page or url_for('main.index'))
        flash('Invalid username or password.', 'danger')
        logger.warning(f"Failed login attempt for username: {form.username.data}")
    return render_template('login.html', form=form)

@auth_bp.route('/logout')
@login_required
@error_handler
def logout():
    """Handle user logout."""
    logout_user()
    flash('You have been logged out.', 'info')
    return redirect(url_for('auth.login'))
