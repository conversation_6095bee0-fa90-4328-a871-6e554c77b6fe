"""
MITRE ATT&CK Heat Map API endpoints.

This module provides REST API endpoints for generating, managing, and analyzing
MITRE ATT&CK heat maps. Follows API-first design principles with comprehensive
validation, error handling, and documentation.
"""

from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
import logging

from api.database import get_db
from api.models import (
    AttackTechniqueResult,
    HeatMapSnapshot,
    Campaign,
    Assessment,
    MitreTechnique,
    MitreTactic
)
from api.schemas.heatmap import (
    AttackTechniqueResultCreate,
    AttackTechniqueResultUpdate,
    AttackTechniqueResultResponse,
    HeatMapResponse,
    HeatMapSnapshotCreate,
    HeatMapSnapshotResponse,
    HeatMapComparisonRequest,
    HeatMapComparisonResponse,
    HeatMapType,
    TechniqueStatus
)
from api.services.heatmap_service import HeatMapService
from api.utils.auth import get_current_user
from api.utils.pagination import PaginationParams, paginate_query
from api.utils.soft_delete import apply_soft_delete_filter

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/v1/heatmap", tags=["Heat Maps"])


@router.post(
    "/technique-results",
    response_model=AttackTechniqueResultResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create attack technique result",
    description="Create a new attack technique result for heat map generation."
)
async def create_technique_result(
    result_data: AttackTechniqueResultCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
) -> AttackTechniqueResultResponse:
    """
    Create a new attack technique result.
    
    Args:
        result_data: Technique result creation data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Created technique result
        
    Raises:
        HTTPException: If creation fails or validation errors occur
    """
    try:
        # Validate campaign exists and user has access
        campaign = db.query(Campaign).filter(
            Campaign.id == result_data.campaign_id
        ).first()
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Campaign {result_data.campaign_id} not found"
            )
        
        # Validate assessment if provided
        if result_data.assessment_id:
            assessment = db.query(Assessment).filter(
                Assessment.id == result_data.assessment_id
            ).first()
            
            if not assessment:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Assessment {result_data.assessment_id} not found"
                )
        
        # Validate technique exists
        technique = db.query(MitreTechnique).filter(
            MitreTechnique.technique_id == result_data.technique_id
        ).first()
        
        if not technique:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Technique {result_data.technique_id} not found"
            )
        
        # Check for existing result
        existing_result = db.query(AttackTechniqueResult).filter(
            AttackTechniqueResult.technique_id == result_data.technique_id,
            AttackTechniqueResult.campaign_id == result_data.campaign_id
        ).first()
        
        if existing_result:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=(
                    f"Result for technique {result_data.technique_id} "
                    f"in campaign {result_data.campaign_id} already exists"
                )
            )
        
        # Create new result
        new_result = AttackTechniqueResult(
            **result_data.dict(),
            last_tested=datetime.utcnow()
        )
        
        db.add(new_result)
        db.commit()
        db.refresh(new_result)
        
        logger.info(
            f"Created technique result {new_result.id} for "
            f"technique {result_data.technique_id} by user {current_user.id}"
        )
        
        return AttackTechniqueResultResponse.from_orm(new_result)
        
    except SQLAlchemyError as e:
        logger.error(f"Database error creating technique result: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create technique result"
        )


@router.get(
    "/technique-results",
    response_model=List[AttackTechniqueResultResponse],
    summary="List attack technique results",
    description="Retrieve attack technique results with optional filtering."
)
async def list_technique_results(
    campaign_id: Optional[int] = Query(
        None,
        description="Filter by campaign ID"
    ),
    assessment_id: Optional[int] = Query(
        None,
        description="Filter by assessment ID"
    ),
    technique_id: Optional[str] = Query(
        None,
        description="Filter by technique ID"
    ),
    pagination: PaginationParams = Depends(),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
) -> List[AttackTechniqueResultResponse]:
    """
    Retrieve attack technique results with optional filtering.
    
    Args:
        campaign_id: Optional campaign ID filter
        assessment_id: Optional assessment ID filter
        technique_id: Optional technique ID filter
        pagination: Pagination parameters
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        List of technique results
    """
    try:
        query = db.query(AttackTechniqueResult)
        
        # Apply soft delete filter
        query = apply_soft_delete_filter(query, AttackTechniqueResult)
        
        # Apply filters
        if campaign_id:
            query = query.filter(
                AttackTechniqueResult.campaign_id == campaign_id
            )
        
        if assessment_id:
            query = query.filter(
                AttackTechniqueResult.assessment_id == assessment_id
            )
        
        if technique_id:
            query = query.filter(
                AttackTechniqueResult.technique_id == technique_id
            )
        
        # Apply pagination
        results = paginate_query(query, pagination)
        
        return [
            AttackTechniqueResultResponse.from_orm(result)
            for result in results
        ]
        
    except SQLAlchemyError as e:
        logger.error(f"Database error listing technique results: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve technique results"
        )


@router.get(
    "/technique-results/{result_id}",
    response_model=AttackTechniqueResultResponse,
    summary="Get attack technique result",
    description="Retrieve a specific attack technique result by ID."
)
async def get_technique_result(
    result_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
) -> AttackTechniqueResultResponse:
    """
    Retrieve a specific attack technique result.
    
    Args:
        result_id: Technique result ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Technique result data
        
    Raises:
        HTTPException: If result not found
    """
    try:
        result = db.query(AttackTechniqueResult).filter(
            AttackTechniqueResult.id == result_id
        ).first()
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Technique result {result_id} not found"
            )
        
        return AttackTechniqueResultResponse.from_orm(result)
        
    except SQLAlchemyError as e:
        logger.error(f"Database error retrieving technique result: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve technique result"
        )


@router.put(
    "/technique-results/{result_id}",
    response_model=AttackTechniqueResultResponse,
    summary="Update attack technique result",
    description="Update an existing attack technique result."
)
async def update_technique_result(
    result_id: int,
    update_data: AttackTechniqueResultUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
) -> AttackTechniqueResultResponse:
    """
    Update an existing attack technique result.
    
    Args:
        result_id: Technique result ID
        update_data: Update data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Updated technique result
        
    Raises:
        HTTPException: If result not found or update fails
    """
    try:
        result = db.query(AttackTechniqueResult).filter(
            AttackTechniqueResult.id == result_id
        ).first()
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Technique result {result_id} not found"
            )
        
        # Update fields
        update_dict = update_data.dict(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(result, field, value)
        
        result.updated_at = datetime.utcnow()
        result.last_tested = datetime.utcnow()
        
        db.commit()
        db.refresh(result)
        
        logger.info(
            f"Updated technique result {result_id} by user {current_user.id}"
        )
        
        return AttackTechniqueResultResponse.from_orm(result)
        
    except SQLAlchemyError as e:
        logger.error(f"Database error updating technique result: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update technique result"
        )


@router.delete(
    "/technique-results/{result_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete attack technique result",
    description="Soft delete an attack technique result."
)
async def delete_technique_result(
    result_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
) -> None:
    """
    Soft delete an attack technique result.

    Args:
        result_id: Technique result ID
        db: Database session
        current_user: Current authenticated user

    Raises:
        HTTPException: If result not found or deletion fails
    """
    try:
        result = db.query(AttackTechniqueResult).filter(
            AttackTechniqueResult.id == result_id
        ).first()

        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Technique result {result_id} not found"
            )

        # Soft delete
        result.deleted_at = datetime.utcnow()
        db.commit()

        logger.info(
            f"Soft deleted technique result {result_id} by user {current_user.id}"
        )

    except SQLAlchemyError as e:
        logger.error(f"Database error deleting technique result: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete technique result"
        )


@router.get(
    "/generate/{campaign_id}",
    response_model=HeatMapResponse,
    summary="Generate campaign heat map",
    description="Generate a MITRE ATT&CK heat map for a specific campaign."
)
async def generate_campaign_heatmap(
    campaign_id: int,
    save_snapshot: bool = Query(
        False,
        description="Save heat map as snapshot"
    ),
    snapshot_name: Optional[str] = Query(
        None,
        description="Snapshot name (required if save_snapshot=true)"
    ),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
) -> HeatMapResponse:
    """
    Generate a MITRE ATT&CK heat map for a campaign.

    Args:
        campaign_id: Campaign ID
        save_snapshot: Whether to save as snapshot
        snapshot_name: Snapshot name (required if saving)
        db: Database session
        current_user: Current authenticated user

    Returns:
        Generated heat map data

    Raises:
        HTTPException: If campaign not found or generation fails
    """
    try:
        # Validate campaign exists
        campaign = db.query(Campaign).filter(
            Campaign.id == campaign_id
        ).first()

        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Campaign {campaign_id} not found"
            )

        # Validate snapshot name if saving
        if save_snapshot and not snapshot_name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="snapshot_name is required when save_snapshot=true"
            )

        # Generate heat map using service
        heatmap_service = HeatMapService(db)
        heatmap_data = await heatmap_service.generate_campaign_heatmap(
            campaign_id=campaign_id,
            save_snapshot=save_snapshot,
            snapshot_name=snapshot_name
        )

        logger.info(
            f"Generated heat map for campaign {campaign_id} "
            f"by user {current_user.id}"
        )

        return heatmap_data

    except SQLAlchemyError as e:
        logger.error(f"Database error generating heat map: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate heat map"
        )


@router.get(
    "/generate/assessment/{assessment_id}",
    response_model=HeatMapResponse,
    summary="Generate assessment heat map",
    description="Generate a MITRE ATT&CK heat map for a specific assessment."
)
async def generate_assessment_heatmap(
    assessment_id: int,
    save_snapshot: bool = Query(
        False,
        description="Save heat map as snapshot"
    ),
    snapshot_name: Optional[str] = Query(
        None,
        description="Snapshot name (required if save_snapshot=true)"
    ),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
) -> HeatMapResponse:
    """
    Generate a MITRE ATT&CK heat map for an assessment.

    Args:
        assessment_id: Assessment ID
        save_snapshot: Whether to save as snapshot
        snapshot_name: Snapshot name (required if saving)
        db: Database session
        current_user: Current authenticated user

    Returns:
        Generated heat map data

    Raises:
        HTTPException: If assessment not found or generation fails
    """
    try:
        # Validate assessment exists
        assessment = db.query(Assessment).filter(
            Assessment.id == assessment_id
        ).first()

        if not assessment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Assessment {assessment_id} not found"
            )

        # Validate snapshot name if saving
        if save_snapshot and not snapshot_name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="snapshot_name is required when save_snapshot=true"
            )

        # Generate heat map using service
        heatmap_service = HeatMapService(db)
        heatmap_data = await heatmap_service.generate_assessment_heatmap(
            assessment_id=assessment_id,
            save_snapshot=save_snapshot,
            snapshot_name=snapshot_name
        )

        logger.info(
            f"Generated heat map for assessment {assessment_id} "
            f"by user {current_user.id}"
        )

        return heatmap_data

    except SQLAlchemyError as e:
        logger.error(f"Database error generating heat map: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate heat map"
        )
