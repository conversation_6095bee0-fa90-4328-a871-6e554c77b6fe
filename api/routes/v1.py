"""API routes for version 1.

This module contains the primary router for version 1 of the API, including
endpoints for user management and preferences.

Attributes:
    router: The main APIRouter instance for v1 endpoints
"""
from fastapi import APIRouter, HTTPException, Query, Depends
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from api.models import User, UserPreference
from api.models.schemas import UserResponse, UserPreferenceResponse, PaginatedResponse
from api.utils.i18n import _
from api.database import get_db

router = APIRouter()

@router.get("/users", response_model=PaginatedResponse[UserResponse])
async def get_users(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(10, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db)
) -> PaginatedResponse[UserResponse]:
    """Get all users with pagination.

    Args:
        page: The page number to retrieve (starts at 1)
        size: Number of items per page (between 1 and 100)
        db: Database session dependency

    Returns:
        PaginatedResponse: A paginated list of users
    """
    query = db.query(User)
    total = query.count()
    users = query.offset((page - 1) * size).limit(size).all()
    return PaginatedResponse(
        items=users,
        total=total,
        page=page,
        size=size
    )

@router.get("/users/{user_id}/preferences", response_model=UserPreferenceResponse)
async def get_user_preferences(
    user_id: int,
    db: Session = Depends(get_db)
) -> UserPreferenceResponse:
    """Get preferences for a specific user.

    Args:
        user_id: ID of the user to get preferences for
        db: Database session dependency

    Returns:
        UserPreferenceResponse: The user's preferences
    """
    preferences = db.query(UserPreference).filter(UserPreference.user_id == user_id).first()
    if not preferences:
        raise HTTPException(
            status_code=404,
            detail=_("User preferences not found")
        )
    return preferences