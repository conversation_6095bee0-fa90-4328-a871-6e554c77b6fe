"""MITRE ATT&CK Framework API endpoints."""
from fastapi import APIRouter, HTTPException, Depends, Query, status
from sqlalchemy.orm import Session
from typing import List, Optional, Dict
from datetime import datetime
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import text, func
import uuid
import logging

from api.database import get_db, DatabaseError
from api.models import (
    MitreVersion, 
    MitreTechnique, 
    MitreTactic,
    MitreDefenseVersion,
    MitreControl,
    StixDB,
    StixObject,
    StixBundle,
    TechniqueScore
)
from api.utils.mitre_import import import_mitre_data, get_current_version
from api.schemas import SuccessResponse, PaginatedResponse
from api.models.schemas import (
    TechniqueCreate,
    TechniqueResponse,
    TechniqueRelationshipCreate,
    TechniqueRelationship,
    TechniqueScoreCreate,
    TechniqueScoreResponse,
    TechniqueWithScores,
    TechniqueScoreBulkCreate,
    ScoreCategory
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/mitre", tags=["MITRE ATT&CK"])

def verify_db_session(db: Session):
    """Verify database session is active."""
    try:
        db.execute(text("SELECT 1"))
    except SQLAlchemyError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )

@router.post("/technique", response_model=TechniqueResponse)
async def create_technique(
    technique: TechniqueCreate,
    db: Session = Depends(get_db)
) -> TechniqueResponse:
    """Create a new MITRE technique."""
    verify_db_session(db)
    try:
        # Check if version exists
        version = db.query(MitreVersion).filter_by(id=technique.version_id).first()
        if not version:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Version not found"
            )

        db_technique = MitreTechnique(
            technique_id=technique.technique_id,
            name=technique.name,
            description=technique.description,
            version_id=technique.version_id
        )
        db.add(db_technique)
        db.commit()
        db.refresh(db_technique)
        return db_technique
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )

@router.get("/technique/{technique_id}", response_model=TechniqueResponse)
async def get_technique(
    technique_id: int,
    db: Session = Depends(get_db)
) -> TechniqueResponse:
    """Get a specific MITRE technique by ID."""
    verify_db_session(db)
    try:
        technique = db.query(MitreTechnique).filter_by(id=technique_id).first()
        if not technique:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Technique not found"
            )
        return technique
    except SQLAlchemyError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )

@router.get("/techniques")
async def list_techniques(
    version_id: Optional[int] = None,
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db)
) -> PaginatedResponse:
    """List MITRE techniques with optional filtering."""
    verify_db_session(db)
    try:
        query = db.query(MitreTechnique)

        if version_id:
            # Verify version exists
            version = db.query(MitreVersion).filter_by(id=version_id).first()
            if not version:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Version not found"
                )
            query = query.filter(MitreTechnique.version_id == version_id)
        else:
            current = get_current_version(db)
            if current:
                query = query.filter(MitreTechnique.version_id == current.id)

        total = query.count()
        techniques = query.offset((page - 1) * size).limit(size).all()

        return PaginatedResponse(
            items=[{
                "id": t.id,
                "technique_id": t.technique_id,
                "name": t.name,
                "description": t.description
            } for t in techniques],
            total=total,
            page=page,
            size=size
        )
    except SQLAlchemyError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )

@router.get("/tactics")
async def list_tactics(
    version_id: Optional[int] = None,
    db: Session = Depends(get_db)
) -> List[dict]:
    """List MITRE tactics."""
    verify_db_session(db)
    try:
        query = db.query(MitreTactic)
        if version_id:
            # Verify version exists
            version = db.query(MitreVersion).filter_by(id=version_id).first()
            if not version:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Version not found"
                )
            query = query.filter(MitreTactic.version_id == version_id)

        tactics = query.all()
        return [{
            "id": t.id,
            "name": t.name,
            "description": t.description
        } for t in tactics]
    except SQLAlchemyError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )

@router.get("/techniques/{technique_id}/relationships")
async def get_technique_relationships(
    technique_id: str,
    db: Session = Depends(get_db)
) -> dict:
    """Get relationships for a technique."""
    verify_db_session(db)
    try:
        relationships = db.query(TechniqueRelationship).filter(
            (TechniqueRelationship.source_technique_id == technique_id) |
            (TechniqueRelationship.target_technique_id == technique_id)
        ).all()

        return {
            "relationships": [
                {
                    "id": r.id,
                    "source_technique_id": r.source_technique_id,
                    "target_technique_id": r.target_technique_id,
                    "relationship_type": r.relationship_type
                } for r in relationships
            ]
        }
    except SQLAlchemyError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )

@router.post("/techniques/relationships", response_model=TechniqueRelationship)
async def create_technique_relationship(
    relationship: TechniqueRelationshipCreate,
    db: Session = Depends(get_db)
) -> TechniqueRelationship:
    """Create a relationship between two techniques."""
    verify_db_session(db)
    try:
        db_relationship = TechniqueRelationship(
            source_technique_id=relationship.source_technique_id,
            target_technique_id=relationship.target_technique_id,
            relationship_type=relationship.relationship_type
        )
        db.add(db_relationship)
        db.commit()
        db.refresh(db_relationship)
        return db_relationship
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )

@router.post("/import")
async def import_mitre(
    version: Optional[str] = None,
    set_as_current: bool = True,
    db: Session = Depends(get_db)
) -> SuccessResponse:
    """Import MITRE ATT&CK data."""
    verify_db_session(db)
    try:
        mitre_version = import_mitre_data(db, version, set_as_current)
        return SuccessResponse(
            message=f"Successfully imported MITRE ATT&CK version {mitre_version.version}",
            data={"version": mitre_version.version}
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error importing MITRE data: {str(e)}"
        )

@router.get("/defense/versions")
async def list_defense_versions(
    db: Session = Depends(get_db)
) -> List[dict]:
    """List all imported MITRE Defense versions."""
    verify_db_session(db)
    try:
        versions = db.query(MitreDefenseVersion).all()
        return [{
            "id": v.id,
            "version": v.version,
            "import_date": v.import_date,
            "is_current": v.is_current
        } for v in versions]
    except SQLAlchemyError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )

@router.get("/defense/controls")
async def list_controls(
    version_id: Optional[int] = None,
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db)
) -> PaginatedResponse:
    """List MITRE Defense controls with pagination."""
    verify_db_session(db)
    try:
        query = db.query(MitreControl)

        if version_id:
            # Verify version exists
            version = db.query(MitreDefenseVersion).filter_by(id=version_id).first()
            if not version:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Version not found"
                )
            query = query.filter(MitreControl.version_id == version_id)

        total = query.count()
        controls = query.offset((page - 1) * size).limit(size).all()

        return PaginatedResponse(
            items=[{
                "id": c.id,
                "external_id": c.external_id,
                "name": c.name,
                "description": c.description,
                "version_id": c.version_id
            } for c in controls],
            total=total,
            page=page,
            size=size
        )
    except SQLAlchemyError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )

@router.get("/defense/controls/{external_id}")
async def get_control(
    external_id: str,
    db: Session = Depends(get_db)
) -> dict:
    """Get a specific MITRE Defense control by external ID."""
    verify_db_session(db)
    try:
        control = db.query(MitreControl).filter_by(external_id=external_id).first()
        if not control:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Control not found"
            )
        return {
            "id": control.id,
            "external_id": control.external_id,
            "name": control.name,
            "description": control.description,
            "version_id": control.version_id,
            "created": control.created,
            "modified": control.modified
        }
    except SQLAlchemyError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )

@router.post("/stix/objects")
async def create_stix_object(
    stix_obj: StixObject,
    db: Session = Depends(get_db)
) -> StixObject:
    """Create a new STIX2 object."""
    verify_db_session(db)
    try:
        db_obj = StixDB.from_stix(stix_obj.dict())
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return StixObject.from_stix(db_obj.to_stix())
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )

@router.get("/stix/objects/{stix_id}")
async def get_stix_object(
    stix_id: str,
    db: Session = Depends(get_db)
) -> StixObject:
    """Get a STIX2 object by ID."""
    verify_db_session(db)
    try:
        db_obj = db.query(StixDB).filter_by(stix_id=stix_id).first()
        if not db_obj:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="STIX object not found"
            )
        return StixObject.from_stix(db_obj.to_stix())
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except SQLAlchemyError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred"
        )

@router.post("/stix/bulk/import")
async def bulk_import_stix_objects(
    bundle: StixBundle,
    db: Session = Depends(get_db)
) -> Dict:
    """Bulk import STIX objects from a bundle."""
    try:
        logger.info("Starting bulk STIX object import")
        # Convert bundle to STIX format for validation
        stix_bundle = bundle.to_stix()

        # Track import statistics
        imported_count = 0
        failed_count = 0
        errors = []

        # Import each object
        for stix_obj in bundle.objects:
            try:
                logger.debug(f"Processing STIX object {stix_obj.id}")
                db_obj = StixDB.from_stix(stix_obj.dict())
                existing = db.query(StixDB).filter_by(stix_id=db_obj.stix_id).first()

                if existing:
                    logger.debug(f"Updating existing object {db_obj.stix_id}")
                    existing.data = db_obj.data
                    existing.type = db_obj.type
                else:
                    logger.debug(f"Adding new object {db_obj.stix_id}")
                    db.add(db_obj)

                imported_count += 1
            except Exception as e:
                failed_count += 1
                error_msg = f"Error importing {stix_obj.id}: {str(e)}"
                logger.error(error_msg)
                errors.append(error_msg)
                continue

        db.commit()
        logger.info(f"Bulk import completed: {imported_count} imported, {failed_count} failed")

        return {
            "status": "success",
            "imported_count": imported_count,
            "failed_count": failed_count,
            "errors": errors
        }
    except ValueError as e:
        error_msg = f"Invalid STIX data: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except SQLAlchemyError as e:
        error_msg = f"Database error during bulk import: {str(e)}"
        logger.error(error_msg)
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred during bulk import"
        )

@router.get("/stix/bulk/export")
async def bulk_export_stix_objects(
    type_filter: Optional[str] = None,
    limit: int = Query(1000, gt=0, le=10000),
    db: Session = Depends(get_db)
) -> StixBundle:
    """Export STIX objects as a bundle with optional filtering."""
    try:
        logger.info(f"Starting bulk STIX object export (type={type_filter}, limit={limit})")
        # Build query
        query = db.query(StixDB)
        if type_filter:
            query = query.filter(StixDB.type == type_filter)

        # Get objects
        db_objects = query.limit(limit).all()
        logger.debug(f"Found {len(db_objects)} objects to export")

        # Convert to STIX objects
        stix_objects = []
        conversion_errors = []
        for db_obj in db_objects:
            try:
                stix_obj = StixObject.from_stix(db_obj.data)
                stix_objects.append(stix_obj)
            except Exception as e:
                error_msg = f"Error converting object {db_obj.stix_id}: {str(e)}"
                logger.error(error_msg)
                conversion_errors.append(error_msg)
                continue

        # Create and return bundle
        bundle_id = f"bundle--{str(uuid.uuid4())}"
        logger.info(f"Creating bundle {bundle_id} with {len(stix_objects)} objects")
        if conversion_errors:
            logger.warning(f"Failed to convert {len(conversion_errors)} objects")

        bundle = StixBundle(
            id=bundle_id,
            objects=stix_objects
        )

        return bundle
    except SQLAlchemyError as e:
        error_msg = f"Database error during bulk export: {str(e)}"
        logger.error(error_msg)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred during bulk export"
        )

@router.post("/techniques/{technique_id}/scores", response_model=TechniqueScoreResponse)
async def create_technique_score(
    technique_id: int,
    score_data: TechniqueScoreCreate,
    db: Session = Depends(get_db)
) -> TechniqueScoreResponse:
    """Create a new score for a MITRE ATT&CK technique.
    
    Args:
        technique_id: ID of the technique to score
        score_data: Score data including category, value, and optional notes
        db: Database session
        
    Returns:
        The created technique score
        
    Raises:
        HTTPException: If the technique doesn't exist or there's a database error
    """
    verify_db_session(db)
    
    # Verify technique exists
    technique = db.query(MitreTechnique).filter_by(id=technique_id).first()
    if not technique:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Technique with ID {technique_id} not found"
        )
    
    try:
        # Check if a score for this category already exists
        existing_score = db.query(TechniqueScore).filter_by(
            technique_id=technique_id,
            category=score_data.category
        ).first()
        
        if existing_score:
            # Update existing score
            existing_score.score = score_data.score
            existing_score.weight = score_data.weight
            existing_score.notes = score_data.notes
            existing_score.updated_at = datetime.utcnow()
            db_score = existing_score
        else:
            # Create new score
            db_score = TechniqueScore(
                technique_id=technique_id,
                category=score_data.category,
                score=score_data.score,
                weight=score_data.weight,
                notes=score_data.notes
            )
            db.add(db_score)
        
        db.commit()
        db.refresh(db_score)
        return db_score
    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"Database error creating technique score: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred while creating technique score"
        )

@router.get("/techniques/{technique_id}/scores", response_model=List[TechniqueScoreResponse])
async def get_technique_scores(
    technique_id: int,
    db: Session = Depends(get_db)
) -> List[TechniqueScoreResponse]:
    """Get all scores for a specific MITRE ATT&CK technique.
    
    Args:
        technique_id: ID of the technique
        db: Database session
        
    Returns:
        List of technique scores
        
    Raises:
        HTTPException: If the technique doesn't exist or there's a database error
    """
    verify_db_session(db)
    
    # Verify technique exists
    technique = db.query(MitreTechnique).filter_by(id=technique_id).first()
    if not technique:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Technique with ID {technique_id} not found"
        )
    
    try:
        scores = db.query(TechniqueScore).filter_by(technique_id=technique_id).all()
        return scores
    except SQLAlchemyError as e:
        logger.error(f"Database error retrieving technique scores: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred while retrieving technique scores"
        )

@router.get("/techniques/{technique_id}/with-scores", response_model=TechniqueWithScores)
async def get_technique_with_scores(
    technique_id: int,
    db: Session = Depends(get_db)
) -> TechniqueWithScores:
    """Get a MITRE ATT&CK technique with all its scores and calculated overall score.
    
    Args:
        technique_id: ID of the technique
        db: Database session
        
    Returns:
        Technique with scores and overall score
        
    Raises:
        HTTPException: If the technique doesn't exist or there's a database error
    """
    verify_db_session(db)
    
    try:
        # Get technique with scores
        technique = db.query(MitreTechnique).filter_by(id=technique_id).first()
        if not technique:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Technique with ID {technique_id} not found"
            )
        
        # Calculate overall score (weighted average)
        scores = technique.scores
        if not scores:
            # Return technique without scores
            result = TechniqueWithScores.from_orm(technique)
            result.scores = []
            result.overall_score = None
            return result
        
        total_weighted_score = sum(score.score * score.weight for score in scores)
        total_weight = sum(score.weight for score in scores)
        
        overall_score = total_weighted_score / total_weight if total_weight > 0 else None
        
        # Create response
        result = TechniqueWithScores.from_orm(technique)
        result.scores = scores
        result.overall_score = overall_score
        
        return result
    except SQLAlchemyError as e:
        logger.error(f"Database error retrieving technique with scores: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred while retrieving technique with scores"
        )

@router.post("/techniques/scores/bulk", response_model=SuccessResponse)
async def create_bulk_technique_scores(
    score_data: TechniqueScoreBulkCreate,
    db: Session = Depends(get_db)
) -> SuccessResponse:
    """Create or update multiple technique scores in bulk.
    
    Args:
        score_data: Bulk score data containing multiple technique scores
        db: Database session
        
    Returns:
        Success response with count of scores created/updated
        
    Raises:
        HTTPException: If any technique doesn't exist or there's a database error
    """
    verify_db_session(db)
    
    try:
        # Verify all techniques exist
        technique_ids = [score.technique_id for score in score_data.scores]
        existing_techniques = db.query(MitreTechnique.id).filter(
            MitreTechnique.id.in_(technique_ids)
        ).all()
        existing_ids = [t.id for t in existing_techniques]
        
        missing_ids = set(technique_ids) - set(existing_ids)
        if missing_ids:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Techniques with IDs {missing_ids} not found"
            )
        
        # Process each score
        created_count = 0
        updated_count = 0
        
        for score in score_data.scores:
            # Check if score already exists
            existing_score = db.query(TechniqueScore).filter_by(
                technique_id=score.technique_id,
                category=score.category
            ).first()
            
            if existing_score:
                # Update existing score
                existing_score.score = score.score
                existing_score.weight = score.weight
                existing_score.notes = score.notes
                existing_score.updated_at = datetime.utcnow()
                updated_count += 1
            else:
                # Create new score
                db_score = TechniqueScore(
                    technique_id=score.technique_id,
                    category=score.category,
                    score=score.score,
                    weight=score.weight,
                    notes=score.notes
                )
                db.add(db_score)
                created_count += 1
        
        db.commit()
        return SuccessResponse(
            success=True,
            message=f"Successfully processed {len(score_data.scores)} scores: {created_count} created, {updated_count} updated"
        )
    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"Database error creating bulk technique scores: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred while creating bulk technique scores"
        )

@router.get("/techniques/scores/categories", response_model=List[str])
async def get_score_categories() -> List[str]:
    """Get all available score categories.
    
    Returns:
        List of score category names
    """
    return [category.value for category in ScoreCategory]

@router.get("/techniques/top-scored", response_model=List[TechniqueWithScores])
async def get_top_scored_techniques(
    category: Optional[ScoreCategory] = None,
    limit: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db)
) -> List[TechniqueWithScores]:
    """Get top-scored MITRE ATT&CK techniques.
    
    Args:
        category: Optional category to filter by
        limit: Maximum number of techniques to return
        db: Database session
        
    Returns:
        List of techniques with scores, ordered by score (highest first)
        
    Raises:
        HTTPException: If there's a database error
    """
    verify_db_session(db)
    
    try:
        # Base query to get techniques with scores
        query = db.query(MitreTechnique)
        
        if category:
            # Filter by specific category and order by that category's score
            subquery = db.query(
                TechniqueScore.technique_id,
                TechniqueScore.score.label('category_score')
            ).filter(
                TechniqueScore.category == category
            ).subquery()
            
            query = query.join(
                subquery,
                MitreTechnique.id == subquery.c.technique_id
            ).order_by(subquery.c.category_score.desc())
        else:
            # Calculate average score across all categories
            subquery = db.query(
                TechniqueScore.technique_id,
                func.avg(TechniqueScore.score).label('avg_score')
            ).group_by(
                TechniqueScore.technique_id
            ).subquery()
            
            query = query.join(
                subquery,
                MitreTechnique.id == subquery.c.technique_id
            ).order_by(subquery.c.avg_score.desc())
        
        # Limit results
        techniques = query.limit(limit).all()
        
        # Calculate overall scores for each technique
        result = []
        for technique in techniques:
            scores = technique.scores
            
            if not scores:
                continue
                
            total_weighted_score = sum(score.score * score.weight for score in scores)
            total_weight = sum(score.weight for score in scores)
            
            overall_score = total_weighted_score / total_weight if total_weight > 0 else None
            
            technique_with_scores = TechniqueWithScores.from_orm(technique)
            technique_with_scores.scores = scores
            technique_with_scores.overall_score = overall_score
            
            result.append(technique_with_scores)
        
        return result
    except SQLAlchemyError as e:
        logger.error(f"Database error retrieving top-scored techniques: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error occurred while retrieving top-scored techniques"
        )