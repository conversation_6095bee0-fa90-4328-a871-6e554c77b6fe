"""User management API endpoints."""

from typing import Optional, List
from datetime import datetime, timedelta
import secrets
from functools import wraps
from flask import jsonify
from fastapi import APIRouter, Depends, HTTPException, status, Security
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from api.database import get_db
from api.models.user import User, UserRole
from api.auth.utils import (
    create_user,
    get_user_by_email,
    get_user_by_username,
    get_users_by_role,
    validate_password,
    send_password_reset_email
)
from api.dependencies import get_current_user
from api.models.schemas import (
    UserCreate, 
    UserUpdate, 
    UserResponse, 
    PasswordResetRequest,
    PasswordReset,
    UnlockAccountRequest
)
from api.utils.rate_limiter import standard_rate_limit, strict_rate_limit, auth_rate_limit, user_rate_limit
from api.utils.error_handler import log_error

router = APIRouter()

# Custom error handler for FastAPI routes
def fastapi_error_handler(func):
    """Decorator to handle SQLAlchemy errors in FastAPI routes."""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except SQLAlchemyError as e:
            # Log the error
            log_error(e, endpoint=func.__name__, request_data=kwargs)
            # Return a user-friendly error response
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="A database error occurred. Our team has been notified."
            )
        except Exception as e:
            # Log the error
            log_error(e, endpoint=func.__name__, request_data=kwargs)
            # Re-raise the exception for FastAPI to handle
            raise
    return wrapper

@router.post("/users/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
@fastapi_error_handler
async def register_user(
    user_data: UserCreate, 
    db: Session = Depends(get_db),
    _: None = Depends(auth_rate_limit)  # Apply auth rate limiting
):
    """Register a new user."""
    try:
        # Check if username/email already exists
        if get_user_by_username(db, user_data.username):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already registered"
            )

        if get_user_by_email(db, user_data.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already exists"
            )

        # Validate password
        valid_pass, pass_msg = validate_password(user_data.password)
        if not valid_pass:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=pass_msg
            )

        # Create user
        user = User(
            username=user_data.username,
            email=user_data.email,
            role=UserRole.VIEWER,
            full_name=user_data.full_name,
            bio=user_data.bio
        )
        user.set_password(user_data.password)

        db.add(user)
        db.commit()
        db.refresh(user)
        return user
    except SQLAlchemyError as e:
        # Log the specific error
        log_error(e, endpoint="register_user", request_data={"username": user_data.username, "email": user_data.email})
        # Return a user-friendly error
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="A database error occurred while registering the user. Our team has been notified."
        )

@router.post("/users/password-reset-request")
def request_password_reset(
    request: PasswordResetRequest, 
    db: Session = Depends(get_db),
    _: None = Depends(strict_rate_limit)  # Apply strict rate limiting
):
    """Request a password reset token."""
    user = get_user_by_email(db, request.email)
    if not user:
        # Don't reveal whether email exists
        return {"message": "If the email exists, a reset link will be sent"}

    # Generate token
    token = secrets.token_urlsafe(32)
    user.set_password_reset_token(token)

    try:
        db.commit()
        send_password_reset_email(user.email, token)
        return {"message": "Password reset instructions sent to email"}
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error sending password reset email"
        )

@router.post("/users/reset-password")
def reset_password(
    reset_data: PasswordReset, 
    db: Session = Depends(get_db),
    _: None = Depends(strict_rate_limit)  # Apply strict rate limiting
):
    """Reset password using reset token."""
    user = db.query(User).filter(
        User.password_reset_token == reset_data.token
    ).first()

    if not user or not user.password_reset_token_valid():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired reset token"
        )

    # Validate new password
    valid_pass, pass_msg = validate_password(reset_data.new_password)
    if not valid_pass:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=pass_msg
        )

    user.set_password(reset_data.new_password)
    user.clear_password_reset_token()

    try:
        db.commit()
        return {"message": "Password reset successful"}
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error resetting password"
        )

@router.post("/users/unlock-account")
def unlock_account(
    unlock_request: UnlockAccountRequest,
    current_user: User = Security(get_current_user, scopes=["admin"]),
    db: Session = Depends(get_db),
    _: None = Depends(strict_rate_limit)  # Apply strict rate limiting
):
    """Unlock a locked user account. Admin only."""
    if not current_user.is_admin():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can unlock accounts"
        )

    user = get_user_by_email(db, unlock_request.email)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    if not user.account_locked:
        return {"message": "Account is not locked"}

    user.unlock_account()
    try:
        db.commit()
        return {"message": "Account unlocked successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error unlocking account"
        )

@router.get("/users/me/preferences", response_model=UserResponse)
def get_user_preferences(
    current_user: User = Depends(get_current_user),
    _: None = Depends(user_rate_limit)  # Apply user-specific rate limiting
):
    """Get current user preferences."""
    return current_user

@router.put("/users/me/preferences", response_model=UserResponse)
def update_user_preferences(
    preferences: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    _: None = Depends(user_rate_limit)  # Apply user-specific rate limiting
):
    """Update current user's preferences."""
    # Update all provided preference fields
    for key, value in preferences.items():
        setattr(current_user.preferences, key, value)

    try:
        db.commit()
        db.refresh(current_user)
        return current_user
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating preferences"
        )

@router.get("/users/{user_id}", response_model=UserResponse)
def get_user(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit)  # Apply standard rate limiting
):
    """Get user by ID."""
    if not current_user.is_admin() and current_user.id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )

    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return user

@router.post("/users/", response_model=List[UserResponse])
def list_users(
    role: Optional[UserRole] = None,
    current_user: User = Security(get_current_user, scopes=["admin"]),
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit)  # Apply standard rate limiting
):
    """List all users, optionally filtered by role. Admin only."""
    if not current_user.is_admin():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )

    if role:
        users = get_users_by_role(db, role)
    else:
        users = db.query(User).all()

    return users