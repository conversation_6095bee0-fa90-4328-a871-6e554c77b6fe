"""API routes for Threat-Defense mapping functionality."""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field
import logging

from api.database import get_db
from api.utils.i18n import _
from api.threat_defense.mapper import ThreatDefenseMapper

logger = logging.getLogger(__name__)
router = APIRouter()

class TechniqueSequence(BaseModel):
    """Request body for attack path coverage visualization."""
    technique_sequence: List[str] = Field(
        ...,
        description="Ordered list of ATT&CK technique IDs representing an attack path"
    )

@router.post("/coverage/visualization")
async def get_attack_path_coverage_visualization(
    request: TechniqueSequence,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """Generate a mermaid visualization for attack path coverage analysis."""
    try:
        mapper = ThreatDefenseMapper(db)

        # Get coverage analysis
        coverage = mapper.get_attack_path_coverage(request.technique_sequence)

        # Get countermeasures for each technique
        countermeasures = mapper.find_d3fend_countermeasures(request.technique_sequence)

        # Generate mermaid chart
        mermaid_chart = generate_coverage_mermaid(
            request.technique_sequence,
            coverage["techniques"],
            countermeasures
        )

        return {
            "mermaid_chart": mermaid_chart,
            "overall_coverage": coverage["overall_coverage"],
            "average_coverage": coverage["average_coverage"]
        }

    except Exception as e:
        logger.error(f"Error generating coverage visualization: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=_("Error generating coverage visualization")
        )

def generate_coverage_mermaid(
    techniques: List[str],
    coverage_data: Dict[str, float],
    countermeasures: Dict[str, List[Dict]]
) -> str:
    """Generate a mermaid flowchart for attack path coverage visualization."""
    mermaid = []

    # Start flowchart
    mermaid.append("flowchart TD")

    # Attack Techniques subgraph
    mermaid.append("    subgraph \"Attack Techniques\"")

    # Add technique nodes and connections
    prev_tech = None
    for tech_id in techniques:
        coverage = coverage_data.get(tech_id, 0.0) * 100
        node_id = tech_id.replace('.', '_')

        # Node definition with coverage
        mermaid.append(f"        {node_id}[\"{tech_id}\\n(Coverage: {coverage:.0f}%)\"]")

        # Connect to previous technique
        if prev_tech:
            mermaid.append(f"        {prev_tech} --> {node_id}")
        prev_tech = node_id

    mermaid.append("    end")

    # D3FEND Countermeasures subgraph
    mermaid.append("\n    subgraph \"D3FEND Countermeasures\"")

    # Add countermeasure nodes and relationships
    defense_count = 1
    for tech_id in techniques:
        measures = countermeasures.get(tech_id, [])
        for measure in measures:
            defense_id = f"D{defense_count}"
            name = measure.get('name', 'Unknown Defense')
            mermaid.append(f"        {defense_id}[\"{name}\"]")
            mermaid.append(f"        {defense_id} -.-> {tech_id.replace('.', '_')}")
            defense_count += 1

    mermaid.append("    end")

    # Add styling classes
    mermaid.extend([
        "\n    classDef highCoverage fill:#a3cfbb,stroke:#333,stroke-width:1px;",
        "    classDef mediumCoverage fill:#ffe6b3,stroke:#333,stroke-width:1px;",
        "    classDef lowCoverage fill:#ffb3b3,stroke:#333,stroke-width:1px;",
        "    classDef defense fill:#b3d1ff,stroke:#333,stroke-width:1px;"
    ])

    # Apply classes based on coverage
    for tech_id in techniques:
        coverage = coverage_data.get(tech_id, 0.0) * 100
        node_id = tech_id.replace('.', '_')
        if coverage >= 80:
            mermaid.append(f"    class {node_id} highCoverage;")
        elif coverage >= 50:
            mermaid.append(f"    class {node_id} mediumCoverage;")
        else:
            mermaid.append(f"    class {node_id} lowCoverage;")

    # Apply defense class to all countermeasures
    defense_ids = [f"D{i}" for i in range(1, defense_count)]
    if defense_ids:
        mermaid.append(f"    class {','.join(defense_ids)} defense;")

    return "\n".join(mermaid)