"""Database reporting endpoints."""
from typing import Dict, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy import text
from sqlalchemy.orm import Session
from api.database import get_db
from api.utils.i18n import _

router = APIRouter()

@router.get("")  # Empty string here since prefix is already /api/v1/db_report
async def get_db_report(db: Session = Depends(get_db)):
    """Get a report of database tables and their contents.

    Returns:
        dict: Report containing:
            - List of all tables in the database
            - Row count for each table
            - Sample data (up to 5 rows) from each table
            - Tables are filtered to only show those in the public schema

    Example response:
        {
            "table_name": {
                "total_rows": 100,
                "sample_data": [
                    {"id": 1, "name": "example", ...},
                    ...
                ]
            },
            ...
        }
    """
    # Get list of all tables
    tables_query = """
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = 'public'
    """
    try:
        tables = [row[0] for row in db.execute(text(tables_query))]
        if not tables:
            raise HTTPException(
                status_code=404,
                detail=_("No tables found in database")
            )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=_("Error accessing database tables")
        )

    report = {}

    for table in tables:
        try:
            # Get row count
            count_query = f"SELECT COUNT(*) FROM {table}"
            row_count = db.execute(text(count_query)).scalar()

            # Get sample rows
            sample_query = f"SELECT * FROM {table} LIMIT 5"
            samples = db.execute(text(sample_query))

            # Convert sample rows to dictionaries
            sample_data = []
            for row in samples:
                row_dict = dict(row._mapping)
                # Convert any non-serializable types to strings
                for key, value in row_dict.items():
                    if hasattr(value, 'isoformat'):  # Handle datetime objects
                        row_dict[key] = value.isoformat()
                sample_data.append(row_dict)

            report[table] = {
                "total_rows": row_count,
                "sample_data": sample_data
            }
        except Exception as e:
            # Log the error but continue with other tables
            report[table] = {
                "error": str(e),
                "total_rows": 0,
                "sample_data": []
            }

    return report