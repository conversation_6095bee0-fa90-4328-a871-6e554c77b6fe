"""API routes for D3FEND ontology data."""
from fastapi import APIRouter, Depends, HTTPException, Query, status, UploadFile, File
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from typing import List, Optional
from datetime import datetime
import urllib.parse
import logging
import tempfile
import os
from rdflib import Graph, URIRef, RDFS, RDF, OWL
from fastapi.encoders import jsonable_encoder

from api.database import get_db, DatabaseError
from api.models.d3fend import (
    D3FENDVersion, D3FENDConcept, d3fend_relationships,
    D3FENDClass, D3FENDProperty, D3FENDCountermeasure
)
from api.utils.i18n import _

router = APIRouter()
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

@router.get("/stats")
def get_d3fend_stats(db: Session = Depends(get_db)):
    """Get statistics about the D3FEND data in the database."""
    try:
        # Get version count
        from sqlalchemy import func
        versions_count = db.query(func.count(D3FENDVersion.id)).scalar()

        # Get concept count
        concepts_count = db.query(func.count(D3FENDConcept.id)).scalar()

        # Get relationship count
        relationships_count = db.query(func.count(d3fend_relationships.c.source_id)).scalar()

        # Get current version info
        current_version = db.query(D3FENDVersion).filter(D3FENDVersion.is_current == True).first()

        # Get current version concept count
        current_version_concepts = 0
        if current_version:
            current_version_concepts = (
                db.query(func.count(D3FENDConcept.id))
                .filter(D3FENDConcept.version_id == current_version.id)
                .scalar()
            )

        return {
            "total_versions": versions_count,
            "total_concepts": concepts_count,
            "total_relationships": relationships_count,
            "current_version": current_version.version if current_version else None,
            "current_version_concepts": current_version_concepts
        }
    except Exception as e:
        logger.error(f"Error retrieving D3FEND statistics: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=_("Error retrieving D3FEND statistics")
        )

@router.get("/version")
def get_version_info(db: Session = Depends(get_db)):
    """Get current D3FEND version information."""
    version = db.query(D3FENDVersion).filter_by(is_current=True).first()
    if not version:
        raise HTTPException(
            status_code=404,
            detail=_("No D3FEND version found")
        )
    return {"current_version": version.version}

@router.get("/concept/{concept_id}")
def get_concept_by_id(concept_id: int, db: Session = Depends(get_db)):
    """Get a specific D3FEND concept by ID."""
    concept = db.query(D3FENDConcept).get(concept_id)
    if not concept:
        raise HTTPException(
            status_code=404,
            detail=_("Concept not found")
        )
    return jsonable_encoder(concept)

@router.get("/concept")
def get_concept_by_uri(uri: str, db: Session = Depends(get_db)):
    """Get a specific D3FEND concept by URI."""
    try:
        concept = db.query(D3FENDConcept).filter(D3FENDConcept.uri == uri).first()
        if not concept:
            raise HTTPException(
                status_code=404,
                detail=_("Concept not found")
            )
        return jsonable_encoder(concept)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving concept by URI: {e}", exc_info=True)
        raise HTTPException(
            status_code=404,
            detail=_("Concept not found")
        )

@router.get("/concepts")
def list_concepts(
    search: Optional[str] = None,
    page: int = Query(1, gt=0),
    size: int = Query(10, gt=0, le=100),
    db: Session = Depends(get_db)
):
    """List D3FEND concepts with optional filtering and pagination."""
    try:
        query = db.query(D3FENDConcept)

        # Get current version by default
        current_version = db.query(D3FENDVersion).filter(D3FENDVersion.is_current == True).first()
        if current_version:
            query = query.filter(D3FENDConcept.version_id == current_version.id)

        # Apply search filter if provided
        if search:
            query = query.filter(D3FENDConcept.name.ilike(f"%{search}%"))

        # Return just the concepts array for all cases
        concepts = query.offset((page - 1) * size).limit(size).all()
        return jsonable_encoder(concepts)

    except Exception as e:
        logger.error(f"Error retrieving concepts: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=_("Error retrieving concepts")
        )

@router.get("/versions")
def list_d3fend_versions(db: Session = Depends(get_db)):
    """List all imported D3FEND ontology versions."""
    versions = db.query(D3FENDVersion).all()
    return jsonable_encoder(versions)

@router.get("/concepts/{concept_uri:path}")
def get_concept(concept_uri: str, db: Session = Depends(get_db)):
    """Get a specific D3FEND concept by URI."""
    try:
        decoded_uri = urllib.parse.unquote(concept_uri)
        concept = db.query(D3FENDConcept).filter(D3FENDConcept.uri == decoded_uri).first()
        if not concept:
            raise HTTPException(
                status_code=404, 
                detail=_("Concept not found")
            )
        return jsonable_encoder(concept)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving concept: {e}", exc_info=True)
        raise HTTPException(
            status_code=404,
            detail=_("Concept not found")
        )

@router.get("/relationships/{source_uri:path}")
def get_concept_relationships(
    source_uri: str,
    relationship_type: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get relationships for a specific D3FEND concept."""
    try:
        decoded_uri = urllib.parse.unquote(source_uri)
        source = db.query(D3FENDConcept).filter(D3FENDConcept.uri == decoded_uri).first()
        if not source:
            raise HTTPException(
                status_code=404,
                detail=_("Concept not found")
            )

        query = (
            db.query(
                d3fend_relationships,
                D3FENDConcept.uri.label('target_uri'),
                D3FENDConcept.name.label('target_name')
            )
            .join(
                D3FENDConcept,
                d3fend_relationships.c.target_id == D3FENDConcept.id
            )
            .filter(d3fend_relationships.c.source_id == source.id)
        )

        if relationship_type:
            query = query.filter(d3fend_relationships.c.relationship_type == relationship_type)

        relationships = query.all()
        result = [
            {
                "source_uri": decoded_uri,
                "target_uri": rel.target_uri,
                "target_name": rel.target_name,
                "relationship_type": rel.relationship_type
            }
            for rel in relationships
        ]

        return jsonable_encoder(result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving relationships: {e}", exc_info=True)
        raise HTTPException(
            status_code=404,
            detail=_("Concept not found")
        )

@router.post("/import/owl")
async def import_owl_ontology(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
) -> dict:
    """Import D3FEND ontology from an OWL file."""
    try:
        logger.info(f"Starting OWL file import: {file.filename}")

        if not file.filename.endswith('.owl'):
            logger.error("Invalid file type - must be .owl")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=_("Only OWL files are supported")
            )

        # Save uploaded file temporarily
        logger.debug("Creating temporary file")
        with tempfile.NamedTemporaryFile(delete=False, suffix='.owl') as temp_file:
            temp_path = temp_file.name
            content = await file.read()
            temp_file.write(content)
            logger.debug(f"Temporary file created at: {temp_path}")

        try:
            # Parse OWL file
            logger.debug("Parsing OWL file with RDFlib")
            g = Graph()
            g.parse(temp_path, format='xml')
            logger.info(f"Successfully parsed OWL file: {len(g)} triples found")

            # Start transaction
            logger.debug("Starting database transaction")

            # Create new version record
            version = D3FENDVersion(
                version=datetime.now().strftime("%Y.%m.%d"),
                import_date=datetime.utcnow(),
                is_current=True
            )
            logger.debug("Creating new D3FEND version record")
            db.add(version)

            # Set all other versions as non-current
            db.query(D3FENDVersion).filter(
                D3FENDVersion.id != version.id
            ).update({"is_current": False})

            # Import concepts
            logger.debug("Importing D3FEND concepts")
            concept_count = 0
            for s, p, o in g.triples((None, RDFS.label, None)):
                if isinstance(s, URIRef):
                    concept = D3FENDConcept(
                        uri=str(s),
                        name=str(o),
                        type='concept',
                        version_id=version.id,
                        definition=str(g.value(s, RDFS.comment)) if g.value(s, RDFS.comment) else None
                    )
                    db.add(concept)
                    concept_count += 1
                    if concept_count % 100 == 0:
                        logger.debug(f"Imported {concept_count} concepts")

            # Import classes
            logger.debug("Importing D3FEND classes")
            class_count = 0
            for s, p, o in g.triples((None, RDF.type, OWL.Class)):
                if isinstance(s, URIRef):
                    d3f_class = D3FENDClass(
                        uri=str(s),
                        name=str(s).split('#')[-1],
                        description=str(g.value(s, RDFS.comment)) if g.value(s, RDFS.comment) else None
                    )
                    db.add(d3f_class)
                    class_count += 1
                    if class_count % 100 == 0:
                        logger.debug(f"Imported {class_count} classes")

            # Import properties
            logger.debug("Importing D3FEND properties")
            prop_count = 0
            for s, p, o in g.triples((None, RDF.type, OWL.ObjectProperty)):
                if isinstance(s, URIRef):
                    d3f_prop = D3FENDProperty(
                        uri=str(s),
                        name=str(s).split('#')[-1],
                        property_type='object',
                        description=str(g.value(s, RDFS.comment)) if g.value(s, RDFS.comment) else None
                    )
                    db.add(d3f_prop)
                    prop_count += 1
                    if prop_count % 100 == 0:
                        logger.debug(f"Imported {prop_count} properties")

            logger.debug("Committing changes to database")
            db.commit()
            logger.info(f"Successfully imported D3FEND ontology: {concept_count} concepts, {class_count} classes, {prop_count} properties")

            return {
                "status": "success",
                "message": _("Successfully imported D3FEND ontology"),
                "stats": {
                    "concepts": concept_count,
                    "classes": class_count,
                    "properties": prop_count
                }
            }

        except SQLAlchemyError as e:
            logger.error(f"Database error during OWL import: {str(e)}", exc_info=True)
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=_("Database error during OWL import")
            )
        except Exception as e:
            logger.error(f"Error processing OWL file: {str(e)}", exc_info=True)
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=_("Error processing OWL file")
            )
        finally:
            # Clean up temporary file
            logger.debug(f"Cleaning up temporary file: {temp_path}")
            os.remove(temp_path)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in OWL import endpoint: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("Error importing OWL file")
        )

@router.get("/classes")
async def list_classes(
    search: Optional[str] = None,
    page: int = Query(1, gt=0),
    size: int = Query(10, gt=0, le=100),
    db: Session = Depends(get_db)
):
    """List D3FEND classes with optional filtering and pagination."""
    try:
        query = db.query(D3FENDClass)

        # Apply search filter if provided
        if search:
            query = query.filter(D3FENDClass.name.ilike(f"%{search}%"))

        total = query.count()
        classes = query.offset((page - 1) * size).limit(size).all()

        return {
            "items": [jsonable_encoder(cls) for cls in classes],
            "total": total,
            "page": page,
            "size": size
        }
    except Exception as e:
        logger.error(f"Error retrieving classes: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("Error retrieving classes")
        )

@router.get("/properties")
async def list_properties(
    property_type: Optional[str] = None,
    page: int = Query(1, gt=0),
    size: int = Query(10, gt=0, le=100),
    db: Session = Depends(get_db)
):
    """List D3FEND properties with optional filtering and pagination."""
    try:
        query = db.query(D3FENDProperty)

        if property_type:
            if property_type not in ['object', 'datatype']:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=_("Invalid property type. Must be 'object' or 'datatype'")
                )
            query = query.filter(D3FENDProperty.property_type == property_type)

        total = query.count()
        properties = query.offset((page - 1) * size).limit(size).all()

        return {
            "items": [jsonable_encoder(prop) for prop in properties],
            "total": total,
            "page": page,
            "size": size
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving properties: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("Error retrieving properties")
        )

@router.get("/countermeasures")
async def list_countermeasures(
    page: int = Query(1, gt=0),
    size: int = Query(10, gt=0, le=100),
    db: Session = Depends(get_db)
):
    """List D3FEND countermeasures with pagination."""
    try:
        query = db.query(D3FENDCountermeasure)
        total = query.count()
        countermeasures = query.offset((page - 1) * size).limit(size).all()

        return {
            "items": [jsonable_encoder(cm) for cm in countermeasures],
            "total": total,
            "page": page,
            "size": size
        }
    except Exception as e:
        logger.error(f"Error retrieving countermeasures: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("Error retrieving countermeasures")
        )