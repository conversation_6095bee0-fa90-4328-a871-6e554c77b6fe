"""
Environment API routes.

This module defines the API routes for managing environments in the RegressionRigor platform.
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session

from api.database import get_db
from api.models.schemas.environment import (
    Environment,
    EnvironmentCreate,
    EnvironmentUpdate,
    EnvironmentWithAssessments
)
from api.models.schemas.assessment import AssessmentBase
from api.services import environment as environment_service
from api.dependencies import get_current_user, get_current_active_user, admin_only


router = APIRouter(prefix="/environments", tags=["Environments"])


@router.get("/", response_model=List[Environment])
async def get_environments(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=100, description="Maximum number of records to return"),
    status: Optional[str] = Query(None, description="Filter by environment status"),
    search: Optional[str] = Query(None, description="Search term to filter environments"),
    include_deleted: bool = Query(False, description="Whether to include soft-deleted environments"),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_user)
):
    """
    Retrieve a list of environments with optional filtering.
    
    Regular users can only see environments they created.
    Admin users can see all environments.
    """
    # Check if user is admin
    is_admin = current_user.get("role") == "admin"
    
    # If not admin, filter by created_by
    created_by = None if is_admin else current_user.get("id")
    
    environments = environment_service.get_environments(
        db=db,
        skip=skip,
        limit=limit,
        status=status,
        search=search,
        created_by=created_by,
        include_deleted=include_deleted and is_admin  # Only admins can see deleted environments
    )
    
    return environments


@router.get("/{environment_id}", response_model=Environment)
async def get_environment(
    environment_id: int = Path(..., ge=1, description="ID of the environment to retrieve"),
    include_deleted: bool = Query(False, description="Whether to include soft-deleted environments"),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_user)
):
    """
    Retrieve a specific environment by ID.
    
    Regular users can only access environments they created.
    Admin users can access any environment.
    """
    environment = environment_service.get_environment_by_id(
        db=db,
        environment_id=environment_id,
        include_deleted=include_deleted and current_user.get("role") == "admin"
    )
    
    if not environment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Environment with ID {environment_id} not found"
        )
    
    # Check if user has access to this environment
    if environment.created_by != current_user.get("id") and current_user.get("role") != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to access this environment"
        )
    
    return environment


@router.post("/", response_model=Environment, status_code=status.HTTP_201_CREATED)
async def create_environment(
    environment_data: EnvironmentCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_user)
):
    """
    Create a new environment.
    
    Users must have the "create_environment" permission.
    """
    # Check if user has permission to create environments
    if current_user.get("role") not in ["admin", "analyst"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to create environments"
        )
    
    environment = environment_service.create_environment(
        db=db,
        environment_data=environment_data,
        user_id=current_user.get("id")
    )
    
    return environment


@router.put("/{environment_id}", response_model=Environment)
async def update_environment(
    environment_data: EnvironmentUpdate,
    environment_id: int = Path(..., ge=1, description="ID of the environment to update"),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_user)
):
    """
    Update an existing environment.
    
    Regular users can only update environments they created.
    Admin users can update any environment.
    """
    # Check if environment exists
    environment = environment_service.get_environment_by_id(db=db, environment_id=environment_id)
    
    if not environment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Environment with ID {environment_id} not found"
        )
    
    # Check if user has permission to update this environment
    if environment.created_by != current_user.get("id") and current_user.get("role") != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to update this environment"
        )
    
    updated_environment = environment_service.update_environment(
        db=db,
        environment_id=environment_id,
        environment_data=environment_data
    )
    
    return updated_environment


@router.delete("/{environment_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_environment(
    environment_id: int = Path(..., ge=1, description="ID of the environment to delete"),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_user)
):
    """
    Soft-delete an environment.
    
    Regular users can only delete environments they created.
    Admin users can delete any environment.
    """
    # Check if environment exists
    environment = environment_service.get_environment_by_id(db=db, environment_id=environment_id)
    
    if not environment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Environment with ID {environment_id} not found"
        )
    
    # Check if user has permission to delete this environment
    if environment.created_by != current_user.get("id") and current_user.get("role") != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to delete this environment"
        )
    
    environment_service.delete_environment(db=db, environment_id=environment_id)
    
    return None


@router.post("/{environment_id}/restore", response_model=Environment)
async def restore_environment(
    environment_id: int = Path(..., ge=1, description="ID of the environment to restore"),
    db: Session = Depends(get_db),
    current_user: dict = Depends(admin_only)
):
    """
    Restore a soft-deleted environment.
    
    Only admin users can restore environments.
    """
    environment = environment_service.restore_environment(db=db, environment_id=environment_id)
    
    if not environment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Environment with ID {environment_id} not found or not deleted"
        )
    
    return environment


@router.get("/{environment_id}/assessments", response_model=List[AssessmentBase])
async def get_environment_assessments(
    environment_id: int = Path(..., ge=1, description="ID of the environment"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=100, description="Maximum number of records to return"),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_user)
):
    """
    Retrieve assessments associated with an environment.
    
    Regular users can only access assessments for environments they created.
    Admin users can access assessments for any environment.
    """
    # Check if environment exists
    environment = environment_service.get_environment_by_id(db=db, environment_id=environment_id)
    
    if not environment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Environment with ID {environment_id} not found"
        )
    
    # Check if user has permission to access this environment
    if environment.created_by != current_user.get("id") and current_user.get("role") != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to access this environment"
        )
    
    assessments = environment_service.get_environment_assessments(
        db=db,
        environment_id=environment_id,
        skip=skip,
        limit=limit
    )
    
    return assessments


@router.post("/{environment_id}/deprecate", response_model=Environment)
async def deprecate_environment(
    environment_id: int = Path(..., ge=1, description="ID of the environment to deprecate"),
    db: Session = Depends(get_db),
    current_user: dict = Depends(admin_only)
):
    """
    Mark an environment as deprecated.
    
    Only admin users can deprecate environments.
    """
    environment = environment_service.deprecate_environment(
        db=db,
        environment_id=environment_id,
        user_id=current_user.get("id")
    )
    
    if not environment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Environment with ID {environment_id} not found"
        )
    
    return environment


@router.post("/{environment_id}/revoke", response_model=Environment)
async def revoke_environment(
    environment_id: int = Path(..., ge=1, description="ID of the environment to revoke"),
    db: Session = Depends(get_db),
    current_user: dict = Depends(admin_only)
):
    """
    Revoke an environment.
    
    Only admin users can revoke environments.
    """
    environment = environment_service.revoke_environment(
        db=db,
        environment_id=environment_id,
        user_id=current_user.get("id")
    )
    
    if not environment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Environment with ID {environment_id} not found"
        )
    
    return environment 