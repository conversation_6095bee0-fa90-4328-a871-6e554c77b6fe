"""API routes for MITRE Defense data."""
from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from typing import List, Optional, Dict
from datetime import datetime
import json
import os
import logging
from pathlib import Path

from api.database import get_db
from api.models.mitre_defense import MitreDefenseVersion, MitreControl
from api.models.mitre import MitreTechnique
from api.schemas import PaginatedResponse
from api.utils.i18n import _
from api.threat_defense.mapper import ThreatDefenseMapper

logger = logging.getLogger(__name__)
router = APIRouter()

def generate_coverage_mermaid(
    techniques: List[str],
    coverage_data: Dict[str, float],
    countermeasures: Dict[str, List[Dict]]
) -> str:
    """Generate a mermaid flowchart for attack path coverage visualization."""
    mermaid = []

    # Start flowchart
    mermaid.append("flowchart TD")

    # Attack Techniques subgraph
    mermaid.append("    subgraph \"Attack Techniques\"")

    # Add technique nodes and connections
    prev_tech = None
    for tech_id in techniques:
        tech = MitreTechnique.query.filter_by(technique_id=tech_id).first()
        if not tech:
            continue

        coverage = coverage_data.get(tech_id, 0.0) * 100
        node_id = tech_id.replace('.', '_')

        # Node definition with coverage
        mermaid.append(f"        {node_id}[\"{tech_id}: {tech.name}\\n(Coverage: {coverage:.0f}%)\"]")

        # Connect to previous technique
        if prev_tech:
            mermaid.append(f"        {prev_tech} --> {node_id}")
        prev_tech = node_id

    mermaid.append("    end")

    # D3FEND Countermeasures subgraph
    mermaid.append("\n    subgraph \"D3FEND Countermeasures\"")

    # Add countermeasure nodes and relationships
    defense_count = 1
    for tech_id in techniques:
        measures = countermeasures.get(tech_id, [])
        for measure in measures:
            defense_id = f"D{defense_count}"
            name = measure.get('name', 'Unknown Defense')
            mermaid.append(f"        {defense_id}[\"{name}\"]")
            mermaid.append(f"        {defense_id} -.-> {tech_id.replace('.', '_')}")
            defense_count += 1

    mermaid.append("    end")

    # Add styling classes
    mermaid.extend([
        "\n    classDef highCoverage fill:#a3cfbb,stroke:#333,stroke-width:1px;",
        "    classDef mediumCoverage fill:#ffe6b3,stroke:#333,stroke-width:1px;",
        "    classDef lowCoverage fill:#ffb3b3,stroke:#333,stroke-width:1px;",
        "    classDef defense fill:#b3d1ff,stroke:#333,stroke-width:1px;"
    ])

    # Apply classes based on coverage
    for tech_id in techniques:
        coverage = coverage_data.get(tech_id, 0.0) * 100
        node_id = tech_id.replace('.', '_')
        if coverage >= 80:
            mermaid.append(f"    class {node_id} highCoverage;")
        elif coverage >= 50:
            mermaid.append(f"    class {node_id} mediumCoverage;")
        else:
            mermaid.append(f"    class {node_id} lowCoverage;")

    # Apply defense class to all countermeasures
    defense_ids = [f"D{i}" for i in range(1, defense_count)]
    if defense_ids:
        mermaid.append(f"    class {','.join(defense_ids)} defense;")

    return "\n".join(mermaid)

@router.post("/coverage/visualization")
async def get_attack_path_coverage_visualization(
    technique_sequence: List[str],
    db: Session = Depends(get_db)
) -> Dict[str, str]:
    """Generate a mermaid visualization for attack path coverage analysis.

    Args:
        technique_sequence: Ordered list of technique IDs representing an attack path

    Returns:
        Dict containing the mermaid chart definition
    """
    try:
        mapper = ThreatDefenseMapper(db)

        # Get coverage analysis
        coverage = mapper.get_attack_path_coverage(technique_sequence)

        # Get countermeasures for each technique
        countermeasures = mapper.find_d3fend_countermeasures(technique_sequence)

        # Generate mermaid chart
        mermaid_chart = generate_coverage_mermaid(
            technique_sequence,
            coverage["techniques"],
            countermeasures
        )

        return {
            "mermaid_chart": mermaid_chart,
            "overall_coverage": coverage["overall_coverage"],
            "average_coverage": coverage["average_coverage"]
        }

    except Exception as e:
        logger.error(f"Error generating coverage visualization: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("Error generating coverage visualization")
        )

@router.get("/versions")
async def list_defense_versions(
    db: Session = Depends(get_db)
) -> List[Dict]:
    """List all imported MITRE Defense data versions."""
    try:
        versions = db.query(MitreDefenseVersion).all()
        if not versions:
            return []  # Return empty list instead of raising 404
        return [{
            "id": v.id,
            "version": v.version,
            "import_date": v.import_date,
            "is_current": v.is_current
        } for v in versions]
    except SQLAlchemyError as e:
        logger.error(f"Database error listing versions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("Database error occurred")
        )

@router.get("/controls")
async def list_controls(
    version_id: Optional[int] = None,
    page: int = Query(1, gt=0),
    size: int = Query(10, gt=0, le=100),
    db: Session = Depends(get_db)
) -> PaginatedResponse:
    """List MITRE Defense controls with pagination."""
    try:
        query = db.query(MitreControl)

        if version_id:
            version = db.query(MitreDefenseVersion).filter_by(id=version_id).first()
            if not version:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=_("Version not found")
                )
            query = query.filter(MitreControl.version_id == version_id)
        else:
            # Get current version by default
            current_version = db.query(MitreDefenseVersion).filter(
                MitreDefenseVersion.is_current == True
            ).first()
            if current_version:
                query = query.filter(MitreControl.version_id == current_version.id)

        total = query.count()
        controls = query.offset((page - 1) * size).limit(size).all()

        return PaginatedResponse(
            items=[{
                "id": c.id,
                "external_id": c.external_id,
                "name": c.name,
                "description": c.description,
                "version_id": c.version_id,
                "created": c.created,
                "modified": c.modified
            } for c in controls],
            total=total,
            page=page,
            size=size
        )
    except SQLAlchemyError as e:
        logger.error(f"Database error listing controls: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("Database error occurred")
        )

@router.get("/controls/{external_id}")
async def get_control(
    external_id: str,
    db: Session = Depends(get_db)
) -> Dict:
    """Get a specific MITRE Defense control by external ID."""
    try:
        control = db.query(MitreControl).filter_by(external_id=external_id).first()
        if not control:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=_("Control not found")
            )
        return {
            "id": control.id,
            "external_id": control.external_id,
            "name": control.name,
            "description": control.description,
            "version_id": control.version_id,
            "created": control.created,
            "modified": control.modified
        }
    except SQLAlchemyError as e:
        logger.error(f"Database error getting control {external_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("Database error occurred")
        )
@router.post("/import")
async def import_defense_data(
    version: Optional[str] = None,
    set_as_current: bool = True,
    force: bool = False,
    db: Session = Depends(get_db)
) -> Dict:
    """Import MITRE Defense data from JSON file.

    Args:
        version: Optional version string override
        set_as_current: Whether to set this as the current version
        force: Whether to overwrite existing version data
    """
    try:
        defense_file = Path("data/mitre/defense-controls.json")
        if not defense_file.is_file():
            logger.error(f"Defense controls file not found: {defense_file}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=_("Defense controls data file not found")
            )

        try:
            with defense_file.open('r') as f:
                data = json.load(f)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in defense controls file: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=_("Invalid JSON in defense controls file")
            )
        except IOError as e:
            logger.error(f"Error reading defense controls file: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=_("Error reading defense controls file")
            )

        # Create new version record
        version_str = version or datetime.now().strftime("%Y.%m.%d")

        # Check if version already exists
        existing_version = db.query(MitreDefenseVersion).filter_by(version=version_str).first()
        if existing_version and not force:
            return {
                "status": "error",
                "message": _("Version already exists"),
                "detail": f"Version {version_str} already exists. Use force=True to overwrite."
            }

        if existing_version and force:
            # Delete existing controls for this version
            db.query(MitreControl).filter_by(version_id=existing_version.id).delete()
            db_version = existing_version
            db_version.import_date = datetime.utcnow()
            db_version.is_current = set_as_current
        else:
            db_version = MitreDefenseVersion(
                version=version_str,
                import_date=datetime.utcnow(),
                is_current=set_as_current
            )
            db.add(db_version)

        if set_as_current:
            # Unset current flag on other versions
            db.query(MitreDefenseVersion).filter(
                MitreDefenseVersion.id != db_version.id
            ).update({"is_current": False})

        db.flush()  # Get version ID

        # Import controls
        control_count = 0
        for control in data.get("controls", []):
            try:
                db_control = MitreControl(
                    external_id=control["id"],
                    name=control["name"],
                    description=control.get("description", ""),
                    version_id=db_version.id,
                    created=datetime.utcnow(),
                    modified=datetime.utcnow()
                )
                db.add(db_control)
                control_count += 1
            except Exception as e:
                logger.error(f"Error importing control {control.get('id')}: {str(e)}")
                continue

        db.commit()
        logger.info(f"Successfully imported {control_count} controls for version {version_str}")

        return {
            "status": "success",
            "message": _("Import completed successfully"),
            "detail": f"Successfully imported {control_count} defense controls"
        }

    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"Database error during import: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("Database error during import")
        )
    except Exception as e:
        logger.error(f"Unexpected error during import: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=_("Unexpected error during import")
        )