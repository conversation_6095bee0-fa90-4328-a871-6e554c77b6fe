"""Campaign management API endpoints.

This module provides API endpoints for managing security testing campaigns,
including creating, retrieving, updating, and deleting campaigns.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Security, Query
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from api.database import get_db
from api.models.base import CampaignDB
from api.models.schemas import CampaignCreate, Campaign
from api.models.user import User
from api.dependencies import get_current_user, get_current_active_user
from api.utils.rate_limiter import standard_rate_limit
from api.utils.error_handler import log_error

router = APIRouter(prefix="/api/v1/campaigns", tags=["campaigns"])

# Custom error handler for FastAPI routes
def fastapi_error_handler(func):
    """Decorator to handle SQLAlchemy errors in FastAPI routes."""
    from functools import wraps
    
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except SQLAlchemyError as e:
            # Log the error
            log_error(e, endpoint=func.__name__, request_data=kwargs)
            # Return a user-friendly error response
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="A database error occurred. Our team has been notified."
            )
        except Exception as e:
            # Log the error
            log_error(e, endpoint=func.__name__, request_data=kwargs)
            # Re-raise the exception
            raise
    
    return wrapper

@router.post("/", response_model=Campaign, status_code=status.HTTP_201_CREATED)
@fastapi_error_handler
async def create_campaign(
    campaign_data: CampaignCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Create a new security testing campaign.
    
    Args:
        campaign_data: The campaign data to create
        db: Database session
        current_user: The authenticated user
        
    Returns:
        The created campaign
    """
    # Check if user has permission to create campaigns
    if not current_user.is_admin() and not current_user.is_analyst():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to create campaigns"
        )
    
    # Create the campaign
    campaign = CampaignDB(
        name=campaign_data.name,
        description=campaign_data.description,
        status=campaign_data.status
    )
    
    db.add(campaign)
    db.commit()
    db.refresh(campaign)
    
    return campaign

@router.get("/", response_model=List[Campaign])
@fastapi_error_handler
async def get_campaigns(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Get all campaigns with optional filtering.
    
    Args:
        skip: Number of records to skip
        limit: Maximum number of records to return
        status: Optional filter by campaign status
        db: Database session
        current_user: The authenticated user
        
    Returns:
        List of campaigns
    """
    query = db.query(CampaignDB).filter(CampaignDB.not_deleted())
    
    # Apply status filter if provided
    if status:
        query = query.filter(CampaignDB.status == status)
    
    # Apply pagination
    campaigns = query.offset(skip).limit(limit).all()
    
    return campaigns

@router.get("/{campaign_id}", response_model=Campaign)
@fastapi_error_handler
async def get_campaign(
    campaign_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Get a specific campaign by ID.
    
    Args:
        campaign_id: The ID of the campaign to retrieve
        db: Database session
        current_user: The authenticated user
        
    Returns:
        The requested campaign
    """
    campaign = db.query(CampaignDB).filter(
        CampaignDB.id == campaign_id,
        CampaignDB.not_deleted()
    ).first()
    
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Campaign with ID {campaign_id} not found"
        )
    
    return campaign

@router.put("/{campaign_id}", response_model=Campaign)
@fastapi_error_handler
async def update_campaign(
    campaign_id: int,
    campaign_data: CampaignCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Update a specific campaign.
    
    Args:
        campaign_id: The ID of the campaign to update
        campaign_data: The updated campaign data
        db: Database session
        current_user: The authenticated user
        
    Returns:
        The updated campaign
    """
    # Check if user has permission to update campaigns
    if not current_user.is_admin() and not current_user.is_analyst():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update campaigns"
        )
    
    campaign = db.query(CampaignDB).filter(
        CampaignDB.id == campaign_id,
        CampaignDB.not_deleted()
    ).first()
    
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Campaign with ID {campaign_id} not found"
        )
    
    # Update campaign fields
    campaign.name = campaign_data.name
    campaign.description = campaign_data.description
    campaign.status = campaign_data.status
    
    db.add(campaign)
    db.commit()
    db.refresh(campaign)
    
    return campaign

@router.delete("/{campaign_id}", status_code=status.HTTP_204_NO_CONTENT)
@fastapi_error_handler
async def delete_campaign(
    campaign_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Delete (soft-delete) a campaign.
    
    Args:
        campaign_id: The ID of the campaign to delete
        db: Database session
        current_user: The authenticated user
    """
    # Check if user has permission to delete campaigns
    if not current_user.is_admin():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete campaigns"
        )
    
    campaign = db.query(CampaignDB).filter(
        CampaignDB.id == campaign_id,
        CampaignDB.not_deleted()
    ).first()
    
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Campaign with ID {campaign_id} not found"
        )
    
    # Soft delete the campaign
    campaign.soft_delete(db)
    
    return None

@router.post("/{campaign_id}/restore", response_model=Campaign)
@fastapi_error_handler
async def restore_campaign(
    campaign_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Restore a soft-deleted campaign.
    
    Args:
        campaign_id: The ID of the campaign to restore
        db: Database session
        current_user: The authenticated user
        
    Returns:
        The restored campaign
    """
    # Check if user has permission to restore campaigns
    if not current_user.is_admin():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to restore campaigns"
        )
    
    campaign = db.query(CampaignDB).filter(
        CampaignDB.id == campaign_id,
        CampaignDB.deleted_time.isnot(None)
    ).first()
    
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Deleted campaign with ID {campaign_id} not found"
        )
    
    # Restore the campaign
    campaign.deleted_time = None
    
    db.add(campaign)
    db.commit()
    db.refresh(campaign)
    
    return campaign 