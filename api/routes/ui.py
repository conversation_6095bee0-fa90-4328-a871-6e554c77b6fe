"""UI routes for the application."""
from fastapi import API<PERSON><PERSON><PERSON>, Request, Depends
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
import json
import logging
from pathlib import Path

from api.database import get_db
from api.threat_defense.mapper import ThreatDefenseMapper
from components.visualizer import create_attack_path_graph

# Configure logging
logger = logging.getLogger(__name__)

# Configure templates
templates_path = Path(__file__).parent.parent.parent / "src" / "templates"
templates = Jinja2Templates(directory=str(templates_path))

router = APIRouter()

@router.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """Serve the main visualization page."""
    api_docs = {
        "openapi_url": "/api/v1/openapi.json",
        "docs_url": "/api/v1/docs",
        "redoc_url": "/api/v1/redoc"
    }
    return templates.TemplateResponse(
        "base.html",
        {
            "request": request, 
            "error": None, 
            "graph_data": None, 
            "techniques": None,
            "api_docs": api_docs
        }
    )

@router.post("/analyze", response_class=HTMLResponse)
async def analyze_path(request: Request, db: Session = Depends(get_db)):
    """Handle form submission and generate visualization."""
    form_data = await request.form()
    technique_ids = form_data.get("technique_ids", "").split("\n")
    technique_sequence = [tid.strip() for tid in technique_ids if tid.strip()]

    if not technique_sequence:
        return templates.TemplateResponse(
            "base.html",
            {"request": request, "error": "Please enter at least one technique ID"}
        )

    try:
        # Initialize the ThreatDefenseMapper
        mapper = ThreatDefenseMapper(db)
        logger.debug(f"Processing technique sequence: {technique_sequence}")

        # Get countermeasures for each technique
        defense_data = mapper.find_d3fend_countermeasures(technique_sequence)
        logger.debug(f"Retrieved defense data: {defense_data}")

        # Get coverage analysis
        coverage_data = mapper.get_attack_path_coverage(technique_sequence)
        logger.debug(f"Coverage analysis: {coverage_data}")

        # Format data for visualization
        techniques_data = []
        for tech_id in technique_sequence:
            technique_info = {
                'technique': {
                    'id': tech_id,
                    'name': 'Unknown Technique',
                    'description': ''
                },
                'coverage': coverage_data['techniques'].get(tech_id, 0.0),
                'countermeasures': []
            }

            if tech_id in defense_data and defense_data[tech_id]:
                # Get the first defense measure if available
                first_defense = defense_data[tech_id][0] if defense_data[tech_id] else {}
                technique_info['technique'].update({
                    'name': first_defense.get('name', 'Unknown Technique'),
                    'description': first_defense.get('description', '')
                })

                # Add all countermeasures
                technique_info['countermeasures'] = [
                    {
                        'd3fend_id': cm.get('d3fend_id', ''),
                        'name': cm.get('name', 'Unknown Control'),
                        'confidence': cm.get('effectiveness', 0.5)
                    }
                    for cm in defense_data[tech_id]
                ]

            techniques_data.append(technique_info)

        # Create visualization data
        graph_data = create_attack_path_graph(techniques_data)
        logger.debug(f"Generated graph data: {graph_data}")

        # Convert graph_data to JSON string for template rendering
        graph_data_json = json.dumps(graph_data)

        return templates.TemplateResponse(
            "base.html",
            {
                "request": request,
                "error": None,
                "graph_data": graph_data_json,
                "techniques": techniques_data
            }
        )
    except Exception as e:
        logger.error(f"Error analyzing attack path: {e}", exc_info=True)
        return templates.TemplateResponse(
            "base.html",
            {"request": request, "error": str(e)}
        )
