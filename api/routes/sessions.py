"""Session management and device tracking endpoints."""
from typing import List
from datetime import datetime, timezone
from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session

from api.database import get_db
from api.models.user import User
from api.models.session import UserSession, DeviceInfo
from api.auth.dependencies import get_current_user
from api.utils.device import extract_device_info, create_or_update_device_info
from api.schemas.session import (
    SessionResponse,
    SessionCreate,
    SessionList,
    DeviceInfoResponse
)

router = APIRouter()

@router.post("/sessions", response_model=SessionResponse)
async def create_session(
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new session for the authenticated user."""
    # Extract device information from request
    device_data = extract_device_info(request)
    device = create_or_update_device_info(db, device_data)
    
    # Create new session
    session = UserSession(
        user_id=current_user.id,
        device_info_id=device.id,
        is_active=True
    )
    db.add(session)
    db.commit()
    db.refresh(session)
    
    return session

@router.get("/sessions", response_model=SessionList)
async def list_sessions(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """List all active sessions for the current user."""
    active_sessions = current_user.get_active_sessions()
    return {"sessions": active_sessions}

@router.delete("/sessions/{session_id}")
async def terminate_session(
    session_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Terminate a specific session."""
    session = db.query(UserSession).filter(
        UserSession.id == session_id,
        UserSession.user_id == current_user.id
    ).first()
    
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    session.terminate()
    db.commit()
    return {"message": "Session terminated successfully"}

@router.post("/sessions/terminate-others")
async def terminate_other_sessions(
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Terminate all sessions except the current one."""
    current_session = db.query(UserSession).filter(
        UserSession.user_id == current_user.id,
        UserSession.device_info_id == create_or_update_device_info(
            db, extract_device_info(request)
        ).id
    ).first()
    
    if current_session:
        current_user.terminate_other_sessions(current_session.id)
        db.commit()
    
    return {"message": "Other sessions terminated successfully"}
