"""
API endpoints for the Enhanced Testcase Chaining & Sequencing feature.

This module contains the FastAPI routes for managing testcase chains, nodes,
edges, executions, and conditions.
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session

from api.database import get_db
from api.models.schemas.testcase_chaining import (
    TestcaseChain, TestcaseChainCreate,
    TestcaseChainNode, TestcaseChainNodeCreate,
    TestcaseChainEdge, TestcaseChainEdgeCreate,
    TestcaseCondition, TestcaseConditionCreate,
    ChainExecution, ChainExecutionCreate,
    NodeExecution
)
from api.services.testcase_chaining import (
    TestcaseChainService, TestcaseChainNodeService, TestcaseChainEdgeService,
    TestcaseConditionService, ChainExecutionService, NodeExecutionService,
    ChainExecutionEngine
)
from api.dependencies import get_current_user

router = APIRouter(prefix="/api/v1/testcase-chains", tags=["testcase-chains"])


# Chain endpoints
@router.post("/", response_model=TestcaseChain)
def create_chain(
    chain_data: TestcaseChainCreate,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Create a new testcase chain."""
    # Set the created_by field to the current user's ID
    chain_data_dict = chain_data.model_dump()
    chain_data_dict["created_by"] = current_user["id"]
    
    # Create the chain using the service
    chain = TestcaseChainService.create_chain(db, TestcaseChainCreate(**chain_data_dict))
    return chain


@router.get("/", response_model=List[TestcaseChain])
def get_chains(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Get all testcase chains."""
    chains = TestcaseChainService.get_chains(db, skip=skip, limit=limit)
    return chains


@router.get("/{chain_id}", response_model=TestcaseChain)
def get_chain(
    chain_id: int,
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Get a testcase chain by ID."""
    chain = TestcaseChainService.get_chain(db, chain_id)
    if not chain:
        raise HTTPException(status_code=404, detail="Chain not found")
    return chain


@router.put("/{chain_id}", response_model=TestcaseChain)
def update_chain(
    chain_id: int,
    chain_data: Dict[str, Any],
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Update a testcase chain."""
    chain = TestcaseChainService.update_chain(db, chain_id, chain_data)
    if not chain:
        raise HTTPException(status_code=404, detail="Chain not found")
    return chain


@router.delete("/{chain_id}")
def delete_chain(
    chain_id: int,
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Delete a testcase chain."""
    success = TestcaseChainService.delete_chain(db, chain_id)
    if not success:
        raise HTTPException(status_code=404, detail="Chain not found")
    return {"message": "Chain deleted successfully"}


# Node endpoints
@router.post("/nodes", response_model=TestcaseChainNode)
def create_node(
    node_data: TestcaseChainNodeCreate,
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Create a new testcase chain node."""
    # Check if the chain exists
    chain = TestcaseChainService.get_chain(db, node_data.chain_id)
    if not chain:
        raise HTTPException(status_code=404, detail="Chain not found")
    
    # Create the node using the service
    node = TestcaseChainNodeService.create_node(db, node_data)
    return node


@router.get("/nodes/{node_id}", response_model=TestcaseChainNode)
def get_node(
    node_id: int,
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Get a testcase chain node by ID."""
    node = TestcaseChainNodeService.get_node(db, node_id)
    if not node:
        raise HTTPException(status_code=404, detail="Node not found")
    return node


@router.get("/{chain_id}/nodes", response_model=List[TestcaseChainNode])
def get_nodes_by_chain(
    chain_id: int,
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Get all nodes for a specific chain."""
    # Check if the chain exists
    chain = TestcaseChainService.get_chain(db, chain_id)
    if not chain:
        raise HTTPException(status_code=404, detail="Chain not found")
    
    nodes = TestcaseChainNodeService.get_nodes_by_chain(db, chain_id)
    return nodes


@router.put("/nodes/{node_id}", response_model=TestcaseChainNode)
def update_node(
    node_id: int,
    node_data: Dict[str, Any],
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Update a testcase chain node."""
    node = TestcaseChainNodeService.update_node(db, node_id, node_data)
    if not node:
        raise HTTPException(status_code=404, detail="Node not found")
    return node


@router.delete("/nodes/{node_id}")
def delete_node(
    node_id: int,
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Delete a testcase chain node."""
    success = TestcaseChainNodeService.delete_node(db, node_id)
    if not success:
        raise HTTPException(status_code=404, detail="Node not found")
    return {"message": "Node deleted successfully"}


# Edge endpoints
@router.post("/edges", response_model=TestcaseChainEdge)
def create_edge(
    edge_data: TestcaseChainEdgeCreate,
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Create a new testcase chain edge."""
    # Check if the source and target nodes exist
    source_node = TestcaseChainNodeService.get_node(db, edge_data.source_node_id)
    if not source_node:
        raise HTTPException(status_code=404, detail="Source node not found")
    
    target_node = TestcaseChainNodeService.get_node(db, edge_data.target_node_id)
    if not target_node:
        raise HTTPException(status_code=404, detail="Target node not found")
    
    # Create the edge using the service
    edge = TestcaseChainEdgeService.create_edge(db, edge_data)
    return edge


@router.get("/edges/{edge_id}", response_model=TestcaseChainEdge)
def get_edge(
    edge_id: int,
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Get a testcase chain edge by ID."""
    edge = TestcaseChainEdgeService.get_edge(db, edge_id)
    if not edge:
        raise HTTPException(status_code=404, detail="Edge not found")
    return edge


@router.get("/nodes/{node_id}/edges", response_model=List[TestcaseChainEdge])
def get_edges_by_node(
    node_id: int,
    direction: Optional[str] = "both",
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Get all edges connected to a specific node."""
    # Check if the node exists
    node = TestcaseChainNodeService.get_node(db, node_id)
    if not node:
        raise HTTPException(status_code=404, detail="Node not found")
    
    # Validate direction parameter
    if direction not in ["outgoing", "incoming", "both"]:
        raise HTTPException(status_code=400, detail="Invalid direction parameter")
    
    edges = TestcaseChainEdgeService.get_edges_by_node(db, node_id, direction)
    return edges


@router.put("/edges/{edge_id}", response_model=TestcaseChainEdge)
def update_edge(
    edge_id: int,
    edge_data: Dict[str, Any],
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Update a testcase chain edge."""
    edge = TestcaseChainEdgeService.update_edge(db, edge_id, edge_data)
    if not edge:
        raise HTTPException(status_code=404, detail="Edge not found")
    return edge


@router.delete("/edges/{edge_id}")
def delete_edge(
    edge_id: int,
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Delete a testcase chain edge."""
    success = TestcaseChainEdgeService.delete_edge(db, edge_id)
    if not success:
        raise HTTPException(status_code=404, detail="Edge not found")
    return {"message": "Edge deleted successfully"}


# Condition endpoints
@router.post("/conditions", response_model=TestcaseCondition)
def create_condition(
    condition_data: TestcaseConditionCreate,
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Create a new testcase condition."""
    # Create the condition using the service
    condition = TestcaseConditionService.create_condition(db, condition_data)
    return condition


@router.get("/conditions/{condition_id}", response_model=TestcaseCondition)
def get_condition(
    condition_id: int,
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Get a testcase condition by ID."""
    condition = TestcaseConditionService.get_condition(db, condition_id)
    if not condition:
        raise HTTPException(status_code=404, detail="Condition not found")
    return condition


@router.get("/testcases/{testcase_id}/conditions", response_model=List[TestcaseCondition])
def get_conditions_by_testcase(
    testcase_id: int,
    condition_type: Optional[str] = None,
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Get all conditions for a specific testcase."""
    # Validate condition_type parameter if provided
    if condition_type and condition_type not in ["precondition", "postcondition"]:
        raise HTTPException(status_code=400, detail="Invalid condition_type parameter")
    
    conditions = TestcaseConditionService.get_conditions_by_testcase(db, testcase_id, condition_type)
    return conditions


@router.put("/conditions/{condition_id}", response_model=TestcaseCondition)
def update_condition(
    condition_id: int,
    condition_data: Dict[str, Any],
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Update a testcase condition."""
    condition = TestcaseConditionService.update_condition(db, condition_id, condition_data)
    if not condition:
        raise HTTPException(status_code=404, detail="Condition not found")
    return condition


@router.delete("/conditions/{condition_id}")
def delete_condition(
    condition_id: int,
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Delete a testcase condition."""
    success = TestcaseConditionService.delete_condition(db, condition_id)
    if not success:
        raise HTTPException(status_code=404, detail="Condition not found")
    return {"message": "Condition deleted successfully"}


# Execution endpoints
@router.post("/{chain_id}/execute", response_model=ChainExecution)
def execute_chain(
    chain_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Execute a testcase chain."""
    # Check if the chain exists
    chain = TestcaseChainService.get_chain(db, chain_id)
    if not chain:
        raise HTTPException(status_code=404, detail="Chain not found")
    
    # Initialize the chain execution
    chain_execution = ChainExecutionEngine.initialize_chain_execution(
        db, chain_id, current_user["id"]
    )
    if not chain_execution:
        raise HTTPException(status_code=500, detail="Failed to initialize chain execution")
    
    # Process the chain execution in the background
    background_tasks.add_task(
        ChainExecutionEngine.process_chain_execution,
        db, chain_execution.id
    )
    
    return chain_execution


@router.get("/executions/{execution_id}", response_model=ChainExecution)
def get_execution(
    execution_id: int,
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Get a chain execution by ID."""
    execution = ChainExecutionService.get_execution(db, execution_id)
    if not execution:
        raise HTTPException(status_code=404, detail="Execution not found")
    return execution


@router.get("/{chain_id}/executions", response_model=List[ChainExecution])
def get_executions_by_chain(
    chain_id: int,
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Get all executions for a specific chain."""
    # Check if the chain exists
    chain = TestcaseChainService.get_chain(db, chain_id)
    if not chain:
        raise HTTPException(status_code=404, detail="Chain not found")
    
    executions = ChainExecutionService.get_executions_by_chain(db, chain_id)
    return executions


@router.get("/executions/{execution_id}/nodes", response_model=List[NodeExecution])
def get_node_executions(
    execution_id: int,
    db: Session = Depends(get_db),
    _: Dict[str, Any] = Depends(get_current_user)
):
    """Get all node executions for a specific chain execution."""
    # Check if the execution exists
    execution = ChainExecutionService.get_execution(db, execution_id)
    if not execution:
        raise HTTPException(status_code=404, detail="Execution not found")
    
    node_executions = NodeExecutionService.get_executions_by_chain_execution(db, execution_id)
    return node_executions 