"""
API routes for the Advanced Soft Deletion Framework.

This module provides REST API endpoints for managing soft deletion policies,
audit logs, scheduled operations, and related functionality.
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from api.auth.dependencies import get_current_user
from api.database import get_db
from api.models.soft_deletion import (
    SoftDeletionAudit,
    SoftDeletionNotification,
    SoftDeletionPolicy,
    SoftDeletionSchedule,
)
from api.models.user import User
from api.schemas.soft_deletion import (
    BulkOperationResponse,
    SoftDeletionAudit as SoftDeletionAuditSchema,
    SoftDeletionNotification as SoftDeletionNotificationSchema,
    SoftDeletionOperationResponse,
    SoftDeletionPolicy as SoftDeletionPolicySchema,
    SoftDeletionPolicyCreate,
    SoftDeletionPolicyUpdate,
    SoftDeletionSchedule as SoftDeletionScheduleSchema,
    SoftDeletionStatsResponse,
)
from api.services.soft_deletion_service import SoftDeletionService

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/policies", response_model=List[SoftDeletionPolicySchema])
async def list_soft_deletion_policies(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    entity_type: Optional[str] = Query(None, description="Filter by entity type"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    List soft deletion policies.
    
    Returns a list of soft deletion policies with optional filtering.
    """
    query = db.query(SoftDeletionPolicy)
    
    if entity_type:
        query = query.filter(SoftDeletionPolicy.entity_type == entity_type)
    
    policies = query.offset(skip).limit(limit).all()
    return policies


@router.post("/policies", response_model=SoftDeletionPolicySchema, status_code=status.HTTP_201_CREATED)
async def create_soft_deletion_policy(
    policy_data: SoftDeletionPolicyCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Create a new soft deletion policy.
    
    Creates a policy that defines retention and purging behavior for a specific entity type.
    """
    # Check if policy already exists for this entity type
    existing_policy = db.query(SoftDeletionPolicy).filter(
        SoftDeletionPolicy.entity_type == policy_data.entity_type
    ).first()
    
    if existing_policy:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Policy already exists for entity type '{policy_data.entity_type}'"
        )
    
    service = SoftDeletionService(db)
    policy = service.create_policy(
        entity_type=policy_data.entity_type,
        retention_period_days=policy_data.retention_period_days,
        auto_purge_enabled=policy_data.auto_purge_enabled,
        cascade_deletion=policy_data.cascade_deletion,
        notification_enabled=policy_data.notification_enabled,
        notification_days_before=policy_data.notification_days_before,
        description=policy_data.description,
        created_by=current_user.id
    )
    
    logger.info(f"Created soft deletion policy for {policy_data.entity_type} by user {current_user.id}")
    return policy


@router.get("/policies/{policy_id}", response_model=SoftDeletionPolicySchema)
async def get_soft_deletion_policy(
    policy_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get a specific soft deletion policy by ID.
    """
    policy = db.query(SoftDeletionPolicy).filter(SoftDeletionPolicy.id == policy_id).first()
    
    if not policy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Soft deletion policy with ID {policy_id} not found"
        )
    
    return policy


@router.put("/policies/{policy_id}", response_model=SoftDeletionPolicySchema)
async def update_soft_deletion_policy(
    policy_id: int,
    policy_update: SoftDeletionPolicyUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update a soft deletion policy.
    
    Updates the specified policy with new values. Only provided fields will be updated.
    """
    policy = db.query(SoftDeletionPolicy).filter(SoftDeletionPolicy.id == policy_id).first()
    
    if not policy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Soft deletion policy with ID {policy_id} not found"
        )
    
    # Update only provided fields
    update_data = policy_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(policy, field, value)
    
    db.add(policy)
    db.commit()
    db.refresh(policy)
    
    logger.info(f"Updated soft deletion policy {policy_id} by user {current_user.id}")
    return policy


@router.delete("/policies/{policy_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_soft_deletion_policy(
    policy_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Delete a soft deletion policy.
    
    Removes the policy and cancels any scheduled operations using it.
    """
    policy = db.query(SoftDeletionPolicy).filter(SoftDeletionPolicy.id == policy_id).first()
    
    if not policy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Soft deletion policy with ID {policy_id} not found"
        )
    
    # Cancel any scheduled operations using this policy
    schedules = db.query(SoftDeletionSchedule).filter(
        SoftDeletionSchedule.policy_id == policy_id,
        SoftDeletionSchedule.status == "pending"
    ).all()
    
    for schedule in schedules:
        schedule.status = "cancelled"
        db.add(schedule)
    
    db.delete(policy)
    db.commit()
    
    logger.info(f"Deleted soft deletion policy {policy_id} by user {current_user.id}")


@router.get("/audit", response_model=List[SoftDeletionAuditSchema])
async def list_audit_logs(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    entity_type: Optional[str] = Query(None, description="Filter by entity type"),
    entity_id: Optional[int] = Query(None, description="Filter by entity ID"),
    operation_type: Optional[str] = Query(None, description="Filter by operation type"),
    performed_by: Optional[int] = Query(None, description="Filter by user who performed operation"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    List soft deletion audit logs.
    
    Returns audit records with optional filtering by various criteria.
    """
    query = db.query(SoftDeletionAudit)
    
    if entity_type:
        query = query.filter(SoftDeletionAudit.entity_type == entity_type)
    
    if entity_id:
        query = query.filter(SoftDeletionAudit.entity_id == entity_id)
    
    if operation_type:
        query = query.filter(SoftDeletionAudit.operation_type == operation_type)
    
    if performed_by:
        query = query.filter(SoftDeletionAudit.performed_by == performed_by)
    
    # Order by most recent first
    query = query.order_by(SoftDeletionAudit.operation_time.desc())
    
    audit_logs = query.offset(skip).limit(limit).all()
    return audit_logs


@router.get("/audit/{audit_id}", response_model=SoftDeletionAuditSchema)
async def get_audit_log(
    audit_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get a specific audit log by ID.
    """
    audit_log = db.query(SoftDeletionAudit).filter(SoftDeletionAudit.id == audit_id).first()
    
    if not audit_log:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Audit log with ID {audit_id} not found"
        )
    
    return audit_log


@router.get("/schedules", response_model=List[SoftDeletionScheduleSchema])
async def list_scheduled_operations(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    entity_type: Optional[str] = Query(None, description="Filter by entity type"),
    operation_type: Optional[str] = Query(None, description="Filter by operation type"),
    status: Optional[str] = Query(None, description="Filter by status"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    List scheduled soft deletion operations.
    
    Returns scheduled operations with optional filtering.
    """
    query = db.query(SoftDeletionSchedule)
    
    if entity_type:
        query = query.filter(SoftDeletionSchedule.entity_type == entity_type)
    
    if operation_type:
        query = query.filter(SoftDeletionSchedule.operation_type == operation_type)
    
    if status:
        query = query.filter(SoftDeletionSchedule.status == status)
    
    # Order by scheduled time
    query = query.order_by(SoftDeletionSchedule.scheduled_for.asc())
    
    schedules = query.offset(skip).limit(limit).all()
    return schedules


@router.get("/notifications", response_model=List[SoftDeletionNotificationSchema])
async def list_notifications(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    recipient_id: Optional[int] = Query(None, description="Filter by recipient"),
    notification_type: Optional[str] = Query(None, description="Filter by notification type"),
    delivery_status: Optional[str] = Query(None, description="Filter by delivery status"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    List soft deletion notifications.
    
    Returns notifications with optional filtering. Users can only see their own notifications
    unless they have admin privileges.
    """
    query = db.query(SoftDeletionNotification)
    
    # Non-admin users can only see their own notifications
    if not current_user.is_admin:
        query = query.filter(SoftDeletionNotification.recipient_id == current_user.id)
    elif recipient_id:
        query = query.filter(SoftDeletionNotification.recipient_id == recipient_id)
    
    if notification_type:
        query = query.filter(SoftDeletionNotification.notification_type == notification_type)
    
    if delivery_status:
        query = query.filter(SoftDeletionNotification.delivery_status == delivery_status)
    
    # Order by most recent first
    query = query.order_by(SoftDeletionNotification.created_at.desc())
    
    notifications = query.offset(skip).limit(limit).all()
    return notifications


@router.get("/stats/{entity_type}", response_model=SoftDeletionStatsResponse)
async def get_entity_stats(
    entity_type: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get soft deletion statistics for a specific entity type.
    
    Returns counts of active, soft-deleted, and scheduled-for-purge entities.
    """
    # This is a placeholder - actual implementation would depend on the specific entity models
    # For now, return basic structure
    policy = db.query(SoftDeletionPolicy).filter(
        SoftDeletionPolicy.entity_type == entity_type
    ).first()
    
    return SoftDeletionStatsResponse(
        entity_type=entity_type,
        total_entities=0,  # Would be calculated from actual entity tables
        active_entities=0,
        soft_deleted_entities=0,
        scheduled_for_purge=0,
        retention_period_days=policy.retention_period_days if policy else None
    )
