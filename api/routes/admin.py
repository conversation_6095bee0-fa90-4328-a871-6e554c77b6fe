"""Admin routes for the Flask application."""
from datetime import datetime, timed<PERSON>ta
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, send_file
from flask_login import login_required, current_user, login_user, logout_user
from sqlalchemy import func, desc, distinct
from functools import wraps
import logging
import csv
import io

from api.database import db
from api.models.user import User, UserRole
from api.models.error_log import ErrorLog
from api.forms import LoginForm
from api.utils.error_handler import error_handler, log_error

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

def admin_required(f):
    """Decorator to require admin role for routes."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin():
            flash('You do not have permission to access this page.', 'danger')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

@admin_bp.route('/login', methods=['GET', 'POST'])
def login():
    """Admin login page."""
    if current_user.is_authenticated and current_user.is_admin():
        return redirect(url_for('admin.dashboard'))

    form = LoginForm()
    if form.validate_on_submit():
        try:
            user = User.query.filter_by(username=form.username.data).first()

            if not user:
                flash('Invalid username or password.', 'danger')
                return render_template('admin/login.html', form=form)

            if not user.check_password(form.password.data):
                flash('Invalid username or password.', 'danger')
                return render_template('admin/login.html', form=form)

            if not user.is_admin():
                flash('You do not have administrator privileges.', 'danger')
                return render_template('admin/login.html', form=form)

            login_user(user)
            user.last_login = datetime.utcnow()
            db.session.commit()
            flash('Welcome back!', 'success')
            return redirect(url_for('admin.dashboard'))

        except Exception as e:
            logger.error(f"Error during login: {str(e)}", exc_info=True)
            db.session.rollback()
            flash('An error occurred during login. Please try again.', 'danger')

    return render_template('admin/login.html', form=form)

@admin_bp.route('/logout')
@login_required
def logout():
    """Logout the current user."""
    logout_user()
    flash('You have been logged out.', 'info')
    return redirect(url_for('admin.login'))

@admin_bp.route('/dashboard')
@login_required
@admin_required
@error_handler
def dashboard():
    """Admin dashboard."""
    # Get counts for dashboard
    user_count = User.query.count()
    active_user_count = User.query.filter_by(is_active=True).count()
    error_count = ErrorLog.query.count()
    recent_errors = ErrorLog.query.order_by(desc(ErrorLog.created_at)).limit(5).all()
    
    return render_template(
        'admin/dashboard.html',
        user_count=user_count,
        active_user_count=active_user_count,
        error_count=error_count,
        recent_errors=recent_errors
    )

@admin_bp.route('/users')
@login_required
@admin_required
@error_handler
def users():
    """User management page."""
    users = User.query.all()
    return render_template('admin/users.html', users=users)

@admin_bp.route('/users/<int:id>')
@login_required
@admin_required
@error_handler
def view_user(id):
    """View user details."""
    user = User.query.get_or_404(id)
    return render_template('admin/view_user.html', user=user)

@admin_bp.route('/error-logs')
@login_required
@admin_required
@error_handler
def error_logs():
    """Error logs management page."""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # Get filter parameters
    error_type = request.args.get('error_type', '')
    endpoint = request.args.get('endpoint', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    
    # Build query
    query = ErrorLog.query
    
    if error_type:
        query = query.filter(ErrorLog.error_type == error_type)
    
    if endpoint:
        query = query.filter(ErrorLog.endpoint == endpoint)
    
    if date_from:
        date_from = datetime.strptime(date_from, '%Y-%m-%d')
        query = query.filter(ErrorLog.created_at >= date_from)
    
    if date_to:
        date_to = datetime.strptime(date_to, '%Y-%m-%d')
        # Add one day to include the entire day
        date_to = date_to + timedelta(days=1)
        query = query.filter(ErrorLog.created_at <= date_to)
    
    # Get total count for pagination
    total = query.count()
    
    # Get paginated results
    error_logs = query.order_by(desc(ErrorLog.created_at)).paginate(page=page, per_page=per_page)
    
    # Get distinct error types and endpoints for filters
    error_types = db.session.query(distinct(ErrorLog.error_type)).all()
    error_types = [t[0] for t in error_types]
    
    endpoints = db.session.query(distinct(ErrorLog.endpoint)).filter(ErrorLog.endpoint != None).all()
    endpoints = [e[0] for e in endpoints]
    
    return render_template(
        'admin/error_logs.html',
        error_logs=error_logs.items,
        page=page,
        total_pages=(total // per_page) + (1 if total % per_page > 0 else 0),
        error_types=error_types,
        endpoints=endpoints
    )

@admin_bp.route('/error-logs/<int:id>/delete', methods=['POST'])
@login_required
@admin_required
@error_handler
def delete_error_log(id):
    """Delete an error log."""
    log = ErrorLog.query.get_or_404(id)
    db.session.delete(log)
    db.session.commit()
    flash('Error log deleted successfully.', 'success')
    return redirect(url_for('admin.error_logs'))

@admin_bp.route('/error-logs/clear', methods=['POST'])
@login_required
@admin_required
@error_handler
def clear_error_logs():
    """Clear error logs."""
    days = request.form.get('days', 0, type=int)
    
    if days > 0:
        # Clear logs older than specified days
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        logs = ErrorLog.query.filter(ErrorLog.created_at < cutoff_date).all()
        count = len(logs)
        
        for log in logs:
            db.session.delete(log)
        
        db.session.commit()
        flash(f'Successfully cleared {count} error logs older than {days} days.', 'success')
    else:
        # Clear all logs
        count = ErrorLog.query.count()
        ErrorLog.query.delete()
        db.session.commit()
        flash(f'Successfully cleared all {count} error logs.', 'success')
    
    return redirect(url_for('admin.error_logs'))

@admin_bp.route('/error-logs/export')
@login_required
@admin_required
@error_handler
def export_error_logs():
    """Export error logs as CSV."""
    # Get filter parameters
    error_type = request.args.get('error_type', '')
    endpoint = request.args.get('endpoint', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    
    # Build query
    query = ErrorLog.query
    
    if error_type:
        query = query.filter(ErrorLog.error_type == error_type)
    
    if endpoint:
        query = query.filter(ErrorLog.endpoint == endpoint)
    
    if date_from:
        date_from = datetime.strptime(date_from, '%Y-%m-%d')
        query = query.filter(ErrorLog.created_at >= date_from)
    
    if date_to:
        date_to = datetime.strptime(date_to, '%Y-%m-%d')
        # Add one day to include the entire day
        date_to = date_to + timedelta(days=1)
        query = query.filter(ErrorLog.created_at <= date_to)
    
    # Get all logs
    logs = query.order_by(desc(ErrorLog.created_at)).all()
    
    # Create CSV file
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow(['ID', 'Error Type', 'Error Message', 'Endpoint', 'Method', 'User ID', 'Created At'])
    
    # Write data
    for log in logs:
        writer.writerow([
            log.id,
            log.error_type,
            log.error_message,
            log.endpoint,
            log.request_method,
            log.user_id,
            log.created_at.strftime('%Y-%m-%d %H:%M:%S')
        ])
    
    # Prepare response
    output.seek(0)
    timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
    return send_file(
        io.BytesIO(output.getvalue().encode('utf-8')),
        mimetype='text/csv',
        as_attachment=True,
        download_name=f'error_logs_{timestamp}.csv'
    )

@admin_bp.route('/settings')
@login_required
@admin_required
def settings():
    """Admin settings page."""
    try:
        return render_template('admin/settings.html', user=current_user)
    except Exception as e:
        logger.error(f"Error loading settings: {str(e)}", exc_info=True)
        flash('Error loading settings.', 'danger')
        return redirect(url_for('admin.dashboard'))