"""Initialize the v1 API package.

This module initialises the v1 API package and sets up the router for all v1 endpoints.
"""

from fastapi import APIRouter

from api.routes.assessment import router as assessment_router

# Temporarily comment out problematic imports
# from api.routes.v1.crud import router as crud_router
# from api.routes.testcase_chaining import router as testcase_chaining_router
from api.routes.campaign import router as campaign_router
from api.routes.testcase import router as testcase_router
from api.routes.v1.admin import router as admin_router
from api.routes.v1.mitre import router as mitre_router
from api.routes.v1.two_factor import router as two_factor_router
from api.routes.v1.validation_demo import router as validation_demo_router
from api.routes.v1_heatmap import router as heatmap_router

# Create the v1 router
router = APIRouter()

# Include all routers
router.include_router(mitre_router, tags=["MITRE"])
router.include_router(two_factor_router, tags=["Two-Factor Authentication"])
router.include_router(admin_router, prefix="/admin", tags=["Admin"])
router.include_router(validation_demo_router, tags=["Validation Demo"])
router.include_router(heatmap_router, prefix="/heatmap", tags=["Heat Map"])
# Temporarily comment out problematic routers
# router.include_router(crud_router, tags=["CRUD"])
# router.include_router(testcase_chaining_router, tags=["Testcase Chaining"])
router.include_router(campaign_router, tags=["Campaigns"])
router.include_router(testcase_router, tags=["Test Cases"])
router.include_router(assessment_router, tags=["Assessments"])

__all__ = ["router"]
