"""Admin endpoints for framework data import."""
import asyncio
import json
import logging
import uuid
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, File, Form, HTTPException, Query, UploadFile, status
from fastapi.responses import JSONResponse, StreamingResponse
from sqlalchemy.orm import Session
from sse_starlette.sse import EventSourceResponse

from api.auth.dependencies import get_admin_user
from api.database import get_db
from api.models.user import User
from api.utils.import_base import BaseFrameworkImporter, ImportError
from api.utils.atlas_importer import AtlasImporter
from api.utils.mitre_importer import MITREImporter
from api.utils.d3fend_import import import_d3fend_ontology

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/admin/import", tags=["Admin Import"])

# In-memory store of import jobs (would be database-backed in production)
import_jobs = {}

async def get_job_status(job_id: str):
    """Get status of an import job."""
    if job_id not in import_jobs:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Import job {job_id} not found"
        )
    return import_jobs[job_id]

async def update_job_status(job_id: str, progress: float, status: str = None, details: Dict = None):
    """Update status of an import job."""
    if job_id not in import_jobs:
        return
        
    job = import_jobs[job_id]
    job["progress"] = progress
    
    if status:
        job["status"] = status
        
    if details:
        job["details"] = details

async def process_import(job_id: str, framework: str, file_content: bytes, db: Session):
    """Process an import job asynchronously."""
    try:
        if framework == "mitre":
            importer = MITREImporter(db)
        elif framework == "atlas":
            importer = AtlasImporter(db)
        elif framework == "d3fend":
            # Special case for D3FEND as it doesn't use the base importer
            try:
                # Save to temporary file
                temp_file = f"/tmp/{job_id}.owl"
                with open(temp_file, "wb") as f:
                    f.write(file_content)
                
                # Update progress
                await update_job_status(job_id, 50, "processing")
                
                # Import
                import_d3fend_ontology(db, temp_file)
                
                # Update status
                await update_job_status(
                    job_id, 
                    100, 
                    "completed", 
                    {"message": "D3FEND ontology imported successfully"}
                )
                return
            except Exception as e:
                logger.error(f"Error importing D3FEND data: {str(e)}")
                await update_job_status(
                    job_id, 
                    100, 
                    "failed", 
                    {"error": str(e)}
                )
                return
        else:
            await update_job_status(
                job_id, 
                100, 
                "failed", 
                {"error": f"Unsupported framework: {framework}"}
            )
            return
            
        # Parse JSON content
        data = json.loads(file_content)
        
        # Update progress after parsing
        await update_job_status(job_id, 20, "processing")
        
        # Validate data
        if not importer.validate_data(data):
            await update_job_status(
                job_id, 
                100, 
                "failed", 
                {"error": "Invalid data format"}
            )
            return
            
        # Update progress after validation
        await update_job_status(job_id, 40, "processing")
        
        # Extract version
        version = importer.extract_version(data)
        
        # Update progress after version extraction
        await update_job_status(job_id, 50, "processing")
        
        # Process import in steps to provide progress updates
        try:
            # Begin transaction
            importer._begin_transaction()
            
            # Import data
            results = importer.import_data(data, version=version)
            
            # Update progress during import
            await update_job_status(job_id, 80, "processing")
            
            # Commit transaction
            importer._commit_transaction()
            
            # Update final status
            await update_job_status(
                job_id, 
                100, 
                "completed", 
                {"results": results}
            )
        except Exception as e:
            importer._rollback_transaction()
            logger.error(f"Error during import: {str(e)}")
            await update_job_status(
                job_id, 
                100, 
                "failed", 
                {"error": str(e)}
            )
    except Exception as e:
        logger.error(f"Unhandled error during import: {str(e)}")
        await update_job_status(
            job_id, 
            100, 
            "failed", 
            {"error": str(e)}
        )

@router.post("")
async def import_framework_data(
    file: UploadFile = File(...),
    framework: str = Form(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_user)
):
    """Import framework data through admin interface."""
    try:
        # Validate framework
        if framework not in ["mitre", "atlas", "d3fend"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported framework: {framework}"
            )
            
        # Check file extension
        file_ext = file.filename.split(".")[-1].lower()
        if framework == "d3fend" and file_ext != "owl":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="D3FEND framework requires OWL file format"
            )
        elif framework in ["mitre", "atlas"] and file_ext != "json":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"{framework.upper()} framework requires JSON file format"
            )
            
        # Create job ID
        job_id = str(uuid.uuid4())
        
        # Initialize job status
        import_jobs[job_id] = {
            "id": job_id,
            "framework": framework,
            "filename": file.filename,
            "status": "uploading",
            "progress": 0,
            "started_at": str(datetime.utcnow()),
            "user_id": current_user.id
        }
        
        # Read file content
        file_content = await file.read()
        
        # Start async import process
        asyncio.create_task(
            process_import(job_id, framework, file_content, db)
        )
        
        return {
            "status": "success",
            "message": "Import started successfully",
            "job_id": job_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting import: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting import: {str(e)}"
        )

@router.get("/status/{job_id}")
async def stream_status(
    job_id: str,
    current_user: User = Depends(get_admin_user)
):
    """Stream status updates for an import job using Server-Sent Events (SSE)."""
    async def event_generator():
        try:
            job = await get_job_status(job_id)
            
            # Initial status
            yield {
                "event": "status",
                "data": json.dumps({
                    "status": job["status"],
                    "progress": job["progress"]
                })
            }
            
            # Stream updates
            while job["status"] not in ["completed", "failed", "cancelled"]:
                # Check if job still exists
                try:
                    job = await get_job_status(job_id)
                except HTTPException:
                    break
                    
                # Send update
                yield {
                    "event": "status",
                    "data": json.dumps({
                        "status": job["status"],
                        "progress": job["progress"],
                        "details": job.get("details")
                    })
                }
                
                # Exit if complete
                if job["status"] in ["completed", "failed", "cancelled"]:
                    break
                    
                # Wait before next update
                await asyncio.sleep(1)
                
            # Final status
            yield {
                "event": "status",
                "data": json.dumps({
                    "status": job["status"],
                    "progress": job["progress"],
                    "details": job.get("details")
                })
            }
            
        except Exception as e:
            logger.error(f"Error streaming job status: {str(e)}")
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }
    
    return EventSourceResponse(event_generator())

@router.post("/cancel/{job_id}")
async def cancel_import_job(
    job_id: str,
    current_user: User = Depends(get_admin_user)
):
    """Cancel an in-progress import job."""
    try:
        job = await get_job_status(job_id)
        
        # Check if job can be cancelled
        if job["status"] in ["completed", "failed", "cancelled"]:
            return {
                "status": "error",
                "message": f"Job already {job['status']}, cannot cancel"
            }
            
        # Update job status
        await update_job_status(
            job_id, 
            job["progress"], 
            "cancelled", 
            {"message": "Job cancelled by user"}
        )
        
        return {
            "status": "success",
            "message": "Job cancelled successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error cancelling job: {str(e)}"
        )

@router.get("/jobs")
async def list_import_jobs(
    limit: int = Query(10, ge=1, le=100),
    status: Optional[str] = None,
    current_user: User = Depends(get_admin_user)
):
    """List import jobs with optional filtering."""
    try:
        # Get all jobs
        all_jobs = list(import_jobs.values())
        
        # Filter by status if provided
        if status:
            filtered_jobs = [j for j in all_jobs if j["status"] == status]
        else:
            filtered_jobs = all_jobs
            
        # Sort by start time (newest first)
        sorted_jobs = sorted(
            filtered_jobs,
            key=lambda j: j["started_at"],
            reverse=True
        )
        
        # Apply limit
        limited_jobs = sorted_jobs[:limit]
        
        return {
            "status": "success",
            "jobs": limited_jobs,
            "total": len(filtered_jobs)
        }
    except Exception as e:
        logger.error(f"Error listing jobs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error listing jobs: {str(e)}"
        )