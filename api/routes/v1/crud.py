"""Generic CRUD operations router."""
from typing import Generic, List, Optional, Type, TypeVar
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from api.database import get_db
from api.models.mixins import SoftDeleteMixin
from api.auth.router import get_current_user_or_bypass

ModelType = TypeVar("ModelType", bound=SoftDeleteMixin)

class CRUDRouter(Generic[ModelType]):
    """Generic CRUD router for models with soft delete."""

    def __init__(
        self,
        model: Type[ModelType],
        prefix: str,
        tags: List[str]
    ):
        """Initialize CRUD router with model type and route configuration."""
        self.model = model
        self.router = APIRouter(prefix=prefix, tags=tags)
        self._register_routes()

    def _register_routes(self):
        """Register all CRUD routes."""
        
        @self.router.get("/")
        async def get_all(
            skip: int = Query(0, ge=0),
            limit: int = Query(10, ge=1, le=100),
            include_deleted: bool = False,
            db: Session = Depends(get_db),
            _: dict = Depends(get_current_user_or_bypass)
        ):
            """Get all records with pagination."""
            items = self.model.get_all(db, skip_deleted=not include_deleted)
            return {
                "items": items[skip:skip + limit],
                "total": len(items)
            }

        @self.router.get("/{id}")
        async def get_one(
            id: int,
            include_deleted: bool = False,
            db: Session = Depends(get_db),
            _: dict = Depends(get_current_user_or_bypass)
        ):
            """Get a single record by ID."""
            item = self.model.get_by_id(db, id, skip_deleted=not include_deleted)
            if not item:
                raise HTTPException(status_code=404, detail="Item not found")
            return item

        @self.router.post("/")
        async def create(
            data: dict,
            db: Session = Depends(get_db),
            _: dict = Depends(get_current_user_or_bypass)
        ):
            """Create a new record."""
            try:
                return self.model.create(db, **data)
            except Exception as e:
                raise HTTPException(
                    status_code=400,
                    detail=f"Could not create item: {str(e)}"
                )

        @self.router.patch("/{id}")
        async def update(
            id: int,
            data: dict,
            db: Session = Depends(get_db),
            _: dict = Depends(get_current_user_or_bypass)
        ):
            """Update an existing record."""
            item = self.model.get_by_id(db, id)
            if not item:
                raise HTTPException(status_code=404, detail="Item not found")
            try:
                item.update(db, **data)
                return item
            except Exception as e:
                raise HTTPException(
                    status_code=400,
                    detail=f"Could not update item: {str(e)}"
                )

        @self.router.delete("/{id}")
        async def delete(
            id: int,
            db: Session = Depends(get_db),
            _: dict = Depends(get_current_user_or_bypass)
        ):
            """Soft delete a record."""
            item = self.model.get_by_id(db, id)
            if not item:
                raise HTTPException(status_code=404, detail="Item not found")
            item.soft_delete(db)
            return {"status": "success", "message": "Item deleted"}

        @self.router.post("/{id}/restore")
        async def restore(
            id: int,
            db: Session = Depends(get_db),
            _: dict = Depends(get_current_user_or_bypass)
        ):
            """Restore a soft-deleted record."""
            item = self.model.get_by_id(db, id, skip_deleted=False)
            if not item:
                raise HTTPException(status_code=404, detail="Item not found")
            if not item.deleted_at:
                raise HTTPException(
                    status_code=400,
                    detail="Item is not deleted"
                )
            item.restore(db)
            return {"status": "success", "message": "Item restored"}
