"""Assessment management API endpoints.

This module provides API endpoints for managing security assessments,
including creating, retrieving, updating, and deleting assessments.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Security, Query
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from api.database import get_db
from api.models.base import AssessmentDB, CampaignDB, TestExecutionDB, TestCaseDB
from api.models.schemas import AssessmentCreate, Assessment
from api.models.user import User
from api.dependencies import get_current_user, get_current_active_user
from api.utils.rate_limiter import standard_rate_limit
from api.utils.error_handler import log_error

router = APIRouter(prefix="/api/v1/assessments", tags=["assessments"])

# Custom error handler for FastAPI routes
def fastapi_error_handler(func):
    """Decorator to handle SQLAlchemy errors in FastAPI routes."""
    from functools import wraps
    
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except SQLAlchemyError as e:
            # Log the error
            log_error(e, endpoint=func.__name__, request_data=kwargs)
            # Return a user-friendly error response
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="A database error occurred. Our team has been notified."
            )
        except Exception as e:
            # Log the error
            log_error(e, endpoint=func.__name__, request_data=kwargs)
            # Re-raise the exception
            raise
    
    return wrapper

@router.post("/", response_model=Assessment, status_code=status.HTTP_201_CREATED)
@fastapi_error_handler
async def create_assessment(
    assessment_data: AssessmentCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Create a new security assessment.
    
    Args:
        assessment_data: The assessment data to create
        db: Database session
        current_user: The authenticated user
        
    Returns:
        The created assessment
    """
    # Check if user has permission to create assessments
    if not current_user.is_admin() and not current_user.is_analyst():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to create assessments"
        )
    
    # Create the assessment
    assessment = AssessmentDB(
        name=assessment_data.name,
        description=assessment_data.description,
        target_system=assessment_data.target_system,
        assessment_type=assessment_data.assessment_type,
        start_date=assessment_data.start_date or datetime.utcnow(),
        end_date=assessment_data.end_date,
        status=assessment_data.status,
        created_by=current_user.id
    )
    
    db.add(assessment)
    db.commit()
    db.refresh(assessment)
    
    # Add campaigns if provided
    if assessment_data.campaign_ids:
        for campaign_id in assessment_data.campaign_ids:
            campaign = db.query(CampaignDB).filter(
                CampaignDB.id == campaign_id,
                CampaignDB.not_deleted()
            ).first()
            
            if not campaign:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Campaign with ID {campaign_id} not found"
                )
            
            assessment.campaigns.append(campaign)
        
        db.add(assessment)
        db.commit()
        db.refresh(assessment)
    
    return assessment

@router.get("/", response_model=List[Assessment])
@fastapi_error_handler
async def get_assessments(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[str] = None,
    assessment_type: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Get all assessments with optional filtering.
    
    Args:
        skip: Number of records to skip
        limit: Maximum number of records to return
        status: Optional filter by assessment status
        assessment_type: Optional filter by assessment type
        db: Database session
        current_user: The authenticated user
        
    Returns:
        List of assessments
    """
    query = db.query(AssessmentDB).filter(AssessmentDB.not_deleted())
    
    # Apply status filter if provided
    if status:
        query = query.filter(AssessmentDB.status == status)
    
    # Apply assessment type filter if provided
    if assessment_type:
        query = query.filter(AssessmentDB.assessment_type == assessment_type)
    
    # Apply pagination
    assessments = query.offset(skip).limit(limit).all()
    
    return assessments

@router.get("/{assessment_id}", response_model=Assessment)
@fastapi_error_handler
async def get_assessment(
    assessment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Get a specific assessment by ID.
    
    Args:
        assessment_id: The ID of the assessment to retrieve
        db: Database session
        current_user: The authenticated user
        
    Returns:
        The requested assessment
    """
    assessment = db.query(AssessmentDB).filter(
        AssessmentDB.id == assessment_id,
        AssessmentDB.not_deleted()
    ).first()
    
    if not assessment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Assessment with ID {assessment_id} not found"
        )
    
    return assessment

@router.put("/{assessment_id}", response_model=Assessment)
@fastapi_error_handler
async def update_assessment(
    assessment_id: int,
    assessment_data: AssessmentCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Update a specific assessment.
    
    Args:
        assessment_id: The ID of the assessment to update
        assessment_data: The updated assessment data
        db: Database session
        current_user: The authenticated user
        
    Returns:
        The updated assessment
    """
    # Check if user has permission to update assessments
    if not current_user.is_admin() and not current_user.is_analyst():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update assessments"
        )
    
    assessment = db.query(AssessmentDB).filter(
        AssessmentDB.id == assessment_id,
        AssessmentDB.not_deleted()
    ).first()
    
    if not assessment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Assessment with ID {assessment_id} not found"
        )
    
    # Update assessment fields
    assessment.name = assessment_data.name
    assessment.description = assessment_data.description
    assessment.target_system = assessment_data.target_system
    assessment.assessment_type = assessment_data.assessment_type
    assessment.start_date = assessment_data.start_date or assessment.start_date
    assessment.end_date = assessment_data.end_date
    assessment.status = assessment_data.status
    
    # Update campaigns if provided
    if assessment_data.campaign_ids is not None:
        # Clear existing campaigns
        assessment.campaigns = []
        
        # Add new campaigns
        for campaign_id in assessment_data.campaign_ids:
            campaign = db.query(CampaignDB).filter(
                CampaignDB.id == campaign_id,
                CampaignDB.not_deleted()
            ).first()
            
            if not campaign:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Campaign with ID {campaign_id} not found"
                )
            
            assessment.campaigns.append(campaign)
    
    db.add(assessment)
    db.commit()
    db.refresh(assessment)
    
    return assessment

@router.delete("/{assessment_id}", status_code=status.HTTP_204_NO_CONTENT)
@fastapi_error_handler
async def delete_assessment(
    assessment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Delete (soft-delete) an assessment.
    
    Args:
        assessment_id: The ID of the assessment to delete
        db: Database session
        current_user: The authenticated user
    """
    # Check if user has permission to delete assessments
    if not current_user.is_admin():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete assessments"
        )
    
    assessment = db.query(AssessmentDB).filter(
        AssessmentDB.id == assessment_id,
        AssessmentDB.not_deleted()
    ).first()
    
    if not assessment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Assessment with ID {assessment_id} not found"
        )
    
    # Soft delete the assessment
    assessment.soft_delete(db)
    
    return None

@router.post("/{assessment_id}/restore", response_model=Assessment)
@fastapi_error_handler
async def restore_assessment(
    assessment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Restore a soft-deleted assessment.
    
    Args:
        assessment_id: The ID of the assessment to restore
        db: Database session
        current_user: The authenticated user
        
    Returns:
        The restored assessment
    """
    # Check if user has permission to restore assessments
    if not current_user.is_admin():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to restore assessments"
        )
    
    assessment = db.query(AssessmentDB).filter(
        AssessmentDB.id == assessment_id,
        AssessmentDB.deleted_time.isnot(None)
    ).first()
    
    if not assessment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Deleted assessment with ID {assessment_id} not found"
        )
    
    # Restore the assessment
    assessment.deleted_time = None
    
    db.add(assessment)
    db.commit()
    db.refresh(assessment)
    
    return assessment

@router.post("/{assessment_id}/campaigns/{campaign_id}", response_model=Assessment)
@fastapi_error_handler
async def add_campaign_to_assessment(
    assessment_id: int,
    campaign_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Add a campaign to an assessment.
    
    Args:
        assessment_id: The ID of the assessment
        campaign_id: The ID of the campaign to add
        db: Database session
        current_user: The authenticated user
        
    Returns:
        The updated assessment
    """
    # Check if user has permission to update assessments
    if not current_user.is_admin() and not current_user.is_analyst():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update assessments"
        )
    
    assessment = db.query(AssessmentDB).filter(
        AssessmentDB.id == assessment_id,
        AssessmentDB.not_deleted()
    ).first()
    
    if not assessment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Assessment with ID {assessment_id} not found"
        )
    
    campaign = db.query(CampaignDB).filter(
        CampaignDB.id == campaign_id,
        CampaignDB.not_deleted()
    ).first()
    
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Campaign with ID {campaign_id} not found"
        )
    
    # Check if campaign is already in the assessment
    if campaign in assessment.campaigns:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Campaign with ID {campaign_id} is already in the assessment"
        )
    
    # Add campaign to assessment
    assessment.campaigns.append(campaign)
    
    db.add(assessment)
    db.commit()
    db.refresh(assessment)
    
    return assessment

@router.delete("/{assessment_id}/campaigns/{campaign_id}", response_model=Assessment)
@fastapi_error_handler
async def remove_campaign_from_assessment(
    assessment_id: int,
    campaign_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Remove a campaign from an assessment.
    
    Args:
        assessment_id: The ID of the assessment
        campaign_id: The ID of the campaign to remove
        db: Database session
        current_user: The authenticated user
        
    Returns:
        The updated assessment
    """
    # Check if user has permission to update assessments
    if not current_user.is_admin() and not current_user.is_analyst():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update assessments"
        )
    
    assessment = db.query(AssessmentDB).filter(
        AssessmentDB.id == assessment_id,
        AssessmentDB.not_deleted()
    ).first()
    
    if not assessment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Assessment with ID {assessment_id} not found"
        )
    
    campaign = db.query(CampaignDB).filter(
        CampaignDB.id == campaign_id,
        CampaignDB.not_deleted()
    ).first()
    
    if not campaign:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Campaign with ID {campaign_id} not found"
        )
    
    # Check if campaign is in the assessment
    if campaign not in assessment.campaigns:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Campaign with ID {campaign_id} is not in the assessment"
        )
    
    # Remove campaign from assessment
    assessment.campaigns.remove(campaign)
    
    db.add(assessment)
    db.commit()
    db.refresh(assessment)
    
    return assessment

@router.get("/{assessment_id}/results", response_model=List[dict])
@fastapi_error_handler
async def get_assessment_results(
    assessment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Get assessment test execution results.
    
    Args:
        assessment_id: The ID of the assessment
        db: Database session
        current_user: The authenticated user
        
    Returns:
        List of test execution results for the assessment
    """
    # Check if the assessment exists
    assessment = db.query(AssessmentDB).filter(
        AssessmentDB.id == assessment_id,
        AssessmentDB.not_deleted()
    ).first()
    
    if not assessment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Assessment with ID {assessment_id} not found"
        )
    
    # Get all test executions for this assessment
    executions = db.query(TestExecutionDB).filter(
        TestExecutionDB.assessment_id == assessment_id
    ).all()
    
    # Convert to dictionary format with test case details
    results = []
    for execution in executions:
        test_case = execution.test_case
        results.append({
            "id": execution.id,
            "test_case_id": execution.test_case_id,
            "test_case_name": test_case.name if test_case else "Unknown",
            "result": execution.result,
            "notes": execution.notes,
            "evidence": execution.evidence,
            "executed_by": execution.executed_by,
            "executed_at": execution.executed_at
        })
    
    return results

@router.get("/{assessment_id}/report", response_model=dict)
@fastapi_error_handler
async def generate_assessment_report(
    assessment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(standard_rate_limit)
):
    """Generate a comprehensive assessment report.
    
    Args:
        assessment_id: The ID of the assessment
        db: Database session
        current_user: The authenticated user
        
    Returns:
        Comprehensive assessment report including summary statistics and results
    """
    # Check if the assessment exists
    assessment = db.query(AssessmentDB).filter(
        AssessmentDB.id == assessment_id,
        AssessmentDB.not_deleted()
    ).first()
    
    if not assessment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Assessment with ID {assessment_id} not found"
        )
    
    # Get all test executions for this assessment
    executions = db.query(TestExecutionDB).filter(
        TestExecutionDB.assessment_id == assessment_id
    ).all()
    
    # Calculate summary statistics
    total_tests = len(executions)
    passed = sum(1 for e in executions if e.result == "pass")
    failed = sum(1 for e in executions if e.result == "fail")
    partial = sum(1 for e in executions if e.result == "partial")
    pending = sum(1 for e in executions if e.result == "pending")
    blocked = sum(1 for e in executions if e.result == "blocked")
    not_applicable = sum(1 for e in executions if e.result == "not_applicable")
    
    # Calculate completion percentage and pass rate
    completed_tests = total_tests - pending
    completion_percentage = (completed_tests / total_tests * 100) if total_tests > 0 else 0
    pass_rate = (passed / completed_tests * 100) if completed_tests > 0 else 0
    
    # Get campaigns associated with this assessment
    campaigns = db.query(CampaignDB).join(
        assessment.campaigns
    ).all()
    
    # Get MITRE ATT&CK coverage if available
    mitre_coverage = {}
    test_cases = db.query(TestCaseDB).join(
        TestExecutionDB, TestExecutionDB.test_case_id == TestCaseDB.id
    ).filter(
        TestExecutionDB.assessment_id == assessment_id
    ).all()
    
    # Count techniques by tactic
    for test_case in test_cases:
        if test_case.mitre_technique_id:
            technique = test_case.mitre_technique
            if technique:
                for tactic in technique.tactics:
                    tactic_name = tactic.name
                    if tactic_name not in mitre_coverage:
                        mitre_coverage[tactic_name] = {"total": 0, "covered": 0, "techniques": []}
                    
                    # Add technique to coverage if not already counted
                    if technique.id not in [t["id"] for t in mitre_coverage[tactic_name]["techniques"]]:
                        mitre_coverage[tactic_name]["total"] += 1
                        mitre_coverage[tactic_name]["techniques"].append({
                            "id": technique.id,
                            "name": technique.name,
                            "covered": True
                        })
                        mitre_coverage[tactic_name]["covered"] += 1
    
    # Generate recommendations based on results
    recommendations = []
    if failed > 0:
        recommendations.append("Address failed test cases to improve security posture")
    if pass_rate < 80:
        recommendations.append("Increase test coverage to achieve at least 80% pass rate")
    if not mitre_coverage:
        recommendations.append("Map test cases to MITRE ATT&CK techniques for better threat coverage analysis")
    
    # Prepare the report
    report = {
        "assessment": {
            "id": assessment.id,
            "name": assessment.name,
            "description": assessment.description,
            "status": assessment.status,
            "start_date": assessment.start_date,
            "end_date": assessment.end_date,
            "created_by": assessment.created_by,
            "created_time": assessment.created_time
        },
        "summary": {
            "total_tests": total_tests,
            "passed": passed,
            "failed": failed,
            "partial": partial,
            "pending": pending,
            "blocked": blocked,
            "not_applicable": not_applicable,
            "completion_percentage": completion_percentage,
            "pass_rate": pass_rate
        },
        "test_executions": [
            {
                "id": execution.id,
                "test_case_id": execution.test_case_id,
                "test_case_name": execution.test_case.name if execution.test_case else "Unknown",
                "result": execution.result,
                "notes": execution.notes,
                "executed_by": execution.executed_by,
                "executed_at": execution.executed_at
            }
            for execution in executions
        ],
        "campaigns": [
            {
                "id": campaign.id,
                "name": campaign.name,
                "status": campaign.status
            }
            for campaign in campaigns
        ],
        "mitre_coverage": mitre_coverage,
        "recommendations": recommendations
    }
    
    return report 