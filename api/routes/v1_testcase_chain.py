"""
API routes for Enhanced Testcase Chaining & Sequencing.

This module provides REST API endpoints for managing testcase chains,
nodes, edges, executions, and related functionality.
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from api.auth.dependencies import get_current_user
from api.database import get_db
from api.models.testcase_chain import (
    ChainExecution,
    NodeExecution,
    TestcaseChain,
    TestcaseChainEdge,
    TestcaseChainNode,
    TestcaseCondition,
)
from api.models.user import User
from api.schemas.testcase_chain import (
    ChainExecution as ChainExecutionSchema,
    ChainExecutionCreate,
    ChainExecutionWithDetails,
    ChainValidationResult,
    TestcaseChain as TestcaseChainSchema,
    TestcaseChainCreate,
    TestcaseChainEdge as TestcaseChainEdgeSchema,
    TestcaseChainEdgeCreate,
    TestcaseChainEdgeUpdate,
    TestcaseChainNode as TestcaseChainNodeSchema,
    TestcaseChainNode<PERSON>reate,
    TestcaseChainNodeUpdate,
    <PERSON>caseChainUpdate,
    TestcaseChainWithNodes,
    TestcaseCondition as TestcaseConditionSchema,
    TestcaseConditionCreate,
    TestcaseConditionUpdate,
)
from api.services.testcase_chain_service import TestcaseChainService

logger = logging.getLogger(__name__)
router = APIRouter()


# Testcase Chain Management
@router.get("/chains", response_model=List[TestcaseChainSchema])
async def list_testcase_chains(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    status: Optional[str] = Query(None, description="Filter by chain status"),
    chain_type: Optional[str] = Query(None, description="Filter by chain type"),
    created_by: Optional[int] = Query(None, description="Filter by creator"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    List testcase chains with optional filtering.
    
    Returns a list of testcase chains with optional filtering by status,
    type, and creator.
    """
    query = db.query(TestcaseChain)
    
    if status:
        query = query.filter(TestcaseChain.status == status)
    
    if chain_type:
        query = query.filter(TestcaseChain.chain_type == chain_type)
    
    if created_by:
        query = query.filter(TestcaseChain.created_by == created_by)
    
    # Order by most recent first
    query = query.order_by(TestcaseChain.created_at.desc())
    
    chains = query.offset(skip).limit(limit).all()
    return chains


@router.post("/chains", response_model=TestcaseChainSchema, status_code=status.HTTP_201_CREATED)
async def create_testcase_chain(
    chain_data: TestcaseChainCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Create a new testcase chain.
    
    Creates a new testcase chain with the specified configuration.
    """
    service = TestcaseChainService(db)
    
    try:
        chain = service.create_chain(
            name=chain_data.name,
            description=chain_data.description,
            chain_type=chain_data.chain_type,
            max_execution_time_minutes=chain_data.max_execution_time_minutes,
            retry_on_failure=chain_data.retry_on_failure,
            auto_cleanup=chain_data.auto_cleanup,
            created_by=current_user.id
        )
        
        logger.info(f"Created testcase chain {chain.id} by user {current_user.id}")
        return chain
        
    except Exception as e:
        logger.error(f"Failed to create testcase chain: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create testcase chain: {str(e)}"
        )


@router.get("/chains/{chain_id}", response_model=TestcaseChainWithNodes)
async def get_testcase_chain(
    chain_id: int,
    include_nodes: bool = Query(True, description="Include chain nodes and edges"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get a specific testcase chain by ID.
    
    Returns the chain details and optionally includes nodes and edges.
    """
    chain = db.query(TestcaseChain).filter(TestcaseChain.id == chain_id).first()
    
    if not chain:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Testcase chain with ID {chain_id} not found"
        )
    
    if include_nodes:
        # Get nodes and edges
        nodes = db.query(TestcaseChainNode).filter(
            TestcaseChainNode.chain_id == chain_id
        ).order_by(TestcaseChainNode.execution_order).all()
        
        edges = db.query(TestcaseChainEdge).join(
            TestcaseChainNode, TestcaseChainEdge.source_node_id == TestcaseChainNode.id
        ).filter(TestcaseChainNode.chain_id == chain_id).all()
        
        return TestcaseChainWithNodes(
            **chain.__dict__,
            nodes=nodes,
            edges=edges
        )
    
    return chain


@router.put("/chains/{chain_id}", response_model=TestcaseChainSchema)
async def update_testcase_chain(
    chain_id: int,
    chain_update: TestcaseChainUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update a testcase chain.
    
    Updates the specified chain with new values. Only provided fields will be updated.
    """
    chain = db.query(TestcaseChain).filter(TestcaseChain.id == chain_id).first()
    
    if not chain:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Testcase chain with ID {chain_id} not found"
        )
    
    # Update only provided fields
    update_data = chain_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(chain, field, value)
    
    db.add(chain)
    db.commit()
    db.refresh(chain)
    
    logger.info(f"Updated testcase chain {chain_id} by user {current_user.id}")
    return chain


@router.delete("/chains/{chain_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_testcase_chain(
    chain_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Delete a testcase chain.
    
    Soft deletes the chain and all associated nodes and edges.
    """
    chain = db.query(TestcaseChain).filter(TestcaseChain.id == chain_id).first()
    
    if not chain:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Testcase chain with ID {chain_id} not found"
        )
    
    # Soft delete the chain (cascade will handle nodes and edges)
    chain.soft_delete()
    db.add(chain)
    db.commit()
    
    logger.info(f"Deleted testcase chain {chain_id} by user {current_user.id}")


# Chain Validation
@router.get("/chains/{chain_id}/validate", response_model=ChainValidationResult)
async def validate_testcase_chain(
    chain_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Validate a testcase chain for structural integrity.
    
    Checks for cycles, unreachable nodes, and other structural issues.
    """
    service = TestcaseChainService(db)
    
    try:
        validation_result = service.validate_chain(chain_id)
        return validation_result
        
    except Exception as e:
        logger.error(f"Failed to validate chain {chain_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to validate chain: {str(e)}"
        )


# Chain Node Management
@router.post("/chains/{chain_id}/nodes", response_model=TestcaseChainNodeSchema, status_code=status.HTTP_201_CREATED)
async def add_node_to_chain(
    chain_id: int,
    node_data: TestcaseChainNodeCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Add a node to a testcase chain.
    
    Adds a testcase as a node in the specified chain.
    """
    service = TestcaseChainService(db)
    
    try:
        node = service.add_node_to_chain(
            chain_id=chain_id,
            testcase_id=node_data.testcase_id,
            node_type=node_data.node_type,
            execution_order=node_data.execution_order,
            position_x=node_data.position_x,
            position_y=node_data.position_y,
            condition_expression=node_data.condition_expression,
            timeout_minutes=node_data.timeout_minutes,
            retry_count=node_data.retry_count,
            continue_on_failure=node_data.continue_on_failure,
            required_for_completion=node_data.required_for_completion
        )
        
        logger.info(f"Added node {node.id} to chain {chain_id} by user {current_user.id}")
        return node
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to add node to chain {chain_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add node to chain: {str(e)}"
        )


@router.put("/nodes/{node_id}", response_model=TestcaseChainNodeSchema)
async def update_chain_node(
    node_id: int,
    node_update: TestcaseChainNodeUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update a chain node.
    
    Updates the specified node with new values. Only provided fields will be updated.
    """
    node = db.query(TestcaseChainNode).filter(TestcaseChainNode.id == node_id).first()
    
    if not node:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Chain node with ID {node_id} not found"
        )
    
    # Update only provided fields
    update_data = node_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(node, field, value)
    
    db.add(node)
    db.commit()
    db.refresh(node)
    
    logger.info(f"Updated chain node {node_id} by user {current_user.id}")
    return node


@router.delete("/nodes/{node_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_chain_node(
    node_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Delete a chain node.
    
    Soft deletes the node and all associated edges.
    """
    node = db.query(TestcaseChainNode).filter(TestcaseChainNode.id == node_id).first()
    
    if not node:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Chain node with ID {node_id} not found"
        )
    
    # Soft delete the node (cascade will handle edges)
    node.soft_delete()
    db.add(node)
    db.commit()
    
    logger.info(f"Deleted chain node {node_id} by user {current_user.id}")


# Chain Edge Management
@router.post("/edges", response_model=TestcaseChainEdgeSchema, status_code=status.HTTP_201_CREATED)
async def create_chain_edge(
    edge_data: TestcaseChainEdgeCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Create an edge between two chain nodes.
    
    Creates a connection between two nodes in a testcase chain.
    """
    service = TestcaseChainService(db)
    
    try:
        edge = service.add_edge_to_chain(
            source_node_id=edge_data.source_node_id,
            target_node_id=edge_data.target_node_id,
            edge_type=edge_data.edge_type,
            condition=edge_data.condition,
            weight=edge_data.weight,
            label=edge_data.label,
            description=edge_data.description
        )
        
        logger.info(f"Created edge {edge.id} by user {current_user.id}")
        return edge
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to create edge: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create edge: {str(e)}"
        )


@router.put("/edges/{edge_id}", response_model=TestcaseChainEdgeSchema)
async def update_chain_edge(
    edge_id: int,
    edge_update: TestcaseChainEdgeUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update a chain edge.

    Updates the specified edge with new values. Only provided fields will be updated.
    """
    edge = db.query(TestcaseChainEdge).filter(TestcaseChainEdge.id == edge_id).first()

    if not edge:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Chain edge with ID {edge_id} not found"
        )

    # Update only provided fields
    update_data = edge_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(edge, field, value)

    db.add(edge)
    db.commit()
    db.refresh(edge)

    logger.info(f"Updated chain edge {edge_id} by user {current_user.id}")
    return edge


@router.delete("/edges/{edge_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_chain_edge(
    edge_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Delete a chain edge.

    Soft deletes the edge connection between two nodes.
    """
    edge = db.query(TestcaseChainEdge).filter(TestcaseChainEdge.id == edge_id).first()

    if not edge:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Chain edge with ID {edge_id} not found"
        )

    # Soft delete the edge
    edge.soft_delete()
    db.add(edge)
    db.commit()

    logger.info(f"Deleted chain edge {edge_id} by user {current_user.id}")


# Chain Execution Management
@router.post("/chains/{chain_id}/execute", response_model=ChainExecutionSchema, status_code=status.HTTP_201_CREATED)
async def start_chain_execution(
    chain_id: int,
    execution_data: ChainExecutionCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Start execution of a testcase chain.

    Initiates execution of the specified chain with the provided context.
    """
    service = TestcaseChainService(db)

    try:
        execution = service.start_chain_execution(
            chain_id=chain_id,
            started_by=current_user.id,
            execution_context=execution_data.execution_context
        )

        logger.info(f"Started execution {execution.id} for chain {chain_id} by user {current_user.id}")
        return execution

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to start chain execution: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start chain execution: {str(e)}"
        )


@router.get("/executions", response_model=List[ChainExecutionSchema])
async def list_chain_executions(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    chain_id: Optional[int] = Query(None, description="Filter by chain ID"),
    status: Optional[str] = Query(None, description="Filter by execution status"),
    started_by: Optional[int] = Query(None, description="Filter by user who started execution"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    List chain executions with optional filtering.

    Returns a list of chain executions with optional filtering.
    """
    query = db.query(ChainExecution)

    if chain_id:
        query = query.filter(ChainExecution.chain_id == chain_id)

    if status:
        query = query.filter(ChainExecution.status == status)

    if started_by:
        query = query.filter(ChainExecution.started_by == started_by)

    # Order by most recent first
    query = query.order_by(ChainExecution.start_time.desc())

    executions = query.offset(skip).limit(limit).all()
    return executions


@router.get("/executions/{execution_id}", response_model=ChainExecutionWithDetails)
async def get_chain_execution(
    execution_id: int,
    include_node_executions: bool = Query(True, description="Include node execution details"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get a specific chain execution by ID.

    Returns the execution details and optionally includes node execution details.
    """
    execution = db.query(ChainExecution).filter(ChainExecution.id == execution_id).first()

    if not execution:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Chain execution with ID {execution_id} not found"
        )

    if include_node_executions:
        # Get node executions
        node_executions = db.query(NodeExecution).filter(
            NodeExecution.chain_execution_id == execution_id
        ).all()

        return ChainExecutionWithDetails(
            **execution.__dict__,
            node_executions=node_executions
        )

    return execution


@router.get("/executions/{execution_id}/next-nodes", response_model=List[TestcaseChainNodeSchema])
async def get_next_executable_nodes(
    execution_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get the next nodes that can be executed in a chain.

    Returns a list of nodes that are ready for execution based on dependencies.
    """
    service = TestcaseChainService(db)

    try:
        nodes = service.get_next_executable_nodes(execution_id)
        return nodes

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to get next executable nodes: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get next executable nodes: {str(e)}"
        )
