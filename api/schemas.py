"""Base schema models for the API."""
from typing import List, Optional, TypeVar, Generic
from pydantic import BaseModel, ConfigDict
from datetime import datetime

class ErrorResponse(BaseModel):
    """Error response schema."""
    detail: str
    code: str
    timestamp: datetime = datetime.utcnow()

    model_config = ConfigDict(
        from_attributes=True,
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )

class SuccessResponse(BaseModel):
    """Success response schema."""
    message: str
    data: Optional[dict] = None

T = TypeVar("T")

class PaginatedResponse(BaseModel, Generic[T]):
    """Paginated response schema with generic type support.

    Args:
        T: The type of items in the response

    Attributes:
        items: List of items of type T
        total: Total number of items across all pages
        page: Current page number
        size: Number of items per page
    """
    items: List[T]
    total: int
    page: int
    size: int