"""Rate limiting router."""

from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from api.database import get_db
from api.models.user import User
from api.auth.deps import get_current_user, get_admin_user
from api.config.rate_limit_config import rate_limit_settings
from api.auth.rate_limit.storage import get_storage

router = APIRouter()

@router.get("/status")
async def get_rate_limit_status(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """Get current rate limit status for the user.
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Rate limit status information
    """
    storage = get_storage()
    
    # Get current usage for user
    current_usage = {}
    for endpoint in rate_limit_settings.ENDPOINT_LIMITS:
        key = f"rate_limit:{current_user.id}:{endpoint}"
        data = await storage.get(key)
        if data:
            current_usage[endpoint] = {
                "count": data["count"],
                "reset_time": int(data["reset_time"])
            }
    
    return {
        "global_limit": rate_limit_settings.DEFAULT_LIMIT,
        "global_window": rate_limit_settings.DEFAULT_WINDOW,
        "endpoint_limits": rate_limit_settings.ENDPOINT_LIMITS,
        "current_usage": current_usage
    }

@router.post("/admin/config")
async def update_rate_limit_config(
    config: Dict[str, Any],
    current_user: User = Depends(get_admin_user)
) -> Dict[str, Any]:
    """Update rate limit configuration.
    
    Args:
        config: New configuration settings
        current_user: Current authenticated admin user
        
    Returns:
        Updated configuration
        
    Raises:
        HTTPException: If configuration is invalid
    """
    try:
        # Validate configuration
        if "DEFAULT_LIMIT" in config:
            if not isinstance(config["DEFAULT_LIMIT"], int) or config["DEFAULT_LIMIT"] < 1:
                raise ValueError("DEFAULT_LIMIT must be a positive integer")
        
        if "DEFAULT_WINDOW" in config:
            if not isinstance(config["DEFAULT_WINDOW"], int) or config["DEFAULT_WINDOW"] < 1:
                raise ValueError("DEFAULT_WINDOW must be a positive integer")
        
        if "ENDPOINT_LIMITS" in config:
            for endpoint, limits in config["ENDPOINT_LIMITS"].items():
                if not isinstance(limits, dict):
                    raise ValueError(f"Invalid limits for endpoint {endpoint}")
                if "limit" not in limits or "window" not in limits:
                    raise ValueError(f"Missing limit or window for endpoint {endpoint}")
                if not isinstance(limits["limit"], int) or limits["limit"] < 1:
                    raise ValueError(f"Invalid limit for endpoint {endpoint}")
                if not isinstance(limits["window"], int) or limits["window"] < 1:
                    raise ValueError(f"Invalid window for endpoint {endpoint}")
        
        # Update configuration
        for key, value in config.items():
            if hasattr(rate_limit_settings, key):
                setattr(rate_limit_settings, key, value)
        
        return {
            "message": "Rate limit configuration updated successfully",
            "config": {
                "DEFAULT_LIMIT": rate_limit_settings.DEFAULT_LIMIT,
                "DEFAULT_WINDOW": rate_limit_settings.DEFAULT_WINDOW,
                "ENDPOINT_LIMITS": rate_limit_settings.ENDPOINT_LIMITS
            }
        }
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update rate limit configuration"
        ) 