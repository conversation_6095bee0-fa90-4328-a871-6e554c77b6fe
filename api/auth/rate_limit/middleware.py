"""Rate limiting middleware for FastAPI."""

import time
from typing import Op<PERSON>, Dict, Any
from fastapi import Request, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response

from api.config.rate_limit_config import rate_limit_settings
from api.auth.rate_limit.storage import get_storage

class RateLimitMiddleware(BaseHTTPMiddleware):
    """Middleware for rate limiting requests."""
    
    def __init__(self, app):
        """Initialize the middleware."""
        super().__init__(app)
        self.storage = get_storage()
    
    async def dispatch(
        self,
        request: Request,
        call_next
    ) -> Response:
        """Process the request and apply rate limiting."""
        if not rate_limit_settings.ENABLED:
            return await call_next(request)
        
        # Get rate limit key
        key = await self._get_rate_limit_key(request)
        if not key:
            return await call_next(request)
        
        # Get rate limit data
        limit, window = await self._get_rate_limit(request)
        if not limit or not window:
            return await call_next(request)
        
        # Check rate limit
        data = await self.storage.get(key)
        if not data:
            # First request in window
            await self.storage.set(key, {
                "count": 1,
                "reset_time": time.time() + window
            }, window)
            response = await call_next(request)
            await self._add_rate_limit_headers(
                response,
                limit,
                1,
                int(time.time() + window)
            )
            return response
        
        # Check if window has expired
        if time.time() > data["reset_time"]:
            # Reset window
            await self.storage.set(key, {
                "count": 1,
                "reset_time": time.time() + window
            }, window)
            response = await call_next(request)
            await self._add_rate_limit_headers(
                response,
                limit,
                1,
                int(time.time() + window)
            )
            return response
        
        # Check if limit exceeded
        if data["count"] >= limit:
            raise HTTPException(
                status_code=429,
                detail="Too many requests"
            )
        
        # Increment counter
        data["count"] += 1
        await self.storage.set(key, data, window)
        
        # Process request
        response = await call_next(request)
        await self._add_rate_limit_headers(
            response,
            limit,
            data["count"],
            int(data["reset_time"])
        )
        return response
    
    async def _get_rate_limit_key(self, request: Request) -> Optional[str]:
        """Get the rate limit key for the request."""
        # Get client identifier (IP or user ID)
        client_id = request.client.host
        if hasattr(request.state, "user"):
            client_id = str(request.state.user.id)
        
        # Get endpoint path
        path = request.url.path
        
        return f"rate_limit:{client_id}:{path}"
    
    async def _get_rate_limit(
        self,
        request: Request
    ) -> tuple[Optional[int], Optional[int]]:
        """Get rate limit settings for the request."""
        # Check endpoint-specific limits
        path = request.url.path
        if path in rate_limit_settings.ENDPOINT_LIMITS:
            limit_data = rate_limit_settings.ENDPOINT_LIMITS[path]
            return limit_data["limit"], limit_data["window"]
        
        # Use default limits
        return (
            rate_limit_settings.DEFAULT_LIMIT,
            rate_limit_settings.DEFAULT_WINDOW
        )
    
    async def _add_rate_limit_headers(
        self,
        response: Response,
        limit: int,
        remaining: int,
        reset_time: int
    ) -> None:
        """Add rate limit headers to the response."""
        response.headers["X-RateLimit-Limit"] = str(limit)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(reset_time)
        
        if remaining == 0:
            response.headers["Retry-After"] = str(reset_time - int(time.time())) 