"""Rate limiting storage backend."""

import time
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import redis
from redis.exceptions import RedisError

from api.config.rate_limit_config import rate_limit_settings

class RateLimitStorage:
    """Base class for rate limit storage backends."""
    
    def __init__(self):
        """Initialize the storage backend."""
        self._storage: Dict[str, Any] = {}
    
    async def get(self, key: str) -> Optional[Dict[str, Any]]:
        """Get rate limit data for a key."""
        raise NotImplementedError
    
    async def set(self, key: str, data: Dict[str, Any], expire: int) -> bool:
        """Set rate limit data for a key with expiration."""
        raise NotImplementedError
    
    async def delete(self, key: str) -> bool:
        """Delete rate limit data for a key."""
        raise NotImplementedError
    
    async def cleanup(self) -> None:
        """Clean up expired rate limit data."""
        raise NotImplementedError

class MemoryStorage(RateLimitStorage):
    """In-memory storage backend for rate limiting."""
    
    async def get(self, key: str) -> Optional[Dict[str, Any]]:
        """Get rate limit data from memory."""
        if key not in self._storage:
            return None
        
        data = self._storage[key]
        if data["expires_at"] < time.time():
            await self.delete(key)
            return None
        
        return data
    
    async def set(self, key: str, data: Dict[str, Any], expire: int) -> bool:
        """Set rate limit data in memory."""
        try:
            self._storage[key] = {
                **data,
                "expires_at": time.time() + expire
            }
            return True
        except Exception:
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete rate limit data from memory."""
        try:
            if key in self._storage:
                del self._storage[key]
            return True
        except Exception:
            return False
    
    async def cleanup(self) -> None:
        """Clean up expired rate limit data from memory."""
        current_time = time.time()
        expired_keys = [
            key for key, data in self._storage.items()
            if data["expires_at"] < current_time
        ]
        for key in expired_keys:
            await self.delete(key)

class RedisStorage(RateLimitStorage):
    """Redis storage backend for rate limiting."""
    
    def __init__(self):
        """Initialize Redis storage."""
        super().__init__()
        if not rate_limit_settings.REDIS_URL:
            raise ValueError("Redis URL is required for Redis storage")
        
        self.redis = redis.from_url(
            rate_limit_settings.REDIS_URL,
            decode_responses=True
        )
    
    async def get(self, key: str) -> Optional[Dict[str, Any]]:
        """Get rate limit data from Redis."""
        try:
            data = self.redis.get(key)
            if not data:
                return None
            return eval(data)  # Convert string representation back to dict
        except RedisError:
            return None
    
    async def set(self, key: str, data: Dict[str, Any], expire: int) -> bool:
        """Set rate limit data in Redis."""
        try:
            self.redis.setex(
                key,
                expire,
                str(data)  # Convert dict to string for storage
            )
            return True
        except RedisError:
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete rate limit data from Redis."""
        try:
            self.redis.delete(key)
            return True
        except RedisError:
            return False
    
    async def cleanup(self) -> None:
        """Clean up expired rate limit data from Redis."""
        # Redis handles expiration automatically
        pass

def get_storage() -> RateLimitStorage:
    """Get the configured storage backend."""
    if rate_limit_settings.STORAGE_TYPE == "redis":
        return RedisStorage()
    return MemoryStorage() 