"""Session management router for the FastAPI application."""

from datetime import datetime, timed<PERSON>ta
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Security, BackgroundTasks
from sqlalchemy.orm import Session
import logging
from pydantic import BaseModel

from api.database import get_db
from api.models.user import User
from api.models.session import UserSession
from api.dependencies import get_current_user
from api.utils.rate_limiter import auth_rate_limit

logger = logging.getLogger(__name__)

# Create the router
router = APIRouter()

class SessionInfo(BaseModel):
    """Schema for session information."""
    id: str
    created_at: datetime
    last_activity: datetime
    ip_address: str
    user_agent: str
    is_active: bool

@router.get("/list/")
async def list_sessions(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(auth_rate_limit)
) -> List[SessionInfo]:
    """List all active sessions for the current user.
    
    Args:
        db: Database session
        current_user: The authenticated user
        
    Returns:
        List of active sessions
    """
    sessions = current_user.get_active_sessions()
    return [
        SessionInfo(
            id=session.id,
            created_at=session.created_at,
            last_activity=session.last_activity,
            ip_address=session.ip_address,
            user_agent=session.user_agent,
            is_active=session.is_active
        )
        for session in sessions
    ]

@router.post("/revoke/{session_id}")
async def revoke_session(
    session_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(auth_rate_limit)
) -> dict:
    """Revoke a specific session.
    
    Args:
        session_id: ID of the session to revoke
        db: Database session
        current_user: The authenticated user
        
    Returns:
        Success message
    """
    session = db.query(UserSession).filter(
        UserSession.id == session_id,
        UserSession.user_id == current_user.id
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )
    
    session.is_active = False
    session.revoked_at = datetime.utcnow()
    db.commit()
    
    logger.info(f"Session {session_id} revoked for user: {current_user.username}")
    
    return {"message": "Session revoked successfully"}

@router.post("/revoke-all/")
async def revoke_all_sessions(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(auth_rate_limit)
) -> dict:
    """Revoke all sessions except the current one.
    
    Args:
        db: Database session
        current_user: The authenticated user
        
    Returns:
        Success message
    """
    # Get current session ID from request
    current_session_id = current_user.get_current_session_id()
    
    # Revoke all other active sessions
    sessions = db.query(UserSession).filter(
        UserSession.user_id == current_user.id,
        UserSession.is_active == True,
        UserSession.id != current_session_id
    ).all()
    
    for session in sessions:
        session.is_active = False
        session.revoked_at = datetime.utcnow()
    
    db.commit()
    
    logger.info(f"All sessions revoked for user: {current_user.username}")
    
    return {"message": "All sessions revoked successfully"}

@router.get("/current/")
async def get_current_session(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(auth_rate_limit)
) -> SessionInfo:
    """Get information about the current session.
    
    Args:
        db: Database session
        current_user: The authenticated user
        
    Returns:
        Current session information
    """
    current_session_id = current_user.get_current_session_id()
    session = db.query(UserSession).filter(
        UserSession.id == current_session_id,
        UserSession.user_id == current_user.id
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Current session not found"
        )
    
    return SessionInfo(
        id=session.id,
        created_at=session.created_at,
        last_activity=session.last_activity,
        ip_address=session.ip_address,
        user_agent=session.user_agent,
        is_active=session.is_active
    ) 