"""Token management router."""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Dict, Any

from api.database import get_db
from api.models.user import User
from api.auth.token_utils import (
    create_access_token,
    create_refresh_token,
    verify_token,
    refresh_access_token,
    revoke_token,
    revoke_all_tokens
)
from api.auth.deps import get_current_user
from api.config.token_config import token_settings

router = APIRouter()

@router.post("/refresh")
async def refresh_token(
    refresh_token: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """Refresh an access token using a refresh token.
    
    Args:
        refresh_token: Valid refresh token
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        New access token
        
    Raises:
        HTTPException: If refresh token is invalid
    """
    try:
        new_access_token = refresh_access_token(
            refresh_token=refresh_token,
            secret_key=token_settings.SECRET_KEY,
            algorithm=token_settings.ALGORITHM,
            access_token_expire_minutes=token_settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
        return {"access_token": new_access_token}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )

@router.post("/revoke")
async def revoke_user_token(
    token: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """Revoke a token.
    
    Args:
        token: Token to revoke
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Success message
        
    Raises:
        HTTPException: If token revocation fails
    """
    if revoke_token(db, token, current_user.id):
        return {"message": "Token revoked successfully"}
    raise HTTPException(
        status_code=status.HTTP_400_BAD_REQUEST,
        detail="Failed to revoke token"
    )

@router.post("/revoke-all")
async def revoke_all_user_tokens(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """Revoke all tokens for the current user.
    
    Args:
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Success message with count of revoked tokens
    """
    revoked_count = revoke_all_tokens(db, current_user.id)
    return {
        "message": "All tokens revoked successfully",
        "revoked_count": revoked_count
    } 