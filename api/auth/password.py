"""Password management router for the FastAPI application."""

from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Security, Body, BackgroundTasks
from sqlalchemy.orm import Session
import secrets
import logging
from pydantic import BaseModel, EmailStr

from api.database import get_db
from api.models.user import User
from api.dependencies import get_current_user
from api.auth.utils import (
    validate_password,
    send_password_reset_email,
    send_password_change_notification
)
from api.utils.rate_limiter import auth_rate_limit

logger = logging.getLogger(__name__)

# Create the router
router = APIRouter()

class PasswordChange(BaseModel):
    """Schema for password change request."""
    current_password: str
    new_password: str

class PasswordResetRequest(BaseModel):
    """Schema for password reset request."""
    email: EmailStr

class PasswordReset(BaseModel):
    """Schema for password reset confirmation."""
    token: str
    new_password: str

@router.post("/change/")
async def change_password(
    password_data: Password<PERSON>hange,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    background_tasks: BackgroundTasks,
    _: None = Depends(auth_rate_limit)
) -> dict:
    """Change user password.
    
    Args:
        password_data: Current and new password
        db: Database session
        current_user: The authenticated user
        background_tasks: Background tasks handler
        
    Returns:
        Success message
    """
    # Verify current password
    if not current_user.check_password(password_data.current_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Incorrect current password"
        )
    
    # Validate new password
    is_valid, message = validate_password(password_data.new_password)
    if not is_valid:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=message
        )
    
    # Check if new password is same as current
    if current_user.check_password(password_data.new_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="New password must be different from current password"
        )
    
    # Update password
    current_user.set_password(password_data.new_password)
    current_user.password_changed_at = datetime.utcnow()
    
    db.commit()
    
    # Send notification email
    background_tasks.add_task(
        send_password_change_notification,
        current_user.email,
        current_user.username
    )
    
    logger.info(f"Password changed for user: {current_user.username}")
    
    return {"message": "Password changed successfully"}

@router.post("/reset-request/")
async def request_password_reset(
    reset_request: PasswordResetRequest,
    db: Session = Depends(get_db),
    background_tasks: BackgroundTasks,
    _: None = Depends(auth_rate_limit)
) -> dict:
    """Request password reset.
    
    Args:
        reset_request: Email address for password reset
        db: Database session
        background_tasks: Background tasks handler
        
    Returns:
        Success message
    """
    user = db.query(User).filter(User.email == reset_request.email).first()
    
    # Don't reveal if email exists
    if not user:
        return {"message": "If the email exists, a password reset link has been sent"}
    
    # Generate reset token
    token = secrets.token_urlsafe(32)
    user.set_password_reset_token(token)
    
    db.commit()
    
    # Send reset email
    background_tasks.add_task(
        send_password_reset_email,
        user.email,
        token
    )
    
    logger.info(f"Password reset requested for user: {user.username}")
    
    return {"message": "If the email exists, a password reset link has been sent"}

@router.post("/reset/")
async def reset_password(
    reset_data: PasswordReset,
    db: Session = Depends(get_db),
    background_tasks: BackgroundTasks,
    _: None = Depends(auth_rate_limit)
) -> dict:
    """Reset password using reset token.
    
    Args:
        reset_data: Reset token and new password
        db: Database session
        background_tasks: Background tasks handler
        
    Returns:
        Success message
    """
    # Find user with valid reset token
    user = db.query(User).filter(
        User.password_reset_token == reset_data.token,
        User.password_reset_expires > datetime.utcnow()
    ).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired reset token"
        )
    
    # Validate new password
    is_valid, message = validate_password(reset_data.new_password)
    if not is_valid:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=message
        )
    
    # Update password
    user.set_password(reset_data.new_password)
    user.password_changed_at = datetime.utcnow()
    user.clear_password_reset_token()
    
    db.commit()
    
    # Send notification email
    background_tasks.add_task(
        send_password_change_notification,
        user.email,
        user.username
    )
    
    logger.info(f"Password reset completed for user: {user.username}")
    
    return {"message": "Password reset successfully"} 