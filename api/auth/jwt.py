"""JWT authentication module for the FastAPI cybersecurity data platform.

This module provides JWT (JSON Web Token) based authentication functionality,
including token generation and validation. It implements security best practices
for token handling and user authentication.

Note:
    In production, ensure SECRET_KEY is properly secured using environment variables.
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import JW<PERSON>rror, jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer

# Security configuration
SECRET_KEY = "your-secret-key-keep-it-secret"  # In production, use env vars
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

def create_access_token(
    data: Dict[str, Any], 
    expires_delta: Optional[timedelta] = None
) -> str:
    """Create a new JWT access token.

    Args:
        data: Dictionary containing claims to be encoded in the token.
        expires_delta: Optional timedelta for token expiration. Defaults to 15 minutes
            if not specified.

    Returns:
        str: Encoded JWT token string.

    Example:
        >>> token = create_access_token({"sub": "<EMAIL>"})
        >>> assert isinstance(token, str)
    """
    to_encode = data.copy()
    expire = datetime.utcnow() + (expires_delta or timedelta(minutes=15))
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(token: str = Depends(oauth2_scheme)) -> str:
    """Validate and extract the current user from a JWT token.

    Args:
        token: JWT token string obtained from the authorization header.

    Returns:
        str: Username extracted from the validated token.

    Raises:
        HTTPException: If token validation fails or required claims are missing.

    Example:
        >>> # In a FastAPI route
        >>> @app.get("/protected")
        >>> async def protected_route(user: str = Depends(get_current_user)):
        >>>     return {"message": f"Hello {user}"}
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        return username
    except JWTError:
        raise credentials_exception