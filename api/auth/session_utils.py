"""Utility functions for session management."""

import hashlib
from datetime import datetime, timed<PERSON><PERSON>
from typing import <PERSON><PERSON>, <PERSON><PERSON>
from fastapi import Request
from sqlalchemy.orm import Session

from api.models.session import UserSession
from api.models.user import User

def hash_token(token: str) -> str:
    """Hash a session token for storage.
    
    Args:
        token: The session token to hash
        
    Returns:
        Hashed token string
    """
    return hashlib.sha256(token.encode()).hexdigest()

def get_client_info(request: Request) -> Tuple[str, str]:
    """Get client IP and user agent from request.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Tuple of (ip_address, user_agent)
    """
    # Get IP address, handling proxy headers
    forwarded_for = request.headers.get("X-Forwarded-For", "")
    if forwarded_for:
        ip_address = forwarded_for.split(",")[0].strip()
    else:
        ip_address = request.client.host if request.client else "unknown"
        
    user_agent = request.headers.get("User-Agent", "unknown")
    
    return ip_address, user_agent

def create_session(
    db: Session,
    user: User,
    token: str,
    request: Request,
    expires_in_hours: int = 24
) -> UserSession:
    """Create a new user session.
    
    Args:
        db: Database session
        user: User object
        token: Session token
        request: FastAPI request object
        expires_in_hours: Number of hours until session expires
        
    Returns:
        Created UserSession instance
    """
    ip_address, user_agent = get_client_info(request)
    token_hash = hash_token(token)
    
    session = UserSession.create_session(
        user_id=user.id,
        token_hash=token_hash,
        ip_address=ip_address,
        user_agent=user_agent,
        expires_in_hours=expires_in_hours
    )
    
    db.add(session)
    db.commit()
    db.refresh(session)
    
    return session

def get_session_by_token(
    db: Session,
    token: str
) -> Optional[UserSession]:
    """Get a session by its token.
    
    Args:
        db: Database session
        token: Session token
        
    Returns:
        UserSession if found and valid, None otherwise
    """
    token_hash = hash_token(token)
    
    session = db.query(UserSession).filter(
        UserSession.token_hash == token_hash,
        UserSession.is_active == True
    ).first()
    
    if session and session.is_expired():
        session.revoke()
        db.commit()
        return None
        
    return session

def revoke_session(
    db: Session,
    session_id: str,
    user_id: str
) -> bool:
    """Revoke a specific session.
    
    Args:
        db: Database session
        session_id: ID of session to revoke
        user_id: ID of user who owns the session
        
    Returns:
        True if session was revoked, False if not found
    """
    session = db.query(UserSession).filter(
        UserSession.id == session_id,
        UserSession.user_id == user_id,
        UserSession.is_active == True
    ).first()
    
    if session:
        session.revoke()
        db.commit()
        return True
        
    return False

def revoke_all_sessions(
    db: Session,
    user_id: str,
    exclude_session_id: Optional[str] = None
) -> int:
    """Revoke all sessions for a user except optionally one.
    
    Args:
        db: Database session
        user_id: ID of user whose sessions to revoke
        exclude_session_id: Optional session ID to exclude from revocation
        
    Returns:
        Number of sessions revoked
    """
    query = db.query(UserSession).filter(
        UserSession.user_id == user_id,
        UserSession.is_active == True
    )
    
    if exclude_session_id:
        query = query.filter(UserSession.id != exclude_session_id)
        
    sessions = query.all()
    
    for session in sessions:
        session.revoke()
        
    db.commit()
    return len(sessions)

def cleanup_expired_sessions(db: Session) -> int:
    """Clean up expired sessions.
    
    Args:
        db: Database session
        
    Returns:
        Number of sessions cleaned up
    """
    expired = db.query(UserSession).filter(
        UserSession.is_active == True,
        UserSession.expires_at <= datetime.utcnow()
    ).all()
    
    for session in expired:
        session.revoke()
        
    db.commit()
    return len(expired) 