"""Token management utilities."""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
import jwt
from sqlalchemy.orm import Session
from fastapi import HTTPEx<PERSON>, status

from api.models.token import TokenBlacklist
from api.config.token_config import token_settings

def create_access_token(
    user_id: str,
    username: str,
    secret_key: Optional[str] = None,
    algorithm: Optional[str] = None,
    access_token_expire_minutes: Optional[int] = None
) -> str:
    """Create a new access token.
    
    Args:
        user_id: ID of the user
        username: Username of the user
        secret_key: Optional secret key for token signing (defaults to config)
        algorithm: Optional algorithm to use for token signing (defaults to config)
        access_token_expire_minutes: Optional minutes until token expires (defaults to config)
        
    Returns:
        JWT access token
    """
    expires_delta = timedelta(minutes=access_token_expire_minutes or token_settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    expire = datetime.utcnow() + expires_delta
    
    to_encode = {
        "sub": user_id,
        "username": username,
        "type": "access",
        "exp": expire
    }
    
    return jwt.encode(
        to_encode,
        secret_key or token_settings.SECRET_KEY,
        algorithm=algorithm or token_settings.ALGORITHM
    )

def create_refresh_token(
    user_id: str,
    username: str,
    secret_key: Optional[str] = None,
    algorithm: Optional[str] = None,
    refresh_token_expire_days: Optional[int] = None
) -> str:
    """Create a new refresh token.
    
    Args:
        user_id: ID of the user
        username: Username of the user
        secret_key: Optional secret key for token signing (defaults to config)
        algorithm: Optional algorithm to use for token signing (defaults to config)
        refresh_token_expire_days: Optional days until token expires (defaults to config)
        
    Returns:
        JWT refresh token
    """
    expires_delta = timedelta(days=refresh_token_expire_days or token_settings.REFRESH_TOKEN_EXPIRE_DAYS)
    expire = datetime.utcnow() + expires_delta
    
    to_encode = {
        "sub": user_id,
        "username": username,
        "type": "refresh",
        "exp": expire
    }
    
    return jwt.encode(
        to_encode,
        secret_key or token_settings.SECRET_KEY,
        algorithm=algorithm or token_settings.ALGORITHM
    )

def verify_token(
    token: str,
    secret_key: Optional[str] = None,
    algorithm: Optional[str] = None
) -> bool:
    """Verify a token's validity.
    
    Args:
        token: JWT token to verify
        secret_key: Optional secret key used for token signing (defaults to config)
        algorithm: Optional algorithm used for token signing (defaults to config)
        
    Returns:
        True if token is valid, False otherwise
    """
    try:
        payload = decode_token(
            token,
            secret_key or token_settings.SECRET_KEY,
            algorithm or token_settings.ALGORITHM
        )
        
        # Check required fields
        if not all(key in payload for key in ["sub", "username", "type", "exp"]):
            return False
            
        # Check token type
        if payload["type"] not in ["access", "refresh"]:
            return False
            
        # Check expiration
        if datetime.fromtimestamp(payload["exp"]) < datetime.utcnow():
            return False
            
        return True
        
    except jwt.InvalidTokenError:
        return False

def decode_token(
    token: str,
    secret_key: Optional[str] = None,
    algorithm: Optional[str] = None
) -> Dict[str, Any]:
    """Decode a JWT token.
    
    Args:
        token: JWT token to decode
        secret_key: Optional secret key used for token signing (defaults to config)
        algorithm: Optional algorithm used for token signing (defaults to config)
        
    Returns:
        Decoded token payload
        
    Raises:
        HTTPException: If token is invalid
    """
    try:
        return jwt.decode(
            token,
            secret_key or token_settings.SECRET_KEY,
            algorithms=[algorithm or token_settings.ALGORITHM]
        )
    except jwt.InvalidTokenError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        ) from e

def refresh_access_token(
    refresh_token: str,
    secret_key: Optional[str] = None,
    algorithm: Optional[str] = None,
    access_token_expire_minutes: Optional[int] = None
) -> str:
    """Create a new access token using a refresh token.
    
    Args:
        refresh_token: Valid refresh token
        secret_key: Optional secret key for token signing (defaults to config)
        algorithm: Optional algorithm to use for token signing (defaults to config)
        access_token_expire_minutes: Optional minutes until new token expires (defaults to config)
        
    Returns:
        New JWT access token
        
    Raises:
        ValueError: If refresh token is invalid
    """
    try:
        payload = decode_token(
            refresh_token,
            secret_key or token_settings.SECRET_KEY,
            algorithm or token_settings.ALGORITHM
        )
        
        if payload["type"] != "refresh":
            raise ValueError("Invalid token type")
            
        return create_access_token(
            user_id=payload["sub"],
            username=payload["username"],
            secret_key=secret_key,
            algorithm=algorithm,
            access_token_expire_minutes=access_token_expire_minutes
        )
        
    except jwt.InvalidTokenError:
        raise ValueError("Invalid refresh token")

def revoke_token(
    db: Session,
    token: str,
    user_id: str
) -> bool:
    """Revoke a token by adding it to the blacklist.
    
    Args:
        db: Database session
        token: Token to revoke
        user_id: ID of the user who owns the token
        
    Returns:
        True if token was revoked, False otherwise
    """
    if not token_settings.BLACKLIST_ENABLED:
        return True
        
    try:
        # Add token to blacklist
        blacklisted_token = TokenBlacklist(
            token=token,
            user_id=user_id,
            revoked_at=datetime.utcnow()
        )
        db.add(blacklisted_token)
        db.commit()
        return True
        
    except Exception:
        db.rollback()
        return False

def revoke_all_tokens(
    db: Session,
    user_id: str,
    exclude_token: Optional[str] = None
) -> int:
    """Revoke all tokens for a user.
    
    Args:
        db: Database session
        user_id: ID of the user whose tokens to revoke
        exclude_token: Optional token to exclude from revocation
        
    Returns:
        Number of tokens revoked
    """
    if not token_settings.BLACKLIST_ENABLED:
        return 0
        
    try:
        # Get all active sessions for the user
        active_sessions = db.query(UserSession).filter(
            UserSession.user_id == user_id,
            UserSession.is_active == True
        ).all()
        
        revoked_count = 0
        
        # Revoke each session's token
        for session in active_sessions:
            if session.token != exclude_token:
                if revoke_token(db, session.token, user_id):
                    revoked_count += 1
                    
        return revoked_count
        
    except Exception:
        db.rollback()
        return 0

def cleanup_blacklist(
    db: Session,
    max_age_hours: Optional[int] = None
) -> int:
    """Clean up old entries from the token blacklist.
    
    Args:
        db: Database session
        max_age_hours: Optional maximum age of blacklist entries in hours (defaults to config)
        
    Returns:
        Number of entries cleaned up
    """
    if not token_settings.BLACKLIST_ENABLED:
        return 0
        
    try:
        max_age = timedelta(hours=max_age_hours or token_settings.BLACKLIST_CLEANUP_INTERVAL_HOURS)
        cutoff_time = datetime.utcnow() - max_age
        
        # Delete old blacklist entries
        deleted_count = db.query(TokenBlacklist).filter(
            TokenBlacklist.revoked_at < cutoff_time
        ).delete()
        
        db.commit()
        return deleted_count
        
    except Exception:
        db.rollback()
        return 0 