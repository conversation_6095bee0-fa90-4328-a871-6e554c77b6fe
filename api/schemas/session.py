"""Pydantic schemas for session management."""
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel

class DeviceInfoResponse(BaseModel):
    """Device information response schema."""
    user_agent: str
    ip_address: Optional[str]
    browser: str
    os: str
    device_type: str
    last_seen: datetime

    class Config:
        """Pydantic config."""
        from_attributes = True

class SessionResponse(BaseModel):
    """Session response schema."""
    id: int
    created_at: datetime
    last_active: Optional[datetime]
    is_active: bool
    device_info: DeviceInfoResponse

    class Config:
        """Pydantic config."""
        from_attributes = True

class SessionList(BaseModel):
    """List of sessions response schema."""
    sessions: List[SessionResponse]

class SessionCreate(BaseModel):
    """Session creation schema."""
    user_agent: str
    ip_address: Optional[str]
