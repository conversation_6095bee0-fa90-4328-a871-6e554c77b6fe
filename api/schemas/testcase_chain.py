"""
Pydantic schemas for Enhanced Testcase Chaining & Sequencing.

This module defines the request/response schemas for testcase chains,
nodes, edges, executions, and conditions.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator


# Enums
class ChainStatus(str, Enum):
    """Enumeration of testcase chain statuses."""
    DRAFT = "draft"
    ACTIVE = "active"
    COMPLETED = "completed"
    ARCHIVED = "archived"
    PAUSED = "paused"


class NodeType(str, Enum):
    """Enumeration of testcase chain node types."""
    START = "start"
    STANDARD = "standard"
    CONDITIONAL = "conditional"
    PARALLEL = "parallel"
    END = "end"


class EdgeType(str, Enum):
    """Enumeration of testcase chain edge types."""
    STANDARD = "standard"
    SUCCESS_PATH = "success_path"
    FAILURE_PATH = "failure_path"
    CONDITIONAL = "conditional"
    PARALLEL = "parallel"


class ExecutionStatus(str, Enum):
    """Enumeration of execution statuses."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    ABORTED = "aborted"
    TIMEOUT = "timeout"


class NodeExecutionStatus(str, Enum):
    """Enumeration of node execution statuses."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    TIMEOUT = "timeout"
    ABORTED = "aborted"


class ConditionType(str, Enum):
    """Enumeration of condition types."""
    PRECONDITION = "precondition"
    POSTCONDITION = "postcondition"
    CLEANUP = "cleanup"


# Testcase Chain Schemas
class TestcaseChainBase(BaseModel):
    """Base schema for testcase chains."""
    name: str = Field(..., description="Name of the testcase chain")
    description: Optional[str] = Field(None, description="Description of the chain")
    chain_type: str = Field("sequential", description="Type of chain execution")
    max_execution_time_minutes: int = Field(60, gt=0, description="Maximum execution time in minutes")
    retry_on_failure: bool = Field(False, description="Whether to retry on failure")
    auto_cleanup: bool = Field(True, description="Whether to auto-cleanup after execution")


class TestcaseChainCreate(TestcaseChainBase):
    """Schema for creating a testcase chain."""
    pass


class TestcaseChainUpdate(BaseModel):
    """Schema for updating a testcase chain."""
    name: Optional[str] = Field(None, description="Name of the testcase chain")
    description: Optional[str] = Field(None, description="Description of the chain")
    status: Optional[ChainStatus] = Field(None, description="Status of the chain")
    chain_type: Optional[str] = Field(None, description="Type of chain execution")
    max_execution_time_minutes: Optional[int] = Field(None, gt=0, description="Maximum execution time")
    retry_on_failure: Optional[bool] = Field(None, description="Whether to retry on failure")
    auto_cleanup: Optional[bool] = Field(None, description="Whether to auto-cleanup")


class TestcaseChain(TestcaseChainBase):
    """Schema for testcase chain responses."""
    id: int
    status: ChainStatus
    created_by: int
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime]
    version: int
    
    class Config:
        from_attributes = True


# Testcase Chain Node Schemas
class TestcaseChainNodeBase(BaseModel):
    """Base schema for testcase chain nodes."""
    testcase_id: int = Field(..., description="ID of the testcase")
    node_type: NodeType = Field(NodeType.STANDARD, description="Type of node")
    execution_order: int = Field(0, description="Execution order within the chain")
    position_x: float = Field(0.0, description="X position for UI display")
    position_y: float = Field(0.0, description="Y position for UI display")
    condition_expression: Optional[str] = Field(None, description="Condition expression for conditional nodes")
    timeout_minutes: int = Field(30, gt=0, description="Timeout for node execution")
    retry_count: int = Field(0, ge=0, description="Number of retries on failure")
    continue_on_failure: bool = Field(False, description="Whether to continue chain on node failure")
    required_for_completion: bool = Field(True, description="Whether node is required for chain completion")


class TestcaseChainNodeCreate(TestcaseChainNodeBase):
    """Schema for creating a testcase chain node."""
    chain_id: int = Field(..., description="ID of the chain")


class TestcaseChainNodeUpdate(BaseModel):
    """Schema for updating a testcase chain node."""
    node_type: Optional[NodeType] = Field(None, description="Type of node")
    execution_order: Optional[int] = Field(None, description="Execution order")
    position_x: Optional[float] = Field(None, description="X position")
    position_y: Optional[float] = Field(None, description="Y position")
    condition_expression: Optional[str] = Field(None, description="Condition expression")
    timeout_minutes: Optional[int] = Field(None, gt=0, description="Timeout")
    retry_count: Optional[int] = Field(None, ge=0, description="Retry count")
    continue_on_failure: Optional[bool] = Field(None, description="Continue on failure")
    required_for_completion: Optional[bool] = Field(None, description="Required for completion")


class TestcaseChainNode(TestcaseChainNodeBase):
    """Schema for testcase chain node responses."""
    id: int
    chain_id: int
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime]
    version: int
    
    class Config:
        from_attributes = True


# Testcase Chain Edge Schemas
class TestcaseChainEdgeBase(BaseModel):
    """Base schema for testcase chain edges."""
    source_node_id: int = Field(..., description="ID of the source node")
    target_node_id: int = Field(..., description="ID of the target node")
    edge_type: EdgeType = Field(EdgeType.STANDARD, description="Type of edge")
    condition: Optional[str] = Field(None, description="Condition for traversing this edge")
    weight: int = Field(1, gt=0, description="Weight for edge prioritization")
    label: Optional[str] = Field(None, description="Label for the edge")
    description: Optional[str] = Field(None, description="Description of the edge")
    
    @validator('target_node_id')
    def validate_no_self_loop(cls, v, values):
        if 'source_node_id' in values and v == values['source_node_id']:
            raise ValueError('Source and target nodes cannot be the same')
        return v


class TestcaseChainEdgeCreate(TestcaseChainEdgeBase):
    """Schema for creating a testcase chain edge."""
    pass


class TestcaseChainEdgeUpdate(BaseModel):
    """Schema for updating a testcase chain edge."""
    edge_type: Optional[EdgeType] = Field(None, description="Type of edge")
    condition: Optional[str] = Field(None, description="Condition for traversing")
    weight: Optional[int] = Field(None, gt=0, description="Weight for prioritization")
    label: Optional[str] = Field(None, description="Label for the edge")
    description: Optional[str] = Field(None, description="Description")


class TestcaseChainEdge(TestcaseChainEdgeBase):
    """Schema for testcase chain edge responses."""
    id: int
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime]
    version: int
    
    class Config:
        from_attributes = True


# Chain Execution Schemas
class ChainExecutionBase(BaseModel):
    """Base schema for chain executions."""
    execution_context: Optional[Dict[str, Any]] = Field(None, description="Execution context and parameters")


class ChainExecutionCreate(ChainExecutionBase):
    """Schema for creating a chain execution."""
    chain_id: int = Field(..., description="ID of the chain to execute")


class ChainExecution(ChainExecutionBase):
    """Schema for chain execution responses."""
    id: int
    chain_id: int
    started_by: int
    start_time: datetime
    end_time: Optional[datetime]
    status: ExecutionStatus
    result_summary: Optional[Dict[str, Any]]
    error_message: Optional[str]
    total_nodes: int
    completed_nodes: int
    failed_nodes: int
    skipped_nodes: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Node Execution Schemas
class NodeExecutionBase(BaseModel):
    """Base schema for node executions."""
    result_data: Optional[Dict[str, Any]] = Field(None, description="Result data from execution")
    output_logs: Optional[str] = Field(None, description="Output logs from execution")


class NodeExecution(NodeExecutionBase):
    """Schema for node execution responses."""
    id: int
    chain_execution_id: int
    node_id: int
    start_time: Optional[datetime]
    end_time: Optional[datetime]
    status: NodeExecutionStatus
    error_message: Optional[str]
    attempt_number: int
    max_attempts: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Testcase Condition Schemas
class TestcaseConditionBase(BaseModel):
    """Base schema for testcase conditions."""
    condition_type: ConditionType = Field(..., description="Type of condition")
    name: str = Field(..., description="Name of the condition")
    description: Optional[str] = Field(None, description="Description of the condition")
    validation_script: Optional[str] = Field(None, description="Script to validate the condition")
    validation_type: str = Field("script", description="Type of validation")
    required: bool = Field(True, description="Whether the condition is required")
    execution_order: int = Field(0, description="Execution order for multiple conditions")
    timeout_seconds: int = Field(30, gt=0, description="Timeout for condition validation")


class TestcaseConditionCreate(TestcaseConditionBase):
    """Schema for creating a testcase condition."""
    testcase_id: int = Field(..., description="ID of the testcase")


class TestcaseConditionUpdate(BaseModel):
    """Schema for updating a testcase condition."""
    condition_type: Optional[ConditionType] = Field(None, description="Type of condition")
    name: Optional[str] = Field(None, description="Name of the condition")
    description: Optional[str] = Field(None, description="Description")
    validation_script: Optional[str] = Field(None, description="Validation script")
    validation_type: Optional[str] = Field(None, description="Validation type")
    required: Optional[bool] = Field(None, description="Whether required")
    execution_order: Optional[int] = Field(None, description="Execution order")
    timeout_seconds: Optional[int] = Field(None, gt=0, description="Timeout")


class TestcaseCondition(TestcaseConditionBase):
    """Schema for testcase condition responses."""
    id: int
    testcase_id: int
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime]
    version: int
    
    class Config:
        from_attributes = True


# Complex Response Schemas
class TestcaseChainWithNodes(TestcaseChain):
    """Schema for chain with its nodes and edges."""
    nodes: List[TestcaseChainNode] = Field(default_factory=list, description="Chain nodes")
    edges: List[TestcaseChainEdge] = Field(default_factory=list, description="Chain edges")


class ChainExecutionWithDetails(ChainExecution):
    """Schema for chain execution with node execution details."""
    node_executions: List[NodeExecution] = Field(default_factory=list, description="Node executions")


class ChainValidationResult(BaseModel):
    """Schema for chain validation results."""
    is_valid: bool = Field(..., description="Whether the chain is valid")
    errors: List[str] = Field(default_factory=list, description="Validation errors")
    warnings: List[str] = Field(default_factory=list, description="Validation warnings")
    cycle_detected: bool = Field(False, description="Whether a cycle was detected")
    unreachable_nodes: List[int] = Field(default_factory=list, description="IDs of unreachable nodes")


class ChainExecutionStats(BaseModel):
    """Schema for chain execution statistics."""
    total_executions: int = Field(..., description="Total number of executions")
    successful_executions: int = Field(..., description="Number of successful executions")
    failed_executions: int = Field(..., description="Number of failed executions")
    average_duration_minutes: Optional[float] = Field(None, description="Average execution duration")
    success_rate: float = Field(..., description="Success rate as percentage")
    most_common_failure_node: Optional[int] = Field(None, description="Node ID with most failures")
