"""
Pydantic schemas for the Advanced Soft Deletion Framework.

This module defines the request/response schemas for soft deletion
policy management, audit logging, and related operations.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator


class OperationType(str, Enum):
    """Enumeration of soft deletion operation types."""
    SOFT_DELETE = "soft_delete"
    RESTORE = "restore"
    PERMANENT_DELETE = "permanent_delete"
    POLICY_CHANGE = "policy_change"
    CASCADE_DELETE = "cascade_delete"
    CASCADE_RESTORE = "cascade_restore"


class ScheduleOperationType(str, Enum):
    """Enumeration of scheduled operation types."""
    PURGE = "purge"
    NOTIFICATION = "notification"
    POLICY_CHECK = "policy_check"


class ScheduleStatus(str, Enum):
    """Enumeration of schedule statuses."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class NotificationType(str, Enum):
    """Enumeration of notification types."""
    PURGE_WARNING = "purge_warning"
    PURGE_REMINDER = "purge_reminder"
    PURGE_FINAL_NOTICE = "purge_final_notice"
    PURGE_COMPLETED = "purge_completed"


class DeliveryStatus(str, Enum):
    """Enumeration of notification delivery statuses."""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    CANCELLED = "cancelled"


# Soft Deletion Policy Schemas
class SoftDeletionPolicyBase(BaseModel):
    """Base schema for soft deletion policies."""
    entity_type: str = Field(..., description="Type of entity this policy applies to")
    retention_period_days: int = Field(..., gt=0, description="Days to retain before purging")
    auto_purge_enabled: bool = Field(True, description="Whether to automatically purge expired records")
    cascade_deletion: bool = Field(True, description="Whether to cascade deletions to related entities")
    notification_enabled: bool = Field(True, description="Whether to send notifications before purging")
    notification_days_before: int = Field(7, ge=0, description="Days before purge to send notifications")
    description: Optional[str] = Field(None, description="Optional description of the policy")


class SoftDeletionPolicyCreate(SoftDeletionPolicyBase):
    """Schema for creating a soft deletion policy."""
    pass


class SoftDeletionPolicyUpdate(BaseModel):
    """Schema for updating a soft deletion policy."""
    retention_period_days: Optional[int] = Field(None, gt=0)
    auto_purge_enabled: Optional[bool] = None
    cascade_deletion: Optional[bool] = None
    notification_enabled: Optional[bool] = None
    notification_days_before: Optional[int] = Field(None, ge=0)
    description: Optional[str] = None


class SoftDeletionPolicy(SoftDeletionPolicyBase):
    """Schema for soft deletion policy responses."""
    id: int
    created_by: Optional[int]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Soft Deletion Audit Schemas
class SoftDeletionAuditBase(BaseModel):
    """Base schema for soft deletion audit records."""
    entity_type: str = Field(..., description="Type of entity affected")
    entity_id: int = Field(..., description="ID of the affected entity")
    operation_type: OperationType = Field(..., description="Type of operation performed")
    reason: Optional[str] = Field(None, description="Reason for the operation")
    cascade_triggered: bool = Field(False, description="Whether cascade operations were triggered")
    affected_entities_count: int = Field(1, gt=0, description="Number of entities affected")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional operation metadata")


class SoftDeletionAuditCreate(SoftDeletionAuditBase):
    """Schema for creating audit records."""
    performed_by: Optional[int] = Field(None, description="ID of user who performed the operation")
    policy_id: Optional[int] = Field(None, description="ID of policy used")
    retention_period_used: Optional[int] = Field(None, description="Retention period at time of operation")
    ip_address: Optional[str] = Field(None, description="IP address of the user")
    user_agent: Optional[str] = Field(None, description="User agent string")


class SoftDeletionAudit(SoftDeletionAuditBase):
    """Schema for soft deletion audit responses."""
    id: int
    performed_by: Optional[int]
    policy_id: Optional[int]
    retention_period_used: Optional[int]
    ip_address: Optional[str]
    user_agent: Optional[str]
    operation_time: datetime
    
    class Config:
        from_attributes = True


# Soft Deletion Operation Schemas
class SoftDeleteRequest(BaseModel):
    """Schema for soft deletion requests."""
    reason: Optional[str] = Field(None, description="Reason for deletion")
    cascade: bool = Field(True, description="Whether to cascade delete related entities")


class RestoreRequest(BaseModel):
    """Schema for restoration requests."""
    reason: Optional[str] = Field(None, description="Reason for restoration")


class PermanentDeleteRequest(BaseModel):
    """Schema for permanent deletion requests."""
    reason: Optional[str] = Field(None, description="Reason for permanent deletion")
    confirm: bool = Field(..., description="Confirmation that permanent deletion is intended")
    
    @validator('confirm')
    def confirm_must_be_true(cls, v):
        if not v:
            raise ValueError('Permanent deletion must be explicitly confirmed')
        return v


# Schedule Schemas
class SoftDeletionScheduleBase(BaseModel):
    """Base schema for soft deletion schedules."""
    entity_type: str = Field(..., description="Type of entity")
    entity_id: int = Field(..., description="ID of the entity")
    operation_type: ScheduleOperationType = Field(..., description="Type of scheduled operation")
    scheduled_for: datetime = Field(..., description="When the operation is scheduled")
    notes: Optional[str] = Field(None, description="Optional notes about the schedule")


class SoftDeletionScheduleCreate(SoftDeletionScheduleBase):
    """Schema for creating schedules."""
    policy_id: int = Field(..., description="ID of the policy governing this schedule")
    max_retries: int = Field(3, ge=0, description="Maximum number of retry attempts")


class SoftDeletionSchedule(SoftDeletionScheduleBase):
    """Schema for schedule responses."""
    id: int
    policy_id: int
    status: ScheduleStatus
    executed_at: Optional[datetime]
    execution_result: Optional[str]
    retry_count: int
    max_retries: int
    created_by: Optional[int]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Notification Schemas
class SoftDeletionNotificationBase(BaseModel):
    """Base schema for soft deletion notifications."""
    entity_type: str = Field(..., description="Type of entity")
    entity_id: int = Field(..., description="ID of the entity")
    notification_type: NotificationType = Field(..., description="Type of notification")
    scheduled_purge_date: datetime = Field(..., description="When the entity will be purged")
    days_until_purge: int = Field(..., ge=0, description="Days until purge")
    subject: Optional[str] = Field(None, description="Notification subject")
    message: Optional[str] = Field(None, description="Notification message")


class SoftDeletionNotificationCreate(SoftDeletionNotificationBase):
    """Schema for creating notifications."""
    recipient_id: int = Field(..., description="ID of the notification recipient")
    delivery_method: Optional[str] = Field("email", description="Method of delivery")


class SoftDeletionNotification(SoftDeletionNotificationBase):
    """Schema for notification responses."""
    id: int
    recipient_id: int
    sent_at: Optional[datetime]
    delivery_status: DeliveryStatus
    delivery_method: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Response Schemas
class SoftDeletionOperationResponse(BaseModel):
    """Schema for soft deletion operation responses."""
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Human-readable message about the operation")
    audit_id: int = Field(..., description="ID of the audit record created")
    affected_entities: int = Field(..., description="Number of entities affected")
    scheduled_operations: List[int] = Field(default_factory=list, description="IDs of scheduled operations created")


class SoftDeletionStatsResponse(BaseModel):
    """Schema for soft deletion statistics."""
    entity_type: str = Field(..., description="Type of entity")
    total_entities: int = Field(..., description="Total number of entities")
    active_entities: int = Field(..., description="Number of active entities")
    soft_deleted_entities: int = Field(..., description="Number of soft-deleted entities")
    scheduled_for_purge: int = Field(..., description="Number of entities scheduled for purge")
    retention_period_days: Optional[int] = Field(None, description="Current retention period")


class BulkOperationResponse(BaseModel):
    """Schema for bulk operation responses."""
    total_requested: int = Field(..., description="Total number of entities requested for operation")
    successful: int = Field(..., description="Number of successful operations")
    failed: int = Field(..., description="Number of failed operations")
    audit_ids: List[int] = Field(..., description="IDs of audit records created")
    errors: List[str] = Field(default_factory=list, description="Error messages for failed operations")
