"""
Heat map schemas for MITRE ATT&CK visualization and analysis.

This module provides Pydantic schemas for heat map data structures,
following PEP 484 (type hints) and PEP 257 (docstring conventions).
"""

from datetime import datetime
from typing import Dict, List, Optional, Union
from pydantic import BaseModel, Field, validator
from enum import Enum


class HeatMapType(str, Enum):
    """Enumeration of heat map types."""
    
    CAMPAIGN = "campaign"
    ASSESSMENT = "assessment"
    ORGANIZATION = "organization"
    COMPARISON = "comparison"


class TechniqueStatus(str, Enum):
    """Enumeration of technique coverage status."""
    
    COVERED = "covered"
    PARTIAL = "partial"
    NOT_COVERED = "not_covered"
    NOT_APPLICABLE = "not_applicable"


class AttackTechniqueResultCreate(BaseModel):
    """Schema for creating attack technique results."""
    
    technique_id: str = Field(
        ...,
        description="MITRE ATT&CK technique ID (e.g., T1566)",
        regex=r"^T\d{4}(\.\d{3})?$"
    )
    campaign_id: int = Field(..., description="Campaign ID", gt=0)
    assessment_id: Optional[int] = Field(
        None,
        description="Assessment ID",
        gt=0
    )
    effectiveness_score: float = Field(
        0.0,
        description="Overall effectiveness score (0-1)",
        ge=0.0,
        le=1.0
    )
    detection_rate: float = Field(
        0.0,
        description="Rate of successful detection (0-1)",
        ge=0.0,
        le=1.0
    )
    prevention_rate: float = Field(
        0.0,
        description="Rate of successful prevention (0-1)",
        ge=0.0,
        le=1.0
    )
    execution_count: int = Field(
        0,
        description="Number of times executed",
        ge=0
    )
    success_count: int = Field(
        0,
        description="Number of successful executions",
        ge=0
    )
    detection_count: int = Field(
        0,
        description="Number of times detected",
        ge=0
    )
    prevention_count: int = Field(
        0,
        description="Number of times prevented",
        ge=0
    )
    test_environment: Optional[str] = Field(
        None,
        description="Environment where tested",
        max_length=100
    )
    notes: Optional[str] = Field(
        None,
        description="Additional notes",
        max_length=1000
    )

    @validator('success_count')
    def success_count_must_not_exceed_execution_count(
        cls, v: int, values: Dict[str, Union[int, float]]
    ) -> int:
        """Validate that success count doesn't exceed execution count."""
        if 'execution_count' in values and v > values['execution_count']:
            raise ValueError(
                'success_count cannot exceed execution_count'
            )
        return v

    @validator('detection_count')
    def detection_count_must_not_exceed_execution_count(
        cls, v: int, values: Dict[str, Union[int, float]]
    ) -> int:
        """Validate that detection count doesn't exceed execution count."""
        if 'execution_count' in values and v > values['execution_count']:
            raise ValueError(
                'detection_count cannot exceed execution_count'
            )
        return v

    @validator('prevention_count')
    def prevention_count_must_not_exceed_execution_count(
        cls, v: int, values: Dict[str, Union[int, float]]
    ) -> int:
        """Validate that prevention count doesn't exceed execution count."""
        if 'execution_count' in values and v > values['execution_count']:
            raise ValueError(
                'prevention_count cannot exceed execution_count'
            )
        return v


class AttackTechniqueResultUpdate(BaseModel):
    """Schema for updating attack technique results."""
    
    effectiveness_score: Optional[float] = Field(
        None,
        description="Overall effectiveness score (0-1)",
        ge=0.0,
        le=1.0
    )
    detection_rate: Optional[float] = Field(
        None,
        description="Rate of successful detection (0-1)",
        ge=0.0,
        le=1.0
    )
    prevention_rate: Optional[float] = Field(
        None,
        description="Rate of successful prevention (0-1)",
        ge=0.0,
        le=1.0
    )
    execution_count: Optional[int] = Field(
        None,
        description="Number of times executed",
        ge=0
    )
    success_count: Optional[int] = Field(
        None,
        description="Number of successful executions",
        ge=0
    )
    detection_count: Optional[int] = Field(
        None,
        description="Number of times detected",
        ge=0
    )
    prevention_count: Optional[int] = Field(
        None,
        description="Number of times prevented",
        ge=0
    )
    test_environment: Optional[str] = Field(
        None,
        description="Environment where tested",
        max_length=100
    )
    notes: Optional[str] = Field(
        None,
        description="Additional notes",
        max_length=1000
    )


class AttackTechniqueResultResponse(BaseModel):
    """Schema for attack technique result responses."""
    
    id: int = Field(..., description="Unique identifier")
    technique_id: str = Field(..., description="MITRE ATT&CK technique ID")
    campaign_id: int = Field(..., description="Campaign ID")
    assessment_id: Optional[int] = Field(None, description="Assessment ID")
    effectiveness_score: float = Field(..., description="Effectiveness score")
    detection_rate: float = Field(..., description="Detection rate")
    prevention_rate: float = Field(..., description="Prevention rate")
    execution_count: int = Field(..., description="Execution count")
    success_count: int = Field(..., description="Success count")
    detection_count: int = Field(..., description="Detection count")
    prevention_count: int = Field(..., description="Prevention count")
    last_tested: Optional[datetime] = Field(
        None,
        description="Last test timestamp"
    )
    test_environment: Optional[str] = Field(
        None,
        description="Test environment"
    )
    notes: Optional[str] = Field(None, description="Additional notes")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        """Pydantic configuration."""
        
        from_attributes = True


class TechniqueHeatMapData(BaseModel):
    """Schema for individual technique heat map data."""
    
    technique_id: str = Field(..., description="MITRE ATT&CK technique ID")
    technique_name: str = Field(..., description="Technique name")
    tactic_id: str = Field(..., description="MITRE ATT&CK tactic ID")
    tactic_name: str = Field(..., description="Tactic name")
    status: TechniqueStatus = Field(..., description="Coverage status")
    effectiveness_score: float = Field(
        ...,
        description="Effectiveness score (0-1)",
        ge=0.0,
        le=1.0
    )
    detection_rate: float = Field(
        ...,
        description="Detection rate (0-1)",
        ge=0.0,
        le=1.0
    )
    prevention_rate: float = Field(
        ...,
        description="Prevention rate (0-1)",
        ge=0.0,
        le=1.0
    )
    execution_count: int = Field(
        ...,
        description="Total executions",
        ge=0
    )
    last_tested: Optional[datetime] = Field(
        None,
        description="Last test timestamp"
    )
    test_cases_count: int = Field(
        ...,
        description="Number of test cases",
        ge=0
    )


class TacticHeatMapData(BaseModel):
    """Schema for tactic-level heat map data."""
    
    tactic_id: str = Field(..., description="MITRE ATT&CK tactic ID")
    tactic_name: str = Field(..., description="Tactic name")
    techniques_count: int = Field(
        ...,
        description="Total techniques in tactic",
        ge=0
    )
    covered_count: int = Field(
        ...,
        description="Covered techniques count",
        ge=0
    )
    partial_count: int = Field(
        ...,
        description="Partially covered techniques count",
        ge=0
    )
    not_covered_count: int = Field(
        ...,
        description="Not covered techniques count",
        ge=0
    )
    average_effectiveness: float = Field(
        ...,
        description="Average effectiveness score",
        ge=0.0,
        le=1.0
    )
    coverage_percentage: float = Field(
        ...,
        description="Coverage percentage",
        ge=0.0,
        le=100.0
    )


class HeatMapMetrics(BaseModel):
    """Schema for overall heat map metrics."""
    
    total_techniques: int = Field(
        ...,
        description="Total techniques evaluated",
        ge=0
    )
    covered_techniques: int = Field(
        ...,
        description="Covered techniques count",
        ge=0
    )
    partial_techniques: int = Field(
        ...,
        description="Partially covered techniques count",
        ge=0
    )
    not_covered_techniques: int = Field(
        ...,
        description="Not covered techniques count",
        ge=0
    )
    overall_coverage_percentage: float = Field(
        ...,
        description="Overall coverage percentage",
        ge=0.0,
        le=100.0
    )
    average_effectiveness: float = Field(
        ...,
        description="Average effectiveness score",
        ge=0.0,
        le=1.0
    )
    average_detection_rate: float = Field(
        ...,
        description="Average detection rate",
        ge=0.0,
        le=1.0
    )
    average_prevention_rate: float = Field(
        ...,
        description="Average prevention rate",
        ge=0.0,
        le=1.0
    )
    total_executions: int = Field(
        ...,
        description="Total test executions",
        ge=0
    )
    last_updated: Optional[datetime] = Field(
        None,
        description="Last update timestamp"
    )


class HeatMapResponse(BaseModel):
    """Schema for complete heat map response."""

    id: Optional[int] = Field(None, description="Heat map snapshot ID")
    name: str = Field(..., description="Heat map name")
    description: Optional[str] = Field(None, description="Heat map description")
    heat_map_type: HeatMapType = Field(..., description="Heat map type")
    campaign_id: Optional[int] = Field(None, description="Campaign ID")
    assessment_id: Optional[int] = Field(None, description="Assessment ID")
    techniques: List[TechniqueHeatMapData] = Field(
        ...,
        description="Technique-level heat map data"
    )
    tactics: List[TacticHeatMapData] = Field(
        ...,
        description="Tactic-level heat map data"
    )
    metrics: HeatMapMetrics = Field(
        ...,
        description="Overall heat map metrics"
    )
    generated_at: datetime = Field(
        ...,
        description="Heat map generation timestamp"
    )
    baseline_comparison: Optional[Dict[str, float]] = Field(
        None,
        description="Comparison with baseline metrics"
    )

    class Config:
        """Pydantic configuration."""

        from_attributes = True


class HeatMapSnapshotCreate(BaseModel):
    """Schema for creating heat map snapshots."""

    campaign_id: Optional[int] = Field(None, description="Campaign ID", gt=0)
    assessment_id: Optional[int] = Field(
        None,
        description="Assessment ID",
        gt=0
    )
    name: str = Field(
        ...,
        description="Snapshot name",
        min_length=1,
        max_length=255
    )
    description: Optional[str] = Field(
        None,
        description="Snapshot description",
        max_length=1000
    )
    snapshot_type: HeatMapType = Field(
        HeatMapType.CAMPAIGN,
        description="Snapshot type"
    )
    baseline_snapshot_id: Optional[int] = Field(
        None,
        description="Baseline snapshot ID for comparison",
        gt=0
    )

    @validator('campaign_id')
    def validate_campaign_or_assessment_id(
        cls, v: Optional[int], values: Dict[str, Union[int, str]]
    ) -> Optional[int]:
        """Validate that either campaign_id or assessment_id is provided."""
        if not v and not values.get('assessment_id'):
            raise ValueError(
                'Either campaign_id or assessment_id must be provided'
            )
        return v


class HeatMapSnapshotResponse(BaseModel):
    """Schema for heat map snapshot responses."""

    id: int = Field(..., description="Snapshot ID")
    campaign_id: Optional[int] = Field(None, description="Campaign ID")
    assessment_id: Optional[int] = Field(None, description="Assessment ID")
    name: str = Field(..., description="Snapshot name")
    description: Optional[str] = Field(None, description="Snapshot description")
    snapshot_type: HeatMapType = Field(..., description="Snapshot type")
    snapshot_date: datetime = Field(..., description="Snapshot creation date")
    technique_coverage: Dict[str, float] = Field(
        ...,
        description="Technique coverage data"
    )
    tactic_effectiveness: Dict[str, float] = Field(
        ...,
        description="Tactic effectiveness data"
    )
    overall_metrics: Dict[str, Union[int, float]] = Field(
        ...,
        description="Overall metrics"
    )
    baseline_snapshot_id: Optional[int] = Field(
        None,
        description="Baseline snapshot ID"
    )
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        """Pydantic configuration."""

        from_attributes = True


class HeatMapComparisonRequest(BaseModel):
    """Schema for heat map comparison requests."""

    baseline_snapshot_id: int = Field(
        ...,
        description="Baseline snapshot ID",
        gt=0
    )
    comparison_snapshot_id: int = Field(
        ...,
        description="Comparison snapshot ID",
        gt=0
    )

    @validator('comparison_snapshot_id')
    def comparison_must_differ_from_baseline(
        cls, v: int, values: Dict[str, int]
    ) -> int:
        """Validate that comparison snapshot differs from baseline."""
        if 'baseline_snapshot_id' in values and v == values['baseline_snapshot_id']:
            raise ValueError(
                'comparison_snapshot_id must differ from baseline_snapshot_id'
            )
        return v


class HeatMapComparisonResponse(BaseModel):
    """Schema for heat map comparison responses."""

    baseline_snapshot: HeatMapSnapshotResponse = Field(
        ...,
        description="Baseline snapshot data"
    )
    comparison_snapshot: HeatMapSnapshotResponse = Field(
        ...,
        description="Comparison snapshot data"
    )
    technique_differences: Dict[str, Dict[str, float]] = Field(
        ...,
        description="Technique-level differences"
    )
    tactic_differences: Dict[str, Dict[str, float]] = Field(
        ...,
        description="Tactic-level differences"
    )
    overall_improvement: Dict[str, float] = Field(
        ...,
        description="Overall improvement metrics"
    )
    generated_at: datetime = Field(
        ...,
        description="Comparison generation timestamp"
    )
