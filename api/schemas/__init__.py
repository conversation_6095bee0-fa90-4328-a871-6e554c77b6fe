"""Initialize the schemas package."""
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel

class ErrorResponse(BaseModel):
    """Error response schema."""
    detail: str
    timestamp: datetime

class SuccessResponse(BaseModel):
    """Success response schema."""
    message: str
    timestamp: datetime

class PaginatedResponse(BaseModel):
    """Base paginated response schema."""
    total: int
    page: int
    size: int
    pages: int

from .session import DeviceInfoResponse, SessionResponse, SessionList, SessionCreate

__all__ = [
    "ErrorResponse",
    "SuccessResponse", 
    "PaginatedResponse",
    "DeviceInfoResponse",
    "SessionResponse",
    "SessionList",
    "SessionCreate"
]