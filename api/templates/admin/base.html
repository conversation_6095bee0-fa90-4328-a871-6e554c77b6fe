<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}RegressionRigor Admin{% endblock %}</title>
    <!-- Material Design Bootstrap -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.0/mdb.dark.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --dark-bg: #1a1d20;
            --darker-bg: #141617;
            --card-bg: #242628;
            --border-color: #2d3136;
            --text-primary: #e9ecef;
            --text-secondary: #adb5bd;
            --accent-color: #0d6efd;
            --hover-bg: #2a2d30;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--dark-bg);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .navbar {
            background-color: var(--darker-bg);
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
            padding: 1rem;
        }

        .sidebar {
            background-color: var(--darker-bg);
            box-shadow: 1px 0 3px rgba(0,0,0,.1);
            padding: 1.5rem;
        }

        .content {
            padding: 2rem;
        }

        .card {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,.05);
            margin-bottom: 1.5rem;
            transition: transform 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .table {
            color: var(--text-primary);
            border-color: var(--border-color);
        }

        .table th {
            border-bottom: 2px solid var(--border-color);
            padding: 1rem;
            font-weight: 500;
        }

        .table td {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .table tbody tr:hover {
            background-color: var(--hover-bg);
        }

        .form-control {
            background-color: var(--darker-bg);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 0.75rem;
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .form-control:focus {
            background-color: var(--card-bg);
            border-color: var(--accent-color);
            box-shadow: 0 0 0 2px rgba(13,110,253,.25);
            color: var(--text-primary);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: var(--accent-color);
            border: none;
        }

        .btn-primary:hover {
            background-color: #0b5ed7;
            transform: translateY(-1px);
        }

        /* Better readability for labels and headings */
        label, h1, h2, h3, h4, h5, h6 {
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        /* Pagination styling */
        .pagination .page-link {
            background-color: var(--card-bg);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        .pagination .page-link:hover {
            background-color: var(--hover-bg);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        /* Badge styling */
        .badge {
            padding: 0.5em 0.75em;
            font-weight: 500;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        {% block content %}{% endblock %}
    </div>
    <!-- MDB JS -->
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.0/mdb.min.js"></script>
</body>
</html>