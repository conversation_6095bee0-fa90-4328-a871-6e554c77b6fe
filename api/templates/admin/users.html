{% extends "admin/base.html" %}

{% block title %}User Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar (same as dashboard) -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.dashboard') }}">
                            <i class="fas fa-home"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('admin.users') }}">
                            <i class="fas fa-users"></i> Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.user_stats') }}">
                            <i class="fas fa-chart-bar"></i> Statistics
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">User Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-mdb-toggle="modal" data-mdb-target="#addUserModal">
                        <i class="fas fa-user-plus"></i> Add User
                    </button>
                </div>
            </div>

            <!-- Search and filters -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" id="searchUser" placeholder="Search users...">
                        <button class="btn btn-outline-primary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-6">
                    <select class="form-select" id="roleFilter">
                        <option value="">All Roles</option>
                        <option value="admin">Admin</option>
                        <option value="analyst">Analyst</option>
                        <option value="user">User</option>
                    </select>
                </div>
            </div>

            <!-- Users Table -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table align-middle mb-0">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Last Login</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="ms-3">
                                                <p class="fw-bold mb-1">{{ user.username }}</p>
                                                <p class="text-muted mb-0">{{ user.full_name }}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ user.email }}</td>
                                    <td>
                                        <span class="badge badge-primary rounded-pill">{{ user.role }}</span>
                                    </td>
                                    <td>
                                        {% if user.is_active %}
                                        <span class="badge badge-success rounded-pill">Active</span>
                                        {% else %}
                                        <span class="badge badge-danger rounded-pill">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ user.last_login|default('Never', true) }}</td>
                                    <td>
                                        <button type="button" class="btn btn-link btn-sm btn-rounded"
                                            onclick="editUser('{{ user.id }}')">
                                            Edit
                                        </button>
                                        {% if user.is_active %}
                                        <button type="button" class="btn btn-link btn-sm btn-rounded text-danger"
                                            onclick="deactivateUser('{{ user.id }}')">
                                            Deactivate
                                        </button>
                                        {% else %}
                                        <button type="button" class="btn btn-link btn-sm btn-rounded text-success"
                                            onclick="activateUser('{{ user.id }}')">
                                            Activate
                                        </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <li class="page-item {% if not prev_page %}disabled{% endif %}">
                        <a class="page-link" href="{{ url_for('admin.users', page=prev_page) if prev_page }}">Previous</a>
                    </li>
                    {% for page in range(1, total_pages + 1) %}
                    <li class="page-item {% if page == current_page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for('admin.users', page=page) }}">{{ page }}</a>
                    </li>
                    {% endfor %}
                    <li class="page-item {% if not next_page %}disabled{% endif %}">
                        <a class="page-link" href="{{ url_for('admin.users', page=next_page) if next_page }}">Next</a>
                    </li>
                </ul>
            </nav>
        </main>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">Add New User</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addUserForm" method="POST" action="{{ url_for('admin.add_user') }}">
                <div class="modal-body">
                    <div class="form-outline mb-4">
                        <input type="text" id="username" name="username" class="form-control" required />
                        <label class="form-label" for="username">Username</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input type="email" id="email" name="email" class="form-control" required />
                        <label class="form-label" for="email">Email</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input type="password" id="password" name="password" class="form-control" required />
                        <label class="form-label" for="password">Password</label>
                    </div>
                    <div class="form-outline mb-4">
                        <select class="form-select" id="role" name="role" required>
                            <option value="user">User</option>
                            <option value="analyst">Analyst</option>
                            <option value="admin">Admin</option>
                        </select>
                        <label class="form-label" for="role">Role</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-mdb-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Add User</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
