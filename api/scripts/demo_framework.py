#!/usr/bin/env python3
"""
Demo script for the Unified Logging and Testing Framework

This script demonstrates the core features of the logging and testing framework:
1. Logging at different levels
2. Performance metrics collection
3. Test scenario creation and execution
4. Correlation ID tracking
5. Exception handling and logging
"""

import sys
import os
import time
import random
import json
import datetime
import argparse
import traceback
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add parent directory to path so we can import from api package
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

try:
    from api.models.logging import LogEntry, PerformanceMetric, TestScenario, TestResult
    from api.models.user import User
    from api.services.logger import Logger
except ImportError as e:
    print(f"ERROR: Failed to import required modules: {e}")
    print("Make sure you're running this script from the project root directory.")
    sys.exit(1)

# Setup database connection
def get_db_session():
    """Create and return a database session"""
    try:
        DATABASE_URL = os.environ.get('DATABASE_URL', 'postgresql://postgres:postgres@localhost:5432/regression')
        engine = create_engine(DATABASE_URL)
        Session = sessionmaker(bind=engine)
        return Session()
    except Exception as e:
        print(f"ERROR: Failed to connect to database: {e}")
        sys.exit(1)

def demo_logging(logger):
    """Demonstrate different logging levels"""
    print("\n==== Demonstrating Logging Capabilities ====")
    
    logger.debug("This is a debug message", "demo_script", {"detail": "Extra debug info"})
    logger.info("User accessed dashboard", "demo_script", {"user_id": 123, "page": "dashboard"})
    logger.warning("API rate limit approaching", "demo_script", {"current_usage": "85%", "limit": "100 requests/min"})
    logger.error("Failed to process payment", "demo_script", {"payment_id": "p_12345", "amount": 99.99})
    
    try:
        # Simulate an error
        result = 100 / 0
    except Exception as e:
        logger.exception(e, "demo_script", {"operation": "division"})
    
    print("✅ Created log entries at different severity levels")
    print("✅ Demonstrated exception logging with stack trace")

def demo_performance_metrics(logger):
    """Demonstrate performance metrics collection"""
    print("\n==== Demonstrating Performance Metrics ====")
    
    # Using performance metric directly
    start_time = time.time()
    # Simulate work
    time.sleep(random.uniform(0.1, 0.3))
    duration = time.time() - start_time
    logger.record_metric("manual_operation", duration, "demo_script", "seconds", {"operation": "data_processing"})
    print(f"✅ Recorded manual performance metric: {duration:.4f} seconds")
    
    # Using time tracking decorator
    @logger.track_time("decorated_function", "demo_script")
    def slow_operation():
        # Simulate work
        time.sleep(random.uniform(0.2, 0.5))
        return {"result": "success"}
    
    result = slow_operation()
    print("✅ Used time tracking decorator for function execution")
    
    # Using context manager for time tracking
    with logger.time_context("context_operation", "demo_script", {"type": "database_query"}):
        # Simulate database work
        time.sleep(random.uniform(0.1, 0.4))
    
    print("✅ Used time tracking context manager for code block execution")

def demo_test_scenarios(db_session, logger):
    """Demonstrate test scenario creation and execution"""
    print("\n==== Demonstrating Test Scenarios ====")
    
    # Create a test user if needed
    user = db_session.query(User).filter(User.username == 'test_user').first()
    if not user:
        user = User(username='test_user', email='<EMAIL>')
        db_session.add(user)
        db_session.commit()
    
    # Create an API test scenario
    api_test = TestScenario(
        name="API Authentication Test",
        description="Tests the API authentication endpoints",
        creator_id=user.id,
        scenario_data={
            "type": "api_test",
            "requests": [
                {
                    "name": "Login Request",
                    "method": "POST",
                    "url": "/api/auth/login",
                    "headers": {"Content-Type": "application/json"},
                    "body": {"username": "test_user", "password": "password123"},
                    "expected_status": 200,
                    "assertions": ["response.body.token exists", "response.time < 500"]
                },
                {
                    "name": "Get User Profile",
                    "method": "GET",
                    "url": "/api/users/me",
                    "headers": {"Authorization": "Bearer {{login_response.token}}"},
                    "expected_status": 200,
                    "assertions": ["response.body.username == 'test_user'"]
                }
            ]
        }
    )
    
    db_session.add(api_test)
    
    # Create a UI test scenario
    ui_test = TestScenario(
        name="UI Login Test",
        description="Tests the login functionality through the UI",
        creator_id=user.id,
        scenario_data={
            "type": "ui_test",
            "steps": [
                {
                    "type": "navigate",
                    "target": "/login",
                    "description": "Navigate to login page"
                },
                {
                    "type": "input",
                    "target": "#username",
                    "value": "test_user",
                    "description": "Enter username"
                },
                {
                    "type": "input",
                    "target": "#password",
                    "value": "password123",
                    "description": "Enter password"
                },
                {
                    "type": "click",
                    "target": "#login-button",
                    "description": "Click login button"
                },
                {
                    "type": "wait",
                    "duration": 1000,
                    "description": "Wait for redirect"
                },
                {
                    "type": "assert",
                    "assertion": "contains(#welcome-message, 'Welcome')",
                    "description": "Verify login success"
                }
            ]
        }
    )
    
    db_session.add(ui_test)
    db_session.commit()
    
    # Simulate test execution
    test_result = TestResult(
        test_scenario_id=api_test.id,
        status="completed",
        duration=1.23,
        result_data={
            "success": True,
            "steps_passed": 2,
            "steps_failed": 0,
            "total_steps": 2,
            "steps": [
                {
                    "name": "Login Request",
                    "status": "passed",
                    "duration": 0.45,
                    "assertions_passed": 2
                },
                {
                    "name": "Get User Profile",
                    "status": "passed",
                    "duration": 0.32,
                    "assertions_passed": 1
                }
            ]
        }
    )
    
    db_session.add(test_result)
    db_session.commit()
    
    print(f"✅ Created API test scenario: '{api_test.name}'")
    print(f"✅ Created UI test scenario: '{ui_test.name}'")
    print(f"✅ Simulated test execution and recorded results")

def demo_context_propagation(logger):
    """Demonstrate correlation ID propagation and context"""
    print("\n==== Demonstrating Context Propagation ====")
    
    # Set a correlation ID to track a request across components
    correlation_id = f"demo-{int(time.time())}"
    logger.correlation_id = correlation_id
    
    # Set a user ID for the current context
    logger.user_id = 123
    
    logger.info("User initiated checkout", "frontend", {"cart_id": "c_789", "items": 3})
    
    def backend_service():
        # The correlation ID is automatically carried over
        logger.info("Processing payment", "payment_service", {"amount": 99.99, "method": "credit_card"})
        
        # Simulate an API call to external service
        third_party_service()
    
    def third_party_service():
        # The correlation ID continues to be propagated
        logger.info("Verifying payment with gateway", "payment_gateway", {"transaction_id": "t_12345"})
        
        # Simulate a slow response
        time.sleep(0.3)
        
        # Log a success response
        logger.info("Payment authorized", "payment_gateway", {"auth_code": "AUTH123"})
    
    backend_service()
    
    # Complete the transaction
    logger.info("Checkout completed successfully", "frontend", {"order_id": "o_456", "confirmation_sent": True})
    
    print(f"✅ All logs share correlation ID: {correlation_id}")
    print("✅ Demonstrated context propagation across services")

def show_ui_examples():
    """Show how to access UI examples"""
    print("\n==== UI Logging & Testing Examples ====")
    print("The UI components have been created with example implementations of:")
    print("1. UI Logger utility (ui/src/utils/logger.js)")
    print("2. UI Test Runner utility (ui/src/utils/testRunner.js)")
    print("3. Example components (ui/src/examples/LoggingExamples.js)")
    print("\nTo access the UI examples in your browser, navigate to:")
    print("http://localhost:3000/examples")
    print("\nThe examples demonstrate:")
    print("✅ Logging at different levels with metadata")
    print("✅ Performance tracking of operations")
    print("✅ Handling and logging exceptions")
    print("✅ Creating and running UI test scenarios")
    print("✅ Integration with React components")

def show_usage_examples():
    """Show code examples of how to use the framework"""
    print("\n==== Framework Usage Examples ====")
    
    # Python example
    print("\n--- Python Example ---")
    python_example = """
    from api.services.logger import Logger
    
    # Create a logger instance
    logger = Logger(db_session)
    
    # Basic logging
    logger.info("User created", "user_service", {"user_id": 123})
    
    # Performance tracking
    @logger.track_time("user_lookup", "user_service")
    def find_user(user_id):
        # Database lookup...
        return user
    
    # With context tracking
    logger.correlation_id = request_id
    logger.user_id = user.id
    
    # Exception handling
    try:
        result = process_data()
    except Exception as e:
        logger.exception(e, "data_processor", {"input_file": "data.csv"})
    """
    print(python_example)
    
    # JavaScript example
    print("\n--- JavaScript Example ---")
    js_example = """
    import logger from 'utils/logger';
    
    // Basic logging
    logger.info('User logged in', 'AuthComponent', { userId: 123 });
    
    // Performance tracking
    const result = await logger.timeFunction(
      async () => { 
        const data = await fetchData();
        return processData(data);
      }, 
      'data_processing', 
      'DataComponent'
    );
    
    // Error handling
    try {
      await submitForm(data);
    } catch (error) {
      logger.exception(error, 'FormComponent', { formId: 'user-profile' });
    }
    
    // HTTP interceptors are automatically set up
    // All requests will be logged and have correlation IDs
    axios.get('/api/data').then(response => {
      // Work with data...
    });
    """
    print(js_example)
    
    # Test creation example
    print("\n--- Test Creation Example ---")
    test_example = """
    // Create a UI test scenario
    const loginTest = await testRunner.createUITestScenario(
      'Login Test',
      'Tests the user login functionality',
      [
        testRunner.buildNavigateStep('/login'),
        testRunner.buildInputStep('#username', 'testuser'),
        testRunner.buildInputStep('#password', 'password123'),
        testRunner.buildClickStep('#login-button'),
        testRunner.buildWaitStep(1000),
        testRunner.buildAssertStep('contains(#welcome-message, "Welcome")')
      ]
    );
    
    // Run the test
    await testRunner.runTest(loginTest.id, (status) => {
      console.log(`Test status: ${status.status}`);
    });
    """
    print(test_example)

def main():
    parser = argparse.ArgumentParser(description='Demo for Unified Logging and Testing Framework')
    parser.add_argument('--demo', choices=['logging', 'metrics', 'tests', 'context', 'ui', 'all'], 
                        default='all', help='Select which demo to run')
    args = parser.parse_args()
    
    # Print header
    print("\n" + "=" * 80)
    print("Unified Logging and Testing Framework Demo".center(80))
    print("=" * 80 + "\n")
    
    try:
        db_session = get_db_session()
        logger = Logger(db_session)
        
        if args.demo in ['logging', 'all']:
            demo_logging(logger)
        
        if args.demo in ['metrics', 'all']:
            demo_performance_metrics(logger)
        
        if args.demo in ['tests', 'all']:
            demo_test_scenarios(db_session, logger)
        
        if args.demo in ['context', 'all']:
            demo_context_propagation(logger)
        
        if args.demo in ['ui', 'all']:
            show_ui_examples()
        
        # Always show code examples
        show_usage_examples()
        
        # Print footer
        print("\n" + "=" * 80)
        print("Demo completed successfully!".center(80))
        print("=" * 80 + "\n")
        
    except Exception as e:
        print("\n❌ ERROR: Demo failed with exception:")
        print(traceback.format_exc())
        sys.exit(1)
    finally:
        if 'db_session' in locals():
            db_session.close()

if __name__ == "__main__":
    main() 