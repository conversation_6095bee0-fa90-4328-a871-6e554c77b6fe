#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to apply database migrations for the logging and testing framework.
This script runs the Alembic migration commands to update the database schema.
"""
import os
import sys
import argparse
import subprocess
from pathlib import Path

# Get the project root directory
ROOT_DIR = Path(__file__).parent.parent

def run_command(command, cwd=None):
    """Run a shell command and print its output."""
    print(f"Running: {command}")
    process = subprocess.Popen(
        command,
        shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True,
        cwd=cwd
    )
    
    for line in process.stdout:
        print(line.strip())
    
    process.wait()
    if process.returncode != 0:
        print(f"Command failed with exit code {process.returncode}")
        sys.exit(process.returncode)
    
    return process.returncode

def main():
    parser = argparse.ArgumentParser(description='Apply database migrations')
    parser.add_argument('--revision', type=str, help='Revision to upgrade/downgrade to')
    parser.add_argument('--downgrade', action='store_true', help='Downgrade instead of upgrade')
    parser.add_argument('--auto-generate', action='store_true', help='Auto-generate migration')
    parser.add_argument('--message', type=str, help='Migration message (required with --auto-generate)')
    parser.add_argument('--show-sql', action='store_true', help='Show SQL statements')
    
    args = parser.parse_args()
    
    # Set environment variables
    os.environ["PYTHONPATH"] = str(ROOT_DIR)
    
    # Change to the migrations directory
    migrations_dir = ROOT_DIR / "migrations"
    
    # Create migrations directory if it doesn't exist
    if not migrations_dir.exists():
        print(f"Creating migrations directory at {migrations_dir}")
        migrations_dir.mkdir(parents=True)
        
        # Initialize alembic
        run_command("alembic init alembic", cwd=str(ROOT_DIR))
        
        print("Alembic initialized. Please configure alembic.ini before continuing.")
        return
    
    # Auto-generate migration if requested
    if args.auto_generate:
        if not args.message:
            print("Error: --message is required with --auto-generate")
            sys.exit(1)
        
        command = f"alembic revision --autogenerate -m '{args.message}'"
        run_command(command, cwd=str(ROOT_DIR))
        return
    
    # Run migrations
    if args.downgrade:
        command = "alembic downgrade"
        if args.revision:
            command += f" {args.revision}"
        else:
            command += " -1"  # Downgrade one revision by default
    else:
        command = "alembic upgrade"
        if args.revision:
            command += f" {args.revision}"
        else:
            command += " head"  # Upgrade to latest revision by default
    
    if args.show_sql:
        command += " --sql"
    
    run_command(command, cwd=str(ROOT_DIR))
    print("Migration completed successfully.")

if __name__ == "__main__":
    main() 