
# This file is injected to disable problematic models
import sys
import logging

logger = logging.getLogger(__name__)

# Define a list of problematic model files to disable
DISABLED_MODELS = [
    '/app/api/models/database/test_case.py',
    '/app/api/models/testcase_chaining.py',
    '/app/api/models/schemas/test_case.py',
    '/app/api/models/schemas/testcase_chaining.py'
]

# Store the original import function
original_import = __import__

# Define a new import function that filters out problematic modules
def filtered_import(name, globals=None, locals=None, fromlist=(), level=0):
    # Check if this is a module to disable
    for disabled_model in DISABLED_MODELS:
        if disabled_model.replace('/', '.').replace('.py', '') in name:
            logger.warning(f"Import of disabled module {name} prevented")
            # Return a dummy module
            import types
            return types.ModuleType(name)
    
    # Call the original import for everything else
    return original_import(name, globals, locals, fromlist, level)

# Replace the built-in import function
sys.meta_path.insert(0, filtered_import)
logger.info("Model disabling patch applied")
