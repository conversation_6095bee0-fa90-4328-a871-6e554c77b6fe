# API Implementation Details

## Version Management
The D3FEND ontology import process uses a versioning system to track different imports of the ontology. Here's how it works:

### Version Handling Strategy
1. Each import creates a version record with a date-based version string (YYYY.MM.DD)
2. If a version already exists for the current date:
   - The existing version is updated
   - All associated concepts are refreshed
   - The version is marked as current
3. Only one version can be marked as current at a time

### Transaction Management
The import process uses a robust transaction management approach:
1. Version updates are performed within a transaction
2. Concept imports are batched for performance
3. Relationship creation includes duplicate checking
4. All operations are wrapped in try-except blocks with proper rollback

## Testing Strategy
The test suite includes:
1. Transaction management using session-level controls
2. Automatic cleanup after each test
3. Version conflict testing
4. Duplicate handling verification
5. Repeated import testing to verify version updates
6. Relationship integrity validation

### Coverage Requirements
All code changes must maintain:
- Minimum 80% overall test coverage
- 100% coverage for critical paths:
  - Version management
  - Database transactions
  - Data integrity checks
- Generated coverage reports in HTML format

### Integration Test Fixtures
Test fixtures provide:
- Isolated database sessions
- Transaction management
- Automatic cleanup
- FastAPI dependency overrides

### Test Cases
1. Full Import Test
   - Verifies basic import functionality
   - Checks concept and relationship creation
   - Validates API endpoints

2. Repeated Import Test
   - Verifies version update handling
   - Tests concept replacement
   - Ensures no duplicate versions

3. Relationship Test
   - Validates relationship creation
   - Checks for relationship integrity
   - Verifies no duplicate relationships

## Usage Notes
1. Always use the provided import utilities rather than direct database operations
2. Monitor logs for import progress and any issues
3. Verify version status through the API endpoints
4. When updating existing data:
   - Use version management to handle updates
   - Verify relationship integrity
   - Check for duplicate prevention

## Running Tests with Coverage
```bash
# Run tests with coverage reporting
pytest --cov=api --cov-report=term-missing

# Generate HTML coverage report
pytest --cov=api --cov-report=html
```

Coverage reports are generated in the `htmlcov` directory.