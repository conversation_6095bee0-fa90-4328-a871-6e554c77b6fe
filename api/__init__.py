"""
RegressionRigor API
A FastAPI-based API application for tracking regression testing activities.
"""

__version__ = "0.1.1"
__author__ = "RegressionRigor Team"

# Avoid circular imports by not importing models at package level
__all__ = [
    "User",
    "UserPreference",
    "ErrorResponse", 
    "SuccessResponse",
    "PaginatedResponse"
]

# These will be populated when the models are imported
User = None
UserPreference = None

def init():
    """Initialize the API package."""
    global User, UserPreference
    from api.models import User as UserModel, UserPreference as UserPrefModel
    from api.schemas import ErrorResponse, SuccessResponse, PaginatedResponse
    
    User = UserModel
    UserPreference = UserPrefModel