"""
API endpoints for the advanced tagging system.

This module provides endpoints for managing tags, tag categories, tag hierarchies,
tag relations, and tag analytics.
"""
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, status
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func, distinct, and_, or_
from sqlalchemy.exc import IntegrityError

from api.database import get_db
from api.utils.rate_limiter import standard_rate_limit
from api.dependencies import get_current_user
from api.models.user import User
from api.models.database.tag import Tag, TagCategory, tag_hierarchy, tag_relation, tag_resource, TagPropagationRule

from models.tag import (
    TagResponse, TagCreate, TagUpdate,
    TagCategoryResponse, TagCategoryCreate, TagCategoryUpdate,
    TagHierarchyCreate, TagRelationCreate,
    TagResourceAssociation, TagAnalytics, TagBulkOperation,
    TagPropagationRuleCreate, TagPropagationRuleResponse
)

router = APIRouter(
    prefix="/v2/tags",
    tags=["advanced-tagging"],
    responses={
        404: {"description": "Not found"},
        400: {"description": "Bad request"},
        401: {"description": "Unauthorized"},
        403: {"description": "Forbidden"},
    },
)

# Tag Category Endpoints
@router.get("/categories", response_model=List[TagCategoryResponse])
async def get_tag_categories(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit),
    current_user: User = Depends(get_current_user)
):
    """
    Get all tag categories with optional search filter.
    """
    query = db.query(TagCategory)
    
    if search:
        query = query.filter(TagCategory.name.ilike(f"%{search}%"))
    
    categories = query.offset(skip).limit(limit).all()
    return categories

@router.post("/categories", response_model=TagCategoryResponse, status_code=status.HTTP_201_CREATED)
async def create_tag_category(
    category: TagCategoryCreate,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit),
    current_user: User = Depends(get_current_user)
):
    """
    Create a new tag category.
    """
    db_category = TagCategory(
        name=category.name,
        description=category.description,
        color=category.color,
        icon=category.icon,
        created_by_id=current_user.id
    )
    
    try:
        db.add(db_category)
        db.commit()
        db.refresh(db_category)
        return db_category
    except IntegrityError:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="A category with this name already exists"
        )

@router.get("/categories/{category_id}", response_model=TagCategoryResponse)
async def get_tag_category(
    category_id: int = Path(..., description="The ID of the tag category"),
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit),
    current_user: User = Depends(get_current_user)
):
    """
    Get a specific tag category by ID.
    """
    category = db.query(TagCategory).filter(TagCategory.id == category_id).first()
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tag category not found"
        )
    return category

@router.put("/categories/{category_id}", response_model=TagCategoryResponse)
async def update_tag_category(
    category_update: TagCategoryUpdate,
    category_id: int = Path(..., description="The ID of the tag category"),
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit),
    current_user: User = Depends(get_current_user)
):
    """
    Update a tag category.
    """
    category = db.query(TagCategory).filter(TagCategory.id == category_id).first()
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tag category not found"
        )
    
    # Update fields if provided
    if category_update.name is not None:
        category.name = category_update.name
    if category_update.description is not None:
        category.description = category_update.description
    if category_update.color is not None:
        category.color = category_update.color
    if category_update.icon is not None:
        category.icon = category_update.icon
    
    category.updated_by_id = current_user.id
    
    try:
        db.commit()
        db.refresh(category)
        return category
    except IntegrityError:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="A category with this name already exists"
        )

@router.delete("/categories/{category_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_tag_category(
    category_id: int = Path(..., description="The ID of the tag category"),
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit),
    current_user: User = Depends(get_current_user)
):
    """
    Delete a tag category.
    """
    category = db.query(TagCategory).filter(TagCategory.id == category_id).first()
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tag category not found"
        )
    
    db.delete(category)
    db.commit()
    return None

# Tag Endpoints
@router.get("/", response_model=List[TagResponse])
async def get_tags(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    category_id: Optional[int] = None,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit),
    current_user: User = Depends(get_current_user)
):
    """
    Get all tags with optional filters.
    """
    query = db.query(Tag)
    
    if search:
        query = query.filter(Tag.name.ilike(f"%{search}%"))
    
    if category_id:
        query = query.filter(Tag.category_id == category_id)
    
    tags = query.offset(skip).limit(limit).all()
    return tags

@router.post("/", response_model=TagResponse, status_code=status.HTTP_201_CREATED)
async def create_tag(
    tag: TagCreate,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit),
    current_user: User = Depends(get_current_user)
):
    """
    Create a new tag.
    """
    # Check if category exists if provided
    if tag.category_id:
        category = db.query(TagCategory).filter(TagCategory.id == tag.category_id).first()
        if not category:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Tag category not found"
            )
    
    db_tag = Tag(
        name=tag.name,
        description=tag.description,
        color=tag.color,
        category_id=tag.category_id,
        created_by_id=current_user.id
    )
    
    try:
        db.add(db_tag)
        db.commit()
        db.refresh(db_tag)
        return db_tag
    except IntegrityError:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="A tag with this name already exists"
        )

# Tag Hierarchy Endpoints
@router.post("/hierarchy", status_code=status.HTTP_201_CREATED)
async def create_tag_hierarchy(
    hierarchy: TagHierarchyCreate,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit),
    current_user: User = Depends(get_current_user)
):
    """
    Create a parent-child relationship between tags.
    """
    # Check if both tags exist
    parent = db.query(Tag).filter(Tag.id == hierarchy.parent_id).first()
    if not parent:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Parent tag with ID {hierarchy.parent_id} not found"
        )
    
    child = db.query(Tag).filter(Tag.id == hierarchy.child_id).first()
    if not child:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Child tag with ID {hierarchy.child_id} not found"
        )
    
    # Check if the relationship would create a cycle
    if hierarchy.parent_id == hierarchy.child_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="A tag cannot be its own parent"
        )
    
    # TODO: Add more sophisticated cycle detection for deeper hierarchies
    
    try:
        # Insert into the association table
        db.execute(
            tag_hierarchy.insert().values(
                parent_id=hierarchy.parent_id,
                child_id=hierarchy.child_id
            )
        )
        db.commit()
        return {"message": "Tag hierarchy created successfully"}
    except IntegrityError:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="This parent-child relationship already exists"
        )

# Tag Relation Endpoints
@router.post("/relations", status_code=status.HTTP_201_CREATED)
async def create_tag_relation(
    relation: TagRelationCreate,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit),
    current_user: User = Depends(get_current_user)
):
    """
    Create a relation between two tags.
    """
    # Check if both tags exist
    tag1 = db.query(Tag).filter(Tag.id == relation.tag_id).first()
    if not tag1:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Tag with ID {relation.tag_id} not found"
        )
    
    tag2 = db.query(Tag).filter(Tag.id == relation.related_tag_id).first()
    if not tag2:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Related tag with ID {relation.related_tag_id} not found"
        )
    
    try:
        # Insert into the association table
        db.execute(
            tag_relation.insert().values(
                tag_id=relation.tag_id,
                related_tag_id=relation.related_tag_id,
                relation_type=relation.relation_type
            )
        )
        db.commit()
        return {"message": "Tag relation created successfully"}
    except IntegrityError:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="This tag relation already exists"
        )

# Tag Resource Association Endpoints
@router.post("/associate", status_code=status.HTTP_201_CREATED)
async def associate_tag_with_resource(
    association: TagResourceAssociation,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit),
    current_user: User = Depends(get_current_user)
):
    """
    Associate a tag with a resource.
    """
    # Check if tag exists
    tag = db.query(Tag).filter(Tag.id == association.tag_id).first()
    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Tag with ID {association.tag_id} not found"
        )
    
    try:
        # Insert into the association table
        db.execute(
            tag_resource.insert().values(
                tag_id=association.tag_id,
                resource_type=association.resource_type,
                resource_id=association.resource_id,
                created_by=current_user.id
            )
        )
        db.commit()
        return {"message": "Tag associated with resource successfully"}
    except IntegrityError:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="This tag is already associated with this resource"
        )

# Bulk Operations
@router.post("/bulk", status_code=status.HTTP_200_OK)
async def bulk_tag_operation(
    operation: TagBulkOperation,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit),
    current_user: User = Depends(get_current_user)
):
    """
    Perform bulk operations on tags.
    """
    if operation.operation_type == "associate":
        # Associate tags with resources
        for tag_id in operation.tag_ids:
            for resource in operation.resources:
                try:
                    # Check if tag exists
                    tag = db.query(Tag).filter(Tag.id == tag_id).first()
                    if not tag:
                        continue  # Skip non-existent tags
                    
                    # Insert into the association table
                    db.execute(
                        tag_resource.insert().values(
                            tag_id=tag_id,
                            resource_type=resource.resource_type,
                            resource_id=resource.resource_id,
                            created_by=current_user.id
                        )
                    )
                except IntegrityError:
                    # Skip if association already exists
                    db.rollback()
                    continue
        
        db.commit()
        return {"message": f"Bulk tag association completed successfully"}
    
    elif operation.operation_type == "dissociate":
        # Dissociate tags from resources
        for tag_id in operation.tag_ids:
            for resource in operation.resources:
                db.execute(
                    tag_resource.delete().where(
                        and_(
                            tag_resource.c.tag_id == tag_id,
                            tag_resource.c.resource_type == resource.resource_type,
                            tag_resource.c.resource_id == resource.resource_id
                        )
                    )
                )
        
        db.commit()
        return {"message": f"Bulk tag dissociation completed successfully"}
    
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Unsupported operation type: {operation.operation_type}"
        )

# Tag Analytics
@router.get("/analytics", response_model=TagAnalytics)
async def get_tag_analytics(
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit),
    current_user: User = Depends(get_current_user)
):
    """
    Get analytics data for tags.
    """
    # Get total tag count
    total_tags = db.query(func.count(Tag.id)).scalar()
    
    # Get tags by category
    tags_by_category = db.query(
        TagCategory.name,
        func.count(Tag.id)
    ).outerjoin(
        Tag, Tag.category_id == TagCategory.id
    ).group_by(
        TagCategory.name
    ).all()
    
    # Get top used tags
    top_tags = db.query(
        Tag.id,
        Tag.name,
        func.count(tag_resource.c.tag_id).label('usage_count')
    ).outerjoin(
        tag_resource, Tag.id == tag_resource.c.tag_id
    ).group_by(
        Tag.id, Tag.name
    ).order_by(
        func.count(tag_resource.c.tag_id).desc()
    ).limit(10).all()
    
    # Get resource types with tag counts
    resource_types = db.query(
        tag_resource.c.resource_type,
        func.count(distinct(tag_resource.c.resource_id)).label('resource_count'),
        func.count(tag_resource.c.tag_id).label('tag_count')
    ).group_by(
        tag_resource.c.resource_type
    ).all()
    
    return {
        "total_tags": total_tags,
        "tags_by_category": {name: count for name, count in tags_by_category},
        "top_tags": [{"id": id, "name": name, "usage_count": count} for id, name, count in top_tags],
        "resource_types": [
            {
                "resource_type": resource_type,
                "resource_count": resource_count,
                "tag_count": tag_count
            }
            for resource_type, resource_count, tag_count in resource_types
        ]
    }

# Tag Propagation Rule Endpoints
@router.get("/propagation-rules", response_model=List[TagPropagationRuleResponse])
async def get_tag_propagation_rules(
    skip: int = 0,
    limit: int = 100,
    source_type: Optional[str] = None,
    target_type: Optional[str] = None,
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit),
    current_user: User = Depends(get_current_user)
):
    """
    Get all tag propagation rules with optional filters.
    """
    query = db.query(TagPropagationRule)
    
    if source_type:
        query = query.filter(TagPropagationRule.source_type == source_type)
    
    if target_type:
        query = query.filter(TagPropagationRule.target_type == target_type)
    
    if is_active is not None:
        query = query.filter(TagPropagationRule.is_active == is_active)
    
    rules = query.offset(skip).limit(limit).all()
    return rules

@router.post("/propagation-rules", response_model=TagPropagationRuleResponse, status_code=status.HTTP_201_CREATED)
async def create_tag_propagation_rule(
    rule: TagPropagationRuleCreate,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit),
    current_user: User = Depends(get_current_user)
):
    """
    Create a new tag propagation rule.
    """
    db_rule = TagPropagationRule(
        source_type=rule.source_type,
        target_type=rule.target_type,
        relation_field=rule.relation_field,
        is_active=rule.is_active,
        description=rule.description,
        created_by_id=current_user.id
    )
    
    try:
        db.add(db_rule)
        db.commit()
        db.refresh(db_rule)
        return db_rule
    except IntegrityError:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="A rule with this source type, target type, and relation field already exists"
        )

@router.get("/propagation-rules/{rule_id}", response_model=TagPropagationRuleResponse)
async def get_tag_propagation_rule(
    rule_id: int = Path(..., description="The ID of the tag propagation rule"),
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit),
    current_user: User = Depends(get_current_user)
):
    """
    Get a specific tag propagation rule by ID.
    """
    rule = db.query(TagPropagationRule).filter(TagPropagationRule.id == rule_id).first()
    if not rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tag propagation rule not found"
        )
    return rule

@router.put("/propagation-rules/{rule_id}", response_model=TagPropagationRuleResponse)
async def update_tag_propagation_rule(
    rule_update: TagPropagationRuleCreate,
    rule_id: int = Path(..., description="The ID of the tag propagation rule"),
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit),
    current_user: User = Depends(get_current_user)
):
    """
    Update a tag propagation rule.
    """
    rule = db.query(TagPropagationRule).filter(TagPropagationRule.id == rule_id).first()
    if not rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tag propagation rule not found"
        )
    
    # Update fields
    rule.source_type = rule_update.source_type
    rule.target_type = rule_update.target_type
    rule.relation_field = rule_update.relation_field
    rule.is_active = rule_update.is_active
    rule.description = rule_update.description
    rule.updated_by_id = current_user.id
    
    try:
        db.commit()
        db.refresh(rule)
        return rule
    except IntegrityError:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="A rule with this source type, target type, and relation field already exists"
        )

@router.delete("/propagation-rules/{rule_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_tag_propagation_rule(
    rule_id: int = Path(..., description="The ID of the tag propagation rule"),
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit),
    current_user: User = Depends(get_current_user)
):
    """
    Delete a tag propagation rule.
    """
    rule = db.query(TagPropagationRule).filter(TagPropagationRule.id == rule_id).first()
    if not rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tag propagation rule not found"
        )
    
    db.delete(rule)
    db.commit()
    return None

@router.post("/propagate", status_code=status.HTTP_200_OK)
async def propagate_tags(
    resource_type: str = Query(..., description="The type of resource to propagate tags from"),
    resource_id: int = Query(..., description="The ID of the resource to propagate tags from"),
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit),
    current_user: User = Depends(get_current_user)
):
    """
    Manually trigger tag propagation for a specific resource.
    
    This endpoint will find all applicable propagation rules for the specified resource
    and apply them to propagate tags to related resources.
    """
    # Find all active propagation rules for this resource type
    rules = db.query(TagPropagationRule).filter(
        TagPropagationRule.source_type == resource_type,
        TagPropagationRule.is_active == True
    ).all()
    
    if not rules:
        return {"message": "No active propagation rules found for this resource type"}
    
    # Get all tags associated with this resource
    tag_associations = db.execute(
        tag_resource.select().where(
            and_(
                tag_resource.c.resource_type == resource_type,
                tag_resource.c.resource_id == resource_id
            )
        )
    ).fetchall()
    
    if not tag_associations:
        return {"message": "No tags found for this resource"}
    
    # Extract tag IDs
    tag_ids = [assoc.tag_id for assoc in tag_associations]
    
    # For each rule, propagate tags to related resources
    propagation_count = 0
    for rule in rules:
        # This is a simplified implementation
        # In a real application, you would need to query the database
        # to find related resources based on the relation_field
        
        # For demonstration purposes, we'll assume we have a way to find related resources
        # In a real implementation, this would be replaced with actual queries
        related_resources = []  # This would be populated with actual related resources
        
        # For each related resource, associate the tags
        for related_resource_id in related_resources:
            for tag_id in tag_ids:
                try:
                    # Check if the association already exists
                    existing = db.execute(
                        tag_resource.select().where(
                            and_(
                                tag_resource.c.tag_id == tag_id,
                                tag_resource.c.resource_type == rule.target_type,
                                tag_resource.c.resource_id == related_resource_id
                            )
                        )
                    ).fetchone()
                    
                    if not existing:
                        # Create the association
                        db.execute(
                            tag_resource.insert().values(
                                tag_id=tag_id,
                                resource_type=rule.target_type,
                                resource_id=related_resource_id,
                                created_by=current_user.id
                            )
                        )
                        propagation_count += 1
                except IntegrityError:
                    db.rollback()
                    continue
    
    db.commit()
    return {"message": f"Successfully propagated {propagation_count} tag associations"} 