"""
API endpoints for comprehensive tagging system feature
"""
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body
from typing import List, Optional, Dict
from models.comprehensive_tagging_system import (
    TagModel, TagCreate, TagUpdate, 
    TagAssociationModel, TagAssociationCreate,
    EntityTags, TagStats
)
from api.models.database.comprehensive_tagging_system import Tag, TagAssociation
from api.database import get_db
from sqlalchemy.orm import Session
from sqlalchemy import func, distinct, and_, or_
from api.utils.rate_limiter import standard_rate_limit
from api.utils.auth import get_current_user
from api.models.user import User
from sqlalchemy.exc import IntegrityError

router = APIRouter(
    prefix="/tags",
    tags=["tags"],
    responses={404: {"description": "Tag not found"}},
)

# Tag management endpoints
@router.get("/", response_model=List[TagModel])
async def get_all_tags(
    skip: int = 0, 
    limit: int = 100,
    search: Optional[str] = None,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit)
):
    """
    Get all tags with optional search filter
    """
    query = db.query(Tag)
    
    if search:
        query = query.filter(Tag.name.ilike(f"%{search}%"))
    
    tags = query.offset(skip).limit(limit).all()
    return tags

@router.get("/stats", response_model=List[TagStats])
async def get_tag_stats(
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit)
):
    """
    Get usage statistics for all tags
    """
    # Get all tags
    tags = db.query(Tag).all()
    
    # Prepare stats
    stats = []
    for tag in tags:
        # Count associations by entity type
        entity_types = {}
        for assoc in tag.associations:
            if assoc.entity_type not in entity_types:
                entity_types[assoc.entity_type] = 0
            entity_types[assoc.entity_type] += 1
        
        stats.append({
            "id": tag.id,
            "name": tag.name,
            "color": tag.color,
            "count": len(tag.associations),
            "entity_types": entity_types
        })
    
    return stats

@router.get("/{tag_id}", response_model=TagModel)
async def get_tag(
    tag_id: int = Path(..., description="The ID of the tag to retrieve"),
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit)
):
    """
    Get a specific tag by ID
    """
    tag = db.query(Tag).filter(Tag.id == tag_id).first()
    if not tag:
        raise HTTPException(status_code=404, detail="Tag not found")
    return tag

@router.post("/", response_model=TagModel)
async def create_tag(
    tag: TagCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(standard_rate_limit)
):
    """
    Create a new tag
    """
    try:
        db_tag = Tag(
            name=tag.name,
            description=tag.description,
            color=tag.color,
            created_by_id=current_user.id
        )
        db.add(db_tag)
        db.commit()
        db.refresh(db_tag)
        return db_tag
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Tag with this name already exists")

@router.put("/{tag_id}", response_model=TagModel)
async def update_tag(
    tag_id: int,
    tag: TagUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(standard_rate_limit)
):
    """
    Update a tag
    """
    db_tag = db.query(Tag).filter(Tag.id == tag_id).first()
    if not db_tag:
        raise HTTPException(status_code=404, detail="Tag not found")
    
    try:
        # Update only provided fields
        if tag.name is not None:
            db_tag.name = tag.name
        if tag.description is not None:
            db_tag.description = tag.description
        if tag.color is not None:
            db_tag.color = tag.color
        
        db.commit()
        db.refresh(db_tag)
        return db_tag
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Tag with this name already exists")

@router.delete("/{tag_id}")
async def delete_tag(
    tag_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(standard_rate_limit)
):
    """
    Delete a tag
    """
    db_tag = db.query(Tag).filter(Tag.id == tag_id).first()
    if not db_tag:
        raise HTTPException(status_code=404, detail="Tag not found")
    
    db.delete(db_tag)
    db.commit()
    return {"message": "Tag deleted"}

# Tag association endpoints
@router.post("/associate", response_model=List[TagAssociationModel])
async def associate_tags(
    entity_tags: EntityTags,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(standard_rate_limit)
):
    """
    Associate tags with an entity
    """
    # Validate entity type
    valid_entity_types = ["test_case", "campaign", "assessment"]
    if entity_tags.entity_type not in valid_entity_types:
        raise HTTPException(status_code=400, detail=f"Invalid entity type. Must be one of: {', '.join(valid_entity_types)}")
    
    # Validate tags exist
    tags = db.query(Tag).filter(Tag.id.in_(entity_tags.tag_ids)).all()
    if len(tags) != len(entity_tags.tag_ids):
        raise HTTPException(status_code=400, detail="One or more tags not found")
    
    # Create associations
    associations = []
    for tag_id in entity_tags.tag_ids:
        # Check if association already exists
        existing = db.query(TagAssociation).filter(
            TagAssociation.tag_id == tag_id,
            TagAssociation.entity_type == entity_tags.entity_type,
            TagAssociation.entity_id == entity_tags.entity_id
        ).first()
        
        if not existing:
            assoc = TagAssociation(
                tag_id=tag_id,
                entity_type=entity_tags.entity_type,
                entity_id=entity_tags.entity_id,
                created_by_id=current_user.id
            )
            db.add(assoc)
            associations.append(assoc)
    
    db.commit()
    
    # Refresh all associations for this entity
    all_assocs = db.query(TagAssociation).filter(
        TagAssociation.entity_type == entity_tags.entity_type,
        TagAssociation.entity_id == entity_tags.entity_id
    ).all()
    
    return all_assocs

@router.delete("/dissociate", response_model=List[TagAssociationModel])
async def dissociate_tags(
    entity_tags: EntityTags,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(standard_rate_limit)
):
    """
    Remove tag associations from an entity
    """
    # Delete specified associations
    db.query(TagAssociation).filter(
        TagAssociation.tag_id.in_(entity_tags.tag_ids),
        TagAssociation.entity_type == entity_tags.entity_type,
        TagAssociation.entity_id == entity_tags.entity_id
    ).delete(synchronize_session=False)
    
    db.commit()
    
    # Return remaining associations
    remaining = db.query(TagAssociation).filter(
        TagAssociation.entity_type == entity_tags.entity_type,
        TagAssociation.entity_id == entity_tags.entity_id
    ).all()
    
    return remaining

@router.get("/entity/{entity_type}/{entity_id}", response_model=List[TagModel])
async def get_entity_tags(
    entity_type: str = Path(..., description="Type of the entity"),
    entity_id: int = Path(..., description="ID of the entity"),
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit)
):
    """
    Get all tags associated with an entity
    """
    # Validate entity type
    valid_entity_types = ["test_case", "campaign", "assessment"]
    if entity_type not in valid_entity_types:
        raise HTTPException(status_code=400, detail=f"Invalid entity type. Must be one of: {', '.join(valid_entity_types)}")
    
    # Get tag IDs associated with this entity
    associations = db.query(TagAssociation).filter(
        TagAssociation.entity_type == entity_type,
        TagAssociation.entity_id == entity_id
    ).all()
    
    tag_ids = [assoc.tag_id for assoc in associations]
    
    # Get the actual tags
    tags = db.query(Tag).filter(Tag.id.in_(tag_ids)).all()
    
    return tags
