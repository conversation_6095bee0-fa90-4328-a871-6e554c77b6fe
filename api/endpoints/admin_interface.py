"""
API endpoints for admin interface feature
"""
from fastapi import APIRouter, Depends, HTTPException, Request, Query
from typing import List, Optional, Dict, Any
from models.admin_interface import (
    AdminSettingModel, AdminSettingCreate, AdminSettingUpdate,
    AdminAuditLogModel, AdminAuditLogCreate,
    SystemConfigurationModel, SystemConfigurationCreate, SystemConfigurationUpdate,
    AdminDashboardWidgetModel, AdminDashboardWidgetCreate, AdminDashboardWidgetUpdate,
    AdminNotificationModel, AdminNotificationCreate, AdminNotificationUpdate,
    AdminDashboardModel, AdminDashboardStats
)
from api.database import get_db
from sqlalchemy.orm import Session
from api.utils.rate_limiter import strict_rate_limit
from api.auth.dependencies import get_current_active_user, get_current_admin_user
from api.models.database.admin_interface import (
    AdminSetting, AdminAuditLog, SystemConfiguration, 
    AdminDashboardWidget, AdminNotification
)
from api.models.user import User, UserRole
from api.models.database.error_handling import ErrorHandling
from sqlalchemy import func, desc
import platform
import psutil
import os
from datetime import datetime, timedelta

router = APIRouter(
    prefix="/admin",
    tags=["admin"],
    dependencies=[Depends(get_current_admin_user)],  # Only admins can access these endpoints
    responses={
        404: {"description": "Resource not found"},
        403: {"description": "Forbidden - Admin access required"}
    },
)

# Helper function to log admin actions
async def log_admin_action(
    db: Session,
    user_id: int,
    action: str,
    resource_type: str,
    resource_id: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None,
    request: Optional[Request] = None
):
    """Log an admin action to the audit log"""
    ip_address = None
    user_agent = None
    
    if request:
        ip_address = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
    
    audit_log = AdminAuditLog(
        user_id=user_id,
        action=action,
        resource_type=resource_type,
        resource_id=str(resource_id) if resource_id is not None else None,
        details=details,
        ip_address=ip_address,
        user_agent=user_agent
    )
    
    db.add(audit_log)
    db.commit()
    db.refresh(audit_log)
    return audit_log

# Dashboard endpoints
@router.get("/dashboard", response_model=AdminDashboardModel)
async def get_admin_dashboard(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
    _: None = Depends(strict_rate_limit)
):
    """
    Get the admin dashboard with statistics, widgets, notifications, and recent audit logs
    """
    # Get dashboard statistics
    total_users = db.query(func.count(User.id)).scalar()
    active_users = db.query(func.count(User.id)).filter(User.is_active == True).scalar()
    
    # Get system information
    uptime_seconds = psutil.boot_time()
    uptime = datetime.now() - datetime.fromtimestamp(uptime_seconds)
    uptime_str = f"{uptime.days} days, {uptime.seconds // 3600} hours, {(uptime.seconds // 60) % 60} minutes"
    
    # Get database size (this is a placeholder, actual implementation would depend on the database)
    database_size = "Unknown"  # In a real implementation, you would query the database for its size
    
    # Get error count in the last 24 hours
    yesterday = datetime.now() - timedelta(days=1)
    error_count = db.query(func.count(ErrorHandling.id)).filter(ErrorHandling.created_at >= yesterday).scalar()
    
    # Get API request count (placeholder)
    api_requests = 1000  # In a real implementation, you would have a way to track API requests
    
    # Create stats object
    stats = AdminDashboardStats(
        total_users=total_users,
        active_users=active_users,
        total_sessions=100,  # Placeholder
        active_sessions=50,  # Placeholder
        system_uptime=uptime_str,
        database_size=database_size,
        error_count_24h=error_count,
        api_requests_24h=api_requests
    )
    
    # Get dashboard widgets
    widgets = db.query(AdminDashboardWidget).filter(AdminDashboardWidget.is_enabled == True).order_by(AdminDashboardWidget.position).all()
    
    # Get admin notifications
    notifications = db.query(AdminNotification).filter(
        (AdminNotification.user_id == current_user.id) | (AdminNotification.user_id == None)
    ).order_by(desc(AdminNotification.created_at)).limit(10).all()
    
    # Get recent audit logs
    audit_logs = db.query(AdminAuditLog).order_by(desc(AdminAuditLog.created_at)).limit(10).all()
    
    # Create dashboard model
    dashboard = AdminDashboardModel(
        stats=stats,
        widgets=widgets,
        notifications=notifications,
        recent_audit_logs=audit_logs
    )
    
    return dashboard

# Admin Settings endpoints
@router.get("/settings", response_model=List[AdminSettingModel])
async def get_all_admin_settings(
    skip: int = 0,
    limit: int = 100,
    category: Optional[str] = None,
    db: Session = Depends(get_db),
    _: None = Depends(strict_rate_limit)
):
    """
    Get all admin settings, optionally filtered by category
    """
    query = db.query(AdminSetting)
    
    if category:
        query = query.filter(AdminSetting.category == category)
    
    settings = query.offset(skip).limit(limit).all()
    return settings

@router.get("/settings/{setting_id}", response_model=AdminSettingModel)
async def get_admin_setting(
    setting_id: int,
    db: Session = Depends(get_db),
    _: None = Depends(strict_rate_limit)
):
    """
    Get a specific admin setting by ID
    """
    setting = db.query(AdminSetting).filter(AdminSetting.id == setting_id).first()
    if setting is None:
        raise HTTPException(status_code=404, detail="Admin setting not found")
    return setting

@router.post("/settings", response_model=AdminSettingModel)
async def create_admin_setting(
    setting: AdminSettingCreate,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
    _: None = Depends(strict_rate_limit)
):
    """
    Create a new admin setting
    """
    # Check if setting with the same key already exists
    existing_setting = db.query(AdminSetting).filter(AdminSetting.key == setting.key).first()
    if existing_setting:
        raise HTTPException(status_code=400, detail=f"Setting with key '{setting.key}' already exists")
    
    # Create new setting
    db_setting = AdminSetting(**setting.dict())
    db.add(db_setting)
    db.commit()
    db.refresh(db_setting)
    
    # Log the action
    await log_admin_action(
        db=db,
        user_id=current_user.id,
        action="create",
        resource_type="admin_setting",
        resource_id=str(db_setting.id),
        details={"key": setting.key, "category": setting.category},
        request=request
    )
    
    return db_setting

@router.put("/settings/{setting_id}", response_model=AdminSettingModel)
async def update_admin_setting(
    setting_id: int,
    setting: AdminSettingUpdate,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
    _: None = Depends(strict_rate_limit)
):
    """
    Update an admin setting
    """
    db_setting = db.query(AdminSetting).filter(AdminSetting.id == setting_id).first()
    if db_setting is None:
        raise HTTPException(status_code=404, detail="Admin setting not found")
    
    # Check if setting is editable
    if not db_setting.is_editable:
        raise HTTPException(status_code=403, detail="This setting cannot be edited")
    
    # Update setting
    update_data = setting.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_setting, key, value)
    
    db.commit()
    db.refresh(db_setting)
    
    # Log the action
    await log_admin_action(
        db=db,
        user_id=current_user.id,
        action="update",
        resource_type="admin_setting",
        resource_id=str(db_setting.id),
        details={"key": db_setting.key, "updated_fields": list(update_data.keys())},
        request=request
    )
    
    return db_setting

@router.delete("/settings/{setting_id}")
async def delete_admin_setting(
    setting_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
    _: None = Depends(strict_rate_limit)
):
    """
    Delete an admin setting
    """
    db_setting = db.query(AdminSetting).filter(AdminSetting.id == setting_id).first()
    if db_setting is None:
        raise HTTPException(status_code=404, detail="Admin setting not found")
    
    # Store setting info for audit log
    setting_key = db_setting.key
    setting_category = db_setting.category
    
    # Delete setting
    db.delete(db_setting)
    db.commit()
    
    # Log the action
    await log_admin_action(
        db=db,
        user_id=current_user.id,
        action="delete",
        resource_type="admin_setting",
        resource_id=str(setting_id),
        details={"key": setting_key, "category": setting_category},
        request=request
    )
    
    return {"message": "Admin setting deleted"}

# Audit Log endpoints
@router.get("/audit-logs", response_model=List[AdminAuditLogModel])
async def get_audit_logs(
    skip: int = 0,
    limit: int = 100,
    user_id: Optional[int] = None,
    action: Optional[str] = None,
    resource_type: Optional[str] = None,
    from_date: Optional[datetime] = None,
    to_date: Optional[datetime] = None,
    db: Session = Depends(get_db),
    _: None = Depends(strict_rate_limit)
):
    """
    Get audit logs with optional filtering
    """
    query = db.query(AdminAuditLog)
    
    # Apply filters
    if user_id:
        query = query.filter(AdminAuditLog.user_id == user_id)
    if action:
        query = query.filter(AdminAuditLog.action == action)
    if resource_type:
        query = query.filter(AdminAuditLog.resource_type == resource_type)
    if from_date:
        query = query.filter(AdminAuditLog.created_at >= from_date)
    if to_date:
        query = query.filter(AdminAuditLog.created_at <= to_date)
    
    # Order by created_at descending (newest first)
    query = query.order_by(desc(AdminAuditLog.created_at))
    
    audit_logs = query.offset(skip).limit(limit).all()
    return audit_logs

@router.get("/audit-logs/{audit_log_id}", response_model=AdminAuditLogModel)
async def get_audit_log(
    audit_log_id: int,
    db: Session = Depends(get_db),
    _: None = Depends(strict_rate_limit)
):
    """
    Get a specific audit log by ID
    """
    audit_log = db.query(AdminAuditLog).filter(AdminAuditLog.id == audit_log_id).first()
    if audit_log is None:
        raise HTTPException(status_code=404, detail="Audit log not found")
    return audit_log

# System Configuration endpoints
@router.get("/system-configs", response_model=List[SystemConfigurationModel])
async def get_system_configurations(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    _: None = Depends(strict_rate_limit)
):
    """
    Get all system configurations
    """
    configs = db.query(SystemConfiguration).offset(skip).limit(limit).all()
    return configs

@router.get("/system-configs/{config_id}", response_model=SystemConfigurationModel)
async def get_system_configuration(
    config_id: int,
    db: Session = Depends(get_db),
    _: None = Depends(strict_rate_limit)
):
    """
    Get a specific system configuration by ID
    """
    config = db.query(SystemConfiguration).filter(SystemConfiguration.id == config_id).first()
    if config is None:
        raise HTTPException(status_code=404, detail="System configuration not found")
    return config

@router.post("/system-configs", response_model=SystemConfigurationModel)
async def create_system_configuration(
    config: SystemConfigurationCreate,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
    _: None = Depends(strict_rate_limit)
):
    """
    Create a new system configuration
    """
    # Check if configuration with the same name already exists
    existing_config = db.query(SystemConfiguration).filter(SystemConfiguration.name == config.name).first()
    if existing_config:
        raise HTTPException(status_code=400, detail=f"Configuration with name '{config.name}' already exists")
    
    # Create new configuration
    db_config = SystemConfiguration(**config.dict())
    db.add(db_config)
    db.commit()
    db.refresh(db_config)
    
    # Log the action
    await log_admin_action(
        db=db,
        user_id=current_user.id,
        action="create",
        resource_type="system_configuration",
        resource_id=str(db_config.id),
        details={"name": config.name, "is_encrypted": config.is_encrypted},
        request=request
    )
    
    return db_config

@router.put("/system-configs/{config_id}", response_model=SystemConfigurationModel)
async def update_system_configuration(
    config_id: int,
    config: SystemConfigurationUpdate,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
    _: None = Depends(strict_rate_limit)
):
    """
    Update a system configuration
    """
    db_config = db.query(SystemConfiguration).filter(SystemConfiguration.id == config_id).first()
    if db_config is None:
        raise HTTPException(status_code=404, detail="System configuration not found")
    
    # Update configuration
    update_data = config.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_config, key, value)
    
    db.commit()
    db.refresh(db_config)
    
    # Log the action
    await log_admin_action(
        db=db,
        user_id=current_user.id,
        action="update",
        resource_type="system_configuration",
        resource_id=str(db_config.id),
        details={"name": db_config.name, "updated_fields": list(update_data.keys())},
        request=request
    )
    
    return db_config

@router.delete("/system-configs/{config_id}")
async def delete_system_configuration(
    config_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
    _: None = Depends(strict_rate_limit)
):
    """
    Delete a system configuration
    """
    db_config = db.query(SystemConfiguration).filter(SystemConfiguration.id == config_id).first()
    if db_config is None:
        raise HTTPException(status_code=404, detail="System configuration not found")
    
    # Store config info for audit log
    config_name = db_config.name
    
    # Delete configuration
    db.delete(db_config)
    db.commit()
    
    # Log the action
    await log_admin_action(
        db=db,
        user_id=current_user.id,
        action="delete",
        resource_type="system_configuration",
        resource_id=str(config_id),
        details={"name": config_name},
        request=request
    )
    
    return {"message": "System configuration deleted"}

# Dashboard Widget endpoints
@router.get("/widgets", response_model=List[AdminDashboardWidgetModel])
async def get_dashboard_widgets(
    skip: int = 0,
    limit: int = 100,
    enabled_only: bool = False,
    db: Session = Depends(get_db),
    _: None = Depends(strict_rate_limit)
):
    """
    Get all dashboard widgets, optionally filtered by enabled status
    """
    query = db.query(AdminDashboardWidget)
    
    if enabled_only:
        query = query.filter(AdminDashboardWidget.is_enabled == True)
    
    # Order by position
    query = query.order_by(AdminDashboardWidget.position)
    
    widgets = query.offset(skip).limit(limit).all()
    return widgets

@router.get("/widgets/{widget_id}", response_model=AdminDashboardWidgetModel)
async def get_dashboard_widget(
    widget_id: int,
    db: Session = Depends(get_db),
    _: None = Depends(strict_rate_limit)
):
    """
    Get a specific dashboard widget by ID
    """
    widget = db.query(AdminDashboardWidget).filter(AdminDashboardWidget.id == widget_id).first()
    if widget is None:
        raise HTTPException(status_code=404, detail="Dashboard widget not found")
    return widget

@router.post("/widgets", response_model=AdminDashboardWidgetModel)
async def create_dashboard_widget(
    widget: AdminDashboardWidgetCreate,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
    _: None = Depends(strict_rate_limit)
):
    """
    Create a new dashboard widget
    """
    # Create new widget
    db_widget = AdminDashboardWidget(**widget.dict())
    db.add(db_widget)
    db.commit()
    db.refresh(db_widget)
    
    # Log the action
    await log_admin_action(
        db=db,
        user_id=current_user.id,
        action="create",
        resource_type="dashboard_widget",
        resource_id=str(db_widget.id),
        details={"name": widget.name, "widget_type": widget.widget_type},
        request=request
    )
    
    return db_widget

@router.put("/widgets/{widget_id}", response_model=AdminDashboardWidgetModel)
async def update_dashboard_widget(
    widget_id: int,
    widget: AdminDashboardWidgetUpdate,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
    _: None = Depends(strict_rate_limit)
):
    """
    Update a dashboard widget
    """
    db_widget = db.query(AdminDashboardWidget).filter(AdminDashboardWidget.id == widget_id).first()
    if db_widget is None:
        raise HTTPException(status_code=404, detail="Dashboard widget not found")
    
    # Update widget
    update_data = widget.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_widget, key, value)
    
    db.commit()
    db.refresh(db_widget)
    
    # Log the action
    await log_admin_action(
        db=db,
        user_id=current_user.id,
        action="update",
        resource_type="dashboard_widget",
        resource_id=str(db_widget.id),
        details={"name": db_widget.name, "updated_fields": list(update_data.keys())},
        request=request
    )
    
    return db_widget

@router.delete("/widgets/{widget_id}")
async def delete_dashboard_widget(
    widget_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
    _: None = Depends(strict_rate_limit)
):
    """
    Delete a dashboard widget
    """
    db_widget = db.query(AdminDashboardWidget).filter(AdminDashboardWidget.id == widget_id).first()
    if db_widget is None:
        raise HTTPException(status_code=404, detail="Dashboard widget not found")
    
    # Store widget info for audit log
    widget_name = db_widget.name
    
    # Delete widget
    db.delete(db_widget)
    db.commit()
    
    # Log the action
    await log_admin_action(
        db=db,
        user_id=current_user.id,
        action="delete",
        resource_type="dashboard_widget",
        resource_id=str(widget_id),
        details={"name": widget_name},
        request=request
    )
    
    return {"message": "Dashboard widget deleted"}

# Admin Notification endpoints
@router.get("/notifications", response_model=List[AdminNotificationModel])
async def get_admin_notifications(
    skip: int = 0,
    limit: int = 100,
    unread_only: bool = False,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
    _: None = Depends(strict_rate_limit)
):
    """
    Get admin notifications for the current user
    """
    query = db.query(AdminNotification).filter(
        (AdminNotification.user_id == current_user.id) | (AdminNotification.user_id == None)
    )
    
    if unread_only:
        query = query.filter(AdminNotification.is_read == False)
    
    # Order by created_at descending (newest first)
    query = query.order_by(desc(AdminNotification.created_at))
    
    notifications = query.offset(skip).limit(limit).all()
    return notifications

@router.get("/notifications/{notification_id}", response_model=AdminNotificationModel)
async def get_admin_notification(
    notification_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
    _: None = Depends(strict_rate_limit)
):
    """
    Get a specific admin notification by ID
    """
    notification = db.query(AdminNotification).filter(
        AdminNotification.id == notification_id,
        ((AdminNotification.user_id == current_user.id) | (AdminNotification.user_id == None))
    ).first()
    
    if notification is None:
        raise HTTPException(status_code=404, detail="Admin notification not found")
    
    return notification

@router.post("/notifications", response_model=AdminNotificationModel)
async def create_admin_notification(
    notification: AdminNotificationCreate,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
    _: None = Depends(strict_rate_limit)
):
    """
    Create a new admin notification
    """
    # Validate user_id if provided
    if notification.user_id:
        user = db.query(User).filter(User.id == notification.user_id).first()
        if user is None:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Check if user is an admin
        if user.role != UserRole.ADMIN:
            raise HTTPException(status_code=400, detail="Notifications can only be sent to admin users")
    
    # Create new notification
    db_notification = AdminNotification(**notification.dict(), is_read=False)
    db.add(db_notification)
    db.commit()
    db.refresh(db_notification)
    
    # Log the action
    await log_admin_action(
        db=db,
        user_id=current_user.id,
        action="create",
        resource_type="admin_notification",
        resource_id=str(db_notification.id),
        details={"title": notification.title, "severity": notification.severity},
        request=request
    )
    
    return db_notification

@router.put("/notifications/{notification_id}", response_model=AdminNotificationModel)
async def update_admin_notification(
    notification_id: int,
    notification: AdminNotificationUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
    _: None = Depends(strict_rate_limit)
):
    """
    Update an admin notification (mark as read/unread)
    """
    db_notification = db.query(AdminNotification).filter(
        AdminNotification.id == notification_id,
        ((AdminNotification.user_id == current_user.id) | (AdminNotification.user_id == None))
    ).first()
    
    if db_notification is None:
        raise HTTPException(status_code=404, detail="Admin notification not found")
    
    # Update notification
    update_data = notification.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_notification, key, value)
    
    db.commit()
    db.refresh(db_notification)
    
    return db_notification

@router.delete("/notifications/{notification_id}")
async def delete_admin_notification(
    notification_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user),
    _: None = Depends(strict_rate_limit)
):
    """
    Delete an admin notification
    """
    db_notification = db.query(AdminNotification).filter(
        AdminNotification.id == notification_id,
        ((AdminNotification.user_id == current_user.id) | (AdminNotification.user_id == None))
    ).first()
    
    if db_notification is None:
        raise HTTPException(status_code=404, detail="Admin notification not found")
    
    # Store notification info for audit log
    notification_title = db_notification.title
    
    # Delete notification
    db.delete(db_notification)
    db.commit()
    
    # Log the action
    await log_admin_action(
        db=db,
        user_id=current_user.id,
        action="delete",
        resource_type="admin_notification",
        resource_id=str(notification_id),
        details={"title": notification_title},
        request=request
    )
    
    return {"message": "Admin notification deleted"}

# System Information endpoints
@router.get("/system-info")
async def get_system_info(
    db: Session = Depends(get_db),
    _: None = Depends(strict_rate_limit)
):
    """
    Get system information
    """
    # Get system information
    system_info = {
        "platform": platform.system(),
        "platform_version": platform.version(),
        "platform_release": platform.release(),
        "architecture": platform.machine(),
        "processor": platform.processor(),
        "hostname": platform.node(),
        "python_version": platform.python_version(),
        "cpu_count": os.cpu_count(),
        "memory_total": psutil.virtual_memory().total,
        "memory_available": psutil.virtual_memory().available,
        "disk_total": psutil.disk_usage('/').total,
        "disk_free": psutil.disk_usage('/').free,
        "uptime_seconds": int(datetime.now().timestamp() - psutil.boot_time()),
        "load_avg": os.getloadavg() if hasattr(os, 'getloadavg') else None,
    }
    
    return system_info
