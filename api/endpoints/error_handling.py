"""
API endpoints for error handling feature
"""
from fastapi import APIRouter, Depends, HTTPException
from typing import List, Optional
from models.error_handling import <PERSON><PERSON>rHandlingModel, ErrorHandlingCreate, ErrorHandlingUpdate
from api.database import get_db
from sqlalchemy.orm import Session
from api.utils.rate_limiter import standard_rate_limit
from api.models.database.error_handling import ErrorHandling

router = APIRouter(
    prefix="/error-handling",
    tags=["error-handling"],
    responses={404: {"description": "Error handling not found"}},
)

@router.get("/", response_model=List[ErrorHandlingModel])
async def get_all_error_handlers(
    skip: int = 0, 
    limit: int = 100,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit)
):
    """
    Get all error handlers
    """
    error_handlers = db.query(ErrorHandling).offset(skip).limit(limit).all()
    return error_handlers

@router.get("/{error_handler_id}", response_model=ErrorHandlingModel)
async def get_error_handler(
    error_handler_id: int,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit)
):
    """
    Get a specific error handler by ID
    """
    error_handler = db.query(ErrorHandling).filter(ErrorHandling.id == error_handler_id).first()
    if error_handler is None:
        raise HTTPException(status_code=404, detail="Error handler not found")
    return error_handler

@router.post("/", response_model=ErrorHandlingModel)
async def create_error_handler(
    error_handler: ErrorHandlingCreate,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit)
):
    """
    Create a new error handler
    """
    db_error_handler = ErrorHandling(
        name=error_handler.name,
        description=error_handler.description,
        error_code=error_handler.error_code,
        error_type=error_handler.error_type,
        error_message=error_handler.error_message,
        is_user_facing=error_handler.is_user_facing,
        http_status_code=error_handler.http_status_code,
        severity=error_handler.severity
    )
    db.add(db_error_handler)
    db.commit()
    db.refresh(db_error_handler)
    return db_error_handler

@router.put("/{error_handler_id}", response_model=ErrorHandlingModel)
async def update_error_handler(
    error_handler_id: int,
    error_handler: ErrorHandlingUpdate,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit)
):
    """
    Update an error handler
    """
    db_error_handler = db.query(ErrorHandling).filter(ErrorHandling.id == error_handler_id).first()
    if db_error_handler is None:
        raise HTTPException(status_code=404, detail="Error handler not found")
    
    update_data = error_handler.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_error_handler, key, value)
    
    db.commit()
    db.refresh(db_error_handler)
    return db_error_handler

@router.delete("/{error_handler_id}")
async def delete_error_handler(
    error_handler_id: int,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit)
):
    """
    Delete an error handler
    """
    db_error_handler = db.query(ErrorHandling).filter(ErrorHandling.id == error_handler_id).first()
    if db_error_handler is None:
        raise HTTPException(status_code=404, detail="Error handler not found")
    
    db.delete(db_error_handler)
    db.commit()
    return {"message": "Error handler deleted"}
