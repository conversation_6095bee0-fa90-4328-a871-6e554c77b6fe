from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc
from typing import List, Optional, Dict, Any
from datetime import datetime
import json

from api.database import get_db
from api.models.logging import TestScenario, TestResult
from api.models.user import User
from api.auth.auth import get_current_user

from pydantic import BaseModel, Field

router = APIRouter(prefix="/testing", tags=["Testing"])

# Pydantic models
class TestScenarioBase(BaseModel):
    name: str
    description: str
    scenario_data: Dict[str, Any]

class TestScenarioCreate(TestScenarioBase):
    pass

class TestScenarioResponse(TestScenarioBase):
    id: int
    creator_id: int
    last_run: Optional[datetime] = None
    status: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class TestResultBase(BaseModel):
    test_scenario_id: int
    status: str
    duration: float
    result_data: Dict[str, Any]

class TestResultCreate(TestResultBase):
    pass

class TestResultResponse(TestResultBase):
    id: int
    run_timestamp: datetime
    created_at: datetime

    class Config:
        orm_mode = True

# Test Scenario endpoints
@router.post("/scenarios", response_model=TestScenarioResponse, status_code=status.HTTP_201_CREATED)
def create_test_scenario(
    scenario: TestScenarioCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new test scenario"""
    db_scenario = TestScenario(
        name=scenario.name,
        description=scenario.description,
        scenario_data=scenario.scenario_data,
        creator_id=current_user.id,
        status="created"
    )
    db.add(db_scenario)
    db.commit()
    db.refresh(db_scenario)
    return db_scenario

@router.get("/scenarios", response_model=List[TestScenarioResponse])
def get_test_scenarios(
    skip: int = 0, 
    limit: int = 100,
    name: Optional[str] = None,
    status: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get all test scenarios with optional filtering"""
    query = db.query(TestScenario)
    
    # Apply filters if provided
    if name:
        query = query.filter(TestScenario.name.ilike(f"%{name}%"))
    
    if status:
        query = query.filter(TestScenario.status == status)
    
    # Unless user is superuser, only show their scenarios
    if not current_user.is_superuser:
        query = query.filter(TestScenario.creator_id == current_user.id)
    
    # Order by newest first
    query = query.order_by(desc(TestScenario.created_at))
    
    return query.offset(skip).limit(limit).all()

@router.get("/scenarios/{scenario_id}", response_model=TestScenarioResponse)
def get_test_scenario(
    scenario_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific test scenario by ID"""
    scenario = db.query(TestScenario).filter(TestScenario.id == scenario_id).first()
    if not scenario:
        raise HTTPException(status_code=404, detail="Test scenario not found")
    
    # Check permissions
    if not current_user.is_superuser and scenario.creator_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this test scenario")
    
    return scenario

@router.put("/scenarios/{scenario_id}", response_model=TestScenarioResponse)
def update_test_scenario(
    scenario_id: int,
    scenario_update: TestScenarioCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update a test scenario"""
    db_scenario = db.query(TestScenario).filter(TestScenario.id == scenario_id).first()
    if not db_scenario:
        raise HTTPException(status_code=404, detail="Test scenario not found")
    
    # Check permissions
    if not current_user.is_superuser and db_scenario.creator_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to update this test scenario")
    
    # Update fields
    db_scenario.name = scenario_update.name
    db_scenario.description = scenario_update.description
    db_scenario.scenario_data = scenario_update.scenario_data
    db_scenario.updated_at = datetime.utcnow()
    
    db.commit()
    db.refresh(db_scenario)
    return db_scenario

@router.delete("/scenarios/{scenario_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_test_scenario(
    scenario_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete a test scenario"""
    db_scenario = db.query(TestScenario).filter(TestScenario.id == scenario_id).first()
    if not db_scenario:
        raise HTTPException(status_code=404, detail="Test scenario not found")
    
    # Check permissions
    if not current_user.is_superuser and db_scenario.creator_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to delete this test scenario")
    
    db.delete(db_scenario)
    db.commit()
    return None

# Test Result endpoints
@router.post("/results", response_model=TestResultResponse, status_code=status.HTTP_201_CREATED)
def create_test_result(
    result: TestResultCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new test result"""
    # Check if the test scenario exists
    scenario = db.query(TestScenario).filter(TestScenario.id == result.test_scenario_id).first()
    if not scenario:
        raise HTTPException(status_code=404, detail="Test scenario not found")
    
    # Check permissions on the scenario
    if not current_user.is_superuser and scenario.creator_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to create results for this test scenario")
    
    # Create the result
    db_result = TestResult(
        test_scenario_id=result.test_scenario_id,
        status=result.status,
        duration=result.duration,
        result_data=result.result_data,
        run_timestamp=datetime.utcnow()
    )
    
    # Update the scenario's last run and status
    scenario.last_run = datetime.utcnow()
    scenario.status = result.status
    
    db.add(db_result)
    db.commit()
    db.refresh(db_result)
    
    return db_result

@router.get("/results", response_model=List[TestResultResponse])
def get_test_results(
    skip: int = 0, 
    limit: int = 100,
    scenario_id: Optional[int] = None,
    status: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get test results with optional filtering"""
    # Start with a query that joins TestResult with TestScenario to check permissions
    query = db.query(TestResult).join(TestScenario)
    
    # Apply filters if provided
    if scenario_id:
        query = query.filter(TestResult.test_scenario_id == scenario_id)
    
    if status:
        query = query.filter(TestResult.status == status)
    
    # Unless user is superuser, only show results for their scenarios
    if not current_user.is_superuser:
        query = query.filter(TestScenario.creator_id == current_user.id)
    
    # Order by newest first
    query = query.order_by(desc(TestResult.run_timestamp))
    
    return query.offset(skip).limit(limit).all()

@router.get("/results/{result_id}", response_model=TestResultResponse)
def get_test_result(
    result_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific test result by ID"""
    # Query that joins TestResult with TestScenario to check permissions
    result = db.query(TestResult).filter(TestResult.id == result_id).first()
    if not result:
        raise HTTPException(status_code=404, detail="Test result not found")
    
    # Get the associated scenario to check permissions
    scenario = db.query(TestScenario).filter(TestScenario.id == result.test_scenario_id).first()
    if not scenario:
        raise HTTPException(status_code=404, detail="Associated test scenario not found")
    
    # Check permissions
    if not current_user.is_superuser and scenario.creator_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this test result")
    
    return result

@router.get("/scenarios/{scenario_id}/results", response_model=List[TestResultResponse])
def get_results_for_scenario(
    scenario_id: int,
    limit: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get test results for a specific scenario"""
    # Check if scenario exists and user has permissions
    scenario = db.query(TestScenario).filter(TestScenario.id == scenario_id).first()
    if not scenario:
        raise HTTPException(status_code=404, detail="Test scenario not found")
    
    if not current_user.is_superuser and scenario.creator_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access results for this scenario")
    
    # Get the results
    results = db.query(TestResult)\
        .filter(TestResult.test_scenario_id == scenario_id)\
        .order_by(desc(TestResult.run_timestamp))\
        .limit(limit)\
        .all()
    
    return results 