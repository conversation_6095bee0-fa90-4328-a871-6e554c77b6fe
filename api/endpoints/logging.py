from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc

from api.database import get_db
from api.models.logging import LogEntry, PerformanceMetric
from api.models.user import User
from api.services.logger import Logger
from api.deps import get_current_active_user, get_current_active_superuser
from api.auth.auth import get_current_user, get_optional_current_user

# Define models for request/response
from pydantic import BaseModel, Field


class LogEntryCreate(BaseModel):
    """Schema for creating a log entry."""
    level: str = Field(..., description="Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)")
    message: str = Field(..., description="Log message")
    source: str = Field(..., description="Log source (API, UI, SYSTEM)")
    component: str = Field(..., description="Component generating the log")
    correlation_id: Optional[str] = Field(None, description="Correlation ID")
    session_id: Optional[str] = Field(None, description="Session ID")
    metadata: Optional[Dict] = Field(None, description="Additional contextual data")


class LogEntryResponse(BaseModel):
    """Schema for log entry response."""
    id: int
    timestamp: datetime
    correlation_id: str
    user_id: Optional[int]
    session_id: Optional[str]
    log_level: str
    source: str
    component: str
    message: str
    metadata: Optional[Dict]
    created_at: datetime

    class Config:
        orm_mode = True


class MetricCreate(BaseModel):
    """Schema for creating a metric."""
    metric_type: str = Field(..., description="Type of metric")
    component: str = Field(..., description="Component being measured")
    value: float = Field(..., description="Numeric value of the metric")
    unit: str = Field(..., description="Unit of measurement")
    correlation_id: Optional[str] = Field(None, description="Correlation ID")
    session_id: Optional[str] = Field(None, description="Session ID")
    metadata: Optional[Dict] = Field(None, description="Additional contextual data")


class MetricResponse(BaseModel):
    """Schema for metric response."""
    id: int
    timestamp: datetime
    metric_type: str
    component: str
    value: float
    unit: str
    correlation_id: Optional[str]
    session_id: Optional[str]
    created_at: datetime

    class Config:
        orm_mode = True


class LoggingConfigUpdate(BaseModel):
    """Schema for updating logging configuration."""
    console_level: Optional[str] = Field(None, description="Minimum level for console logging")
    db_level: Optional[str] = Field(None, description="Minimum level for database logging")
    capture_request_body: Optional[bool] = Field(None, description="Whether to capture request bodies")
    capture_response_body: Optional[bool] = Field(None, description="Whether to capture response bodies")
    sensitive_fields: Optional[List[str]] = Field(None, description="Fields to redact from logs")
    async_logging: Optional[bool] = Field(None, description="Whether to use async logging")
    max_log_age_days: Optional[int] = Field(None, description="Maximum age of logs in days")


class LoggingConfig(BaseModel):
    """Schema for logging configuration."""
    console_level: str
    db_level: str
    capture_request_body: bool
    capture_response_body: bool
    sensitive_fields: List[str]
    async_logging: bool
    max_log_age_days: int


# Create router
router = APIRouter(
    prefix="/api/logs",
    tags=["logging"],
    responses={404: {"description": "Not found"}},
)


# API Endpoints
@router.post("", response_model=LogEntryResponse, status_code=201)
def create_log_entry(
    log_entry: LogEntryCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new log entry."""
    logger = Logger(db_session=db)
    
    # Set the user ID from the authenticated user
    logger.user_id = current_user.id
    
    # Set correlation ID if provided
    if log_entry.correlation_id:
        logger.correlation_id = log_entry.correlation_id
    
    # Set session ID if provided
    if log_entry.session_id:
        logger.session_id = log_entry.session_id
    
    # Log the message
    logger.log(
        level=log_entry.level,
        message=log_entry.message,
        source=log_entry.source,
        component=log_entry.component,
        metadata=log_entry.metadata
    )
    
    # Retrieve the created log entry
    db_log_entry = db.query(LogEntry).order_by(desc(LogEntry.id)).first()
    
    return db_log_entry


@router.get("", response_model=List[LogEntryResponse])
def get_logs(
    level: Optional[str] = Query(None, description="Filter by log level"),
    source: Optional[str] = Query(None, description="Filter by source"),
    component: Optional[str] = Query(None, description="Filter by component"),
    correlation_id: Optional[str] = Query(None, description="Filter by correlation ID"),
    session_id: Optional[str] = Query(None, description="Filter by session ID"),
    user_id: Optional[int] = Query(None, description="Filter by user ID"),
    start_time: Optional[datetime] = Query(None, description="Filter by start time"),
    end_time: Optional[datetime] = Query(None, description="Filter by end time"),
    limit: int = Query(50, description="Maximum number of logs to return"),
    offset: int = Query(0, description="Number of logs to skip"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Query logs with filtering options.
    Regular users can only see their own logs. Superusers can see all logs.
    """
    query = db.query(LogEntry)
    
    # Apply filters
    if level:
        query = query.filter(LogEntry.log_level == level.upper())
    
    if source:
        query = query.filter(LogEntry.source == source)
    
    if component:
        query = query.filter(LogEntry.component.like(f"%{component}%"))
    
    if correlation_id:
        query = query.filter(LogEntry.correlation_id == correlation_id)
    
    if session_id:
        query = query.filter(LogEntry.session_id == session_id)
    
    # Regular users can only see their own logs
    if not current_user.is_superuser:
        query = query.filter(LogEntry.user_id == current_user.id)
    # Superusers can filter by user_id if provided
    elif user_id:
        query = query.filter(LogEntry.user_id == user_id)
    
    if start_time:
        query = query.filter(LogEntry.timestamp >= start_time)
    
    if end_time:
        query = query.filter(LogEntry.timestamp <= end_time)
    
    # Apply pagination
    logs = query.order_by(desc(LogEntry.timestamp)).offset(offset).limit(limit).all()
    
    return logs


@router.get("/{log_id}", response_model=LogEntryResponse)
def get_log_entry(
    log_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get a specific log entry by ID."""
    log_entry = db.query(LogEntry).filter(LogEntry.id == log_id).first()
    
    if not log_entry:
        raise HTTPException(status_code=404, detail="Log entry not found")
    
    # Regular users can only see their own logs
    if not current_user.is_superuser and log_entry.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this log")
    
    return log_entry


@router.get("/correlation/{correlation_id}", response_model=List[LogEntryResponse])
def get_logs_by_correlation(
    correlation_id: str,
    limit: int = Query(50, description="Maximum number of logs to return"),
    offset: int = Query(0, description="Number of logs to skip"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get all logs for a correlation ID."""
    query = db.query(LogEntry).filter(LogEntry.correlation_id == correlation_id)
    
    # Regular users can only see their own logs
    if not current_user.is_superuser:
        query = query.filter(LogEntry.user_id == current_user.id)
    
    logs = query.order_by(LogEntry.timestamp).offset(offset).limit(limit).all()
    
    if not logs:
        raise HTTPException(status_code=404, detail="No logs found for this correlation ID")
    
    return logs


# Metrics API endpoints
@router.post("/metrics", response_model=MetricResponse, status_code=201)
def record_metric(
    metric: MetricCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Record a new performance metric."""
    logger = Logger(db_session=db)
    
    # Set correlation ID if provided
    if metric.correlation_id:
        logger.correlation_id = metric.correlation_id
    
    # Set session ID if provided
    if metric.session_id:
        logger.session_id = metric.session_id
    
    # Record the metric
    logger.record_metric(
        metric_type=metric.metric_type,
        component=metric.component,
        value=metric.value,
        unit=metric.unit,
        metadata=metric.metadata
    )
    
    # Retrieve the created metric
    db_metric = db.query(PerformanceMetric).order_by(desc(PerformanceMetric.id)).first()
    
    return db_metric


@router.get("/metrics", response_model=List[MetricResponse])
def get_metrics(
    metric_type: Optional[str] = Query(None, description="Filter by metric type"),
    component: Optional[str] = Query(None, description="Filter by component"),
    correlation_id: Optional[str] = Query(None, description="Filter by correlation ID"),
    session_id: Optional[str] = Query(None, description="Filter by session ID"),
    start_time: Optional[datetime] = Query(None, description="Filter by start time"),
    end_time: Optional[datetime] = Query(None, description="Filter by end time"),
    limit: int = Query(50, description="Maximum number of metrics to return"),
    offset: int = Query(0, description="Number of metrics to skip"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_superuser)
):
    """Query performance metrics with filtering options."""
    query = db.query(PerformanceMetric)
    
    # Apply filters
    if metric_type:
        query = query.filter(PerformanceMetric.metric_type == metric_type)
    
    if component:
        query = query.filter(PerformanceMetric.component.like(f"%{component}%"))
    
    if correlation_id:
        query = query.filter(PerformanceMetric.correlation_id == correlation_id)
    
    if session_id:
        query = query.filter(PerformanceMetric.session_id == session_id)
    
    if start_time:
        query = query.filter(PerformanceMetric.timestamp >= start_time)
    
    if end_time:
        query = query.filter(PerformanceMetric.timestamp <= end_time)
    
    # Apply pagination
    metrics = query.order_by(desc(PerformanceMetric.timestamp)).offset(offset).limit(limit).all()
    
    return metrics


# Configuration API endpoints
@router.get("/config", response_model=LoggingConfig)
def get_logging_config(
    current_user: User = Depends(get_current_active_superuser)
):
    """Get current logging configuration."""
    logger = Logger()
    return logger.config


@router.put("/config", response_model=LoggingConfig)
def update_logging_config(
    config_update: LoggingConfigUpdate,
    current_user: User = Depends(get_current_active_superuser)
):
    """Update logging configuration."""
    logger = Logger()
    
    # Update configuration with provided values
    for key, value in config_update.dict(exclude_unset=True).items():
        logger.config[key] = value
    
    # Return the updated configuration
    return logger.config


@router.post("/config/reset", response_model=LoggingConfig)
def reset_logging_config(
    current_user: User = Depends(get_current_active_superuser)
):
    """Reset logging configuration to defaults."""
    logger = Logger()
    
    # Reset to default configuration
    logger.config = {
        "console_level": "INFO",
        "db_level": "INFO",
        "capture_request_body": False,
        "capture_response_body": False,
        "sensitive_fields": ["password", "token", "secret", "credit_card", "ssn"],
        "async_logging": False,
        "max_log_age_days": 30
    }
    
    return logger.config


@router.post("/batch", response_model=List[LogEntryResponse], status_code=status.HTTP_201_CREATED)
async def create_log_entries_batch(
    log_entries: List[LogEntryCreate],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_optional_current_user)
):
    """Create multiple log entries in a single request"""
    created_logs = []
    for entry in log_entries:
        db_entry = LogEntry(
            log_level=entry.level,
            message=entry.message,
            source=entry.source,
            component=entry.component,
            correlation_id=entry.correlation_id,
            session_id=entry.session_id,
            user_id=current_user.id if current_user else None,
            metadata=entry.metadata
        )
        db.add(db_entry)
        created_logs.append(db_entry)
    
    db.commit()
    for log in created_logs:
        db.refresh(log)
    
    return created_logs 