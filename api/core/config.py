from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    # Database settings
    DATABASE_URL: str = "postgresql+psycopg2://regrigor:regrigor_password@postgres:5432/regrigor_db"
    SQLALCHEMY_TRACK_MODIFICATIONS: bool = False
    SQLALCHEMY_ECHO: bool = False

    # Security settings
    SESSION_SECRET: str = "dev_session_secret_key_12345"
    JWT_SECRET_KEY: str = "dev_jwt_secret_key_67890"
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # Redis settings
    REDIS_URL: str = "redis://redis:6379/0"

    # API settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "RegressionRigor"

    # CORS settings
    BACKEND_CORS_ORIGINS: list[str] = ["*"]

    class Config:
        case_sensitive = True


settings = Settings() 