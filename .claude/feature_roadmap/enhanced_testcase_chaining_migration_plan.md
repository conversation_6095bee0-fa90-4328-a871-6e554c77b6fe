# Enhanced Testcase Chaining & Sequencing - Migration Plan

## Overview

This document outlines the migration plan for implementing the Enhanced Testcase Chaining & Sequencing feature in the Regression Rigor platform. It covers the necessary database schema changes, code modifications, and deployment strategy to ensure a smooth transition to the new functionality.

## Implementation Strategy

### Phase 1: Database Schema Changes

#### Step 1: Create New Tables

The following database tables need to be created:

1. **testcase_chains**
   ```sql
   CREATE TABLE testcase_chains (
       id SERIAL PRIMARY KEY,
       name VARCHAR(100) NOT NULL,
       description TEXT,
       created_by INTEGER REFERENCES flask_users(id),
       status VARCHAR(20) DEFAULT 'draft',
       created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       deleted_time TIMESTAMP
   );
   
   CREATE INDEX idx_tc_created_by ON testcase_chains (created_by);
   CREATE INDEX idx_tc_status ON testcase_chains (status);
   CREATE INDEX idx_tc_deleted_time ON testcase_chains (deleted_time);
   ```

2. **testcase_chain_nodes**
   ```sql
   CREATE TABLE testcase_chain_nodes (
       id SERIAL PRIMARY KEY,
       chain_id INTEGER REFERENCES testcase_chains(id) ON DELETE CASCADE,
       testcase_id INTEGER REFERENCES test_cases(id) ON DELETE CASCADE,
       node_type VARCHAR(20) DEFAULT 'standard',
       position_x FLOAT DEFAULT 0,
       position_y FLOAT DEFAULT 0,
       execution_order INTEGER DEFAULT 0,
       condition_expression TEXT,
       created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       deleted_time TIMESTAMP
   );
   
   CREATE INDEX idx_tcn_chain_id ON testcase_chain_nodes (chain_id);
   CREATE INDEX idx_tcn_testcase_id ON testcase_chain_nodes (testcase_id);
   CREATE INDEX idx_tcn_node_type ON testcase_chain_nodes (node_type);
   CREATE INDEX idx_tcn_deleted_time ON testcase_chain_nodes (deleted_time);
   ```

3. **testcase_chain_edges**
   ```sql
   CREATE TABLE testcase_chain_edges (
       id SERIAL PRIMARY KEY,
       source_node_id INTEGER REFERENCES testcase_chain_nodes(id) ON DELETE CASCADE,
       target_node_id INTEGER REFERENCES testcase_chain_nodes(id) ON DELETE CASCADE,
       edge_type VARCHAR(20) DEFAULT 'standard',
       condition TEXT,
       created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       deleted_time TIMESTAMP
   );
   
   CREATE INDEX idx_tce_source_node_id ON testcase_chain_edges (source_node_id);
   CREATE INDEX idx_tce_target_node_id ON testcase_chain_edges (target_node_id);
   CREATE INDEX idx_tce_edge_type ON testcase_chain_edges (edge_type);
   CREATE INDEX idx_tce_deleted_time ON testcase_chain_edges (deleted_time);
   ```

4. **chain_executions**
   ```sql
   CREATE TABLE chain_executions (
       id SERIAL PRIMARY KEY,
       chain_id INTEGER REFERENCES testcase_chains(id) ON DELETE CASCADE,
       started_by INTEGER REFERENCES flask_users(id),
       start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       end_time TIMESTAMP,
       status VARCHAR(20) DEFAULT 'running',
       created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       deleted_time TIMESTAMP
   );
   
   CREATE INDEX idx_ce_chain_id ON chain_executions (chain_id);
   CREATE INDEX idx_ce_started_by ON chain_executions (started_by);
   CREATE INDEX idx_ce_status ON chain_executions (status);
   CREATE INDEX idx_ce_start_time ON chain_executions (start_time);
   CREATE INDEX idx_ce_deleted_time ON chain_executions (deleted_time);
   ```

5. **node_executions**
   ```sql
   CREATE TABLE node_executions (
       id SERIAL PRIMARY KEY,
       chain_execution_id INTEGER REFERENCES chain_executions(id) ON DELETE CASCADE,
       node_id INTEGER REFERENCES testcase_chain_nodes(id) ON DELETE CASCADE,
       start_time TIMESTAMP,
       end_time TIMESTAMP,
       status VARCHAR(20) DEFAULT 'pending',
       result_data JSONB,
       created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       deleted_time TIMESTAMP
   );
   
   CREATE INDEX idx_ne_chain_execution_id ON node_executions (chain_execution_id);
   CREATE INDEX idx_ne_node_id ON node_executions (node_id);
   CREATE INDEX idx_ne_status ON node_executions (status);
   CREATE INDEX idx_ne_deleted_time ON node_executions (deleted_time);
   ```

6. **testcase_conditions**
   ```sql
   CREATE TABLE testcase_conditions (
       id SERIAL PRIMARY KEY,
       testcase_id INTEGER REFERENCES test_cases(id) ON DELETE CASCADE,
       condition_type VARCHAR(20) NOT NULL,
       name VARCHAR(100) NOT NULL,
       description TEXT,
       validation_script TEXT,
       required BOOLEAN DEFAULT TRUE,
       created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       deleted_time TIMESTAMP
   );
   
   CREATE INDEX idx_tc_testcase_id ON testcase_conditions (testcase_id);
   CREATE INDEX idx_tc_condition_type ON testcase_conditions (condition_type);
   CREATE INDEX idx_tc_deleted_time ON testcase_conditions (deleted_time);
   ```

#### Step 2: Create Database Migration Scripts

1. Generate migration script using the SQLAlchemy migration framework:
   ```bash
   python -m alembic revision --autogenerate -m "Add testcase chaining tables"
   ```

2. Review and adjust the auto-generated migration script to ensure correctness.

3. Prepare a rollback script in case issues arise during deployment.

### Phase 2: Backend Implementation

#### Step 1: Create ORM Models

1. Add the SQLAlchemy models for all new tables in `api/models/base.py`:
   - TestcaseChainDB
   - TestcaseChainNodeDB
   - TestcaseChainEdgeDB
   - ChainExecutionDB
   - NodeExecutionDB
   - TestcaseConditionDB

2. Add relationships between models to ensure proper ORM functionality.

#### Step 2: Create API Schema Models

1. Add Pydantic models in `api/models/schemas.py` for request and response validation:
   - TestcaseChainCreate, TestcaseChain, TestcaseChainDetail
   - TestcaseChainNodeCreate, TestcaseChainNode
   - TestcaseChainEdgeCreate, TestcaseChainEdge
   - ChainExecution, ChainExecutionDetail
   - NodeExecution
   - TestcaseConditionCreate, TestcaseCondition

#### Step 3: Implement Service Classes

1. Create service classes for chain management and execution:
   - TestcaseChainService: For CRUD operations on chains
   - ChainExecutionService: For executing and managing chain executions
   - ConditionValidatorService: For validating preconditions and postconditions

#### Step 4: Implement API Endpoints

1. Create API routes in `api/routers/`:
   - `testcase_chains.py`: For chain management endpoints
   - `chain_executions.py`: For chain execution endpoints
   - `testcase_conditions.py`: For condition management endpoints

2. Register the new routers in the main API application.

#### Step 5: Implement Background Jobs

1. Create background job modules in `api/jobs/`:
   - `chain_execution_job.py`: For processing chain executions
   - `node_cleanup_job.py`: For handling cleanup after node executions

2. Register the jobs with the job scheduler.

### Phase 3: Frontend Implementation

#### Step 1: Create React Components

1. Develop the Chain Designer component:
   ```
   frontend/src/components/testcase-chains/ChainDesigner.jsx
   frontend/src/components/testcase-chains/NodePalette.jsx
   frontend/src/components/testcase-chains/PropertiesPanel.jsx
   ```

2. Develop the Chain Visualization component:
   ```
   frontend/src/components/testcase-chains/ChainVisualization.jsx
   frontend/src/components/testcase-chains/ExecutionControls.jsx
   frontend/src/components/testcase-chains/ResultsPanel.jsx
   ```

3. Develop the Condition Management component:
   ```
   frontend/src/components/testcase-conditions/ConditionEditor.jsx
   frontend/src/components/testcase-conditions/ConditionList.jsx
   ```

#### Step 2: Create API Client Services

1. Implement JavaScript API client services for interacting with the backend:
   ```
   frontend/src/services/testcaseChainService.js
   frontend/src/services/chainExecutionService.js
   frontend/src/services/conditionService.js
   ```

#### Step 3: Create Views and Routes

1. Create views for the new features:
   ```
   frontend/src/views/testcase-chains/ChainListView.jsx
   frontend/src/views/testcase-chains/ChainDetailView.jsx
   frontend/src/views/testcase-chains/ChainDesignerView.jsx
   frontend/src/views/testcase-chains/ExecutionView.jsx
   ```

2. Add routes to the application router:
   ```jsx
   <Route path="/testcase-chains" component={ChainListView} />
   <Route path="/testcase-chains/:id" component={ChainDetailView} />
   <Route path="/testcase-chains/:id/design" component={ChainDesignerView} />
   <Route path="/testcase-chains/:id/executions/:executionId" component={ExecutionView} />
   ```

#### Step 4: Update Navigation

1. Add links to the new features in the main navigation menu:
   ```jsx
   <Nav.Item>
     <Nav.Link as={Link} to="/testcase-chains">
       Testcase Chains
     </Nav.Link>
   </Nav.Item>
   ```

2. Add contextual links in relevant views (campaigns, testcases) to create or view chains.

### Phase 4: Integration and Testing

#### Step 1: Unit Testing

1. Create unit tests for all models and services:
   ```
   tests/test_api/test_models/test_testcase_chain_models.py
   tests/test_api/test_services/test_testcase_chain_service.py
   tests/test_api/test_services/test_chain_execution_service.py
   ```

#### Step 2: API Testing

1. Create API tests for all new endpoints:
   ```
   tests/test_api/test_endpoints/test_testcase_chain_api.py
   tests/test_api/test_endpoints/test_chain_execution_api.py
   tests/test_api/test_endpoints/test_condition_api.py
   ```

#### Step 3: Integration Testing

1. Create integration tests for the complete chain execution flow:
   ```
   tests/test_api/test_integration/test_testcase_chain_execution.py
   ```

#### Step 4: Frontend Testing

1. Create tests for React components:
   ```
   frontend/src/__tests__/components/testcase-chains/ChainDesigner.test.jsx
   frontend/src/__tests__/components/testcase-chains/ChainVisualization.test.jsx
   ```

2. Create end-to-end tests for the complete workflow:
   ```
   tests/e2e/testcase_chain_workflow.spec.js
   ```

### Phase 5: Documentation

#### Step 1: API Documentation

1. Update the API documentation with the new endpoints:
   ```
   docs/api/testcase_chains.md
   docs/api/chain_executions.md
   docs/api/testcase_conditions.md
   ```

#### Step 2: User Documentation

1. Create user guides for the new features:
   ```
   docs/user/testcase_chains.md
   docs/user/chain_designer.md
   docs/user/chain_execution.md
   ```

#### Step 3: Developer Documentation

1. Create developer documentation for extending the system:
   ```
   docs/dev/testcase_chain_architecture.md
   docs/dev/extending_chain_execution.md
   ```

#### Step 4: Examples and Tutorials

1. Create example chains and tutorials:
   ```
   docs/examples/ransomware_chain.md
   docs/examples/data_exfiltration_chain.md
   docs/tutorials/creating_your_first_chain.md
   ```

## Deployment Strategy

### 1. Staged Deployment

We will use a staged deployment approach to minimize risk:

1. **Stage 1**: Deploy database changes and backend API
   - Run database migrations
   - Deploy API endpoints but keep them feature-flagged
   - Deploy service classes and background jobs

2. **Stage 2**: Deploy frontend components
   - Deploy React components with feature flags
   - Enable the features for selected test users

3. **Stage 3**: Full rollout
   - Enable all features for all users
   - Monitor for issues and gather feedback

### 2. Feature Flags

We will use feature flags to control the rollout:

1. Create feature flags for:
   - `enable_testcase_chains`: Controls overall access to the feature
   - `enable_chain_execution`: Controls the ability to execute chains
   - `enable_chain_designer`: Controls access to the visual designer

2. Initially set all flags to off except for test accounts.

3. Gradually enable flags based on successful testing.

### 3. Rollback Plan

In case of issues, we have the following rollback plan:

1. **For frontend issues**:
   - Disable feature flags to hide the functionality
   - Deploy fixes and re-enable when ready

2. **For API issues**:
   - Disable affected endpoints via feature flags
   - Deploy fixes and re-enable when ready

3. **For database issues**:
   - Execute rollback migration if necessary
   - Address issues in a new migration
   - Re-deploy corrected migrations

## Timeline and Dependencies

### Week 1: Database and Core Backend

**Day 1-2: Database**
- Create database migration scripts
- Test migrations in development environment
- Implement ORM models
- Unit test database models

**Day 3-5: Core Backend**
- Implement service classes
- Implement API endpoints
- Unit test services
- API test endpoints

### Week 2: Execution Engine and Background Jobs

**Day 1-3: Execution Engine**
- Implement execution sequencing logic
- Implement condition validation
- Unit test execution engine
- Integration test execution workflows

**Day 4-5: Background Jobs**
- Implement background job system
- Implement chain execution job
- Implement cleanup jobs
- Test job scheduling and execution

### Week 3: Frontend Implementation

**Day 1-2: Base Components**
- Implement API client services
- Implement views and routes
- Implement list and detail views

**Day 3-5: Advanced Components**
- Implement chain designer
- Implement chain visualization
- Implement execution controls
- Test frontend components

### Week 4: Testing, Documentation, and Deployment

**Day 1-2: Testing**
- Complete integration testing
- End-to-end testing
- Performance testing
- Fix identified issues

**Day 3-4: Documentation**
- Write API documentation
- Write user guides
- Write developer documentation
- Create examples and tutorials

**Day 5: Deployment**
- Staged deployment to production
- Feature flag configuration
- Monitoring and issue resolution

## Conclusion

This migration plan provides a comprehensive approach to implementing the Enhanced Testcase Chaining & Sequencing feature. By following this structured approach, we can ensure a smooth transition to the new functionality while minimizing risks and maintaining system stability.

The plan includes detailed steps for database schema changes, backend implementation, frontend development, testing, and documentation. It also provides a staged deployment strategy with feature flags for controlled rollout and a rollback plan in case issues arise.

By executing this plan, we will significantly enhance the platform's capabilities for security testing and assessment, enabling the creation and execution of complex attack chains that better represent real-world threat scenarios. 