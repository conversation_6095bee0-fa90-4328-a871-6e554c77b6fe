# Advanced Soft-Deletion Framework - Feature Plan

## Overview

The Advanced Soft-Deletion Framework enhances the existing soft-deletion capabilities in the Regression Rigor platform by introducing configurable retention policies, recovery workflows, cascade controls, and comprehensive audit trails. This feature ensures data integrity while providing flexibility in data management, allowing for temporary deletion of records with the option to recover them within a defined retention period.

## Feature Description

The Advanced Soft-Deletion Framework will include the following key features:

1. **Configurable Retention Policies**
   - Define retention periods for different entity types
   - Configure automatic purging of expired soft-deleted records
   - Set granular retention rules based on entity properties

2. **Recovery Workflows**
   - Implement user-friendly recovery process for soft-deleted entities
   - Support batch recovery operations
   - Include validation to prevent data inconsistencies during recovery

3. **Cascade Control**
   - Configure relationship-specific cascade behavior
   - Control how deletion cascades through related entities
   - Support options for blocking deletion if dependent records exist

4. **Audit Trail**
   - Track all deletion and recovery operations
   - Record metadata including timestamp, user, reason, and affected entities
   - Support compliance reporting for data lifecycle management

## Implementation Plan

### Phase 1: Planning and Design (1-2 days)

#### Database Schema

1. **SoftDeletionPolicy Model**
   - `id`: Unique identifier
   - `entity_type`: Type of entity this policy applies to
   - `retention_period_days`: Number of days to retain deleted records
   - `auto_purge`: Boolean indicating if automatic purging is enabled
   - `created_at`: Creation timestamp
   - `updated_at`: Update timestamp

2. **SoftDeletionAudit Model**
   - `id`: Unique identifier
   - `entity_type`: Type of entity affected
   - `entity_id`: ID of the affected entity
   - `operation_type`: Type of operation (soft delete, purge, recover)
   - `performed_by`: User who performed the operation
   - `reason`: Reason for the operation
   - `operation_time`: When the operation was performed
   - `metadata`: JSON field for additional context

3. **Modify Existing Models**
   - Add `deleted_at`: Soft deletion timestamp
   - Add `deleted_by`: User who deleted the record
   - Add `deletion_reason`: Reason for deletion
   - Add `cascade_strategy`: Strategy for handling related records

#### API Design

1. **Policy Management Endpoints**
   - `GET /api/v1/deletion-policies`: List all deletion policies
   - `POST /api/v1/deletion-policies`: Create a new deletion policy
   - `GET /api/v1/deletion-policies/{policy_id}`: Get a specific policy
   - `PUT /api/v1/deletion-policies/{policy_id}`: Update a policy
   - `DELETE /api/v1/deletion-policies/{policy_id}`: Delete a policy

2. **Soft Deletion Operations**
   - `DELETE /api/v1/{entity_type}/{entity_id}`: Soft delete an entity (with `permanent=false` query param)
   - `DELETE /api/v1/{entity_type}/{entity_id}?permanent=true`: Permanently delete an entity
   - `POST /api/v1/{entity_type}/{entity_id}/recover`: Recover a soft-deleted entity

3. **Audit and Reporting**
   - `GET /api/v1/deletion-audit`: Get deletion audit records with filtering
   - `GET /api/v1/deletion-audit/stats`: Get deletion statistics
   - `GET /api/v1/deletion-audit/pending-purge`: Get records pending purge

#### Background Jobs

1. **Auto-Purge Job**
   - Identify soft-deleted records past retention period
   - Permanently delete expired records
   - Log purge operations in audit trail

2. **Retention Report Job**
   - Generate reports on upcoming purges
   - Track retention policy compliance

### Phase 2: Core Implementation (3-4 days)

1. Implement database models and migrations
2. Create core API endpoints for policy management
3. Implement soft deletion and recovery logic
4. Add basic audit logging functionality

### Phase 3: Advanced Features (3-4 days)

1. Implement configurable retention policies
2. Add cascade deletion control logic
3. Create background jobs for auto-purging
4. Implement notification system for pending purges

### Phase 4: Testing and Documentation (2-3 days)

1. Create comprehensive test suite
   - Unit tests for models and services
   - Integration tests for API endpoints
   - End-to-end tests for complete workflows
   - Performance tests for bulk operations

2. Documentation
   - API documentation
   - User guides
   - Admin configuration documentation
   - Code documentation

### Phase 5: Review and Refinement (1-2 days)

1. Code review and refinement
2. Performance optimization
3. Final testing and documentation updates

## Implementation Details

### Database Models Implementation

```python
class SoftDeletionPolicy(db.Model):
    __tablename__ = 'soft_deletion_policies'
    
    id = db.Column(db.Integer, primary_key=True)
    entity_type = db.Column(db.String(50), nullable=False)
    retention_period_days = db.Column(db.Integer, nullable=False)
    auto_purge = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (db.UniqueConstraint('entity_type', name='uq_entity_type'),)


class SoftDeletionAudit(db.Model):
    __tablename__ = 'soft_deletion_audits'
    
    id = db.Column(db.Integer, primary_key=True)
    entity_type = db.Column(db.String(50), nullable=False)
    entity_id = db.Column(db.Integer, nullable=False)
    operation_type = db.Column(db.String(20), nullable=False)  # 'soft_delete', 'recover', 'purge'
    performed_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    reason = db.Column(db.Text)
    operation_time = db.Column(db.DateTime, default=datetime.utcnow)
    metadata = db.Column(db.JSON)
    
    __table_args__ = (db.Index('idx_entity', 'entity_type', 'entity_id'),)


# Mixin for models that support soft deletion
class SoftDeletionMixin:
    deleted_at = db.Column(db.DateTime, nullable=True)
    deleted_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    deletion_reason = db.Column(db.Text, nullable=True)
    
    @property
    def is_deleted(self):
        return self.deleted_at is not None
    
    def soft_delete(self, user_id, reason=None):
        self.deleted_at = datetime.utcnow()
        self.deleted_by = user_id
        self.deletion_reason = reason
        
    def recover(self):
        self.deleted_at = None
        self.deleted_by = None
        self.deletion_reason = None
```

### API Services Implementation

```python
class SoftDeletionService:
    @staticmethod
    def soft_delete_entity(entity_type, entity_id, user_id, reason=None, cascade=True):
        """Soft delete an entity and optionally cascade to related entities"""
        # Get the entity
        entity = get_entity_by_type_and_id(entity_type, entity_id)
        if not entity:
            raise EntityNotFoundError(f"{entity_type} with id {entity_id} not found")
        
        # Check if already deleted
        if hasattr(entity, 'is_deleted') and entity.is_deleted:
            raise AlreadyDeletedError(f"{entity_type} with id {entity_id} is already deleted")
        
        # Perform soft deletion
        entity.soft_delete(user_id, reason)
        
        # Create audit record
        audit = SoftDeletionAudit(
            entity_type=entity_type,
            entity_id=entity_id,
            operation_type='soft_delete',
            performed_by=user_id,
            reason=reason
        )
        db.session.add(audit)
        
        # Handle cascade if enabled
        if cascade:
            related_entities = get_related_entities(entity_type, entity_id)
            for rel_type, rel_id in related_entities:
                SoftDeletionService.soft_delete_entity(rel_type, rel_id, user_id, 
                                                      f"Cascaded from {entity_type} {entity_id}", 
                                                      cascade=False)
        
        db.session.commit()
        return entity
    
    @staticmethod
    def recover_entity(entity_type, entity_id, user_id, reason=None, cascade=True):
        """Recover a soft-deleted entity and optionally cascade to related entities"""
        # Similar implementation to soft_delete_entity but for recovery
        pass
    
    @staticmethod
    def purge_entity(entity_type, entity_id, user_id, reason=None):
        """Permanently delete a soft-deleted entity"""
        # Implementation for permanent deletion
        pass
    
    @staticmethod
    def get_entities_pending_purge():
        """Get all entities that are past their retention period"""
        # Implementation to find expired soft-deleted records
        pass
```

### Background Jobs Implementation

```python
def auto_purge_job():
    """Background job to automatically purge expired soft-deleted records"""
    entities_to_purge = SoftDeletionService.get_entities_pending_purge()
    for entity_type, entity_id in entities_to_purge:
        try:
            SoftDeletionService.purge_entity(
                entity_type, 
                entity_id, 
                system_user_id,
                "Automatic purge due to retention policy expiration"
            )
        except Exception as e:
            log_error(f"Failed to purge {entity_type} {entity_id}: {str(e)}")
```

## Testing Strategy

1. **Unit Tests**
   - Test model methods (soft_delete, recover)
   - Test service methods (soft_delete_entity, recover_entity, purge_entity)
   - Test policy enforcement logic

2. **Integration Tests**
   - Test API endpoints
   - Test background jobs
   - Test cascade behavior

3. **End-to-End Tests**
   - Test complete workflows (delete → recover, delete → expire → purge)
   - Test UI components (if applicable)

4. **Performance Tests**
   - Test bulk operations
   - Test with large datasets
   - Test background job performance

## Expected Outcomes

1. **Enhanced Data Management**
   - Improved control over deleted data
   - Reduced risk of data loss
   - Better compliance with data retention policies

2. **Improved User Experience**
   - Straightforward recovery of accidentally deleted items
   - Transparent audit of deletion operations
   - Configurable deletion behavior

3. **Operational Benefits**
   - Automated data cleanup
   - Reduced database size through controlled purging
   - Clear audit trail for compliance reporting

## Conclusion

The Advanced Soft-Deletion Framework will significantly improve the data management capabilities of the Regression Rigor platform. By implementing configurable retention policies, recovery workflows, cascade controls, and comprehensive audit trails, we will enhance data integrity while providing flexibility in how deleted data is managed.

This feature follows the successful implementation of the Comprehensive Tagging System and continues our focus on building robust foundational features for the platform. 