# Complete Core CRUD Operations

## 1. Title and Overview
### 1.1 Document Title & Version
Complete Core CRUD Operations Product Requirements Document
Version: 1.0
Date: March 13, 2023
Author: Regression Rigor Team

### 1.2 Product Summary
The Complete Core CRUD Operations feature aims to enhance and finalize the fundamental functionality of the Regression Rigor platform by ensuring all core components have fully implemented Create, Read, Update, and Delete operations. This includes User Authentication, Campaign Management, Test Case Management, and Assessment/Test Outcome Management.

These core operations form the backbone of the platform, enabling security professionals to manage users, organize security testing campaigns, create and execute test cases, and track assessment outcomes. By completing these features, we will establish a solid foundation for more advanced capabilities like MITRE Navigator integration and enhanced testcase chaining.

The implementation will focus on filling gaps in the existing codebase, enhancing current functionality, and ensuring a consistent, secure, and user-friendly experience across all core components.

## 2. User Personas
### 2.1 Key User Types

1. **Security Administrators**
   - Role: Responsible for managing users, permissions, and overall platform configuration
   - Technical Expertise: High (security professionals with technical background)
   - Primary Goals: Ensure secure access to the platform, manage user accounts, configure platform settings

2. **Security Testers**
   - Role: Create and execute security tests, document findings
   - Technical Expertise: High (penetration testers, security analysts)
   - Primary Goals: Organize testing activities, execute tests, record results

3. **Security Managers**
   - Role: Oversee security testing programs, review results, make decisions
   - Technical Expertise: Medium to High (security management with technical understanding)
   - Primary Goals: Track testing progress, analyze results, generate reports

4. **Developers**
   - Role: Review security findings, implement fixes
   - Technical Expertise: High (software developers)
   - Primary Goals: Understand security issues, track remediation progress

### 2.2 Basic Persona Details

**Security Administrator (Sam)**
- Needs: Efficient user management, role-based access control, audit logging
- Pain Points: Complex user management interfaces, lack of granular permissions, insufficient audit trails
- How This Feature Helps: Provides comprehensive user management capabilities, role-based access control, and detailed audit logging

**Security Tester (Taylor)**
- Needs: Easy creation and management of test cases, efficient execution tracking
- Pain Points: Cumbersome test case creation, difficulty tracking test execution status
- How This Feature Helps: Streamlines test case creation and management, provides clear execution tracking

**Security Manager (Morgan)**
- Needs: Campaign organization, progress tracking, results analysis
- Pain Points: Limited visibility into testing progress, difficulty organizing related tests
- How This Feature Helps: Enables structured campaign management, provides clear progress tracking and results analysis

**Developer (Dana)**
- Needs: Clear understanding of security findings, ability to track remediation
- Pain Points: Unclear security findings, difficulty tracking remediation status
- How This Feature Helps: Provides detailed test outcomes with clear remediation tracking

### 2.3 Role-based Access

**Administrator**
- Full access to all platform features
- User management (create, update, delete users)
- Role management (assign roles, modify permissions)
- Platform configuration
- Access to all campaigns, test cases, and assessments

**Manager**
- Create and manage campaigns
- Assign testers to campaigns
- View all test cases and results
- Generate reports
- Cannot manage users or configure platform settings

**Tester**
- Create and execute test cases within assigned campaigns
- Record test results
- View assigned campaigns and related test cases
- Cannot create campaigns or manage users

**Observer**
- View assigned campaigns, test cases, and results
- Cannot create or modify any data
- Limited to read-only access

## 3. User Stories

### US-001: User Registration
**Description:** As a new user, I want to register for an account so that I can access the platform.

**Acceptance Criteria:**
- User can access a registration form
- Form validates required fields (username, email, password)
- Password strength requirements are enforced
- Email verification is required
- User receives confirmation upon successful registration
- Administrators are notified of new user registrations

### US-002: User Authentication
**Description:** As a user, I want to securely log in to the platform so that I can access my account.

**Acceptance Criteria:**
- User can log in with username/email and password
- JWT authentication is implemented
- Failed login attempts are limited to prevent brute force attacks
- User sessions expire after a configurable period of inactivity
- Users can log out from any page
- Login attempts are logged for security auditing

### US-003: Password Management
**Description:** As a user, I want to reset my password if I forget it so that I can regain access to my account.

**Acceptance Criteria:**
- User can request a password reset via email
- Reset links expire after a configurable time period
- User must confirm current password when changing password
- Password history prevents reuse of recent passwords
- User receives notification when password is changed

### US-004: User Profile Management
**Description:** As a user, I want to view and update my profile information so that my account details remain accurate.

**Acceptance Criteria:**
- User can view their profile information
- User can update name, email, and other profile details
- Email changes require verification
- User can set preferences for notifications
- Profile changes are logged for audit purposes

### US-005: User Role Management
**Description:** As an administrator, I want to assign roles to users so that they have appropriate access permissions.

**Acceptance Criteria:**
- Administrator can view all users and their current roles
- Administrator can assign predefined roles to users
- Role changes take effect immediately
- Role assignments are logged for audit purposes
- Users receive notification when their role changes

### US-006: Campaign Creation
**Description:** As a manager, I want to create a new security testing campaign so that I can organize related testing activities.

**Acceptance Criteria:**
- Manager can create a new campaign with name, description, and status
- Manager can set campaign start and end dates
- Manager can assign testers to the campaign
- Campaign creation is logged for audit purposes
- Campaign appears in the campaigns list immediately after creation

### US-007: Campaign Management
**Description:** As a manager, I want to update campaign details and status so that I can keep campaign information current.

**Acceptance Criteria:**
- Manager can edit campaign name, description, dates, and status
- Manager can add or remove testers from the campaign
- Changes to campaign details are logged
- Campaign status can be changed (active, inactive, completed)
- Campaign details page shows last updated timestamp

### US-008: Campaign Deletion
**Description:** As a manager, I want to delete campaigns that are no longer needed so that I can keep the platform organized.

**Acceptance Criteria:**
- Manager can mark campaigns for deletion
- Confirmation is required before deletion
- Deletion is logged for audit purposes
- Associated test cases and results are preserved or archived
- Deleted campaigns no longer appear in the active campaigns list

### US-009: Campaign Viewing
**Description:** As a user, I want to view campaigns I have access to so that I can see my assigned work.

**Acceptance Criteria:**
- User can see a list of campaigns they have access to
- List can be filtered by status, date, or name
- Campaign details show assigned testers and progress
- Campaign details include links to associated test cases
- Campaign metrics show completion percentage and results summary

### US-010: Test Case Creation
**Description:** As a tester, I want to create new test cases so that I can document testing procedures.

**Acceptance Criteria:**
- Tester can create a new test case with name, description, and steps
- Test case can be assigned to a campaign
- Test case includes expected results
- Test case creation is logged for audit purposes
- Test case appears in the campaign's test cases list

### US-011: Test Case Management
**Description:** As a tester, I want to update test cases so that I can keep testing procedures current.

**Acceptance Criteria:**
- Tester can edit test case name, description, steps, and expected results
- Changes to test cases are logged
- Test case history is preserved
- Test case details page shows last updated timestamp
- Test case can be reassigned to different campaigns

### US-012: Test Case Deletion
**Description:** As a tester, I want to delete test cases that are no longer needed so that I can keep the platform organized.

**Acceptance Criteria:**
- Tester can mark test cases for deletion
- Confirmation is required before deletion
- Deletion is logged for audit purposes
- Associated test results are preserved or archived
- Deleted test cases no longer appear in the active test cases list

### US-013: Test Case Viewing
**Description:** As a user, I want to view test cases I have access to so that I can understand testing procedures.

**Acceptance Criteria:**
- User can see a list of test cases they have access to
- List can be filtered by campaign, status, or name
- Test case details show steps and expected results
- Test case details include links to associated campaigns
- Test case history shows changes over time

### US-014: Test Execution Recording
**Description:** As a tester, I want to record test execution results so that I can document the outcome of testing activities.

**Acceptance Criteria:**
- Tester can record test execution with actual results
- Tester can mark test as passed, failed, or blocked
- Tester can attach evidence (screenshots, logs)
- Test execution is logged with timestamp and executor
- Test status is updated in the campaign dashboard

### US-015: Test Result Management
**Description:** As a tester, I want to update test results so that I can correct or add information.

**Acceptance Criteria:**
- Tester can edit test results and status
- Changes to test results are logged
- Test result history is preserved
- Test result details page shows last updated timestamp
- Notifications are sent to relevant stakeholders on status changes

### US-016: Test Result Viewing
**Description:** As a user, I want to view test results so that I can understand the outcome of testing activities.

**Acceptance Criteria:**
- User can see a list of test results they have access to
- List can be filtered by campaign, status, or date
- Test result details show actual results and status
- Test result details include links to associated test cases
- Test result history shows changes over time

### US-017: Assessment Summary Creation
**Description:** As a manager, I want to create assessment summaries so that I can document the overall findings of a campaign.

**Acceptance Criteria:**
- Manager can create an assessment summary for a campaign
- Summary includes overall status, key findings, and recommendations
- Manager can attach supporting documents
- Summary creation is logged for audit purposes
- Summary appears in the campaign details page

### US-018: Assessment Summary Management
**Description:** As a manager, I want to update assessment summaries so that I can keep findings and recommendations current.

**Acceptance Criteria:**
- Manager can edit assessment summary details
- Changes to assessment summaries are logged
- Assessment summary history is preserved
- Assessment summary page shows last updated timestamp
- Notifications are sent to relevant stakeholders on updates

### US-019: Assessment Report Generation
**Description:** As a manager, I want to generate assessment reports so that I can share findings with stakeholders.

**Acceptance Criteria:**
- Manager can generate reports in multiple formats (PDF, HTML)
- Reports include campaign details, test results, and summary
- Reports can be customized with templates
- Report generation is logged for audit purposes
- Reports can be downloaded or shared via email

### US-020: Secure API Access
**Description:** As a developer, I want to access the platform via API so that I can integrate with other tools.

**Acceptance Criteria:**
- API endpoints are available for all CRUD operations
- API access requires authentication with API keys
- API requests are rate-limited to prevent abuse
- API access is logged for audit purposes
- API documentation is available for developers

## 4. Technical Requirements
### 4.1 Database Schema Changes

The following database schema changes are required:

1. **User Management Enhancements**
   - Add `password_reset_token` and `password_reset_expires` to the `users` table
   - Add `email_verified` boolean field to the `users` table
   - Create `user_roles` table with many-to-many relationship to `users`
   - Create `role_permissions` table to define granular permissions

2. **Campaign Management Enhancements**
   - Add `start_date` and `end_date` to the `campaigns` table
   - Create `campaign_users` table for campaign assignments
   - Add `archived` boolean field to the `campaigns` table

3. **Test Case Management Enhancements**
   - Add `priority` and `complexity` fields to the `test_cases` table
   - Create `test_case_history` table to track changes
   - Add `prerequisites` text field to the `test_cases` table

4. **Assessment Tracking Enhancements**
   - Create `test_executions` table to track individual test runs
   - Create `test_evidence` table for attachments
   - Create `assessment_summaries` table for campaign-level findings
   - Create `assessment_reports` table to track generated reports

### 4.2 API Endpoints

The following API endpoints need to be created or enhanced:

1. **User Management**
   - `POST /api/v1/auth/register` - Register a new user
   - `POST /api/v1/auth/login` - Authenticate a user
   - `POST /api/v1/auth/logout` - Log out a user
   - `POST /api/v1/auth/reset-password` - Request password reset
   - `PUT /api/v1/auth/reset-password/{token}` - Reset password with token
   - `GET /api/v1/users` - List users (admin only)
   - `GET /api/v1/users/{id}` - Get user details
   - `PUT /api/v1/users/{id}` - Update user details
   - `DELETE /api/v1/users/{id}` - Delete a user (admin only)
   - `PUT /api/v1/users/{id}/roles` - Assign roles to a user

2. **Campaign Management**
   - `GET /api/v1/campaigns` - List campaigns
   - `POST /api/v1/campaigns` - Create a campaign
   - `GET /api/v1/campaigns/{id}` - Get campaign details
   - `PUT /api/v1/campaigns/{id}` - Update campaign details
   - `DELETE /api/v1/campaigns/{id}` - Delete a campaign
   - `GET /api/v1/campaigns/{id}/test-cases` - List test cases in a campaign
   - `POST /api/v1/campaigns/{id}/users` - Assign users to a campaign
   - `DELETE /api/v1/campaigns/{id}/users/{user_id}` - Remove user from a campaign

3. **Test Case Management**
   - `GET /api/v1/test-cases` - List test cases
   - `POST /api/v1/test-cases` - Create a test case
   - `GET /api/v1/test-cases/{id}` - Get test case details
   - `PUT /api/v1/test-cases/{id}` - Update test case details
   - `DELETE /api/v1/test-cases/{id}` - Delete a test case
   - `GET /api/v1/test-cases/{id}/history` - Get test case history
   - `GET /api/v1/test-cases/{id}/executions` - List test executions

4. **Assessment Tracking**
   - `POST /api/v1/test-cases/{id}/executions` - Record test execution
   - `GET /api/v1/test-executions/{id}` - Get test execution details
   - `PUT /api/v1/test-executions/{id}` - Update test execution
   - `POST /api/v1/test-executions/{id}/evidence` - Add evidence to test execution
   - `GET /api/v1/campaigns/{id}/summary` - Get campaign assessment summary
   - `POST /api/v1/campaigns/{id}/summary` - Create campaign assessment summary
   - `PUT /api/v1/campaigns/{id}/summary` - Update campaign assessment summary
   - `POST /api/v1/campaigns/{id}/reports` - Generate assessment report
   - `GET /api/v1/reports/{id}` - Download assessment report

### 4.3 Integration Points

The Core CRUD Operations feature integrates with the following components:

1. **Authentication System**
   - JWT token generation and validation
   - Password hashing and verification
   - Session management

2. **Email Service**
   - User registration confirmation
   - Password reset notifications
   - Assessment report sharing

3. **File Storage System**
   - Test evidence storage
   - Report generation and storage

4. **Audit Logging System**
   - User activity logging
   - Security event logging
   - Data change tracking

5. **Future Features**
   - MITRE Navigator integration for mapping test outcomes to TTPs
   - Enhanced Testcase Chaining for complex test scenarios
   - Comprehensive Tagging System for organizing entities

### 4.4 Performance Requirements

1. **Response Time**
   - API endpoints should respond within 200ms for simple operations
   - Complex operations (report generation, bulk operations) should complete within 5 seconds
   - Background tasks should be used for operations that may take longer

2. **Scalability**
   - System should support up to 1000 concurrent users
   - Database should efficiently handle up to 10,000 campaigns
   - Test case storage should scale to millions of records

3. **Availability**
   - Core CRUD operations should have 99.9% uptime
   - Scheduled maintenance should not impact data integrity

## 5. UI/UX Requirements
### 5.1 User Interface Components

1. **User Management**
   - User registration form
   - Login form with password reset option
   - User profile page with editable fields
   - User management dashboard for administrators
   - Role assignment interface

2. **Campaign Management**
   - Campaign creation form
   - Campaign details page with editable fields
   - Campaign list view with filtering and sorting
   - User assignment interface for campaigns
   - Campaign dashboard showing progress and metrics

3. **Test Case Management**
   - Test case creation form with step builder
   - Test case details page with editable fields
   - Test case list view with filtering and sorting
   - Test case history view
   - Test execution interface

4. **Assessment Tracking**
   - Test execution recording form
   - Evidence upload interface
   - Test result details page
   - Assessment summary creation interface
   - Report generation interface with template selection

### 5.2 User Flows

1. **User Registration and Authentication**
   - User visits registration page
   - User completes registration form
   - User receives verification email
   - User verifies email and is directed to login
   - User logs in and is directed to dashboard

2. **Campaign Management**
   - Manager creates a new campaign
   - Manager assigns testers to the campaign
   - Manager monitors campaign progress
   - Manager updates campaign status as needed
   - Manager generates assessment report when complete

3. **Test Case Management**
   - Tester creates a new test case
   - Tester assigns test case to a campaign
   - Tester updates test case as needed
   - Tester executes test case and records results
   - Tester attaches evidence to test results

4. **Assessment Reporting**
   - Manager reviews test results
   - Manager creates assessment summary
   - Manager generates assessment report
   - Manager shares report with stakeholders
   - Manager archives campaign when complete

### 5.3 Mockups/Wireframes

Mockups will be created for the following key interfaces:
- User registration and login forms
- User profile and management pages
- Campaign creation and management interfaces
- Test case creation and execution interfaces
- Assessment summary and reporting interfaces

## 6. Testing Requirements
### 6.1 Unit Testing

1. **User Management**
   - Test user creation, update, and deletion
   - Test password hashing and verification
   - Test role assignment and permission checking
   - Test password reset functionality

2. **Campaign Management**
   - Test campaign creation, update, and deletion
   - Test user assignment to campaigns
   - Test campaign status changes
   - Test campaign filtering and sorting

3. **Test Case Management**
   - Test test case creation, update, and deletion
   - Test test case assignment to campaigns
   - Test test case history tracking
   - Test test case filtering and sorting

4. **Assessment Tracking**
   - Test test execution recording
   - Test evidence attachment
   - Test assessment summary creation
   - Test report generation

### 6.2 Integration Testing

1. **Authentication Flow**
   - Test complete registration, verification, and login flow
   - Test password reset flow
   - Test session management and expiration
   - Test API authentication with JWT

2. **Campaign Workflow**
   - Test campaign creation with user assignment
   - Test adding test cases to campaigns
   - Test executing test cases within campaigns
   - Test generating reports from campaign results

3. **API Integration**
   - Test API endpoints for all CRUD operations
   - Test API authentication and authorization
   - Test API rate limiting
   - Test API error handling

### 6.3 User Acceptance Testing

1. **User Management**
   - Verify user registration process is intuitive
   - Verify password reset process works as expected
   - Verify user profile management is user-friendly
   - Verify role-based access control works correctly

2. **Campaign Management**
   - Verify campaign creation process is straightforward
   - Verify campaign management interfaces are intuitive
   - Verify campaign progress tracking is accurate
   - Verify campaign reporting provides valuable insights

3. **Test Case Management**
   - Verify test case creation process is efficient
   - Verify test case execution interface is user-friendly
   - Verify test result recording is straightforward
   - Verify evidence attachment works as expected

## 7. Implementation Plan
### 7.1 Phase 1: API Implementation

1. **User Management API (Week 1)**
   - Implement user registration and authentication endpoints
   - Implement password reset functionality
   - Implement user profile management endpoints
   - Implement role-based access control

2. **Campaign Management API (Week 1)**
   - Implement campaign CRUD endpoints
   - Implement user assignment endpoints
   - Implement campaign filtering and sorting

3. **Test Case Management API (Week 2)**
   - Implement test case CRUD endpoints
   - Implement test case history tracking
   - Implement test case filtering and sorting

4. **Assessment Tracking API (Week 2)**
   - Implement test execution recording endpoints
   - Implement evidence attachment endpoints
   - Implement assessment summary endpoints
   - Implement report generation endpoints

### 7.2 Phase 2: API Testing

1. **Unit Tests (Week 3)**
   - Write unit tests for all models
   - Write unit tests for service layer functions
   - Ensure at least 80% code coverage

2. **Integration Tests (Week 3)**
   - Write integration tests for API endpoints
   - Test authentication and authorization
   - Test error handling and edge cases

### 7.3 Phase 3: UI Implementation

1. **User Management UI (Week 4)**
   - Implement registration and login forms
   - Implement user profile page
   - Implement user management dashboard

2. **Campaign Management UI (Week 4)**
   - Implement campaign creation and editing forms
   - Implement campaign list and details pages
   - Implement user assignment interface

3. **Test Case Management UI (Week 5)**
   - Implement test case creation and editing forms
   - Implement test case list and details pages
   - Implement test execution interface

4. **Assessment Tracking UI (Week 5)**
   - Implement test result recording interface
   - Implement evidence upload interface
   - Implement assessment summary interface
   - Implement report generation interface

### 7.4 Phase 4: UI Testing

1. **Component Tests (Week 6)**
   - Write tests for UI components
   - Test form validation
   - Test state management

2. **End-to-End Tests (Week 6)**
   - Write tests for complete user flows
   - Test cross-browser compatibility
   - Test responsive design

## 8. Timeline and Dependencies

### Timeline

1. **Week 1: Database Schema and User/Campaign API**
   - Database schema changes
   - User management API
   - Campaign management API

2. **Week 2: Test Case and Assessment API**
   - Test case management API
   - Assessment tracking API
   - API documentation

3. **Week 3: API Testing**
   - Unit tests
   - Integration tests
   - Performance testing

4. **Week 4: User and Campaign UI**
   - User management UI
   - Campaign management UI
   - Initial UI testing

5. **Week 5: Test Case and Assessment UI**
   - Test case management UI
   - Assessment tracking UI
   - Report generation UI

6. **Week 6: UI Testing and Finalization**
   - Component tests
   - End-to-end tests
   - Final adjustments and bug fixes

### Dependencies

1. **External Dependencies**
   - JWT authentication library
   - Email service integration
   - File storage system
   - Report generation library

2. **Internal Dependencies**
   - Database schema changes must be completed before API implementation
   - API implementation must be completed before UI implementation
   - API testing must be completed before UI testing
   - Core CRUD operations must be completed before MITRE Navigator integration

## 9. Success Metrics

1. **Functionality Metrics**
   - 100% of specified API endpoints implemented and tested
   - 100% of specified UI components implemented and tested
   - All user stories implemented with acceptance criteria met

2. **Performance Metrics**
   - API response times within specified limits
   - UI interactions responsive (< 100ms)
   - Report generation within specified time limits

3. **Quality Metrics**
   - Code coverage > 80% for backend code
   - Zero critical or high-severity bugs
   - All automated tests passing

4. **User Experience Metrics**
   - User satisfaction score > 4/5 in initial feedback
   - Task completion time reduced compared to manual processes
   - Reduction in support requests related to core functionality

## 10. Open Questions

1. **Authentication Strategy**
   - Should we implement multi-factor authentication in this phase?
   - What should be the JWT token expiration policy?
   - Should we support OAuth integration with third-party providers?

2. **Data Retention**
   - What should be the policy for archived campaigns and test cases?
   - Should we implement automatic archiving based on age?
   - How should we handle test evidence storage limits?

3. **Reporting Capabilities**
   - What report formats should be supported (beyond PDF and HTML)?
   - Should we implement scheduled report generation?
   - What level of customization should be supported for report templates?

4. **Integration Priorities**
   - Which integration points should be prioritized for the initial release?
   - Should we implement API webhooks for integration with external systems?
   - How should we handle backward compatibility with existing integrations? 