# Enhanced Testcase Chaining & Sequencing - Feature Plan

## Overview

The Enhanced Testcase Chaining & Sequencing feature will introduce the ability to create, manage, and execute sequences of testcases that represent complete attack chains or scenarios. This functionality is crucial for simulating complex attack patterns such as ransomware attacks, persistent threats, or multi-stage exploits. By enabling security professionals to define relationships between testcases and enforce execution order, we will provide a more realistic and comprehensive security testing experience that better represents real-world attack scenarios.

## Feature Description

The Enhanced Testcase Chaining & Sequencing feature will include the following key capabilities:

1. **Testcase Dependency Management**
   - Define predecessor/successor relationships between testcases
   - Specify required testcases that must complete before others can start
   - Support multiple dependency types (hard dependencies, soft dependencies)
   - Create complex dependency graphs with branches and convergence points

2. **Execution Order Enforcement**
   - Ensure testcases execute in the correct sequence
   - Handle success/failure paths differently
   - Implement timeout and retry mechanisms
   - Support parallel execution of independent testcases

3. **Flow Control and Conditionals**
   - Define conditions for testcase execution based on previous results
   - Support if-then-else logic in testcase chains
   - Implement loop constructs for repeated testing
   - Create decision points where execution paths can diverge

4. **Precondition/Postcondition Validation**
   - Specify required system state before testcase execution
   - Validate system state after testcase completion
   - Automatically create cleanup steps to restore system state
   - Detect and handle state conflicts between testcases

5. **Testcase Chain Visualization**
   - Create interactive directed graphs of testcase relationships
   - Visualize execution flow and current state during runs
   - Highlight successful/failed paths
   - Support zooming and filtering of complex graphs

## Implementation Plan

### Phase 1: Core Data Model & API Implementation (1 week)

#### Database Schema Design

1. **TestcaseChain Model**
   ```python
   class TestcaseChainDB(Base, VersionMixin):
       """Database model for testcase chains."""
       __tablename__ = "testcase_chains"

       id: Mapped[int] = mapped_column(primary_key=True, index=True)
       name: Mapped[str] = mapped_column(nullable=False)
       description: Mapped[Optional[str]]
       created_by: Mapped[int] = mapped_column(ForeignKey("flask_users.id"))
       status: Mapped[str] = mapped_column(server_default="draft")  # draft, active, completed, archived

       # Relationships
       creator: Mapped["User"] = relationship(foreign_keys=[created_by])
       chain_nodes: Mapped[List["TestcaseChainNodeDB"]] = relationship(
           back_populates="chain",
           cascade="all, delete-orphan"
       )
       executions: Mapped[List["ChainExecutionDB"]] = relationship(
           back_populates="chain",
           cascade="all, delete-orphan"
       )
   ```

2. **TestcaseChainNode Model**
   ```python
   class TestcaseChainNodeDB(Base, VersionMixin):
       """Database model for nodes in a testcase chain."""
       __tablename__ = "testcase_chain_nodes"

       id: Mapped[int] = mapped_column(primary_key=True, index=True)
       chain_id: Mapped[int] = mapped_column(ForeignKey("testcase_chains.id", ondelete="CASCADE"))
       testcase_id: Mapped[int] = mapped_column(ForeignKey("test_cases.id", ondelete="CASCADE"))
       node_type: Mapped[str] = mapped_column(server_default="standard")  # standard, conditional, start, end
       position_x: Mapped[float] = mapped_column(default=0)  # For UI positioning
       position_y: Mapped[float] = mapped_column(default=0)  # For UI positioning
       execution_order: Mapped[int] = mapped_column(default=0)
       condition_expression: Mapped[Optional[str]]  # For conditional nodes
       
       # Relationships
       chain: Mapped["TestcaseChainDB"] = relationship(back_populates="chain_nodes")
       testcase: Mapped["TestCaseDB"] = relationship()
       outgoing_edges: Mapped[List["TestcaseChainEdgeDB"]] = relationship(
           foreign_keys="[TestcaseChainEdgeDB.source_node_id]",
           back_populates="source_node",
           cascade="all, delete-orphan"
       )
       incoming_edges: Mapped[List["TestcaseChainEdgeDB"]] = relationship(
           foreign_keys="[TestcaseChainEdgeDB.target_node_id]",
           back_populates="target_node",
           cascade="all, delete-orphan"
       )
   ```

3. **TestcaseChainEdge Model**
   ```python
   class TestcaseChainEdgeDB(Base, VersionMixin):
       """Database model for edges connecting testcase chain nodes."""
       __tablename__ = "testcase_chain_edges"

       id: Mapped[int] = mapped_column(primary_key=True, index=True)
       source_node_id: Mapped[int] = mapped_column(ForeignKey("testcase_chain_nodes.id", ondelete="CASCADE"))
       target_node_id: Mapped[int] = mapped_column(ForeignKey("testcase_chain_nodes.id", ondelete="CASCADE"))
       edge_type: Mapped[str] = mapped_column(server_default="standard")  # standard, success_path, failure_path
       condition: Mapped[Optional[str]]  # Condition for taking this edge
       
       # Relationships
       source_node: Mapped["TestcaseChainNodeDB"] = relationship(
           foreign_keys=[source_node_id],
           back_populates="outgoing_edges"
       )
       target_node: Mapped["TestcaseChainNodeDB"] = relationship(
           foreign_keys=[target_node_id],
           back_populates="incoming_edges"
       )
   ```

4. **ChainExecution Model**
   ```python
   class ChainExecutionDB(Base, VersionMixin):
       """Database model for testcase chain executions."""
       __tablename__ = "chain_executions"

       id: Mapped[int] = mapped_column(primary_key=True, index=True)
       chain_id: Mapped[int] = mapped_column(ForeignKey("testcase_chains.id", ondelete="CASCADE"))
       started_by: Mapped[int] = mapped_column(ForeignKey("flask_users.id"))
       start_time: Mapped[datetime] = mapped_column(default=func.now())
       end_time: Mapped[Optional[datetime]]
       status: Mapped[str] = mapped_column(server_default="running")  # running, completed, failed, aborted
       
       # Relationships
       chain: Mapped["TestcaseChainDB"] = relationship(back_populates="executions")
       starter: Mapped["User"] = relationship(foreign_keys=[started_by])
       node_executions: Mapped[List["NodeExecutionDB"]] = relationship(
           back_populates="chain_execution",
           cascade="all, delete-orphan"
       )
   ```

5. **NodeExecution Model**
   ```python
   class NodeExecutionDB(Base, VersionMixin):
       """Database model for testcase chain node executions."""
       __tablename__ = "node_executions"

       id: Mapped[int] = mapped_column(primary_key=True, index=True)
       chain_execution_id: Mapped[int] = mapped_column(ForeignKey("chain_executions.id", ondelete="CASCADE"))
       node_id: Mapped[int] = mapped_column(ForeignKey("testcase_chain_nodes.id", ondelete="CASCADE"))
       start_time: Mapped[Optional[datetime]]
       end_time: Mapped[Optional[datetime]]
       status: Mapped[str] = mapped_column(server_default="pending")  # pending, running, completed, failed, skipped
       result_data: Mapped[Optional[dict]] = mapped_column(type_=JSON)
       
       # Relationships
       chain_execution: Mapped["ChainExecutionDB"] = relationship(back_populates="node_executions")
       node: Mapped["TestcaseChainNodeDB"] = relationship()
   ```

6. **Pre/Post Condition Models**
   ```python
   class TestcaseConditionDB(Base, VersionMixin):
       """Database model for testcase preconditions and postconditions."""
       __tablename__ = "testcase_conditions"

       id: Mapped[int] = mapped_column(primary_key=True, index=True)
       testcase_id: Mapped[int] = mapped_column(ForeignKey("test_cases.id", ondelete="CASCADE"))
       condition_type: Mapped[str] = mapped_column(nullable=False)  # precondition, postcondition
       name: Mapped[str] = mapped_column(nullable=False)
       description: Mapped[Optional[str]]
       validation_script: Mapped[Optional[str]]  # Script to validate the condition
       required: Mapped[bool] = mapped_column(default=True)  # Is this condition required for execution?
       
       # Relationships
       testcase: Mapped["TestCaseDB"] = relationship(back_populates="conditions")
   ```

#### API Design

1. **Testcase Chain Management Endpoints**
   - `POST /api/v1/testcase-chains`: Create a new testcase chain
   - `GET /api/v1/testcase-chains`: List all testcase chains
   - `GET /api/v1/testcase-chains/{chain_id}`: Get a specific testcase chain
   - `PUT /api/v1/testcase-chains/{chain_id}`: Update a testcase chain
   - `DELETE /api/v1/testcase-chains/{chain_id}`: Delete a testcase chain

2. **Chain Node Management Endpoints**
   - `POST /api/v1/testcase-chains/{chain_id}/nodes`: Add a node to a chain
   - `GET /api/v1/testcase-chains/{chain_id}/nodes`: List all nodes in a chain
   - `PUT /api/v1/testcase-chains/{chain_id}/nodes/{node_id}`: Update a node
   - `DELETE /api/v1/testcase-chains/{chain_id}/nodes/{node_id}`: Remove a node from a chain

3. **Chain Edge Management Endpoints**
   - `POST /api/v1/testcase-chains/{chain_id}/edges`: Create an edge between nodes
   - `GET /api/v1/testcase-chains/{chain_id}/edges`: List all edges in a chain
   - `PUT /api/v1/testcase-chains/{chain_id}/edges/{edge_id}`: Update an edge
   - `DELETE /api/v1/testcase-chains/{chain_id}/edges/{edge_id}`: Delete an edge

4. **Chain Execution Endpoints**
   - `POST /api/v1/testcase-chains/{chain_id}/execute`: Execute a testcase chain
   - `GET /api/v1/testcase-chains/{chain_id}/executions`: List executions of a chain
   - `GET /api/v1/testcase-chains/{chain_id}/executions/{execution_id}`: Get execution details
   - `POST /api/v1/testcase-chains/{chain_id}/executions/{execution_id}/abort`: Abort a running execution

5. **Condition Management Endpoints**
   - `POST /api/v1/test-cases/{testcase_id}/conditions`: Add a condition to a testcase
   - `GET /api/v1/test-cases/{testcase_id}/conditions`: List conditions for a testcase
   - `PUT /api/v1/test-cases/{testcase_id}/conditions/{condition_id}`: Update a condition
   - `DELETE /api/v1/test-cases/{testcase_id}/conditions/{condition_id}`: Delete a condition

### Phase 2: Chain Execution Engine Implementation (1 week)

1. **Execution Sequencer Service**
   ```python
   class ChainExecutionService:
       """Service for executing testcase chains."""
       
       @staticmethod
       def start_execution(chain_id, user_id):
           """Start executing a testcase chain."""
           # Create execution record
           # Initialize node executions
           # Identify start nodes and queue for execution
           # Return execution ID
           
       @staticmethod
       def process_execution(execution_id):
           """Process a running chain execution."""
           # Get current execution state
           # Process pending nodes if their dependencies are satisfied
           # Update execution status
           # Return current status
           
       @staticmethod
       def process_node_execution(node_execution_id):
           """Execute a specific node in the chain."""
           # Check preconditions
           # Execute the testcase
           # Check postconditions
           # Determine next nodes based on result
           # Return execution result
           
       @staticmethod
       def abort_execution(execution_id, user_id):
           """Abort a running chain execution."""
           # Mark execution as aborted
           # Stop any running nodes
           # Clean up resources
           # Return abort status
   ```

2. **Background Jobs**
   ```python
   def chain_execution_job():
       """Background job to process chain executions."""
       # Get all running chain executions
       # Process each execution
       # Update status
       
   def node_cleanup_job():
       """Background job to clean up after node executions."""
       # Get completed node executions
       # Run cleanup steps
       # Update status
   ```

3. **Condition Validator Service**
   ```python
   class ConditionValidatorService:
       """Service for validating testcase conditions."""
       
       @staticmethod
       def validate_preconditions(testcase_id, context=None):
           """Validate preconditions for a testcase."""
           # Get all preconditions
           # Run validation scripts
           # Return results
           
       @staticmethod
       def validate_postconditions(testcase_id, context=None):
           """Validate postconditions for a testcase."""
           # Get all postconditions
           # Run validation scripts
           # Return results
   ```

### Phase 3: Frontend Implementation (1 week)

1. **Chain Designer UI**
   - Canvas for visually creating and editing testcase chains
   - Node palette with testcase library
   - Properties panel for configuring nodes and edges
   - Validation and error highlighting
   - Save, load, and export functionality

2. **Chain Visualization UI**
   - Interactive graph visualization of testcase chains
   - Status indicators for nodes and edges
   - Zoom, pan, and focus controls
   - Export to PNG/SVG options
   - Filtering and highlighting options

3. **Chain Execution UI**
   - Execution control panel
   - Real-time execution status visualization
   - Execution history and details view
   - Result analysis and reporting
   - Error and warning display

4. **Condition Management UI**
   - UI for defining and editing testcase conditions
   - Condition validation testing
   - Condition library and reuse functionality
   - Integration with testcase editor

### Phase 4: Testing and Documentation (1 week)

1. **Testing Plan**
   - Unit tests for all services and models
   - Integration tests for API endpoints
   - End-to-end tests for complete workflows
   - Stress tests for the execution engine
   - UI component tests

2. **Documentation**
   - API documentation for all endpoints
   - User guide for creating and executing chains
   - Admin guide for managing the system
   - Developer documentation for extending the system
   - Examples and best practices

## Implementation Details

### Service Implementation

1. **Chain Management Service**
   ```python
   class TestcaseChainService:
       """Service for managing testcase chains."""
       
       @staticmethod
       def create_chain(name, description, user_id):
           """Create a new testcase chain."""
           chain = TestcaseChainDB(
               name=name,
               description=description,
               created_by=user_id,
               status="draft"
           )
           db.session.add(chain)
           db.session.commit()
           return chain
       
       @staticmethod
       def add_node(chain_id, testcase_id, node_type="standard", position_x=0, position_y=0):
           """Add a node to a testcase chain."""
           node = TestcaseChainNodeDB(
               chain_id=chain_id,
               testcase_id=testcase_id,
               node_type=node_type,
               position_x=position_x,
               position_y=position_y
           )
           db.session.add(node)
           db.session.commit()
           return node
       
       @staticmethod
       def connect_nodes(source_node_id, target_node_id, edge_type="standard", condition=None):
           """Connect two nodes in a testcase chain."""
           edge = TestcaseChainEdgeDB(
               source_node_id=source_node_id,
               target_node_id=target_node_id,
               edge_type=edge_type,
               condition=condition
           )
           db.session.add(edge)
           db.session.commit()
           return edge
       
       @staticmethod
       def validate_chain(chain_id):
           """Validate a testcase chain for execution."""
           # Check for start and end nodes
           # Verify no cycles
           # Ensure all nodes are reachable
           # Validate conditions
           # Return validation results
   ```

2. **Chain Execution Service**
   ```python
   class ExecutionService:
       """Service for executing testcase chains."""
       
       @staticmethod
       def create_execution(chain_id, user_id):
           """Create a new execution of a testcase chain."""
           # Create execution record
           execution = ChainExecutionDB(
               chain_id=chain_id,
               started_by=user_id,
               status="running"
           )
           db.session.add(execution)
           
           # Create node execution records
           chain = db.session.query(TestcaseChainDB).get(chain_id)
           for node in chain.chain_nodes:
               node_execution = NodeExecutionDB(
                   chain_execution_id=execution.id,
                   node_id=node.id,
                   status="pending"
               )
               db.session.add(node_execution)
           
           db.session.commit()
           
           # Start background job to process execution
           enqueue_execution_job(execution.id)
           
           return execution
       
       @staticmethod
       def process_execution(execution_id):
           """Process a chain execution."""
           execution = db.session.query(ChainExecutionDB).get(execution_id)
           
           # Find all pending nodes with no pending dependencies
           ready_nodes = find_ready_nodes(execution_id)
           
           for node_execution in ready_nodes:
               # Start node execution
               node_execution.status = "running"
               node_execution.start_time = datetime.utcnow()
               db.session.commit()
               
               # Execute testcase
               result = execute_testcase(node_execution.node.testcase_id)
               
               # Update node execution
               node_execution.status = "completed" if result.success else "failed"
               node_execution.end_time = datetime.utcnow()
               node_execution.result_data = result.data
               db.session.commit()
               
               # Process outgoing edges based on result
               process_outgoing_edges(node_execution)
           
           # Check if execution is complete
           pending_nodes = db.session.query(NodeExecutionDB).filter(
               NodeExecutionDB.chain_execution_id == execution_id,
               NodeExecutionDB.status.in_(["pending", "running"])
           ).count()
           
           if pending_nodes == 0:
               execution.status = "completed"
               execution.end_time = datetime.utcnow()
               db.session.commit()
           
           return execution
   ```

### API Controller Implementation

1. **Chain Management Controller**
   ```python
   @router.post("/testcase-chains", response_model=TestcaseChain)
   def create_testcase_chain(chain: TestcaseChainCreate, current_user=Depends(get_current_user)):
       """Create a new testcase chain."""
       db_chain = TestcaseChainService.create_chain(
           name=chain.name,
           description=chain.description,
           user_id=current_user.id
       )
       return db_chain
   
   @router.get("/testcase-chains", response_model=List[TestcaseChain])
   def list_testcase_chains(
       skip: int = 0,
       limit: int = 100,
       current_user=Depends(get_current_user)
   ):
       """List all testcase chains."""
       chains = db.session.query(TestcaseChainDB).offset(skip).limit(limit).all()
       return chains
   
   @router.get("/testcase-chains/{chain_id}", response_model=TestcaseChainDetail)
   def get_testcase_chain(chain_id: int, current_user=Depends(get_current_user)):
       """Get details of a specific testcase chain."""
       chain = db.session.query(TestcaseChainDB).get(chain_id)
       if not chain:
           raise HTTPException(status_code=404, detail="Testcase chain not found")
       return chain
   
   @router.put("/testcase-chains/{chain_id}", response_model=TestcaseChain)
   def update_testcase_chain(
       chain_id: int,
       chain_update: TestcaseChainUpdate,
       current_user=Depends(get_current_user)
   ):
       """Update a testcase chain."""
       db_chain = db.session.query(TestcaseChainDB).get(chain_id)
       if not db_chain:
           raise HTTPException(status_code=404, detail="Testcase chain not found")
       
       # Update fields
       for field, value in chain_update.dict(exclude_unset=True).items():
           setattr(db_chain, field, value)
       
       db.session.commit()
       return db_chain
   ```

2. **Chain Execution Controller**
   ```python
   @router.post("/testcase-chains/{chain_id}/execute", response_model=ChainExecution)
   def execute_testcase_chain(chain_id: int, current_user=Depends(get_current_user)):
       """Execute a testcase chain."""
       # Check if chain exists
       chain = db.session.query(TestcaseChainDB).get(chain_id)
       if not chain:
           raise HTTPException(status_code=404, detail="Testcase chain not found")
       
       # Validate chain
       validation = TestcaseChainService.validate_chain(chain_id)
       if not validation.valid:
           raise HTTPException(
               status_code=400,
               detail=f"Chain validation failed: {validation.error}"
           )
       
       # Create and start execution
       execution = ExecutionService.create_execution(chain_id, current_user.id)
       
       return execution
   
   @router.get("/testcase-chains/{chain_id}/executions", response_model=List[ChainExecution])
   def list_chain_executions(
       chain_id: int,
       skip: int = 0,
       limit: int = 100,
       current_user=Depends(get_current_user)
   ):
       """List all executions of a testcase chain."""
       executions = db.session.query(ChainExecutionDB).filter(
           ChainExecutionDB.chain_id == chain_id
       ).offset(skip).limit(limit).all()
       
       return executions
   
   @router.get(
       "/testcase-chains/{chain_id}/executions/{execution_id}",
       response_model=ChainExecutionDetail
   )
   def get_chain_execution(
       chain_id: int,
       execution_id: int,
       current_user=Depends(get_current_user)
   ):
       """Get details of a specific chain execution."""
       execution = db.session.query(ChainExecutionDB).filter(
           ChainExecutionDB.chain_id == chain_id,
           ChainExecutionDB.id == execution_id
       ).first()
       
       if not execution:
           raise HTTPException(status_code=404, detail="Execution not found")
       
       return execution
   ```

## UI Design

### Chain Designer Component

The Chain Designer will be a React-based component that provides a canvas for creating and editing testcase chains. Key features include:

1. **Canvas Area**
   - Interactive workspace for arranging nodes and connections
   - Grid-based layout with snap-to-grid functionality
   - Zoom and pan controls
   - Mini-map for navigation in complex chains

2. **Node Palette**
   - Library of available testcases grouped by categories
   - Drag-and-drop interface for adding nodes to the canvas
   - Search and filter functionality
   - Quick preview of testcase details

3. **Properties Panel**
   - Edit properties of selected nodes or edges
   - Configure conditions and parameters
   - Set execution options
   - Apply visual styling

4. **Toolbar**
   - Save, load, and export options
   - Undo/redo functionality
   - Validation and verification tools
   - Layout options (auto-arrange, align, distribute)

### Chain Visualization Component

The Chain Visualization component will provide a real-time view of chain executions, with the following features:

1. **Graph Visualization**
   - Directed graph representation of the chain
   - Different shapes and colors for node types
   - Status indicators (pending, running, completed, failed)
   - Highlighting of current execution path

2. **Execution Controls**
   - Start, pause, resume, and abort buttons
   - Execution speed control
   - Step-by-step execution option
   - Jump to specific node

3. **Results Panel**
   - Real-time display of execution results
   - Log output from testcases
   - Error and warning messages
   - Summary statistics

## Testing Strategy

1. **Unit Tests**
   - Test individual services and functions
   - Mock dependencies for isolated testing
   - Ensure high coverage of business logic
   - Test edge cases and error handling

2. **Integration Tests**
   - Test API endpoints with real database interactions
   - Verify correct behavior of the execution engine
   - Test interactions between components
   - Validate transaction handling and concurrency

3. **End-to-End Tests**
   - Test complete workflows from chain creation to execution
   - Verify correct behavior of the UI components
   - Test different chain configurations and scenarios
   - Validate performance under load

4. **UI Component Tests**
   - Test React components with Jest and React Testing Library
   - Verify correct rendering and interaction
   - Test accessibility and responsiveness
   - Validate state management

## Migration Plan

### Database Migrations

1. **Create TestcaseChain Table**
   ```sql
   CREATE TABLE testcase_chains (
       id SERIAL PRIMARY KEY,
       name VARCHAR(100) NOT NULL,
       description TEXT,
       created_by INTEGER REFERENCES flask_users(id),
       status VARCHAR(20) DEFAULT 'draft',
       created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       deleted_time TIMESTAMP
   );
   
   CREATE INDEX idx_tc_created_by ON testcase_chains (created_by);
   CREATE INDEX idx_tc_status ON testcase_chains (status);
   CREATE INDEX idx_tc_deleted_time ON testcase_chains (deleted_time);
   ```

2. **Create TestcaseChainNode Table**
   ```sql
   CREATE TABLE testcase_chain_nodes (
       id SERIAL PRIMARY KEY,
       chain_id INTEGER REFERENCES testcase_chains(id) ON DELETE CASCADE,
       testcase_id INTEGER REFERENCES test_cases(id) ON DELETE CASCADE,
       node_type VARCHAR(20) DEFAULT 'standard',
       position_x FLOAT DEFAULT 0,
       position_y FLOAT DEFAULT 0,
       execution_order INTEGER DEFAULT 0,
       condition_expression TEXT,
       created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       deleted_time TIMESTAMP
   );
   
   CREATE INDEX idx_tcn_chain_id ON testcase_chain_nodes (chain_id);
   CREATE INDEX idx_tcn_testcase_id ON testcase_chain_nodes (testcase_id);
   CREATE INDEX idx_tcn_node_type ON testcase_chain_nodes (node_type);
   CREATE INDEX idx_tcn_deleted_time ON testcase_chain_nodes (deleted_time);
   ```

3. **Create TestcaseChainEdge Table**
   ```sql
   CREATE TABLE testcase_chain_edges (
       id SERIAL PRIMARY KEY,
       source_node_id INTEGER REFERENCES testcase_chain_nodes(id) ON DELETE CASCADE,
       target_node_id INTEGER REFERENCES testcase_chain_nodes(id) ON DELETE CASCADE,
       edge_type VARCHAR(20) DEFAULT 'standard',
       condition TEXT,
       created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       deleted_time TIMESTAMP
   );
   
   CREATE INDEX idx_tce_source_node_id ON testcase_chain_edges (source_node_id);
   CREATE INDEX idx_tce_target_node_id ON testcase_chain_edges (target_node_id);
   CREATE INDEX idx_tce_edge_type ON testcase_chain_edges (edge_type);
   CREATE INDEX idx_tce_deleted_time ON testcase_chain_edges (deleted_time);
   ```

4. **Create ChainExecution Table**
   ```sql
   CREATE TABLE chain_executions (
       id SERIAL PRIMARY KEY,
       chain_id INTEGER REFERENCES testcase_chains(id) ON DELETE CASCADE,
       started_by INTEGER REFERENCES flask_users(id),
       start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       end_time TIMESTAMP,
       status VARCHAR(20) DEFAULT 'running',
       created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       deleted_time TIMESTAMP
   );
   
   CREATE INDEX idx_ce_chain_id ON chain_executions (chain_id);
   CREATE INDEX idx_ce_started_by ON chain_executions (started_by);
   CREATE INDEX idx_ce_status ON chain_executions (status);
   CREATE INDEX idx_ce_start_time ON chain_executions (start_time);
   CREATE INDEX idx_ce_deleted_time ON chain_executions (deleted_time);
   ```

5. **Create NodeExecution Table**
   ```sql
   CREATE TABLE node_executions (
       id SERIAL PRIMARY KEY,
       chain_execution_id INTEGER REFERENCES chain_executions(id) ON DELETE CASCADE,
       node_id INTEGER REFERENCES testcase_chain_nodes(id) ON DELETE CASCADE,
       start_time TIMESTAMP,
       end_time TIMESTAMP,
       status VARCHAR(20) DEFAULT 'pending',
       result_data JSONB,
       created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       deleted_time TIMESTAMP
   );
   
   CREATE INDEX idx_ne_chain_execution_id ON node_executions (chain_execution_id);
   CREATE INDEX idx_ne_node_id ON node_executions (node_id);
   CREATE INDEX idx_ne_status ON node_executions (status);
   CREATE INDEX idx_ne_deleted_time ON node_executions (deleted_time);
   ```

6. **Create TestcaseCondition Table**
   ```sql
   CREATE TABLE testcase_conditions (
       id SERIAL PRIMARY KEY,
       testcase_id INTEGER REFERENCES test_cases(id) ON DELETE CASCADE,
       condition_type VARCHAR(20) NOT NULL,
       name VARCHAR(100) NOT NULL,
       description TEXT,
       validation_script TEXT,
       required BOOLEAN DEFAULT TRUE,
       created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       deleted_time TIMESTAMP
   );
   
   CREATE INDEX idx_tc_testcase_id ON testcase_conditions (testcase_id);
   CREATE INDEX idx_tc_condition_type ON testcase_conditions (condition_type);
   CREATE INDEX idx_tc_deleted_time ON testcase_conditions (deleted_time);
   ```

### Code Deployment

1. **Backend Deployment**
   - Deploy database migrations
   - Deploy new API endpoints
   - Configure background jobs
   - Update dependencies if needed

2. **Frontend Deployment**
   - Deploy new React components
   - Update navigation and menus
   - Deploy new assets (icons, styles)
   - Update client-side dependencies

### Data Migration

No data migration is needed for this feature as it introduces new functionality without modifying existing data structures.

## Expected Outcomes

1. **Enhanced Testing Capabilities**
   - Ability to create and execute complex attack chains
   - More realistic testing scenarios
   - Better coverage of multi-step attacks
   - Improved understanding of attack paths

2. **Improved Visualization**
   - Clear visualization of attack chains
   - Better communication of test results
   - Enhanced understanding of attack techniques
   - Visual identification of critical paths

3. **Automation Benefits**
   - Automated execution of complex test scenarios
   - Consistent and repeatable testing
   - Reduced manual effort
   - Faster testing cycles

## Conclusion

The Enhanced Testcase Chaining & Sequencing feature will significantly improve the testing capabilities of the Regression Rigor platform by enabling the creation and execution of complex attack chains. This functionality is crucial for simulating real-world attack scenarios and providing a more comprehensive security testing framework. By implementing this feature, we will take a significant step toward realizing our vision of a complete security testing platform that can effectively validate security controls and identify gaps in defenses.

The implementation plan outlined in this document provides a structured approach to developing this feature over a one-month period, with clear milestones and deliverables. Upon completion, the platform will have significantly enhanced capabilities for security testing and assessment. 