# Advanced Soft-Deletion Framework - Migration Plan

## Overview

This document outlines the migration plan for implementing the Advanced Soft-Deletion Framework. The migration will involve database schema changes, code modifications, and data transformation to support the new soft-deletion capabilities.

## Database Migrations

### 1. Create New Tables

#### Migration 1: Create Soft Deletion Policy Table

```sql
CREATE TABLE soft_deletion_policies (
    id SERIAL PRIMARY KEY,
    entity_type VARCHAR(50) NOT NULL,
    retention_period_days INTEGER NOT NULL,
    auto_purge BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT uq_entity_type UNIQUE (entity_type)
);

-- <PERSON>reate indexes
CREATE INDEX idx_sdp_entity_type ON soft_deletion_policies (entity_type);
```

#### Migration 2: Create Soft Deletion Audit Table

```sql
CREATE TABLE soft_deletion_audits (
    id SERIAL PRIMARY KEY,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INTEGER NOT NULL,
    operation_type VARCHAR(20) NOT NULL,
    performed_by INTEGER REFERENCES users(id),
    reason TEXT,
    operation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB,
    CONSTRAINT chk_operation_type CHECK (operation_type IN ('soft_delete', 'recover', 'purge'))
);

-- Create indexes
CREATE INDEX idx_sda_entity ON soft_deletion_audits (entity_type, entity_id);
CREATE INDEX idx_sda_operation ON soft_deletion_audits (operation_type);
CREATE INDEX idx_sda_performed_by ON soft_deletion_audits (performed_by);
CREATE INDEX idx_sda_operation_time ON soft_deletion_audits (operation_time);
```

### 2. Modify Existing Tables

#### Migration 3: Add Soft Deletion Fields to Core Tables

We need to execute this migration for all tables that should support soft deletion. The tables include:

- testcases
- campaigns
- techniques
- procedures
- reports
- artifacts
- environments
- targets
- test_results

For each table, add the following columns:

```sql
-- For each table that supports soft deletion
ALTER TABLE [table_name]
ADD COLUMN deleted_at TIMESTAMP,
ADD COLUMN deleted_by INTEGER REFERENCES users(id),
ADD COLUMN deletion_reason TEXT;

-- Create indexes
CREATE INDEX idx_[table_name]_deleted_at ON [table_name] (deleted_at);
```

### 3. Initialize Default Policies

#### Migration 4: Insert Default Soft Deletion Policies

```sql
-- Insert default policies for each entity type
INSERT INTO soft_deletion_policies (entity_type, retention_period_days, auto_purge)
VALUES
('testcases', 90, true),
('campaigns', 180, true),
('techniques', 365, false),
('procedures', 180, true),
('reports', 365, true),
('artifacts', 30, true),
('environments', 90, false),
('targets', 90, false),
('test_results', 90, true);
```

## Code Migration

### 1. Create Base Models and Mixins

1. Create the `SoftDeletionMixin` class
2. Create the `SoftDeletionPolicy` model
3. Create the `SoftDeletionAudit` model

### 2. Update Existing Models

For each model that needs soft deletion support:

1. Add the `SoftDeletionMixin` to the model's inheritance
2. Update queries to filter out soft-deleted records by default
3. Add methods for soft deletion and recovery

### 3. Create Service Classes

1. Implement the `SoftDeletionService` class with methods for:
   - Soft deleting entities
   - Recovering entities
   - Purging entities
   - Managing retention policies

### 4. Update API Controllers

1. Modify delete endpoints to use soft deletion by default
2. Add endpoints for permanent deletion
3. Add endpoints for recovery
4. Add endpoints for policy management

### 5. Implement Background Jobs

1. Create a job for automatically purging expired records
2. Create a job for generating retention reports

## Data Migration

### 1. Handle Existing Deleted Records

For any tables that already implement a form of soft deletion:

```python
def migrate_existing_soft_deleted_records():
    """
    Migrate any records that use the old soft-deletion mechanism
    to the new framework.
    """
    # For each table with an existing 'is_deleted' flag or similar
    records = db.session.query(Model).filter(Model.is_deleted == True).all()
    
    for record in records:
        # Set the new deleted_at field if not already set
        if not record.deleted_at:
            record.deleted_at = record.updated_at or datetime.utcnow()
        
        # Create an audit record
        audit = SoftDeletionAudit(
            entity_type=record.__tablename__,
            entity_id=record.id,
            operation_type='soft_delete',
            # Use system user for migration
            performed_by=system_user_id,
            reason='Migrated from previous soft-deletion system',
            operation_time=record.deleted_at
        )
        db.session.add(audit)
    
    db.session.commit()
```

### 2. Update Queries in Application Code

Search for all queries that might need to be updated to handle soft-deleted records:

```bash
grep -r "WHERE" --include="*.py" /path/to/project
```

Update queries to include the condition `deleted_at IS NULL` where appropriate.

## UI Changes

### 1. Update List Views

1. Add an option to show/hide soft-deleted items
2. Add visual indication for soft-deleted items
3. Add recovery controls to item actions

### 2. Add Deletion Confirmation Dialogs

1. Update deletion confirmation dialogs to:
   - Explain soft-deletion concept
   - Show retention period
   - Provide option for permanent deletion (for authorized users)

### 3. Add Recovery UI

1. Create a recovery interface for soft-deleted items
2. Add batch recovery capabilities
3. Show expiration date for soft-deleted items

### 4. Add Admin Interfaces

1. Create interfaces for managing retention policies
2. Add deletion audit log viewer
3. Create a dashboard for monitoring deletion activities

## Testing Plan

### 1. Database Migration Tests

1. Test forward migrations
2. Test rollback migrations
3. Verify data integrity after migrations

### 2. Functional Tests

1. Test soft deletion
2. Test recovery
3. Test permanent deletion
4. Test cascade behavior
5. Test retention policy enforcement

### 3. Performance Tests

1. Test with large datasets
2. Benchmark query performance with soft-deletion conditions
3. Test background job performance

## Rollout Strategy

### 1. Staged Deployment

1. Deploy database changes first
2. Deploy code changes in stages:
   - Core models and services
   - API endpoints
   - Background jobs
   - UI changes

### 2. Feature Flags

1. Use feature flags to control the rollout
2. Allow gradual enabling of different components

### 3. Monitoring

1. Add specific monitoring for soft-deletion operations
2. Monitor database performance
3. Track usage of recovery functionality

## Rollback Plan

In case issues are encountered during deployment:

1. Disable the feature via feature flags
2. Roll back code changes if necessary
3. Keep database changes if possible (to avoid data loss)
4. Have scripts ready to revert to previous behavior if necessary

## Documentation

1. Update API documentation
2. Create user guides for soft deletion and recovery
3. Document the retention policy configuration
4. Update developer documentation

## Timeline

1. Database migrations: 1 day
2. Core implementation: 3 days
3. Service and API implementation: 3 days
4. Background jobs: 2 days
5. UI changes: 2 days
6. Testing: 2 days
7. Documentation: 1 day

**Total estimated time**: 14 days (2 weeks)

## Conclusion

This migration plan provides a comprehensive approach to implementing the Advanced Soft-Deletion Framework. It covers database changes, code modifications, data migration, testing, and rollout strategy. Following this plan will ensure a smooth transition to the new soft-deletion capabilities with minimal disruption to users. 