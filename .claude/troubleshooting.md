# Troubleshooting Guide

This guide provides solutions for common issues that may arise when using the Regression Rigor application.

## Docker and Deployment Issues

### Container Startup Failures

**Symptoms:**
- Docker containers fail to start
- `docker-compose up` command shows errors
- Services are not accessible

**Possible Causes and Solutions:**

1. **Port Conflicts**
   - **Cause**: The ports required by the application (8010, 3010, 5440, 9010) are already in use.
   - **Solution**: 
     - Check for processes using these ports: `sudo lsof -i :8010`
     - Stop conflicting processes or modify the port mappings in `docker-compose.yml`

2. **Database Connection Issues**
   - **Cause**: The PostgreSQL container failed to initialize properly.
   - **Solution**:
     - Check PostgreSQL logs: `docker logs regrigor-postgres`
     - Ensure the data volume is properly mounted
     - Try removing the volume and restarting: `docker-compose down -v && docker-compose up -d`

3. **Docker Resource Constraints**
   - **Cause**: Insufficient resources allocated to Dock<PERSON>.
   - **Solution**:
     - Increase Docker's memory and CPU allocation in Docker Desktop settings
     - Restart Docker daemon

4. **Network Issues**
   - **Cause**: Docker network configuration problems.
   - **Solution**:
     - Recreate the Docker network: `docker-compose down && docker network prune && docker-compose up -d`
     - Check network settings in `docker-compose.yml`

### Container Communication Problems

**Symptoms:**
- Services start but can't communicate with each other
- API returns database connection errors
- Redis connection failures

**Possible Causes and Solutions:**

1. **Container Name Resolution**
   - **Cause**: DNS resolution between containers is not working.
   - **Solution**:
     - Ensure all services are on the same Docker network
     - Try using IP addresses instead of container names for debugging
     - Restart the Docker daemon

2. **Environment Variables**
   - **Cause**: Incorrect environment variables for service connections.
   - **Solution**:
     - Verify DATABASE_URL, REDIS_URL, and other connection strings in `docker-compose.yml`
     - Ensure the host names match the service names in Docker Compose

## Authentication Issues

### Login Failures

**Symptoms:**
- Unable to log in despite correct credentials
- "Invalid username or password" errors
- Authentication timeouts

**Possible Causes and Solutions:**

1. **Database User Table Issues**
   - **Cause**: User records may be corrupted or missing.
   - **Solution**:
     - Check the user table in the database: `SELECT * FROM "user" WHERE username = 'your_username';`
     - Reset the password if necessary
     - Create a new user if the account is missing

2. **Password Hashing**
   - **Cause**: Password hashing algorithm mismatch.
   - **Solution**:
     - Verify the hashing algorithm in the code
     - Reset the password through the admin interface or directly in the database

3. **Session Management**
   - **Cause**: Session storage issues.
   - **Solution**:
     - Clear browser cookies and cache
     - Check Redis connection for session storage
     - Verify session configuration in the application

### Two-Factor Authentication Problems

**Symptoms:**
- 2FA code is not accepted
- Unable to set up 2FA
- 2FA screen doesn't appear when expected

**Possible Causes and Solutions:**

1. **Time Synchronization**
   - **Cause**: Time difference between the server and authenticator app.
   - **Solution**:
     - Ensure the server's time is synchronized: `sudo ntpdate -u pool.ntp.org`
     - Check the time on the device running the authenticator app

2. **Secret Key Issues**
   - **Cause**: Corrupted or improperly stored 2FA secret.
   - **Solution**:
     - Disable and re-enable 2FA for the user
     - Check the database for proper storage of the 2FA secret

3. **QR Code Generation**
   - **Cause**: QR code generation failures.
   - **Solution**:
     - Check for Python library dependencies for QR code generation
     - Try manual entry of the secret key instead of scanning the QR code

## API Issues

### Rate Limiting Problems

**Symptoms:**
- Receiving 429 Too Many Requests errors
- API requests are blocked unexpectedly
- Inconsistent rate limiting behavior

**Possible Causes and Solutions:**

1. **Redis Connection**
   - **Cause**: Redis connection issues affecting rate limiter functionality.
   - **Solution**:
     - Check Redis connection: `docker exec -it regrigor-redis redis-cli ping`
     - Verify Redis URL in environment variables
     - Restart the Redis container

2. **Rate Limit Configuration**
   - **Cause**: Misconfigured rate limits in the code.
   - **Solution**:
     - Review rate limit settings in `api/middleware/rate_limit.py`
     - Adjust rate limits if they're too restrictive
     - Check for IP whitelisting configuration

3. **Load Balancer or Proxy Issues**
   - **Cause**: Multiple requests appear to come from the same IP due to proxying.
   - **Solution**:
     - Ensure X-Forwarded-For headers are properly processed
     - Configure rate limiting to use user IDs instead of IPs when authenticated

### API Endpoint Errors

**Symptoms:**
- 500 Internal Server Error responses
- 404 Not Found for existing endpoints
- Unexpected API behavior

**Possible Causes and Solutions:**

1. **Code Errors**
   - **Cause**: Bugs in the API implementation.
   - **Solution**:
     - Check API logs: `docker logs regrigor-api`
     - Look for exceptions and stack traces
     - Fix the identified issues in the code

2. **Database Schema Mismatch**
   - **Cause**: API code expects a different database schema than what exists.
   - **Solution**:
     - Run database migrations: `alembic upgrade head`
     - Check for pending migrations: `alembic current`
     - Compare model definitions with actual database schema

3. **Input Validation**
   - **Cause**: Invalid input data causing API errors.
   - **Solution**:
     - Check request payload against API schema requirements
     - Add more detailed validation to the API
     - Improve error messages for validation failures

## Database Issues

### Migration Failures

**Symptoms:**
- Database migrations fail to apply
- Alembic errors during migration
- Database schema is out of sync with the code

**Possible Causes and Solutions:**

1. **Conflicting Migrations**
   - **Cause**: Multiple migration branches or conflicts.
   - **Solution**:
     - Check Alembic migration history: `alembic history`
     - Resolve conflicts by editing migration files
     - Consider resetting the migration history in development environments

2. **Database Connection Issues**
   - **Cause**: Unable to connect to the database during migration.
   - **Solution**:
     - Verify database connection settings
     - Ensure the database server is running
     - Check for network issues between the application and database

3. **Permission Issues**
   - **Cause**: Insufficient database permissions for migrations.
   - **Solution**:
     - Ensure the database user has necessary permissions
     - Check for schema ownership issues
     - Grant additional permissions if needed

### Data Integrity Problems

**Symptoms:**
- Inconsistent data in the application
- Foreign key constraint violations
- Unexpected query results

**Possible Causes and Solutions:**

1. **Missing Constraints**
   - **Cause**: Database schema lacks proper constraints.
   - **Solution**:
     - Add missing constraints through migrations
     - Validate existing data before adding constraints
     - Fix application code to respect constraints

2. **Race Conditions**
   - **Cause**: Concurrent operations causing data integrity issues.
   - **Solution**:
     - Implement proper transaction handling
     - Add row-level locking where needed
     - Use database constraints to enforce integrity

3. **Data Corruption**
   - **Cause**: Corrupted data in the database.
   - **Solution**:
     - Run database consistency checks
     - Restore from backup if necessary
     - Implement data validation and cleanup scripts

## Frontend Issues

### UI Rendering Problems

**Symptoms:**
- UI elements not displaying correctly
- JavaScript errors in the browser console
- Blank or partially loaded pages

**Possible Causes and Solutions:**

1. **Browser Compatibility**
   - **Cause**: Code using features not supported in the user's browser.
   - **Solution**:
     - Check browser console for errors
     - Add polyfills for unsupported features
     - Test in multiple browsers

2. **CSS Issues**
   - **Cause**: CSS conflicts or missing styles.
   - **Solution**:
     - Inspect elements to identify CSS problems
     - Fix specificity issues in CSS
     - Ensure all CSS files are properly loaded

3. **JavaScript Errors**
   - **Cause**: JavaScript runtime errors.
   - **Solution**:
     - Check browser console for error messages
     - Debug the JavaScript code
     - Fix identified issues

### Performance Issues

**Symptoms:**
- Slow page loading
- UI lag or unresponsiveness
- High resource usage in the browser

**Possible Causes and Solutions:**

1. **Large Payloads**
   - **Cause**: API returning too much data.
   - **Solution**:
     - Implement pagination for large data sets
     - Optimize API responses to include only necessary data
     - Add compression for API responses

2. **Inefficient Rendering**
   - **Cause**: Inefficient DOM manipulation or rendering logic.
   - **Solution**:
     - Optimize JavaScript code
     - Reduce unnecessary re-renders
     - Use browser developer tools to profile performance

3. **Resource Loading**
   - **Cause**: Slow loading of scripts, styles, or images.
   - **Solution**:
     - Optimize asset sizes
     - Implement lazy loading
     - Use a CDN for static assets

## MITRE ATT&CK Integration Issues

### Data Import Problems

**Symptoms:**
- MITRE ATT&CK data import fails
- Missing or incomplete technique data
- Relationship data not properly imported

**Possible Causes and Solutions:**

1. **Source Data Format**
   - **Cause**: Changes in the MITRE ATT&CK data format.
   - **Solution**:
     - Update the import code to match the current format
     - Check for schema changes in the MITRE ATT&CK data
     - Use a more recent version of the MITRE ATT&CK data

2. **Import Process Errors**
   - **Cause**: Bugs in the import process.
   - **Solution**:
     - Check logs for import errors
     - Debug the import process step by step
     - Fix identified issues in the import code

3. **Database Constraints**
   - **Cause**: Imported data violating database constraints.
   - **Solution**:
     - Validate data before insertion
     - Handle constraint violations gracefully
     - Fix data inconsistencies

### Visualization Issues

**Symptoms:**
- Technique relationship graph not rendering
- D3.js errors in the console
- Incomplete or incorrect visualization

**Possible Causes and Solutions:**

1. **Data Structure**
   - **Cause**: Visualization expecting a different data structure.
   - **Solution**:
     - Check the API response format
     - Ensure the data is properly formatted for D3.js
     - Update the visualization code to match the data structure

2. **Browser Compatibility**
   - **Cause**: D3.js features not supported in the browser.
   - **Solution**:
     - Check for browser compatibility issues
     - Add polyfills or fallbacks
     - Test in multiple browsers

3. **Performance Issues**
   - **Cause**: Too many nodes or edges causing performance problems.
   - **Solution**:
     - Limit the number of displayed nodes
     - Implement pagination or filtering
     - Optimize the D3.js code for better performance

## Logging and Monitoring Issues

### Missing Logs

**Symptoms:**
- Expected logs not appearing
- Incomplete log information
- Log levels not working as expected

**Possible Causes and Solutions:**

1. **Logger Configuration**
   - **Cause**: Incorrect logger configuration.
   - **Solution**:
     - Check logger configuration in the code
     - Verify log levels are set correctly
     - Ensure log handlers are properly configured

2. **Log Storage**
   - **Cause**: Logs being written to unexpected locations.
   - **Solution**:
     - Check log file paths
     - Verify permissions for log directories
     - Configure log rotation to prevent file size issues

3. **Container Logging**
   - **Cause**: Docker container logs not being captured.
   - **Solution**:
     - Use `docker logs` to view container logs
     - Configure logging drivers in Docker
     - Implement a centralized logging solution

### Monitoring Alerts

**Symptoms:**
- Missing or delayed monitoring alerts
- False positive alerts
- Alert configuration issues

**Possible Causes and Solutions:**

1. **Alert Configuration**
   - **Cause**: Incorrectly configured alert thresholds or conditions.
   - **Solution**:
     - Review alert configuration
     - Adjust thresholds based on normal usage patterns
     - Test alert triggers

2. **Monitoring Service Issues**
   - **Cause**: Problems with the monitoring service itself.
   - **Solution**:
     - Check the status of the monitoring service
     - Verify connectivity between the application and monitoring service
     - Restart the monitoring service if needed

3. **Notification Delivery**
   - **Cause**: Alerts generated but not delivered.
   - **Solution**:
     - Check notification channel configuration (email, SMS, etc.)
     - Verify recipient information
     - Test notification delivery

## Security Issues

### Authentication Bypass Attempts

**Symptoms:**
- Suspicious login attempts
- Unexpected authentication successes
- Authentication logs showing unusual patterns

**Possible Causes and Solutions:**

1. **Brute Force Attacks**
   - **Cause**: Attackers trying multiple credentials.
   - **Solution**:
     - Implement account lockout after failed attempts
     - Add CAPTCHA for login attempts
     - Use rate limiting for authentication endpoints

2. **Session Hijacking**
   - **Cause**: Attackers stealing session tokens.
   - **Solution**:
     - Use secure and HTTP-only cookies
     - Implement proper CSRF protection
     - Add IP binding for sessions

3. **Insecure Authentication Logic**
   - **Cause**: Bugs in authentication code.
   - **Solution**:
     - Audit authentication code
     - Fix identified vulnerabilities
     - Implement security testing

### Data Exposure

**Symptoms:**
- Sensitive data visible to unauthorized users
- Excessive information in API responses
- Missing access controls

**Possible Causes and Solutions:**

1. **Missing Authorization Checks**
   - **Cause**: Endpoints not properly checking user permissions.
   - **Solution**:
     - Add authorization checks to all endpoints
     - Implement role-based access control
     - Audit API endpoints for permission checks

2. **Overly Verbose Responses**
   - **Cause**: API returning more data than necessary.
   - **Solution**:
     - Filter sensitive data from responses
     - Implement data masking for sensitive fields
     - Use different response schemas for different user roles

3. **Insecure Direct Object References**
   - **Cause**: Allowing access to objects via predictable IDs without authorization.
   - **Solution**:
     - Verify ownership or permission for all object access
     - Use unpredictable IDs or tokens
     - Implement proper access control checks 