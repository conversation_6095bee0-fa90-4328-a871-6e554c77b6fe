# Database Schema Documentation

## Overview

Regression Rigor uses PostgreSQL as its primary database. The schema is designed to support the application's core functionality, including user management, MITRE ATT&CK integration, and error tracking.

## Database Connection

- **Host**: postgres (Docker container name)
- **Port**: 5432 (internal), 5440 (external)
- **Database**: regrigor_db
- **Username**: regrigor
- **Password**: regrigor_password

## Entity Relationship Diagram

```
┌─────────────┐       ┌───────────────┐       ┌────────────────┐
│    User     │       │   Technique   │       │ ErrorHandling  │
├─────────────┤       ├───────────────┤       ├────────────────┤
│ id          │       │ id            │       │ id             │
│ username    │       │ technique_id  │       │ error_type     │
│ email       │       │ name          │       │ error_message  │
│ password    │       │ description   │       │ stack_trace    │
│ is_active   │       │ tactic        │       │ user_id        │
│ is_superuser│       │ platforms     │       │ request_path   │
│ created_at  │       │ data_sources  │       │ request_method │
│ updated_at  │       │ is_deleted    │       │ request_data   │
│ tfa_secret  │       │ created_at    │       │ created_at     │
│ tfa_enabled │       │ updated_at    │       └────────┬───────┘
└──────┬──────┘       └───────┬───────┘                │
       │                      │                        │
       │                      │                        │
       │                      │                        │
       │                      │                        │
       │                      ▼                        │
       │               ┌─────────────────┐             │
       │               │ TechniqueRelation│            │
       │               ├─────────────────┤             │
       │               │ id              │             │
       │               │ source_id       │             │
       │               │ target_id       │             │
       │               │ relation_type   │             │
       │               │ created_at      │             │
       │               │ updated_at      │             │
       │               └─────────────────┘             │
       │                                               │
       ▼                                               │
┌─────────────┐                                        │
│   Session   │                                        │
├─────────────┤                                        │
│ id          │                                        │
│ user_id     │◄───────────────────────────────────────┘
│ token       │
│ expires_at  │
│ created_at  │
│ updated_at  │
└─────────────┘
```

## Tables

### User

Stores user account information.

| Column       | Type         | Constraints       | Description                           |
|--------------|--------------|-------------------|---------------------------------------|
| id           | INTEGER      | PK, AUTO INCREMENT| Unique identifier                     |
| username     | VARCHAR(50)  | UNIQUE, NOT NULL  | User's login name                     |
| email        | VARCHAR(100) | UNIQUE, NOT NULL  | User's email address                  |
| password     | VARCHAR(255) | NOT NULL          | Hashed password                       |
| is_active    | BOOLEAN      | NOT NULL, DEFAULT TRUE | Whether the user account is active |
| is_superuser | BOOLEAN      | NOT NULL, DEFAULT FALSE | Whether the user has admin privileges |
| created_at   | TIMESTAMP    | NOT NULL, DEFAULT NOW() | When the user was created       |
| updated_at   | TIMESTAMP    | NOT NULL, DEFAULT NOW() | When the user was last updated |
| tfa_secret   | VARCHAR(32)  | NULL              | Secret key for two-factor authentication |
| tfa_enabled  | BOOLEAN      | NOT NULL, DEFAULT FALSE | Whether 2FA is enabled for the user |

**Indexes:**
- PRIMARY KEY (id)
- UNIQUE INDEX idx_user_username (username)
- UNIQUE INDEX idx_user_email (email)

### Session

Stores user session information.

| Column     | Type         | Constraints       | Description                           |
|------------|--------------|-------------------|---------------------------------------|
| id         | INTEGER      | PK, AUTO INCREMENT| Unique identifier                     |
| user_id    | INTEGER      | FK, NOT NULL      | Reference to the user                 |
| token      | VARCHAR(255) | UNIQUE, NOT NULL  | Session token                         |
| expires_at | TIMESTAMP    | NOT NULL          | When the session expires              |
| created_at | TIMESTAMP    | NOT NULL, DEFAULT NOW() | When the session was created    |
| updated_at | TIMESTAMP    | NOT NULL, DEFAULT NOW() | When the session was last updated |

**Indexes:**
- PRIMARY KEY (id)
- UNIQUE INDEX idx_session_token (token)
- INDEX idx_session_user_id (user_id)

**Foreign Keys:**
- FOREIGN KEY (user_id) REFERENCES User(id) ON DELETE CASCADE

### Technique

Stores MITRE ATT&CK technique information.

| Column       | Type         | Constraints       | Description                           |
|--------------|--------------|-------------------|---------------------------------------|
| id           | INTEGER      | PK, AUTO INCREMENT| Unique identifier                     |
| technique_id | VARCHAR(20)  | UNIQUE, NOT NULL  | MITRE ATT&CK technique ID (e.g., T1001) |
| name         | VARCHAR(100) | NOT NULL          | Technique name                        |
| description  | TEXT         | NOT NULL          | Technique description                 |
| tactic       | VARCHAR(50)  | NOT NULL          | Associated tactic                     |
| platforms    | JSONB        | NOT NULL          | Supported platforms (array)           |
| data_sources | JSONB        | NOT NULL          | Data sources (array)                  |
| is_deleted   | BOOLEAN      | NOT NULL, DEFAULT FALSE | Soft delete flag                |
| created_at   | TIMESTAMP    | NOT NULL, DEFAULT NOW() | When the technique was created  |
| updated_at   | TIMESTAMP    | NOT NULL, DEFAULT NOW() | When the technique was last updated |

**Indexes:**
- PRIMARY KEY (id)
- UNIQUE INDEX idx_technique_technique_id (technique_id)
- INDEX idx_technique_tactic (tactic)
- INDEX idx_technique_is_deleted (is_deleted)

### TechniqueRelation

Stores relationships between MITRE ATT&CK techniques.

| Column        | Type         | Constraints       | Description                           |
|---------------|--------------|-------------------|---------------------------------------|
| id            | INTEGER      | PK, AUTO INCREMENT| Unique identifier                     |
| source_id     | INTEGER      | FK, NOT NULL      | Source technique ID                   |
| target_id     | INTEGER      | FK, NOT NULL      | Target technique ID                   |
| relation_type | VARCHAR(50)  | NOT NULL          | Type of relationship                  |
| created_at    | TIMESTAMP    | NOT NULL, DEFAULT NOW() | When the relation was created   |
| updated_at    | TIMESTAMP    | NOT NULL, DEFAULT NOW() | When the relation was last updated |

**Indexes:**
- PRIMARY KEY (id)
- UNIQUE INDEX idx_technique_relation_unique (source_id, target_id, relation_type)
- INDEX idx_technique_relation_source (source_id)
- INDEX idx_technique_relation_target (target_id)

**Foreign Keys:**
- FOREIGN KEY (source_id) REFERENCES Technique(id) ON DELETE CASCADE
- FOREIGN KEY (target_id) REFERENCES Technique(id) ON DELETE CASCADE

### ErrorHandling

Stores error information for debugging and monitoring.

| Column         | Type         | Constraints       | Description                           |
|----------------|--------------|-------------------|---------------------------------------|
| id             | INTEGER      | PK, AUTO INCREMENT| Unique identifier                     |
| error_type     | VARCHAR(50)  | NOT NULL          | Type of error                         |
| error_message  | TEXT         | NOT NULL          | Error message                         |
| stack_trace    | TEXT         | NULL              | Stack trace (if available)            |
| user_id        | INTEGER      | FK, NULL          | User who encountered the error        |
| request_path   | VARCHAR(255) | NOT NULL          | API path that caused the error        |
| request_method | VARCHAR(10)  | NOT NULL          | HTTP method (GET, POST, etc.)         |
| request_data   | JSONB        | NULL              | Request data (if available)           |
| created_at     | TIMESTAMP    | NOT NULL, DEFAULT NOW() | When the error was recorded     |

**Indexes:**
- PRIMARY KEY (id)
- INDEX idx_error_handling_error_type (error_type)
- INDEX idx_error_handling_user_id (user_id)
- INDEX idx_error_handling_created_at (created_at)

**Foreign Keys:**
- FOREIGN KEY (user_id) REFERENCES User(id) ON DELETE SET NULL

## Database Migrations

Database migrations are managed using Alembic. Migration files are stored in the `migrations/versions/` directory.

To run migrations:

```bash
alembic upgrade head
```

To create a new migration:

```bash
alembic revision --autogenerate -m "Description of changes"
```

## Data Types

### JSONB

PostgreSQL's JSONB type is used for storing structured data that doesn't require a fixed schema, such as:

- Technique platforms
- Technique data sources
- Request data in error logs

### TEXT

The TEXT type is used for storing large text fields, such as:

- Technique descriptions
- Error messages
- Stack traces

## Constraints and Data Integrity

### Primary Keys

All tables have an integer primary key named `id` that auto-increments.

### Foreign Keys

Foreign keys are used to enforce referential integrity between tables:

- Session.user_id → User.id
- TechniqueRelation.source_id → Technique.id
- TechniqueRelation.target_id → Technique.id
- ErrorHandling.user_id → User.id

### Unique Constraints

Unique constraints are used to prevent duplicate data:

- User.username
- User.email
- Session.token
- Technique.technique_id
- TechniqueRelation (source_id, target_id, relation_type)

### Not Null Constraints

Most columns have NOT NULL constraints to ensure data integrity.

### Default Values

Default values are provided for common fields:

- User.is_active = TRUE
- User.is_superuser = FALSE
- User.tfa_enabled = FALSE
- Technique.is_deleted = FALSE
- created_at = NOW()
- updated_at = NOW()

## Soft Deletion

Soft deletion is implemented for the Technique table using the `is_deleted` flag. This allows "deleted" records to be restored if needed.

## Timestamps

All tables include `created_at` and `updated_at` timestamps to track when records were created and last modified.

## Indexing Strategy

Indexes are created on:

- Primary keys
- Foreign keys
- Columns frequently used in WHERE clauses
- Columns used for sorting
- Unique constraints

## Query Optimization

The database schema is designed to optimize common queries:

- User authentication and session validation
- Technique listing and filtering
- Relationship traversal
- Error log analysis

## Backup and Recovery

Database backups are performed daily using pg_dump. Backups are stored in the `backups/` directory and retained for 30 days.

To restore from a backup:

```bash
pg_restore -d regrigor_db backups/backup_file.dump
``` 