# MITRE ATT&CK Technique Scoring System

## Overview

The Technique Scoring System allows security analysts to assign numerical scores to MITRE ATT&CK techniques across multiple categories. This helps organizations prioritize which techniques to focus on for defense, monitoring, and mitigation based on their specific environment and threat landscape.

## Scoring Categories

The system supports the following scoring categories:

| Category | Description | Score Range |
|----------|-------------|------------|
| Impact | Potential damage if the technique is successfully executed | 0-10 |
| Likelihood | Probability of the technique being used against your organization | 0-10 |
| Detectability | How easily the technique can be detected (higher score = easier to detect) | 0-10 |
| Exploitability | How easily the technique can be exploited (higher score = easier to exploit) | 0-10 |
| Custom | User-defined category for organization-specific scoring | 0-10 |

## Weighted Scoring

Each score can be assigned a weight (0-1) to reflect its importance in the overall calculation. The overall score for a technique is calculated as a weighted average of all its category scores:

```
Overall Score = Σ(Score × Weight) / Σ(Weight)
```

## API Endpoints

### Create or Update a Score

**Endpoint:** `POST /api/v1/mitre/techniques/{technique_id}/scores`

**Request Body:**
```json
{
  "technique_id": 123,
  "category": "impact",
  "score": 8.5,
  "weight": 0.8,
  "notes": "High impact due to potential data exfiltration"
}
```

**Response:**
```json
{
  "id": 42,
  "technique_id": 123,
  "category": "impact",
  "score": 8.5,
  "weight": 0.8,
  "notes": "High impact due to potential data exfiltration",
  "created_by": 1,
  "created_at": "2025-03-10T14:30:00Z",
  "updated_at": "2025-03-10T14:30:00Z"
}
```

### Get Scores for a Technique

**Endpoint:** `GET /api/v1/mitre/techniques/{technique_id}/scores`

**Response:**
```json
[
  {
    "id": 42,
    "technique_id": 123,
    "category": "impact",
    "score": 8.5,
    "weight": 0.8,
    "notes": "High impact due to potential data exfiltration",
    "created_by": 1,
    "created_at": "2025-03-10T14:30:00Z",
    "updated_at": "2025-03-10T14:30:00Z"
  },
  {
    "id": 43,
    "technique_id": 123,
    "category": "likelihood",
    "score": 6.0,
    "weight": 1.0,
    "notes": "Moderate likelihood based on threat intelligence",
    "created_by": 1,
    "created_at": "2025-03-10T14:35:00Z",
    "updated_at": "2025-03-10T14:35:00Z"
  }
]
```

### Get Technique with Scores and Overall Score

**Endpoint:** `GET /api/v1/mitre/techniques/{technique_id}/with-scores`

**Response:**
```json
{
  "id": 123,
  "technique_id": "T1566",
  "name": "Phishing",
  "description": "Phishing is a technique used by adversaries...",
  "version_id": 5,
  "created_at": "2025-01-15T10:00:00Z",
  "updated_at": "2025-01-15T10:00:00Z",
  "scores": [
    {
      "id": 42,
      "technique_id": 123,
      "category": "impact",
      "score": 8.5,
      "weight": 0.8,
      "notes": "High impact due to potential data exfiltration",
      "created_by": 1,
      "created_at": "2025-03-10T14:30:00Z",
      "updated_at": "2025-03-10T14:30:00Z"
    },
    {
      "id": 43,
      "technique_id": 123,
      "category": "likelihood",
      "score": 6.0,
      "weight": 1.0,
      "notes": "Moderate likelihood based on threat intelligence",
      "created_by": 1,
      "created_at": "2025-03-10T14:35:00Z",
      "updated_at": "2025-03-10T14:35:00Z"
    }
  ],
  "overall_score": 7.11
}
```

### Bulk Create or Update Scores

**Endpoint:** `POST /api/v1/mitre/techniques/scores/bulk`

**Request Body:**
```json
{
  "scores": [
    {
      "technique_id": 123,
      "category": "impact",
      "score": 8.5,
      "weight": 0.8,
      "notes": "High impact due to potential data exfiltration"
    },
    {
      "technique_id": 123,
      "category": "likelihood",
      "score": 6.0,
      "weight": 1.0,
      "notes": "Moderate likelihood based on threat intelligence"
    },
    {
      "technique_id": 124,
      "category": "impact",
      "score": 9.0,
      "weight": 1.0,
      "notes": "Critical impact on business operations"
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully processed 3 scores: 2 created, 1 updated"
}
```

### Get Available Score Categories

**Endpoint:** `GET /api/v1/mitre/techniques/scores/categories`

**Response:**
```json
[
  "impact",
  "likelihood",
  "detectability",
  "exploitability",
  "custom"
]
```

### Get Top-Scored Techniques

**Endpoint:** `GET /api/v1/mitre/techniques/top-scored?category=impact&limit=5`

**Parameters:**
- `category` (optional): Filter by specific category
- `limit` (optional, default=10): Maximum number of techniques to return

**Response:**
```json
[
  {
    "id": 124,
    "technique_id": "T1486",
    "name": "Data Encrypted for Impact",
    "description": "Adversaries may encrypt data...",
    "version_id": 5,
    "created_at": "2025-01-15T10:00:00Z",
    "updated_at": "2025-01-15T10:00:00Z",
    "scores": [
      {
        "id": 44,
        "technique_id": 124,
        "category": "impact",
        "score": 9.0,
        "weight": 1.0,
        "notes": "Critical impact on business operations",
        "created_by": 1,
        "created_at": "2025-03-10T14:40:00Z",
        "updated_at": "2025-03-10T14:40:00Z"
      }
    ],
    "overall_score": 9.0
  },
  {
    "id": 123,
    "technique_id": "T1566",
    "name": "Phishing",
    "description": "Phishing is a technique used by adversaries...",
    "version_id": 5,
    "created_at": "2025-01-15T10:00:00Z",
    "updated_at": "2025-01-15T10:00:00Z",
    "scores": [
      {
        "id": 42,
        "technique_id": 123,
        "category": "impact",
        "score": 8.5,
        "weight": 0.8,
        "notes": "High impact due to potential data exfiltration",
        "created_by": 1,
        "created_at": "2025-03-10T14:30:00Z",
        "updated_at": "2025-03-10T14:30:00Z"
      },
      {
        "id": 43,
        "technique_id": 123,
        "category": "likelihood",
        "score": 6.0,
        "weight": 1.0,
        "notes": "Moderate likelihood based on threat intelligence",
        "created_by": 1,
        "created_at": "2025-03-10T14:35:00Z",
        "updated_at": "2025-03-10T14:35:00Z"
      }
    ],
    "overall_score": 7.11
  }
]
```

## Use Cases

### Risk Assessment

Security teams can use the scoring system to assess the risk posed by different attack techniques based on their organization's specific environment, assets, and threat landscape.

### Defense Prioritization

By identifying techniques with high impact and likelihood scores, organizations can prioritize which defensive measures to implement first.

### Threat Intelligence Integration

Scores can be updated based on new threat intelligence, allowing organizations to adapt their security posture as the threat landscape evolves.

### Security Control Mapping

Techniques with high scores can be mapped to security controls to ensure that the most critical threats are adequately mitigated.

## Best Practices

1. **Consistent Scoring**: Establish clear guidelines for scoring to ensure consistency across different analysts and teams.

2. **Regular Updates**: Review and update scores regularly based on new threat intelligence and changes in your environment.

3. **Contextual Notes**: Always include detailed notes explaining the rationale behind each score to provide context for future reference.

4. **Weighted Analysis**: Use weights strategically to reflect your organization's security priorities and risk tolerance.

5. **Collaborative Scoring**: Involve multiple stakeholders (security, IT, business) in the scoring process to capture different perspectives.

## Database Schema

The technique scoring system is implemented with the following database schema:

```sql
CREATE TABLE technique_scores (
    id SERIAL PRIMARY KEY,
    technique_id INTEGER NOT NULL REFERENCES mitre_techniques(id) ON DELETE CASCADE,
    category scorecategory NOT NULL,
    score FLOAT NOT NULL,
    weight FLOAT NOT NULL DEFAULT 1.0,
    notes TEXT,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    UNIQUE (technique_id, category)
);

CREATE INDEX ix_technique_scores_technique_id ON technique_scores (technique_id);
CREATE INDEX ix_technique_scores_category ON technique_scores (category);
``` 