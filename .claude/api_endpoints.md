# API Endpoints Documentation

## Base URL

All API endpoints are accessible at `http://localhost:8010/api/v1/`.

## Authentication

Most endpoints require authentication. To authenticate, include a valid JWT token in the Authorization header:

```
Authorization: Bearer <token>
```

To obtain a token, use the `/api/v1/auth/token/` endpoint.

## Rate Limiting

The API implements rate limiting to protect against abuse. Different endpoints have different rate limits:

- Standard endpoints: 100 requests per minute
- Strict endpoints: 20 requests per minute
- Authentication endpoints: 5 requests per minute
- User-specific endpoints: 200 requests per minute per user

When a rate limit is exceeded, the API will return a 429 Too Many Requests response with a Retry-After header.

## Error Handling

All API endpoints return standardized error responses with appropriate HTTP status codes:

- 400 Bad Request: Invalid input data
- 401 Unauthorized: Missing or invalid authentication
- 403 Forbidden: Insufficient permissions
- 404 Not Found: Resource not found
- 422 Unprocessable Entity: Validation error
- 429 Too Many Requests: Rate limit exceeded
- 500 Internal Server Error: Server-side error

Error responses include a detail field with a descriptive error message.

## Endpoints

### Authentication

#### POST /api/v1/auth/token/

Obtain a JWT token for authentication.

**Request Body:**
```json
{
  "username": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "access_token": "string",
  "token_type": "bearer",
  "user_id": "integer",
  "requires_2fa": "boolean"
}
```

#### POST /api/v1/auth/token/refresh/

Refresh an existing JWT token.

**Request Body:**
```json
{
  "refresh_token": "string"
}
```

**Response:**
```json
{
  "access_token": "string",
  "token_type": "bearer"
}
```

#### POST /api/v1/auth/logout/

Invalidate the current JWT token.

**Request Body:** None

**Response:**
```json
{
  "detail": "Successfully logged out"
}
```

### Two-Factor Authentication

#### POST /api/v1/two-factor/setup/

Set up two-factor authentication for a user.

**Request Body:** None

**Response:**
```json
{
  "secret": "string",
  "qr_code": "string"
}
```

#### POST /api/v1/two-factor/verify/

Verify a two-factor authentication code.

**Request Body:**
```json
{
  "code": "string"
}
```

**Response:**
```json
{
  "access_token": "string",
  "token_type": "bearer"
}
```

#### POST /api/v1/two-factor/disable/

Disable two-factor authentication for a user.

**Request Body:**
```json
{
  "password": "string"
}
```

**Response:**
```json
{
  "detail": "Two-factor authentication disabled"
}
```

### User Management

#### GET /api/v1/users/

Get a list of users.

**Query Parameters:**
- skip (integer, default: 0): Number of records to skip
- limit (integer, default: 100): Maximum number of records to return

**Response:**
```json
[
  {
    "id": "integer",
    "username": "string",
    "email": "string",
    "is_active": "boolean",
    "is_superuser": "boolean",
    "created_at": "datetime",
    "updated_at": "datetime"
  }
]
```

#### GET /api/v1/users/{user_id}/

Get a specific user by ID.

**Path Parameters:**
- user_id (integer): The ID of the user

**Response:**
```json
{
  "id": "integer",
  "username": "string",
  "email": "string",
  "is_active": "boolean",
  "is_superuser": "boolean",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

#### POST /api/v1/users/

Create a new user.

**Request Body:**
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "is_active": "boolean",
  "is_superuser": "boolean"
}
```

**Response:**
```json
{
  "id": "integer",
  "username": "string",
  "email": "string",
  "is_active": "boolean",
  "is_superuser": "boolean",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

#### PUT /api/v1/users/{user_id}/

Update a user.

**Path Parameters:**
- user_id (integer): The ID of the user

**Request Body:**
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "is_active": "boolean",
  "is_superuser": "boolean"
}
```

**Response:**
```json
{
  "id": "integer",
  "username": "string",
  "email": "string",
  "is_active": "boolean",
  "is_superuser": "boolean",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

#### DELETE /api/v1/users/{user_id}/

Delete a user.

**Path Parameters:**
- user_id (integer): The ID of the user

**Response:**
```json
{
  "detail": "User deleted successfully"
}
```

### MITRE ATT&CK Integration

#### GET /api/v1/mitre/techniques/

Get a list of MITRE ATT&CK techniques.

**Query Parameters:**
- skip (integer, default: 0): Number of records to skip
- limit (integer, default: 100): Maximum number of records to return
- include_deleted (boolean, default: false): Whether to include deleted techniques

**Response:**
```json
[
  {
    "id": "string",
    "technique_id": "string",
    "name": "string",
    "description": "string",
    "tactic": "string",
    "platforms": ["string"],
    "data_sources": ["string"],
    "is_deleted": "boolean",
    "created_at": "datetime",
    "updated_at": "datetime"
  }
]
```

#### GET /api/v1/mitre/techniques/{technique_id}/

Get a specific MITRE ATT&CK technique.

**Path Parameters:**
- technique_id (string): The ID of the technique (e.g., "T1001")

**Query Parameters:**
- include_deleted (boolean, default: false): Whether to include deleted techniques

**Response:**
```json
{
  "id": "string",
  "technique_id": "string",
  "name": "string",
  "description": "string",
  "tactic": "string",
  "platforms": ["string"],
  "data_sources": ["string"],
  "is_deleted": "boolean",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

#### POST /api/v1/mitre/techniques/

Create a new MITRE ATT&CK technique.

**Request Body:**
```json
{
  "technique_id": "string",
  "name": "string",
  "description": "string",
  "tactic": "string",
  "platforms": ["string"],
  "data_sources": ["string"]
}
```

**Response:**
```json
{
  "id": "string",
  "technique_id": "string",
  "name": "string",
  "description": "string",
  "tactic": "string",
  "platforms": ["string"],
  "data_sources": ["string"],
  "is_deleted": "boolean",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

#### PUT /api/v1/mitre/techniques/{technique_id}/

Update a MITRE ATT&CK technique.

**Path Parameters:**
- technique_id (string): The ID of the technique (e.g., "T1001")

**Request Body:**
```json
{
  "name": "string",
  "description": "string",
  "tactic": "string",
  "platforms": ["string"],
  "data_sources": ["string"]
}
```

**Response:**
```json
{
  "id": "string",
  "technique_id": "string",
  "name": "string",
  "description": "string",
  "tactic": "string",
  "platforms": ["string"],
  "data_sources": ["string"],
  "is_deleted": "boolean",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

#### DELETE /api/v1/mitre/techniques/{technique_id}/

Soft-delete a MITRE ATT&CK technique.

**Path Parameters:**
- technique_id (string): The ID of the technique (e.g., "T1001")

**Response:**
```json
{
  "detail": "Technique deleted successfully"
}
```

#### POST /api/v1/mitre/techniques/{technique_id}/restore/

Restore a soft-deleted MITRE ATT&CK technique.

**Path Parameters:**
- technique_id (string): The ID of the technique (e.g., "T1001")

**Response:**
```json
{
  "detail": "Technique restored successfully"
}
```

### Error Handling

#### GET /api/v1/error-handling/

Get a list of error handling records.

**Query Parameters:**
- skip (integer, default: 0): Number of records to skip
- limit (integer, default: 100): Maximum number of records to return

**Response:**
```json
[
  {
    "id": "integer",
    "error_type": "string",
    "error_message": "string",
    "stack_trace": "string",
    "user_id": "integer",
    "request_path": "string",
    "request_method": "string",
    "request_data": "object",
    "created_at": "datetime"
  }
]
```

#### GET /api/v1/error-handling/{error_id}/

Get a specific error handling record.

**Path Parameters:**
- error_id (integer): The ID of the error record

**Response:**
```json
{
  "id": "integer",
  "error_type": "string",
  "error_message": "string",
  "stack_trace": "string",
  "user_id": "integer",
  "request_path": "string",
  "request_method": "string",
  "request_data": "object",
  "created_at": "datetime"
}
```

### Admin Interface

#### GET /api/v1/admin/dashboard/

Get admin dashboard data.

**Response:**
```json
{
  "user_count": "integer",
  "technique_count": "integer",
  "error_count": "integer",
  "recent_errors": [
    {
      "id": "integer",
      "error_type": "string",
      "error_message": "string",
      "created_at": "datetime"
    }
  ],
  "recent_users": [
    {
      "id": "integer",
      "username": "string",
      "created_at": "datetime"
    }
  ]
}
```

## API Versioning

The API uses URL versioning (e.g., `/api/v1/`). When a new version is released, the old version will be maintained for a deprecation period before being removed.

# API Endpoints

This document provides an overview of the API endpoints available in the RegressionRigor platform.

## Authentication

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/auth/register` | POST | Register a new user |
| `/api/v1/auth/login` | POST | Login and get access token |
| `/api/v1/auth/refresh` | POST | Refresh access token |
| `/api/v1/auth/logout` | POST | Logout and invalidate token |
| `/api/v1/auth/verify-email/{token}` | GET | Verify email address |
| `/api/v1/auth/request-password-reset` | POST | Request password reset |
| `/api/v1/auth/reset-password/{token}` | POST | Reset password with token |
| `/api/v1/auth/change-password` | POST | Change password (authenticated) |
| `/api/v1/auth/2fa/setup` | POST | Set up two-factor authentication |
| `/api/v1/auth/2fa/verify` | POST | Verify 2FA code during login |
| `/api/v1/auth/2fa/backup-codes` | GET | Get backup codes for 2FA |
| `/api/v1/auth/2fa/disable` | POST | Disable two-factor authentication |

## User Management

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/users` | GET | List all users |
| `/api/v1/users/{id}` | GET | Get user details |
| `/api/v1/users/{id}` | PUT | Update user details |
| `/api/v1/users/{id}` | DELETE | Delete user |
| `/api/v1/users/{id}/roles` | GET | Get user roles |
| `/api/v1/users/{id}/roles` | PUT | Update user roles |
| `/api/v1/users/me` | GET | Get current user details |
| `/api/v1/users/me` | PUT | Update current user details |

## Session Management

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/sessions` | GET | List all active sessions |
| `/api/v1/sessions/{id}` | GET | Get session details |
| `/api/v1/sessions/{id}` | DELETE | Terminate session |
| `/api/v1/sessions/me` | GET | Get current session details |
| `/api/v1/sessions/me` | DELETE | Terminate current session |

## User Preferences

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/preferences` | GET | Get user preferences |
| `/api/v1/preferences` | PUT | Update user preferences |
| `/api/v1/preferences/{key}` | GET | Get specific preference |
| `/api/v1/preferences/{key}` | PUT | Update specific preference |

## MITRE ATT&CK Framework

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/mitre/tactics` | GET | List all tactics |
| `/api/v1/mitre/tactics/{id}` | GET | Get tactic details |
| `/api/v1/mitre/techniques` | GET | List all techniques |
| `/api/v1/mitre/techniques/{id}` | GET | Get technique details |
| `/api/v1/mitre/techniques/{id}/subtechniques` | GET | List subtechniques |
| `/api/v1/mitre/groups` | GET | List all groups |
| `/api/v1/mitre/groups/{id}` | GET | Get group details |
| `/api/v1/mitre/groups/{id}/techniques` | GET | List techniques used by group |
| `/api/v1/mitre/software` | GET | List all software |
| `/api/v1/mitre/software/{id}` | GET | Get software details |
| `/api/v1/mitre/software/{id}/techniques` | GET | List techniques used by software |
| `/api/v1/mitre/matrices` | GET | List all matrices |
| `/api/v1/mitre/matrices/{id}` | GET | Get matrix details |
| `/api/v1/mitre/matrices/{id}/tactics` | GET | List tactics in matrix |
| `/api/v1/mitre/matrices/{id}/techniques` | GET | List techniques in matrix |

## D3FEND Framework

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/d3fend/artifacts` | GET | List all artifacts |
| `/api/v1/d3fend/artifacts/{id}` | GET | Get artifact details |
| `/api/v1/d3fend/techniques` | GET | List all defensive techniques |
| `/api/v1/d3fend/techniques/{id}` | GET | Get defensive technique details |
| `/api/v1/d3fend/techniques/{id}/mitigates` | GET | List mitigated ATT&CK techniques |
| `/api/v1/d3fend/tactics` | GET | List all defensive tactics |
| `/api/v1/d3fend/tactics/{id}` | GET | Get defensive tactic details |
| `/api/v1/d3fend/tactics/{id}/techniques` | GET | List techniques in tactic |

## Assessment Management

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/assessments` | GET | List all assessments with filtering |
| `/api/v1/assessments/{id}` | GET | Get assessment details by ID |
| `/api/v1/assessments` | POST | Create a new assessment |
| `/api/v1/assessments/{id}` | PUT | Update an existing assessment |
| `/api/v1/assessments/{id}` | DELETE | Soft-delete an assessment |
| `/api/v1/assessments/{id}/restore` | POST | Restore a soft-deleted assessment |
| `/api/v1/assessments/{id}/executions` | GET | Get test executions for an assessment |
| `/api/v1/assessments/{id}/summary` | GET | Get assessment summary |
| `/api/v1/assessments/{id}/report` | GET | Generate assessment report |
| `/api/v1/assessments/executions` | POST | Create a new test execution |
| `/api/v1/assessments/executions/{id}` | GET | Get test execution by ID |
| `/api/v1/assessments/executions/{id}` | PUT | Update a test execution |
| `/api/v1/assessments/executions/{id}` | DELETE | Delete a test execution |

## Error Handling

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/errors` | GET | List all error logs |
| `/api/v1/errors/{id}` | GET | Get error details |
| `/api/v1/errors/stats` | GET | Get error statistics |

## Admin Interface

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/admin/users` | GET | List all users (admin) |
| `/api/v1/admin/users/{id}` | GET | Get user details (admin) |
| `/api/v1/admin/users/{id}` | PUT | Update user (admin) |
| `/api/v1/admin/users/{id}` | DELETE | Delete user (admin) |
| `/api/v1/admin/sessions` | GET | List all sessions (admin) |
| `/api/v1/admin/sessions/{id}` | DELETE | Terminate session (admin) |
| `/api/v1/admin/logs` | GET | View system logs (admin) |
| `/api/v1/admin/stats` | GET | View system statistics (admin) |
| `/api/v1/admin/settings` | GET | View system settings (admin) |
| `/api/v1/admin/settings` | PUT | Update system settings (admin) | 