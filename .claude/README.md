# Regression Rigor Documentation

This directory contains comprehensive documentation for the Regression Rigor project.

## Documentation Overview

### Architecture and Design

- [Architecture](architecture.md) - Detailed description of the application's architecture and component relationships
- [Database Schema](database_schema.md) - Documentation of the database schema and relationships

### API and Endpoints

- [API Endpoints](api_endpoints.md) - Comprehensive documentation of all API endpoints and their functionality

### User Interface

- [UI Components](ui_components.md) - Documentation of UI components and their interactions

### Workflows and Use Cases

- [Workflows](workflows.md) - Common workflows and use cases for the application

### Troubleshooting

- [Troubleshooting Guide](troubleshooting.md) - Solutions for common issues

### Project Management

- [Roadmap](roadmap.md) - Development roadmap and progress tracking
- [Guidelines](guidelines.md) - Development guidelines and best practices
- [Contributing](CONTRIBUTING.md) - Guidelines for contributing to the project
- [Setup](setup.md) - Setup instructions for development environments

## Directory Structure

- `patterns/` - Common code patterns and examples
- `qa/` - Quality assurance documentation and test plans
- `code_index/` - Code index and navigation helpers
- `debug_history/` - History of debugging sessions and solutions
- `metadata/` - Project metadata and configuration
- `cheatsheets/` - Quick reference guides for common tasks

## Port Configuration

The application uses the following port scheme:

- API/Backend: 8010
- React/Frontend: 3010
- PostgreSQL: 5440
- Redis: 9010

## Getting Started

To get started with the Regression Rigor project:

1. Review the [Architecture](architecture.md) document to understand the system design
2. Set up your development environment using the [Setup](setup.md) guide
3. Explore the [API Endpoints](api_endpoints.md) and [UI Components](ui_components.md) documentation
4. Follow the [Workflows](workflows.md) guide to understand common use cases
5. Refer to the [Troubleshooting Guide](troubleshooting.md) if you encounter issues

## Contributing

Please read the [Contributing](CONTRIBUTING.md) guide before making changes to the project.

## License

This project is licensed under the proprietary license - see the LICENSE file in the root directory for details. 