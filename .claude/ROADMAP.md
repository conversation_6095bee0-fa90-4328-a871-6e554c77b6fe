# Project Roadmap

## Current Phase: Core Functionality Development

We are currently focused on implementing the essential features that form the foundation of the RegressionRigor platform. This includes user authentication, campaign management, test case management, and assessment tracking.

## Prioritized Features

1. **Complete Core CRUD Operations** *(In Progress - v0.2.0)*
   - User Authentication ✅ (v0.1.1)
   - Campaign Management API 🔄 (In Development)
   - Test Case Management API 🔄 (In Development)
   - Assessment/Test Outcome Management API 📅 (Planned)
   
   *These features are essential for the basic functionality of the BAS tool. They provide the foundation for all other features and enable users to create, manage, and track security testing activities.*

2. **MITRE Navigator Integration** *(Planned - v0.3.0)*
   - Import MITRE ATT&CK data
   - Visual mapping of test outcomes to TTPs
   - Coverage analysis and reporting
   
   *This integration will enhance the product's credibility and usability by mapping security tests to industry-standard frameworks.*

3. **Security Framework Updates** *(Planned - v0.4.0)*
   - MITRE ATT&CK data import and synchronization
   - D3FEND framework integration
   - STIX/TAXII support for threat intelligence
   
   *Keeping security frameworks updated is critical for maintaining the relevance and effectiveness of the platform.*

4. **Enhanced Testcase Chaining & Sequencing** *(Planned - v0.5.0)*
   - API implementation ✅
   - UI implementation for chain creation and visualization
   - Conditional execution paths
   
   *This feature will improve the depth and realism of security simulations by allowing complex attack scenarios.*

5. **Comprehensive Tagging System** *(Planned - v0.6.0)*
   - Hierarchical tag structure
   - Tag-based filtering and reporting
   - Tag inheritance and propagation
   
   *This enhancement will improve organization and searchability of test cases and results.*

## Future Phases

### Phase 2: Advanced Features (v0.7.0 - v0.9.0)
- Advanced Soft-Deletion Framework
- Automated Test Execution Engine
- Comprehensive Reporting System
- Advanced Search and Filtering
- Bulk Operations and Imports
- API Rate Limiting and Throttling

### Phase 3: Enterprise Capabilities (v1.0.0+)
- Team Management and Collaboration
- Role-Based Dashboards
- Integration with External Tools (Jira, GitHub, etc.)
- Custom Workflow Support
- Enterprise SSO Integration
- Compliance Reporting Templates

## Current Development Focus

We are currently working on the Campaign Management and Test Case Management API endpoints as part of the Core CRUD Operations feature. This includes:

1. Implementing RESTful API endpoints for campaigns and test cases
2. Adding proper validation and error handling
3. Implementing role-based access control
4. Creating comprehensive test coverage
5. Documenting the API endpoints

The next milestone is to complete the Assessment Management API to round out the Core CRUD Operations feature.

## Timeline

- **v0.1.1** (Completed): Enhanced User Authentication
- **v0.2.0** (Target: April 2024): Complete Core CRUD Operations
- **v0.3.0** (Target: May 2024): MITRE Navigator Integration
- **v0.4.0** (Target: June 2024): Security Framework Updates
- **v0.5.0** (Target: July 2024): Enhanced Testcase Chaining UI
- **v0.6.0** (Target: August 2024): Comprehensive Tagging System 