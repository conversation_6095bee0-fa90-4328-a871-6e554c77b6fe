# Common Workflows and Use Cases

This document outlines the common workflows and use cases for the Regression Rigor application.

## User Management Workflows

### User Registration

1. Navigate to the login page
2. Click "Register" to access the registration form
3. Fill in the required fields:
   - Username
   - Email
   - Password
   - Confirm Password
4. Submit the form
5. Verify email address (if email verification is enabled)
6. Log in with the new credentials

### User Authentication

#### Standard Login

1. Navigate to the login page
2. Enter username and password
3. Click "Login"
4. If credentials are valid, the user is redirected to the dashboard
5. If credentials are invalid, an error message is displayed

#### Two-Factor Authentication

1. Complete the standard login process
2. If 2FA is enabled for the user, a 2FA code input screen is displayed
3. Enter the 2FA code from an authenticator app
4. Click "Verify"
5. If the code is valid, the user is redirected to the dashboard
6. If the code is invalid, an error message is displayed

### Password Reset

1. Navigate to the login page
2. Click "Forgot Password"
3. Enter the email address associated with the account
4. Click "Reset Password"
5. Check email for a password reset link
6. Click the link in the email
7. Enter a new password and confirm it
8. Click "Save"
9. Log in with the new password

### User Profile Management

1. Log in to the application
2. Click on the user avatar in the top-right corner
3. Select "Profile" from the dropdown menu
4. View and edit profile information:
   - Update email address
   - Change password
   - Enable/disable two-factor authentication
   - Manage API keys
5. Save changes

## MITRE ATT&CK Integration Workflows

### Browsing Techniques

1. Log in to the application
2. Navigate to the "MITRE ATT&CK" section
3. Browse techniques using the available filters:
   - Tactic
   - Platform
   - Data Source
   - Search term
4. Click on a technique to view its details

### Technique Relationship Exploration

1. Navigate to the "MITRE ATT&CK" section
2. Click on the "Relationships" tab
3. Select a technique from the dropdown or search
4. View the technique's relationships in the graph visualization
5. Click on related techniques to explore further
6. Use the zoom and pan controls to navigate the graph
7. Filter relationships by type using the sidebar controls

### Adding a New Technique

1. Navigate to the "MITRE ATT&CK" section
2. Click "Add Technique"
3. Fill in the required fields:
   - Technique ID (e.g., T1001)
   - Name
   - Description
   - Tactic
   - Platforms
   - Data Sources
4. Click "Save"
5. The new technique is added to the database

### Creating Technique Relationships

1. Navigate to the "MITRE ATT&CK" section
2. Click on the "Relationships" tab
3. Click "Add Relationship"
4. Select the source technique
5. Select the target technique
6. Choose the relationship type:
   - Prerequisite
   - Contains
   - Offers
   - Belongs To
7. Click "Save"
8. The new relationship is added to the graph visualization

## Error Handling Workflows

### Viewing Error Logs

1. Log in as an administrator
2. Navigate to the "Error Logs" section
3. Browse errors using the available filters:
   - Error Type
   - User
   - Date Range
   - Request Path
4. Click on an error to view its details:
   - Error Message
   - Stack Trace
   - Request Data
   - User Information
   - Timestamp

### Error Resolution Tracking

1. Navigate to the "Error Logs" section
2. Click on an error to view its details
3. Click "Mark as Resolved"
4. Add resolution notes (optional)
5. Click "Save"
6. The error is marked as resolved and moved to the resolved errors list

### Error Analysis

1. Navigate to the "Error Logs" section
2. Click on the "Analytics" tab
3. View error statistics and trends:
   - Errors by type
   - Errors by endpoint
   - Errors over time
   - Most common errors
4. Export error data for further analysis

## Administrative Workflows

### User Management (Admin)

1. Log in as an administrator
2. Navigate to the "Admin" section
3. Click on "Users"
4. View a list of all users
5. Perform user management actions:
   - Create new users
   - Edit existing users
   - Deactivate/reactivate users
   - Reset passwords
   - Assign roles

### System Monitoring

1. Log in as an administrator
2. Navigate to the "Admin" section
3. Click on "Dashboard"
4. View system statistics:
   - Active users
   - API request volume
   - Error rate
   - Database size
   - Cache hit rate
5. Set up alerts for critical metrics

### Database Management

1. Log in as an administrator
2. Navigate to the "Admin" section
3. Click on "Database"
4. View database statistics:
   - Table sizes
   - Row counts
   - Index usage
   - Query performance
5. Perform database maintenance actions:
   - Run vacuum
   - Reindex tables
   - View slow queries

## API Usage Workflows

### API Authentication

1. Obtain an API key from the user profile page
2. Include the API key in the Authorization header:
   ```
   Authorization: Bearer <api_key>
   ```
3. Make API requests with the authenticated header

### Retrieving MITRE ATT&CK Data

1. Authenticate with the API
2. Make a GET request to `/api/v1/mitre/techniques/`
3. Use query parameters to filter the results:
   - `tactic`: Filter by tactic
   - `platform`: Filter by platform
   - `data_source`: Filter by data source
   - `search`: Search by name or description
   - `skip`: Pagination offset
   - `limit`: Pagination limit
4. Process the JSON response

### Creating Resources via API

1. Authenticate with the API
2. Prepare the request payload in JSON format
3. Make a POST request to the appropriate endpoint:
   - `/api/v1/mitre/techniques/` for creating techniques
   - `/api/v1/users/` for creating users
4. Include the Content-Type header:
   ```
   Content-Type: application/json
   ```
5. Handle the response:
   - 201 Created: Resource created successfully
   - 400 Bad Request: Invalid input data
   - 401 Unauthorized: Invalid or missing API key
   - 422 Unprocessable Entity: Validation error

## Reporting Workflows

### Generating Reports

1. Navigate to the "Reports" section
2. Select a report type:
   - User Activity
   - Technique Coverage
   - Error Summary
   - System Performance
3. Configure report parameters:
   - Date range
   - Included data
   - Grouping options
   - Visualization preferences
4. Click "Generate Report"
5. View the generated report in the browser

### Exporting Data

1. Navigate to the relevant section (Users, Techniques, Errors, etc.)
2. Apply filters to narrow down the data
3. Click "Export"
4. Select the export format:
   - CSV
   - JSON
   - PDF
   - Excel
5. Configure export options (if applicable)
6. Click "Download"
7. Save the exported file

## Integration Workflows

### MITRE ATT&CK Data Import

1. Log in as an administrator
2. Navigate to the "Admin" section
3. Click on "Integrations"
4. Select "MITRE ATT&CK Import"
5. Choose the import source:
   - MITRE ATT&CK Enterprise Matrix
   - MITRE ATT&CK Mobile Matrix
   - MITRE ATT&CK ICS Matrix
   - Custom JSON file
6. Configure import options:
   - Update existing techniques
   - Import relationships
   - Import data sources
7. Click "Start Import"
8. Monitor the import progress
9. Review the import summary

### External System Integration

1. Log in as an administrator
2. Navigate to the "Admin" section
3. Click on "Integrations"
4. Select the external system to integrate with:
   - SIEM systems
   - Vulnerability scanners
   - Threat intelligence platforms
5. Configure the integration:
   - API endpoints
   - Authentication credentials
   - Data mapping
   - Sync frequency
6. Test the connection
7. Save the configuration
8. Enable the integration

## Troubleshooting Workflows

### Application Error Recovery

1. Encounter an application error
2. Note the error message and error ID
3. Try refreshing the page
4. If the error persists, try logging out and back in
5. If the error still persists, contact the administrator with the error ID

### Performance Issue Diagnosis

1. Experience a performance issue
2. Note the specific action that is slow
3. Check the network tab in browser developer tools
4. Identify slow API requests
5. Report the issue to the administrator with:
   - Steps to reproduce
   - Timing information
   - Browser and OS details

### Data Discrepancy Resolution

1. Identify a data discrepancy
2. Document the expected vs. actual data
3. Check for recent changes that might have affected the data
4. Report the issue to the administrator with:
   - Description of the discrepancy
   - Expected data
   - Actual data
   - When the discrepancy was first noticed 