# Development Roadmap

## Project: Regression Rigor

This roadmap outlines the development plan for the Regression Rigor project, a cybersecurity data platform for managing security testing campaigns and integrating with the MITRE ATT&CK framework.

## Completed Tasks

### Project Organization
- [x] Move all documentation files to the `docs/` directory
- [x] Create an index file in the `docs/` directory
- [x] Update the main README.md to reference the new documentation structure
- [x] Move all Docker-related files to the `.dockerwrapper` directory
- [x] Update paths in Dockerfiles and scripts
- [x] Create a README.md in the `.dockerwrapper` directory

### Authentication System Enhancement
- [x] Add two-factor authentication fields to the User model
- [x] Create schema models for 2FA
- [x] Implement 2FA methods in the User model
- [x] Create routes for 2FA
- [x] Update the authentication flow to support 2FA
- [x] Create a database migration for 2FA fields
- [x] Update dependencies in pyproject.toml
- [x] Update Dockerfiles to use dependencies from pyproject.toml
- [x] Rebuild Docker containers with updated dependencies

### Database Schema Verification
- [x] Verify the database schema for all models
- [x] Ensure proper relationships between models
- [x] Document the database schema in `docs/database_schema.md`

### API Enhancement
- [x] Implement rate limiting for API endpoints
  - [x] Create enhanced rate limiting middleware
  - [x] Configure tiered rate limits for different endpoint types
  - [x] Add IP whitelist/blacklist functionality
  - [x] Implement path-based rate limiting
  - [x] Add proper error responses with retry-after headers

### Comprehensive Tagging System
- [x] Implement hierarchical tag structures
- [x] Create tag categories for better organization
- [x] Implement tag inheritance between related entities
- [x] Add tag-based filtering in all list views
- [x] Develop tag analytics for identifying testing patterns
- [x] Implement bulk tagging operations
- [x] Create tag propagation rules
- [x] Add tag relations for defining relationships between tags
- [x] Create database models for all tagging components
- [x] Implement API endpoints for advanced tagging features
- [x] Add comprehensive tests for the tagging system
- [x] Update documentation with tagging system details

## Current Tasks

### API Enhancement (Continued)
- [ ] Add comprehensive input validation
- [ ] Improve error handling and responses

## Comprehensive Future Roadmap

### Phase 1: Core Regression Testing Framework Enhancement (3 months)

#### 1. Enhanced Testcase Chaining & Sequencing
- **Timeline**: 1 month
- **Description**: Implement the ability to create, manage, and execute sequences of testcases representing complete attack chains (like ransomware)
- **Features**:
  - Definition of sequential dependencies between testcases
  - Execution order enforcement
  - Automated flow control for testcase chains
  - Precondition/postcondition validation between steps
  - Visual representation of test chains with directed graphs

#### 2. Comprehensive Tagging System
- **Timeline**: 2 weeks
- **Status**: ✅ Completed (March 11, 2025)
- **Description**: Implement advanced tagging capabilities across all entities
- **Features**:
  - ✅ Hierarchical tag structures
  - ✅ Tag categories for better organization
  - ✅ Tag inheritance between related entities
  - ✅ Tag-based filtering in all list views
  - ✅ Tag analytics for identifying testing patterns
  - ✅ Bulk tagging operations
  - ✅ Tag propagation rules
  - ✅ Tag relations for defining relationships between tags

#### 3. Advanced Soft-Deletion Framework
- **Timeline**: 2 weeks
- **Description**: Enhance the existing soft-deletion capabilities
- **Features**:
  - Configurable retention policies
  - Scheduled purging of soft-deleted records
  - Recovery workflows for soft-deleted entities
  - Cascade control for soft-deletion relationships
  - Audit trail for deletion and recovery actions

#### 4. Testcase Template System
- **Timeline**: 1 month
- **Description**: Create a system for managing reusable testcase templates
- **Features**:
  - Library of standard testcases based on MITRE ATT&CK techniques
  - Version control for templates
  - Template inheritance and specialization
  - Template variable substitution for environment-specific values
  - Import/export of template libraries

### Phase 2: MITRE ATT&CK Integration & Visualization (3 months)

#### 5. Enhanced MITRE ATT&CK Navigator Integration
- **Timeline**: 1 month
- **Description**: Integrate and customize the MITRE ATT&CK Navigator
- **Features**:
  - Embedded navigator view within the application
  - Custom styling and branding
  - Integration with test results for heat-map visualization
  - Time-based visualization of test coverage
  - Custom layer management for organizational context

#### 6. Advanced Attack Path Visualization
- **Timeline**: 1 month
- **Description**: Create interactive visualizations for attack paths
- **Features**:
  - D3.js-based interactive graph visualization
  - Multiple visualization modes (tactics, techniques, testcases)
  - Drill-down capabilities for detailed analysis
  - Animated playback of attack sequences
  - Export of visualizations as SVG/PNG

#### 7. Tactical Phase Mapping
- **Timeline**: 2 weeks
- **Description**: Enhanced mapping between MITRE tactics (phases) and techniques
- **Features**:
  - Custom phase definitions beyond standard MITRE tactics
  - Phase-specific reporting and analytics
  - Cross-phase relationship mapping
  - Customizable phase taxonomy
  - Phase-based filtering of testcases

#### 8. Technique Relationship Mapping
- **Timeline**: 3 weeks
- **Description**: Track and visualize relationships between techniques
- **Features**:
  - Prerequisite and dependency mapping
  - Technique similarity analysis
  - Common chaining patterns identification
  - Alternative technique suggestions
  - Impact analysis for defense bypass

### Phase 3: Threat Resilience Framework (2 months)

#### 9. Resilience Measurement Framework
- **Timeline**: 3 weeks
- **Description**: Develop a framework for measuring defensive resilience
- **Features**:
  - Multi-dimensional scoring methodology
  - Weighted scoring based on technique impact and likelihood
  - Historical trend analysis
  - Benchmark comparison against industry standards
  - Adaptive scoring based on environmental factors

#### 10. Defense Effectiveness Dashboard
- **Timeline**: 1 month
- **Description**: Create a comprehensive dashboard for monitoring defensive effectiveness
- **Features**:
  - Real-time monitoring of resilience metrics
  - Drill-down capabilities for detailed analysis
  - Customizable dashboard layouts
  - Alert thresholds for critical resilience changes
  - Executive summary reporting

#### 11. Control Gap Analysis
- **Timeline**: 2 weeks
- **Description**: Automatically identify and report control gaps
- **Features**:
  - Mapping of testcases to security controls
  - Identification of uncovered techniques
  - Prioritization of control gaps based on risk
  - Recommended mitigations for identified gaps
  - Integration with vulnerability management systems

### Phase 4: Comprehensive Export & Reporting System (2 months)

#### 12. Universal Export Framework
- **Timeline**: 3 weeks
- **Description**: Implement a flexible system for exporting data in various formats
- **Features**:
  - Support for JSON, CSV, XLSX formats
  - Report templates for PDF generation
  - LaTeX export for academic/formal documentation
  - Customizable export templates
  - Scheduled automated exports

#### 13. Advanced Reporting Engine
- **Timeline**: 1 month
- **Description**: Create a powerful reporting engine for comprehensive analysis
- **Features**:
  - Interactive report builder
  - Multiple visualization options (charts, tables, matrices)
  - Comparison reporting between assessments
  - Trend analysis over time
  - Automated executive summaries
  - Integration with business intelligence tools

#### 14. Compliance Mapping & Reporting
- **Timeline**: 2 weeks
- **Description**: Map testcases and results to regulatory compliance frameworks
- **Features**:
  - Pre-built mappings to common frameworks (NIST, ISO, PCI-DSS, etc.)
  - Compliance gap analysis
  - Evidence collection for audits
  - Compliance reporting templates
  - Custom compliance framework definitions

### Phase 5: Advanced Analytics & Intelligence (3 months)

#### 15. Predictive Security Analytics
- **Timeline**: 1 month
- **Description**: Implement machine learning for predictive security analysis
- **Features**:
  - Attack chain prediction based on initial techniques
  - Control effectiveness prediction
  - Anomaly detection in test results
  - Prioritization suggestions for testing
  - Integration with threat intelligence for relevance scoring

#### 16. Defensive Evolution Tracking
- **Timeline**: 3 weeks
- **Description**: Track how defensive capabilities evolve over time
- **Features**:
  - Historical resilience trending
  - Regression detection
  - Defense improvement velocity metrics
  - Maturity models for defensive capabilities
  - Comparative benchmarking

#### 17. Threat Intelligence Integration
- **Timeline**: 1 month
- **Description**: Integrate with threat intelligence sources
- **Features**:
  - Automated import from STIX/TAXII feeds
  - Mapping of intelligence to testcases
  - Relevance scoring for techniques based on intelligence
  - Custom intelligence source integration
  - Intelligence-driven test prioritization

#### 18. Adaptive Testing Framework
- **Timeline**: 1 month
- **Description**: Create an adaptive framework that evolves testing based on results
- **Features**:
  - Dynamic test case selection based on previous results
  - Auto-generation of test variations
  - Automated difficulty scaling
  - Defense evasion automation
  - Self-optimizing test sequences 