# Product Requirements Document: Core CRUD Operations

## Overview

The Core CRUD Operations feature encompasses the essential functionality for managing campaigns, test cases, and assessments in the RegressionRigor platform. This document outlines the requirements for implementing comprehensive Create, Read, Update, and Delete operations for these core entities, along with the necessary API endpoints, validation rules, and access controls.

## Business Objectives

1. Enable security teams to efficiently create and manage testing campaigns
2. Provide a structured way to organize and execute test cases
3. Track assessment outcomes and generate reports
4. Ensure proper access control based on user roles
5. Support the foundation for more advanced features like test case chaining and MITRE mapping

## User Stories

### Campaign Management

1. As a security operator, I want to create a new campaign with a name, description, and status
2. As a security operator, I want to update campaign details to reflect changes in scope or objectives
3. As a security operator, I want to view a list of all campaigns with filtering and sorting options
4. As a security operator, I want to archive or delete campaigns that are no longer needed
5. As a security operator, I want to assign test cases to campaigns
6. As a viewer, I want to view campaign details and associated test cases

### Test Case Management

1. As a security operator, I want to create test cases with detailed steps, expected outcomes, and MITRE mappings
2. As a security operator, I want to update test case details as techniques evolve
3. As a security operator, I want to view a list of all test cases with filtering by technique, tactic, or status
4. As a security operator, I want to archive or delete test cases that are no longer relevant
5. As a security operator, I want to clone existing test cases to create variations
6. As a viewer, I want to view test case details including execution history

### Assessment Management

1. As a security operator, I want to record test outcomes (pass/fail/partial) with evidence
2. As a security operator, I want to add notes and observations to test executions
3. As a security operator, I want to track remediation status for failed tests
4. As a security operator, I want to generate reports on assessment results
5. As an admin, I want to review and approve assessment findings
6. As a viewer, I want to view assessment results with appropriate access controls

## Functional Requirements

### Campaign API Endpoints

| Endpoint | Method | Description | Access |
|----------|--------|-------------|--------|
| `/api/campaigns` | GET | List all campaigns with pagination, filtering, and sorting | All authenticated users |
| `/api/campaigns/{id}` | GET | Get campaign details by ID | All authenticated users |
| `/api/campaigns` | POST | Create a new campaign | Operator, Admin |
| `/api/campaigns/{id}` | PUT | Update an existing campaign | Operator, Admin |
| `/api/campaigns/{id}` | DELETE | Soft-delete a campaign | Operator, Admin |
| `/api/campaigns/{id}/restore` | POST | Restore a soft-deleted campaign | Admin |
| `/api/campaigns/{id}/test-cases` | GET | List test cases associated with a campaign | All authenticated users |
| `/api/campaigns/{id}/test-cases` | POST | Add test cases to a campaign | Operator, Admin |

### Test Case API Endpoints

| Endpoint | Method | Description | Access |
|----------|--------|-------------|--------|
| `/api/test-cases` | GET | List all test cases with pagination, filtering, and sorting | All authenticated users |
| `/api/test-cases/{id}` | GET | Get test case details by ID | All authenticated users |
| `/api/test-cases` | POST | Create a new test case | Operator, Admin |
| `/api/test-cases/{id}` | PUT | Update an existing test case | Operator, Admin |
| `/api/test-cases/{id}` | DELETE | Soft-delete a test case | Operator, Admin |
| `/api/test-cases/{id}/restore` | POST | Restore a soft-deleted test case | Admin |
| `/api/test-cases/{id}/clone` | POST | Clone a test case | Operator, Admin |
| `/api/test-cases/{id}/executions` | GET | List execution history for a test case | All authenticated users |

### Assessment API Endpoints

| Endpoint | Method | Description | Access |
|----------|--------|-------------|--------|
| `/api/assessments` | GET | List all assessments with pagination, filtering, and sorting | All authenticated users |
| `/api/assessments/{id}` | GET | Get assessment details by ID | All authenticated users |
| `/api/assessments` | POST | Create a new assessment | Operator, Admin |
| `/api/assessments/{id}` | PUT | Update an existing assessment | Operator, Admin |
| `/api/assessments/{id}` | DELETE | Soft-delete an assessment | Operator, Admin |
| `/api/assessments/{id}/restore` | POST | Restore a soft-deleted assessment | Admin |
| `/api/assessments/{id}/results` | GET | Get assessment results | All authenticated users |
| `/api/assessments/{id}/report` | GET | Generate assessment report | All authenticated users |

## Data Models

### Campaign Model

```python
class Campaign(Base, SoftDeleteMixin):
    __tablename__ = "campaigns"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    status = Column(String, default="draft")  # draft, active, completed, archived
    start_date = Column(DateTime, nullable=True)
    end_date = Column(DateTime, nullable=True)
    created_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    creator = relationship("User", foreign_keys=[created_by])
    test_cases = relationship("TestCase", secondary="campaign_test_cases", back_populates="campaigns")
    assessments = relationship("Assessment", back_populates="campaign")
```

### Test Case Model

```python
class TestCase(Base, SoftDeleteMixin):
    __tablename__ = "test_cases"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    steps = Column(Text, nullable=True)
    expected_result = Column(Text, nullable=True)
    actual_result = Column(Text, nullable=True)
    status = Column(String, default="draft")  # draft, active, deprecated
    mitre_technique_id = Column(String, ForeignKey("mitre_techniques.id"), nullable=True)
    created_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    creator = relationship("User", foreign_keys=[created_by])
    mitre_technique = relationship("MitreTechnique")
    campaigns = relationship("Campaign", secondary="campaign_test_cases", back_populates="test_cases")
    executions = relationship("TestExecution", back_populates="test_case")
```

### Assessment Model

```python
class Assessment(Base, SoftDeleteMixin):
    __tablename__ = "assessments"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    campaign_id = Column(Integer, ForeignKey("campaigns.id"))
    status = Column(String, default="pending")  # pending, in_progress, completed, reviewed
    start_date = Column(DateTime, nullable=True)
    end_date = Column(DateTime, nullable=True)
    created_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    campaign = relationship("Campaign", back_populates="assessments")
    creator = relationship("User", foreign_keys=[created_by])
    test_executions = relationship("TestExecution", back_populates="assessment")
```

### Test Execution Model

```python
class TestExecution(Base):
    __tablename__ = "test_executions"

    id = Column(Integer, primary_key=True, index=True)
    test_case_id = Column(Integer, ForeignKey("test_cases.id"))
    assessment_id = Column(Integer, ForeignKey("assessments.id"))
    result = Column(String, default="pending")  # pending, pass, fail, partial
    notes = Column(Text, nullable=True)
    evidence = Column(Text, nullable=True)  # JSON or links to evidence files
    executed_by = Column(Integer, ForeignKey("users.id"))
    executed_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    test_case = relationship("TestCase", back_populates="executions")
    assessment = relationship("Assessment", back_populates="test_executions")
    executor = relationship("User", foreign_keys=[executed_by])
```

## Validation Rules

### Campaign Validation

- Name: Required, 3-100 characters
- Description: Optional, max 5000 characters
- Status: Required, one of: "draft", "active", "completed", "archived"
- Start Date: Optional, must be before End Date if both are provided
- End Date: Optional, must be after Start Date if both are provided

### Test Case Validation

- Name: Required, 3-100 characters
- Description: Optional, max 5000 characters
- Steps: Optional, max 10000 characters
- Expected Result: Optional, max 5000 characters
- Status: Required, one of: "draft", "active", "deprecated"
- MITRE Technique ID: Optional, must be a valid technique ID

### Assessment Validation

- Name: Required, 3-100 characters
- Description: Optional, max 5000 characters
- Campaign ID: Required, must be a valid campaign ID
- Status: Required, one of: "pending", "in_progress", "completed", "reviewed"
- Start Date: Optional, must be before End Date if both are provided
- End Date: Optional, must be after Start Date if both are provided

### Test Execution Validation

- Test Case ID: Required, must be a valid test case ID
- Assessment ID: Required, must be a valid assessment ID
- Result: Required, one of: "pending", "pass", "fail", "partial"
- Notes: Optional, max 5000 characters
- Evidence: Optional, valid JSON or file references

## Access Control

- **Admin**: Full access to all endpoints and operations
- **Operator**: Can create, read, update, and soft-delete campaigns, test cases, and assessments
- **Viewer**: Can only read campaigns, test cases, assessments, and their related data

## Error Handling

All API endpoints should return appropriate HTTP status codes and error messages:

- 200: Success
- 201: Resource created
- 400: Bad request (validation error)
- 401: Unauthorized (not authenticated)
- 403: Forbidden (not authorized)
- 404: Resource not found
- 409: Conflict (e.g., duplicate name)
- 500: Server error

Error responses should include:
- Error code
- Error message
- Details about the error (for validation errors)

## Performance Requirements

- API endpoints should respond within 500ms for simple queries
- List endpoints should support pagination with configurable page size
- Heavy operations should be asynchronous where appropriate
- Database queries should be optimized with proper indexing

## Security Requirements

- All endpoints must require authentication
- Role-based access control must be enforced
- Input validation must be performed on all user inputs
- Sensitive data must be properly sanitized in responses
- API rate limiting should be implemented to prevent abuse

## Implementation Phases

### Phase 1: Campaign Management
- Implement campaign CRUD endpoints
- Add validation and access control
- Create unit and integration tests

### Phase 2: Test Case Management
- Implement test case CRUD endpoints
- Add MITRE technique mapping
- Implement test case cloning
- Create unit and integration tests

### Phase 3: Assessment Management
- Implement assessment CRUD endpoints
- Add test execution tracking
- Implement basic reporting
- Create unit and integration tests

### Phase 4: Integration and Refinement
- Integrate all components
- Implement advanced filtering and sorting
- Add bulk operations
- Performance optimization
- Comprehensive testing

## Success Criteria

1. All API endpoints are implemented and working as specified
2. Proper validation and error handling is in place
3. Role-based access control is enforced
4. Unit and integration tests achieve >90% code coverage
5. API response times meet performance requirements
6. Documentation is complete and accurate 