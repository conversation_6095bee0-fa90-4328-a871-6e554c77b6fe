# Development Guidelines

## Feature Development Process

All features in the Regression Rigor platform must follow this comprehensive development process:

1. **PRD Creation and Approval**:
   - Create a detailed Product Requirements Document (PRD)
   - Get approval from all stakeholders
   - Use the template in `.claude/PRD_TEMPLATE.md`
   - Follow the process outlined in `.claude/PRD_PROCESS.md`

2. **API First**: 
   - Design and implement the API endpoints
   - Create database models and migrations
   - Implement service layer business logic
   - Document API endpoints with OpenAPI specifications

3. **API Test Suite (Extensive) Second**:
   - Implement comprehensive unit tests for models
   - Create integration tests for API endpoints
   - Test all success and error scenarios
   - Ensure at least 80% code coverage for backend code

4. **UI Third**:
   - Implement frontend components and services
   - Create user interfaces that consume the APIs
   - Follow the design system and UX guidelines
   - Implement client-side validation and error handling

5. **UI Test Suite (Extensive) Last**:
   - Create component tests for UI elements
   - Implement end-to-end tests for critical user flows
   - Test across different screen sizes and browsers
   - Verify accessibility compliance

This cycle ensures that requirements are clearly defined before development begins, and that the backend is solid and well-tested before frontend development begins, reducing the likelihood of changes to the API during UI development.

## Code Quality Standards

- All code must pass linting checks
- Pull requests require at least one code review
- New features must include appropriate tests
- Documentation must be updated with each feature

## Git Workflow

- Feature branches should be created from the `develop` branch
- Branch naming convention: `feature/feature-name`, `bugfix/issue-description`
- Commit messages should be descriptive and reference issue numbers
- Pull requests should include a summary of changes and testing performed

## Documentation Requirements

Each feature must include:
- Product Requirements Document (PRD)
- API documentation (OpenAPI/Swagger)
- Database schema changes
- Frontend component documentation
- User guide updates (if applicable)
- Test coverage report 