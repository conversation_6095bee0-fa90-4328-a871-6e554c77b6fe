# Product Requirements Document: Hierarchical Data Model Alignment

## Overview

The Regression Rigor platform requires a data model restructuring to better align with the security testing hierarchy shown in the conceptual diagram. This restructuring will enhance the platform's ability to organize security testing activities in a more intuitive and industry-standard way, improving usability and reporting capabilities.

## Background

The current data model has implemented Campaigns, Assessments, and Test Cases, but the relationships between these entities do not fully align with the hierarchical structure shown in the conceptual diagram. Additionally, the Environment entity is missing from the current implementation.

The conceptual diagram shows the following hierarchy:
1. Environments (Test Environment) - Top level
2. Assessments (Test Scope) - Second level
3. Campaigns (Groups of Tests) - Third level
4. Test Cases (Individual Tests) - Bottom level

Our current implementation has a different structure where Campaigns contain Assessments, which is the reverse of what's shown in the diagram.

## Goals

1. Restructure the data model to align with the conceptual hierarchy
2. Implement the Environment entity as the top-level container
3. Adjust relationships between entities to reflect the proper hierarchy
4. Ensure backward compatibility where possible
5. Update API endpoints to support the new structure
6. Provide migration path for existing data
7. Resolve circular database dependencies between Assessment and Campaign models

## Non-Goals

1. Redesigning the entire application architecture
2. Changing the core functionality of existing entities
3. Implementing UI changes (will be addressed in a separate PRD)

## Requirements

### 1. Environment Entity

#### 1.1 Data Model
- Create an Environment model with the following attributes:
  - id (UUID): Unique identifier
  - name (String): Name of the environment
  - description (Text): Detailed description
  - type (Enum): Type of environment (e.g., Production, Staging, Development, Test)
  - status (Enum): Status of the environment (e.g., Active, Inactive, Deprecated)
  - created_by (Foreign Key): Reference to the user who created the environment
  - created_at (DateTime): Timestamp of creation
  - updated_at (DateTime): Timestamp of last update
  - deleted_at (DateTime, nullable): Timestamp of soft deletion
  - is_deprecated (Boolean): Flag indicating if the environment is deprecated
  - is_revoked (Boolean): Flag indicating if the environment is revoked
  - revoked_by_id (Foreign Key, nullable): Reference to the user who revoked the environment
  - version (Integer): Version number for optimistic locking

#### 1.2 Relationships
- One-to-Many relationship with Assessments (One Environment can have multiple Assessments)

#### 1.3 API Endpoints
- GET /api/v1/environments/ - List all environments
- GET /api/v1/environments/{environment_id} - Get a specific environment
- POST /api/v1/environments/ - Create a new environment
- PUT /api/v1/environments/{environment_id} - Update an environment
- DELETE /api/v1/environments/{environment_id} - Soft-delete an environment
- POST /api/v1/environments/{environment_id}/restore - Restore a soft-deleted environment
- GET /api/v1/environments/{environment_id}/assessments - Get assessments for an environment

### 2. Assessment Entity Restructuring

#### 2.1 Data Model Updates
- Update the Assessment model to include:
  - environment_id (Foreign Key): Reference to the environment
  - Remove campaign_id as a required field (make it nullable)
  - Eventually remove campaign_id completely to resolve circular dependencies

#### 2.2 Relationship Changes
- Change the relationship with Campaign from Many-to-One to One-to-Many (One Assessment can have multiple Campaigns)
- Add Many-to-One relationship with Environment (Many Assessments belong to One Environment)
- Remove the circular dependency where Assessment references Campaign and Campaign references Assessment

#### 2.3 API Endpoint Updates
- Update existing assessment endpoints to reflect new relationships
- Add new endpoints:
  - GET /api/v1/assessments/{assessment_id}/campaigns - Get campaigns for an assessment
  - POST /api/v1/assessments/{assessment_id}/campaigns - Assign campaigns to an assessment

### 3. Campaign Entity Restructuring

#### 3.1 Data Model Updates
- Update the Campaign model to include:
  - assessment_id (Foreign Key, nullable): Reference to the assessment
  - Make existing fields compatible with the new structure

#### 3.2 Relationship Changes
- Change the relationship with Assessment from One-to-Many to Many-to-One (Many Campaigns belong to One Assessment)
- Maintain the Many-to-Many relationship with Test Cases
- Remove the circular dependency where Campaign references Assessment and Assessment references Campaign

#### 3.3 API Endpoint Updates
- Update existing campaign endpoints to reflect new relationships
- Ensure backward compatibility for existing API consumers

### 4. Test Case Entity (No Major Changes)

#### 4.1 Relationship Verification
- Verify that the existing relationships with Campaigns are maintained
- Ensure test executions are properly linked to assessments

### 5. Migration Strategy

#### 5.1 Database Migration
- Create migration scripts to:
  - Add the Environment table
  - Update the Assessment table structure
  - Update the Campaign table structure
  - Preserve existing data relationships
  - Remove circular dependencies between Assessment and Campaign

#### 5.2 Data Migration
- Create a default Environment for existing Assessments
- Adjust the relationships between existing Campaigns and Assessments
- Ensure no data loss during migration

### 6. Documentation Updates

#### 6.1 API Documentation
- Update API documentation to reflect the new structure
- Provide examples of how to use the new endpoints

#### 6.2 Data Model Documentation
- Update data model documentation with the new entity relationships
- Create diagrams showing the new hierarchy

## User Stories

1. As a security administrator, I want to create different environments to organize my security testing activities, so that I can separate production, staging, and development testing.

2. As a security tester, I want to create assessments within a specific environment, so that I can properly scope my testing activities.

3. As a security manager, I want to assign campaigns to assessments, so that I can organize related security tests together.

4. As a security analyst, I want to view all campaigns within an assessment, so that I can understand the testing coverage.

5. As a security engineer, I want to migrate existing data to the new structure without losing any information, so that I can benefit from the new organization without redoing previous work.

6. As a developer, I want to have a clean data model without circular dependencies, so that I can maintain and extend the codebase more easily.

## Acceptance Criteria

1. The Environment entity is implemented with all required attributes and relationships.
2. The Assessment entity is updated to reference an Environment and contain multiple Campaigns.
3. The Campaign entity is updated to reference an Assessment.
4. All API endpoints are updated to support the new structure.
5. Migration scripts successfully transfer existing data to the new structure.
6. API documentation is updated to reflect the changes.
7. All tests pass, including new tests for the Environment entity and updated relationship tests.
8. Circular dependencies between Assessment and Campaign models are resolved.

## Technical Considerations

1. **Database Impact**: This change will require significant database schema changes and data migration.
2. **API Compatibility**: Existing API consumers may need to update their integration to accommodate the new structure.
3. **Performance**: The additional relationships may impact query performance, requiring optimization.
4. **Security**: Access control must be updated to reflect the new hierarchy.
5. **Circular Dependencies**: The current circular references between Assessment and Campaign models need to be resolved to improve code maintainability.

## Timeline

- Phase 1 (Week 1): Implement Environment entity and update data models
- Phase 2 (Week 2): Update API endpoints and service layer
- Phase 3 (Week 3): Create migration scripts and test data migration
- Phase 4 (Week 4): Update documentation and perform final testing
- Phase 5 (Week 5): Resolve circular dependencies and ensure proper hierarchical relationships

## Success Metrics

1. All existing functionality continues to work with the new structure.
2. New hierarchical queries (e.g., "get all test cases for an environment") are efficient.
3. User feedback indicates the new structure is more intuitive and aligns with their mental model.
4. No data loss during migration.
5. No circular dependencies in the data model.

## Risks and Mitigations

| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| Data loss during migration | High | Medium | Create comprehensive backup before migration, implement rollback capability |
| API breaking changes | Medium | High | Provide backward compatibility layer, communicate changes to API consumers |
| Performance degradation | Medium | Medium | Optimize queries, add appropriate indexes |
| Increased complexity | Low | High | Provide clear documentation, implement intuitive API design |
| Circular dependencies | Medium | High | Carefully design relationships, use proper ORM patterns, implement clear hierarchy |

## Dependencies

1. Existing Assessment Management API
2. Existing Campaign Management API
3. Database migration framework
4. Authentication and authorization system

## Open Questions

1. Should we implement a versioning system for Environments?
2. How should we handle existing Assessments that don't logically fit into any Environment?
3. Should we allow Campaigns to exist without an Assessment in the new structure?
4. How will this change affect reporting and analytics features? 