# UI Components Documentation

## Overview

The Regression Rigor frontend is built using a combination of Flask templates and modern JavaScript. The UI is designed to be responsive, accessible, and user-friendly.

## Component Architecture

The UI follows a component-based architecture with the following layers:

1. **Layout Components**: Define the overall structure of the application
2. **Page Components**: Represent individual pages or views
3. **Feature Components**: Implement specific features or functionality
4. **Common Components**: Reusable UI elements used across the application

## Layout Components

### MainLayout

The main layout component that wraps all pages and provides the common structure.

**Features:**
- Responsive navigation bar
- Sidebar for main navigation
- Footer with application information
- Authentication state management
- Notifications area

**File Location:** `templates/layouts/main.html`

### AuthLayout

A simplified layout for authentication-related pages.

**Features:**
- Centered content area
- Logo and branding
- Minimal navigation

**File Location:** `templates/layouts/auth.html`

## Page Components

### Dashboard

The main dashboard page that displays an overview of the application.

**Features:**
- Summary statistics
- Recent activity
- Quick access to common actions
- Status indicators

**File Location:** `templates/pages/dashboard.html`

**API Dependencies:**
- `GET /api/v1/admin/dashboard/`

### User Management

Page for managing users in the system.

**Features:**
- User listing with pagination
- User creation form
- User editing capabilities
- User deletion with confirmation
- Role assignment

**File Location:** `templates/pages/users.html`

**API Dependencies:**
- `GET /api/v1/users/`
- `POST /api/v1/users/`
- `PUT /api/v1/users/{user_id}/`
- `DELETE /api/v1/users/{user_id}/`

### MITRE ATT&CK Explorer

Page for exploring and managing MITRE ATT&CK techniques.

**Features:**
- Technique listing with filtering and search
- Technique details view
- Technique creation and editing
- Technique relationship visualization
- Technique tagging and categorization

**File Location:** `templates/pages/mitre.html`

**API Dependencies:**
- `GET /api/v1/mitre/techniques/`
- `GET /api/v1/mitre/techniques/{technique_id}/`
- `POST /api/v1/mitre/techniques/`
- `PUT /api/v1/mitre/techniques/{technique_id}/`
- `DELETE /api/v1/mitre/techniques/{technique_id}/`

### Error Logs

Page for viewing and managing error logs.

**Features:**
- Error listing with filtering and search
- Error details view
- Error resolution tracking
- Error statistics and trends

**File Location:** `templates/pages/errors.html`

**API Dependencies:**
- `GET /api/v1/error-handling/`
- `GET /api/v1/error-handling/{error_id}/`

### Profile

User profile page for managing account settings.

**Features:**
- Profile information display and editing
- Password change form
- Two-factor authentication setup
- API key management
- Session management

**File Location:** `templates/pages/profile.html`

**API Dependencies:**
- `GET /api/v1/users/{user_id}/`
- `PUT /api/v1/users/{user_id}/`
- `POST /api/v1/two-factor/setup/`
- `POST /api/v1/two-factor/verify/`
- `POST /api/v1/two-factor/disable/`

### Login

Login page for user authentication.

**Features:**
- Username/password login form
- Two-factor authentication input
- Password reset link
- Remember me functionality

**File Location:** `templates/pages/login.html`

**API Dependencies:**
- `POST /api/v1/auth/token/`
- `POST /api/v1/two-factor/verify/`

## Feature Components

### TechniqueGraph

Interactive graph visualization for MITRE ATT&CK techniques and their relationships.

**Features:**
- Force-directed graph layout
- Zoom and pan controls
- Node selection and highlighting
- Relationship visualization
- Filtering options

**File Location:** `static/js/components/technique-graph.js`

**API Dependencies:**
- `GET /api/v1/mitre/techniques/`

### DataTable

Reusable data table component with advanced features.

**Features:**
- Pagination
- Sorting
- Filtering
- Column customization
- Row selection
- Bulk actions
- Export functionality

**File Location:** `static/js/components/data-table.js`

### NotificationCenter

Component for displaying and managing notifications.

**Features:**
- Different notification types (info, success, warning, error)
- Auto-dismissal
- Action buttons
- Notification grouping
- Notification history

**File Location:** `static/js/components/notification-center.js`

### SearchBar

Advanced search component with autocomplete.

**Features:**
- Real-time suggestions
- Search history
- Advanced search syntax
- Filtering options
- Recent searches

**File Location:** `static/js/components/search-bar.js`

## Common Components

### Button

Reusable button component with various styles and states.

**Variants:**
- Primary
- Secondary
- Danger
- Success
- Link

**States:**
- Default
- Hover
- Active
- Disabled
- Loading

**File Location:** `static/css/components/button.css`

### Form Controls

Collection of form control components.

**Components:**
- Text Input
- Textarea
- Select
- Checkbox
- Radio Button
- Toggle Switch
- Date Picker
- File Upload

**File Location:** `static/css/components/forms.css`

### Modal

Reusable modal dialog component.

**Features:**
- Different sizes
- Customizable header and footer
- Close button
- Backdrop click handling
- Keyboard navigation

**File Location:** `static/js/components/modal.js`

### Card

Reusable card component for displaying content in a contained area.

**Variants:**
- Default
- Elevated
- Outlined
- Interactive

**File Location:** `static/css/components/card.css`

### Alert

Component for displaying alert messages.

**Variants:**
- Info
- Success
- Warning
- Error

**Features:**
- Dismissible
- Icon support
- Action buttons
- Timeout auto-dismissal

**File Location:** `static/css/components/alert.css`

## Component Interactions

### Authentication Flow

1. User enters credentials on the Login page
2. Login component sends authentication request to the API
3. If 2FA is required, the 2FA input is displayed
4. On successful authentication, the user is redirected to the Dashboard
5. MainLayout updates to show authenticated user information
6. NotificationCenter displays a success message

### Data Management Flow

1. User navigates to a data management page (e.g., User Management)
2. DataTable component loads data from the API
3. User can filter, sort, and paginate the data
4. User can select rows for bulk actions
5. User can click on a row to view details
6. User can edit or delete items through modal dialogs
7. NotificationCenter displays success or error messages

### Visualization Flow

1. User navigates to the MITRE ATT&CK Explorer
2. TechniqueGraph component loads data from the API
3. User can interact with the graph to explore relationships
4. User can filter the graph by various criteria
5. User can click on a node to view technique details
6. User can edit technique relationships through a modal dialog

## Responsive Design

The UI is designed to be responsive and work well on different screen sizes:

- **Desktop**: Full-featured interface with sidebar navigation
- **Tablet**: Collapsible sidebar and optimized layouts
- **Mobile**: Hamburger menu navigation and stacked layouts

## Accessibility

The UI follows accessibility best practices:

- Proper semantic HTML
- ARIA attributes where needed
- Keyboard navigation support
- Color contrast compliance
- Screen reader compatibility
- Focus management

## Theming

The UI supports theming with:

- Light and dark mode
- Color scheme customization
- Font size adjustments
- Spacing customization

## Performance Optimization

The UI is optimized for performance:

- Lazy loading of components
- Code splitting
- Asset optimization
- Caching strategies
- Minimal dependencies 