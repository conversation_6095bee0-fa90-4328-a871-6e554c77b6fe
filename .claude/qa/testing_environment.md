# Testing Environment Documentation

This document explains the setup and usage of the testing environment in Docker, including the recent improvements made in version v0.2.1.

## Overview

The RegressionRigor project uses Docker containers for development and testing, with a focus on ensuring consistent and reliable test execution across different environments. The testing environment has been significantly improved to address several issues related to circular imports, SQLAlchemy table definition conflicts, and Flask-SQLAlchemy dependencies.

## Docker Container Setup

The project uses the following Docker containers:
- `regrigor-api` - FastAPI backend container (port 8010)
- `regrigor-web` - Flask frontend container (port 3010)
- `regrigor-redis` - Redis container (port 9010)
- `regrigor-postgres` - PostgreSQL database container (port 5440)

These containers are defined in the `.dockerwrapper/docker-compose.yml` file and can be started using:

```bash
docker-compose -f .dockerwrapper/docker-compose.yml up -d
```

## Testing Improvements (v0.2.1)

### Issues Addressed

1. **Circular Import Issues**
   - Problem: Circular imports between models caused conflicts in SQLAlchemy table definitions
   - Solution: Created a test-specific environment with isolated imports and modified table definitions

2. **Table Definition Conflicts**
   - Problem: "Table already defined" errors occurred due to multiple imports of model definitions
   - Solution: Added `extend_existing=True` to table definitions to allow redefinition

3. **Flask-SQLAlchemy Dependency**
   - Problem: Some models required Flask-SQLAlchemy, which is not always available in test environments
   - Solution: Created a mock Flask-SQLAlchemy module that provides necessary functionality

### Implementation Details

#### 1. Mock Flask-SQLAlchemy Module

We created a mock Flask-SQLAlchemy module that provides the necessary classes and functions for models to work without Flask:

```python
# Location: /tmp/mock_modules/flask_sqlalchemy/__init__.py
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, backref
from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Boolean, Text, JSON

class SQLAlchemy:
    def __init__(self, model_class=None, **kwargs):
        self.Model = model_class or declarative_base()
        
    def create_all(self):
        pass
        
    def drop_all(self):
        pass
        
    def session(self):
        from sqlalchemy.orm import Session
        return Session()

# Create a default instance
db = SQLAlchemy()
```

#### 2. Fixed Assessment Model

We modified the Assessment model to include `extend_existing=True` in the table definitions:

```python
class Assessment(Base, SoftDeleteMixin):
    __tablename__ = "assessments"
    __table_args__ = {"extend_existing": True}
    # ... rest of model definition
```

#### 3. Separate Test Environment

We created a separate test environment that doesn't rely on the existing conftest.py file:

```python
# tests/conftest_fixed.py
# ... config and imports ...

# Create a test-specific Base class and engine
metadata = MetaData(naming_convention=convention)
TestBase = declarative_base(metadata=metadata)

# Create a test engine
test_engine = create_engine(DATABASE_URL, ...)

# Import only necessary models
from api.models.user import User, UserRole
```

## Running Tests

### Standalone Tests

To run standalone tests that don't rely on models:

```bash
docker exec -it regrigor-api bash -c "cd /tmp/standalone_tests && python -m pytest test_standalone.py -v"
```

### Fixed Model Tests

To run tests with our fixed models:

```bash
docker exec -it regrigor-api bash -c "cd /app && python run_fixed_tests.py"
```

## Future Improvements

For a more permanent solution, consider:

1. Refactoring models to avoid circular imports
2. Adding `extend_existing=True` to all table definitions in original code
3. Ensuring Flask-SQLAlchemy is properly installed in Docker
4. Creating a more robust test setup that doesn't rely on the production code

## Conclusion

The testing environment improvements in v0.2.1 provide a reliable way to run tests in the Docker container without modifying the original codebase. This approach allows for consistent testing across different environments and serves as a foundation for more extensive test coverage. 