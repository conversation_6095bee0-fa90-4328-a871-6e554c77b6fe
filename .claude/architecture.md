# Regression Rigor Architecture

## Overview

Regression Rigor is a comprehensive cybersecurity data platform designed for managing security testing campaigns and integrating with the MITRE ATT&CK framework. The application follows a modern microservices architecture with clear separation of concerns.

## System Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Flask Frontend │────▶│  FastAPI Backend│────▶│   PostgreSQL    │
│    (Port 3010)  │     │    (Port 8010)  │     │   (Port 5440)   │
│                 │     │                 │     │                 │
└─────────────────┘     └────────┬────────┘     └─────────────────┘
                                 │
                                 ▼
                        ┌─────────────────┐
                        │                 │
                        │  Redis Cache    │
                        │   (Port 9010)   │
                        │                 │
                        └─────────────────┘
```

## Component Relationships

### Frontend (Flask)

The Flask frontend serves as the user interface for the application. It communicates with the FastAPI backend to retrieve and manipulate data.

**Key Responsibilities:**
- Render HTML templates and serve static assets
- Handle user authentication and session management
- Make API calls to the backend for data operations
- Implement user interface logic and form handling

**Dependencies:**
- FastAPI Backend: For all data operations and business logic
- PostgreSQL: For session storage (via the backend)
- Redis: For caching and rate limiting (via the backend)

### Backend (FastAPI)

The FastAPI backend serves as the API layer and implements the core business logic of the application.

**Key Responsibilities:**
- Expose RESTful API endpoints for data operations
- Implement business logic and validation
- Handle database operations
- Manage authentication and authorization
- Integrate with external services and APIs
- Implement rate limiting and caching

**Dependencies:**
- PostgreSQL: For data persistence
- Redis: For caching, rate limiting, and background tasks

### Database (PostgreSQL)

PostgreSQL serves as the primary data store for the application.

**Key Responsibilities:**
- Store application data
- Maintain data integrity through constraints and relationships
- Support complex queries and transactions
- Provide backup and recovery capabilities

### Cache (Redis)

Redis serves as a caching layer and supports various auxiliary functions.

**Key Responsibilities:**
- Cache frequently accessed data
- Support rate limiting for API endpoints
- Store session data
- Facilitate background task queuing and processing

## Data Flow

1. **User Request Flow:**
   - User interacts with the Flask frontend
   - Frontend makes API calls to the FastAPI backend
   - Backend processes the request, interacting with PostgreSQL and Redis as needed
   - Backend returns data to the frontend
   - Frontend renders the response to the user

2. **Authentication Flow:**
   - User submits credentials to the frontend
   - Frontend forwards credentials to the backend
   - Backend validates credentials against the database
   - Backend generates JWT token and returns it to the frontend
   - Frontend stores the token and includes it in subsequent requests

3. **Data Modification Flow:**
   - User submits form data to the frontend
   - Frontend validates input and sends it to the backend
   - Backend performs additional validation and business logic
   - Backend updates the database
   - Backend invalidates relevant cache entries
   - Backend returns success/failure to the frontend
   - Frontend updates the UI accordingly

## Deployment Architecture

The application is containerized using Docker, with each component running in its own container:

- **regrigor-web**: Flask frontend container
- **regrigor-api**: FastAPI backend container
- **regrigor-postgres**: PostgreSQL database container
- **regrigor-redis**: Redis cache container

Docker Compose is used to orchestrate the containers and manage their interactions.

## Security Architecture

1. **Authentication**: JWT-based authentication with optional two-factor authentication
2. **Authorization**: Role-based access control (RBAC) for API endpoints
3. **API Security**: Rate limiting, input validation, and error handling
4. **Data Security**: Encrypted connections and secure credential storage

## Scalability Considerations

- Horizontal scaling of the frontend and backend containers
- Database read replicas for scaling read operations
- Redis clustering for cache scalability
- Load balancing for distributing traffic

## Monitoring and Observability

- Logging: Centralized logging with structured log format
- Metrics: Application and system metrics collection
- Tracing: Request tracing for performance analysis
- Alerting: Automated alerts for system issues

## Disaster Recovery

- Database backups: Regular automated backups
- High availability: Redundant components where possible
- Failover mechanisms: Automatic failover for critical components
- Recovery procedures: Documented recovery processes 