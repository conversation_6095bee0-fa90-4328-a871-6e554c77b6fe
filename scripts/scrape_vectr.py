import trafilatura
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_vectr_features():
    """Scrape features from vectr.io."""
    url = "https://vectr.io/features/"
    logger.info(f"Fetching content from {url}")
    
    downloaded = trafilatura.fetch_url(url)
    if downloaded:
        text = trafilatura.extract(downloaded)
        logger.info("Content extracted successfully")
        return text
    else:
        logger.error("Failed to download content")
        return None

if __name__ == "__main__":
    content = get_vectr_features()
    if content:
        print(content)
