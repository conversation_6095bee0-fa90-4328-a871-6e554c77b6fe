#!/usr/bin/env python3
"""Script to generate test and documentation coverage reports.

This script runs pytest with coverage and interrogate to generate comprehensive
coverage reports for both tests and documentation. It will generate both reports
independently, ensuring documentation coverage is calculated even if tests fail.
"""
import subprocess
import sys
from pathlib import Path
from datetime import datetime
import shutil
import logging
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_command(command, description):
    """Run a shell command and handle its output.

    Args:
        command: The command to run as a list of strings
        description: Description of what the command does

    Returns:
        tuple: (success, output_text, error_text)
    """
    print(f"\n=== Running {description} ===")
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        return True, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        print(f"Error running {description}:")
        print(e.stdout)
        print(e.stderr, file=sys.stderr)
        return False, e.stdout, e.stderr

def ensure_directory(path):
    """Ensure a directory exists, creating it if necessary.

    Args:
        path: Path to the directory to ensure exists
    """
    try:
        path.mkdir(parents=True, exist_ok=True)
        logger.info(f"Ensured directory exists: {path}")
    except Exception as e:
        logger.error(f"Failed to create directory {path}: {e}")
        raise

def safe_move(src, dst):
    """Safely move a file or directory.

    Args:
        src: Source path
        dst: Destination path

    Returns:
        bool: True if move succeeded, False otherwise
    """
    try:
        if dst.exists():
            if dst.is_dir():
                shutil.rmtree(dst)
            else:
                dst.unlink()
        shutil.move(str(src), str(dst))
        logger.info(f"Successfully moved {src} to {dst}")
        return True
    except Exception as e:
        logger.error(f"Failed to move {src} to {dst}: {e}")
        return False

def copy_coverage_files(doc_cov_dir):
    """Copy documentation coverage files to the report directory.

    Args:
        doc_cov_dir: Path to the documentation coverage directory

    Returns:
        bool: True if all files were copied successfully
    """
    success = True
    badge_source = Path("docs/interrogate_badge.svg")
    if badge_source.exists():
        badge_dest = doc_cov_dir / "interrogate_badge.svg"
        if not safe_move(badge_source, badge_dest):
            logger.warning("Failed to move documentation badge")
            success = False

    return success

def parse_doc_coverage(output):
    """Parse documentation coverage percentage from interrogate output.

    Args:
        output: String output from interrogate command

    Returns:
        tuple: (coverage_percentage, total_files, covered_files)
    """
    try:
        coverage = {
            'percentage': None,
            'total_files': 0,
            'covered_files': 0,
            'module_details': []
        }

        for line in output.split('\n'):
            # Get overall coverage
            if 'actual: ' in line and '%' in line:
                coverage['percentage'] = float(line.split('actual: ')[1].split('%')[0])

            # Parse file-level details
            if '.py' in line and '%' in line:
                # Example line: "api/routes/v1.py - 67%"
                parts = line.strip().split(' - ')
                if len(parts) == 2:
                    file_path = parts[0]
                    file_coverage = float(parts[1].strip('%'))
                    coverage['module_details'].append({
                        'file': file_path,
                        'coverage': file_coverage
                    })
                    if file_coverage == 100:
                        coverage['covered_files'] += 1
                    coverage['total_files'] += 1

        return coverage
    except Exception as e:
        logger.error(f"Failed to parse documentation coverage: {e}")
        return None

def write_coverage_summary(summary_path, timestamp, test_success, doc_success, test_cov_dir, doc_cov_dir, doc_coverage):
    """Write a summary of the coverage reports.

    Args:
        summary_path: Path to write the summary file
        timestamp: Timestamp for the report
        test_success: Whether test coverage generation succeeded
        doc_success: Whether documentation coverage generation succeeded
        test_cov_dir: Directory containing test coverage reports
        doc_cov_dir: Directory containing documentation coverage reports
        doc_coverage: Documentation coverage percentage
    """
    try:
        with open(summary_path, "w") as f:
            f.write(f"Coverage Reports Summary ({timestamp})\n")
            f.write("=" * 50 + "\n\n")

            # Test Coverage Section
            f.write("Test Coverage Report\n")
            f.write("-" * 20 + "\n")
            f.write(f"Status: {'SUCCESS' if test_success else 'FAILED'}\n")
            f.write(f"Location: {test_cov_dir if test_success else 'N/A'}\n\n")

            # Documentation Coverage Section
            f.write("Documentation Coverage Report\n")
            f.write("-" * 30 + "\n")
            f.write(f"Status: {'SUCCESS' if doc_success else 'WARNING: Below Threshold'}\n")
            if doc_coverage:
                f.write(f"Overall Coverage: {doc_coverage['percentage']:.1f}%\n")
                f.write(f"Files Analyzed: {doc_coverage['total_files']}\n")
                f.write(f"Fully Documented Files: {doc_coverage['covered_files']}\n")

                if doc_coverage['module_details']:
                    f.write("\nModule-level Coverage:\n")
                    for module in sorted(doc_coverage['module_details'], 
                                      key=lambda x: x['coverage']):
                        f.write(f"  {module['file']}: {module['coverage']:.1f}%\n")

            f.write(f"\nLocation: {doc_cov_dir if doc_success else 'N/A'}\n")

            # Recommendations
            f.write("\nRecommendations:\n")
            if not test_success:
                f.write("- Fix failing tests to improve test coverage\n")
            if doc_coverage and doc_coverage['percentage'] < 95:
                f.write("- Improve documentation coverage to meet 95% threshold\n")
                if doc_coverage['module_details']:
                    lowest_coverage = min(doc_coverage['module_details'], 
                                       key=lambda x: x['coverage'])
                    f.write(f"  Priority: {lowest_coverage['file']} "
                           f"({lowest_coverage['coverage']:.1f}%)\n")

        logger.info(f"Coverage summary written to {summary_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to write summary report: {e}")
        return False

def main():
    """Generate both test and documentation coverage reports.

    Returns:
        int: 0 if both reports generated successfully, 1 if either failed,
             2 if both failed
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    reports_dir = Path("coverage_reports") / timestamp
    docs_dir = Path("docs")

    failures = 0

    try:
        # Ensure required directories exist
        ensure_directory(reports_dir)
        ensure_directory(docs_dir)

        # Run test coverage - continue even if it fails
        test_success, test_stdout, test_stderr = run_command(
            ["pytest"],
            "test coverage"
        )

        test_cov_dir = reports_dir / "test_coverage"
        if test_success and Path("htmlcov").exists():
            if safe_move(Path("htmlcov"), test_cov_dir):
                print(f"\nTest coverage report generated in {test_cov_dir}")
            else:
                test_success = False
                failures += 1
                print("\nFailed to move test coverage report")
        else:
            failures += 1
            print("\nTest coverage report generation failed")

        # Run documentation coverage independently
        doc_cov_dir = reports_dir / "doc_coverage"
        ensure_directory(doc_cov_dir)

        doc_success, doc_stdout, doc_stderr = run_command(
            ["interrogate", "api", "-v", "-o", str(doc_cov_dir / "coverage.txt")],
            "documentation coverage"
        )

        # Check if documentation coverage files were generated despite failing threshold
        doc_cov_file = doc_cov_dir / "coverage.txt"
        if doc_cov_file.exists():
            if not doc_success:
                logger.warning("Documentation coverage is below the threshold but report was generated")
            doc_success = True  # Consider it a success if files were generated
            doc_coverage = parse_doc_coverage(doc_stdout)
            copy_coverage_files(doc_cov_dir)
            print(f"\nDocumentation coverage report generated in {doc_cov_dir}")
        else:
            failures += 1
            doc_coverage = None
            print("\nDocumentation coverage report generation failed")

        # Generate summary report
        summary_path = reports_dir / "coverage_summary.txt"
        if not write_coverage_summary(
            summary_path, timestamp, test_success, doc_success,
            test_cov_dir, doc_cov_dir, doc_coverage
        ):
            failures += 1

    except Exception as e:
        logger.error(f"Unexpected error during report generation: {e}")
        return 2

    if failures == 0:
        print("\nAll coverage reports generated successfully")
        return 0
    elif failures == 1:
        print("\nOne coverage report failed to generate")
        return 1
    else:
        print("\nBoth coverage reports failed to generate")
        return 2

if __name__ == "__main__":
    sys.exit(main())