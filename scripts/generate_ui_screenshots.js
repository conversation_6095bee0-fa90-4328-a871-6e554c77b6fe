/**
 * Assessment Management UI Screenshot Generator
 * 
 * This script helps to generate screenshots of the Assessment Management UI
 * for documentation purposes. It uses <PERSON><PERSON> to automate browser interactions
 * and capture screenshots of key UI components.
 * 
 * Usage:
 * 1. Make sure you have Node.js installed
 * 2. Install dependencies: npm install @playwright/test
 * 3. Run: node generate_ui_screenshots.js
 */

const { chromium } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

// Create screenshots directory if it doesn't exist
const screenshotsDir = path.join(__dirname, '../docs/screenshots');
if (!fs.existsSync(screenshotsDir)) {
  fs.mkdirSync(screenshotsDir, { recursive: true });
}

async function generateScreenshots() {
  console.log('Launching browser...');
  const browser = await chromium.launch({
    headless: false
  });

  const context = await browser.newContext({
    viewport: { width: 1280, height: 800 }
  });
  
  const page = await context.newPage();
  
  try {
    // Login to the application
    console.log('Logging in...');
    await page.goto('http://localhost:3000/login');
    await page.waitForSelector('input[name="username"]');
    await page.fill('input[name="username"]', 'admin');
    await page.fill('input[name="password"]', 'password');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();

    // Wait for dashboard to load
    await page.waitForSelector('.dashboard-container');
    
    // Navigate to Assessments page
    console.log('Navigating to Assessments page...');
    await page.click('a[href="/assessments"]');
    await page.waitForSelector('.table-container');
    await page.waitForTimeout(1000); // Wait for animations to complete
    
    // Take screenshot of the assessments list view
    console.log('Capturing Assessments List View...');
    await page.screenshot({ path: path.join(screenshotsDir, '01-assessments-list.png'), fullPage: true });
    
    // Open filter panel
    console.log('Capturing Filter Panel...');
    await page.click('button:has-text("Filters")');
    await page.waitForTimeout(500);
    await page.screenshot({ path: path.join(screenshotsDir, '02-assessments-filters.png') });
    
    // Navigate to the first assessment detail page
    console.log('Navigating to Assessment Detail...');
    await page.click('table a[href^="/assessments/"]');
    await page.waitForSelector('div[role="tabpanel"]');
    await page.waitForTimeout(1000);
    await page.screenshot({ path: path.join(screenshotsDir, '03-assessment-detail-overview.png'), fullPage: true });
    
    // Switch to Test Executions tab
    console.log('Capturing Test Executions Tab...');
    await page.click('button[role="tab"]:nth-child(2)');
    await page.waitForTimeout(1000);
    await page.screenshot({ path: path.join(screenshotsDir, '04-assessment-test-executions.png'), fullPage: true });
    
    // Switch to Campaigns tab
    console.log('Capturing Campaigns Tab...');
    await page.click('button[role="tab"]:nth-child(3)');
    await page.waitForTimeout(1000);
    await page.screenshot({ path: path.join(screenshotsDir, '05-assessment-campaigns.png'), fullPage: true });
    
    // Switch to Report tab
    console.log('Capturing Report Tab...');
    await page.click('button[role="tab"]:nth-child(4)');
    await page.waitForTimeout(2000); // Wait for report data to load
    await page.screenshot({ path: path.join(screenshotsDir, '06-assessment-report-tab.png'), fullPage: true });
    
    // Navigate to full report view
    console.log('Navigating to Full Report...');
    await page.click('button:has-text("View Full Report")');
    await page.waitForTimeout(2000);
    await page.screenshot({ path: path.join(screenshotsDir, '07-full-report.png'), fullPage: true });
    
    // Capture Findings section expanded
    console.log('Capturing Expanded Findings...');
    await page.click('.findings-section .MuiAccordionSummary-root');
    await page.waitForTimeout(500);
    await page.screenshot({ path: path.join(screenshotsDir, '08-findings-expanded.png') });
    
    // Go back to assessments list
    console.log('Going back to Assessments list...');
    await page.goto('http://localhost:3000/assessments');
    await page.waitForSelector('.table-container');
    
    // Navigate to create assessment form
    console.log('Navigating to Create Assessment form...');
    await page.click('button:has-text("New Assessment")');
    await page.waitForSelector('form');
    await page.waitForTimeout(1000);
    await page.screenshot({ path: path.join(screenshotsDir, '09-create-assessment-form.png'), fullPage: true });
    
    // Fill out form with sample data
    console.log('Filling out Assessment form...');
    await page.fill('input[name="name"]', 'Sample Assessment');
    await page.fill('textarea[name="description"]', 'This is a sample assessment created for documentation purposes.');
    await page.click('input[name="target_system"]');
    await page.fill('input[name="target_system"]', 'Web Application');
    
    // Capture form with validation
    console.log('Capturing form validation...');
    await page.click('button[type="submit"]');
    await page.waitForTimeout(500);
    await page.screenshot({ path: path.join(screenshotsDir, '10-form-validation.png') });

    console.log('Screenshot generation complete!');
    console.log(`Screenshots saved to ${screenshotsDir}`);
    
  } catch (error) {
    console.error('Error generating screenshots:', error);
  } finally {
    await browser.close();
  }
}

generateScreenshots().catch(console.error); 