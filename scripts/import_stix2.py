#!/usr/bin/env python3
"""
Script to import STIX 2.1 data.

This script imports STIX 2.1 data from a file or URL into the database.
"""
import os
import sys
import logging
import argparse
import requests
import json
import tempfile
from datetime import datetime

# Add project root to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from api.database import SessionLocal
from api.utils.stix_utils import validate_stix_bundle, import_stix_bundle

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def download_stix_data(url, output_path=None):
    """
    Download STIX data from a URL.
    
    Args:
        url: URL to download the data from
        output_path: Path to save the downloaded data to
        
    Returns:
        dict: The downloaded STIX data
    """
    logger.info(f"Downloading STIX data from {url}")
    
    try:
        response = requests.get(url, timeout=60)
        response.raise_for_status()
        data = response.json()
        
        # Save to file if output_path is provided
        if output_path:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            with open(output_path, 'w') as f:
                json.dump(data, f, indent=2)
            logger.info(f"Saved STIX data to {output_path}")
            
        return data
    except requests.exceptions.RequestException as e:
        logger.error(f"Error downloading STIX data: {e}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing STIX data: {e}")
        sys.exit(1)

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Import STIX 2.1 data")
    parser.add_argument(
        "--url",
        help="URL to download the STIX data from"
    )
    parser.add_argument(
        "--file",
        help="Path to a local STIX bundle file"
    )
    parser.add_argument(
        "--output",
        help="Path to save the downloaded data to"
    )
    
    args = parser.parse_args()
    
    if not args.url and not args.file:
        logger.error("Either --url or --file must be provided")
        parser.print_help()
        sys.exit(1)
    
    try:
        # Get STIX data from URL or file
        if args.url:
            data = download_stix_data(args.url, args.output)
        else:
            logger.info(f"Loading STIX data from {args.file}")
            try:
                with open(args.file, 'r') as f:
                    data = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError) as e:
                logger.error(f"Error loading STIX data: {e}")
                sys.exit(1)
        
        # Validate STIX bundle
        try:
            bundle = validate_stix_bundle(data)
            logger.info(f"Valid STIX bundle with {len(bundle.objects)} objects")
        except ValueError as e:
            logger.error(f"Invalid STIX bundle: {e}")
            sys.exit(1)
            
        # Import STIX bundle
        db = SessionLocal()
        try:
            logger.info("Importing STIX data into database")
            objects = import_stix_bundle(db, bundle)
            logger.info(f"Successfully imported {len(objects)} STIX objects")
        except Exception as e:
            logger.error(f"Error importing STIX data: {e}")
            sys.exit(1)
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 