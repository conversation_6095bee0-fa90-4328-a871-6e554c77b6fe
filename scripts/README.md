# UI Documentation Scripts

This directory contains scripts for maintaining and generating documentation for the UI components.

## Screenshot Generator

The `generate_ui_screenshots.js` script is designed to automate the process of creating screenshots for UI documentation. This is particularly useful for:

- Creating visual documentation for new features
- Updating documentation when UI changes
- Ensuring consistency in documentation visuals

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

Install the required dependencies:

```bash
npm install @playwright/test
# or
yarn add @playwright/test
```

### Usage

1. Make sure the application is running locally on port 3000
2. Run the screenshot generator:

```bash
node scripts/generate_ui_screenshots.js
```

3. The script will automatically:
   - Launch a Chromium browser
   - Log in to the application
   - Navigate through the Assessment Management features
   - Take screenshots at key points
   - Save the screenshots to the `docs/screenshots` directory

### Output

The script generates the following screenshots:

1. `01-assessments-list.png` - Main assessments list view
2. `02-assessments-filters.png` - Filter panel expanded
3. `03-assessment-detail-overview.png` - Assessment detail overview tab
4. `04-assessment-test-executions.png` - Test executions tab
5. `05-assessment-campaigns.png` - Campaigns tab
6. `06-assessment-report-tab.png` - Report tab in detail view
7. `07-full-report.png` - Full report view
8. `08-findings-expanded.png` - Expanded findings section
9. `09-create-assessment-form.png` - Create assessment form
10. `10-form-validation.png` - Form validation in action

### Customization

You can modify the script to:
- Change the screenshot dimensions
- Add more UI components to capture
- Switch browsers (Playwright supports Chromium, Firefox, and WebKit)
- Customize the login credentials
- Change the output directory

### Troubleshooting

If you encounter issues:

1. Make sure the application is running and accessible
2. Check that the selectors in the script match your current UI implementation
3. Try running with headless mode disabled to see what's happening in the browser
4. Increase timeout values if pages are loading slowly
5. If elements aren't being found, try using Playwright's built-in debugging tools:
   ```javascript
   // Add this to the script for debugging
   await page.pause(); // This will pause execution and open Playwright Inspector
   ```

### Including Screenshots in Documentation

Use the generated screenshots in your markdown documentation:

```markdown
![Assessments List](../screenshots/01-assessments-list.png)
``` 