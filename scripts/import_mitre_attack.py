#!/usr/bin/env python3
"""
Script to import the latest version of MITRE ATT&CK.

This script downloads the latest MITRE ATT&CK Enterprise data and imports it into the database.
"""
import os
import sys
import logging
import argparse
import requests
import json
from datetime import datetime

# Add project root to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from api.database import SessionLocal
from api.utils.mitre_import import import_mitre_data, validate_mitre_data

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# MITRE ATT&CK Enterprise STIX URL
MITRE_ENTERPRISE_URL = "https://raw.githubusercontent.com/mitre/cti/master/enterprise-attack/enterprise-attack.json"

def download_mitre_data(url=MITRE_ENTERPRISE_URL, output_path=None):
    """
    Download the latest MITRE ATT&CK data.
    
    Args:
        url: URL to download the data from
        output_path: Path to save the downloaded data to
        
    Returns:
        dict: The downloaded MITRE ATT&CK data
    """
    logger.info(f"Downloading MITRE ATT&CK data from {url}")
    
    try:
        response = requests.get(url, timeout=60)
        response.raise_for_status()
        data = response.json()
        
        # Save to file if output_path is provided
        if output_path:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            with open(output_path, 'w') as f:
                json.dump(data, f, indent=2)
            logger.info(f"Saved MITRE ATT&CK data to {output_path}")
            
        return data
    except requests.exceptions.RequestException as e:
        logger.error(f"Error downloading MITRE ATT&CK data: {e}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing MITRE ATT&CK data: {e}")
        sys.exit(1)

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Import the latest MITRE ATT&CK data")
    parser.add_argument(
        "--url", 
        default=MITRE_ENTERPRISE_URL,
        help="URL to download the MITRE ATT&CK data from"
    )
    parser.add_argument(
        "--output", 
        default="3rdparty/mitre-cti/enterprise-attack/enterprise-attack.json",
        help="Path to save the downloaded data to"
    )
    parser.add_argument(
        "--force-update", 
        action="store_true",
        help="Force update if version already exists"
    )
    parser.add_argument(
        "--skip-download", 
        action="store_true",
        help="Skip downloading and use existing file"
    )
    
    args = parser.parse_args()
    
    try:
        # Download or load data
        if args.skip_download:
            logger.info(f"Using existing MITRE ATT&CK data from {args.output}")
            try:
                with open(args.output, 'r') as f:
                    data = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError) as e:
                logger.error(f"Error loading existing MITRE ATT&CK data: {e}")
                sys.exit(1)
        else:
            data = download_mitre_data(args.url, args.output)
        
        # Validate data
        if not validate_mitre_data(data):
            logger.error("Invalid MITRE ATT&CK data")
            sys.exit(1)
            
        # Import data
        db = SessionLocal()
        try:
            logger.info("Importing MITRE ATT&CK data into database")
            version = import_mitre_data(db, set_as_current=True)
            logger.info(f"Successfully imported MITRE ATT&CK version {version.version}")
        except Exception as e:
            logger.error(f"Error importing MITRE ATT&CK data: {e}")
            sys.exit(1)
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 