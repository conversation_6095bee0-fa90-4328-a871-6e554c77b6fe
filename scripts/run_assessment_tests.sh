#!/bin/bash

# <PERSON>ript to run assessment tests for the Regression Rigor platform

# Display help if requested
if [ "$1" == "--help" ] || [ "$1" == "-h" ]; then
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --api             Run API tests only"
    echo "  --ui              Run UI tests only"
    echo "  --all             Run all tests (default)"
    echo "  --verbose, -v     Verbose output"
    echo "  --file=FILENAME   Run a specific test file"
    echo "  --testcase=NAME   Run a specific test by name (pattern matching)"
    echo "  --help, -h        Display this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --api                     Run all API tests"
    echo "  $0 --ui                      Run all UI tests"
    echo "  $0 --file=test_execution_api.py   Run a specific API test file"
    echo "  $0 --file=assessment-list.spec.js Run a specific UI test file"
    echo "  $0 --testcase=validation     Run tests matching 'validation' in the name"
    exit 0
fi

# Initialize variables
RUN_API=true
RUN_UI=true
VERBOSE=false
SPECIFIC_FILE=""
TESTCASE_PATTERN=""

# Parse arguments
for arg in "$@"; do
    case $arg in
        --api)
            RUN_API=true
            RUN_UI=false
            shift
            ;;
        --ui)
            RUN_API=false
            RUN_UI=true
            shift
            ;;
        --all)
            RUN_API=true
            RUN_UI=true
            shift
            ;;
        --verbose|-v)
            VERBOSE=true
            shift
            ;;
        --file=*)
            SPECIFIC_FILE="${arg#*=}"
            shift
            ;;
        --testcase=*)
            TESTCASE_PATTERN="${arg#*=}"
            shift
            ;;
        *)
            # Unknown option
            echo "Unknown option: $arg"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Set up output verbosity for pytest
PYTEST_VERBOSE=""
if [ "$VERBOSE" = true ]; then
    PYTEST_VERBOSE="-v"
fi

# Run API tests if selected
if [ "$RUN_API" = true ]; then
    echo "========================================"
    echo "Running Assessment API Tests"
    echo "========================================"
    
    # Check if a specific file was requested
    if [ -n "$SPECIFIC_FILE" ] && [[ "$SPECIFIC_FILE" == *".py" ]]; then
        # Find the test file in the tests directory
        TEST_FILE_PATH=$(find tests -name "$SPECIFIC_FILE" -type f)
        
        if [ -z "$TEST_FILE_PATH" ]; then
            echo "Error: Test file '$SPECIFIC_FILE' not found"
            exit 1
        fi
        
        # Run the specific test file
        if [ -n "$TESTCASE_PATTERN" ]; then
            # Run specific test case in the file
            echo "Running test case matching '$TESTCASE_PATTERN' in $TEST_FILE_PATH"
            python -m pytest "$TEST_FILE_PATH" -k "$TESTCASE_PATTERN" $PYTEST_VERBOSE
        else
            # Run all tests in the file
            echo "Running all tests in $TEST_FILE_PATH"
            python -m pytest "$TEST_FILE_PATH" $PYTEST_VERBOSE
        fi
    else
        # Run all assessment API tests if no specific file was provided
        echo "Running all assessment API tests..."
        if [ -n "$TESTCASE_PATTERN" ]; then
            # Run specific test cases across all files
            echo "Running test cases matching '$TESTCASE_PATTERN'"
            python -m pytest tests/test_assessment_api.py tests/test_assessment_api_extended.py tests/test_execution_api.py tests/test_bulk_assessment_operations.py -k "$TESTCASE_PATTERN" $PYTEST_VERBOSE
        else
            # Run all API tests
            python -m pytest tests/test_assessment_api.py tests/test_assessment_api_extended.py tests/test_execution_api.py tests/test_bulk_assessment_operations.py $PYTEST_VERBOSE
        fi
    fi
fi

# Run UI tests if selected
if [ "$RUN_UI" = true ]; then
    echo "========================================"
    echo "Running Assessment UI Tests"
    echo "========================================"
    
    # Check if we're in the project root directory
    if [ ! -d "ui" ]; then
        echo "Error: 'ui' directory not found. Make sure you're running this script from the project root."
        exit 1
    fi
    
    # Navigate to the UI directory
    cd ui
    
    # Check if Playwright is installed
    if ! npx playwright --version > /dev/null 2>&1; then
        echo "Error: Playwright not found. Please install it first:"
        echo "npx playwright install"
        exit 1
    fi
    
    # Check if a specific file was requested
    if [ -n "$SPECIFIC_FILE" ] && [[ "$SPECIFIC_FILE" == *".spec.js" ]]; then
        # Find the test file in the ui/tests directory
        UI_TEST_FILE_PATH=$(find tests -name "$SPECIFIC_FILE" -type f)
        
        if [ -z "$UI_TEST_FILE_PATH" ]; then
            echo "Error: UI test file '$SPECIFIC_FILE' not found"
            exit 1
        fi
        
        # Run the specific UI test file
        if [ -n "$TESTCASE_PATTERN" ]; then
            # Run specific test case in the file
            echo "Running UI test case matching '$TESTCASE_PATTERN' in $UI_TEST_FILE_PATH"
            npx playwright test "$UI_TEST_FILE_PATH" --grep="$TESTCASE_PATTERN"
        else
            # Run all tests in the file
            echo "Running all tests in $UI_TEST_FILE_PATH"
            npx playwright test "$UI_TEST_FILE_PATH"
        fi
    else
        # Run all assessment UI tests if no specific file was provided
        echo "Running all assessment UI tests..."
        if [ -n "$TESTCASE_PATTERN" ]; then
            # Run specific test cases across all files
            echo "Running UI test cases matching '$TESTCASE_PATTERN'"
            npx playwright test tests/assessments --grep="$TESTCASE_PATTERN"
        else
            # Run all UI tests
            npx playwright test tests/assessments
        fi
    fi
    
    # Return to the original directory
    cd ..
fi

echo "========================================"
echo "Assessment Tests Completed"
echo "========================================" 