#!/usr/bin/env python3
"""
Script to update all security frameworks.

This script updates MITRE ATT&CK, D3FEND, and STIX2 data in the database.
"""
import os
import sys
import logging
import argparse
import subprocess
import time
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Script paths
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
MITRE_SCRIPT = os.path.join(SCRIPT_DIR, "import_mitre_attack.py")
D3FEND_SCRIPT = os.path.join(SCRIPT_DIR, "import_d3fend.py")
STIX_SCRIPT = os.path.join(SCRIPT_DIR, "import_stix2.py")

def run_script(script_path, args=None):
    """
    Run a Python script with the given arguments.
    
    Args:
        script_path: Path to the script to run
        args: List of arguments to pass to the script
        
    Returns:
        bool: True if the script ran successfully, False otherwise
    """
    cmd = [sys.executable, script_path]
    if args:
        cmd.extend(args)
        
    logger.info(f"Running: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info(f"Output: {result.stdout}")
        if result.stderr:
            logger.warning(f"Stderr: {result.stderr}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Error running script: {e}")
        logger.error(f"Stdout: {e.stdout}")
        logger.error(f"Stderr: {e.stderr}")
        return False

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Update all security frameworks")
    parser.add_argument(
        "--mitre-only", 
        action="store_true",
        help="Only update MITRE ATT&CK"
    )
    parser.add_argument(
        "--d3fend-only", 
        action="store_true",
        help="Only update D3FEND"
    )
    parser.add_argument(
        "--stix-only", 
        action="store_true",
        help="Only update STIX2"
    )
    parser.add_argument(
        "--stix-url",
        help="URL to download STIX data from"
    )
    parser.add_argument(
        "--stix-file",
        help="Path to a local STIX bundle file"
    )
    parser.add_argument(
        "--force-update", 
        action="store_true",
        help="Force update if versions already exist"
    )
    parser.add_argument(
        "--skip-download", 
        action="store_true",
        help="Skip downloading and use existing files"
    )
    
    args = parser.parse_args()
    
    # Determine which frameworks to update
    update_mitre = not (args.d3fend_only or args.stix_only) or args.mitre_only
    update_d3fend = not (args.mitre_only or args.stix_only) or args.d3fend_only
    update_stix = not (args.mitre_only or args.d3fend_only) or args.stix_only
    
    # Track success/failure
    success = True
    
    # Update MITRE ATT&CK
    if update_mitre:
        logger.info("Updating MITRE ATT&CK...")
        mitre_args = []
        if args.force_update:
            mitre_args.append("--force-update")
        if args.skip_download:
            mitre_args.append("--skip-download")
            
        if not run_script(MITRE_SCRIPT, mitre_args):
            logger.error("Failed to update MITRE ATT&CK")
            success = False
        else:
            logger.info("Successfully updated MITRE ATT&CK")
    
    # Update D3FEND
    if update_d3fend:
        logger.info("Updating D3FEND...")
        d3fend_args = []
        if args.skip_download:
            d3fend_args.append("--skip-download")
            
        if not run_script(D3FEND_SCRIPT, d3fend_args):
            logger.error("Failed to update D3FEND")
            success = False
        else:
            logger.info("Successfully updated D3FEND")
    
    # Update STIX2
    if update_stix and (args.stix_url or args.stix_file):
        logger.info("Updating STIX2...")
        stix_args = []
        if args.stix_url:
            stix_args.extend(["--url", args.stix_url])
        if args.stix_file:
            stix_args.extend(["--file", args.stix_file])
            
        if not run_script(STIX_SCRIPT, stix_args):
            logger.error("Failed to update STIX2")
            success = False
        else:
            logger.info("Successfully updated STIX2")
    elif update_stix:
        logger.warning("Skipping STIX2 update: no URL or file provided")
    
    # Final status
    if success:
        logger.info("All requested framework updates completed successfully")
        return 0
    else:
        logger.error("One or more framework updates failed")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 