"""Seed script for ATLAS test data."""
import json
import os
import sys
from datetime import datetime
from pathlib import Path

import click
from sqlalchemy import create_engine
from sqlalchemy.orm import Session

# Add project root to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from api.models.atlas import (
    AtlasVersion,
    AtlasTactic, 
    AtlasTechnique,
    AtlasMatrix,
    AtlasMatrixItem
)

DEFAULT_ATLAS_MATRIX = "3rdparty/atlas-navigator-data/dist/default-navigator-layers/atlas_layer_matrix.json"

@click.command()
@click.option(
    '--database-url',
    default=lambda: os.environ.get('DATABASE_URL'),
    help='Database URL to seed'
)
@click.option(
    '--matrix-file',
    default=DEFAULT_ATLAS_MATRIX,
    help='Path to ATLAS matrix JSON file'
)
@click.option(
    '--force-update',
    is_flag=True,
    help='Force update if version exists'
)
def seed_database(database_url: str, matrix_file: str, force_update: bool):
    """Seed the database with ATLAS data."""
    if not database_url:
        click.echo("Error: DATABASE_URL environment variable not set")
        sys.exit(1)

    # Ensure matrix file exists
    matrix_path = Path(matrix_file)
    if not matrix_path.exists():
        click.echo(f"Error: Matrix file not found: {matrix_file}")
        sys.exit(1)

    try:
        # Load matrix data
        with open(matrix_path) as f:
            matrix_data = json.load(f)

        engine = create_engine(database_url)
        session = Session(engine)

        try:
            # Check for existing version
            version_id = matrix_data["metadata"][0]["value"]
            existing_version = session.query(AtlasVersion).filter_by(version=version_id).first()

            if existing_version and not force_update:
                click.echo(f"Version {version_id} already exists. Use --force-update to update it.")
                return

            # Create or update version
            if existing_version and force_update:
                # Delete existing data for this version
                session.query(AtlasMatrixItem).filter(
                    AtlasMatrixItem.matrix_id.in_(
                        session.query(AtlasMatrix.id).filter_by(version_id=existing_version.id)
                    )
                ).delete(synchronize_session=False)
                session.query(AtlasMatrix).filter_by(version_id=existing_version.id).delete()
                session.query(AtlasTechnique).filter_by(version_id=existing_version.id).delete()
                session.query(AtlasTactic).filter_by(version_id=existing_version.id).delete()

                # Update version record
                existing_version.name = matrix_data["name"]
                existing_version.description = matrix_data["description"]
                existing_version.import_date = datetime.utcnow()
                version = existing_version
            else:
                # Create new version
                version = AtlasVersion(
                    version=version_id,
                    name=matrix_data["name"],
                    description=matrix_data["description"],
                    import_date=datetime.utcnow(),
                    is_current=True
                )
                session.add(version)

            session.flush()

            # Create matrix
            matrix = AtlasMatrix(
                name=matrix_data["name"],
                description=matrix_data["description"],
                version_id=version.id
            )
            session.add(matrix)
            session.flush()

            # First pass: Extract unique tactics and create records
            tactics = {}
            click.echo("Processing tactics...")
            for technique in matrix_data["techniques"]:
                if "tactic" in technique and technique["tactic"]:
                    tactic_name = technique["tactic"]
                    if tactic_name not in tactics:
                        tactic = AtlasTactic(
                            external_id=f"TA{len(tactics):04d}",
                            name=tactic_name,
                            version_id=version.id
                        )
                        session.add(tactic)
                        tactics[tactic_name] = tactic
            session.flush()

            # Second pass: Create techniques and track parent-child relationships
            techniques = {}
            subtechniques = []
            click.echo("Processing techniques...")
            for technique in matrix_data["techniques"]:
                technique_id = technique["techniqueID"]
                is_subtechnique = technique_id.count('.') == 2

                # Extract name and description correctly
                name = technique.get("name", "") or technique.get("techniqueID", "")

                # Build description with URL if available
                description = technique.get("description", "")
                url = technique.get("url", "")
                if url:
                    description = f"{description}\n\nMore information: {url}"
                elif "metadata" in technique:
                    for meta in technique["metadata"]:
                        if meta.get("name") == "url" and meta.get("value"):
                            description = f"{description}\n\nMore information: {meta['value']}"

                # Create technique
                tech = AtlasTechnique(
                    external_id=technique_id,
                    name=name,
                    description=description,
                    is_subtechnique=is_subtechnique,
                    version_id=version.id
                )

                # Link to tactic if specified
                if "tactic" in technique and technique["tactic"]:
                    tech.tactic_id = tactics[technique["tactic"]].id

                session.add(tech)
                techniques[technique_id] = tech

                # Store subtechnique info for later linking
                if is_subtechnique:
                    parent_id = '.'.join(technique_id.split('.')[:2])
                    subtechniques.append((tech, parent_id))

            session.flush()

            # Third pass: Create matrix items for technique-tactic relationships
            click.echo("Creating matrix items...")
            for technique in matrix_data["techniques"]:
                technique_id = technique["techniqueID"]
                if "tactic" in technique and technique["tactic"]:
                    tech = techniques[technique_id]
                    tactic = tactics[technique["tactic"]]

                    matrix_item = AtlasMatrixItem(
                        matrix_id=matrix.id,
                        technique_id=tech.id,
                        tactic_id=tactic.id,
                        color=technique.get("color"),
                        show_subtechniques=technique.get("showSubtechniques", True)
                    )
                    session.add(matrix_item)

            # Fourth pass: Link subtechniques to parents
            click.echo("Linking subtechniques to parents...")
            for subtechnique, parent_id in subtechniques:
                if parent_id in techniques:
                    subtechnique.parent_technique_id = techniques[parent_id].id
                    click.echo(f"Linked {subtechnique.external_id} to parent {parent_id}")
                else:
                    click.echo(f"Warning: Parent {parent_id} not found for {subtechnique.external_id}")

            # Final commit
            session.commit()
            click.echo("Successfully seeded database with ATLAS data")

        except Exception as e:
            session.rollback()
            click.echo(f"Error seeding database: {str(e)}")
            raise
        finally:
            session.close()

    except Exception as e:
        click.echo(f"Error processing ATLAS data: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    seed_database()