#!/usr/bin/env python3
"""
<PERSON>ript to import the latest version of D3FEND.

This script downloads the latest D3FEND ontology and imports it into the database.
"""
import os
import sys
import logging
import argparse
import requests
import tempfile
from datetime import datetime

# Add project root to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from api.database import SessionLocal
from api.utils.d3fend_import import import_d3fend_ontology

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# D3FEND OWL URL
D3FEND_OWL_URL = "https://d3fend.mitre.org/ontology/d3fend.owl"

def download_d3fend_ontology(url=D3FEND_OWL_URL, output_path=None):
    """
    Download the latest D3FEND ontology.
    
    Args:
        url: URL to download the ontology from
        output_path: Path to save the downloaded ontology to
        
    Returns:
        str: Path to the downloaded ontology file
    """
    logger.info(f"Downloading D3FEND ontology from {url}")
    
    try:
        response = requests.get(url, timeout=60)
        response.raise_for_status()
        
        # If output_path is not provided, create a temporary file
        if not output_path:
            fd, output_path = tempfile.mkstemp(suffix=".owl")
            os.close(fd)
        else:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
        # Save the ontology to file
        with open(output_path, 'wb') as f:
            f.write(response.content)
            
        logger.info(f"Saved D3FEND ontology to {output_path}")
        return output_path
    except requests.exceptions.RequestException as e:
        logger.error(f"Error downloading D3FEND ontology: {e}")
        sys.exit(1)

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Import the latest D3FEND ontology")
    parser.add_argument(
        "--url", 
        default=D3FEND_OWL_URL,
        help="URL to download the D3FEND ontology from"
    )
    parser.add_argument(
        "--output", 
        default="3rdparty/d3fend/d3fend.owl",
        help="Path to save the downloaded ontology to"
    )
    parser.add_argument(
        "--skip-download", 
        action="store_true",
        help="Skip downloading and use existing file"
    )
    
    args = parser.parse_args()
    
    try:
        # Download or use existing ontology
        if args.skip_download:
            logger.info(f"Using existing D3FEND ontology from {args.output}")
            if not os.path.exists(args.output):
                logger.error(f"D3FEND ontology file not found: {args.output}")
                sys.exit(1)
            ontology_path = args.output
        else:
            ontology_path = download_d3fend_ontology(args.url, args.output)
        
        # Import ontology
        db = SessionLocal()
        try:
            logger.info("Importing D3FEND ontology into database")
            import_d3fend_ontology(db, ontology_path)
            logger.info("Successfully imported D3FEND ontology")
        except Exception as e:
            logger.error(f"Error importing D3FEND ontology: {e}")
            sys.exit(1)
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 