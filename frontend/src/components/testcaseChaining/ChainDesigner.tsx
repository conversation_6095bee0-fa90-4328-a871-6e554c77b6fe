import React, { useState, useEffect, useCallback } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import ReactFlow, {
  Background,
  Controls,
  MiniMap,
  addEdge,
  Node,
  Edge,
  Connection,
  useNodesState,
  useEdgesState,
  MarkerType
} from 'reactflow';
import 'reactflow/dist/style.css';
import {
  Box,
  Button,
  Typography,
  Paper,
  Drawer,
  List,
  ListItem,
  ListItemText,
  Divider,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
  Alert
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import SaveIcon from '@mui/icons-material/Save';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import CloseIcon from '@mui/icons-material/Close';

import {
  TestcaseChain,
  TestcaseChainNode,
  TestcaseChainEdge,
  NodeType,
  EdgeType,
  CreateNodeRequest,
  CreateEdgeRequest,
  UpdateNodeRequest
} from '../../types/testcaseChaining';
import { TestCase } from '../../types/testcase';
import {
  getChain,
  getNodes,
  getEdges,
  createNode,
  updateNode,
  deleteNode,
  createEdge,
  deleteEdge,
  executeChain
} from '../../services/testcaseChaining';
import { getTestCases } from '../../services/testcase';

// Custom node types
const nodeTypes = {
  start: ({ data }: any) => (
    <div className="react-flow__node-default" style={{ background: '#4CAF50', color: 'white', borderRadius: '50%', width: '60px', height: '60px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
      <div>Start</div>
    </div>
  ),
  standard: ({ data }: any) => (
    <div className="react-flow__node-default" style={{ background: '#2196F3', color: 'white', padding: '10px', borderRadius: '5px', maxWidth: '200px' }}>
      <div>{data.label}</div>
    </div>
  ),
  decision: ({ data }: any) => (
    <div className="react-flow__node-default" style={{ background: '#FF9800', color: 'white', padding: '10px', borderRadius: '5px', transform: 'rotate(45deg)', width: '60px', height: '60px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
      <div style={{ transform: 'rotate(-45deg)' }}>{data.label}</div>
    </div>
  ),
  end: ({ data }: any) => (
    <div className="react-flow__node-default" style={{ background: '#F44336', color: 'white', borderRadius: '50%', width: '60px', height: '60px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
      <div>End</div>
    </div>
  )
};

interface ChainDesignerProps {}

const ChainDesigner: React.FC<ChainDesignerProps> = () => {
  const { chainId } = useParams<{ chainId: string }>();
  const navigate = useNavigate();
  
  const [chain, setChain] = useState<TestcaseChain | null>(null);
  const [testCases, setTestCases] = useState<TestCase[]>([]);
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [selectedEdge, setSelectedEdge] = useState<Edge | null>(null);
  const [addNodeDialogOpen, setAddNodeDialogOpen] = useState<boolean>(false);
  const [newNodeData, setNewNodeData] = useState<Partial<CreateNodeRequest>>({
    chain_id: chainId,
    node_type: NodeType.STANDARD,
    position_x: 100,
    position_y: 100,
    execution_order: 0
  });
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Load chain data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        if (!chainId) return;
        
        // Fetch chain details
        const chainData = await getChain(chainId);
        setChain(chainData);
        
        // Fetch nodes and edges
        const nodesData = await getNodes(chainId);
        const edgesData = await getEdges(chainId);
        
        // Fetch test cases for node creation
        const testCasesData = await getTestCases();
        setTestCases(testCasesData);
        
        // Convert to ReactFlow format
        const flowNodes = nodesData.map((node: TestcaseChainNode) => ({
          id: node.id,
          type: node.node_type.toLowerCase(),
          position: { x: node.position_x, y: node.position_y },
          data: { 
            label: node.testcase?.name || `Node ${node.id}`,
            nodeData: node
          }
        }));
        
        const flowEdges = edgesData.map((edge: TestcaseChainEdge) => ({
          id: edge.id,
          source: edge.source_node_id,
          target: edge.target_node_id,
          type: 'smoothstep',
          animated: edge.edge_type === EdgeType.CONDITIONAL,
          markerEnd: {
            type: MarkerType.ArrowClosed,
            width: 20,
            height: 20
          },
          style: { stroke: edge.edge_type === EdgeType.CONDITIONAL ? '#FF9800' : '#2196F3' },
          data: { edgeData: edge }
        }));
        
        setNodes(flowNodes);
        setEdges(flowEdges);
        setError(null);
      } catch (err) {
        console.error(err);
        setError('Failed to load chain data');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [chainId]);

  // Handle node selection
  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node);
    setSelectedEdge(null);
    setDrawerOpen(true);
  }, []);

  // Handle edge selection
  const onEdgeClick = useCallback((event: React.MouseEvent, edge: Edge) => {
    setSelectedEdge(edge);
    setSelectedNode(null);
    setDrawerOpen(true);
  }, []);

  // Handle adding a new edge
  const onConnect = useCallback(async (connection: Connection) => {
    try {
      if (!chainId || !connection.source || !connection.target) return;
      
      const newEdge: CreateEdgeRequest = {
        source_node_id: connection.source,
        target_node_id: connection.target,
        edge_type: EdgeType.STANDARD
      };
      
      const createdEdge = await createEdge(newEdge);
      
      const flowEdge = {
        id: createdEdge.id,
        source: createdEdge.source_node_id,
        target: createdEdge.target_node_id,
        type: 'smoothstep',
        animated: false,
        markerEnd: {
          type: MarkerType.ArrowClosed,
          width: 20,
          height: 20
        },
        style: { stroke: '#2196F3' },
        data: { edgeData: createdEdge }
      };
      
      setEdges((eds) => addEdge(flowEdge, eds));
      setSuccessMessage('Edge created successfully');
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err) {
      console.error(err);
      setError('Failed to create edge');
      setTimeout(() => setError(null), 3000);
    }
  }, [chainId, setEdges]);

  // Handle node updates
  const handleUpdateNode = async () => {
    try {
      if (!selectedNode || !selectedNode.data.nodeData) return;
      
      const nodeData = selectedNode.data.nodeData;
      const updateData: UpdateNodeRequest = {
        node_type: nodeData.node_type,
        position_x: selectedNode.position.x,
        position_y: selectedNode.position.y,
        execution_order: nodeData.execution_order
      };
      
      await updateNode(nodeData.id, updateData);
      setSuccessMessage('Node updated successfully');
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err) {
      console.error(err);
      setError('Failed to update node');
      setTimeout(() => setError(null), 3000);
    }
  };

  // Handle node deletion
  const handleDeleteNode = async () => {
    try {
      if (!selectedNode || !selectedNode.data.nodeData) return;
      
      const nodeId = selectedNode.data.nodeData.id;
      await deleteNode(nodeId);
      
      setNodes((nds) => nds.filter((node) => node.id !== selectedNode.id));
      setDrawerOpen(false);
      setSelectedNode(null);
      setSuccessMessage('Node deleted successfully');
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err) {
      console.error(err);
      setError('Failed to delete node');
      setTimeout(() => setError(null), 3000);
    }
  };

  // Handle edge deletion
  const handleDeleteEdge = async () => {
    try {
      if (!selectedEdge || !selectedEdge.data.edgeData) return;
      
      const edgeId = selectedEdge.data.edgeData.id;
      await deleteEdge(edgeId);
      
      setEdges((eds) => eds.filter((edge) => edge.id !== selectedEdge.id));
      setDrawerOpen(false);
      setSelectedEdge(null);
      setSuccessMessage('Edge deleted successfully');
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err) {
      console.error(err);
      setError('Failed to delete edge');
      setTimeout(() => setError(null), 3000);
    }
  };

  // Handle adding a new node
  const handleAddNode = async () => {
    try {
      if (!chainId || !newNodeData.testcase_id) return;
      
      const nodeRequest: CreateNodeRequest = {
        chain_id: chainId,
        testcase_id: newNodeData.testcase_id,
        node_type: newNodeData.node_type || NodeType.STANDARD,
        position_x: newNodeData.position_x || 100,
        position_y: newNodeData.position_y || 100,
        execution_order: newNodeData.execution_order || 0,
        condition_expression: newNodeData.condition_expression
      };
      
      const createdNode = await createNode(nodeRequest);
      
      // Find the testcase to get the name
      const testcase = testCases.find(tc => tc.id === createdNode.testcase_id);
      
      const newNode = {
        id: createdNode.id,
        type: createdNode.node_type.toLowerCase(),
        position: { x: createdNode.position_x, y: createdNode.position_y },
        data: { 
          label: testcase?.name || `Node ${createdNode.id}`,
          nodeData: createdNode
        }
      };
      
      setNodes((nds) => [...nds, newNode]);
      setAddNodeDialogOpen(false);
      setNewNodeData({
        chain_id: chainId,
        node_type: NodeType.STANDARD,
        position_x: 100,
        position_y: 100,
        execution_order: 0
      });
      setSuccessMessage('Node created successfully');
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err) {
      console.error(err);
      setError('Failed to create node');
      setTimeout(() => setError(null), 3000);
    }
  };

  // Handle executing the chain
  const handleExecuteChain = async () => {
    try {
      if (!chainId) return;
      
      await executeChain({ chain_id: chainId });
      setSuccessMessage('Chain execution started');
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err) {
      console.error(err);
      setError('Failed to execute chain');
      setTimeout(() => setError(null), 3000);
    }
  };

  // Handle node drag
  const onNodeDragStop = (event: React.MouseEvent, node: Node) => {
    const updatedNodes = nodes.map(n => {
      if (n.id === node.id) {
        if (n.data.nodeData) {
          n.data.nodeData.position_x = node.position.x;
          n.data.nodeData.position_y = node.position.y;
        }
      }
      return n;
    });
    
    setNodes(updatedNodes);
  };

  if (loading) return <Typography>Loading chain designer...</Typography>;
  if (error && !successMessage) return <Alert severity="error">{error}</Alert>;

  return (
    <Box sx={{ height: 'calc(100vh - 100px)', width: '100%' }}>
      {successMessage && (
        <Alert severity="success" sx={{ position: 'absolute', top: 10, right: 10, zIndex: 1000 }}>
          {successMessage}
        </Alert>
      )}
      
      <Paper sx={{ p: 2, mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5">{chain?.name || 'Chain Designer'}</Typography>
        <Box>
          <Button 
            variant="contained" 
            color="primary" 
            startIcon={<AddIcon />} 
            onClick={() => setAddNodeDialogOpen(true)}
            sx={{ mr: 1 }}
          >
            Add Node
          </Button>
          <Button 
            variant="contained" 
            color="secondary" 
            startIcon={<SaveIcon />} 
            onClick={handleUpdateNode}
            sx={{ mr: 1 }}
          >
            Save Changes
          </Button>
          <Button 
            variant="contained" 
            color="success" 
            startIcon={<PlayArrowIcon />} 
            onClick={handleExecuteChain}
          >
            Execute Chain
          </Button>
        </Box>
      </Paper>
      
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeClick={onNodeClick}
        onEdgeClick={onEdgeClick}
        onNodeDragStop={onNodeDragStop}
        nodeTypes={nodeTypes}
        fitView
      >
        <Background />
        <Controls />
        <MiniMap />
      </ReactFlow>
      
      {/* Node/Edge Details Drawer */}
      <Drawer
        anchor="right"
        open={drawerOpen}
        onClose={() => setDrawerOpen(false)}
        sx={{ width: 300 }}
      >
        <Box sx={{ width: 300, p: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              {selectedNode ? 'Node Details' : selectedEdge ? 'Edge Details' : 'Details'}
            </Typography>
            <IconButton onClick={() => setDrawerOpen(false)}>
              <CloseIcon />
            </IconButton>
          </Box>
          
          <Divider sx={{ mb: 2 }} />
          
          {selectedNode && selectedNode.data.nodeData && (
            <List>
              <ListItem>
                <ListItemText primary="ID" secondary={selectedNode.data.nodeData.id} />
              </ListItem>
              <ListItem>
                <ListItemText primary="Type" secondary={selectedNode.data.nodeData.node_type} />
              </ListItem>
              <ListItem>
                <ListItemText primary="Testcase" secondary={selectedNode.data.label} />
              </ListItem>
              <ListItem>
                <ListItemText primary="Execution Order" secondary={selectedNode.data.nodeData.execution_order} />
              </ListItem>
              <ListItem>
                <ListItemText primary="Position" secondary={`X: ${Math.round(selectedNode.position.x)}, Y: ${Math.round(selectedNode.position.y)}`} />
              </ListItem>
              
              <Divider sx={{ my: 2 }} />
              
              <ListItem>
                <Button 
                  variant="contained" 
                  color="error" 
                  startIcon={<DeleteIcon />} 
                  onClick={handleDeleteNode}
                  fullWidth
                >
                  Delete Node
                </Button>
              </ListItem>
            </List>
          )}
          
          {selectedEdge && selectedEdge.data.edgeData && (
            <List>
              <ListItem>
                <ListItemText primary="ID" secondary={selectedEdge.data.edgeData.id} />
              </ListItem>
              <ListItem>
                <ListItemText primary="Type" secondary={selectedEdge.data.edgeData.edge_type} />
              </ListItem>
              <ListItem>
                <ListItemText primary="Source" secondary={selectedEdge.source} />
              </ListItem>
              <ListItem>
                <ListItemText primary="Target" secondary={selectedEdge.target} />
              </ListItem>
              {selectedEdge.data.edgeData.condition && (
                <ListItem>
                  <ListItemText primary="Condition" secondary={selectedEdge.data.edgeData.condition} />
                </ListItem>
              )}
              
              <Divider sx={{ my: 2 }} />
              
              <ListItem>
                <Button 
                  variant="contained" 
                  color="error" 
                  startIcon={<DeleteIcon />} 
                  onClick={handleDeleteEdge}
                  fullWidth
                >
                  Delete Edge
                </Button>
              </ListItem>
            </List>
          )}
        </Box>
      </Drawer>
      
      {/* Add Node Dialog */}
      <Dialog open={addNodeDialogOpen} onClose={() => setAddNodeDialogOpen(false)}>
        <DialogTitle>Add New Node</DialogTitle>
        <DialogContent>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>Testcase</InputLabel>
            <Select
              value={newNodeData.testcase_id || ''}
              onChange={(e) => setNewNodeData({ ...newNodeData, testcase_id: e.target.value as string })}
              label="Testcase"
            >
              {testCases.map((testcase) => (
                <MenuItem key={testcase.id} value={testcase.id}>
                  {testcase.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>Node Type</InputLabel>
            <Select
              value={newNodeData.node_type || NodeType.STANDARD}
              onChange={(e) => setNewNodeData({ ...newNodeData, node_type: e.target.value as NodeType })}
              label="Node Type"
            >
              <MenuItem value={NodeType.START}>Start</MenuItem>
              <MenuItem value={NodeType.STANDARD}>Standard</MenuItem>
              <MenuItem value={NodeType.DECISION}>Decision</MenuItem>
              <MenuItem value={NodeType.END}>End</MenuItem>
            </Select>
          </FormControl>
          
          <TextField
            margin="dense"
            label="Execution Order"
            type="number"
            fullWidth
            value={newNodeData.execution_order || 0}
            onChange={(e) => setNewNodeData({ ...newNodeData, execution_order: parseInt(e.target.value) })}
            sx={{ mt: 2 }}
          />
          
          <TextField
            margin="dense"
            label="Position X"
            type="number"
            fullWidth
            value={newNodeData.position_x || 100}
            onChange={(e) => setNewNodeData({ ...newNodeData, position_x: parseInt(e.target.value) })}
            sx={{ mt: 2 }}
          />
          
          <TextField
            margin="dense"
            label="Position Y"
            type="number"
            fullWidth
            value={newNodeData.position_y || 100}
            onChange={(e) => setNewNodeData({ ...newNodeData, position_y: parseInt(e.target.value) })}
            sx={{ mt: 2 }}
          />
          
          {newNodeData.node_type === NodeType.DECISION && (
            <TextField
              margin="dense"
              label="Condition Expression"
              fullWidth
              multiline
              rows={3}
              value={newNodeData.condition_expression || ''}
              onChange={(e) => setNewNodeData({ ...newNodeData, condition_expression: e.target.value })}
              sx={{ mt: 2 }}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddNodeDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleAddNode} 
            color="primary"
            disabled={!newNodeData.testcase_id}
          >
            Add Node
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ChainDesigner; 