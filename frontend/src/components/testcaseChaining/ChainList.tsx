import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Box, 
  <PERSON>ton, 
  Card, 
  CardContent, 
  Typography, 
  Grid, 
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import { TestcaseChain, ChainStatus, CreateChainRequest } from '../../types/testcaseChaining';
import { getChains, createChain, deleteChain } from '../../services/testcaseChaining';

const ChainList: React.FC = () => {
  const [chains, setChains] = useState<TestcaseChain[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [openCreateDialog, setOpenCreateDialog] = useState<boolean>(false);
  const [newChain, setNewChain] = useState<CreateChainRequest>({
    name: '',
    description: ''
  });

  const fetchChains = async () => {
    try {
      setLoading(true);
      const data = await getChains();
      setChains(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch testcase chains');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchChains();
  }, []);

  const handleCreateChain = async () => {
    try {
      const createdChain = await createChain(newChain);
      setChains([...chains, createdChain]);
      setOpenCreateDialog(false);
      setNewChain({ name: '', description: '' });
    } catch (err) {
      setError('Failed to create testcase chain');
      console.error(err);
    }
  };

  const handleDeleteChain = async (id: string) => {
    try {
      await deleteChain(id);
      setChains(chains.filter(chain => chain.id !== id));
    } catch (err) {
      setError('Failed to delete testcase chain');
      console.error(err);
    }
  };

  const getStatusColor = (status: ChainStatus) => {
    switch (status) {
      case ChainStatus.ACTIVE:
        return 'success';
      case ChainStatus.DRAFT:
        return 'warning';
      case ChainStatus.ARCHIVED:
        return 'error';
      default:
        return 'default';
    }
  };

  if (loading) return <Typography>Loading...</Typography>;
  if (error) return <Typography color="error">{error}</Typography>;

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Testcase Chains</Typography>
        <Button 
          variant="contained" 
          color="primary" 
          startIcon={<AddIcon />}
          onClick={() => setOpenCreateDialog(true)}
        >
          Create Chain
        </Button>
      </Box>

      <Grid container spacing={3}>
        {chains.length === 0 ? (
          <Grid item xs={12}>
            <Typography>No testcase chains found. Create your first chain to get started.</Typography>
          </Grid>
        ) : (
          chains.map(chain => (
            <Grid item xs={12} md={6} lg={4} key={chain.id}>
              <Card>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="h6" component={Link} to={`/testcase-chains/${chain.id}`} style={{ textDecoration: 'none', color: 'inherit' }}>
                      {chain.name}
                    </Typography>
                    <Chip 
                      label={chain.status} 
                      color={getStatusColor(chain.status) as any}
                      size="small"
                    />
                  </Box>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    {chain.description}
                  </Typography>
                  <Box mt={2} display="flex" justifyContent="space-between">
                    <Typography variant="caption" color="textSecondary">
                      Created: {new Date(chain.created_at).toLocaleDateString()}
                    </Typography>
                    <Button 
                      size="small" 
                      color="error" 
                      onClick={() => handleDeleteChain(chain.id)}
                    >
                      Delete
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))
        )}
      </Grid>

      {/* Create Chain Dialog */}
      <Dialog open={openCreateDialog} onClose={() => setOpenCreateDialog(false)}>
        <DialogTitle>Create New Testcase Chain</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Name"
            fullWidth
            value={newChain.name}
            onChange={(e) => setNewChain({ ...newChain, name: e.target.value })}
          />
          <TextField
            margin="dense"
            label="Description"
            fullWidth
            multiline
            rows={4}
            value={newChain.description}
            onChange={(e) => setNewChain({ ...newChain, description: e.target.value })}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenCreateDialog(false)}>Cancel</Button>
          <Button 
            onClick={handleCreateChain} 
            color="primary"
            disabled={!newChain.name.trim()}
          >
            Create
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ChainList; 