import axios from 'axios';
import { 
  Testcase<PERSON>hai<PERSON>, 
  TestcaseChainNode, 
  TestcaseChainEdge, 
  ChainExecution, 
  TestcaseCondition,
  CreateChainRequest,
  UpdateChainRequest,
  CreateNodeRequest,
  UpdateNodeRequest,
  CreateEdgeRequest,
  CreateConditionRequest,
  UpdateConditionRequest,
  ExecuteChainRequest
} from '../types/testcaseChaining';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';
const API_VERSION = '/api/v1';
const BASE_URL = `${API_URL}${API_VERSION}`;

// Chain endpoints
export const getChains = async (): Promise<TestcaseChain[]> => {
  const response = await axios.get(`${BASE_URL}/testcase-chains/`);
  return response.data;
};

export const getChain = async (id: string): Promise<TestcaseChain> => {
  const response = await axios.get(`${BASE_URL}/testcase-chains/${id}`);
  return response.data;
};

export const createChain = async (data: CreateChainRequest): Promise<TestcaseChain> => {
  const response = await axios.post(`${BASE_URL}/testcase-chains/`, data);
  return response.data;
};

export const updateChain = async (id: string, data: UpdateChainRequest): Promise<TestcaseChain> => {
  const response = await axios.put(`${BASE_URL}/testcase-chains/${id}`, data);
  return response.data;
};

export const deleteChain = async (id: string): Promise<void> => {
  await axios.delete(`${BASE_URL}/testcase-chains/${id}`);
};

// Node endpoints
export const getNodes = async (chainId: string): Promise<TestcaseChainNode[]> => {
  const response = await axios.get(`${BASE_URL}/testcase-chains/${chainId}/nodes/`);
  return response.data;
};

export const getNode = async (id: string): Promise<TestcaseChainNode> => {
  const response = await axios.get(`${BASE_URL}/testcase-chain-nodes/${id}`);
  return response.data;
};

export const createNode = async (data: CreateNodeRequest): Promise<TestcaseChainNode> => {
  const response = await axios.post(`${BASE_URL}/testcase-chain-nodes/`, data);
  return response.data;
};

export const updateNode = async (id: string, data: UpdateNodeRequest): Promise<TestcaseChainNode> => {
  const response = await axios.put(`${BASE_URL}/testcase-chain-nodes/${id}`, data);
  return response.data;
};

export const deleteNode = async (id: string): Promise<void> => {
  await axios.delete(`${BASE_URL}/testcase-chain-nodes/${id}`);
};

// Edge endpoints
export const getEdges = async (chainId: string): Promise<TestcaseChainEdge[]> => {
  const response = await axios.get(`${BASE_URL}/testcase-chains/${chainId}/edges/`);
  return response.data;
};

export const getEdge = async (id: string): Promise<TestcaseChainEdge> => {
  const response = await axios.get(`${BASE_URL}/testcase-chain-edges/${id}`);
  return response.data;
};

export const createEdge = async (data: CreateEdgeRequest): Promise<TestcaseChainEdge> => {
  const response = await axios.post(`${BASE_URL}/testcase-chain-edges/`, data);
  return response.data;
};

export const deleteEdge = async (id: string): Promise<void> => {
  await axios.delete(`${BASE_URL}/testcase-chain-edges/${id}`);
};

// Execution endpoints
export const getChainExecutions = async (chainId: string): Promise<ChainExecution[]> => {
  const response = await axios.get(`${BASE_URL}/testcase-chains/${chainId}/executions/`);
  return response.data;
};

export const getChainExecution = async (id: string): Promise<ChainExecution> => {
  const response = await axios.get(`${BASE_URL}/chain-executions/${id}`);
  return response.data;
};

export const executeChain = async (data: ExecuteChainRequest): Promise<ChainExecution> => {
  const response = await axios.post(`${BASE_URL}/chain-executions/`, data);
  return response.data;
};

// Condition endpoints
export const getTestcaseConditions = async (testcaseId: string): Promise<TestcaseCondition[]> => {
  const response = await axios.get(`${BASE_URL}/testcases/${testcaseId}/conditions/`);
  return response.data;
};

export const getTestcaseCondition = async (id: string): Promise<TestcaseCondition> => {
  const response = await axios.get(`${BASE_URL}/testcase-conditions/${id}`);
  return response.data;
};

export const createTestcaseCondition = async (data: CreateConditionRequest): Promise<TestcaseCondition> => {
  const response = await axios.post(`${BASE_URL}/testcase-conditions/`, data);
  return response.data;
};

export const updateTestcaseCondition = async (id: string, data: UpdateConditionRequest): Promise<TestcaseCondition> => {
  const response = await axios.put(`${BASE_URL}/testcase-conditions/${id}`, data);
  return response.data;
};

export const deleteTestcaseCondition = async (id: string): Promise<void> => {
  await axios.delete(`${BASE_URL}/testcase-conditions/${id}`);
}; 