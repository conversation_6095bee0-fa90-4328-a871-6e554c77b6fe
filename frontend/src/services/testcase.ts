import axios from 'axios';
import { TestCase, CreateTestCaseRequest, UpdateTestCaseRequest } from '../types/testcase';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';
const API_VERSION = '/api/v1';
const BASE_URL = `${API_URL}${API_VERSION}`;

export const getTestCases = async (): Promise<TestCase[]> => {
  const response = await axios.get(`${BASE_URL}/testcases/`);
  return response.data;
};

export const getTestCase = async (id: string): Promise<TestCase> => {
  const response = await axios.get(`${BASE_URL}/testcases/${id}`);
  return response.data;
};

export const getTestCasesByCampaign = async (campaignId: string): Promise<TestCase[]> => {
  const response = await axios.get(`${BASE_URL}/campaigns/${campaignId}/testcases/`);
  return response.data;
};

export const createTestCase = async (data: CreateTestCaseRequest): Promise<TestCase> => {
  const response = await axios.post(`${BASE_URL}/testcases/`, data);
  return response.data;
};

export const updateTestCase = async (id: string, data: UpdateTestCaseRequest): Promise<TestCase> => {
  const response = await axios.put(`${BASE_URL}/testcases/${id}`, data);
  return response.data;
};

export const deleteTestCase = async (id: string): Promise<void> => {
  await axios.delete(`${BASE_URL}/testcases/${id}`);
}; 