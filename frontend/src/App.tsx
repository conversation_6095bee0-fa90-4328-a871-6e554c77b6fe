import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme, CssBaseline } from '@mui/material';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import Campaigns from './pages/Campaigns';
import TestCases from './pages/TestCases';
import Assessments from './pages/Assessments';
import TestcaseChains from './pages/TestcaseChains';
import TestcaseChainDesigner from './pages/TestcaseChainDesigner';

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

const App: React.FC = () => {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/campaigns" element={<Campaigns />} />
            <Route path="/testcases" element={<TestCases />} />
            <Route path="/assessments" element={<Assessments />} />
            <Route path="/testcase-chains" element={<TestcaseChains />} />
            <Route path="/testcase-chains/:chainId" element={<TestcaseChainDesigner />} />
          </Routes>
        </Layout>
      </Router>
    </ThemeProvider>
  );
};

export default App;
