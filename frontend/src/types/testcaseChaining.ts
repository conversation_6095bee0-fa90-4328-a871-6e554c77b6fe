import { TestCase } from './testcase';

export enum ChainStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  ARCHIVED = 'archived'
}

export enum NodeType {
  START = 'start',
  STANDARD = 'standard',
  DECISION = 'decision',
  END = 'end'
}

export enum EdgeType {
  STANDARD = 'standard',
  CONDITIONAL = 'conditional'
}

export enum ExecutionStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  SKIPPED = 'skipped'
}

export enum ConditionType {
  PRECONDITION = 'precondition',
  POSTCONDITION = 'postcondition'
}

export interface TestcaseChain {
  id: string;
  name: string;
  description: string;
  created_by: string;
  status: ChainStatus;
  created_at: string;
  updated_at: string;
  nodes?: TestcaseChainNode[];
  edges?: TestcaseChainEdge[];
}

export interface TestcaseChainNode {
  id: string;
  chain_id: string;
  testcase_id: string;
  node_type: NodeType;
  position_x: number;
  position_y: number;
  execution_order: number;
  condition_expression?: string;
  created_at: string;
  updated_at: string;
  testcase?: TestCase;
}

export interface TestcaseChainEdge {
  id: string;
  source_node_id: string;
  target_node_id: string;
  edge_type: EdgeType;
  condition?: string;
  created_at: string;
  updated_at: string;
  source_node?: TestcaseChainNode;
  target_node?: TestcaseChainNode;
}

export interface ChainExecution {
  id: string;
  chain_id: string;
  started_by: string;
  start_time: string;
  end_time?: string;
  status: ExecutionStatus;
  created_at: string;
  updated_at: string;
  chain?: TestcaseChain;
  node_executions?: NodeExecution[];
}

export interface NodeExecution {
  id: string;
  chain_execution_id: string;
  node_id: string;
  start_time: string;
  end_time?: string;
  status: ExecutionStatus;
  result_data?: any;
  created_at: string;
  updated_at: string;
  node?: TestcaseChainNode;
}

export interface TestcaseCondition {
  id: string;
  testcase_id: string;
  condition_type: ConditionType;
  name: string;
  description: string;
  validation_script: string;
  required: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateChainRequest {
  name: string;
  description: string;
}

export interface UpdateChainRequest {
  name?: string;
  description?: string;
  status?: ChainStatus;
}

export interface CreateNodeRequest {
  chain_id: string;
  testcase_id: string;
  node_type: NodeType;
  position_x: number;
  position_y: number;
  execution_order: number;
  condition_expression?: string;
}

export interface UpdateNodeRequest {
  node_type?: NodeType;
  position_x?: number;
  position_y?: number;
  execution_order?: number;
  condition_expression?: string;
}

export interface CreateEdgeRequest {
  source_node_id: string;
  target_node_id: string;
  edge_type: EdgeType;
  condition?: string;
}

export interface CreateConditionRequest {
  testcase_id: string;
  condition_type: ConditionType;
  name: string;
  description: string;
  validation_script: string;
  required: boolean;
}

export interface UpdateConditionRequest {
  condition_type?: ConditionType;
  name?: string;
  description?: string;
  validation_script?: string;
  required?: boolean;
}

export interface ExecuteChainRequest {
  chain_id: string;
} 