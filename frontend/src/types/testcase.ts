export interface TestCase {
  id: string;
  name: string;
  description: string;
  campaign_id: string;
  status: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  steps?: TestCaseStep[];
  tags?: string[];
}

export interface TestCaseStep {
  id: string;
  testcase_id: string;
  order: number;
  description: string;
  expected_result: string;
  created_at: string;
  updated_at: string;
}

export interface CreateTestCaseRequest {
  name: string;
  description: string;
  campaign_id: string;
  steps?: Omit<TestCaseStep, 'id' | 'testcase_id' | 'created_at' | 'updated_at'>[];
  tags?: string[];
}

export interface UpdateTestCaseRequest {
  name?: string;
  description?: string;
  status?: string;
  tags?: string[];
} 