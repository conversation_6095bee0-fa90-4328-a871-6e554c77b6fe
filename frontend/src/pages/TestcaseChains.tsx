import React from 'react';
import { Box, Container, Typography } from '@mui/material';
import ChainList from '../components/testcaseChaining/ChainList';

const TestcaseChains: React.FC = () => {
  return (
    <Container maxWidth="xl">
      <Box py={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          Testcase Chains
        </Typography>
        <Typography variant="body1" color="textSecondary" paragraph>
          Create and manage complex attack chains by connecting multiple test cases with conditional logic and execution order.
        </Typography>
        
        <ChainList />
      </Box>
    </Container>
  );
};

export default TestcaseChains; 