import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { useParams, useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>, Card, Container, Row, Col, Form, Alert, Spinner, Badge, InputGroup, Dropdown } from 'react-bootstrap';
import './styles.css';

interface Tag {
  id: number;
  name: string;
  description: string;
  color: string;
  created_at: string;
  updated_at: string | null;
  usage_count: number;
}

interface TagStats {
  id: number;
  name: string;
  color: string;
  count: number;
  entity_types: Record<string, number>;
}

const TagManagement: React.FC = () => {
  const [tags, setTags] = useState<Tag[]>([]);
  const [tagStats, setTagStats] = useState<TagStats[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<{name: string, description: string, color: string}>({
    name: '',
    description: '',
    color: '#3498db'
  });
  const [isEditing, setIsEditing] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [showStats, setShowStats] = useState<boolean>(false);
  
  const navigate = useNavigate();
  
  useEffect(() => {
    fetchTags();
  }, []);
  
  const fetchTags = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/v1/tags', {
        params: { search: searchTerm }
      });
      setTags(response.data);
      setError(null);
      
      // Also fetch tag stats
      const statsResponse = await axios.get('/api/v1/tags/stats');
      setTagStats(statsResponse.data);
    } catch (err) {
      console.error('Error fetching tags:', err);
      setError('Failed to load tags. Please try again later.');
      // For development, use mock data when API is not available
      setTags([
        { 
          id: 1, 
          name: 'Bug', 
          description: 'Issues that need to be fixed',
          color: '#e74c3c',
          created_at: new Date().toISOString(),
          updated_at: null,
          usage_count: 5
        },
        { 
          id: 2, 
          name: 'Feature', 
          description: 'New functionality',
          color: '#2ecc71',
          created_at: new Date().toISOString(),
          updated_at: null,
          usage_count: 3
        },
        { 
          id: 3, 
          name: 'Documentation', 
          description: 'Documentation related',
          color: '#3498db',
          created_at: new Date().toISOString(),
          updated_at: null,
          usage_count: 2
        }
      ]);
      
      setTagStats([
        {
          id: 1,
          name: 'Bug',
          color: '#e74c3c',
          count: 5,
          entity_types: { 'test_case': 3, 'campaign': 2 }
        },
        {
          id: 2,
          name: 'Feature',
          color: '#2ecc71',
          count: 3,
          entity_types: { 'test_case': 1, 'campaign': 1, 'assessment': 1 }
        },
        {
          id: 3,
          name: 'Documentation',
          color: '#3498db',
          count: 2,
          entity_types: { 'test_case': 2 }
        }
      ]);
    } finally {
      setLoading(false);
    }
  };
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (isEditing) {
        // Update existing tag
        await axios.put(`/api/v1/tags/${isEditing}`, formData);
      } else {
        // Create new tag
        await axios.post('/api/v1/tags/', formData);
      }
      
      // Reset form and refresh data
      setFormData({ name: '', description: '', color: '#3498db' });
      setIsEditing(null);
      fetchTags();
    } catch (err) {
      console.error('Error saving tag:', err);
      setError('Failed to save tag. Please try again.');
    }
  };
  
  const handleEdit = (tag: Tag) => {
    setFormData({
      name: tag.name,
      description: tag.description || '',
      color: tag.color
    });
    setIsEditing(tag.id);
  };
  
  const handleDelete = async (id: number) => {
    if (!window.confirm('Are you sure you want to delete this tag? This will remove it from all associated entities.')) {
      return;
    }
    
    try {
      await axios.delete(`/api/v1/tags/${id}`);
      fetchTags();
    } catch (err) {
      console.error('Error deleting tag:', err);
      setError('Failed to delete tag. Please try again.');
    }
  };
  
  const handleCancel = () => {
    setFormData({ name: '', description: '', color: '#3498db' });
    setIsEditing(null);
  };
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchTags();
  };
  
  const toggleStats = () => {
    setShowStats(!showStats);
  };

  if (loading && tags.length === 0) {
    return (
      <Container className="mt-4">
        <div className="text-center">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p>Loading tags...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container className="tag-management-container mt-4">
      <h1>Tag Management</h1>
      <p>Create and manage tags to categorize and organize your test cases, campaigns, and assessments.</p>
      
      {error && <Alert variant="danger">{error}</Alert>}
      
      <Row className="mb-4">
        <Col md={6}>
          <Card>
            <Card.Header>
              {isEditing ? 'Edit Tag' : 'Create New Tag'}
            </Card.Header>
            <Card.Body>
              <Form onSubmit={handleSubmit}>
                <Form.Group className="mb-3">
                  <Form.Label>Name</Form.Label>
                  <Form.Control 
                    type="text" 
                    name="name" 
                    value={formData.name} 
                    onChange={handleInputChange} 
                    required 
                    placeholder="Enter tag name"
                  />
                  <Form.Text className="text-muted">
                    Tag names must be unique
                  </Form.Text>
                </Form.Group>
                
                <Form.Group className="mb-3">
                  <Form.Label>Description</Form.Label>
                  <Form.Control 
                    as="textarea" 
                    name="description" 
                    value={formData.description} 
                    onChange={handleInputChange} 
                    rows={2}
                    placeholder="Optional description" 
                  />
                </Form.Group>
                
                <Form.Group className="mb-3">
                  <Form.Label>Color</Form.Label>
                  <InputGroup>
                    <Form.Control 
                      type="color" 
                      name="color" 
                      value={formData.color} 
                      onChange={handleInputChange} 
                      title="Choose tag color"
                    />
                    <Form.Control 
                      type="text" 
                      name="color" 
                      value={formData.color} 
                      onChange={handleInputChange} 
                      pattern="^#[0-9A-Fa-f]{6}$"
                      placeholder="#RRGGBB"
                    />
                  </InputGroup>
                  <Form.Text className="text-muted">
                    Use hex color format (e.g., #3498db)
                  </Form.Text>
                </Form.Group>
                
                <div className="d-flex gap-2">
                  <Button variant="primary" type="submit">
                    {isEditing ? 'Update Tag' : 'Create Tag'}
                  </Button>
                  {isEditing && (
                    <Button variant="secondary" onClick={handleCancel}>
                      Cancel
                    </Button>
                  )}
                </div>
              </Form>
            </Card.Body>
          </Card>
        </Col>
        
        <Col md={6}>
          <Card>
            <Card.Header className="d-flex justify-content-between align-items-center">
              <span>Tag Preview</span>
              <Button 
                variant="outline-secondary" 
                size="sm"
                onClick={toggleStats}
              >
                {showStats ? 'Hide Stats' : 'Show Stats'}
              </Button>
            </Card.Header>
            <Card.Body>
              {formData.name ? (
                <div className="tag-preview">
                  <h5>Preview:</h5>
                  <div className="mb-3">
                    <Badge 
                      style={{ 
                        backgroundColor: formData.color,
                        color: getBrightness(formData.color) > 128 ? '#000' : '#fff'
                      }}
                      className="tag-badge"
                    >
                      {formData.name}
                    </Badge>
                  </div>
                  {formData.description && (
                    <p className="text-muted small">{formData.description}</p>
                  )}
                </div>
              ) : (
                <p className="text-muted">Enter a tag name to see a preview</p>
              )}
              
              {showStats && tagStats.length > 0 && (
                <div className="tag-stats mt-4">
                  <h5>Tag Usage Statistics</h5>
                  <div className="tag-stats-chart">
                    {tagStats.map(stat => (
                      <div key={stat.id} className="tag-stat-item">
                        <div className="tag-stat-bar" style={{ width: `${Math.min(100, stat.count * 10)}%`, backgroundColor: stat.color }}>
                          <span style={{ color: getBrightness(stat.color) > 128 ? '#000' : '#fff' }}>
                            {stat.name} ({stat.count})
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>
      
      <Card>
        <Card.Header>
          <div className="d-flex justify-content-between align-items-center">
            <h2 className="mb-0">Tags</h2>
            <Form onSubmit={handleSearch} className="d-flex">
              <Form.Control
                type="text"
                placeholder="Search tags..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="me-2"
              />
              <Button variant="outline-primary" type="submit">Search</Button>
            </Form>
          </div>
        </Card.Header>
        <Card.Body>
          {tags.length === 0 ? (
            <Alert variant="info">No tags found. Create your first tag above.</Alert>
          ) : (
            <Row xs={1} md={2} lg={3} className="g-4">
              {tags.map(tag => (
                <Col key={tag.id}>
                  <Card className="tag-item h-100">
                    <Card.Header style={{ backgroundColor: tag.color, color: getBrightness(tag.color) > 128 ? '#000' : '#fff' }}>
                      {tag.name}
                    </Card.Header>
                    <Card.Body>
                      <Card.Text>{tag.description || <span className="text-muted">No description</span>}</Card.Text>
                      <div className="tag-meta">
                        <Badge bg="secondary">Used {tag.usage_count} times</Badge>
                      </div>
                    </Card.Body>
                    <Card.Footer className="d-flex justify-content-between">
                      <Button variant="outline-primary" size="sm" onClick={() => handleEdit(tag)}>
                        Edit
                      </Button>
                      <Button variant="outline-danger" size="sm" onClick={() => handleDelete(tag.id)}>
                        Delete
                      </Button>
                    </Card.Footer>
                  </Card>
                </Col>
              ))}
            </Row>
          )}
        </Card.Body>
      </Card>
    </Container>
  );
};

// Helper function to determine if text should be black or white based on background color
function getBrightness(hexColor: string): number {
  // Remove # if present
  hexColor = hexColor.replace('#', '');
  
  // Convert to RGB
  const r = parseInt(hexColor.substr(0, 2), 16);
  const g = parseInt(hexColor.substr(2, 2), 16);
  const b = parseInt(hexColor.substr(4, 2), 16);
  
  // Calculate brightness (perceived luminance)
  return (r * 299 + g * 587 + b * 114) / 1000;
}

export default TagManagement; 