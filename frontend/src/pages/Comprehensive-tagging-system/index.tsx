import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { useParams, useNavigate } from 'react-router-dom';
import { <PERSON><PERSON>, Card, Container, Row, Col, Form, Alert, Spinner } from 'react-bootstrap';
import './styles.css';

interface Comprehensive-tagging-systemData {
  id: number;
  name: string;
  description: string;
  created_at: string;
  updated_at: string | null;
}

const Comprehensive-tagging-system: React.FC = () => {
  const [data, setData] = useState<Comprehensive-tagging-systemData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<{name: string, description: string}>({
    name: '',
    description: ''
  });
  const [isEditing, setIsEditing] = useState<number | null>(null);
  
  const navigate = useNavigate();
  
  useEffect(() => {
    fetchComprehensive-tagging-systemData();
  }, []);
  
  const fetchComprehensive-tagging-systemData = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/v1/comprehensive-tagging-system');
      setData(response.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching comprehensive-tagging-system data:', err);
      setError('Failed to load comprehensive-tagging-system data. Please try again later.');
      // For development, use mock data when API is not available
      setData([
        { 
          id: 1, 
          name: 'Sample Comprehensive-tagging-system', 
          description: 'This is a sample comprehensive-tagging-system',
          created_at: new Date().toISOString(),
          updated_at: null
        }
      ]);
    } finally {
      setLoading(false);
    }
  };
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (isEditing) {
        // Update existing comprehensive-tagging-system
        await axios.put(`/api/v1/comprehensive-tagging-system/${isEditing}`, formData);
      } else {
        // Create new comprehensive-tagging-system
        await axios.post('/api/v1/comprehensive-tagging-system/', formData);
      }
      
      // Reset form and refresh data
      setFormData({ name: '', description: '' });
      setIsEditing(null);
      fetchComprehensive-tagging-systemData();
    } catch (err) {
      console.error('Error saving comprehensive-tagging-system:', err);
      setError('Failed to save comprehensive-tagging-system. Please try again.');
    }
  };
  
  const handleEdit = (item: Comprehensive-tagging-systemData) => {
    setFormData({
      name: item.name,
      description: item.description || ''
    });
    setIsEditing(item.id);
  };
  
  const handleDelete = async (id: number) => {
    if (!window.confirm('Are you sure you want to delete this comprehensive-tagging-system?')) {
      return;
    }
    
    try {
      await axios.delete(`/api/v1/comprehensive-tagging-system/${id}`);
      fetchComprehensive-tagging-systemData();
    } catch (err) {
      console.error('Error deleting comprehensive-tagging-system:', err);
      setError('Failed to delete comprehensive-tagging-system. Please try again.');
    }
  };
  
  const handleCancel = () => {
    setFormData({ name: '', description: '' });
    setIsEditing(null);
  };

  if (loading && data.length === 0) {
    return (
      <Container className="mt-4">
        <div className="text-center">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p>Loading comprehensive-tagging-system data...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container className="comprehensive-tagging-system-container mt-4">
      <h1>Comprehensive-tagging-system Management</h1>
      <p>This is the comprehensive-tagging-system management interface.</p>
      
      {error && <Alert variant="danger">{error}</Alert>}
      
      <Card className="mb-4">
        <Card.Header>
          {isEditing ? 'Edit Comprehensive-tagging-system' : 'Create New Comprehensive-tagging-system'}
        </Card.Header>
        <Card.Body>
          <Form onSubmit={handleSubmit}>
            <Form.Group className="mb-3">
              <Form.Label>Name</Form.Label>
              <Form.Control 
                type="text" 
                name="name" 
                value={formData.name} 
                onChange={handleInputChange} 
                required 
              />
            </Form.Group>
            
            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control 
                as="textarea" 
                name="description" 
                value={formData.description} 
                onChange={handleInputChange} 
                rows={3} 
              />
            </Form.Group>
            
            <div className="d-flex gap-2">
              <Button variant="primary" type="submit">
                {isEditing ? 'Update' : 'Create'}
              </Button>
              {isEditing && (
                <Button variant="secondary" onClick={handleCancel}>
                  Cancel
                </Button>
              )}
            </div>
          </Form>
        </Card.Body>
      </Card>
      
      <h2>Comprehensive-tagging-system List</h2>
      {data.length === 0 ? (
        <Alert variant="info">No comprehensive-tagging-system data found.</Alert>
      ) : (
        <Row xs={1} md={2} lg={3} className="g-4">
          {data.map(item => (
            <Col key={item.id}>
              <Card className="comprehensive-tagging-system-item">
                <Card.Body>
                  <Card.Title>{item.name}</Card.Title>
                  <Card.Text>{item.description}</Card.Text>
                  <div className="comprehensive-tagging-system-meta">
                    <small>Created: {new Date(item.created_at).toLocaleDateString()}</small>
                    {item.updated_at && (
                      <small>Updated: {new Date(item.updated_at).toLocaleDateString()}</small>
                    )}
                  </div>
                </Card.Body>
                <Card.Footer className="d-flex justify-content-between">
                  <Button variant="outline-primary" size="sm" onClick={() => handleEdit(item)}>
                    Edit
                  </Button>
                  <Button variant="outline-danger" size="sm" onClick={() => handleDelete(item.id)}>
                    Delete
                  </Button>
                </Card.Footer>
              </Card>
            </Col>
          ))}
        </Row>
      )}
    </Container>
  );
};

export default Comprehensive-tagging-system;
