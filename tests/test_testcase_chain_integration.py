"""
Integration tests for Enhanced Testcase Chaining & Sequencing.

This module contains comprehensive integration tests that test the complete
workflow from chain creation through execution, including real database
interactions and end-to-end scenarios.
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from testcontainers.postgres import PostgresContainer

from api.database import Base
from api.models.testcase_chain import (
    ChainExecution,
    NodeExecution,
    TestcaseChain,
    TestcaseChainEdge,
    TestcaseChainNode,
    TestcaseCondition,
)
from api.models.database.test_case import TestCase
from api.models.user import User
from api.services.testcase_chain_service import TestcaseChainService


@pytest.fixture(scope="session")
def postgres_container():
    """Create a PostgreSQL test container."""
    with PostgresContainer("postgres:13") as postgres:
        yield postgres


@pytest.fixture(scope="session")
def test_engine(postgres_container):
    """Create a test database engine."""
    connection_url = postgres_container.get_connection_url()
    engine = create_engine(connection_url)
    Base.metadata.create_all(engine)
    return engine


@pytest.fixture
def test_db(test_engine):
    """Create a test database session."""
    SessionLocal = sessionmaker(bind=test_engine)
    session = SessionLocal()
    try:
        yield session
    finally:
        session.rollback()
        session.close()


@pytest.fixture
def test_user(test_db):
    """Create a test user."""
    user = User(
        id=1,
        username="testuser",
        email="<EMAIL>",
        hashed_password="hashed_password",
        is_active=True,
        is_admin=False
    )
    test_db.add(user)
    test_db.commit()
    test_db.refresh(user)
    return user


@pytest.fixture
def test_testcases(test_db):
    """Create test testcases."""
    testcases = []
    for i in range(1, 6):
        testcase = TestCase(
            id=100 + i,
            name=f"Test Case {i}",
            description=f"Description for test case {i}",
            type="automated",
            status="active",
            priority="medium",
            complexity="moderate",
            expected_result=f"Expected result {i}",
            created_by="1"
        )
        test_db.add(testcase)
        testcases.append(testcase)
    
    test_db.commit()
    for testcase in testcases:
        test_db.refresh(testcase)
    return testcases


class TestTestcaseChainIntegration:
    """Integration tests for testcase chain functionality."""
    
    def test_complete_chain_lifecycle(self, test_db, test_user, test_testcases):
        """Test complete chain lifecycle from creation to execution."""
        service = TestcaseChainService(test_db)
        
        # 1. Create a chain
        chain = service.create_chain(
            name="Integration Test Chain",
            description="Complete integration test scenario",
            chain_type="sequential",
            max_execution_time_minutes=60,
            retry_on_failure=True,
            auto_cleanup=True,
            created_by=test_user.id
        )
        
        assert chain.id is not None
        assert chain.name == "Integration Test Chain"
        assert chain.status == "draft"
        
        # 2. Add nodes to the chain
        start_node = service.add_node_to_chain(
            chain_id=chain.id,
            testcase_id=test_testcases[0].id,
            node_type="start",
            execution_order=1,
            position_x=100.0,
            position_y=100.0
        )
        
        middle_node = service.add_node_to_chain(
            chain_id=chain.id,
            testcase_id=test_testcases[1].id,
            node_type="standard",
            execution_order=2,
            position_x=200.0,
            position_y=100.0,
            timeout_minutes=45,
            retry_count=2
        )
        
        end_node = service.add_node_to_chain(
            chain_id=chain.id,
            testcase_id=test_testcases[2].id,
            node_type="end",
            execution_order=3,
            position_x=300.0,
            position_y=100.0
        )
        
        assert len([start_node, middle_node, end_node]) == 3
        
        # 3. Add edges between nodes
        edge1 = service.add_edge_to_chain(
            source_node_id=start_node.id,
            target_node_id=middle_node.id,
            edge_type="standard",
            weight=1,
            label="Start to Middle"
        )
        
        edge2 = service.add_edge_to_chain(
            source_node_id=middle_node.id,
            target_node_id=end_node.id,
            edge_type="success_path",
            condition="result.status == 'success'",
            weight=1,
            label="Middle to End"
        )
        
        assert len([edge1, edge2]) == 2
        
        # 4. Validate the chain
        validation_result = service.validate_chain(chain.id)
        assert validation_result.is_valid is True
        assert len(validation_result.errors) == 0
        assert validation_result.cycle_detected is False
        
        # 5. Start chain execution
        execution = service.start_chain_execution(
            chain_id=chain.id,
            started_by=test_user.id,
            execution_context={
                "environment": "test",
                "timeout": 300
            }
        )
        
        assert execution.id is not None
        assert execution.status == "running"
        assert execution.total_nodes == 3
        assert execution.completed_nodes == 0
        
        # 6. Verify node executions were created
        node_executions = test_db.query(NodeExecution).filter(
            NodeExecution.chain_execution_id == execution.id
        ).all()
        
        assert len(node_executions) == 3
        for node_exec in node_executions:
            assert node_exec.status == "pending"
            assert node_exec.attempt_number == 1
        
        # 7. Get next executable nodes
        next_nodes = service.get_next_executable_nodes(execution.id)
        assert len(next_nodes) == 1
        assert next_nodes[0].id == start_node.id
        
        # 8. Simulate node execution completion
        start_node_execution = next(
            ne for ne in node_executions if ne.node_id == start_node.id
        )
        
        updated_execution = service.update_node_execution_status(
            node_execution_id=start_node_execution.id,
            status="completed",
            result_data={"status": "success", "output": "Start node completed"},
            output_logs="Start node execution logs"
        )
        
        assert updated_execution.status == "completed"
        assert updated_execution.result_data["status"] == "success"
        
        # 9. Verify chain execution stats updated
        test_db.refresh(execution)
        assert execution.completed_nodes == 1
        assert execution.failed_nodes == 0
        
        # 10. Get next executable nodes after first completion
        next_nodes = service.get_next_executable_nodes(execution.id)
        assert len(next_nodes) == 1
        assert next_nodes[0].id == middle_node.id
    
    def test_parallel_chain_execution(self, test_db, test_user, test_testcases):
        """Test parallel chain execution with multiple simultaneous nodes."""
        service = TestcaseChainService(test_db)
        
        # Create a parallel chain
        chain = service.create_chain(
            name="Parallel Test Chain",
            description="Test parallel execution",
            chain_type="parallel",
            created_by=test_user.id
        )
        
        # Add start node
        start_node = service.add_node_to_chain(
            chain_id=chain.id,
            testcase_id=test_testcases[0].id,
            node_type="start",
            execution_order=1
        )
        
        # Add two parallel nodes
        parallel_node1 = service.add_node_to_chain(
            chain_id=chain.id,
            testcase_id=test_testcases[1].id,
            node_type="parallel",
            execution_order=2,
            position_x=150.0,
            position_y=50.0
        )
        
        parallel_node2 = service.add_node_to_chain(
            chain_id=chain.id,
            testcase_id=test_testcases[2].id,
            node_type="parallel",
            execution_order=2,
            position_x=150.0,
            position_y=150.0
        )
        
        # Add end node
        end_node = service.add_node_to_chain(
            chain_id=chain.id,
            testcase_id=test_testcases[3].id,
            node_type="end",
            execution_order=3
        )
        
        # Create edges for parallel execution
        service.add_edge_to_chain(start_node.id, parallel_node1.id, edge_type="parallel")
        service.add_edge_to_chain(start_node.id, parallel_node2.id, edge_type="parallel")
        service.add_edge_to_chain(parallel_node1.id, end_node.id, edge_type="standard")
        service.add_edge_to_chain(parallel_node2.id, end_node.id, edge_type="standard")
        
        # Validate parallel chain
        validation_result = service.validate_chain(chain.id)
        assert validation_result.is_valid is True
        
        # Start execution
        execution = service.start_chain_execution(
            chain_id=chain.id,
            started_by=test_user.id
        )
        
        # Complete start node
        node_executions = test_db.query(NodeExecution).filter(
            NodeExecution.chain_execution_id == execution.id
        ).all()
        
        start_execution = next(ne for ne in node_executions if ne.node_id == start_node.id)
        service.update_node_execution_status(start_execution.id, "completed")
        
        # Verify both parallel nodes are now executable
        next_nodes = service.get_next_executable_nodes(execution.id)
        next_node_ids = {node.id for node in next_nodes}
        assert next_node_ids == {parallel_node1.id, parallel_node2.id}
    
    def test_conditional_chain_execution(self, test_db, test_user, test_testcases):
        """Test conditional chain execution with branching logic."""
        service = TestcaseChainService(test_db)
        
        # Create conditional chain
        chain = service.create_chain(
            name="Conditional Test Chain",
            description="Test conditional execution",
            chain_type="conditional",
            created_by=test_user.id
        )
        
        # Add nodes
        start_node = service.add_node_to_chain(
            chain_id=chain.id,
            testcase_id=test_testcases[0].id,
            node_type="start",
            execution_order=1
        )
        
        conditional_node = service.add_node_to_chain(
            chain_id=chain.id,
            testcase_id=test_testcases[1].id,
            node_type="conditional",
            execution_order=2,
            condition_expression="previous_result.status == 'success'"
        )
        
        success_node = service.add_node_to_chain(
            chain_id=chain.id,
            testcase_id=test_testcases[2].id,
            node_type="standard",
            execution_order=3,
            position_x=200.0,
            position_y=50.0
        )
        
        failure_node = service.add_node_to_chain(
            chain_id=chain.id,
            testcase_id=test_testcases[3].id,
            node_type="standard",
            execution_order=3,
            position_x=200.0,
            position_y=150.0
        )
        
        # Create conditional edges
        service.add_edge_to_chain(start_node.id, conditional_node.id, edge_type="standard")
        service.add_edge_to_chain(
            conditional_node.id, 
            success_node.id, 
            edge_type="success_path",
            condition="result.status == 'success'"
        )
        service.add_edge_to_chain(
            conditional_node.id, 
            failure_node.id, 
            edge_type="failure_path",
            condition="result.status == 'failed'"
        )
        
        # Validate conditional chain
        validation_result = service.validate_chain(chain.id)
        assert validation_result.is_valid is True
        
        # Start execution
        execution = service.start_chain_execution(
            chain_id=chain.id,
            started_by=test_user.id
        )
        
        # Complete start node with success
        node_executions = test_db.query(NodeExecution).filter(
            NodeExecution.chain_execution_id == execution.id
        ).all()
        
        start_execution = next(ne for ne in node_executions if ne.node_id == start_node.id)
        service.update_node_execution_status(
            start_execution.id, 
            "completed",
            result_data={"status": "success"}
        )
        
        # Complete conditional node
        conditional_execution = next(ne for ne in node_executions if ne.node_id == conditional_node.id)
        service.update_node_execution_status(
            conditional_execution.id,
            "completed", 
            result_data={"status": "success"}
        )
        
        # Verify only success node is executable (not failure node)
        next_nodes = service.get_next_executable_nodes(execution.id)
        assert len(next_nodes) == 1
        assert next_nodes[0].id == success_node.id
