<?xml version="1.0"?>
<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
         xmlns:rdfs="http://www.w3.org/2000/01/rdf-schema#"
         xmlns:owl="http://www.w3.org/2002/07/owl#"
         xmlns:d3f="http://d3fend.mitre.org/ontologies/d3fend.owl#">

    <!-- Test Classes -->
    <owl:Class rdf:about="http://d3fend.mitre.org/ontologies/d3fend.owl#TestCountermeasure">
        <rdfs:label>Test Countermeasure</rdfs:label>
        <rdfs:comment>A test countermeasure for D3FEND ontology testing</rdfs:comment>
        <rdfs:subClassOf rdf:resource="http://d3fend.mitre.org/ontologies/d3fend.owl#Countermeasure"/>
    </owl:Class>

    <owl:Class rdf:about="http://d3fend.mitre.org/ontologies/d3fend.owl#TestArtifact">
        <rdfs:label>Test Digital Artifact</rdfs:label>
        <rdfs:comment>A test digital artifact for D3FEND ontology testing</rdfs:comment>
    </owl:Class>

    <!-- Test Properties -->
    <owl:ObjectProperty rdf:about="http://d3fend.mitre.org/ontologies/d3fend.owl#targets">
        <rdfs:label>targets</rdfs:label>
        <rdfs:comment>Test property indicating what a countermeasure targets</rdfs:comment>
        <rdfs:domain rdf:resource="http://d3fend.mitre.org/ontologies/d3fend.owl#TestCountermeasure"/>
        <rdfs:range rdf:resource="http://d3fend.mitre.org/ontologies/d3fend.owl#TestArtifact"/>
    </owl:ObjectProperty>

</rdf:RDF>
