"""
Tests for the Testcase Chain Service.

This module contains comprehensive tests for the testcase chain service,
including chain creation, validation, execution orchestration, and dependency resolution.
"""

import pytest
from datetime import datetime
from unittest.mock import Mock, patch

from sqlalchemy.orm import Session

from api.models.testcase_chain import (
    ChainExecution,
    NodeExecution,
    TestcaseChain,
    TestcaseChainEdge,
    TestcaseChainNode,
)
from api.schemas.testcase_chain import ChainValidationResult
from api.services.testcase_chain_service import TestcaseChainService


@pytest.fixture
def mock_db():
    """Create a mock database session."""
    return Mock(spec=Session)


@pytest.fixture
def testcase_chain_service(mock_db):
    """Create a testcase chain service instance."""
    return TestcaseChainService(mock_db)


@pytest.fixture
def sample_chain():
    """Create a sample testcase chain."""
    return TestcaseChain(
        id=1,
        name="Test Attack Chain",
        description="Sample attack chain for testing",
        status="draft",
        chain_type="sequential",
        max_execution_time_minutes=60,
        retry_on_failure=False,
        auto_cleanup=True,
        created_by=1
    )


@pytest.fixture
def sample_nodes():
    """Create sample chain nodes."""
    return [
        TestcaseChainNode(
            id=1,
            chain_id=1,
            testcase_id=101,
            node_type="start",
            execution_order=1,
            position_x=100.0,
            position_y=100.0
        ),
        TestcaseChainNode(
            id=2,
            chain_id=1,
            testcase_id=102,
            node_type="standard",
            execution_order=2,
            position_x=200.0,
            position_y=100.0
        ),
        TestcaseChainNode(
            id=3,
            chain_id=1,
            testcase_id=103,
            node_type="end",
            execution_order=3,
            position_x=300.0,
            position_y=100.0
        )
    ]


@pytest.fixture
def sample_edges():
    """Create sample chain edges."""
    return [
        TestcaseChainEdge(
            id=1,
            source_node_id=1,
            target_node_id=2,
            edge_type="standard",
            weight=1
        ),
        TestcaseChainEdge(
            id=2,
            source_node_id=2,
            target_node_id=3,
            edge_type="standard",
            weight=1
        )
    ]


class TestTestcaseChainService:
    """Test cases for the TestcaseChainService class."""
    
    def test_create_chain(self, testcase_chain_service, mock_db):
        """Test creating a new testcase chain."""
        result = testcase_chain_service.create_chain(
            name="Test Chain",
            description="Test description",
            chain_type="sequential",
            max_execution_time_minutes=90,
            retry_on_failure=True,
            auto_cleanup=False,
            created_by=1
        )
        
        assert result.name == "Test Chain"
        assert result.description == "Test description"
        assert result.chain_type == "sequential"
        assert result.max_execution_time_minutes == 90
        assert result.retry_on_failure is True
        assert result.auto_cleanup is False
        assert result.created_by == 1
        
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()
    
    def test_add_node_to_chain_success(self, testcase_chain_service, mock_db, sample_chain):
        """Test successfully adding a node to a chain."""
        mock_db.query.return_value.filter.return_value.first.side_effect = [
            sample_chain,  # Chain exists
            None  # No existing node
        ]
        
        result = testcase_chain_service.add_node_to_chain(
            chain_id=1,
            testcase_id=101,
            node_type="start",
            execution_order=1,
            position_x=100.0,
            position_y=100.0,
            timeout_minutes=30,
            retry_count=2,
            continue_on_failure=True,
            required_for_completion=False
        )
        
        assert result.chain_id == 1
        assert result.testcase_id == 101
        assert result.node_type == "start"
        assert result.execution_order == 1
        assert result.position_x == 100.0
        assert result.position_y == 100.0
        assert result.timeout_minutes == 30
        assert result.retry_count == 2
        assert result.continue_on_failure is True
        assert result.required_for_completion is False
        
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()
    
    def test_add_node_to_chain_chain_not_found(self, testcase_chain_service, mock_db):
        """Test adding a node to a non-existent chain."""
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        with pytest.raises(ValueError, match="Chain with ID 999 not found"):
            testcase_chain_service.add_node_to_chain(
                chain_id=999,
                testcase_id=101
            )
    
    def test_add_node_to_chain_duplicate_testcase(self, testcase_chain_service, mock_db, sample_chain):
        """Test adding a duplicate testcase to a chain."""
        existing_node = TestcaseChainNode(id=1, chain_id=1, testcase_id=101)
        mock_db.query.return_value.filter.return_value.first.side_effect = [
            sample_chain,  # Chain exists
            existing_node  # Existing node
        ]
        
        with pytest.raises(ValueError, match="Testcase 101 already exists in chain 1"):
            testcase_chain_service.add_node_to_chain(
                chain_id=1,
                testcase_id=101
            )
    
    def test_add_edge_to_chain_success(self, testcase_chain_service, mock_db):
        """Test successfully adding an edge to a chain."""
        source_node = TestcaseChainNode(id=1, chain_id=1, testcase_id=101)
        target_node = TestcaseChainNode(id=2, chain_id=1, testcase_id=102)
        
        mock_db.query.return_value.filter.return_value.first.side_effect = [
            source_node,  # Source node exists
            target_node,  # Target node exists
            None  # No existing edge
        ]
        
        result = testcase_chain_service.add_edge_to_chain(
            source_node_id=1,
            target_node_id=2,
            edge_type="success_path",
            condition="result == 'success'",
            weight=2,
            label="Success Path",
            description="Path taken on success"
        )
        
        assert result.source_node_id == 1
        assert result.target_node_id == 2
        assert result.edge_type == "success_path"
        assert result.condition == "result == 'success'"
        assert result.weight == 2
        assert result.label == "Success Path"
        assert result.description == "Path taken on success"
        
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()
    
    def test_add_edge_to_chain_self_loop(self, testcase_chain_service, mock_db):
        """Test adding a self-loop edge (should fail)."""
        with pytest.raises(ValueError, match="Source and target nodes cannot be the same"):
            testcase_chain_service.add_edge_to_chain(
                source_node_id=1,
                target_node_id=1
            )
    
    def test_add_edge_to_chain_nodes_not_found(self, testcase_chain_service, mock_db):
        """Test adding an edge when nodes don't exist."""
        mock_db.query.return_value.filter.return_value.first.side_effect = [
            None,  # Source node not found
            None   # Target node not found
        ]
        
        with pytest.raises(ValueError, match="Source or target node not found"):
            testcase_chain_service.add_edge_to_chain(
                source_node_id=1,
                target_node_id=2
            )
    
    def test_add_edge_to_chain_different_chains(self, testcase_chain_service, mock_db):
        """Test adding an edge between nodes in different chains."""
        source_node = TestcaseChainNode(id=1, chain_id=1, testcase_id=101)
        target_node = TestcaseChainNode(id=2, chain_id=2, testcase_id=102)
        
        mock_db.query.return_value.filter.return_value.first.side_effect = [
            source_node,  # Source node exists
            target_node   # Target node exists (different chain)
        ]
        
        with pytest.raises(ValueError, match="Source and target nodes must be in the same chain"):
            testcase_chain_service.add_edge_to_chain(
                source_node_id=1,
                target_node_id=2
            )
    
    def test_add_edge_to_chain_duplicate_edge(self, testcase_chain_service, mock_db):
        """Test adding a duplicate edge."""
        source_node = TestcaseChainNode(id=1, chain_id=1, testcase_id=101)
        target_node = TestcaseChainNode(id=2, chain_id=1, testcase_id=102)
        existing_edge = TestcaseChainEdge(id=1, source_node_id=1, target_node_id=2)
        
        mock_db.query.return_value.filter.return_value.first.side_effect = [
            source_node,  # Source node exists
            target_node,  # Target node exists
            existing_edge  # Existing edge
        ]
        
        with pytest.raises(ValueError, match="Edge already exists between nodes 1 and 2"):
            testcase_chain_service.add_edge_to_chain(
                source_node_id=1,
                target_node_id=2
            )
    
    def test_validate_chain_success(self, testcase_chain_service, mock_db, sample_chain, sample_nodes, sample_edges):
        """Test successful chain validation."""
        mock_db.query.return_value.filter.return_value.first.return_value = sample_chain
        mock_db.query.return_value.filter.return_value.all.side_effect = [
            sample_nodes,  # Nodes query
            sample_edges   # Edges query
        ]
        
        result = testcase_chain_service.validate_chain(1)
        
        assert isinstance(result, ChainValidationResult)
        assert result.is_valid is True
        assert len(result.errors) == 0
        assert result.cycle_detected is False
        assert len(result.unreachable_nodes) == 0
    
    def test_validate_chain_not_found(self, testcase_chain_service, mock_db):
        """Test validation of non-existent chain."""
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        result = testcase_chain_service.validate_chain(999)
        
        assert result.is_valid is False
        assert "Chain with ID 999 not found" in result.errors
    
    def test_validate_chain_no_nodes(self, testcase_chain_service, mock_db, sample_chain):
        """Test validation of chain with no nodes."""
        mock_db.query.return_value.filter.return_value.first.return_value = sample_chain
        mock_db.query.return_value.filter.return_value.all.return_value = []
        
        result = testcase_chain_service.validate_chain(1)
        
        assert result.is_valid is False
        assert "Chain has no nodes" in result.errors
    
    def test_detect_cycles_no_cycle(self, testcase_chain_service, sample_nodes, sample_edges):
        """Test cycle detection with no cycles."""
        result = testcase_chain_service._detect_cycles(sample_nodes, sample_edges)
        assert result is False
    
    def test_detect_cycles_with_cycle(self, testcase_chain_service):
        """Test cycle detection with a cycle."""
        nodes = [
            TestcaseChainNode(id=1, chain_id=1, testcase_id=101),
            TestcaseChainNode(id=2, chain_id=1, testcase_id=102),
            TestcaseChainNode(id=3, chain_id=1, testcase_id=103)
        ]
        
        # Create a cycle: 1 -> 2 -> 3 -> 1
        edges = [
            TestcaseChainEdge(id=1, source_node_id=1, target_node_id=2),
            TestcaseChainEdge(id=2, source_node_id=2, target_node_id=3),
            TestcaseChainEdge(id=3, source_node_id=3, target_node_id=1)  # Creates cycle
        ]
        
        result = testcase_chain_service._detect_cycles(nodes, edges)
        assert result is True
    
    def test_find_unreachable_nodes_all_reachable(self, testcase_chain_service, sample_nodes, sample_edges):
        """Test finding unreachable nodes when all are reachable."""
        result = testcase_chain_service._find_unreachable_nodes(sample_nodes, sample_edges)
        assert len(result) == 0
    
    def test_find_unreachable_nodes_with_unreachable(self, testcase_chain_service):
        """Test finding unreachable nodes when some are unreachable."""
        nodes = [
            TestcaseChainNode(id=1, chain_id=1, testcase_id=101, node_type="start"),
            TestcaseChainNode(id=2, chain_id=1, testcase_id=102),
            TestcaseChainNode(id=3, chain_id=1, testcase_id=103),  # Unreachable
            TestcaseChainNode(id=4, chain_id=1, testcase_id=104)   # Unreachable
        ]
        
        # Only connect 1 -> 2, leaving 3 and 4 unreachable
        edges = [
            TestcaseChainEdge(id=1, source_node_id=1, target_node_id=2)
        ]
        
        result = testcase_chain_service._find_unreachable_nodes(nodes, edges)
        assert set(result) == {3, 4}
