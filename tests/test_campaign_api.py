"""
Tests for the Campaign Management API.

This module contains tests for the campaign endpoints.
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from api.main import app
from api.models.campaign import Campaign
from api.models.schemas.campaign import Campaign<PERSON><PERSON>, TestCaseAssignment
from api.services.campaign import create_campaign
from api.auth.utils import create_access_token

client = TestClient(app)


@pytest.fixture
def db_session(monkeypatch):
    """Create a test database session."""
    # This would typically use a test database
    from api.database import get_db
    db = next(get_db())
    yield db


@pytest.fixture
def admin_token():
    """Create an admin token for testing."""
    access_token = create_access_token(
        data={"sub": "<EMAIL>", "role": "admin"},
        expires_delta=timedelta(minutes=30)
    )
    return access_token


@pytest.fixture
def user_token():
    """Create a regular user token for testing."""
    access_token = create_access_token(
        data={"sub": "<EMAIL>", "role": "user"},
        expires_delta=timedelta(minutes=30)
    )
    return access_token


@pytest.fixture
def test_campaign(db_session):
    """Create a test campaign."""
    campaign_data = CampaignCreate(
        name="Test Campaign",
        description="Test campaign for API testing",
        status="draft",
        start_date=datetime.utcnow(),
        end_date=datetime.utcnow() + timedelta(days=30)
    )
    
    campaign = create_campaign(db_session, campaign_data, user_id=1)
    return campaign


def test_get_campaigns(admin_token):
    """Test getting a list of campaigns."""
    response = client.get(
        "/api/v1/campaigns/",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert isinstance(response.json(), list)


def test_get_campaign(admin_token, test_campaign):
    """Test getting a specific campaign."""
    response = client.get(
        f"/api/v1/campaigns/{test_campaign.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert response.json()["id"] == test_campaign.id
    assert response.json()["name"] == test_campaign.name


def test_create_campaign(admin_token):
    """Test creating a new campaign."""
    campaign_data = {
        "name": "New Test Campaign",
        "description": "New test campaign for API testing",
        "status": "draft",
        "start_date": datetime.utcnow().isoformat(),
        "end_date": (datetime.utcnow() + timedelta(days=30)).isoformat()
    }
    
    response = client.post(
        "/api/v1/campaigns/",
        json=campaign_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 201
    assert response.json()["name"] == campaign_data["name"]


def test_update_campaign(admin_token, test_campaign):
    """Test updating a campaign."""
    update_data = {
        "name": "Updated Test Campaign",
        "status": "active"
    }
    
    response = client.put(
        f"/api/v1/campaigns/{test_campaign.id}",
        json=update_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert response.json()["name"] == update_data["name"]
    assert response.json()["status"] == update_data["status"]


def test_delete_campaign(admin_token, test_campaign):
    """Test deleting a campaign."""
    response = client.delete(
        f"/api/v1/campaigns/{test_campaign.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 204


def test_restore_campaign(admin_token, test_campaign, db_session):
    """Test restoring a soft-deleted campaign."""
    # First, delete the campaign
    test_campaign.soft_delete(db_session)
    
    response = client.post(
        f"/api/v1/campaigns/{test_campaign.id}/restore",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert response.json()["id"] == test_campaign.id
    assert response.json()["deleted_at"] is None


def test_assign_test_cases(admin_token, test_campaign):
    """Test assigning test cases to a campaign."""
    assignment_data = {
        "test_case_ids": [1, 2, 3]
    }
    
    response = client.post(
        f"/api/v1/campaigns/{test_campaign.id}/test-cases",
        json=assignment_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 204


def test_remove_test_case(admin_token, test_campaign):
    """Test removing a test case from a campaign."""
    response = client.delete(
        f"/api/v1/campaigns/{test_campaign.id}/test-cases/1",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 204


def test_get_campaign_summary(admin_token, test_campaign):
    """Test getting a campaign summary."""
    response = client.get(
        f"/api/v1/campaigns/{test_campaign.id}/summary",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert "total_test_cases" in response.json()
    assert "total_assessments" in response.json()
    assert "test_case_status" in response.json()
    assert "assessment_status" in response.json()
    assert "completion_percentage" in response.json()


def test_unauthorized_access(user_token):
    """Test unauthorized access to admin-only endpoints."""
    # Regular users can't restore campaigns
    response = client.post(
        "/api/v1/campaigns/1/restore",
        headers={"Authorization": f"Bearer {user_token}"}
    )
    assert response.status_code == 403 