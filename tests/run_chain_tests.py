#!/usr/bin/env python3
"""
Test runner for Enhanced Testcase Chaining & Sequencing tests.

This script provides a comprehensive test runner for all testcase chaining
tests with different execution modes and reporting options.
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path
from typing import List, Dict, Any


def run_command(cmd: List[str], description: str) -> Dict[str, Any]:
    """Run a command and return results."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            check=False
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"Exit code: {result.returncode}")
        print(f"Duration: {duration:.2f} seconds")
        
        if result.stdout:
            print(f"\nSTDOUT:\n{result.stdout}")
        
        if result.stderr:
            print(f"\nSTDERR:\n{result.stderr}")
        
        return {
            "success": result.returncode == 0,
            "returncode": result.returncode,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "duration": duration,
            "description": description
        }
        
    except Exception as e:
        print(f"Error running command: {e}")
        return {
            "success": False,
            "returncode": -1,
            "stdout": "",
            "stderr": str(e),
            "duration": 0,
            "description": description
        }


def run_unit_tests() -> Dict[str, Any]:
    """Run unit tests for testcase chain service."""
    cmd = [
        "python", "-m", "pytest",
        "tests/test_testcase_chain_service.py",
        "-v",
        "--tb=short",
        "--durations=10",
        "-m", "not slow"
    ]
    return run_command(cmd, "Unit Tests - Testcase Chain Service")


def run_api_tests() -> Dict[str, Any]:
    """Run API tests for testcase chain endpoints."""
    cmd = [
        "python", "-m", "pytest",
        "tests/test_testcase_chain_api.py",
        "-v",
        "--tb=short",
        "--durations=10"
    ]
    return run_command(cmd, "API Tests - Testcase Chain Endpoints")


def run_integration_tests() -> Dict[str, Any]:
    """Run integration tests with real database."""
    cmd = [
        "python", "-m", "pytest",
        "tests/test_testcase_chain_integration.py",
        "-v",
        "--tb=short",
        "--durations=10",
        "-m", "integration"
    ]
    return run_command(cmd, "Integration Tests - Real Database")


def run_behavior_tests() -> Dict[str, Any]:
    """Run behavioral tests for attack scenarios."""
    cmd = [
        "python", "-m", "pytest",
        "tests/test_testcase_chain_behavior.py",
        "-v",
        "--tb=short",
        "--durations=10",
        "-m", "behavior"
    ]
    return run_command(cmd, "Behavioral Tests - Attack Scenarios")


def run_e2e_tests() -> Dict[str, Any]:
    """Run end-to-end tests."""
    cmd = [
        "python", "-m", "pytest",
        "tests/test_testcase_chain_e2e.py",
        "-v",
        "--tb=short",
        "--durations=10",
        "-m", "e2e"
    ]
    return run_command(cmd, "End-to-End Tests - Complete Workflows")


def run_performance_tests() -> Dict[str, Any]:
    """Run performance tests."""
    cmd = [
        "python", "-m", "pytest",
        "tests/test_testcase_chain_performance.py",
        "-v",
        "--tb=short",
        "--durations=10",
        "-m", "performance"
    ]
    return run_command(cmd, "Performance Tests - Load and Scalability")


def run_all_chain_tests() -> Dict[str, Any]:
    """Run all testcase chain tests."""
    cmd = [
        "python", "-m", "pytest",
        "tests/test_testcase_chain_service.py",
        "tests/test_testcase_chain_api.py",
        "tests/test_testcase_chain_integration.py",
        "tests/test_testcase_chain_behavior.py",
        "tests/test_testcase_chain_e2e.py",
        "tests/test_testcase_chain_performance.py",
        "-v",
        "--tb=short",
        "--durations=20",
        "--cov=api.services.testcase_chain_service",
        "--cov=api.routes.v1_testcase_chain",
        "--cov=api.models.testcase_chain",
        "--cov-report=html:htmlcov/chain_tests",
        "--cov-report=term-missing"
    ]
    return run_command(cmd, "All Testcase Chain Tests with Coverage")


def run_quick_tests() -> Dict[str, Any]:
    """Run quick tests (unit and API only)."""
    cmd = [
        "python", "-m", "pytest",
        "tests/test_testcase_chain_service.py",
        "tests/test_testcase_chain_api.py",
        "-v",
        "--tb=short",
        "-m", "not slow and not performance"
    ]
    return run_command(cmd, "Quick Tests - Unit and API")


def run_slow_tests() -> Dict[str, Any]:
    """Run slow tests (integration, behavior, e2e, performance)."""
    cmd = [
        "python", "-m", "pytest",
        "tests/test_testcase_chain_integration.py",
        "tests/test_testcase_chain_behavior.py",
        "tests/test_testcase_chain_e2e.py",
        "tests/test_testcase_chain_performance.py",
        "-v",
        "--tb=short",
        "--durations=20"
    ]
    return run_command(cmd, "Slow Tests - Integration, Behavior, E2E, Performance")


def generate_test_report(results: List[Dict[str, Any]]) -> None:
    """Generate a comprehensive test report."""
    print(f"\n{'='*80}")
    print("TESTCASE CHAINING TEST REPORT")
    print(f"{'='*80}")
    
    total_tests = len(results)
    passed_tests = sum(1 for r in results if r["success"])
    failed_tests = total_tests - passed_tests
    total_duration = sum(r["duration"] for r in results)
    
    print(f"\nSUMMARY:")
    print(f"  Total Test Suites: {total_tests}")
    print(f"  Passed: {passed_tests}")
    print(f"  Failed: {failed_tests}")
    print(f"  Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    print(f"  Total Duration: {total_duration:.2f} seconds")
    
    print(f"\nDETAILED RESULTS:")
    for i, result in enumerate(results, 1):
        status = "✅ PASS" if result["success"] else "❌ FAIL"
        print(f"  {i}. {status} - {result['description']} ({result['duration']:.2f}s)")
        if not result["success"]:
            print(f"     Error: {result['stderr'][:100]}...")
    
    if failed_tests > 0:
        print(f"\n⚠️  {failed_tests} test suite(s) failed. Check the detailed output above.")
    else:
        print(f"\n🎉 All test suites passed successfully!")
    
    print(f"\n{'='*80}")


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(
        description="Test runner for Enhanced Testcase Chaining & Sequencing"
    )
    
    parser.add_argument(
        "test_type",
        choices=[
            "unit", "api", "integration", "behavior", "e2e", "performance",
            "all", "quick", "slow"
        ],
        help="Type of tests to run"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output"
    )
    
    parser.add_argument(
        "--coverage", "-c",
        action="store_true",
        help="Generate coverage report"
    )
    
    args = parser.parse_args()
    
    # Change to project root directory
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    print(f"Running testcase chaining tests from: {os.getcwd()}")
    print(f"Test type: {args.test_type}")
    
    # Map test types to functions
    test_functions = {
        "unit": run_unit_tests,
        "api": run_api_tests,
        "integration": run_integration_tests,
        "behavior": run_behavior_tests,
        "e2e": run_e2e_tests,
        "performance": run_performance_tests,
        "all": run_all_chain_tests,
        "quick": run_quick_tests,
        "slow": run_slow_tests
    }
    
    # Run the selected tests
    start_time = time.time()
    
    if args.test_type in test_functions:
        result = test_functions[args.test_type]()
        results = [result]
    else:
        print(f"Unknown test type: {args.test_type}")
        sys.exit(1)
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # Generate report
    generate_test_report(results)
    
    print(f"\nTotal execution time: {total_time:.2f} seconds")
    
    # Exit with appropriate code
    if all(r["success"] for r in results):
        print("\n✅ All tests completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
