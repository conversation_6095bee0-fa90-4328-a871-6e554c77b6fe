"""
Unit tests for heat map service.

This module provides comprehensive unit tests for the HeatMapService class,
following PEP 8 style guidelines and testing best practices.
"""

import pytest
from datetime import datetime, timezone
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from api.services.heatmap_service import HeatMapService
from api.models import (
    AttackTechniqueResult,
    HeatMapSnapshot,
    Campaign,
    Assessment,
    MitreTechnique,
    MitreTactic,
    TestCase
)
from api.schemas.heatmap import (
    HeatMapType,
    TechniqueStatus
)


class TestHeatMapService:
    """Test cases for HeatMapService class."""
    
    @pytest.fixture
    def mock_db(self) -> Mock:
        """Create mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def heatmap_service(self, mock_db: Mock) -> HeatMapService:
        """Create HeatMapService instance with mock database."""
        return HeatMapService(mock_db)
    
    @pytest.fixture
    def sample_campaign(self) -> Campaign:
        """Create sample campaign for testing."""
        campaign = Campaign()
        campaign.id = 1
        campaign.name = "Test Campaign"
        campaign.description = "Test campaign description"
        return campaign
    
    @pytest.fixture
    def sample_assessment(self) -> Assessment:
        """Create sample assessment for testing."""
        assessment = Assessment()
        assessment.id = 1
        assessment.name = "Test Assessment"
        assessment.description = "Test assessment description"
        return assessment
    
    @pytest.fixture
    def sample_technique(self) -> MitreTechnique:
        """Create sample MITRE technique for testing."""
        technique = MitreTechnique()
        technique.technique_id = "T1566"
        technique.name = "Phishing"
        technique.tactic_id = 1
        return technique
    
    @pytest.fixture
    def sample_tactic(self) -> MitreTactic:
        """Create sample MITRE tactic for testing."""
        tactic = MitreTactic()
        tactic.id = 1
        tactic.tactic_id = "TA0001"
        tactic.name = "Initial Access"
        return tactic
    
    @pytest.fixture
    def sample_technique_result(self) -> AttackTechniqueResult:
        """Create sample technique result for testing."""
        result = AttackTechniqueResult()
        result.id = 1
        result.technique_id = "T1566"
        result.campaign_id = 1
        result.assessment_id = None
        result.effectiveness_score = 0.8
        result.detection_rate = 0.7
        result.prevention_rate = 0.6
        result.execution_count = 10
        result.success_count = 8
        result.detection_count = 7
        result.prevention_count = 6
        result.last_tested = datetime.now(timezone.utc)
        result.test_environment = "test"
        result.notes = "Test notes"
        result.created_at = datetime.now(timezone.utc)
        result.updated_at = datetime.now(timezone.utc)
        result.deleted_at = None
        return result
    
    async def test_generate_campaign_heatmap_success(
        self,
        heatmap_service: HeatMapService,
        mock_db: Mock,
        sample_campaign: Campaign,
        sample_technique: MitreTechnique,
        sample_tactic: MitreTactic,
        sample_technique_result: AttackTechniqueResult
    ) -> None:
        """Test successful campaign heat map generation."""
        # Setup mock database queries
        mock_db.query.return_value.filter.return_value.first.return_value = sample_campaign
        mock_db.query.return_value.filter.return_value.all.return_value = [sample_technique_result]
        
        # Mock technique and tactic queries
        def mock_query_side_effect(model):
            if model == MitreTechnique:
                mock_query = Mock()
                mock_query.filter.return_value.first.return_value = sample_technique
                return mock_query
            elif model == MitreTactic:
                mock_query = Mock()
                mock_query.filter.return_value.first.return_value = sample_tactic
                return mock_query
            elif model == TestCase:
                mock_query = Mock()
                mock_query.filter.return_value.count.return_value = 5
                return mock_query
            else:
                return Mock()
        
        mock_db.query.side_effect = mock_query_side_effect
        
        # Execute test
        result = await heatmap_service.generate_campaign_heatmap(
            campaign_id=1,
            save_snapshot=False
        )
        
        # Assertions
        assert result.name == "Campaign Test Campaign Heat Map"
        assert result.heat_map_type == HeatMapType.CAMPAIGN
        assert result.campaign_id == 1
        assert result.assessment_id is None
        assert len(result.techniques) == 1
        assert len(result.tactics) == 1
        assert result.metrics.total_techniques == 1
        assert result.metrics.covered_techniques == 1
        assert result.metrics.overall_coverage_percentage == 100.0
    
    async def test_generate_campaign_heatmap_campaign_not_found(
        self,
        heatmap_service: HeatMapService,
        mock_db: Mock
    ) -> None:
        """Test campaign heat map generation with non-existent campaign."""
        # Setup mock to return None for campaign
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Execute test and expect ValueError
        with pytest.raises(ValueError, match="Campaign 999 not found"):
            await heatmap_service.generate_campaign_heatmap(campaign_id=999)
    
    async def test_generate_assessment_heatmap_success(
        self,
        heatmap_service: HeatMapService,
        mock_db: Mock,
        sample_assessment: Assessment,
        sample_technique: MitreTechnique,
        sample_tactic: MitreTactic,
        sample_technique_result: AttackTechniqueResult
    ) -> None:
        """Test successful assessment heat map generation."""
        # Modify technique result for assessment
        sample_technique_result.assessment_id = 1
        sample_technique_result.campaign_id = None
        
        # Setup mock database queries
        mock_db.query.return_value.filter.return_value.first.return_value = sample_assessment
        mock_db.query.return_value.filter.return_value.all.return_value = [sample_technique_result]
        
        # Mock technique and tactic queries
        def mock_query_side_effect(model):
            if model == MitreTechnique:
                mock_query = Mock()
                mock_query.filter.return_value.first.return_value = sample_technique
                return mock_query
            elif model == MitreTactic:
                mock_query = Mock()
                mock_query.filter.return_value.first.return_value = sample_tactic
                return mock_query
            elif model == TestCase:
                mock_query = Mock()
                mock_query.filter.return_value.count.return_value = 3
                return mock_query
            else:
                return Mock()
        
        mock_db.query.side_effect = mock_query_side_effect
        
        # Execute test
        result = await heatmap_service.generate_assessment_heatmap(
            assessment_id=1,
            save_snapshot=False
        )
        
        # Assertions
        assert result.name == "Assessment Test Assessment Heat Map"
        assert result.heat_map_type == HeatMapType.ASSESSMENT
        assert result.campaign_id is None
        assert result.assessment_id == 1
        assert len(result.techniques) == 1
        assert len(result.tactics) == 1
    
    def test_determine_technique_status_covered(
        self,
        heatmap_service: HeatMapService
    ) -> None:
        """Test technique status determination for covered technique."""
        status = heatmap_service._determine_technique_status(0.9)
        assert status == TechniqueStatus.COVERED
    
    def test_determine_technique_status_partial(
        self,
        heatmap_service: HeatMapService
    ) -> None:
        """Test technique status determination for partially covered technique."""
        status = heatmap_service._determine_technique_status(0.6)
        assert status == TechniqueStatus.PARTIAL
    
    def test_determine_technique_status_not_covered(
        self,
        heatmap_service: HeatMapService
    ) -> None:
        """Test technique status determination for not covered technique."""
        status = heatmap_service._determine_technique_status(0.2)
        assert status == TechniqueStatus.NOT_COVERED
    
    async def test_save_snapshot_success(
        self,
        heatmap_service: HeatMapService,
        mock_db: Mock
    ) -> None:
        """Test successful snapshot saving."""
        # Create mock heat map response
        from api.schemas.heatmap import (
            HeatMapResponse,
            TechniqueHeatMapData,
            TacticHeatMapData,
            HeatMapMetrics
        )
        
        technique_data = TechniqueHeatMapData(
            technique_id="T1566",
            technique_name="Phishing",
            tactic_id="TA0001",
            tactic_name="Initial Access",
            status=TechniqueStatus.COVERED,
            effectiveness_score=0.8,
            detection_rate=0.7,
            prevention_rate=0.6,
            execution_count=10,
            last_tested=datetime.now(timezone.utc),
            test_cases_count=5
        )
        
        tactic_data = TacticHeatMapData(
            tactic_id="TA0001",
            tactic_name="Initial Access",
            techniques_count=1,
            covered_count=1,
            partial_count=0,
            not_covered_count=0,
            average_effectiveness=0.8,
            coverage_percentage=100.0
        )
        
        metrics = HeatMapMetrics(
            total_techniques=1,
            covered_techniques=1,
            partial_techniques=0,
            not_covered_techniques=0,
            overall_coverage_percentage=100.0,
            average_effectiveness=0.8,
            average_detection_rate=0.7,
            average_prevention_rate=0.6,
            total_executions=10,
            last_updated=datetime.now(timezone.utc)
        )
        
        heatmap_response = HeatMapResponse(
            name="Test Heat Map",
            description="Test description",
            heat_map_type=HeatMapType.CAMPAIGN,
            campaign_id=1,
            assessment_id=None,
            techniques=[technique_data],
            tactics=[tactic_data],
            metrics=metrics,
            generated_at=datetime.now(timezone.utc)
        )
        
        # Mock database operations
        mock_snapshot = Mock()
        mock_snapshot.id = 1
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        
        # Execute test
        with patch('api.services.heatmap_service.HeatMapSnapshot', return_value=mock_snapshot):
            result = await heatmap_service._save_snapshot(
                heatmap_response=heatmap_response,
                snapshot_name="Test Snapshot",
                campaign_id=1
            )
        
        # Assertions
        assert result == mock_snapshot
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()
