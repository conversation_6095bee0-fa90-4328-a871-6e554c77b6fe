"""Tests for the MITRE ATT&CK technique scoring feature."""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.main import app
from api.models import MitreTechnique, MitreVersion, TechniqueScore
from api.models.schemas import ScoreCategory

client = TestClient(app)

@pytest.fixture
def test_technique(db: Session):
    """Create a test technique for scoring."""
    # Create a test version
    version = MitreVersion(
        version="test-version",
        is_current=True
    )
    db.add(version)
    db.commit()
    
    # Create a test technique
    technique = MitreTechnique(
        technique_id="T9999",
        name="Test Technique",
        description="Test technique for scoring",
        version_id=version.id
    )
    db.add(technique)
    db.commit()
    
    yield technique
    
    # Clean up
    db.query(TechniqueScore).filter_by(technique_id=technique.id).delete()
    db.query(MitreTechnique).filter_by(id=technique.id).delete()
    db.query(MitreVersion).filter_by(id=version.id).delete()
    db.commit()

def test_create_technique_score(test_technique, db: Session):
    """Test creating a score for a technique."""
    # Create a score
    score_data = {
        "technique_id": test_technique.id,
        "category": "impact",
        "score": 8.5,
        "weight": 0.8,
        "notes": "Test score"
    }
    
    response = client.post(
        f"/api/v1/mitre/techniques/{test_technique.id}/scores",
        json=score_data
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["technique_id"] == test_technique.id
    assert data["category"] == "impact"
    assert data["score"] == 8.5
    assert data["weight"] == 0.8
    assert data["notes"] == "Test score"
    
    # Verify in database
    db_score = db.query(TechniqueScore).filter_by(technique_id=test_technique.id).first()
    assert db_score is not None
    assert db_score.category == "impact"
    assert db_score.score == 8.5

def test_get_technique_scores(test_technique, db: Session):
    """Test retrieving scores for a technique."""
    # Create test scores
    score1 = TechniqueScore(
        technique_id=test_technique.id,
        category="impact",
        score=8.5,
        weight=0.8,
        notes="Impact score"
    )
    score2 = TechniqueScore(
        technique_id=test_technique.id,
        category="likelihood",
        score=6.0,
        weight=1.0,
        notes="Likelihood score"
    )
    db.add(score1)
    db.add(score2)
    db.commit()
    
    # Get scores
    response = client.get(f"/api/v1/mitre/techniques/{test_technique.id}/scores")
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 2
    
    categories = [score["category"] for score in data]
    assert "impact" in categories
    assert "likelihood" in categories

def test_get_technique_with_scores(test_technique, db: Session):
    """Test retrieving a technique with its scores and overall score."""
    # Create test scores
    score1 = TechniqueScore(
        technique_id=test_technique.id,
        category="impact",
        score=8.5,
        weight=0.8,
        notes="Impact score"
    )
    score2 = TechniqueScore(
        technique_id=test_technique.id,
        category="likelihood",
        score=6.0,
        weight=1.0,
        notes="Likelihood score"
    )
    db.add(score1)
    db.add(score2)
    db.commit()
    
    # Get technique with scores
    response = client.get(f"/api/v1/mitre/techniques/{test_technique.id}/with-scores")
    
    assert response.status_code == 200
    data = response.json()
    
    assert data["id"] == test_technique.id
    assert data["technique_id"] == test_technique.technique_id
    assert data["name"] == test_technique.name
    assert len(data["scores"]) == 2
    
    # Verify overall score calculation
    # (8.5 * 0.8 + 6.0 * 1.0) / (0.8 + 1.0) = 7.11
    assert round(data["overall_score"], 2) == 7.11

def test_bulk_create_technique_scores(test_technique, db: Session):
    """Test bulk creating scores for techniques."""
    # Create bulk scores
    score_data = {
        "scores": [
            {
                "technique_id": test_technique.id,
                "category": "impact",
                "score": 8.5,
                "weight": 0.8,
                "notes": "Impact score"
            },
            {
                "technique_id": test_technique.id,
                "category": "likelihood",
                "score": 6.0,
                "weight": 1.0,
                "notes": "Likelihood score"
            }
        ]
    }
    
    response = client.post(
        "/api/v1/mitre/techniques/scores/bulk",
        json=score_data
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "Successfully processed 2 scores" in data["message"]
    
    # Verify in database
    db_scores = db.query(TechniqueScore).filter_by(technique_id=test_technique.id).all()
    assert len(db_scores) == 2
    
    categories = [score.category for score in db_scores]
    assert "impact" in categories
    assert "likelihood" in categories

def test_get_score_categories():
    """Test retrieving available score categories."""
    response = client.get("/api/v1/mitre/techniques/scores/categories")
    
    assert response.status_code == 200
    data = response.json()
    
    # Verify all categories are returned
    assert "impact" in data
    assert "likelihood" in data
    assert "detectability" in data
    assert "exploitability" in data
    assert "custom" in data
    assert len(data) == 5

def test_get_top_scored_techniques(test_technique, db: Session):
    """Test retrieving top-scored techniques."""
    # Create test scores
    score = TechniqueScore(
        technique_id=test_technique.id,
        category="impact",
        score=8.5,
        weight=1.0,
        notes="Impact score"
    )
    db.add(score)
    db.commit()
    
    # Get top-scored techniques
    response = client.get("/api/v1/mitre/techniques/top-scored?category=impact&limit=5")
    
    assert response.status_code == 200
    data = response.json()
    
    assert len(data) > 0
    assert data[0]["id"] == test_technique.id
    assert data[0]["overall_score"] == 8.5 