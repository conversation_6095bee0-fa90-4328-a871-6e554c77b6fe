"""
Tests for comprehensive tagging system feature
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from api.main import app
from models.comprehensive_tagging_system import TagCreate, TagUpdate, EntityTags
from datetime import datetime
import json

client = TestClient(app)

@pytest.fixture
def sample_tag_data():
    """Fixture for sample tag data"""
    return {
        "name": "Test Tag",
        "description": "Test description",
        "color": "#FF5733"
    }

@pytest.fixture
def auth_headers():
    """Fixture for authentication headers"""
    # This is a placeholder - in a real test, you would get a valid token
    return {"Authorization": "Bearer test_token"}

def test_get_all_tags(auth_headers):
    """Test getting all tags"""
    response = client.get("/api/v1/tags/", headers=auth_headers)
    assert response.status_code == 200
    assert isinstance(response.json(), list)

def test_get_tag_not_found(auth_headers):
    """Test getting a non-existent tag"""
    tag_id = 9999  # Assuming this ID doesn't exist
    response = client.get(f"/api/v1/tags/{tag_id}", headers=auth_headers)
    assert response.status_code == 404

def test_create_tag(sample_tag_data, auth_headers):
    """Test creating a tag"""
    response = client.post("/api/v1/tags/", json=sample_tag_data, headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == sample_tag_data["name"]
    assert data["description"] == sample_tag_data["description"]
    assert data["color"] == sample_tag_data["color"]
    assert "id" in data
    assert "created_at" in data
    
    # Store the created ID for other tests
    return data["id"]

def test_update_tag(sample_tag_data, auth_headers):
    """Test updating a tag"""
    # First create a tag
    create_response = client.post("/api/v1/tags/", json=sample_tag_data, headers=auth_headers)
    tag_id = create_response.json()["id"]
    
    # Now update it
    update_data = {
        "name": "Updated Tag",
        "description": "Updated description",
        "color": "#3498DB"
    }
    response = client.put(f"/api/v1/tags/{tag_id}", json=update_data, headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == update_data["name"]
    assert data["description"] == update_data["description"]
    assert data["color"] == update_data["color"]
    assert "updated_at" in data
    
    # Verify the update with a GET request
    get_response = client.get(f"/api/v1/tags/{tag_id}", headers=auth_headers)
    assert get_response.status_code == 200
    assert get_response.json()["name"] == update_data["name"]

def test_delete_tag(sample_tag_data, auth_headers):
    """Test deleting a tag"""
    # First create a tag
    create_response = client.post("/api/v1/tags/", json=sample_tag_data, headers=auth_headers)
    tag_id = create_response.json()["id"]
    
    # Now delete it
    response = client.delete(f"/api/v1/tags/{tag_id}", headers=auth_headers)
    assert response.status_code == 200
    assert response.json()["message"] == "Tag deleted"
    
    # Verify it's gone with a GET request
    get_response = client.get(f"/api/v1/tags/{tag_id}", headers=auth_headers)
    assert get_response.status_code == 404

def test_tag_validation_errors(auth_headers):
    """Test validation errors when creating a tag"""
    # Missing required field
    invalid_data = {
        "description": "Missing name field"
    }
    response = client.post("/api/v1/tags/", json=invalid_data, headers=auth_headers)
    assert response.status_code == 422
    
    # Invalid color format
    invalid_data = {
        "name": "Invalid Color Tag",
        "description": "Tag with invalid color",
        "color": "not-a-hex-color"
    }
    response = client.post("/api/v1/tags/", json=invalid_data, headers=auth_headers)
    assert response.status_code == 422

def test_tag_association(sample_tag_data, auth_headers):
    """Test associating tags with entities"""
    # First create a tag
    create_response = client.post("/api/v1/tags/", json=sample_tag_data, headers=auth_headers)
    tag_id = create_response.json()["id"]
    
    # Associate tag with a test entity
    entity_data = {
        "entity_type": "test_case",
        "entity_id": 1,
        "tag_ids": [tag_id]
    }
    response = client.post("/api/v1/tags/associate", json=entity_data, headers=auth_headers)
    assert response.status_code == 200
    assert len(response.json()) > 0
    
    # Get tags for the entity
    get_response = client.get(f"/api/v1/tags/entity/test_case/1", headers=auth_headers)
    assert get_response.status_code == 200
    assert len(get_response.json()) > 0
    assert get_response.json()[0]["id"] == tag_id
    
    # Dissociate tag
    response = client.delete("/api/v1/tags/dissociate", json=entity_data, headers=auth_headers)
    assert response.status_code == 200
    
    # Verify tag is dissociated
    get_response = client.get(f"/api/v1/tags/entity/test_case/1", headers=auth_headers)
    assert get_response.status_code == 200
    assert len(get_response.json()) == 0

def test_tag_stats(sample_tag_data, auth_headers):
    """Test tag statistics"""
    # First create a tag
    create_response = client.post("/api/v1/tags/", json=sample_tag_data, headers=auth_headers)
    tag_id = create_response.json()["id"]
    
    # Associate tag with multiple entities
    entity_types = ["test_case", "campaign", "assessment"]
    for entity_type in entity_types:
        entity_data = {
            "entity_type": entity_type,
            "entity_id": 1,
            "tag_ids": [tag_id]
        }
        client.post("/api/v1/tags/associate", json=entity_data, headers=auth_headers)
    
    # Get tag stats
    response = client.get("/api/v1/tags/stats", headers=auth_headers)
    assert response.status_code == 200
    
    # Find our tag in the stats
    tag_stats = None
    for stat in response.json():
        if stat["id"] == tag_id:
            tag_stats = stat
            break
    
    assert tag_stats is not None
    assert tag_stats["count"] == 3
    assert len(tag_stats["entity_types"]) == 3
    for entity_type in entity_types:
        assert entity_type in tag_stats["entity_types"]
        assert tag_stats["entity_types"][entity_type] == 1
