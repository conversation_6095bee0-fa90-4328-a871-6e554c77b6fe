"""
Tests for bulk assessment operations API.

This module contains tests for bulk operations on assessments and test executions.
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import random
import string

from api.main import app
from api.models.assessment import Assessment, TestExecution
from api.models.schemas.assessment import AssessmentCreate, TestExecutionCreate
from api.services.assessment import create_assessment, create_test_execution
from api.auth.utils import create_access_token

client = TestClient(app)


@pytest.fixture
def db_session(monkeypatch):
    """Create a test database session."""
    # This would typically use a test database
    from api.database import get_db
    db = next(get_db())
    yield db


@pytest.fixture
def admin_token():
    """Create an admin token for testing."""
    access_token = create_access_token(
        data={"sub": "<EMAIL>", "role": "admin"},
        expires_delta=timedelta(minutes=30)
    )
    return access_token


def generate_unique_name():
    """Generate a unique name for test data."""
    return "Test " + ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))


def test_bulk_create_assessments(admin_token):
    """Test creating multiple assessments in one request."""
    bulk_data = {
        "assessments": [
            {
                "name": generate_unique_name(),
                "description": "Bulk created assessment 1",
                "target_system": "System A",
                "assessment_type": "vulnerability",
                "status": "planned",
                "start_date": datetime.utcnow().isoformat(),
                "end_date": (datetime.utcnow() + timedelta(days=30)).isoformat()
            },
            {
                "name": generate_unique_name(),
                "description": "Bulk created assessment 2",
                "target_system": "System B",
                "assessment_type": "penetration",
                "status": "planned",
                "start_date": datetime.utcnow().isoformat(),
                "end_date": (datetime.utcnow() + timedelta(days=30)).isoformat()
            },
            {
                "name": generate_unique_name(),
                "description": "Bulk created assessment 3",
                "target_system": "System C",
                "assessment_type": "code_review",
                "status": "planned",
                "start_date": datetime.utcnow().isoformat(),
                "end_date": (datetime.utcnow() + timedelta(days=30)).isoformat()
            }
        ]
    }
    
    response = client.post(
        "/api/v1/assessments/bulk",
        json=bulk_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 201
    
    # Verify the response contains information about the created assessments
    results = response.json()
    assert "created" in results
    assert results["created"] == 3
    assert "assessments" in results
    assert len(results["assessments"]) == 3
    
    # Check that each assessment has a valid ID
    assessment_ids = [a["id"] for a in results["assessments"]]
    assert len(assessment_ids) == 3
    
    # Check that the assessments were actually created
    for assessment_id in assessment_ids:
        response = client.get(
            f"/api/v1/assessments/{assessment_id}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 200
    
    # Clean up created assessments
    for assessment_id in assessment_ids:
        client.delete(
            f"/api/v1/assessments/{assessment_id}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )


def test_bulk_update_assessments(admin_token):
    """Test updating multiple assessments in one request."""
    # First, create assessments to update
    assessment_ids = []
    for i in range(3):
        assessment_data = {
            "name": generate_unique_name(),
            "description": f"Test assessment {i+1} for bulk update",
            "target_system": f"System {i+1}",
            "assessment_type": "vulnerability",
            "status": "planned",
            "start_date": datetime.utcnow().isoformat(),
            "end_date": (datetime.utcnow() + timedelta(days=30)).isoformat()
        }
        
        response = client.post(
            "/api/v1/assessments/",
            json=assessment_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 201
        assessment_ids.append(response.json()["id"])
    
    # Now update all assessments to "in_progress" status
    bulk_update_data = {
        "assessments": [
            {
                "id": assessment_ids[0],
                "status": "in_progress",
                "description": "Updated description for assessment 1"
            },
            {
                "id": assessment_ids[1],
                "status": "in_progress",
                "target_system": "Updated System 2"
            },
            {
                "id": assessment_ids[2],
                "status": "in_progress",
                "assessment_type": "compliance"
            }
        ]
    }
    
    response = client.put(
        "/api/v1/assessments/bulk",
        json=bulk_update_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    
    # Verify the response contains information about the updated assessments
    results = response.json()
    assert "updated" in results
    assert results["updated"] == 3
    
    # Check that each assessment was actually updated
    for i, assessment_id in enumerate(assessment_ids):
        response = client.get(
            f"/api/v1/assessments/{assessment_id}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 200
        assessment = response.json()
        
        # All should have status updated to "in_progress"
        assert assessment["status"] == "in_progress"
        
        # Check specific updates for each assessment
        if i == 0:
            assert assessment["description"] == "Updated description for assessment 1"
        elif i == 1:
            assert assessment["target_system"] == "Updated System 2"
        elif i == 2:
            assert assessment["assessment_type"] == "compliance"
    
    # Clean up created assessments
    for assessment_id in assessment_ids:
        client.delete(
            f"/api/v1/assessments/{assessment_id}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )


def test_bulk_delete_assessments(admin_token):
    """Test deleting multiple assessments in one request."""
    # First, create assessments to delete
    assessment_ids = []
    for i in range(3):
        assessment_data = {
            "name": generate_unique_name(),
            "description": f"Test assessment {i+1} for bulk delete",
            "target_system": f"System {i+1}",
            "assessment_type": "vulnerability",
            "status": "planned",
            "start_date": datetime.utcnow().isoformat(),
            "end_date": (datetime.utcnow() + timedelta(days=30)).isoformat()
        }
        
        response = client.post(
            "/api/v1/assessments/",
            json=assessment_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 201
        assessment_ids.append(response.json()["id"])
    
    # Now delete all assessments in one request
    bulk_delete_data = {
        "ids": assessment_ids
    }
    
    response = client.post(
        "/api/v1/assessments/bulk-delete",
        json=bulk_delete_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    
    # Verify the response contains information about the deleted assessments
    results = response.json()
    assert "deleted" in results
    assert results["deleted"] == 3
    
    # Check that each assessment was actually deleted (should return 404)
    for assessment_id in assessment_ids:
        response = client.get(
            f"/api/v1/assessments/{assessment_id}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 404


def test_bulk_partial_success(admin_token):
    """Test bulk operations with partial success."""
    # Create some valid assessments
    valid_assessments = [
        {
            "name": generate_unique_name(),
            "description": "Valid assessment 1",
            "target_system": "System A",
            "assessment_type": "vulnerability",
            "status": "planned",
            "start_date": datetime.utcnow().isoformat(),
            "end_date": (datetime.utcnow() + timedelta(days=30)).isoformat()
        },
        {
            "name": generate_unique_name(),
            "description": "Valid assessment 2",
            "target_system": "System B",
            "assessment_type": "penetration",
            "status": "planned",
            "start_date": datetime.utcnow().isoformat(),
            "end_date": (datetime.utcnow() + timedelta(days=30)).isoformat()
        }
    ]
    
    # Add an invalid assessment (missing required fields)
    invalid_assessment = {
        "description": "Invalid assessment with missing name",
        "status": "planned"
    }
    
    bulk_data = {
        "assessments": valid_assessments + [invalid_assessment]
    }
    
    response = client.post(
        "/api/v1/assessments/bulk",
        json=bulk_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    # Check for partial success (HTTP 207 Multi-Status)
    assert response.status_code == 207
    
    results = response.json()
    assert "created" in results
    assert results["created"] == 2  # Only the valid assessments
    assert "failed" in results
    assert results["failed"] == 1  # The invalid assessment
    assert "assessments" in results
    assert len(results["assessments"]) == 2
    assert "errors" in results
    assert len(results["errors"]) == 1
    
    # Clean up created assessments
    assessment_ids = [a["id"] for a in results["assessments"]]
    for assessment_id in assessment_ids:
        client.delete(
            f"/api/v1/assessments/{assessment_id}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )


def test_bulk_assign_campaigns(admin_token):
    """Test assigning multiple campaigns to assessments in bulk."""
    # First, create an assessment
    assessment_data = {
        "name": generate_unique_name(),
        "description": "Test assessment for bulk campaign assignment",
        "target_system": "Test System",
        "assessment_type": "vulnerability",
        "status": "planned",
        "start_date": datetime.utcnow().isoformat(),
        "end_date": (datetime.utcnow() + timedelta(days=30)).isoformat()
    }
    
    response = client.post(
        "/api/v1/assessments/",
        json=assessment_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 201
    assessment_id = response.json()["id"]
    
    # Get a list of campaigns to assign
    response = client.get(
        "/api/v1/campaigns/",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    
    campaigns = response.json()
    if len(campaigns) >= 2:
        campaign_ids = [campaigns[0]["id"], campaigns[1]["id"]]
        
        # Assign campaigns to the assessment in bulk
        bulk_assign_data = {
            "assessment_id": assessment_id,
            "campaign_ids": campaign_ids
        }
        
        response = client.post(
            "/api/v1/assessments/bulk-assign-campaigns",
            json=bulk_assign_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 200
        
        # Verify the campaigns were assigned
        response = client.get(
            f"/api/v1/assessments/{assessment_id}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 200
        
        assessment_campaigns = response.json()["campaigns"]
        assigned_campaign_ids = [c["id"] for c in assessment_campaigns]
        
        # All campaign IDs should be in the assigned campaigns
        for campaign_id in campaign_ids:
            assert campaign_id in assigned_campaign_ids
    
    # Clean up
    client.delete(
        f"/api/v1/assessments/{assessment_id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    ) 