"""
Utility functions for tests.
"""

import random
import string
from api.models.user import User, User<PERSON><PERSON>


def random_string(length=10):
    """Generate a random string of fixed length."""
    letters = string.ascii_lowercase
    return ''.join(random.choice(letters) for i in range(length))


def create_test_user(session, role=UserRole.VIEWER):
    """Create a test user with the given role."""
    username = f"testuser_{random_string(8)}"
    email = f"{username}@example.com"
    password = "TestPass123!"
    
    user = User(username=username, email=email, role=role)
    user.set_password(password)
    session.add(user)
    session.commit()
    
    return {
        'user': user,
        'username': username,
        'email': email,
        'password': password
    }


def login_user(client, username, password):
    """Log in a user and return the response."""
    return client.post('/login', data={
        'username': username,
        'password': password
    }, follow_redirects=True)


def get_api_token(client, username, password):
    """Get an API token for the given user."""
    response = client.post('/api/auth/token', json={
        'username': username,
        'password': password
    })
    token_data = response.get_json()
    return token_data.get('access_token')


def api_headers(token):
    """Create headers for API requests with authentication."""
    return {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
