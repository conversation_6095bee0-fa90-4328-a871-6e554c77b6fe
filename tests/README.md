# Regression Rigor Test Suite

This directory contains the test suite for the Regression Rigor platform. The tests are organized by module and type to ensure comprehensive coverage of the codebase.

## Test Structure

- `tests/conftest.py`: Contains common fixtures and test configuration
- `tests/utils/`: Contains test utilities, factories, and helpers
- `tests/test_api/`: Contains API tests
- `tests/test_models/`: Contains model tests
- `tests/test_services/`: Contains service tests

## Test Categories

Tests are categorized using pytest markers:

- `unit`: Unit tests for individual components in isolation
- `integration`: Integration tests for interactions between components
- `api`: API tests for testing HTTP endpoints
- `mitre`: MITRE ATT&CK integration tests
- `campaign`: Campaign tests for testing campaign functionality
- `testcase`: Test case tests for testing test case functionality
- `execution`: Execution framework tests for testing the execution framework
- `slow`: Slow tests that take longer to run

## Running Tests

### Running All Tests

```bash
pytest
```

### Running Tests by Category

```bash
# Run unit tests
pytest -m unit

# Run integration tests
pytest -m integration

# Run API tests
pytest -m api

# Run MITRE ATT&CK tests
pytest -m mitre

# Run campaign tests
pytest -m campaign

# Run test case tests
pytest -m testcase

# Run execution framework tests
pytest -m execution
```

### Running Tests by Module

```bash
# Run API tests
pytest tests/test_api/

# Run model tests
pytest tests/test_models/

# Run service tests
pytest tests/test_services/
```

### Running Tests with Coverage

```bash
pytest --cov=api --cov-report=html
```

## Test Utilities

### Factories

The `tests/utils/factories.py` module contains factories for creating test objects. These factories use the Factory pattern to create objects with sensible defaults.

Example:

```python
from tests.utils.factories import UserFactory, CampaignFactory

# Create a user
user = UserFactory.create(db_session, username="testuser")

# Create a campaign
campaign = CampaignFactory.create(db_session, name="Test Campaign")
```

### Assertions

The `tests/utils/assertions.py` module contains custom assertions for testing.

Example:

```python
from tests.utils.assertions import assert_pagination_response, assert_json_response

# Assert that a response has the correct pagination structure
assert_pagination_response(response.json())

# Assert that a JSON response contains the expected keys
assert_json_response(response.json(), ["id", "name", "description"])
```

### Helpers

The `tests/utils/helpers.py` module contains helper functions for testing.

Example:

```python
from tests.utils.helpers import auth_headers, get_auth_token

# Get authentication headers
headers = auth_headers(token)

# Get an authentication token
token = get_auth_token(client, "testuser", "password")
```

## Writing New Tests

When adding new features or fixing bugs, please add appropriate tests to maintain or improve test coverage. Follow these guidelines:

1. **Use fixtures**: Use fixtures for common setup and teardown
2. **Use factories**: Use factories to create test objects
3. **Use markers**: Use markers to categorize tests
4. **Test isolation**: Each test should be isolated and not depend on other tests
5. **Descriptive names**: Use descriptive names for tests
6. **Assertions**: Use assertions to verify test results
7. **Documentation**: Document tests with docstrings
8. **Coverage**: Aim for high test coverage

## Test Coverage

The goal is to maintain at least 80% test coverage for all modules. Critical components should have 100% coverage.

To view test coverage, run:

```bash
pytest --cov=api --cov-report=html
```

This will generate an HTML report in the `htmlcov` directory.
