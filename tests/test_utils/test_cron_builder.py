"""Tests for the cron builder utility.

This module contains tests for the cron builder utility.
"""
import pytest

from api.utils.cron_builder import (
    build_cron_expression,
    build_from_preset,
    get_human_readable_description,
)


def test_build_cron_expression():
    """Test building cron expressions from user-friendly inputs."""
    # Test with integers
    assert build_cron_expression(0, 0) == "0 0 * * *"
    assert build_cron_expression(15, 8) == "15 8 * * *"
    
    # Test with lists
    assert build_cron_expression([0, 15, 30, 45], [8, 12, 16]) == "0,15,30,45 8,12,16 * * *"
    
    # Test with strings
    assert build_cron_expression("*/15", "8-17") == "*/15 8-17 * * *"
    assert build_cron_expression("0", "0", "1", "*", "1-5") == "0 0 1 * 1-5"
    
    # Test with mixed types
    assert build_cron_expression(0, "*/6", 1, [1, 4, 7, 10], "1-5") == "0 */6 1 1,4,7,10 1-5"
    
    # Test with defaults
    assert build_cron_expression() == "* * * * *"
    assert build_cron_expression(0) == "0 * * * *"
    assert build_cron_expression(hours=0) == "* 0 * * *"


def test_build_cron_expression_validation():
    """Test validation in build_cron_expression."""
    # Test invalid integers
    with pytest.raises(ValueError):
        build_cron_expression(60)  # Minute out of range
    
    with pytest.raises(ValueError):
        build_cron_expression(hours=24)  # Hour out of range
    
    with pytest.raises(ValueError):
        build_cron_expression(days_of_month=32)  # Day out of range
    
    with pytest.raises(ValueError):
        build_cron_expression(months=13)  # Month out of range
    
    with pytest.raises(ValueError):
        build_cron_expression(days_of_week=7)  # Day of week out of range
    
    # Test invalid lists
    with pytest.raises(ValueError):
        build_cron_expression([0, 60])  # Minute out of range
    
    with pytest.raises(ValueError):
        build_cron_expression(hours=[0, 24])  # Hour out of range
    
    # Test invalid strings
    with pytest.raises(ValueError):
        build_cron_expression("60")  # Minute out of range
    
    with pytest.raises(ValueError):
        build_cron_expression("*/a")  # Invalid step
    
    with pytest.raises(ValueError):
        build_cron_expression("a-b")  # Invalid range
    
    with pytest.raises(ValueError):
        build_cron_expression("1,a")  # Invalid list


def test_build_from_preset():
    """Test building cron expressions from presets."""
    # Test basic presets
    assert build_from_preset("hourly") == "0 * * * *"
    assert build_from_preset("daily") == "0 0 * * *"
    assert build_from_preset("weekly") == "0 0 * * 0"
    assert build_from_preset("monthly") == "0 0 1 * *"
    assert build_from_preset("yearly") == "0 0 1 1 *"
    assert build_from_preset("weekdays") == "0 0 * * 1-5"
    assert build_from_preset("weekends") == "0 0 * * 0,6"
    assert build_from_preset("business_hours") == "0 9-17 * * 1-5"
    assert build_from_preset("every_minute") == "* * * * *"
    assert build_from_preset("every_5_minutes") == "*/5 * * * *"
    assert build_from_preset("every_15_minutes") == "*/15 * * * *"
    assert build_from_preset("every_30_minutes") == "*/30 * * * *"
    
    # Test presets with customizations
    assert build_from_preset("daily", hour=8) == "0 8 * * *"
    assert build_from_preset("weekly", day=1) == "0 0 * * 1"
    assert build_from_preset("monthly", day=15) == "0 0 15 * *"
    
    # Test invalid presets
    with pytest.raises(ValueError):
        build_from_preset("invalid_preset")
    
    # Test invalid customizations
    with pytest.raises(ValueError):
        build_from_preset("daily", hour=24)
    
    with pytest.raises(ValueError):
        build_from_preset("weekly", day=7)
    
    with pytest.raises(ValueError):
        build_from_preset("monthly", day=32)


def test_get_human_readable_description():
    """Test getting human-readable descriptions of cron expressions."""
    # Test common patterns
    assert get_human_readable_description("* * * * *") == "Every minute"
    assert get_human_readable_description("*/5 * * * *") == "Every 5 minutes"
    assert get_human_readable_description("*/15 * * * *") == "Every 15 minutes"
    assert get_human_readable_description("*/30 * * * *") == "Every 30 minutes"
    assert get_human_readable_description("0 * * * *") == "Every hour at the start of the hour"
    assert get_human_readable_description("0 0 * * *") == "Every day at midnight"
    
    # Test time expressions
    assert get_human_readable_description("0 8 * * *") == "Every day at 8 AM"
    assert get_human_readable_description("30 12 * * *") == "Every day at 12:30 PM"
    assert get_human_readable_description("0 0 * * *") == "Every day at midnight"
    assert get_human_readable_description("0 12 * * *") == "Every day at noon"
    
    # Test day of week expressions
    assert get_human_readable_description("0 0 * * 1") == "At midnight on Monday"
    assert get_human_readable_description("0 8 * * 1-5") == "At 8 AM on Monday through Friday"
    assert get_human_readable_description("0 0 * * 0,6") == "At midnight on Sunday and Saturday"
    
    # Test day of month expressions
    assert get_human_readable_description("0 0 1 * *") == "At midnight on the 1st of the month"
    assert get_human_readable_description("0 0 15 * *") == "At midnight on the 15th of the month"
    assert get_human_readable_description("0 0 1,15 * *") == "At midnight on the 1st and 15th of the month"
    
    # Test month expressions
    assert get_human_readable_description("0 0 1 1 *") == "At midnight on the 1st of January"
    assert get_human_readable_description("0 0 1 1,7 *") == "At midnight on the 1st of January and July"
    
    # Test complex expressions
    assert get_human_readable_description("0 9 * * 1-5") == "At 9 AM on Monday through Friday"
    assert get_human_readable_description("0 0 1 */3 *") == "At midnight on the 1st of every 3 months"
    
    # Test invalid expressions
    assert get_human_readable_description("* * *") == "Invalid cron expression"
