"""Tests for the cron expression parser and scheduler.

This module contains tests for the cron expression parser and scheduler.
"""
import pytest
from datetime import datetime, timedelta

from api.utils.cron import CronExpression, is_valid_cron, get_next_run


def test_cron_expression_parsing():
    """Test parsing cron expressions."""
    # Test simple expressions
    cron = CronExpression("0 0 * * *")
    assert cron.minute == [0]
    assert cron.hour == [0]
    assert cron.day == list(range(1, 32))
    assert cron.month == list(range(1, 13))
    assert cron.day_of_week == list(range(0, 7))
    
    # Test ranges
    cron = CronExpression("0-15 8-17 * * *")
    assert cron.minute == list(range(0, 16))
    assert cron.hour == list(range(8, 18))
    
    # Test lists
    cron = CronExpression("0,15,30,45 0,12 * * *")
    assert cron.minute == [0, 15, 30, 45]
    assert cron.hour == [0, 12]
    
    # Test steps
    cron = CronExpression("*/15 */6 * * *")
    assert cron.minute == [0, 15, 30, 45]
    assert cron.hour == [0, 6, 12, 18]
    
    # Test complex expression
    cron = CronExpression("0,15,30,45 8-17/3 * * 1-5")
    assert cron.minute == [0, 15, 30, 45]
    assert cron.hour == [8, 11, 14, 17]
    assert cron.day_of_week == [1, 2, 3, 4, 5]


def test_invalid_cron_expressions():
    """Test invalid cron expressions."""
    # Test invalid number of parts
    with pytest.raises(ValueError):
        CronExpression("0 0 * *")
    
    # Test invalid values
    with pytest.raises(ValueError):
        CronExpression("60 0 * * *")  # Minute out of range
    
    with pytest.raises(ValueError):
        CronExpression("0 24 * * *")  # Hour out of range
    
    with pytest.raises(ValueError):
        CronExpression("0 0 0 * *")  # Day out of range
    
    with pytest.raises(ValueError):
        CronExpression("0 0 * 13 *")  # Month out of range
    
    with pytest.raises(ValueError):
        CronExpression("0 0 * * 7")  # Day of week out of range


def test_next_run_calculation():
    """Test calculating the next run time."""
    now = datetime.now().replace(second=0, microsecond=0)
    
    # Test daily at midnight
    cron = CronExpression("0 0 * * *")
    next_run = cron.get_next_run(now)
    expected = now.replace(hour=0, minute=0) + timedelta(days=1)
    assert next_run == expected
    
    # Test every 15 minutes
    cron = CronExpression("*/15 * * * *")
    next_run = cron.get_next_run(now)
    minute = ((now.minute // 15) + 1) * 15
    if minute == 60:
        expected = now.replace(minute=0) + timedelta(hours=1)
    else:
        expected = now.replace(minute=minute)
    assert next_run == expected
    
    # Test weekdays at 9am
    cron = CronExpression("0 9 * * 1-5")
    next_run = cron.get_next_run(now)
    
    # Calculate expected next weekday at 9am
    expected = now.replace(hour=9, minute=0)
    if now.hour >= 9:
        expected += timedelta(days=1)
    
    # Adjust if it's weekend
    while expected.weekday() > 4:  # 5 and 6 are weekend days
        expected += timedelta(days=1)
    
    assert next_run == expected
    
    # Test first day of month at noon
    cron = CronExpression("0 12 1 * *")
    next_run = cron.get_next_run(now)
    
    # Calculate expected first day of next month at noon
    if now.day == 1 and now.hour < 12:
        expected = now.replace(day=1, hour=12, minute=0)
    else:
        if now.month == 12:
            expected = datetime(now.year + 1, 1, 1, 12, 0)
        else:
            expected = datetime(now.year, now.month + 1, 1, 12, 0)
    
    assert next_run == expected


def test_is_valid_cron():
    """Test the is_valid_cron function."""
    assert is_valid_cron("0 0 * * *") is True
    assert is_valid_cron("*/15 * * * *") is True
    assert is_valid_cron("0 9 * * 1-5") is True
    
    assert is_valid_cron("0 0 * *") is False
    assert is_valid_cron("60 0 * * *") is False
    assert is_valid_cron("0 24 * * *") is False


def test_get_next_run():
    """Test the get_next_run function."""
    now = datetime.now().replace(second=0, microsecond=0)
    
    # Test daily at midnight
    next_run = get_next_run("0 0 * * *", now)
    expected = now.replace(hour=0, minute=0) + timedelta(days=1)
    assert next_run == expected
