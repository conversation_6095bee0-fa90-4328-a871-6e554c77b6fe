"""
Extended tests for the Assessment Management API.

This module contains additional tests for the assessment and test execution endpoints,
focusing on edge cases, validation, and error handling.
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from api.main import app
from api.models.assessment import Assessment, TestExecution
from api.models.schemas.assessment import AssessmentCreate, TestExecutionCreate
from api.services.assessment import create_assessment, create_test_execution, get_assessment_by_id
from api.auth.utils import create_access_token

client = TestClient(app)


@pytest.fixture
def db_session(monkeypatch):
    """Create a test database session."""
    # This would typically use a test database
    from api.database import get_db
    db = next(get_db())
    yield db


@pytest.fixture
def admin_token():
    """Create an admin token for testing."""
    access_token = create_access_token(
        data={"sub": "<EMAIL>", "role": "admin"},
        expires_delta=timedelta(minutes=30)
    )
    return access_token


@pytest.fixture
def operator_token():
    """Create an operator token for testing."""
    access_token = create_access_token(
        data={"sub": "<EMAIL>", "role": "operator"},
        expires_delta=timedelta(minutes=30)
    )
    return access_token


@pytest.fixture
def viewer_token():
    """Create a viewer token for testing."""
    access_token = create_access_token(
        data={"sub": "<EMAIL>", "role": "viewer"},
        expires_delta=timedelta(minutes=30)
    )
    return access_token


@pytest.fixture
def test_assessment(db_session):
    """Create a test assessment."""
    assessment_data = AssessmentCreate(
        name="Test Assessment",
        description="Test assessment for API testing",
        target_system="Test System",
        assessment_type="vulnerability",
        status="planned",
        start_date=datetime.utcnow(),
        end_date=datetime.utcnow() + timedelta(days=7),
        campaign_id=1
    )
    
    assessment = create_assessment(db_session, assessment_data, user_id=1)
    return assessment


def test_get_assessments_with_filtering(admin_token):
    """Test getting a filtered list of assessments."""
    response = client.get(
        "/api/v1/assessments/?status=planned",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    # Verify all returned assessments have status "planned"
    for assessment in response.json():
        assert assessment["status"] == "planned"
    
    # Test filtering by type
    response = client.get(
        "/api/v1/assessments/?assessment_type=vulnerability",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    # Verify all returned assessments have type "vulnerability"
    for assessment in response.json():
        assert assessment["assessment_type"] == "vulnerability"


def test_get_assessments_with_pagination(admin_token):
    """Test assessment list pagination."""
    # Get first page with 2 items per page
    response = client.get(
        "/api/v1/assessments/?skip=0&limit=2",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    first_page = response.json()
    assert len(first_page) <= 2  # Could be less if there aren't enough assessments
    
    if len(first_page) == 2:  # Only test second page if first page was full
        # Get second page
        response = client.get(
            "/api/v1/assessments/?skip=2&limit=2",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 200
        second_page = response.json()
        
        # Verify the pages contain different assessments
        if len(second_page) > 0:
            assert first_page[0]["id"] != second_page[0]["id"]


def test_get_assessments_with_sorting(admin_token):
    """Test assessment list sorting."""
    # Get assessments sorted by name ascending
    response = client.get(
        "/api/v1/assessments/?sort=name",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    asc_sorted = response.json()
    
    if len(asc_sorted) > 1:
        # Verify sorting is correct
        for i in range(len(asc_sorted) - 1):
            assert asc_sorted[i]["name"] <= asc_sorted[i + 1]["name"]
    
    # Get assessments sorted by name descending
    response = client.get(
        "/api/v1/assessments/?sort=-name",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    desc_sorted = response.json()
    
    if len(desc_sorted) > 1:
        # Verify sorting is correct
        for i in range(len(desc_sorted) - 1):
            assert desc_sorted[i]["name"] >= desc_sorted[i + 1]["name"]


def test_create_assessment_validation(admin_token):
    """Test assessment creation with validation errors."""
    # Missing required fields
    assessment_data = {
        "description": "Test assessment with missing fields",
        "status": "planned"
    }
    
    response = client.post(
        "/api/v1/assessments/",
        json=assessment_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 422
    
    # Invalid status
    assessment_data = {
        "name": "Invalid Status Assessment",
        "description": "Test assessment with invalid status",
        "target_system": "Test System",
        "assessment_type": "vulnerability",
        "status": "invalid_status",
        "start_date": datetime.utcnow().isoformat(),
        "end_date": (datetime.utcnow() + timedelta(days=7)).isoformat(),
        "campaign_id": 1
    }
    
    response = client.post(
        "/api/v1/assessments/",
        json=assessment_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 422
    
    # End date before start date
    assessment_data = {
        "name": "Invalid Dates Assessment",
        "description": "Test assessment with invalid dates",
        "target_system": "Test System",
        "assessment_type": "vulnerability",
        "status": "planned",
        "start_date": (datetime.utcnow() + timedelta(days=7)).isoformat(),
        "end_date": datetime.utcnow().isoformat(),
        "campaign_id": 1
    }
    
    response = client.post(
        "/api/v1/assessments/",
        json=assessment_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 422


def test_update_assessment_validation(admin_token, test_assessment):
    """Test assessment update with validation errors."""
    # Invalid status
    update_data = {
        "status": "invalid_status"
    }
    
    response = client.put(
        f"/api/v1/assessments/{test_assessment.id}",
        json=update_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 422
    
    # End date before start date
    update_data = {
        "start_date": (datetime.utcnow() + timedelta(days=7)).isoformat(),
        "end_date": datetime.utcnow().isoformat()
    }
    
    response = client.put(
        f"/api/v1/assessments/{test_assessment.id}",
        json=update_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 422


def test_get_nonexistent_assessment(admin_token):
    """Test getting a non-existent assessment."""
    response = client.get(
        "/api/v1/assessments/9999",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 404


def test_update_nonexistent_assessment(admin_token):
    """Test updating a non-existent assessment."""
    update_data = {
        "name": "Updated Non-existent Assessment"
    }
    
    response = client.put(
        "/api/v1/assessments/9999",
        json=update_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 404


def test_delete_nonexistent_assessment(admin_token):
    """Test deleting a non-existent assessment."""
    response = client.delete(
        "/api/v1/assessments/9999",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 404


def test_role_based_access_control(admin_token, operator_token, viewer_token, test_assessment):
    """Test role-based access control for assessment endpoints."""
    # Viewer should be able to read
    response = client.get(
        f"/api/v1/assessments/{test_assessment.id}",
        headers={"Authorization": f"Bearer {viewer_token}"}
    )
    assert response.status_code == 200
    
    # Viewer should not be able to create
    assessment_data = {
        "name": "Viewer Created Assessment",
        "description": "This should fail",
        "target_system": "Test System",
        "assessment_type": "vulnerability",
        "status": "planned",
        "start_date": datetime.utcnow().isoformat(),
        "end_date": (datetime.utcnow() + timedelta(days=7)).isoformat(),
        "campaign_id": 1
    }
    
    response = client.post(
        "/api/v1/assessments/",
        json=assessment_data,
        headers={"Authorization": f"Bearer {viewer_token}"}
    )
    assert response.status_code == 403
    
    # Viewer should not be able to update
    update_data = {
        "name": "Viewer Updated Assessment"
    }
    
    response = client.put(
        f"/api/v1/assessments/{test_assessment.id}",
        json=update_data,
        headers={"Authorization": f"Bearer {viewer_token}"}
    )
    assert response.status_code == 403
    
    # Viewer should not be able to delete
    response = client.delete(
        f"/api/v1/assessments/{test_assessment.id}",
        headers={"Authorization": f"Bearer {viewer_token}"}
    )
    assert response.status_code == 403
    
    # Operator should be able to create, read, update, delete
    response = client.get(
        f"/api/v1/assessments/{test_assessment.id}",
        headers={"Authorization": f"Bearer {operator_token}"}
    )
    assert response.status_code == 200
    
    operator_assessment_data = {
        "name": "Operator Created Assessment",
        "description": "This should succeed",
        "target_system": "Test System",
        "assessment_type": "vulnerability",
        "status": "planned",
        "start_date": datetime.utcnow().isoformat(),
        "end_date": (datetime.utcnow() + timedelta(days=7)).isoformat(),
        "campaign_id": 1
    }
    
    response = client.post(
        "/api/v1/assessments/",
        json=operator_assessment_data,
        headers={"Authorization": f"Bearer {operator_token}"}
    )
    assert response.status_code == 201
    operator_assessment_id = response.json()["id"]
    
    operator_update_data = {
        "name": "Operator Updated Assessment"
    }
    
    response = client.put(
        f"/api/v1/assessments/{test_assessment.id}",
        json=operator_update_data,
        headers={"Authorization": f"Bearer {operator_token}"}
    )
    assert response.status_code == 200
    
    # Clean up the operator created assessment
    response = client.delete(
        f"/api/v1/assessments/{operator_assessment_id}",
        headers={"Authorization": f"Bearer {operator_token}"}
    )
    assert response.status_code == 204


def test_assessment_report_elements(admin_token, test_assessment, test_execution):
    """Test that the assessment report contains all required elements."""
    response = client.get(
        f"/api/v1/assessments/{test_assessment.id}/report",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    report = response.json()
    
    # Check all required sections are present
    assert "assessment" in report
    assert "summary" in report
    assert "test_executions" in report
    assert "campaigns" in report
    assert "mitre_coverage" in report
    assert "recommendations" in report
    
    # Check assessment details
    assert report["assessment"]["id"] == test_assessment.id
    assert report["assessment"]["name"] == test_assessment.name
    
    # Check summary contains statistics
    assert "total_tests" in report["summary"]
    assert "passed_tests" in report["summary"]
    assert "failed_tests" in report["summary"]
    assert "partial_tests" in report["summary"]
    assert "pass_rate" in report["summary"]
    
    # Check test executions contain required fields
    if len(report["test_executions"]) > 0:
        test_execution = report["test_executions"][0]
        assert "id" in test_execution
        assert "test_case_id" in test_execution
        assert "result" in test_execution
        assert "notes" in test_execution
        assert "evidence" in test_execution
        assert "executed_at" in test_execution
    
    # Check MITRE coverage contains required fields
    mitre_coverage = report["mitre_coverage"]
    assert "tactics" in mitre_coverage
    assert "techniques" in mitre_coverage
    assert "coverage_percentage" in mitre_coverage


def test_assessment_results_filtering(admin_token, test_assessment, test_execution):
    """Test filtering assessment results."""
    # Filter by result
    response = client.get(
        f"/api/v1/assessments/{test_assessment.id}/results?result=pass",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    # Verify all returned executions have result "pass"
    for execution in response.json():
        assert execution["result"] == "pass"
    
    # Filter by date range
    yesterday = (datetime.utcnow() - timedelta(days=1)).isoformat()
    tomorrow = (datetime.utcnow() + timedelta(days=1)).isoformat()
    
    response = client.get(
        f"/api/v1/assessments/{test_assessment.id}/results?from_date={yesterday}&to_date={tomorrow}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    # All executions should be within date range, but this is hard to verify in a unit test
    
    # Test combined filters
    response = client.get(
        f"/api/v1/assessments/{test_assessment.id}/results?result=pass&from_date={yesterday}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    # Results should be filtered by both criteria


def test_create_test_execution_validation(admin_token, test_assessment):
    """Test test execution creation with validation errors."""
    # Missing required fields
    execution_data = {
        "notes": "Test execution with missing fields"
    }
    
    response = client.post(
        "/api/v1/assessments/executions",
        json=execution_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 422
    
    # Invalid result
    execution_data = {
        "test_case_id": 1,
        "assessment_id": test_assessment.id,
        "result": "invalid_result",
        "notes": "Test execution with invalid result"
    }
    
    response = client.post(
        "/api/v1/assessments/executions",
        json=execution_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 422


def test_update_test_execution_validation(admin_token, test_execution):
    """Test test execution update with validation errors."""
    # Invalid result
    update_data = {
        "result": "invalid_result"
    }
    
    response = client.put(
        f"/api/v1/assessments/executions/{test_execution.id}",
        json=update_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 422 