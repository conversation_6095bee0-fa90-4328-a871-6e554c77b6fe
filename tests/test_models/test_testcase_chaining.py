"""
Unit tests for the testcase chaining models.

This module contains tests for the database models related to the Enhanced
Testcase Chaining & Sequencing feature.
"""
import pytest
from datetime import datetime
from sqlalchemy.orm import Session

from api.models.testcase_chaining import (
    TestcaseChainDB, TestcaseChainNodeDB, TestcaseChainEdgeDB,
    ChainExecutionDB, NodeExecutionDB, TestcaseConditionDB
)
from api.models.base import TestCaseDB, CampaignDB


@pytest.fixture
def test_campaign(db_session: Session):
    """Create a test campaign for use in tests."""
    campaign = CampaignDB(name="Test Campaign", description="Test Campaign Description")
    db_session.add(campaign)
    db_session.commit()
    db_session.refresh(campaign)
    return campaign


@pytest.fixture
def test_case(db_session: Session, test_campaign):
    """Create a test case for use in tests."""
    test_case = TestCaseDB(
        name="Test Case",
        description="Test Case Description",
        campaign_id=test_campaign.id,
        expected_result="Expected Result",
        status="pending"
    )
    db_session.add(test_case)
    db_session.commit()
    db_session.refresh(test_case)
    return test_case


@pytest.fixture
def test_user_id():
    """Return a test user ID for use in tests."""
    return 1  # Assuming user with ID 1 exists


@pytest.fixture
def test_chain(db_session: Session, test_user_id):
    """Create a test chain for use in tests."""
    chain = TestcaseChainDB(
        name="Test Chain",
        description="Test Chain Description",
        created_by=test_user_id,
        status="draft"
    )
    db_session.add(chain)
    db_session.commit()
    db_session.refresh(chain)
    return chain


@pytest.fixture
def test_node(db_session: Session, test_chain, test_case):
    """Create a test node for use in tests."""
    node = TestcaseChainNodeDB(
        chain_id=test_chain.id,
        testcase_id=test_case.id,
        node_type="standard",
        position_x=100.0,
        position_y=100.0,
        execution_order=1
    )
    db_session.add(node)
    db_session.commit()
    db_session.refresh(node)
    return node


def test_create_chain(db_session: Session, test_user_id):
    """Test creating a testcase chain."""
    chain = TestcaseChainDB(
        name="Test Chain",
        description="Test Chain Description",
        created_by=test_user_id,
        status="draft"
    )
    db_session.add(chain)
    db_session.commit()
    db_session.refresh(chain)
    
    assert chain.id is not None
    assert chain.name == "Test Chain"
    assert chain.description == "Test Chain Description"
    assert chain.created_by == test_user_id
    assert chain.status == "draft"
    assert chain.created_time is not None
    assert chain.updated_time is not None
    assert chain.deleted_time is None


def test_create_node(db_session: Session, test_chain, test_case):
    """Test creating a testcase chain node."""
    node = TestcaseChainNodeDB(
        chain_id=test_chain.id,
        testcase_id=test_case.id,
        node_type="standard",
        position_x=100.0,
        position_y=100.0,
        execution_order=1
    )
    db_session.add(node)
    db_session.commit()
    db_session.refresh(node)
    
    assert node.id is not None
    assert node.chain_id == test_chain.id
    assert node.testcase_id == test_case.id
    assert node.node_type == "standard"
    assert node.position_x == 100.0
    assert node.position_y == 100.0
    assert node.execution_order == 1
    assert node.created_time is not None
    assert node.updated_time is not None
    assert node.deleted_time is None


def test_create_edge(db_session: Session, test_node):
    """Test creating a testcase chain edge."""
    # Create a second node to connect to
    node2 = TestcaseChainNodeDB(
        chain_id=test_node.chain_id,
        testcase_id=test_node.testcase_id,
        node_type="standard",
        position_x=200.0,
        position_y=200.0,
        execution_order=2
    )
    db_session.add(node2)
    db_session.commit()
    db_session.refresh(node2)
    
    # Create an edge between the nodes
    edge = TestcaseChainEdgeDB(
        source_node_id=test_node.id,
        target_node_id=node2.id,
        edge_type="standard"
    )
    db_session.add(edge)
    db_session.commit()
    db_session.refresh(edge)
    
    assert edge.id is not None
    assert edge.source_node_id == test_node.id
    assert edge.target_node_id == node2.id
    assert edge.edge_type == "standard"
    assert edge.created_time is not None
    assert edge.updated_time is not None
    assert edge.deleted_time is None


def test_create_condition(db_session: Session, test_case):
    """Test creating a testcase condition."""
    condition = TestcaseConditionDB(
        testcase_id=test_case.id,
        condition_type="precondition",
        name="Test Condition",
        description="Test Condition Description",
        validation_script="return True",
        required=True
    )
    db_session.add(condition)
    db_session.commit()
    db_session.refresh(condition)
    
    assert condition.id is not None
    assert condition.testcase_id == test_case.id
    assert condition.condition_type == "precondition"
    assert condition.name == "Test Condition"
    assert condition.description == "Test Condition Description"
    assert condition.validation_script == "return True"
    assert condition.required is True
    assert condition.created_time is not None
    assert condition.updated_time is not None
    assert condition.deleted_time is None


def test_create_chain_execution(db_session: Session, test_chain, test_user_id):
    """Test creating a chain execution."""
    execution = ChainExecutionDB(
        chain_id=test_chain.id,
        started_by=test_user_id,
        start_time=datetime.now(),
        status="running"
    )
    db_session.add(execution)
    db_session.commit()
    db_session.refresh(execution)
    
    assert execution.id is not None
    assert execution.chain_id == test_chain.id
    assert execution.started_by == test_user_id
    assert execution.start_time is not None
    assert execution.end_time is None
    assert execution.status == "running"
    assert execution.created_time is not None
    assert execution.updated_time is not None
    assert execution.deleted_time is None


def test_create_node_execution(db_session: Session, test_node):
    """Test creating a node execution."""
    # Create a chain execution first
    execution = ChainExecutionDB(
        chain_id=test_node.chain_id,
        started_by=1,  # Assuming user with ID 1 exists
        start_time=datetime.now(),
        status="running"
    )
    db_session.add(execution)
    db_session.commit()
    db_session.refresh(execution)
    
    # Create a node execution
    node_execution = NodeExecutionDB(
        chain_execution_id=execution.id,
        node_id=test_node.id,
        start_time=datetime.now(),
        status="running",
        result_data={"test": "data"}
    )
    db_session.add(node_execution)
    db_session.commit()
    db_session.refresh(node_execution)
    
    assert node_execution.id is not None
    assert node_execution.chain_execution_id == execution.id
    assert node_execution.node_id == test_node.id
    assert node_execution.start_time is not None
    assert node_execution.end_time is None
    assert node_execution.status == "running"
    assert node_execution.result_data == {"test": "data"}
    assert node_execution.created_time is not None
    assert node_execution.updated_time is not None
    assert node_execution.deleted_time is None


def test_relationships(db_session: Session, test_chain, test_node):
    """Test the relationships between models."""
    # Test chain to nodes relationship
    assert len(test_chain.chain_nodes) == 1
    assert test_chain.chain_nodes[0].id == test_node.id
    
    # Test node to chain relationship
    assert test_node.chain.id == test_chain.id
    
    # Create a condition for the testcase
    test_case = db_session.query(TestCaseDB).filter(TestCaseDB.id == test_node.testcase_id).first()
    condition = TestcaseConditionDB(
        testcase_id=test_case.id,
        condition_type="precondition",
        name="Test Condition",
        description="Test Condition Description",
        validation_script="return True",
        required=True
    )
    db_session.add(condition)
    db_session.commit()
    db_session.refresh(condition)
    db_session.refresh(test_case)
    
    # Test testcase to conditions relationship
    assert len(test_case.conditions) == 1
    assert test_case.conditions[0].id == condition.id
    
    # Test condition to testcase relationship
    assert condition.testcase.id == test_case.id 