"""Unit tests for dashboard models."""
import pytest
from datetime import datetime
from sqlalchemy.exc import IntegrityError

from api.models.database.dashboard import Dashboard
from api.models.database.dashboard_widget import DashboardWidget
from api.models.database.dashboard_filter import DashboardFilter
from api.models.database.dashboard_sharing import DashboardShare, SharePermission
from api.models.user import User, UserRole


class TestDashboardModel:
    """Test the Dashboard model."""

    def test_create_dashboard(self, db_session):
        """Test creating a dashboard."""
        # Create a test user
        user = User(
            username="dashboarduser",
            email="<EMAIL>",
            role=UserRole.ANALYST
        )
        user.set_password("password123")
        db_session.add(user)
        db_session.commit()

        # Create a dashboard
        dashboard = Dashboard(
            name="Test Dashboard",
            description="Test dashboard description",
            layout={"type": "grid", "columns": 12},
            is_public=False,
            created_by_id=user.id
        )
        db_session.add(dashboard)
        db_session.commit()
        db_session.refresh(dashboard)

        # Verify dashboard was created
        assert dashboard.id is not None
        assert dashboard.name == "Test Dashboard"
        assert dashboard.description == "Test dashboard description"
        assert dashboard.layout == {"type": "grid", "columns": 12}
        assert dashboard.is_public is False
        assert dashboard.created_by_id == user.id
        assert dashboard.created_at is not None
        assert dashboard.created_by.username == "dashboarduser"

    def test_dashboard_to_dict(self, db_session):
        """Test the to_dict method of Dashboard."""
        # Create a test user
        user = User(
            username="dictuser",
            email="<EMAIL>",
            role=UserRole.ANALYST
        )
        user.set_password("password123")
        db_session.add(user)
        db_session.commit()

        # Create a dashboard
        dashboard = Dashboard(
            name="Dict Dashboard",
            description="Dict dashboard description",
            layout={"type": "grid", "columns": 12},
            is_public=True,
            created_by_id=user.id
        )
        db_session.add(dashboard)
        db_session.commit()

        # Test to_dict method
        dashboard_dict = dashboard.to_dict()
        assert dashboard_dict["id"] == dashboard.id
        assert dashboard_dict["name"] == "Dict Dashboard"
        assert dashboard_dict["description"] == "Dict dashboard description"
        assert dashboard_dict["layout"] == {"type": "grid", "columns": 12}
        assert dashboard_dict["is_public"] is True
        assert dashboard_dict["created_by_id"] == user.id
        assert "created_at" in dashboard_dict
        assert "widgets" in dashboard_dict
        assert "filters" in dashboard_dict

    def test_dashboard_unique_constraints(self, db_session):
        """Test that dashboard names don't need to be unique."""
        # Create a test user
        user = User(
            username="uniqueuser",
            email="<EMAIL>",
            role=UserRole.ANALYST
        )
        user.set_password("password123")
        db_session.add(user)
        db_session.commit()

        # Create a dashboard
        dashboard1 = Dashboard(
            name="Same Name Dashboard",
            description="First dashboard",
            layout={},
            is_public=False,
            created_by_id=user.id
        )
        db_session.add(dashboard1)
        db_session.commit()

        # Create another dashboard with the same name
        dashboard2 = Dashboard(
            name="Same Name Dashboard",
            description="Second dashboard",
            layout={},
            is_public=False,
            created_by_id=user.id
        )
        db_session.add(dashboard2)
        db_session.commit()

        # Verify both dashboards were created
        assert dashboard1.id != dashboard2.id
        assert dashboard1.name == dashboard2.name
        assert dashboard1.description != dashboard2.description


class TestDashboardWidgetModel:
    """Test the DashboardWidget model."""

    def test_create_widget(self, db_session):
        """Test creating a dashboard widget."""
        # Create a test user
        user = User(
            username="widgetuser",
            email="<EMAIL>",
            role=UserRole.ANALYST
        )
        user.set_password("password123")
        db_session.add(user)
        db_session.commit()

        # Create a dashboard
        dashboard = Dashboard(
            name="Widget Dashboard",
            description="Dashboard for widget test",
            layout={},
            is_public=False,
            created_by_id=user.id
        )
        db_session.add(dashboard)
        db_session.commit()

        # Create a widget
        widget = DashboardWidget(
            dashboard_id=dashboard.id,
            widget_type="chart",
            title="Test Widget",
            config={"chart_type": "bar"},
            position={"x": 0, "y": 0, "w": 6, "h": 4}
        )
        db_session.add(widget)
        db_session.commit()
        db_session.refresh(widget)

        # Verify widget was created
        assert widget.id is not None
        assert widget.dashboard_id == dashboard.id
        assert widget.widget_type == "chart"
        assert widget.title == "Test Widget"
        assert widget.config == {"chart_type": "bar"}
        assert widget.position == {"x": 0, "y": 0, "w": 6, "h": 4}
        assert widget.created_at is not None
        assert widget.dashboard.name == "Widget Dashboard"

    def test_widget_to_dict(self, db_session):
        """Test the to_dict method of DashboardWidget."""
        # Create a test user
        user = User(
            username="widgetdictuser",
            email="<EMAIL>",
            role=UserRole.ANALYST
        )
        user.set_password("password123")
        db_session.add(user)
        db_session.commit()

        # Create a dashboard
        dashboard = Dashboard(
            name="Widget Dict Dashboard",
            description="Dashboard for widget dict test",
            layout={},
            is_public=False,
            created_by_id=user.id
        )
        db_session.add(dashboard)
        db_session.commit()

        # Create a widget
        widget = DashboardWidget(
            dashboard_id=dashboard.id,
            widget_type="metric",
            title="Metric Widget",
            config={"metric_name": "total_tests"},
            position={"x": 0, "y": 0, "w": 3, "h": 2}
        )
        db_session.add(widget)
        db_session.commit()

        # Test to_dict method
        widget_dict = widget.to_dict()
        assert widget_dict["id"] == widget.id
        assert widget_dict["dashboard_id"] == dashboard.id
        assert widget_dict["widget_type"] == "metric"
        assert widget_dict["title"] == "Metric Widget"
        assert widget_dict["config"] == {"metric_name": "total_tests"}
        assert widget_dict["position"] == {"x": 0, "y": 0, "w": 3, "h": 2}
        assert "created_at" in widget_dict

    def test_widget_cascade_delete(self, db_session):
        """Test that widgets are deleted when dashboard is deleted."""
        # Create a test user
        user = User(
            username="cascadeuser",
            email="<EMAIL>",
            role=UserRole.ANALYST
        )
        user.set_password("password123")
        db_session.add(user)
        db_session.commit()

        # Create a dashboard
        dashboard = Dashboard(
            name="Cascade Dashboard",
            description="Dashboard for cascade test",
            layout={},
            is_public=False,
            created_by_id=user.id
        )
        db_session.add(dashboard)
        db_session.commit()

        # Create a widget
        widget = DashboardWidget(
            dashboard_id=dashboard.id,
            widget_type="chart",
            title="Cascade Widget",
            config={},
            position={}
        )
        db_session.add(widget)
        db_session.commit()

        # Get the widget ID
        widget_id = widget.id

        # Delete the dashboard
        db_session.delete(dashboard)
        db_session.commit()

        # Verify widget was deleted
        assert db_session.query(DashboardWidget).filter_by(id=widget_id).first() is None


class TestDashboardFilterModel:
    """Test the DashboardFilter model."""

    def test_create_filter(self, db_session):
        """Test creating a dashboard filter."""
        # Create a test user
        user = User(
            username="filteruser",
            email="<EMAIL>",
            role=UserRole.ANALYST
        )
        user.set_password("password123")
        db_session.add(user)
        db_session.commit()

        # Create a dashboard
        dashboard = Dashboard(
            name="Filter Dashboard",
            description="Dashboard for filter test",
            layout={},
            is_public=False,
            created_by_id=user.id
        )
        db_session.add(dashboard)
        db_session.commit()

        # Create a filter
        filter_obj = DashboardFilter(
            dashboard_id=dashboard.id,
            name="Test Filter",
            field="status",
            operator="equals",
            value="passed",
            is_global=True,
            applies_to_widgets=None
        )
        db_session.add(filter_obj)
        db_session.commit()
        db_session.refresh(filter_obj)

        # Verify filter was created
        assert filter_obj.id is not None
        assert filter_obj.dashboard_id == dashboard.id
        assert filter_obj.name == "Test Filter"
        assert filter_obj.field == "status"
        assert filter_obj.operator == "equals"
        assert filter_obj.value == "passed"
        assert filter_obj.is_global is True
        assert filter_obj.applies_to_widgets is None
        assert filter_obj.created_at is not None
        assert filter_obj.dashboard.name == "Filter Dashboard"

    def test_filter_to_dict(self, db_session):
        """Test the to_dict method of DashboardFilter."""
        # Create a test user
        user = User(
            username="filterdictuser",
            email="<EMAIL>",
            role=UserRole.ANALYST
        )
        user.set_password("password123")
        db_session.add(user)
        db_session.commit()

        # Create a dashboard
        dashboard = Dashboard(
            name="Filter Dict Dashboard",
            description="Dashboard for filter dict test",
            layout={},
            is_public=False,
            created_by_id=user.id
        )
        db_session.add(dashboard)
        db_session.commit()

        # Create a filter
        filter_obj = DashboardFilter(
            dashboard_id=dashboard.id,
            name="Dict Filter",
            field="priority",
            operator="in",
            value=["high", "critical"],
            is_global=False,
            applies_to_widgets=[1, 2, 3]
        )
        db_session.add(filter_obj)
        db_session.commit()

        # Test to_dict method
        filter_dict = filter_obj.to_dict()
        assert filter_dict["id"] == filter_obj.id
        assert filter_dict["dashboard_id"] == dashboard.id
        assert filter_dict["name"] == "Dict Filter"
        assert filter_dict["field"] == "priority"
        assert filter_dict["operator"] == "in"
        assert filter_dict["value"] == ["high", "critical"]
        assert filter_dict["is_global"] is False
        assert filter_dict["applies_to_widgets"] == [1, 2, 3]
        assert "created_at" in filter_dict

    def test_filter_cascade_delete(self, db_session):
        """Test that filters are deleted when dashboard is deleted."""
        # Create a test user
        user = User(
            username="filtercascadeuser",
            email="<EMAIL>",
            role=UserRole.ANALYST
        )
        user.set_password("password123")
        db_session.add(user)
        db_session.commit()

        # Create a dashboard
        dashboard = Dashboard(
            name="Filter Cascade Dashboard",
            description="Dashboard for filter cascade test",
            layout={},
            is_public=False,
            created_by_id=user.id
        )
        db_session.add(dashboard)
        db_session.commit()

        # Create a filter
        filter_obj = DashboardFilter(
            dashboard_id=dashboard.id,
            name="Cascade Filter",
            field="date",
            operator="greater_than",
            value="2023-01-01",
            is_global=True,
            applies_to_widgets=None
        )
        db_session.add(filter_obj)
        db_session.commit()

        # Get the filter ID
        filter_id = filter_obj.id

        # Delete the dashboard
        db_session.delete(dashboard)
        db_session.commit()

        # Verify filter was deleted
        assert db_session.query(DashboardFilter).filter_by(id=filter_id).first() is None


class TestDashboardShareModel:
    """Test the DashboardShare model."""

    def test_create_share(self, db_session):
        """Test creating a dashboard share."""
        # Create test users
        owner = User(
            username="shareowner",
            email="<EMAIL>",
            role=UserRole.ANALYST
        )
        owner.set_password("password123")
        db_session.add(owner)

        recipient = User(
            username="sharerecipient",
            email="<EMAIL>",
            role=UserRole.ANALYST
        )
        recipient.set_password("password123")
        db_session.add(recipient)
        db_session.commit()

        # Create a dashboard
        dashboard = Dashboard(
            name="Share Dashboard",
            description="Dashboard for share test",
            layout={},
            is_public=False,
            created_by_id=owner.id
        )
        db_session.add(dashboard)
        db_session.commit()

        # Create a share
        share = DashboardShare(
            dashboard_id=dashboard.id,
            user_id=recipient.id,
            permission=SharePermission.VIEW,
            created_by_id=owner.id
        )
        db_session.add(share)
        db_session.commit()
        db_session.refresh(share)

        # Verify share was created
        assert share.id is not None
        assert share.dashboard_id == dashboard.id
        assert share.user_id == recipient.id
        assert share.permission == SharePermission.VIEW
        assert share.created_by_id == owner.id
        assert share.created_at is not None
        assert share.dashboard.name == "Share Dashboard"
        assert share.user.username == "sharerecipient"
        assert share.created_by.username == "shareowner"

    def test_share_to_dict(self, db_session):
        """Test the to_dict method of DashboardShare."""
        # Create test users
        owner = User(
            username="sharedictowner",
            email="<EMAIL>",
            role=UserRole.ANALYST
        )
        owner.set_password("password123")
        db_session.add(owner)

        recipient = User(
            username="sharedictrecipient",
            email="<EMAIL>",
            role=UserRole.ANALYST
        )
        recipient.set_password("password123")
        db_session.add(recipient)
        db_session.commit()

        # Create a dashboard
        dashboard = Dashboard(
            name="Share Dict Dashboard",
            description="Dashboard for share dict test",
            layout={},
            is_public=False,
            created_by_id=owner.id
        )
        db_session.add(dashboard)
        db_session.commit()

        # Create a share
        share = DashboardShare(
            dashboard_id=dashboard.id,
            user_id=recipient.id,
            permission=SharePermission.EDIT,
            created_by_id=owner.id
        )
        db_session.add(share)
        db_session.commit()

        # Test to_dict method
        share_dict = share.to_dict()
        assert share_dict["id"] == share.id
        assert share_dict["dashboard_id"] == dashboard.id
        assert share_dict["user_id"] == recipient.id
        assert share_dict["permission"] == "EDIT"
        assert share_dict["created_by_id"] == owner.id
        assert "created_at" in share_dict

    def test_share_unique_constraint(self, db_session):
        """Test that a user can only have one share per dashboard."""
        # Create test users
        owner = User(
            username="uniqueshareowner",
            email="<EMAIL>",
            role=UserRole.ANALYST
        )
        owner.set_password("password123")
        db_session.add(owner)

        recipient = User(
            username="uniquesharerecipient",
            email="<EMAIL>",
            role=UserRole.ANALYST
        )
        recipient.set_password("password123")
        db_session.add(recipient)
        db_session.commit()

        # Create a dashboard
        dashboard = Dashboard(
            name="Unique Share Dashboard",
            description="Dashboard for unique share test",
            layout={},
            is_public=False,
            created_by_id=owner.id
        )
        db_session.add(dashboard)
        db_session.commit()

        # Create a share
        share1 = DashboardShare(
            dashboard_id=dashboard.id,
            user_id=recipient.id,
            permission=SharePermission.VIEW,
            created_by_id=owner.id
        )
        db_session.add(share1)
        db_session.commit()

        # Try to create another share for the same user and dashboard
        share2 = DashboardShare(
            dashboard_id=dashboard.id,
            user_id=recipient.id,
            permission=SharePermission.EDIT,
            created_by_id=owner.id
        )
        db_session.add(share2)
        
        # This should raise an IntegrityError due to the unique constraint
        with pytest.raises(IntegrityError):
            db_session.commit()
        
        # Rollback the transaction
        db_session.rollback()

    def test_share_cascade_delete(self, db_session):
        """Test that shares are deleted when dashboard is deleted."""
        # Create test users
        owner = User(
            username="sharecascadeowner",
            email="<EMAIL>",
            role=UserRole.ANALYST
        )
        owner.set_password("password123")
        db_session.add(owner)

        recipient = User(
            username="sharecascaderecipient",
            email="<EMAIL>",
            role=UserRole.ANALYST
        )
        recipient.set_password("password123")
        db_session.add(recipient)
        db_session.commit()

        # Create a dashboard
        dashboard = Dashboard(
            name="Share Cascade Dashboard",
            description="Dashboard for share cascade test",
            layout={},
            is_public=False,
            created_by_id=owner.id
        )
        db_session.add(dashboard)
        db_session.commit()

        # Create a share
        share = DashboardShare(
            dashboard_id=dashboard.id,
            user_id=recipient.id,
            permission=SharePermission.VIEW,
            created_by_id=owner.id
        )
        db_session.add(share)
        db_session.commit()

        # Get the share ID
        share_id = share.id

        # Delete the dashboard
        db_session.delete(dashboard)
        db_session.commit()

        # Verify share was deleted
        assert db_session.query(DashboardShare).filter_by(id=share_id).first() is None
