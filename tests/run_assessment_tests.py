#!/usr/bin/env python
"""
Test runner for assessment, campaign, and test case tests.

This script runs the test suite for the assessment, campaign, and test case
features, providing a convenient way to execute all related tests.
"""
import os
import sys
import pytest
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('assessment_tests.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Add project root to Python path
project_root = str(Path(__file__).parent.parent)
sys.path.insert(0, project_root)

def run_tests():
    """Run the assessment, campaign, and test case tests."""
    logger.info("Starting assessment, campaign, and test case tests")

    # Define test files to run
    test_files = [
        "tests/test_api/test_assessments.py",
        "tests/test_api/test_campaigns.py",
        "tests/test_api/test_test_cases.py",
        "tests/test_api/test_integration.py",
        "tests/test_api/test_execution_framework_reporting.py",
        "tests/test_api/test_execution_framework_comparison.py",
        "tests/test_services/test_execution_framework_reporting.py",
        "tests/test_services/test_execution_framework_comparison.py"
    ]

    # Run the tests
    exit_code = pytest.main([
        "-v",
        "--tb=native",
        "--no-header",
        "--show-capture=all",
        *test_files
    ])

    if exit_code == 0:
        logger.info("All tests passed successfully!")
    else:
        logger.error(f"Tests failed with exit code: {exit_code}")

    return exit_code

if __name__ == "__main__":
    sys.exit(run_tests())