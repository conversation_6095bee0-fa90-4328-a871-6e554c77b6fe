"""
Performance and load tests for Enhanced Testcase Chaining & Sequencing.

This module contains performance tests to ensure the testcase chaining system
can handle large-scale chains, concurrent executions, and high-load scenarios
efficiently.
"""

import pytest
import time
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor
from unittest.mock import Mock, patch

from api.models.testcase_chain import (
    TestcaseChain,
    TestcaseChainNode,
    TestcaseChainEdge,
    ChainExecution
)
from api.services.testcase_chain_service import TestcaseChainService


class TestChainPerformance:
    """Performance tests for chain operations."""
    
    @pytest.fixture
    def mock_db(self):
        """Create a mock database session."""
        return Mock()
    
    @pytest.fixture
    def service(self, mock_db):
        """Create a testcase chain service."""
        return TestcaseChainService(mock_db)
    
    def test_large_chain_creation_performance(self, service, mock_db):
        """Test performance of creating chains with many nodes."""
        # Mock database responses
        mock_chain = TestcaseChain(id=1, name="Large Chain", created_by=1)
        mock_db.query.return_value.filter.return_value.first.side_effect = [
            mock_chain,  # Chain exists
            None,  # No duplicate nodes
        ] * 1000  # Support 1000 nodes
        
        # Test creating a chain with 1000 nodes
        start_time = time.time()
        
        # Create the chain
        chain = service.create_chain(
            name="Large Performance Test Chain",
            description="Chain with 1000 nodes for performance testing",
            chain_type="sequential",
            created_by=1
        )
        
        # Add 1000 nodes
        nodes = []
        for i in range(1000):
            mock_node = TestcaseChainNode(
                id=i + 1,
                chain_id=1,
                testcase_id=1000 + i,
                node_type="standard" if i not in [0, 999] else ("start" if i == 0 else "end"),
                execution_order=i + 1
            )
            
            node = service.add_node_to_chain(
                chain_id=1,
                testcase_id=1000 + i,
                node_type=mock_node.node_type,
                execution_order=i + 1
            )
            nodes.append(node)
        
        end_time = time.time()
        creation_time = end_time - start_time
        
        # Performance assertions
        assert creation_time < 10.0  # Should complete within 10 seconds
        assert len(nodes) == 1000
        
        # Verify database operations were called efficiently
        assert mock_db.add.call_count >= 1000  # At least one call per node
        assert mock_db.commit.call_count >= 1000  # At least one commit per node
    
    def test_large_chain_validation_performance(self, service, mock_db):
        """Test performance of validating large chains."""
        # Create mock nodes for a large chain
        nodes = []
        edges = []
        
        # Create 500 nodes in a linear chain
        for i in range(500):
            node = TestcaseChainNode(
                id=i + 1,
                chain_id=1,
                testcase_id=2000 + i,
                node_type="start" if i == 0 else ("end" if i == 499 else "standard"),
                execution_order=i + 1
            )
            nodes.append(node)
            
            # Create edge to next node (except for last node)
            if i < 499:
                edge = TestcaseChainEdge(
                    id=i + 1,
                    source_node_id=i + 1,
                    target_node_id=i + 2,
                    edge_type="standard"
                )
                edges.append(edge)
        
        # Mock database responses
        mock_chain = TestcaseChain(id=1, name="Large Validation Chain")
        mock_db.query.return_value.filter.return_value.first.return_value = mock_chain
        mock_db.query.return_value.filter.return_value.all.side_effect = [nodes, edges]
        
        # Test validation performance
        start_time = time.time()
        
        validation_result = service.validate_chain(1)
        
        end_time = time.time()
        validation_time = end_time - start_time
        
        # Performance assertions
        assert validation_time < 5.0  # Should validate within 5 seconds
        assert validation_result.is_valid is True
        assert validation_result.cycle_detected is False
        assert len(validation_result.unreachable_nodes) == 0
    
    def test_cycle_detection_performance(self, service):
        """Test performance of cycle detection in complex graphs."""
        # Create a complex graph with potential cycles
        nodes = []
        edges = []
        
        # Create 100 nodes
        for i in range(100):
            node = TestcaseChainNode(
                id=i + 1,
                chain_id=1,
                testcase_id=3000 + i
            )
            nodes.append(node)
        
        # Create edges that form a complex graph with cycles
        for i in range(100):
            # Forward edges
            if i < 99:
                edge = TestcaseChainEdge(
                    id=len(edges) + 1,
                    source_node_id=i + 1,
                    target_node_id=i + 2
                )
                edges.append(edge)
            
            # Create some backward edges to form cycles
            if i > 0 and i % 10 == 0:
                edge = TestcaseChainEdge(
                    id=len(edges) + 1,
                    source_node_id=i + 1,
                    target_node_id=max(1, i - 5)  # Create cycle
                )
                edges.append(edge)
        
        # Test cycle detection performance
        start_time = time.time()
        
        has_cycle = service._detect_cycles(nodes, edges)
        
        end_time = time.time()
        detection_time = end_time - start_time
        
        # Performance assertions
        assert detection_time < 2.0  # Should detect cycles within 2 seconds
        assert has_cycle is True  # Should detect the cycles we created
    
    def test_concurrent_chain_operations(self, service, mock_db):
        """Test concurrent chain operations performance."""
        # Mock database responses for concurrent operations
        mock_chains = [
            TestcaseChain(id=i, name=f"Concurrent Chain {i}", created_by=1)
            for i in range(1, 11)
        ]
        
        mock_db.query.return_value.filter.return_value.first.side_effect = mock_chains * 10
        
        def create_chain_worker(chain_id):
            """Worker function for creating chains concurrently."""
            return service.create_chain(
                name=f"Concurrent Chain {chain_id}",
                description=f"Chain created by worker {chain_id}",
                chain_type="sequential",
                created_by=1
            )
        
        # Test concurrent chain creation
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [
                executor.submit(create_chain_worker, i)
                for i in range(1, 11)
            ]
            
            results = [future.result() for future in futures]
        
        end_time = time.time()
        concurrent_time = end_time - start_time
        
        # Performance assertions
        assert concurrent_time < 5.0  # Should complete within 5 seconds
        assert len(results) == 10
        assert all(result is not None for result in results)
    
    def test_execution_monitoring_performance(self, service, mock_db):
        """Test performance of execution monitoring for multiple chains."""
        # Create mock executions
        mock_executions = []
        for i in range(50):
            execution = ChainExecution(
                id=i + 1,
                chain_id=i + 1,
                started_by=1,
                status="running",
                total_nodes=10,
                completed_nodes=i % 10,
                failed_nodes=0
            )
            mock_executions.append(execution)
        
        mock_db.query.return_value.filter.return_value.all.return_value = mock_executions
        
        # Test monitoring performance
        start_time = time.time()
        
        # Simulate monitoring 50 concurrent executions
        for execution in mock_executions:
            mock_db.query.return_value.filter.return_value.first.return_value = execution
            
            # Get execution status
            status = execution.status
            progress = execution.completed_nodes / execution.total_nodes
            
            # Simulate some monitoring logic
            assert status in ["pending", "running", "completed", "failed"]
            assert 0 <= progress <= 1
        
        end_time = time.time()
        monitoring_time = end_time - start_time
        
        # Performance assertions
        assert monitoring_time < 1.0  # Should monitor all executions within 1 second
    
    def test_dependency_resolution_performance(self, service):
        """Test performance of dependency resolution for complex chains."""
        # Create a complex dependency graph
        nodes = []
        edges = []
        
        # Create 200 nodes with complex dependencies
        for i in range(200):
            node = TestcaseChainNode(
                id=i + 1,
                chain_id=1,
                testcase_id=4000 + i,
                execution_order=i + 1
            )
            nodes.append(node)
        
        # Create complex dependency edges
        for i in range(200):
            # Each node depends on 2-3 previous nodes
            for j in range(max(0, i - 3), i):
                if j >= 0:
                    edge = TestcaseChainEdge(
                        id=len(edges) + 1,
                        source_node_id=j + 1,
                        target_node_id=i + 1
                    )
                    edges.append(edge)
        
        # Mock execution with some completed nodes
        mock_execution = ChainExecution(
            id=1,
            chain_id=1,
            status="running",
            total_nodes=200
        )
        
        # Mock some completed node executions
        completed_nodes = set(range(1, 51))  # First 50 nodes completed
        
        # Test dependency resolution performance
        start_time = time.time()
        
        # Find next executable nodes
        next_nodes = []
        for node in nodes:
            # Check if all dependencies are satisfied
            dependencies_satisfied = True
            for edge in edges:
                if edge.target_node_id == node.id:
                    if edge.source_node_id not in completed_nodes:
                        dependencies_satisfied = False
                        break
            
            if dependencies_satisfied and node.id not in completed_nodes:
                next_nodes.append(node)
        
        end_time = time.time()
        resolution_time = end_time - start_time
        
        # Performance assertions
        assert resolution_time < 3.0  # Should resolve dependencies within 3 seconds
        assert len(next_nodes) > 0  # Should find some executable nodes
    
    def test_memory_usage_large_chains(self, service, mock_db):
        """Test memory usage with large chains."""
        import psutil
        import os
        
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create large chain structures
        large_chains = []
        for chain_id in range(10):
            # Mock large chain
            mock_chain = TestcaseChain(
                id=chain_id + 1,
                name=f"Memory Test Chain {chain_id}",
                created_by=1
            )
            large_chains.append(mock_chain)
            
            # Create many nodes for each chain
            nodes = []
            for node_id in range(100):
                node = TestcaseChainNode(
                    id=node_id + 1,
                    chain_id=chain_id + 1,
                    testcase_id=5000 + node_id,
                    execution_order=node_id + 1
                )
                nodes.append(node)
        
        # Get memory usage after creating large structures
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory usage assertions
        assert memory_increase < 100  # Should not use more than 100MB additional memory
        assert len(large_chains) == 10
    
    @pytest.mark.asyncio
    async def test_async_chain_operations(self, service, mock_db):
        """Test asynchronous chain operations performance."""
        # Mock database responses
        mock_chain = TestcaseChain(id=1, name="Async Chain", created_by=1)
        mock_db.query.return_value.filter.return_value.first.return_value = mock_chain
        
        async def async_chain_operation(operation_id):
            """Simulate async chain operation."""
            await asyncio.sleep(0.1)  # Simulate async work
            return f"Operation {operation_id} completed"
        
        # Test concurrent async operations
        start_time = time.time()
        
        tasks = [
            async_chain_operation(i)
            for i in range(20)
        ]
        
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        async_time = end_time - start_time
        
        # Performance assertions
        assert async_time < 1.0  # Should complete much faster than sequential (2 seconds)
        assert len(results) == 20
        assert all("completed" in result for result in results)
