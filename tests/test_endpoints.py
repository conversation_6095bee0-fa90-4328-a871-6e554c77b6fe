"""Tests for API endpoints using FastAPI's TestClient."""
import pytest
from fastapi.testclient import TestClient
import base64

@pytest.mark.asyncio
async def test_main_ui(client: TestClient):
    """Test the main UI endpoint."""
    response = client.get("/")
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]

@pytest.mark.asyncio
async def test_swagger_ui(client: TestClient):
    """Test the Swagger UI endpoint."""
    response = client.get("/api/v1/docs")
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]
    # Check for Swagger UI specific content
    assert "swagger-ui" in response.text.lower()

@pytest.mark.asyncio
async def test_redoc_interface(client: TestClient):
    """Test the ReDoc interface endpoint."""
    response = client.get("/api/v1/redoc")
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]
    # Check for ReDoc specific content
    assert "redoc" in response.text.lower()

@pytest.mark.asyncio
async def test_openapi_json(client: TestClient):
    """Test the OpenAPI JSON endpoint."""
    response = client.get("/api/v1/openapi.json")
    assert response.status_code == 200
    assert response.headers["content-type"] == "application/json"
    # Verify JSON content
    api_spec = response.json()
    assert "openapi" in api_spec
    assert "paths" in api_spec
    assert "info" in api_spec

@pytest.mark.asyncio
async def test_admin_interface_unauthorized(client: TestClient):
    """Test the admin interface endpoint without authentication."""
    response = client.get("/admin/", follow_redirects=True)
    assert response.status_code == 401
    assert "Not authenticated" in response.text

@pytest.mark.asyncio
async def test_admin_interface_authorized(client: TestClient):
    """Test the admin interface endpoint with correct authentication."""
    credentials = base64.b64encode(b"admin:admin").decode('utf-8')
    headers = {"Authorization": f"Basic {credentials}"}
    response = client.get("/admin/", headers=headers, follow_redirects=True)
    assert response.status_code == 200
    assert "RegressionRigor Admin" in response.text

@pytest.mark.asyncio
async def test_admin_interface_invalid_credentials(client: TestClient):
    """Test the admin interface with invalid credentials."""
    credentials = base64.b64encode(b"wrong:password").decode('utf-8')
    headers = {"Authorization": f"Basic {credentials}"}
    response = client.get("/admin/", headers=headers, follow_redirects=True)
    assert response.status_code == 401
    assert "Invalid credentials" in response.text

@pytest.mark.asyncio
async def test_admin_interface_invalid_auth_header(client: TestClient):
    """Test the admin interface with invalid authorization header."""
    headers = {"Authorization": "Invalid Auth"}
    response = client.get("/admin/", headers=headers, follow_redirects=True)
    assert response.status_code == 401
    assert "Invalid authentication method" in response.text

@pytest.mark.asyncio
async def test_health_check(client: TestClient):
    """Test the health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "healthy"}

@pytest.mark.asyncio
async def test_analyze_endpoint(client: TestClient):
    """Test the analyze endpoint with test data."""
    test_data = {
        "technique_ids": "T1055\nT1056"
    }
    response = client.post("/analyze", data=test_data)
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]
    # Verify response content
    assert "Coverage Analysis" in response.text