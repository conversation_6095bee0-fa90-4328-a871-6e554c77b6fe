"""
Tests for the comparison service functions in the execution framework.

This module contains tests for the comparison service functions, including
campaign comparison and time period comparison reports.
"""
import io
import json
import pytest
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from fastapi import HTTPException
from api.services.execution_framework import comparison
from api.models.database.execution_framework.models import (
    Campaign,
    ExecutionLog,
    ExecutionStatus,
    MitreTechnique,
    TestCase,
)
from api.models.database.execution_framework.analytics_models import (
    ExecutionMetric,
    ExecutionTrend,
    MitreCoverageMetric,
)


@pytest.fixture
def mock_campaign():
    """Create a mock campaign for testing."""
    return Campaign(
        id=1,
        name="Test Campaign 1",
        description="Test campaign for comparison",
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by="test_user",
        test_cases=[MagicMock(test_case_id=1), MagicMock(test_case_id=2)]
    )


@pytest.fixture
def mock_campaign2():
    """Create a second mock campaign for testing."""
    return Campaign(
        id=2,
        name="Test Campaign 2",
        description="Second test campaign for comparison",
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by="test_user",
        test_cases=[MagicMock(test_case_id=2), MagicMock(test_case_id=3)]
    )


@pytest.fixture
def mock_test_case():
    """Create a mock test case for testing."""
    return TestCase(
        id=1,
        name="Test Case 1",
        description="Test case for comparison",
        command="echo 'test'",
        executor_type="bash",
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by="test_user",
        mitre_techniques=["T1059.001", "T1059.004"]
    )


@pytest.fixture
def mock_test_case2():
    """Create a second mock test case for testing."""
    return TestCase(
        id=2,
        name="Test Case 2",
        description="Second test case for comparison",
        command="Get-Process",
        executor_type="powershell",
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by="test_user",
        mitre_techniques=["T1057", "T1059.001"]
    )


@pytest.fixture
def mock_test_case3():
    """Create a third mock test case for testing."""
    return TestCase(
        id=3,
        name="Test Case 3",
        description="Third test case for comparison",
        command="netstat -ano",
        executor_type="cmd",
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by="test_user",
        mitre_techniques=["T1049", "T1057"]
    )


@pytest.fixture
def mock_mitre_technique():
    """Create a mock MITRE technique for testing."""
    return MitreTechnique(
        id=1,
        technique_id="T1059.001",
        name="PowerShell",
        description="Adversaries may abuse PowerShell commands and scripts for execution.",
        tactic="execution",
        platforms=["windows"],
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


@pytest.fixture
def mock_mitre_technique2():
    """Create a second mock MITRE technique for testing."""
    return MitreTechnique(
        id=2,
        technique_id="T1057",
        name="Process Discovery",
        description="Adversaries may attempt to get information about running processes on a system.",
        tactic="discovery",
        platforms=["windows", "macos", "linux"],
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


@pytest.fixture
def mock_mitre_technique3():
    """Create a third mock MITRE technique for testing."""
    return MitreTechnique(
        id=3,
        technique_id="T1049",
        name="System Network Connections Discovery",
        description="Adversaries may attempt to get a listing of network connections to or from the compromised system.",
        tactic="discovery",
        platforms=["windows", "macos", "linux"],
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


@pytest.fixture
def mock_mitre_technique4():
    """Create a fourth mock MITRE technique for testing."""
    return MitreTechnique(
        id=4,
        technique_id="T1059.004",
        name="Unix Shell",
        description="Adversaries may abuse Unix shell commands and scripts for execution.",
        tactic="execution",
        platforms=["macos", "linux"],
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


@pytest.fixture
def mock_execution_log():
    """Create a mock execution log for testing."""
    return ExecutionLog(
        id=1,
        test_case_id=1,
        campaign_id=1,
        status=ExecutionStatus.COMPLETED.value,
        start_time=datetime.now() - timedelta(days=1),
        end_time=datetime.now() - timedelta(days=1) + timedelta(minutes=5),
        output="Test output",
        created_at=datetime.now() - timedelta(days=1),
        updated_at=datetime.now() - timedelta(days=1)
    )


@pytest.fixture
def mock_execution_log2():
    """Create a second mock execution log for testing."""
    return ExecutionLog(
        id=2,
        test_case_id=2,
        campaign_id=1,
        status=ExecutionStatus.COMPLETED.value,
        start_time=datetime.now() - timedelta(days=1),
        end_time=datetime.now() - timedelta(days=1) + timedelta(minutes=3),
        output="Test output 2",
        created_at=datetime.now() - timedelta(days=1),
        updated_at=datetime.now() - timedelta(days=1)
    )


@pytest.fixture
def mock_execution_log3():
    """Create a third mock execution log for testing."""
    return ExecutionLog(
        id=3,
        test_case_id=2,
        campaign_id=2,
        status=ExecutionStatus.COMPLETED.value,
        start_time=datetime.now() - timedelta(hours=5),
        end_time=datetime.now() - timedelta(hours=5) + timedelta(minutes=4),
        output="Test output 3",
        created_at=datetime.now() - timedelta(hours=5),
        updated_at=datetime.now() - timedelta(hours=5)
    )


@pytest.fixture
def mock_execution_log4():
    """Create a fourth mock execution log for testing."""
    return ExecutionLog(
        id=4,
        test_case_id=3,
        campaign_id=2,
        status=ExecutionStatus.COMPLETED.value,
        start_time=datetime.now() - timedelta(hours=4),
        end_time=datetime.now() - timedelta(hours=4) + timedelta(minutes=6),
        output="Test output 4",
        created_at=datetime.now() - timedelta(hours=4),
        updated_at=datetime.now() - timedelta(hours=4)
    )


@pytest.fixture
def mock_execution_metric():
    """Create a mock execution metric for testing."""
    return ExecutionMetric(
        id=1,
        test_case_id=1,
        campaign_id=1,
        execution_count=10,
        success_count=8,
        failure_count=2,
        success_rate=80.0,
        execution_time=300.0,
        created_at=datetime.now() - timedelta(days=1),
        updated_at=datetime.now() - timedelta(days=1)
    )


@pytest.fixture
def mock_execution_metric2():
    """Create a second mock execution metric for testing."""
    return ExecutionMetric(
        id=2,
        test_case_id=2,
        campaign_id=1,
        execution_count=15,
        success_count=14,
        failure_count=1,
        success_rate=93.3,
        execution_time=180.0,
        created_at=datetime.now() - timedelta(days=1),
        updated_at=datetime.now() - timedelta(days=1)
    )


@pytest.fixture
def mock_execution_metric3():
    """Create a third mock execution metric for testing."""
    return ExecutionMetric(
        id=3,
        test_case_id=2,
        campaign_id=2,
        execution_count=12,
        success_count=10,
        failure_count=2,
        success_rate=83.3,
        execution_time=200.0,
        created_at=datetime.now() - timedelta(hours=5),
        updated_at=datetime.now() - timedelta(hours=5)
    )


@pytest.fixture
def mock_execution_metric4():
    """Create a fourth mock execution metric for testing."""
    return ExecutionMetric(
        id=4,
        test_case_id=3,
        campaign_id=2,
        execution_count=8,
        success_count=7,
        failure_count=1,
        success_rate=87.5,
        execution_time=250.0,
        created_at=datetime.now() - timedelta(hours=4),
        updated_at=datetime.now() - timedelta(hours=4)
    )


@pytest.fixture
def mock_db_session(
    mock_campaign,
    mock_campaign2,
    mock_test_case,
    mock_test_case2,
    mock_test_case3,
    mock_mitre_technique,
    mock_mitre_technique2,
    mock_mitre_technique3,
    mock_mitre_technique4,
    mock_execution_log,
    mock_execution_log2,
    mock_execution_log3,
    mock_execution_log4,
    mock_execution_metric,
    mock_execution_metric2,
    mock_execution_metric3,
    mock_execution_metric4,
):
    """Create a mock database session with test data."""
    db_session = MagicMock()
    
    # Configure query mocks
    campaign_query = MagicMock()
    campaign_query.filter.return_value.first.side_effect = lambda: mock_campaign if campaign_query.filter.call_args[0][0].right.value == 1 else mock_campaign2
    
    test_case_query = MagicMock()
    test_case_query.filter.return_value.all.return_value = [mock_test_case, mock_test_case2, mock_test_case3]
    
    mitre_technique_query = MagicMock()
    mitre_technique_query.filter.return_value.all.return_value = [
        mock_mitre_technique, 
        mock_mitre_technique2, 
        mock_mitre_technique3, 
        mock_mitre_technique4
    ]
    
    execution_log_query = MagicMock()
    execution_log_query.filter.return_value.all.return_value = [
        mock_execution_log, 
        mock_execution_log2, 
        mock_execution_log3, 
        mock_execution_log4
    ]
    
    execution_metric_query = MagicMock()
    execution_metric_query.filter.return_value.all.return_value = [
        mock_execution_metric, 
        mock_execution_metric2, 
        mock_execution_metric3, 
        mock_execution_metric4
    ]
    
    # Configure db_session.query to return appropriate mock query
    def query_side_effect(model):
        if model == Campaign:
            return campaign_query
        elif model == TestCase:
            return test_case_query
        elif model == MitreTechnique:
            return mitre_technique_query
        elif model == ExecutionLog:
            return execution_log_query
        elif model == ExecutionMetric:
            return execution_metric_query
        return MagicMock()
    
    db_session.query.side_effect = query_side_effect
    
    return db_session


def test_generate_campaign_comparison_report_xlsx(mock_db_session):
    """Test generating a campaign comparison report in XLSX format."""
    with patch('api.services.execution_framework.comparison._generate_comparison_xlsx_report') as mock_generate_xlsx:
        # Mock the report generation function
        mock_output = io.BytesIO(b"test xlsx data")
        mock_filename = "test_comparison_report.xlsx"
        mock_generate_xlsx.return_value = (mock_output, mock_filename)
        
        # Call the function
        output, filename = comparison.generate_campaign_comparison_report(
            db=mock_db_session,
            campaign_id1=1,
            campaign_id2=2,
            format="xlsx"
        )
        
        # Check the result
        assert output == mock_output
        assert filename == mock_filename
        
        # Verify the query was called with correct parameters
        mock_db_session.query.assert_called()
        mock_generate_xlsx.assert_called_once()


def test_generate_campaign_comparison_report_json(mock_db_session):
    """Test generating a campaign comparison report in JSON format."""
    with patch('api.services.execution_framework.comparison._generate_comparison_json_report') as mock_generate_json:
        # Mock the report generation function
        mock_output = io.BytesIO(json.dumps({"test": "data"}).encode('utf-8'))
        mock_filename = "test_comparison_report.json"
        mock_generate_json.return_value = (mock_output, mock_filename)
        
        # Call the function
        output, filename = comparison.generate_campaign_comparison_report(
            db=mock_db_session,
            campaign_id1=1,
            campaign_id2=2,
            format="json"
        )
        
        # Check the result
        assert output == mock_output
        assert filename == mock_filename
        
        # Verify the query was called with correct parameters
        mock_db_session.query.assert_called()
        mock_generate_json.assert_called_once()


def test_generate_campaign_comparison_report_invalid_format(mock_db_session):
    """Test generating a campaign comparison report with an invalid format."""
    # Call the function with an invalid format
    with pytest.raises(HTTPException) as excinfo:
        comparison.generate_campaign_comparison_report(
            db=mock_db_session,
            campaign_id1=1,
            campaign_id2=2,
            format="pdf"  # Invalid format for campaign comparison
        )
    
    # Check the exception
    assert excinfo.value.status_code == 400
    assert "Unsupported report format" in excinfo.value.detail


def test_generate_campaign_comparison_report_campaign_not_found(mock_db_session):
    """Test generating a campaign comparison report with a non-existent campaign."""
    # Configure the mock to return None for campaign_id=999
    campaign_query = MagicMock()
    campaign_query.filter.return_value.first.side_effect = lambda: None if campaign_query.filter.call_args[0][0].right.value == 999 else mock_db_session.query(Campaign).filter().first()
    
    mock_db_session.query.return_value = campaign_query
    
    # Call the function with a non-existent campaign
    with pytest.raises(HTTPException) as excinfo:
        comparison.generate_campaign_comparison_report(
            db=mock_db_session,
            campaign_id1=1,
            campaign_id2=999,  # Non-existent campaign
            format="xlsx"
        )
    
    # Check the exception
    assert excinfo.value.status_code == 404
    assert "Campaign with ID 999 not found" in excinfo.value.detail


def test_generate_time_period_comparison_report_xlsx(mock_db_session):
    """Test generating a time period comparison report in XLSX format."""
    with patch('api.services.execution_framework.comparison._generate_comparison_xlsx_report') as mock_generate_xlsx:
        # Mock the report generation function
        mock_output = io.BytesIO(b"test xlsx data")
        mock_filename = "test_time_period_comparison_report.xlsx"
        mock_generate_xlsx.return_value = (mock_output, mock_filename)
        
        # Call the function
        start_date1 = datetime.now() - timedelta(days=30)
        end_date1 = datetime.now() - timedelta(days=15)
        start_date2 = datetime.now() - timedelta(days=14)
        end_date2 = datetime.now()
        
        output, filename = comparison.generate_time_period_comparison_report(
            db=mock_db_session,
            start_date1=start_date1,
            end_date1=end_date1,
            start_date2=start_date2,
            end_date2=end_date2,
            campaign_id=1,
            format="xlsx"
        )
        
        # Check the result
        assert output == mock_output
        assert filename == mock_filename
        
        # Verify the query was called with correct parameters
        mock_db_session.query.assert_called()
        mock_generate_xlsx.assert_called_once()


def test_generate_time_period_comparison_report_json(mock_db_session):
    """Test generating a time period comparison report in JSON format."""
    with patch('api.services.execution_framework.comparison._generate_comparison_json_report') as mock_generate_json:
        # Mock the report generation function
        mock_output = io.BytesIO(json.dumps({"test": "data"}).encode('utf-8'))
        mock_filename = "test_time_period_comparison_report.json"
        mock_generate_json.return_value = (mock_output, mock_filename)
        
        # Call the function
        start_date1 = datetime.now() - timedelta(days=30)
        end_date1 = datetime.now() - timedelta(days=15)
        start_date2 = datetime.now() - timedelta(days=14)
        end_date2 = datetime.now()
        
        output, filename = comparison.generate_time_period_comparison_report(
            db=mock_db_session,
            start_date1=start_date1,
            end_date1=end_date1,
            start_date2=start_date2,
            end_date2=end_date2,
            test_case_id=1,
            format="json"
        )
        
        # Check the result
        assert output == mock_output
        assert filename == mock_filename
        
        # Verify the query was called with correct parameters
        mock_db_session.query.assert_called()
        mock_generate_json.assert_called_once()


def test_generate_time_period_comparison_report_invalid_format(mock_db_session):
    """Test generating a time period comparison report with an invalid format."""
    # Call the function with an invalid format
    with pytest.raises(HTTPException) as excinfo:
        start_date1 = datetime.now() - timedelta(days=30)
        end_date1 = datetime.now() - timedelta(days=15)
        start_date2 = datetime.now() - timedelta(days=14)
        end_date2 = datetime.now()
        
        comparison.generate_time_period_comparison_report(
            db=mock_db_session,
            start_date1=start_date1,
            end_date1=end_date1,
            start_date2=start_date2,
            end_date2=end_date2,
            campaign_id=1,
            format="csv"  # Invalid format for time period comparison
        )
    
    # Check the exception
    assert excinfo.value.status_code == 400
    assert "Unsupported report format" in excinfo.value.detail


def test_generate_comparison_xlsx_report():
    """Test generating an Excel comparison report."""
    # Create test data
    summary_data = {
        "campaign1": {
            "id": 1,
            "name": "Test Campaign 1",
            "test_case_count": 2,
            "technique_count": 3,
            "success_rate": 85.0,
            "avg_execution_time": 240.0,
        },
        "campaign2": {
            "id": 2,
            "name": "Test Campaign 2",
            "test_case_count": 2,
            "technique_count": 2,
            "success_rate": 90.0,
            "avg_execution_time": 220.0,
        },
        "comparison": {
            "shared_techniques": 1,
            "unique_to_campaign1": 2,
            "unique_to_campaign2": 1,
            "total_unique_techniques": 4,
            "success_rate_diff": 5.0,
            "execution_time_diff": -20.0,
        }
    }
    
    technique_data = [
        {
            "technique_id": "T1059.001",
            "name": "PowerShell",
            "tactic": "execution",
            "in_campaign1": True,
            "in_campaign2": False,
            "status": "Campaign 1 Only",
        },
        {
            "technique_id": "T1057",
            "name": "Process Discovery",
            "tactic": "discovery",
            "in_campaign1": True,
            "in_campaign2": True,
            "status": "Both",
        },
        {
            "technique_id": "T1049",
            "name": "System Network Connections Discovery",
            "tactic": "discovery",
            "in_campaign1": False,
            "in_campaign2": True,
            "status": "Campaign 2 Only",
        }
    ]
    
    # Call the function
    output, filename = comparison._generate_comparison_xlsx_report(
        summary_data=summary_data,
        technique_data=technique_data,
        entity1_name="Test Campaign 1",
        entity2_name="Test Campaign 2"
    )
    
    # Check the result
    assert isinstance(output, io.BytesIO)
    assert "comparison_report_" in filename
    assert filename.endswith(".xlsx")
    
    # Verify the output contains Excel data
    output.seek(0)
    assert output.read(4) == b"PK\x03\x04"  # Excel files start with this signature


def test_generate_comparison_json_report():
    """Test generating a JSON comparison report."""
    # Create test data
    summary_data = {
        "campaign1": {
            "id": 1,
            "name": "Test Campaign 1",
            "test_case_count": 2,
            "technique_count": 3,
            "success_rate": 85.0,
            "avg_execution_time": 240.0,
        },
        "campaign2": {
            "id": 2,
            "name": "Test Campaign 2",
            "test_case_count": 2,
            "technique_count": 2,
            "success_rate": 90.0,
            "avg_execution_time": 220.0,
        },
        "comparison": {
            "shared_techniques": 1,
            "unique_to_campaign1": 2,
            "unique_to_campaign2": 1,
            "total_unique_techniques": 4,
            "success_rate_diff": 5.0,
            "execution_time_diff": -20.0,
        }
    }
    
    technique_data = [
        {
            "technique_id": "T1059.001",
            "name": "PowerShell",
            "tactic": "execution",
            "in_campaign1": True,
            "in_campaign2": False,
            "status": "Campaign 1 Only",
        },
        {
            "technique_id": "T1057",
            "name": "Process Discovery",
            "tactic": "discovery",
            "in_campaign1": True,
            "in_campaign2": True,
            "status": "Both",
        },
        {
            "technique_id": "T1049",
            "name": "System Network Connections Discovery",
            "tactic": "discovery",
            "in_campaign1": False,
            "in_campaign2": True,
            "status": "Campaign 2 Only",
        }
    ]
    
    # Call the function
    output, filename = comparison._generate_comparison_json_report(
        summary_data=summary_data,
        technique_data=technique_data
    )
    
    # Check the result
    assert isinstance(output, io.BytesIO)
    assert "comparison_report_" in filename
    assert filename.endswith(".json")
    
    # Verify the output contains JSON data
    output.seek(0)
    content = json.loads(output.read().decode('utf-8'))
    assert "summary" in content
    assert "techniques" in content
    assert "generated_at" in content
    assert content["summary"] == summary_data
    assert content["techniques"] == technique_data
