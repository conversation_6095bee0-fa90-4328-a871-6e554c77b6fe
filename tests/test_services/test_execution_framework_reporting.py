"""
Tests for the reporting service functions in the execution framework.

This module contains tests for the reporting service functions, including
execution summary reports, MITRE coverage reports, and visualization reports.
"""
import io
import json
import os
import pytest
import tempfile
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock, mock_open

from api.services.execution_framework import reporting
from api.models.database.execution_framework.models import (
    Campaign,
    ExecutionLog,
    ExecutionStatus,
    MitreTechnique,
    TestCase,
)
from api.models.database.execution_framework.analytics_models import (
    ExecutionMetric,
    ExecutionTrend,
    MitreCoverageMetric,
)


@pytest.fixture
def mock_campaign():
    """Create a mock campaign for testing."""
    return Campaign(
        id=1,
        name="Test Campaign",
        description="Test campaign for reporting",
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by="test_user",
        test_cases=[MagicMock(test_case_id=1), MagicMock(test_case_id=2)]
    )


@pytest.fixture
def mock_test_case():
    """Create a mock test case for testing."""
    return TestCase(
        id=1,
        name="Test Case",
        description="Test case for reporting",
        command="echo 'test'",
        executor_type="bash",
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by="test_user",
        mitre_techniques=["T1059.001", "T1059.004"]
    )


@pytest.fixture
def mock_mitre_technique():
    """Create a mock MITRE technique for testing."""
    return MitreTechnique(
        id=1,
        technique_id="T1059.001",
        name="PowerShell",
        description="Adversaries may abuse PowerShell commands and scripts for execution.",
        tactic="execution",
        platforms=["windows"],
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


@pytest.fixture
def mock_execution_log():
    """Create a mock execution log for testing."""
    return ExecutionLog(
        id=1,
        test_case_id=1,
        campaign_id=1,
        status=ExecutionStatus.COMPLETED.value,
        start_time=datetime.now() - timedelta(days=1),
        end_time=datetime.now() - timedelta(days=1) + timedelta(minutes=5),
        output="Test output",
        created_at=datetime.now() - timedelta(days=1),
        updated_at=datetime.now() - timedelta(days=1)
    )


@pytest.fixture
def mock_execution_metric():
    """Create a mock execution metric for testing."""
    return ExecutionMetric(
        id=1,
        test_case_id=1,
        campaign_id=1,
        execution_count=10,
        success_count=8,
        failure_count=2,
        success_rate=80.0,
        execution_time=300.0,
        created_at=datetime.now() - timedelta(days=1),
        updated_at=datetime.now() - timedelta(days=1)
    )


@pytest.fixture
def mock_db_session(
    mock_campaign,
    mock_test_case,
    mock_mitre_technique,
    mock_execution_log,
    mock_execution_metric,
):
    """Create a mock database session with test data."""
    db_session = MagicMock()
    
    # Configure query mocks
    campaign_query = MagicMock()
    campaign_query.filter.return_value.first.return_value = mock_campaign
    
    test_case_query = MagicMock()
    test_case_query.filter.return_value.all.return_value = [mock_test_case]
    
    mitre_technique_query = MagicMock()
    mitre_technique_query.filter.return_value.all.return_value = [mock_mitre_technique]
    
    execution_log_query = MagicMock()
    execution_log_query.filter.return_value.all.return_value = [mock_execution_log]
    
    execution_metric_query = MagicMock()
    execution_metric_query.filter.return_value.all.return_value = [mock_execution_metric]
    
    # Configure db_session.query to return appropriate mock query
    def query_side_effect(model):
        if model == Campaign:
            return campaign_query
        elif model == TestCase:
            return test_case_query
        elif model == MitreTechnique:
            return mitre_technique_query
        elif model == ExecutionLog:
            return execution_log_query
        elif model == ExecutionMetric:
            return execution_metric_query
        return MagicMock()
    
    db_session.query.side_effect = query_side_effect
    
    return db_session


@pytest.fixture
def sample_layer_data():
    """Create sample layer data for testing."""
    return {
        "name": "Test Layer",
        "description": "Test layer for visualization",
        "domain": "enterprise-attack",
        "version": "17.0",
        "techniques": [
            {
                "techniqueID": "T1059.001",
                "tactic": "execution",
                "color": "#8ec843",
                "comment": "High coverage"
            },
            {
                "techniqueID": "T1057",
                "tactic": "discovery",
                "color": "#ffe766",
                "comment": "Medium coverage"
            },
            {
                "techniqueID": "T1049",
                "tactic": "discovery",
                "color": "#ff6666",
                "comment": "Low coverage"
            }
        ]
    }


def test_generate_execution_summary_report(mock_db_session):
    """Test generating an execution summary report."""
    with patch('api.services.execution_framework.reporting._generate_xlsx_report') as mock_generate_xlsx:
        # Mock the report generation function
        mock_output = io.BytesIO(b"test xlsx data")
        mock_filename = "execution_summary_report.xlsx"
        mock_generate_xlsx.return_value = (mock_output, mock_filename)
        
        # Call the function
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        
        output, filename = reporting.generate_execution_summary_report(
            db=mock_db_session,
            start_date=start_date,
            end_date=end_date,
            campaign_id=1,
            format="xlsx"
        )
        
        # Check the result
        assert output == mock_output
        assert filename == mock_filename
        
        # Verify the query was called with correct parameters
        mock_db_session.query.assert_called_with(ExecutionLog)
        mock_db_session.query().filter.assert_called()


def test_generate_mitre_coverage_report(mock_db_session):
    """Test generating a MITRE coverage report."""
    with patch('api.services.execution_framework.reporting._generate_xlsx_report') as mock_generate_xlsx, \
         patch('api.services.execution_framework.mitre_navigator.generate_layer_json') as mock_generate_layer:
        
        # Mock the report generation function
        mock_output = io.BytesIO(b"test xlsx data")
        mock_filename = "mitre_coverage_report.xlsx"
        mock_generate_xlsx.return_value = (mock_output, mock_filename)
        
        # Mock the layer generation function
        mock_generate_layer.return_value = {"name": "Test Layer", "techniques": []}
        
        # Call the function
        output, filename = reporting.generate_mitre_coverage_report(
            db=mock_db_session,
            test_case_id=1,
            format="xlsx"
        )
        
        # Check the result
        assert output == mock_output
        assert filename == mock_filename
        
        # Verify the query was called with correct parameters
        mock_db_session.query.assert_called()
        mock_generate_layer.assert_called()


def test_generate_mitre_coverage_visualization_report(mock_db_session, sample_layer_data):
    """Test generating a MITRE coverage visualization report."""
    with patch('api.services.execution_framework.reporting._generate_mitre_coverage_pdf_report') as mock_generate_pdf, \
         patch('api.services.execution_framework.mitre_navigator.generate_layer_json') as mock_generate_layer:
        
        # Mock the report generation function
        mock_output = io.BytesIO(b"test pdf data")
        mock_filename = "mitre_coverage_visualization_report.pdf"
        mock_generate_pdf.return_value = (mock_output, mock_filename)
        
        # Mock the layer generation function
        mock_generate_layer.return_value = sample_layer_data
        
        # Call the function
        output, filename = reporting.generate_mitre_coverage_visualization_report(
            db=mock_db_session,
            campaign_id=1,
            format="pdf"
        )
        
        # Check the result
        assert output == mock_output
        assert filename == mock_filename
        
        # Verify the functions were called with correct parameters
        mock_db_session.query.assert_called()
        mock_generate_layer.assert_called()
        mock_generate_pdf.assert_called()


def test_generate_analytics_report(mock_db_session):
    """Test generating an analytics report."""
    with patch('api.services.execution_framework.reporting._generate_xlsx_report') as mock_generate_xlsx:
        # Mock the report generation function
        mock_output = io.BytesIO(b"test xlsx data")
        mock_filename = "analytics_report.xlsx"
        mock_generate_xlsx.return_value = (mock_output, mock_filename)
        
        # Call the function
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        
        output, filename = reporting.generate_analytics_report(
            db=mock_db_session,
            start_date=start_date,
            end_date=end_date,
            test_case_id=1,
            format="xlsx"
        )
        
        # Check the result
        assert output == mock_output
        assert filename == mock_filename
        
        # Verify the query was called with correct parameters
        mock_db_session.query.assert_called_with(ExecutionMetric)
        mock_db_session.query().filter.assert_called()


def test_generate_layer_visualization(sample_layer_data):
    """Test generating a layer visualization."""
    with patch('tempfile.TemporaryDirectory') as mock_temp_dir, \
         patch('json.dump') as mock_json_dump, \
         patch('os.path.join', return_value='/tmp/test/visualization.png'), \
         patch('api.services.execution_framework.reporting.Image') as mock_image, \
         patch('api.services.execution_framework.reporting.ImageDraw') as mock_image_draw, \
         patch('api.services.execution_framework.reporting.ImageFont') as mock_image_font:
        
        # Mock the temporary directory
        mock_temp_dir.return_value.__enter__.return_value = '/tmp/test'
        
        # Mock the PIL Image
        mock_img = MagicMock()
        mock_image.new.return_value = mock_img
        
        # Mock the PIL ImageDraw
        mock_draw = MagicMock()
        mock_image_draw.Draw.return_value = mock_draw
        
        # Mock the PIL ImageFont
        mock_font = MagicMock()
        mock_image_font.truetype.return_value = mock_font
        
        # Call the function
        result = reporting._generate_layer_visualization(sample_layer_data)
        
        # Check the result
        assert result == '/tmp/test/visualization.png'
        
        # Verify the functions were called with correct parameters
        mock_temp_dir.assert_called_once()
        mock_json_dump.assert_called_once()
        mock_image.new.assert_called_once()
        mock_image_draw.Draw.assert_called_once_with(mock_img)
        mock_img.save.assert_called_once()


def test_generate_mitre_coverage_pdf_report(sample_layer_data):
    """Test generating a MITRE coverage PDF report."""
    with patch('api.services.execution_framework.reporting._generate_layer_visualization') as mock_generate_viz, \
         patch('api.services.execution_framework.reporting.SimpleDocTemplate') as mock_simple_doc, \
         patch('api.services.execution_framework.reporting.getSampleStyleSheet') as mock_get_styles, \
         patch('api.services.execution_framework.reporting.Table') as mock_table, \
         patch('api.services.execution_framework.reporting.TableStyle') as mock_table_style, \
         patch('api.services.execution_framework.reporting.Paragraph') as mock_paragraph, \
         patch('api.services.execution_framework.reporting.Spacer') as mock_spacer, \
         patch('api.services.execution_framework.reporting.RLImage') as mock_rl_image:
        
        # Mock the visualization generation function
        mock_generate_viz.return_value = '/tmp/test/visualization.png'
        
        # Mock the ReportLab components
        mock_doc = MagicMock()
        mock_simple_doc.return_value = mock_doc
        
        mock_styles = {
            'Title': MagicMock(),
            'Heading1': MagicMock(),
            'Normal': MagicMock()
        }
        mock_get_styles.return_value = mock_styles
        
        # Create test data
        summary_data = {
            'report_title': 'Test MITRE Coverage Report',
            'generated_at': datetime.now().isoformat(),
            'total_techniques': 3,
            'covered_techniques': 2,
            'coverage_percentage': 66.67,
            'tactics_covered': 2
        }
        
        technique_data = [
            {
                'technique_id': 'T1059.001',
                'tactic': 'execution',
                'color': '#8ec843',
                'comment': 'High coverage'
            },
            {
                'technique_id': 'T1057',
                'tactic': 'discovery',
                'color': '#ffe766',
                'comment': 'Medium coverage'
            },
            {
                'technique_id': 'T1049',
                'tactic': 'discovery',
                'color': '#ff6666',
                'comment': 'Low coverage'
            }
        ]
        
        # Call the function
        output, filename = reporting._generate_mitre_coverage_pdf_report(
            summary_data=summary_data,
            technique_data=technique_data,
            layer_data=sample_layer_data
        )
        
        # Check the result
        assert isinstance(output, io.BytesIO)
        assert "mitre_coverage_report_" in filename
        assert filename.endswith(".pdf")
        
        # Verify the functions were called with correct parameters
        mock_generate_viz.assert_called_once_with(sample_layer_data)
        mock_simple_doc.assert_called_once()
        mock_get_styles.assert_called_once()
        mock_doc.build.assert_called_once()


def test_generate_xlsx_report():
    """Test generating an Excel report."""
    # Create test data
    data = [
        {"id": 1, "name": "Test 1", "value": 100},
        {"id": 2, "name": "Test 2", "value": 200}
    ]
    
    # Call the function
    output, filename = reporting._generate_xlsx_report(data, "test_report")
    
    # Check the result
    assert isinstance(output, io.BytesIO)
    assert "test_report_" in filename
    assert filename.endswith(".xlsx")
    
    # Verify the output contains Excel data
    output.seek(0)
    assert output.read(4) == b"PK\x03\x04"  # Excel files start with this signature


def test_generate_csv_report():
    """Test generating a CSV report."""
    # Create test data
    data = [
        {"id": 1, "name": "Test 1", "value": 100},
        {"id": 2, "name": "Test 2", "value": 200}
    ]
    
    # Call the function
    output, filename = reporting._generate_csv_report(data, "test_report")
    
    # Check the result
    assert isinstance(output, io.BytesIO)
    assert "test_report_" in filename
    assert filename.endswith(".csv")
    
    # Verify the output contains CSV data
    output.seek(0)
    content = output.read().decode('utf-8')
    assert "id,name,value" in content
    assert "1,Test 1,100" in content
    assert "2,Test 2,200" in content


def test_generate_json_report():
    """Test generating a JSON report."""
    # Create test data
    data = [
        {"id": 1, "name": "Test 1", "value": 100},
        {"id": 2, "name": "Test 2", "value": 200}
    ]
    
    # Call the function
    output, filename = reporting._generate_json_report(data, "test_report")
    
    # Check the result
    assert isinstance(output, io.BytesIO)
    assert "test_report_" in filename
    assert filename.endswith(".json")
    
    # Verify the output contains JSON data
    output.seek(0)
    content = json.loads(output.read().decode('utf-8'))
    assert len(content) == 2
    assert content[0]["id"] == 1
    assert content[0]["name"] == "Test 1"
    assert content[0]["value"] == 100
    assert content[1]["id"] == 2
    assert content[1]["name"] == "Test 2"
    assert content[1]["value"] == 200
