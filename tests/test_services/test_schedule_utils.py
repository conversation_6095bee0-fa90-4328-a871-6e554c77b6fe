"""Tests for the schedule utilities.

This module contains tests for the schedule utilities, including
calculating next run times based on different frequencies.
"""
import pytest
from datetime import datetime, timedelta

from api.services.schedule_utils import calculate_next_run_time


def test_calculate_next_run_time_once():
    """Test calculating next run time for once frequency."""
    start_date = datetime(2023, 1, 1, 12, 0)
    
    # For once frequency, the next run time should be the start date
    next_run = calculate_next_run_time(
        frequency="once",
        start_date=start_date,
    )
    
    assert next_run == start_date


def test_calculate_next_run_time_daily():
    """Test calculating next run time for daily frequency."""
    from_dt = datetime(2023, 1, 1, 12, 0)
    
    # For daily frequency, the next run time should be the next occurrence of the time of day
    next_run = calculate_next_run_time(
        frequency="daily",
        start_date=from_dt,
        time_of_day="08:00",
        from_dt=from_dt,
    )
    
    # Since 8am is before 12pm, the next run should be tomorrow at 8am
    expected = datetime(2023, 1, 2, 8, 0)
    assert next_run == expected
    
    # Test when the time of day is after the current time
    next_run = calculate_next_run_time(
        frequency="daily",
        start_date=from_dt,
        time_of_day="14:00",
        from_dt=from_dt,
    )
    
    # Since 2pm is after 12pm, the next run should be today at 2pm
    expected = datetime(2023, 1, 1, 14, 0)
    assert next_run == expected


def test_calculate_next_run_time_weekly():
    """Test calculating next run time for weekly frequency."""
    from_dt = datetime(2023, 1, 1, 12, 0)  # Sunday
    
    # For weekly frequency, the next run time should be the next occurrence of the day of week
    next_run = calculate_next_run_time(
        frequency="weekly",
        start_date=from_dt,
        day_of_week=1,  # Monday
        time_of_day="08:00",
        from_dt=from_dt,
    )
    
    # Since Monday is after Sunday, the next run should be Monday at 8am
    expected = datetime(2023, 1, 2, 8, 0)
    assert next_run == expected
    
    # Test when the day of week is the same as the current day
    next_run = calculate_next_run_time(
        frequency="weekly",
        start_date=from_dt,
        day_of_week=6,  # Sunday
        time_of_day="14:00",
        from_dt=from_dt,
    )
    
    # Since it's Sunday and 2pm is after 12pm, the next run should be today at 2pm
    expected = datetime(2023, 1, 1, 14, 0)
    assert next_run == expected
    
    # Test when the day of week is the same as the current day but the time has passed
    next_run = calculate_next_run_time(
        frequency="weekly",
        start_date=from_dt,
        day_of_week=6,  # Sunday
        time_of_day="08:00",
        from_dt=from_dt,
    )
    
    # Since it's Sunday and 8am is before 12pm, the next run should be next Sunday at 8am
    expected = datetime(2023, 1, 8, 8, 0)
    assert next_run == expected


def test_calculate_next_run_time_monthly():
    """Test calculating next run time for monthly frequency."""
    from_dt = datetime(2023, 1, 15, 12, 0)
    
    # For monthly frequency, the next run time should be the next occurrence of the day of month
    next_run = calculate_next_run_time(
        frequency="monthly",
        start_date=from_dt,
        day_of_month=1,
        time_of_day="08:00",
        from_dt=from_dt,
    )
    
    # Since the 1st is before the 15th, the next run should be the 1st of next month
    expected = datetime(2023, 2, 1, 8, 0)
    assert next_run == expected
    
    # Test when the day of month is after the current day
    next_run = calculate_next_run_time(
        frequency="monthly",
        start_date=from_dt,
        day_of_month=20,
        time_of_day="08:00",
        from_dt=from_dt,
    )
    
    # Since the 20th is after the 15th, the next run should be the 20th of this month
    expected = datetime(2023, 1, 20, 8, 0)
    assert next_run == expected
    
    # Test with a day that doesn't exist in some months (e.g., 31st)
    from_dt = datetime(2023, 1, 31, 12, 0)
    next_run = calculate_next_run_time(
        frequency="monthly",
        start_date=from_dt,
        day_of_month=31,
        time_of_day="08:00",
        from_dt=from_dt,
    )
    
    # Since February doesn't have 31 days, the next run should be the 28th of February
    expected = datetime(2023, 2, 28, 8, 0)
    assert next_run == expected


def test_calculate_next_run_time_quarterly():
    """Test calculating next run time for quarterly frequency."""
    from_dt = datetime(2023, 1, 15, 12, 0)
    
    # For quarterly frequency, the next run time should be the next quarter
    next_run = calculate_next_run_time(
        frequency="quarterly",
        start_date=from_dt,
        day_of_month=1,
        time_of_day="08:00",
        from_dt=from_dt,
    )
    
    # The next quarter after January is April
    expected = datetime(2023, 4, 1, 8, 0)
    assert next_run == expected
    
    # Test from a different quarter
    from_dt = datetime(2023, 7, 15, 12, 0)
    next_run = calculate_next_run_time(
        frequency="quarterly",
        start_date=from_dt,
        day_of_month=1,
        time_of_day="08:00",
        from_dt=from_dt,
    )
    
    # The next quarter after July is October
    expected = datetime(2023, 10, 1, 8, 0)
    assert next_run == expected


def test_calculate_next_run_time_cron():
    """Test calculating next run time for cron frequency."""
    from_dt = datetime(2023, 1, 1, 12, 0)
    
    # For cron frequency, the next run time should be calculated based on the cron expression
    next_run = calculate_next_run_time(
        frequency="cron",
        start_date=from_dt,
        cron_expression="0 0 * * *",  # Daily at midnight
        from_dt=from_dt,
    )
    
    # The next run should be tomorrow at midnight
    expected = datetime(2023, 1, 2, 0, 0)
    assert next_run == expected
    
    # Test with a more complex cron expression
    next_run = calculate_next_run_time(
        frequency="cron",
        start_date=from_dt,
        cron_expression="0 9 * * 1-5",  # Weekdays at 9am
        from_dt=from_dt,
    )
    
    # January 1, 2023 is a Sunday, so the next run should be Monday at 9am
    expected = datetime(2023, 1, 2, 9, 0)
    assert next_run == expected
