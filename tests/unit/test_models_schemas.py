"""Unit tests for the models.schemas module."""
import pytest
from datetime import datetime
from pydantic import ValidationError
from api.models.schemas import (
    UserBase, UserCreate, UserResponse, 
    TechniqueScoreCreate, TechniqueScoreResponse
)


def test_user_base_model():
    """Test the UserBase model validation."""
    # Valid data
    user_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "full_name": "Test User",
        "bio": "This is a test user"
    }
    user = UserBase(**user_data)
    assert user.username == "testuser"
    assert user.email == "<EMAIL>"
    assert user.full_name == "Test User"
    assert user.bio == "This is a test user"
    
    # Invalid email
    with pytest.raises(ValidationError):
        UserBase(username="testuser", email="invalid-email")
    
    # Missing required field
    with pytest.raises(ValidationError):
        UserBase(email="<EMAIL>")


def test_user_create_model():
    """Test the UserCreate model validation."""
    # Valid data
    user_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "securepassword",
        "full_name": "Test User"
    }
    user = UserCreate(**user_data)
    assert user.username == "testuser"
    assert user.email == "<EMAIL>"
    assert user.password == "securepassword"
    assert user.full_name == "Test User"
    
    # Password too short
    with pytest.raises(ValidationError):
        UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="short"
        )


def test_user_response_model():
    """Test the UserResponse model."""
    # Valid data
    user_data = {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "full_name": "Test User",
        "role": "user",
        "is_active": True,
        "created_at": datetime.now(),
        "last_login": datetime.now(),
        "two_factor_enabled": False
    }
    user = UserResponse(**user_data)
    assert user.id == 1
    assert user.username == "testuser"
    assert user.email == "<EMAIL>"
    assert user.role == "user"
    assert user.is_active is True
    assert user.two_factor_enabled is False


def test_technique_score_create_model():
    """Test the TechniqueScoreCreate model validation."""
    # Valid data
    score_data = {
        "technique_id": 1,
        "category": "impact",
        "score": 7.5,
        "weight": 0.8,
        "notes": "High impact score due to potential data loss"
    }
    score = TechniqueScoreCreate(**score_data)
    assert score.technique_id == 1
    assert score.category == "impact"
    assert score.score == 7.5
    assert score.weight == 0.8
    assert score.notes == "High impact score due to potential data loss"
    
    # Score out of range
    with pytest.raises(ValueError):
        TechniqueScoreCreate(
            technique_id=1,
            category="impact",
            score=11,
            weight=0.8
        )
    
    # Weight out of range
    with pytest.raises(ValueError):
        TechniqueScoreCreate(
            technique_id=1,
            category="impact",
            score=7,
            weight=1.5
        )


def test_technique_score_response_model():
    """Test the TechniqueScoreResponse model."""
    # Valid data
    score_data = {
        "id": 1,
        "technique_id": 1,
        "category": "impact",
        "score": 7.5,
        "weight": 0.8,
        "notes": "High impact score due to potential data loss",
        "created_by": 1,
        "created_at": datetime.now(),
        "updated_at": datetime.now()
    }
    score = TechniqueScoreResponse(**score_data)
    assert score.id == 1
    assert score.technique_id == 1
    assert score.category == "impact"
    assert score.score == 7.5
    assert score.weight == 0.8
    assert score.notes == "High impact score due to potential data loss"
    assert score.created_by == 1
