"""Unit tests for the config module."""
import os
import pytest
from api.config import Settings


def test_settings_default_values():
    """Test that Setting<PERSON> has the expected default values."""
    settings = Settings()
    assert settings.API_V1_STR == '/api/v1'
    assert settings.PROJECT_NAME == 'RegrigorRigor'
    assert settings.SECRET_KEY == os.getenv('SECRET_KEY', 'development_secret_key')
    assert settings.ACCESS_TOKEN_EXPIRE_MINUTES == int(os.getenv(
        'ACCESS_TOKEN_EXPIRE_MINUTES', '30'))
    assert settings.JWT_ALGORITHM == os.getenv('JWT_ALGORITHM', 'HS256')
    assert settings.ALGORITHM == os.getenv('ALGORITHM', 'HS256')


def test_settings_cors_origins():
    """Test that CORS_ORIGINS is properly configured."""
    settings = Settings()
    assert isinstance(settings.CORS_ORIGINS, list)
    assert 'http://localhost' in settings.CORS_ORIGINS
    assert 'http://localhost:3000' in settings.CORS_ORIGINS
    assert 'http://localhost:8000' in settings.CORS_ORIGINS
    assert 'http://localhost:8080' in settings.CORS_ORIGINS


def test_settings_env_override():
    """Test that environment variables override default settings."""
    # Save original environment variables
    original_secret_key = os.environ.get('SECRET_KEY')
    original_project_name = os.environ.get('PROJECT_NAME')
    
    try:
        # Set environment variables for testing
        os.environ['SECRET_KEY'] = 'test_secret_key'
        os.environ['PROJECT_NAME'] = 'TestProject'
        
        # Create settings with new environment variables
        settings = Settings()
        
        # Check that environment variables were used
        assert settings.SECRET_KEY == 'test_secret_key'
        assert settings.PROJECT_NAME == 'TestProject'
    finally:
        # Restore original environment variables
        if original_secret_key:
            os.environ['SECRET_KEY'] = original_secret_key
        else:
            os.environ.pop('SECRET_KEY', None)
            
        if original_project_name:
            os.environ['PROJECT_NAME'] = original_project_name
        else:
            os.environ.pop('PROJECT_NAME', None)


def test_settings_config_class():
    """Test the Config inner class of Settings."""
    assert Settings.Config.env_file == '.env'
    assert Settings.Config.case_sensitive is True
