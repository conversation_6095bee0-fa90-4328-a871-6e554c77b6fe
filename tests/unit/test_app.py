"""
Unit tests for the main FastAPI application.
"""

import pytest
import json
from fastapi import status

def test_root_endpoint(client):
    """Test the root endpoint returns 200 status code."""
    response = client.get("/")
    assert response.status_code == status.HTTP_200_OK
    assert "Welcome" in response.text

def test_health_endpoint(client):
    """Test the health endpoint returns correct response."""
    response = client.get("/api/v1/health")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["status"] == "healthy"

def test_api_docs_endpoint(client):
    """Test the API documentation endpoint."""
    response = client.get("/docs")
    assert response.status_code == status.HTTP_200_OK
    assert "swagger" in response.text.lower()

def test_openapi_schema(client):
    """Test the OpenAPI schema endpoint."""
    response = client.get("/openapi.json")
    assert response.status_code == status.HTTP_200_OK
    schema = response.json()
    assert "openapi" in schema
    assert "paths" in schema
    assert "components" in schema

def test_404_error(client):
    """Test that a 404 error is handled correctly."""
    response = client.get("/nonexistent-route")
    assert response.status_code == status.HTTP_404_NOT_FOUND
