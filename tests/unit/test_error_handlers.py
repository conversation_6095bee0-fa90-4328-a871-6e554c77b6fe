"""
Unit tests for error handlers in FastAPI application.
"""

import pytest
from fastapi import status, HTTPException
from sqlalchemy.exc import SQLAlchemyError

def test_404_error_handler(client):
    """Test the 404 error handler."""
    response = client.get('/nonexistent-route')
    assert response.status_code == status.HTTP_404_NOT_FOUND
    data = response.json()
    assert "detail" in data
    assert "not found" in data["detail"].lower()

def test_validation_error(client):
    """Test validation error handling."""
    # Send invalid data to an endpoint that requires validation
    invalid_data = {
        "username": "",  # Empty username should fail validation
        "password": "short"  # Too short password should fail validation
    }

    response = client.post("/api/v1/auth/register", json=invalid_data)
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    data = response.json()
    assert "detail" in data
    assert isinstance(data["detail"], list)  # Validation errors are returned as a list

def test_unauthorized_error(client):
    """Test unauthorized error handling."""
    # Try to access a protected endpoint without authentication
    response = client.get("/api/v1/users/me")
    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    data = response.json()
    assert "detail" in data
    assert "not authenticated" in data["detail"].lower()

def test_forbidden_error(client, test_user_token):
    """Test forbidden error handling."""
    # Try to access an admin-only endpoint with a regular user token
    headers = {"Authorization": f"Bearer {test_user_token}"}
    response = client.get("/api/v1/admin/users", headers=headers)
    assert response.status_code == status.HTTP_403_FORBIDDEN
    data = response.json()
    assert "detail" in data
    assert "permission" in data["detail"].lower() or "forbidden" in data["detail"].lower()

def test_database_error(client, test_app, test_user_token, monkeypatch):
    """Test database error handling."""
    # This test requires monkeypatching a dependency to simulate a database error
    from api.database import get_db

    # Create a route that will raise a database error
    @test_app.get("/api/v1/test/db-error")
    async def db_error_route():
        raise SQLAlchemyError("Test database error")

    headers = {"Authorization": f"Bearer {test_user_token}"}
    response = client.get("/api/v1/test/db-error", headers=headers)
    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    data = response.json()
    assert "detail" in data
    assert "database" in data["detail"].lower() or "internal server error" in data["detail"].lower()

def test_internal_server_error(client, test_app, test_user_token):
    """Test general exception handling."""
    # Create a route that will raise a general exception
    @test_app.get("/api/v1/test/general-error")
    async def general_error_route():
        raise Exception("Test general error")

    headers = {"Authorization": f"Bearer {test_user_token}"}
    response = client.get("/api/v1/test/general-error", headers=headers)
    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    data = response.json()
    assert "detail" in data
