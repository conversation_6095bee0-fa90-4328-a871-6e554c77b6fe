"""Unit tests for database models."""
import pytest
from datetime import datetime, timedelta
from sqlalchemy.exc import IntegrityError

from api.models.user import User
from api.models.campaign import Campaign
from api.models.testcase import TestCase
from api.models.technique import Technique
from api.models.score import TechniqueScore
from api.database import db


@pytest.fixture
def db_session():
    """Create a database session for testing."""
    # Create all tables
    db.create_all()
    
    # Create a session
    session = db.session
    
    yield session
    
    # Rollback any changes
    session.rollback()
    
    # Drop all tables
    db.drop_all()


class TestUserModel:
    """Tests for the User model."""
    
    def test_create_user(self, db_session):
        """Test creating a user."""
        user = User(
            username="testuser",
            email="<EMAIL>",
            full_name="Test User",
            role="user"
        )
        user.set_password("password123")
        
        db_session.add(user)
        db_session.commit()
        
        # Retrieve the user from the database
        retrieved_user = db_session.query(User).filter_by(username="testuser").first()
        
        assert retrieved_user is not None
        assert retrieved_user.username == "testuser"
        assert retrieved_user.email == "<EMAIL>"
        assert retrieved_user.full_name == "Test User"
        assert retrieved_user.role == "user"
        assert retrieved_user.is_active is True
        assert retrieved_user.check_password("password123") is True
        assert retrieved_user.check_password("wrongpassword") is False
    
    def test_unique_username_constraint(self, db_session):
        """Test that usernames must be unique."""
        # Create first user
        user1 = User(
            username="testuser",
            email="<EMAIL>",
            full_name="Test User 1",
            role="user"
        )
        user1.set_password("password123")
        db_session.add(user1)
        db_session.commit()
        
        # Try to create second user with same username
        user2 = User(
            username="testuser",
            email="<EMAIL>",
            full_name="Test User 2",
            role="user"
        )
        user2.set_password("password456")
        db_session.add(user2)
        
        # Should raise IntegrityError
        with pytest.raises(IntegrityError):
            db_session.commit()
        
        db_session.rollback()
    
    def test_unique_email_constraint(self, db_session):
        """Test that emails must be unique."""
        # Create first user
        user1 = User(
            username="testuser1",
            email="<EMAIL>",
            full_name="Test User 1",
            role="user"
        )
        user1.set_password("password123")
        db_session.add(user1)
        db_session.commit()
        
        # Try to create second user with same email
        user2 = User(
            username="testuser2",
            email="<EMAIL>",
            full_name="Test User 2",
            role="user"
        )
        user2.set_password("password456")
        db_session.add(user2)
        
        # Should raise IntegrityError
        with pytest.raises(IntegrityError):
            db_session.commit()
        
        db_session.rollback()
    
    def test_user_relationships(self, db_session):
        """Test user relationships with other models."""
        # Create a user
        user = User(
            username="testuser",
            email="<EMAIL>",
            full_name="Test User",
            role="user"
        )
        user.set_password("password123")
        db_session.add(user)
        db_session.commit()
        
        # Create a campaign owned by the user
        campaign = Campaign(
            name="Test Campaign",
            description="Test campaign description",
            created_by=user.id,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db_session.add(campaign)
        db_session.commit()
        
        # Create a technique score by the user
        technique = Technique(
            name="Test Technique",
            description="Test technique description",
            mitre_id="T1234",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db_session.add(technique)
        db_session.commit()
        
        score = TechniqueScore(
            technique_id=technique.id,
            category="impact",
            score=7.5,
            weight=0.8,
            notes="Test score notes",
            created_by=user.id,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db_session.add(score)
        db_session.commit()
        
        # Retrieve the user with relationships
        retrieved_user = db_session.query(User).filter_by(username="testuser").first()
        
        # Check relationships
        assert len(retrieved_user.campaigns) == 1
        assert retrieved_user.campaigns[0].name == "Test Campaign"
        assert len(retrieved_user.technique_scores) == 1
        assert retrieved_user.technique_scores[0].score == 7.5


class TestCampaignModel:
    """Tests for the Campaign model."""
    
    def test_create_campaign(self, db_session):
        """Test creating a campaign."""
        # Create a user first
        user = User(
            username="testuser",
            email="<EMAIL>",
            full_name="Test User",
            role="user"
        )
        db_session.add(user)
        db_session.commit()
        
        # Create a campaign
        campaign = Campaign(
            name="Test Campaign",
            description="Test campaign description",
            created_by=user.id,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db_session.add(campaign)
        db_session.commit()
        
        # Retrieve the campaign from the database
        retrieved_campaign = db_session.query(Campaign).filter_by(name="Test Campaign").first()
        
        assert retrieved_campaign is not None
        assert retrieved_campaign.name == "Test Campaign"
        assert retrieved_campaign.description == "Test campaign description"
        assert retrieved_campaign.created_by == user.id
        assert retrieved_campaign.created_at is not None
        assert retrieved_campaign.updated_at is not None
    
    def test_campaign_test_cases_relationship(self, db_session):
        """Test campaign relationship with test cases."""
        # Create a user
        user = User(
            username="testuser",
            email="<EMAIL>",
            full_name="Test User",
            role="user"
        )
        db_session.add(user)
        db_session.commit()
        
        # Create a campaign
        campaign = Campaign(
            name="Test Campaign",
            description="Test campaign description",
            created_by=user.id,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db_session.add(campaign)
        db_session.commit()
        
        # Create test cases for the campaign
        for i in range(3):
            test_case = TestCase(
                name=f"Test Case {i+1}",
                description=f"Test case {i+1} description",
                campaign_id=campaign.id,
                expected_result="Pass",
                status="pending",
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            db_session.add(test_case)
        db_session.commit()
        
        # Retrieve the campaign with test cases
        retrieved_campaign = db_session.query(Campaign).filter_by(name="Test Campaign").first()
        
        # Check relationship
        assert len(retrieved_campaign.test_cases) == 3
        assert retrieved_campaign.test_cases[0].name == "Test Case 1"
        assert retrieved_campaign.test_cases[1].name == "Test Case 2"
        assert retrieved_campaign.test_cases[2].name == "Test Case 3"


class TestTestCaseModel:
    """Tests for the TestCase model."""
    
    def test_create_test_case(self, db_session):
        """Test creating a test case."""
        # Create a user
        user = User(
            username="testuser",
            email="<EMAIL>",
            full_name="Test User",
            role="user"
        )
        db_session.add(user)
        db_session.commit()
        
        # Create a campaign
        campaign = Campaign(
            name="Test Campaign",
            description="Test campaign description",
            created_by=user.id,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db_session.add(campaign)
        db_session.commit()
        
        # Create a test case
        test_case = TestCase(
            name="Test Case",
            description="Test case description",
            campaign_id=campaign.id,
            expected_result="Pass",
            status="pending",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db_session.add(test_case)
        db_session.commit()
        
        # Retrieve the test case from the database
        retrieved_test_case = db_session.query(TestCase).filter_by(name="Test Case").first()
        
        assert retrieved_test_case is not None
        assert retrieved_test_case.name == "Test Case"
        assert retrieved_test_case.description == "Test case description"
        assert retrieved_test_case.campaign_id == campaign.id
        assert retrieved_test_case.expected_result == "Pass"
        assert retrieved_test_case.status == "pending"
        assert retrieved_test_case.created_at is not None
        assert retrieved_test_case.updated_at is not None
    
    def test_test_case_status_update(self, db_session):
        """Test updating test case status."""
        # Create a user
        user = User(
            username="testuser",
            email="<EMAIL>",
            full_name="Test User",
            role="user"
        )
        db_session.add(user)
        db_session.commit()
        
        # Create a campaign
        campaign = Campaign(
            name="Test Campaign",
            description="Test campaign description",
            created_by=user.id,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db_session.add(campaign)
        db_session.commit()
        
        # Create a test case
        test_case = TestCase(
            name="Test Case",
            description="Test case description",
            campaign_id=campaign.id,
            expected_result="Pass",
            status="pending",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db_session.add(test_case)
        db_session.commit()
        
        # Update test case status
        test_case.status = "running"
        test_case.updated_at = datetime.now()
        db_session.commit()
        
        # Retrieve the updated test case
        retrieved_test_case = db_session.query(TestCase).filter_by(name="Test Case").first()
        assert retrieved_test_case.status == "running"
        
        # Update test case status again
        test_case.status = "completed"
        test_case.result = "pass"
        test_case.updated_at = datetime.now()
        db_session.commit()
        
        # Retrieve the updated test case again
        retrieved_test_case = db_session.query(TestCase).filter_by(name="Test Case").first()
        assert retrieved_test_case.status == "completed"
        assert retrieved_test_case.result == "pass"
