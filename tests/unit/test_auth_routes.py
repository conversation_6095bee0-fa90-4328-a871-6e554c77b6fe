"""Unit tests for the auth routes module."""
import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, patch
from flask import Flask, session
from api.routes.auth import auth_bp, login_manager


@pytest.fixture
def app():
    """Create a Flask app for testing."""
    app = Flask(__name__)
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False
    app.config['SECRET_KEY'] = 'test-key'
    
    # Register the auth blueprint
    app.register_blueprint(auth_bp)
    
    # Initialize login manager
    login_manager.init_app(app)
    
    return app


@pytest.fixture
def client(app):
    """Create a test client for the app."""
    return app.test_client()


@patch('api.routes.auth.User')
def test_login_get(mock_user, client):
    """Test the login route with GET method."""
    response = client.get('/auth/login')
    assert response.status_code == 200
    assert b'login.html' in response.data


@patch('api.routes.auth.User')
@patch('api.routes.auth.LoginForm')
def test_login_post_success(mock_form, mock_user, client):
    """Test successful login with POST method."""
    # Mock the form validation
    form_instance = mock_form.return_value
    form_instance.validate_on_submit.return_value = True
    form_instance.username.data = 'testuser'
    form_instance.password.data = 'password123'
    
    # Mock the user query
    user_instance = MagicMock()
    user_instance.username = 'testuser'
    user_instance.check_password.return_value = True
    
    mock_user.query.filter_by.return_value.first.return_value = user_instance
    
    # Mock login_user function
    with patch('api.routes.auth.login_user') as mock_login:
        response = client.post('/auth/login', data={
            'username': 'testuser',
            'password': 'password123'
        })
        
        # Check that login_user was called
        mock_login.assert_called_once_with(user_instance)
        
        # Check the response
        assert response.status_code == 302  # Redirect


@patch('api.routes.auth.User')
@patch('api.routes.auth.LoginForm')
def test_login_post_invalid_credentials(mock_form, mock_user, client):
    """Test login with invalid credentials."""
    # Mock the form validation
    form_instance = mock_form.return_value
    form_instance.validate_on_submit.return_value = True
    form_instance.username.data = 'testuser'
    form_instance.password.data = 'wrongpassword'
    
    # Mock the user query
    user_instance = MagicMock()
    user_instance.check_password.return_value = False
    
    mock_user.query.filter_by.return_value.first.return_value = user_instance
    
    # Mock login_user function
    with patch('api.routes.auth.login_user') as mock_login:
        response = client.post('/auth/login', data={
            'username': 'testuser',
            'password': 'wrongpassword'
        })
        
        # Check that login_user was not called
        mock_login.assert_not_called()
        
        # Check the response
        assert response.status_code == 200  # Return to login page


@patch('api.routes.auth.User')
@patch('api.routes.auth.LoginForm')
def test_login_post_user_not_found(mock_form, mock_user, client):
    """Test login with non-existent user."""
    # Mock the form validation
    form_instance = mock_form.return_value
    form_instance.validate_on_submit.return_value = True
    form_instance.username.data = 'nonexistent'
    form_instance.password.data = 'password123'
    
    # Mock the user query to return None
    mock_user.query.filter_by.return_value.first.return_value = None
    
    # Mock login_user function
    with patch('api.routes.auth.login_user') as mock_login:
        response = client.post('/auth/login', data={
            'username': 'nonexistent',
            'password': 'password123'
        })
        
        # Check that login_user was not called
        mock_login.assert_not_called()
        
        # Check the response
        assert response.status_code == 200  # Return to login page


@patch('api.routes.auth.logout_user')
def test_logout(mock_logout, client):
    """Test the logout route."""
    # Mock login_required decorator
    with patch('api.routes.auth.login_required', lambda x: x):
        response = client.get('/auth/logout')
        
        # Check that logout_user was called
        mock_logout.assert_called_once()
        
        # Check the response
        assert response.status_code == 302  # Redirect to login page


@patch('api.routes.auth.User')
def test_load_user_success(mock_user, app):
    """Test the load_user function with valid user ID."""
    # Mock the user query
    user_instance = MagicMock()
    user_instance.id = 1
    mock_user.query.get.return_value = user_instance
    
    # Call the load_user function
    with app.app_context():
        result = login_manager.user_loader_callback('1')
        
        # Check that the user was returned
        assert result == user_instance
        mock_user.query.get.assert_called_once_with(1)


@patch('api.routes.auth.User')
def test_load_user_not_found(mock_user, app):
    """Test the load_user function with invalid user ID."""
    # Mock the user query to return None
    mock_user.query.get.return_value = None
    
    # Call the load_user function
    with app.app_context():
        result = login_manager.user_loader_callback('999')
        
        # Check that None was returned
        assert result is None
        mock_user.query.get.assert_called_once_with(999)


@patch('api.routes.auth.User')
def test_load_user_exception(mock_user, app):
    """Test the load_user function when an exception occurs."""
    # Mock the user query to raise an exception
    mock_user.query.get.side_effect = Exception("Database error")
    
    # Mock the log_error function
    with patch('api.routes.auth.log_error') as mock_log_error:
        # Call the load_user function
        with app.app_context():
            result = login_manager.user_loader_callback('1')
            
            # Check that None was returned
            assert result is None
            
            # Check that log_error was called
            mock_log_error.assert_called_once()
