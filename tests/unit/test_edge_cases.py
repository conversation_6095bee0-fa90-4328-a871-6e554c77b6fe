"""Unit tests for edge cases and error handling."""
import pytest
from unittest.mock import MagicMock, patch
from datetime import datetime, timedelta
import json
import os

from api.models.user import User
from api.models.campaign import Campaign
from api.models.testcase import TestCase
from api.services.auth import AuthService
from api.services.campaign import CampaignService
from api.services.testcase import TestCaseService
from api.utils.caching import cached, memoize
from api.utils.query_optimization import batch_load
from api.utils.computational_optimization import parallelize, vectorize
from api.database import db
from api.exceptions import (
    AuthenticationError,
    AuthorizationError,
    ResourceNotFoundError,
    ValidationError,
    RateLimitExceededError,
)


class TestAuthenticationEdgeCases:
    """Tests for authentication edge cases."""
    
    def test_login_with_inactive_user(self):
        """Test login with an inactive user account."""
        # Create mock user
        mock_user = MagicMock(spec=User)
        mock_user.is_active = False
        mock_user.check_password.return_value = True
        
        # Create mock user repository
        mock_user_repo = MagicMock()
        mock_user_repo.get_by_username.return_value = mock_user
        
        # Create auth service with mock repository
        auth_service = AuthService(user_repository=mock_user_repo)
        
        # Attempt login with inactive user
        with pytest.raises(AuthenticationError) as excinfo:
            auth_service.authenticate("inactive_user", "password123")
        
        assert "Account is inactive" in str(excinfo.value)
    
    def test_login_with_expired_password(self):
        """Test login with an expired password."""
        # Create mock user
        mock_user = MagicMock(spec=User)
        mock_user.is_active = True
        mock_user.check_password.return_value = True
        mock_user.password_last_changed = datetime.now() - timedelta(days=91)  # 91 days old
        
        # Create mock user repository
        mock_user_repo = MagicMock()
        mock_user_repo.get_by_username.return_value = mock_user
        
        # Create auth service with mock repository
        auth_service = AuthService(user_repository=mock_user_repo)
        
        # Patch the password expiry setting
        with patch("api.config.settings.PASSWORD_EXPIRY_DAYS", 90):
            # Attempt login with expired password
            with pytest.raises(AuthenticationError) as excinfo:
                auth_service.authenticate("user", "expired_password")
            
            assert "Password has expired" in str(excinfo.value)
    
    def test_login_after_max_attempts(self):
        """Test login after maximum failed attempts."""
        # Create mock user
        mock_user = MagicMock(spec=User)
        mock_user.is_active = True
        mock_user.failed_login_attempts = 5  # Max attempts
        mock_user.last_failed_login = datetime.now() - timedelta(minutes=10)  # 10 minutes ago
        
        # Create mock user repository
        mock_user_repo = MagicMock()
        mock_user_repo.get_by_username.return_value = mock_user
        
        # Create auth service with mock repository
        auth_service = AuthService(user_repository=mock_user_repo)
        
        # Patch the max attempts and lockout duration settings
        with patch("api.config.settings.MAX_LOGIN_ATTEMPTS", 5), \
             patch("api.config.settings.ACCOUNT_LOCKOUT_MINUTES", 15):
            # Attempt login after max attempts
            with pytest.raises(AuthenticationError) as excinfo:
                auth_service.authenticate("user", "password123")
            
            assert "Account is locked" in str(excinfo.value)
    
    def test_token_refresh_with_expired_refresh_token(self):
        """Test token refresh with an expired refresh token."""
        # Create auth service
        auth_service = AuthService()
        
        # Mock the token verification to fail
        with patch.object(auth_service, "_verify_refresh_token", side_effect=AuthenticationError("Refresh token has expired")):
            # Attempt to refresh with expired token
            with pytest.raises(AuthenticationError) as excinfo:
                auth_service.refresh_token("expired_refresh_token")
            
            assert "Refresh token has expired" in str(excinfo.value)


class TestAuthorizationEdgeCases:
    """Tests for authorization edge cases."""
    
    def test_access_with_insufficient_permissions(self):
        """Test accessing a resource with insufficient permissions."""
        # Create mock user with 'user' role
        mock_user = MagicMock(spec=User)
        mock_user.role = "user"
        
        # Create mock campaign owned by another user
        mock_campaign = MagicMock(spec=Campaign)
        mock_campaign.created_by = 999  # Different user ID
        
        # Create mock campaign repository
        mock_campaign_repo = MagicMock()
        mock_campaign_repo.get_by_id.return_value = mock_campaign
        
        # Create campaign service with mock repository
        campaign_service = CampaignService(campaign_repository=mock_campaign_repo)
        
        # Attempt to delete campaign without permission
        with pytest.raises(AuthorizationError) as excinfo:
            campaign_service.delete_campaign(1, mock_user)
        
        assert "Permission denied" in str(excinfo.value)
    
    def test_admin_override_permissions(self):
        """Test admin role overriding normal permission checks."""
        # Create mock user with 'admin' role
        mock_user = MagicMock(spec=User)
        mock_user.role = "admin"
        mock_user.id = 123
        
        # Create mock campaign owned by another user
        mock_campaign = MagicMock(spec=Campaign)
        mock_campaign.created_by = 999  # Different user ID
        
        # Create mock campaign repository
        mock_campaign_repo = MagicMock()
        mock_campaign_repo.get_by_id.return_value = mock_campaign
        mock_campaign_repo.delete.return_value = True
        
        # Create campaign service with mock repository
        campaign_service = CampaignService(campaign_repository=mock_campaign_repo)
        
        # Admin should be able to delete any campaign
        result = campaign_service.delete_campaign(1, mock_user)
        
        assert result is True
        mock_campaign_repo.delete.assert_called_once_with(1)


class TestResourceNotFoundEdgeCases:
    """Tests for resource not found edge cases."""
    
    def test_get_nonexistent_campaign(self):
        """Test getting a campaign that doesn't exist."""
        # Create mock campaign repository that returns None
        mock_campaign_repo = MagicMock()
        mock_campaign_repo.get_by_id.return_value = None
        
        # Create campaign service with mock repository
        campaign_service = CampaignService(campaign_repository=mock_campaign_repo)
        
        # Attempt to get nonexistent campaign
        with pytest.raises(ResourceNotFoundError) as excinfo:
            campaign_service.get_campaign(999)
        
        assert "Campaign not found" in str(excinfo.value)
    
    def test_update_nonexistent_test_case(self):
        """Test updating a test case that doesn't exist."""
        # Create mock test case repository that returns None
        mock_testcase_repo = MagicMock()
        mock_testcase_repo.get_by_id.return_value = None
        
        # Create test case service with mock repository
        testcase_service = TestCaseService(testcase_repository=mock_testcase_repo)
        
        # Mock user
        mock_user = MagicMock(spec=User)
        mock_user.id = 123
        
        # Attempt to update nonexistent test case
        with pytest.raises(ResourceNotFoundError) as excinfo:
            testcase_service.update_test_case(999, {"name": "Updated Test Case"}, mock_user)
        
        assert "Test case not found" in str(excinfo.value)


class TestValidationEdgeCases:
    """Tests for validation edge cases."""
    
    def test_create_campaign_with_invalid_data(self):
        """Test creating a campaign with invalid data."""
        # Create campaign service
        campaign_service = CampaignService()
        
        # Mock user
        mock_user = MagicMock(spec=User)
        mock_user.id = 123
        
        # Attempt to create campaign with invalid data
        with pytest.raises(ValidationError) as excinfo:
            campaign_service.create_campaign({
                "name": "",  # Empty name
                "description": "Test campaign"
            }, mock_user)
        
        assert "Name cannot be empty" in str(excinfo.value)
    
    def test_update_test_case_with_invalid_status(self):
        """Test updating a test case with an invalid status."""
        # Create mock test case
        mock_testcase = MagicMock(spec=TestCase)
        mock_testcase.id = 1
        
        # Create mock test case repository
        mock_testcase_repo = MagicMock()
        mock_testcase_repo.get_by_id.return_value = mock_testcase
        
        # Create test case service with mock repository
        testcase_service = TestCaseService(testcase_repository=mock_testcase_repo)
        
        # Mock user
        mock_user = MagicMock(spec=User)
        mock_user.id = 123
        
        # Attempt to update test case with invalid status
        with pytest.raises(ValidationError) as excinfo:
            testcase_service.update_test_case(1, {"status": "invalid_status"}, mock_user)
        
        assert "Invalid status" in str(excinfo.value)


class TestCachingEdgeCases:
    """Tests for caching edge cases."""
    
    def test_cache_with_unhashable_arguments(self):
        """Test caching with unhashable arguments."""
        # Create a cached function
        @cached(ttl=60)
        def cached_function(arg1, arg2):
            return f"{arg1}-{arg2}"
        
        # Call with hashable arguments
        result1 = cached_function("a", "b")
        assert result1 == "a-b"
        
        # Call with unhashable arguments (should not raise error)
        result2 = cached_function({"key": "value"}, ["list", "item"])
        assert result2 == "{'key': 'value'}-['list', 'item']"
    
    def test_memoize_with_large_result(self):
        """Test memoization with a large result."""
        # Create a memoized function that returns a large result
        @memoize
        def generate_large_data(size):
            return "x" * size
        
        # Call with a reasonable size
        result1 = generate_large_data(1000)
        assert len(result1) == 1000
        
        # Call with a very large size (should still work)
        result2 = generate_large_data(1000000)
        assert len(result2) == 1000000


class TestParallelizationEdgeCases:
    """Tests for parallelization edge cases."""
    
    def test_parallelize_with_empty_list(self):
        """Test parallelization with an empty list."""
        # Define a function to parallelize
        def process_item(item):
            return item * 2
        
        # Parallelize with empty list
        result = parallelize(process_item, [])
        
        # Should return empty list
        assert result == []
    
    def test_parallelize_with_error_in_worker(self):
        """Test parallelization with an error in a worker."""
        # Define a function that raises an error for certain inputs
        def process_item(item):
            if item == 3:
                raise ValueError("Error processing item 3")
            return item * 2
        
        # Parallelize with input that will cause an error
        with pytest.raises(ValueError) as excinfo:
            parallelize(process_item, [1, 2, 3, 4, 5])
        
        assert "Error processing item 3" in str(excinfo.value)


class TestVectorizationEdgeCases:
    """Tests for vectorization edge cases."""
    
    def test_vectorize_with_non_vectorizable_function(self):
        """Test vectorization with a function that cannot be vectorized."""
        # Define a function that cannot be easily vectorized
        @vectorize
        def process_complex_data(data):
            if isinstance(data, list):
                result = []
                for item in data:
                    if isinstance(item, dict):
                        result.append(item.get("value", 0) * 2)
                    else:
                        result.append(0)
                return result
            return 0
        
        # Call with complex data
        data = [{"value": 1}, {"value": 2}, {"other": 3}]
        result = process_complex_data(data)
        
        # Should fall back to non-vectorized implementation
        assert result == [2, 4, 0]


class TestBatchLoadingEdgeCases:
    """Tests for batch loading edge cases."""
    
    def test_batch_load_with_duplicate_ids(self):
        """Test batch loading with duplicate IDs."""
        # Mock session and query
        mock_session = MagicMock()
        mock_query = MagicMock()
        mock_session.execute.return_value.scalars.return_value.all.return_value = [
            MagicMock(id=1, name="Item 1"),
            MagicMock(id=2, name="Item 2"),
        ]
        
        # Mock model class and ID field
        mock_model = MagicMock()
        mock_id_field = MagicMock()
        mock_id_field.name = "id"
        
        # Batch load with duplicate IDs
        result = batch_load(
            mock_model,
            mock_id_field,
            [1, 2, 1, 2, 3],  # Duplicate IDs
            mock_session
        )
        
        # Should return unique items
        assert len(result) == 2
        assert 1 in result
        assert 2 in result
        assert 3 not in result  # ID 3 not found
