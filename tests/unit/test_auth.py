"""
Unit tests for authentication functionality.
"""

import pytest
from fastapi import status
from api.models.user import User, User<PERSON><PERSON>

def test_register_user(client, db_session):
    """Test user registration."""
    user_data = {
        "username": "newuser",
        "email": "<EMAIL>",
        "password": "Password123!",
        "role": "ANALYST"
    }

    response = client.post("/api/v1/auth/register", json=user_data)
    assert response.status_code == status.HTTP_201_CREATED

    # Check that the user was created in the database
    user = db_session.query(User).filter(User.username == "newuser").first()
    assert user is not None
    assert user.email == "<EMAIL>"
    assert user.role == UserRole.ANALYST

def test_register_duplicate_username(client, test_user, db_session):
    """Test registration with a duplicate username."""
    user_data = {
        "username": "testuser",  # Same as test_user fixture
        "email": "<EMAIL>",
        "password": "Password123!",
        "role": "ANALYST"
    }

    response = client.post("/api/v1/auth/register", json=user_data)
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert "username already exists" in response.text.lower()

def test_login_success(client, test_user):
    """Test successful login."""
    login_data = {
        "username": "testuser",
        "password": "testpass123"
    }

    response = client.post("/api/v1/auth/token", data=login_data)
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert "access_token" in data
    assert "token_type" in data
    assert data["token_type"] == "bearer"

def test_login_invalid_credentials(client, test_user):
    """Test login with invalid credentials."""
    login_data = {
        "username": "testuser",
        "password": "wrongpassword"
    }

    response = client.post("/api/v1/auth/token", data=login_data)
    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    assert "invalid username or password" in response.text.lower()

def test_get_current_user(client, test_user_token):
    """Test getting current user information."""
    headers = {"Authorization": f"Bearer {test_user_token}"}
    response = client.get("/api/v1/users/me", headers=headers)

    assert response.status_code == status.HTTP_200_OK
    user_data = response.json()
    assert user_data["username"] == "testuser"
    assert user_data["email"] == "<EMAIL>"
    assert user_data["role"] == UserRole.ANALYST.value

def test_unauthorized_access(client):
    """Test accessing protected endpoint without token."""
    response = client.get("/api/v1/users/me")
    assert response.status_code == status.HTTP_401_UNAUTHORIZED

def test_refresh_token(client, test_user_token):
    """Test refreshing an access token."""
    headers = {"Authorization": f"Bearer {test_user_token}"}
    response = client.post("/api/v1/auth/refresh", headers=headers)

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert "access_token" in data
    assert data["access_token"] != test_user_token  # New token should be different
