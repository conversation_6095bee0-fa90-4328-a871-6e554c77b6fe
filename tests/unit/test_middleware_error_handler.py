"""Unit tests for the middleware.error_handler module."""
import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, patch
from flask import <PERSON>lask, jsonify, request
from werkzeug.exceptions import NotFound, BadRequest, InternalServerError
from api.middleware.error_handler import error_handler, register_error_handlers


@pytest.fixture
def app():
    """Create a Flask app for testing."""
    app = Flask(__name__)
    app.config['TESTING'] = True
    return app


@pytest.fixture
def client(app):
    """Create a test client for the app."""
    return app.test_client()


def test_error_handler_decorator_success():
    """Test the error_handler decorator with a successful function."""
    # Create a mock function that returns successfully
    mock_func = MagicMock(return_value={"success": True})
    
    # Apply the error_handler decorator
    decorated_func = error_handler(mock_func)
    
    # Call the decorated function
    result = decorated_func()
    
    # Check that the original function was called and its result returned
    mock_func.assert_called_once()
    assert result == {"success": True}


def test_error_handler_decorator_exception():
    """Test the error_handler decorator with a function that raises an exception."""
    # Create a mock function that raises an exception
    mock_func = MagicMock(side_effect=ValueError("Test error"))
    
    # Apply the error_handler decorator
    decorated_func = error_handler(mock_func)
    
    # Mock the logging and jsonify functions
    with patch('api.middleware.error_handler.logging') as mock_logging:
        with patch('api.middleware.error_handler.jsonify') as mock_jsonify:
            mock_jsonify.return_value = {"error": "Test error"}
            
            # Call the decorated function
            result = decorated_func()
            
            # Check that the error was logged
            mock_logging.error.assert_called_once()
            
            # Check that jsonify was called with the error message
            mock_jsonify.assert_called_once()
            assert result == {"error": "Test error"}


def test_register_error_handlers(app):
    """Test registering error handlers with a Flask app."""
    # Mock the app.errorhandler method
    app.errorhandler = MagicMock()
    
    # Call register_error_handlers
    register_error_handlers(app)
    
    # Check that app.errorhandler was called for each error type
    assert app.errorhandler.call_count >= 3
    
    # Check specific error types
    app.errorhandler.assert_any_call(404)
    app.errorhandler.assert_any_call(400)
    app.errorhandler.assert_any_call(500)


def test_not_found_handler(app, client):
    """Test the 404 error handler."""
    # Register error handlers
    register_error_handlers(app)
    
    # Create a route that will trigger a 404
    @app.route('/nonexistent')
    def nonexistent():
        raise NotFound("Resource not found")
    
    # Make a request to the nonexistent route
    response = client.get('/nonexistent')
    
    # Check the response
    assert response.status_code == 404
    assert b"Resource not found" in response.data


def test_bad_request_handler(app, client):
    """Test the 400 error handler."""
    # Register error handlers
    register_error_handlers(app)
    
    # Create a route that will trigger a 400
    @app.route('/bad-request')
    def bad_request():
        raise BadRequest("Bad request")
    
    # Make a request to the bad request route
    response = client.get('/bad-request')
    
    # Check the response
    assert response.status_code == 400
    assert b"Bad request" in response.data


def test_internal_server_error_handler(app, client):
    """Test the 500 error handler."""
    # Register error handlers
    register_error_handlers(app)
    
    # Create a route that will trigger a 500
    @app.route('/server-error')
    def server_error():
        raise InternalServerError("Internal server error")
    
    # Make a request to the server error route
    response = client.get('/server-error')
    
    # Check the response
    assert response.status_code == 500
    assert b"Internal server error" in response.data


def test_generic_exception_handler(app, client):
    """Test handling a generic exception."""
    # Register error handlers
    register_error_handlers(app)
    
    # Create a route that will raise a generic exception
    @app.route('/generic-error')
    def generic_error():
        raise ValueError("Generic error")
    
    # Make a request to the generic error route
    response = client.get('/generic-error')
    
    # Check the response
    assert response.status_code == 500
    assert b"Generic error" in response.data


def test_error_handler_with_json_response(app, client):
    """Test that the error handler returns JSON for API requests."""
    # Register error handlers
    register_error_handlers(app)
    
    # Create a route that will raise an exception
    @app.route('/api-error')
    def api_error():
        raise ValueError("API error")
    
    # Make a request to the API error route with JSON accept header
    response = client.get('/api-error', headers={'Accept': 'application/json'})
    
    # Check the response
    assert response.status_code == 500
    assert response.content_type == 'application/json'
    assert b"API error" in response.data


def test_error_handler_with_html_response(app, client):
    """Test that the error handler returns HTML for browser requests."""
    # Register error handlers
    register_error_handlers(app)
    
    # Create a route that will raise an exception
    @app.route('/html-error')
    def html_error():
        raise ValueError("HTML error")
    
    # Make a request to the HTML error route with HTML accept header
    response = client.get('/html-error', headers={'Accept': 'text/html'})
    
    # Check the response
    assert response.status_code == 500
    assert b"HTML error" in response.data
