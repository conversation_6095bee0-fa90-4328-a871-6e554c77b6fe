"""Unit tests for the utils.common module."""
import pytest
from datetime import datetime, timedelta
from api.utils.common import (
    generate_random_string,
    format_datetime,
    is_valid_uuid,
    truncate_string,
    merge_dicts,
    deep_get
)


def test_generate_random_string():
    """Test the generate_random_string function."""
    # Test default length
    random_str = generate_random_string()
    assert isinstance(random_str, str)
    assert len(random_str) == 16
    
    # Test custom length
    random_str = generate_random_string(length=32)
    assert len(random_str) == 32
    
    # Test different character sets
    random_str = generate_random_string(length=10, chars="ABC123")
    assert len(random_str) == 10
    assert all(c in "ABC123" for c in random_str)
    
    # Test uniqueness
    random_str1 = generate_random_string()
    random_str2 = generate_random_string()
    assert random_str1 != random_str2


def test_format_datetime():
    """Test the format_datetime function."""
    # Test with a specific datetime
    dt = datetime(2023, 1, 15, 14, 30, 45)
    
    # Test default format
    formatted = format_datetime(dt)
    assert formatted == "2023-01-15 14:30:45"
    
    # Test custom format
    formatted = format_datetime(dt, fmt="%Y/%m/%d")
    assert formatted == "2023/01/15"
    
    # Test with None
    assert format_datetime(None) is None
    
    # Test with default value for None
    assert format_datetime(None, default="N/A") == "N/A"


def test_is_valid_uuid():
    """Test the is_valid_uuid function."""
    # Valid UUIDs
    assert is_valid_uuid("123e4567-e89b-12d3-a456-************") is True
    assert is_valid_uuid("123E4567-E89B-12D3-A456-************") is True
    
    # Invalid UUIDs
    assert is_valid_uuid("not-a-uuid") is False
    assert is_valid_uuid("123e4567-e89b-12d3-a456") is False
    assert is_valid_uuid("") is False
    assert is_valid_uuid(None) is False
    assert is_valid_uuid(123) is False


def test_truncate_string():
    """Test the truncate_string function."""
    # Test with string shorter than max_length
    assert truncate_string("Hello", 10) == "Hello"
    
    # Test with string equal to max_length
    assert truncate_string("Hello", 5) == "Hello"
    
    # Test with string longer than max_length
    assert truncate_string("Hello, world!", 5) == "Hello..."
    
    # Test with custom suffix
    assert truncate_string("Hello, world!", 5, suffix="[...]") == "Hello[...]"
    
    # Test with empty string
    assert truncate_string("", 10) == ""
    
    # Test with None
    assert truncate_string(None, 10) is None


def test_merge_dicts():
    """Test the merge_dicts function."""
    # Test basic merge
    dict1 = {"a": 1, "b": 2}
    dict2 = {"c": 3, "d": 4}
    merged = merge_dicts(dict1, dict2)
    assert merged == {"a": 1, "b": 2, "c": 3, "d": 4}
    
    # Test with overlapping keys
    dict1 = {"a": 1, "b": 2}
    dict2 = {"b": 3, "c": 4}
    merged = merge_dicts(dict1, dict2)
    assert merged == {"a": 1, "b": 3, "c": 4}
    
    # Test with nested dictionaries
    dict1 = {"a": 1, "b": {"x": 1, "y": 2}}
    dict2 = {"c": 3, "b": {"y": 3, "z": 4}}
    merged = merge_dicts(dict1, dict2)
    assert merged == {"a": 1, "b": {"x": 1, "y": 3, "z": 4}, "c": 3}
    
    # Test with empty dictionaries
    assert merge_dicts({}, {}) == {}
    assert merge_dicts({"a": 1}, {}) == {"a": 1}
    assert merge_dicts({}, {"b": 2}) == {"b": 2}


def test_deep_get():
    """Test the deep_get function."""
    # Test nested dictionary
    data = {"a": {"b": {"c": 42}}}
    
    # Test valid path
    assert deep_get(data, ["a", "b", "c"]) == 42
    
    # Test invalid path
    assert deep_get(data, ["a", "x", "c"]) is None
    
    # Test with default value
    assert deep_get(data, ["a", "x", "c"], default="not found") == "not found"
    
    # Test with empty path
    assert deep_get(data, []) == data
    
    # Test with None data
    assert deep_get(None, ["a", "b"]) is None
