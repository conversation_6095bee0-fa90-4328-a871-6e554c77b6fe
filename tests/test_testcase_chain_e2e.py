"""
End-to-End tests for Enhanced Testcase Chaining & Sequencing API.

This module contains comprehensive end-to-end tests that simulate real user
interactions with the testcase chaining API, including complete workflows
from chain creation through execution monitoring.
"""

import pytest
import json
from datetime import datetime
from unittest.mock import Mock, patch

from fastapi.testclient import TestClient

from api.main import app
from api.models.testcase_chain import (
    TestcaseChain,
    TestcaseChainNode,
    TestcaseChainEdge,
    ChainExecution,
    NodeExecution
)
from api.models.user import User


@pytest.fixture
def client():
    """Create a test client."""
    return TestClient(app)


@pytest.fixture
def mock_user():
    """Create a mock authenticated user."""
    user = Mock(spec=User)
    user.id = 1
    user.username = "testuser"
    user.email = "<EMAIL>"
    user.is_admin = False
    return user


class TestCompleteChainWorkflowE2E:
    """End-to-end tests for complete chain workflows."""
    
    @patch('api.routes.v1_testcase_chain.get_current_user')
    @patch('api.routes.v1_testcase_chain.get_db')
    def test_complete_attack_chain_workflow(self, mock_get_db, mock_get_user, client, mock_user):
        """
        Test complete attack chain workflow from creation to execution.
        
        This test simulates a real user creating a multi-stage attack chain,
        adding nodes and edges, validating the chain, and executing it.
        """
        mock_get_user.return_value = mock_user
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Step 1: Create a new attack chain
        chain_data = {
            "name": "Advanced Persistence Attack",
            "description": "Multi-stage persistence attack scenario",
            "chain_type": "sequential",
            "max_execution_time_minutes": 120,
            "retry_on_failure": True,
            "auto_cleanup": False
        }
        
        # Mock chain creation
        mock_chain = TestcaseChain(
            id=1,
            name=chain_data["name"],
            description=chain_data["description"],
            status="draft",
            chain_type=chain_data["chain_type"],
            max_execution_time_minutes=chain_data["max_execution_time_minutes"],
            retry_on_failure=chain_data["retry_on_failure"],
            auto_cleanup=chain_data["auto_cleanup"],
            created_by=mock_user.id,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        with patch('api.routes.v1_testcase_chain.TestcaseChainService') as mock_service_class:
            mock_service = Mock()
            mock_service_class.return_value = mock_service
            mock_service.create_chain.return_value = mock_chain
            
            response = client.post("/api/v1/testcase-chains/chains", json=chain_data)
            
            assert response.status_code == 201
            chain_response = response.json()
            assert chain_response["name"] == chain_data["name"]
            assert chain_response["id"] == 1
            chain_id = chain_response["id"]
        
        # Step 2: Add nodes to the chain
        attack_stages = [
            {
                "name": "Initial Access",
                "testcase_id": 101,
                "node_type": "start",
                "execution_order": 1,
                "position_x": 100.0,
                "position_y": 100.0
            },
            {
                "name": "Execution",
                "testcase_id": 102,
                "node_type": "standard",
                "execution_order": 2,
                "position_x": 200.0,
                "position_y": 100.0,
                "timeout_minutes": 45,
                "retry_count": 2
            },
            {
                "name": "Persistence",
                "testcase_id": 103,
                "node_type": "conditional",
                "execution_order": 3,
                "position_x": 300.0,
                "position_y": 100.0,
                "condition_expression": "previous_result.access_level == 'admin'",
                "continue_on_failure": False
            },
            {
                "name": "Defense Evasion",
                "testcase_id": 104,
                "node_type": "standard",
                "execution_order": 4,
                "position_x": 400.0,
                "position_y": 100.0
            },
            {
                "name": "Exfiltration",
                "testcase_id": 105,
                "node_type": "end",
                "execution_order": 5,
                "position_x": 500.0,
                "position_y": 100.0
            }
        ]
        
        node_ids = []
        for i, stage in enumerate(attack_stages):
            mock_node = TestcaseChainNode(
                id=i + 1,
                chain_id=chain_id,
                **stage
            )
            
            with patch('api.routes.v1_testcase_chain.TestcaseChainService') as mock_service_class:
                mock_service = Mock()
                mock_service_class.return_value = mock_service
                mock_service.add_node_to_chain.return_value = mock_node
                
                response = client.post(f"/api/v1/testcase-chains/chains/{chain_id}/nodes", json=stage)
                
                assert response.status_code == 201
                node_response = response.json()
                assert node_response["testcase_id"] == stage["testcase_id"]
                node_ids.append(node_response["id"])
        
        # Step 3: Create edges between nodes
        edges_data = [
            {
                "source_node_id": node_ids[0],
                "target_node_id": node_ids[1],
                "edge_type": "standard",
                "label": "Initial Access to Execution"
            },
            {
                "source_node_id": node_ids[1],
                "target_node_id": node_ids[2],
                "edge_type": "success_path",
                "condition": "result.status == 'success'",
                "label": "Execution Success to Persistence"
            },
            {
                "source_node_id": node_ids[2],
                "target_node_id": node_ids[3],
                "edge_type": "conditional",
                "condition": "result.persistence_established == true",
                "label": "Persistence to Defense Evasion"
            },
            {
                "source_node_id": node_ids[3],
                "target_node_id": node_ids[4],
                "edge_type": "standard",
                "label": "Defense Evasion to Exfiltration"
            }
        ]
        
        for i, edge_data in enumerate(edges_data):
            mock_edge = TestcaseChainEdge(
                id=i + 1,
                **edge_data
            )
            
            with patch('api.routes.v1_testcase_chain.TestcaseChainService') as mock_service_class:
                mock_service = Mock()
                mock_service_class.return_value = mock_service
                mock_service.add_edge_to_chain.return_value = mock_edge
                
                response = client.post("/api/v1/testcase-chains/edges", json=edge_data)
                
                assert response.status_code == 201
                edge_response = response.json()
                assert edge_response["source_node_id"] == edge_data["source_node_id"]
                assert edge_response["target_node_id"] == edge_data["target_node_id"]
        
        # Step 4: Validate the chain
        mock_validation_result = Mock()
        mock_validation_result.is_valid = True
        mock_validation_result.errors = []
        mock_validation_result.warnings = []
        mock_validation_result.cycle_detected = False
        mock_validation_result.unreachable_nodes = []
        
        with patch('api.routes.v1_testcase_chain.TestcaseChainService') as mock_service_class:
            mock_service = Mock()
            mock_service_class.return_value = mock_service
            mock_service.validate_chain.return_value = mock_validation_result
            
            response = client.get(f"/api/v1/testcase-chains/chains/{chain_id}/validate")
            
            assert response.status_code == 200
            validation_response = response.json()
            assert validation_response["is_valid"] is True
            assert len(validation_response["errors"]) == 0
        
        # Step 5: Start chain execution
        execution_data = {
            "execution_context": {
                "environment": "staging",
                "target_host": "*************",
                "attack_vector": "phishing",
                "timeout": 7200
            }
        }
        
        mock_execution = ChainExecution(
            id=1,
            chain_id=chain_id,
            started_by=mock_user.id,
            start_time=datetime.utcnow(),
            status="running",
            execution_context=execution_data["execution_context"],
            total_nodes=len(attack_stages),
            completed_nodes=0,
            failed_nodes=0,
            skipped_nodes=0
        )
        
        with patch('api.routes.v1_testcase_chain.TestcaseChainService') as mock_service_class:
            mock_service = Mock()
            mock_service_class.return_value = mock_service
            mock_service.start_chain_execution.return_value = mock_execution
            
            response = client.post(f"/api/v1/testcase-chains/chains/{chain_id}/execute", json=execution_data)
            
            assert response.status_code == 201
            execution_response = response.json()
            assert execution_response["status"] == "running"
            assert execution_response["total_nodes"] == len(attack_stages)
            execution_id = execution_response["id"]
        
        # Step 6: Monitor execution progress
        mock_db.query.return_value.filter.return_value.first.return_value = mock_execution
        mock_db.query.return_value.filter.return_value.all.return_value = []
        
        response = client.get(f"/api/v1/testcase-chains/executions/{execution_id}")
        
        assert response.status_code == 200
        execution_status = response.json()
        assert execution_status["status"] == "running"
        assert execution_status["chain_id"] == chain_id
        
        # Step 7: Get next executable nodes
        mock_next_nodes = [
            TestcaseChainNode(
                id=node_ids[0],
                chain_id=chain_id,
                testcase_id=101,
                node_type="start"
            )
        ]
        
        with patch('api.routes.v1_testcase_chain.TestcaseChainService') as mock_service_class:
            mock_service = Mock()
            mock_service_class.return_value = mock_service
            mock_service.get_next_executable_nodes.return_value = mock_next_nodes
            
            response = client.get(f"/api/v1/testcase-chains/executions/{execution_id}/next-nodes")
            
            assert response.status_code == 200
            next_nodes = response.json()
            assert len(next_nodes) == 1
            assert next_nodes[0]["node_type"] == "start"
    
    @patch('api.routes.v1_testcase_chain.get_current_user')
    @patch('api.routes.v1_testcase_chain.get_db')
    def test_chain_management_lifecycle(self, mock_get_db, mock_get_user, client, mock_user):
        """Test complete chain management lifecycle including updates and deletion."""
        mock_get_user.return_value = mock_user
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Create chain
        chain_data = {
            "name": "Test Management Chain",
            "description": "Chain for testing management operations",
            "chain_type": "sequential"
        }
        
        mock_chain = TestcaseChain(
            id=2,
            **chain_data,
            status="draft",
            created_by=mock_user.id,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        with patch('api.routes.v1_testcase_chain.TestcaseChainService') as mock_service_class:
            mock_service = Mock()
            mock_service_class.return_value = mock_service
            mock_service.create_chain.return_value = mock_chain
            
            response = client.post("/api/v1/testcase-chains/chains", json=chain_data)
            assert response.status_code == 201
            chain_id = response.json()["id"]
        
        # Update chain
        update_data = {
            "name": "Updated Management Chain",
            "description": "Updated description",
            "status": "active"
        }
        
        mock_db.query.return_value.filter.return_value.first.return_value = mock_chain
        
        response = client.put(f"/api/v1/testcase-chains/chains/{chain_id}", json=update_data)
        assert response.status_code == 200
        
        # Get updated chain
        mock_db.query.return_value.filter.return_value.first.return_value = mock_chain
        mock_db.query.return_value.filter.return_value.order_by.return_value.all.return_value = []
        mock_db.query.return_value.join.return_value.filter.return_value.all.return_value = []
        
        response = client.get(f"/api/v1/testcase-chains/chains/{chain_id}")
        assert response.status_code == 200
        
        # Delete chain
        response = client.delete(f"/api/v1/testcase-chains/chains/{chain_id}")
        assert response.status_code == 204
    
    @patch('api.routes.v1_testcase_chain.get_current_user')
    @patch('api.routes.v1_testcase_chain.get_db')
    def test_error_handling_scenarios(self, mock_get_db, mock_get_user, client, mock_user):
        """Test various error handling scenarios in the API."""
        mock_get_user.return_value = mock_user
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Test chain not found
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        response = client.get("/api/v1/testcase-chains/chains/999")
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
        
        # Test validation failure
        with patch('api.routes.v1_testcase_chain.TestcaseChainService') as mock_service_class:
            mock_service = Mock()
            mock_service_class.return_value = mock_service
            mock_service.validate_chain.side_effect = Exception("Validation error")
            
            response = client.get("/api/v1/testcase-chains/chains/1/validate")
            assert response.status_code == 400
            assert "Failed to validate chain" in response.json()["detail"]
        
        # Test execution failure
        execution_data = {"execution_context": {}}
        
        with patch('api.routes.v1_testcase_chain.TestcaseChainService') as mock_service_class:
            mock_service = Mock()
            mock_service_class.return_value = mock_service
            mock_service.start_chain_execution.side_effect = ValueError("Chain validation failed")
            
            response = client.post("/api/v1/testcase-chains/chains/1/execute", json=execution_data)
            assert response.status_code == 400
            assert "Chain validation failed" in response.json()["detail"]
