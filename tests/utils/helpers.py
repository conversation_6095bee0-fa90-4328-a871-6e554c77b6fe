"""Helper functions for tests."""
import json
import random
import string
from typing import Any, Dict, List, Optional, Union, Tuple
from datetime import datetime, timedelta

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session


def get_auth_token(client: TestClient, username: str, password: str) -> str:
    """Get an authentication token for a user.
    
    Args:
        client: FastAPI test client
        username: Username
        password: Password
        
    Returns:
        str: Authentication token
    """
    response = client.post(
        "/api/v1/auth/token/",
        data={"username": username, "password": password}
    )
    assert response.status_code == 200, f"Failed to get auth token: {response.text}"
    return response.json()["access_token"]


def auth_headers(token: str) -> Dict[str, str]:
    """Create authentication headers with a token.
    
    Args:
        token: Authentication token
        
    Returns:
        Dict[str, str]: Headers dictionary
    """
    return {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }


def random_date(start_date: datetime, end_date: datetime) -> datetime:
    """Generate a random date between start_date and end_date.
    
    Args:
        start_date: Start date
        end_date: End date
        
    Returns:
        datetime: Random date
    """
    delta = end_date - start_date
    random_days = random.randint(0, delta.days)
    random_seconds = random.randint(0, 86399)  # 86399 = 23:59:59
    return start_date + timedelta(days=random_days, seconds=random_seconds)


def random_bool() -> bool:
    """Generate a random boolean value.
    
    Returns:
        bool: Random boolean
    """
    return random.choice([True, False])


def random_choice(choices: List[Any]) -> Any:
    """Choose a random item from a list.
    
    Args:
        choices: List of choices
        
    Returns:
        Any: Random choice
    """
    return random.choice(choices)


def random_int(min_value: int = 1, max_value: int = 100) -> int:
    """Generate a random integer.
    
    Args:
        min_value: Minimum value (default: 1)
        max_value: Maximum value (default: 100)
        
    Returns:
        int: Random integer
    """
    return random.randint(min_value, max_value)


def random_float(min_value: float = 0.0, max_value: float = 1.0) -> float:
    """Generate a random float.
    
    Args:
        min_value: Minimum value (default: 0.0)
        max_value: Maximum value (default: 1.0)
        
    Returns:
        float: Random float
    """
    return random.uniform(min_value, max_value)


def random_string(length: int = 10, include_digits: bool = True) -> str:
    """Generate a random string.
    
    Args:
        length: Length of the string (default: 10)
        include_digits: Whether to include digits (default: True)
        
    Returns:
        str: Random string
    """
    chars = string.ascii_lowercase
    if include_digits:
        chars += string.digits
    return ''.join(random.choice(chars) for _ in range(length))


def random_email() -> str:
    """Generate a random email address.
    
    Returns:
        str: Random email address
    """
    username = random_string(8)
    domain = random_string(6)
    tld = random.choice(["com", "org", "net", "io"])
    return f"{username}@{domain}.{tld}"


def random_url() -> str:
    """Generate a random URL.
    
    Returns:
        str: Random URL
    """
    domain = random_string(8)
    tld = random.choice(["com", "org", "net", "io"])
    path = random_string(6)
    return f"https://{domain}.{tld}/{path}"


def random_dict(keys: List[str], value_func: Optional[Callable] = None) -> Dict[str, Any]:
    """Generate a random dictionary.
    
    Args:
        keys: List of keys
        value_func: Optional function to generate values (default: random_string)
        
    Returns:
        Dict[str, Any]: Random dictionary
    """
    if value_func is None:
        value_func = lambda: random_string(8)
    
    return {key: value_func() for key in keys}


def random_list(length: int = 5, item_func: Optional[Callable] = None) -> List[Any]:
    """Generate a random list.
    
    Args:
        length: Length of the list (default: 5)
        item_func: Optional function to generate items (default: random_string)
        
    Returns:
        List[Any]: Random list
    """
    if item_func is None:
        item_func = lambda: random_string(8)
    
    return [item_func() for _ in range(length)]


def create_test_data(db_session: Session) -> Dict[str, Any]:
    """Create test data for tests.
    
    This is a convenience function that creates a set of related test data
    using the factories.
    
    Args:
        db_session: SQLAlchemy session
        
    Returns:
        Dict[str, Any]: Dictionary of created test data
    """
    from tests.utils.factories import (
        UserFactory, CampaignFactory, TestCaseFactory, 
        MitreFactory, OrganizationFactory
    )
    
    # Create users
    admin = UserFactory.create(
        db_session, 
        username="admin_user", 
        email="<EMAIL>", 
        role="admin"
    )
    
    analyst = UserFactory.create(
        db_session, 
        username="analyst_user", 
        email="<EMAIL>", 
        role="analyst"
    )
    
    # Create organizations
    org1 = OrganizationFactory.create(
        db_session, 
        name="Organization 1"
    )
    
    org2 = OrganizationFactory.create(
        db_session, 
        name="Organization 2"
    )
    
    # Create MITRE data
    mitre_version = MitreFactory.create_version(
        db_session, 
        version="test_version", 
        name="Test Version"
    )
    
    tactic1 = MitreFactory.create_tactic(
        db_session, 
        external_id="TA0001", 
        name="Initial Access", 
        version_id=mitre_version.id
    )
    
    tactic2 = MitreFactory.create_tactic(
        db_session, 
        external_id="TA0002", 
        name="Execution", 
        version_id=mitre_version.id
    )
    
    technique1 = MitreFactory.create_technique(
        db_session, 
        technique_id="T1190", 
        name="Exploit Public-Facing Application", 
        version_id=mitre_version.id, 
        tactics=[tactic1]
    )
    
    technique2 = MitreFactory.create_technique(
        db_session, 
        technique_id="T1059", 
        name="Command and Scripting Interpreter", 
        version_id=mitre_version.id, 
        tactics=[tactic2]
    )
    
    # Create campaigns
    campaign1 = CampaignFactory.create(
        db_session, 
        name="Campaign 1", 
        organizations=[org1]
    )
    
    campaign2 = CampaignFactory.create(
        db_session, 
        name="Campaign 2", 
        organizations=[org2]
    )
    
    # Create test cases
    test_case1 = TestCaseFactory.create(
        db_session, 
        name="Test Case 1", 
        campaign_id=campaign1.id, 
        mitre_techniques=[technique1]
    )
    
    test_case2 = TestCaseFactory.create(
        db_session, 
        name="Test Case 2", 
        campaign_id=campaign1.id, 
        mitre_techniques=[technique2]
    )
    
    test_case3 = TestCaseFactory.create(
        db_session, 
        name="Test Case 3", 
        campaign_id=campaign2.id, 
        mitre_techniques=[technique1, technique2]
    )
    
    return {
        "users": {
            "admin": admin,
            "analyst": analyst
        },
        "organizations": {
            "org1": org1,
            "org2": org2
        },
        "mitre": {
            "version": mitre_version,
            "tactics": {
                "tactic1": tactic1,
                "tactic2": tactic2
            },
            "techniques": {
                "technique1": technique1,
                "technique2": technique2
            }
        },
        "campaigns": {
            "campaign1": campaign1,
            "campaign2": campaign2
        },
        "test_cases": {
            "test_case1": test_case1,
            "test_case2": test_case2,
            "test_case3": test_case3
        }
    }
