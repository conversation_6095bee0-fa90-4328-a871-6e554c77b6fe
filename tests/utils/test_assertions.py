"""Tests for the assertions module."""
import pytest
from datetime import datetime, timedelta

from tests.utils.assertions import (
    assert_datetime_close,
    assert_json_response,
    assert_pagination_response,
    assert_error_response,
    assert_contains_subset,
    assert_list_contains_dict_with_key_value
)


def test_assert_datetime_close():
    """Test assert_datetime_close."""
    now = datetime.utcnow()
    
    # Test with datetimes that are close
    assert_datetime_close(now, now + timedelta(seconds=1))
    assert_datetime_close(now, now - timedelta(seconds=1))
    
    # Test with custom seconds parameter
    assert_datetime_close(now, now + timedelta(seconds=10), seconds=15)
    
    # Test with datetimes that are not close
    with pytest.raises(AssertionError):
        assert_datetime_close(now, now + timedelta(seconds=10))


def test_assert_json_response():
    """Test assert_json_response."""
    # Test with all expected keys present
    response_data = {"id": 1, "name": "Test", "description": "Test description"}
    assert_json_response(response_data, ["id", "name", "description"])
    
    # Test with missing key
    with pytest.raises(AssertionError):
        assert_json_response(response_data, ["id", "name", "description", "missing_key"])


def test_assert_pagination_response():
    """Test assert_pagination_response."""
    # Test with valid pagination response
    response_data = {"items": [{"id": 1}, {"id": 2}], "total": 2}
    assert_pagination_response(response_data)
    
    # Test with expected total
    assert_pagination_response(response_data, expected_total=2)
    
    # Test with incorrect expected total
    with pytest.raises(AssertionError):
        assert_pagination_response(response_data, expected_total=3)
    
    # Test with missing items key
    with pytest.raises(AssertionError):
        assert_pagination_response({"total": 2})
    
    # Test with missing total key
    with pytest.raises(AssertionError):
        assert_pagination_response({"items": []})
    
    # Test with items not a list
    with pytest.raises(AssertionError):
        assert_pagination_response({"items": "not a list", "total": 2})
    
    # Test with total not an integer
    with pytest.raises(AssertionError):
        assert_pagination_response({"items": [], "total": "not an integer"})


def test_assert_error_response():
    """Test assert_error_response."""
    # Test with valid error response
    response_data = {"detail": "Error message", "status_code": 400}
    assert_error_response(response_data, 400)
    
    # Test with incorrect status code
    with pytest.raises(AssertionError):
        assert_error_response(response_data, 404)
    
    # Test with missing detail key
    with pytest.raises(AssertionError):
        assert_error_response({"status_code": 400}, 400)
    
    # Test with missing status_code key
    with pytest.raises(AssertionError):
        assert_error_response({"detail": "Error message"}, 400)


def test_assert_contains_subset():
    """Test assert_contains_subset."""
    # Test with subset contained in superset
    superset = {"id": 1, "name": "Test", "description": "Test description"}
    subset = {"id": 1, "name": "Test"}
    assert_contains_subset(subset, superset)
    
    # Test with subset not contained in superset
    with pytest.raises(AssertionError):
        assert_contains_subset({"id": 1, "missing_key": "value"}, superset)
    
    # Test with different values
    with pytest.raises(AssertionError):
        assert_contains_subset({"id": 2}, superset)


def test_assert_list_contains_dict_with_key_value():
    """Test assert_list_contains_dict_with_key_value."""
    # Test with item in list
    items = [{"id": 1, "name": "Test"}, {"id": 2, "name": "Test 2"}]
    assert_list_contains_dict_with_key_value(items, "id", 1)
    
    # Test with item not in list
    with pytest.raises(AssertionError):
        assert_list_contains_dict_with_key_value(items, "id", 3)
    
    # Test with key not in any item
    with pytest.raises(AssertionError):
        assert_list_contains_dict_with_key_value(items, "missing_key", "value")
