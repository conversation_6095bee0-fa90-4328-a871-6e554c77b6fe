"""Custom assertions for tests."""
from typing import Any, Dict, List, Optional, Union, Callable
import json
from datetime import datetime


def assert_datetime_close(dt1: datetime, dt2: datetime, seconds: int = 5) -> None:
    """Assert that two datetimes are close to each other.
    
    Args:
        dt1: First datetime
        dt2: Second datetime
        seconds: Maximum allowed difference in seconds (default: 5)
    """
    diff = abs((dt1 - dt2).total_seconds())
    assert diff <= seconds, f"Datetimes differ by {diff} seconds, which is more than {seconds} seconds"


def assert_json_response(response_data: Dict[str, Any], expected_keys: List[str]) -> None:
    """Assert that a JSON response contains the expected keys.
    
    Args:
        response_data: JSON response data
        expected_keys: List of expected keys
    """
    for key in expected_keys:
        assert key in response_data, f"Expected key '{key}' not found in response"


def assert_pagination_response(response_data: Dict[str, Any], expected_total: Optional[int] = None) -> None:
    """Assert that a paginated response has the correct structure.
    
    Args:
        response_data: JSON response data
        expected_total: Optional expected total count
    """
    assert "items" in response_data, "Response does not contain 'items' key"
    assert "total" in response_data, "Response does not contain 'total' key"
    assert isinstance(response_data["items"], list), "'items' is not a list"
    assert isinstance(response_data["total"], int), "'total' is not an integer"
    
    if expected_total is not None:
        assert response_data["total"] == expected_total, f"Expected total {expected_total}, got {response_data['total']}"


def assert_error_response(response_data: Dict[str, Any], expected_status_code: int) -> None:
    """Assert that an error response has the correct structure.
    
    Args:
        response_data: JSON response data
        expected_status_code: Expected HTTP status code
    """
    assert "detail" in response_data, "Error response does not contain 'detail' key"
    assert "status_code" in response_data, "Error response does not contain 'status_code' key"
    assert response_data["status_code"] == expected_status_code, \
        f"Expected status code {expected_status_code}, got {response_data['status_code']}"


def assert_model_equal(model1: Any, model2: Any, exclude_fields: Optional[List[str]] = None) -> None:
    """Assert that two SQLAlchemy models are equal, excluding specified fields.
    
    Args:
        model1: First model
        model2: Second model
        exclude_fields: Optional list of fields to exclude from comparison
    """
    if exclude_fields is None:
        exclude_fields = []
    
    # Add common fields that should be excluded by default
    default_exclude = ["id", "created_on", "updated_on", "deleted_at"]
    exclude_fields = list(set(exclude_fields + default_exclude))
    
    # Get all attributes of the models
    attrs1 = {k: v for k, v in model1.__dict__.items() 
              if not k.startswith('_') and k not in exclude_fields}
    attrs2 = {k: v for k, v in model2.__dict__.items() 
              if not k.startswith('_') and k not in exclude_fields}
    
    # Compare attributes
    for key, value in attrs1.items():
        assert key in attrs2, f"Key '{key}' not found in second model"
        assert attrs2[key] == value, f"Values for key '{key}' differ: {value} != {attrs2[key]}"


def assert_contains_subset(subset: Dict[str, Any], superset: Dict[str, Any]) -> None:
    """Assert that a dictionary contains a subset of another dictionary.
    
    Args:
        subset: Subset dictionary
        superset: Superset dictionary
    """
    for key, value in subset.items():
        assert key in superset, f"Key '{key}' not found in superset"
        assert superset[key] == value, f"Values for key '{key}' differ: {value} != {superset[key]}"


def assert_list_contains_dict_with_key_value(
    items: List[Dict[str, Any]], 
    key: str, 
    value: Any
) -> None:
    """Assert that a list contains a dictionary with a specific key-value pair.
    
    Args:
        items: List of dictionaries
        key: Key to check
        value: Expected value
    """
    for item in items:
        if key in item and item[key] == value:
            return
    
    assert False, f"No item found with {key}={value} in the list"


def assert_response_matches_model(
    response_data: Dict[str, Any], 
    model: Any, 
    exclude_fields: Optional[List[str]] = None
) -> None:
    """Assert that a response matches a model.
    
    Args:
        response_data: Response data
        model: Model to compare against
        exclude_fields: Optional list of fields to exclude from comparison
    """
    if exclude_fields is None:
        exclude_fields = []
    
    # Add common fields that should be excluded by default
    default_exclude = ["created_on", "updated_on", "deleted_at"]
    exclude_fields = list(set(exclude_fields + default_exclude))
    
    # Get all attributes of the model
    model_attrs = {k: v for k, v in model.__dict__.items() 
                  if not k.startswith('_') and k not in exclude_fields}
    
    # Compare attributes
    for key, value in model_attrs.items():
        if key in response_data:
            assert response_data[key] == value, f"Values for key '{key}' differ: {value} != {response_data[key]}"
