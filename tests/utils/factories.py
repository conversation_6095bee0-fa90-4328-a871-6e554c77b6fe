"""Test data factories for creating test objects."""
import random
import string
from datetime import datetime, timed<PERSON>ta
from typing import List, Optional, Dict, Any, Union

from sqlalchemy.orm import Session

from api.models.base import CampaignDB, TestCaseDB, OrganizationDB
from api.models.mitre import MitreVersion, MitreTechnique, MitreTactic
from api.models.user import User, User<PERSON><PERSON>


def random_string(length: int = 10) -> str:
    """Generate a random string of fixed length."""
    letters = string.ascii_lowercase
    return ''.join(random.choice(letters) for _ in range(length))


class UserFactory:
    """Factory for creating User objects."""

    @staticmethod
    def create(
        db_session: Session,
        username: Optional[str] = None,
        email: Optional[str] = None,
        role: UserRole = UserRole.ANALYST,
        password: str = "password123",
        commit: bool = True
    ) -> User:
        """Create a User object.
        
        Args:
            db_session: SQLAlchemy session
            username: Optional username (random if not provided)
            email: Optional email (random if not provided)
            role: User role (default: ANALYST)
            password: Password (default: "password123")
            commit: Whether to commit the session (default: True)
            
        Returns:
            User: Created User object
        """
        if username is None:
            username = f"user_{random_string(8)}"
        
        if email is None:
            email = f"{username}@example.com"
        
        user = User(
            username=username,
            email=email,
            role=role
        )
        user.set_password(password)
        
        db_session.add(user)
        
        if commit:
            db_session.commit()
            db_session.refresh(user)
        
        return user


class CampaignFactory:
    """Factory for creating Campaign objects."""
    
    @staticmethod
    def create(
        db_session: Session,
        name: Optional[str] = None,
        description: Optional[str] = None,
        status: str = "active",
        organizations: Optional[List[OrganizationDB]] = None,
        commit: bool = True
    ) -> CampaignDB:
        """Create a Campaign object.
        
        Args:
            db_session: SQLAlchemy session
            name: Optional name (random if not provided)
            description: Optional description (random if not provided)
            status: Status (default: "active")
            organizations: Optional list of organizations
            commit: Whether to commit the session (default: True)
            
        Returns:
            CampaignDB: Created Campaign object
        """
        if name is None:
            name = f"Campaign {random_string(8)}"
        
        if description is None:
            description = f"Description for {name}"
        
        campaign = CampaignDB(
            name=name,
            description=description,
            status=status
        )
        
        if organizations:
            campaign.organizations = organizations
        
        db_session.add(campaign)
        
        if commit:
            db_session.commit()
            db_session.refresh(campaign)
        
        return campaign


class TestCaseFactory:
    """Factory for creating TestCase objects."""
    
    @staticmethod
    def create(
        db_session: Session,
        name: Optional[str] = None,
        description: Optional[str] = None,
        campaign_id: Optional[int] = None,
        expected_result: Optional[str] = None,
        mitre_techniques: Optional[List[MitreTechnique]] = None,
        commit: bool = True
    ) -> TestCaseDB:
        """Create a TestCase object.
        
        Args:
            db_session: SQLAlchemy session
            name: Optional name (random if not provided)
            description: Optional description (random if not provided)
            campaign_id: Optional campaign ID
            expected_result: Optional expected result
            mitre_techniques: Optional list of MITRE techniques
            commit: Whether to commit the session (default: True)
            
        Returns:
            TestCaseDB: Created TestCase object
        """
        if name is None:
            name = f"Test Case {random_string(8)}"
        
        if description is None:
            description = f"Description for {name}"
        
        if expected_result is None:
            expected_result = f"Expected result for {name}"
        
        test_case = TestCaseDB(
            name=name,
            description=description,
            campaign_id=campaign_id,
            expected_result=expected_result
        )
        
        if mitre_techniques:
            test_case.mitre_techniques = mitre_techniques
        
        db_session.add(test_case)
        
        if commit:
            db_session.commit()
            db_session.refresh(test_case)
        
        return test_case


class MitreFactory:
    """Factory for creating MITRE objects."""
    
    @staticmethod
    def create_version(
        db_session: Session,
        version: Optional[str] = None,
        name: Optional[str] = None,
        description: Optional[str] = None,
        is_current: bool = True,
        technology_domain: str = "enterprise",
        commit: bool = True
    ) -> MitreVersion:
        """Create a MITRE version.
        
        Args:
            db_session: SQLAlchemy session
            version: Optional version string (random if not provided)
            name: Optional name (random if not provided)
            description: Optional description (random if not provided)
            is_current: Whether this is the current version (default: True)
            technology_domain: Technology domain (default: "enterprise")
            commit: Whether to commit the session (default: True)
            
        Returns:
            MitreVersion: Created MitreVersion object
        """
        if version is None:
            version = f"v{random.randint(1, 20)}.{random.randint(0, 9)}"
        
        if name is None:
            name = f"MITRE ATT&CK {version}"
        
        if description is None:
            description = f"Description for {name}"
        
        mitre_version = MitreVersion(
            version=version,
            name=name,
            description=description,
            import_date=datetime.utcnow(),
            is_current=is_current,
            technology_domain=technology_domain
        )
        
        db_session.add(mitre_version)
        
        if commit:
            db_session.commit()
            db_session.refresh(mitre_version)
        
        return mitre_version
    
    @staticmethod
    def create_tactic(
        db_session: Session,
        external_id: Optional[str] = None,
        name: Optional[str] = None,
        description: Optional[str] = None,
        version_id: Optional[int] = None,
        commit: bool = True
    ) -> MitreTactic:
        """Create a MITRE tactic.
        
        Args:
            db_session: SQLAlchemy session
            external_id: Optional external ID (random if not provided)
            name: Optional name (random if not provided)
            description: Optional description (random if not provided)
            version_id: Optional version ID
            commit: Whether to commit the session (default: True)
            
        Returns:
            MitreTactic: Created MitreTactic object
        """
        if external_id is None:
            external_id = f"TA{random.randint(1000, 9999)}"
        
        if name is None:
            name = f"Tactic {random_string(8)}"
        
        if description is None:
            description = f"Description for {name}"
        
        tactic = MitreTactic(
            external_id=external_id,
            name=name,
            description=description,
            version_id=version_id
        )
        
        db_session.add(tactic)
        
        if commit:
            db_session.commit()
            db_session.refresh(tactic)
        
        return tactic
    
    @staticmethod
    def create_technique(
        db_session: Session,
        technique_id: Optional[str] = None,
        name: Optional[str] = None,
        description: Optional[str] = None,
        version_id: Optional[int] = None,
        detection: Optional[str] = None,
        platforms: Optional[List[str]] = None,
        tactics: Optional[List[MitreTactic]] = None,
        commit: bool = True
    ) -> MitreTechnique:
        """Create a MITRE technique.
        
        Args:
            db_session: SQLAlchemy session
            technique_id: Optional technique ID (random if not provided)
            name: Optional name (random if not provided)
            description: Optional description (random if not provided)
            version_id: Optional version ID
            detection: Optional detection string
            platforms: Optional list of platforms
            tactics: Optional list of tactics
            commit: Whether to commit the session (default: True)
            
        Returns:
            MitreTechnique: Created MitreTechnique object
        """
        if technique_id is None:
            technique_id = f"T{random.randint(1000, 9999)}"
        
        if name is None:
            name = f"Technique {random_string(8)}"
        
        if description is None:
            description = f"Description for {name}"
        
        if detection is None:
            detection = f"Detection methods for {name}"
        
        if platforms is None:
            platforms = ["Windows", "macOS", "Linux"]
        
        technique = MitreTechnique(
            technique_id=technique_id,
            name=name,
            description=description,
            version_id=version_id,
            detection=detection,
            platforms=platforms
        )
        
        if tactics:
            technique.tactics = tactics
        
        db_session.add(technique)
        
        if commit:
            db_session.commit()
            db_session.refresh(technique)
        
        return technique


class OrganizationFactory:
    """Factory for creating Organization objects."""
    
    @staticmethod
    def create(
        db_session: Session,
        name: Optional[str] = None,
        description: Optional[str] = None,
        commit: bool = True
    ) -> OrganizationDB:
        """Create an Organization object.
        
        Args:
            db_session: SQLAlchemy session
            name: Optional name (random if not provided)
            description: Optional description (random if not provided)
            commit: Whether to commit the session (default: True)
            
        Returns:
            OrganizationDB: Created Organization object
        """
        if name is None:
            name = f"Organization {random_string(8)}"
        
        if description is None:
            description = f"Description for {name}"
        
        organization = OrganizationDB(
            name=name,
            description=description
        )
        
        db_session.add(organization)
        
        if commit:
            db_session.commit()
            db_session.refresh(organization)
        
        return organization
