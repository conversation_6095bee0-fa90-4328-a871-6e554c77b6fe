"""Integration tests for the web interface using Selenium."""
import pytest
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def test_home_page(selenium):
    """Test that the home page loads correctly."""
    selenium.get("http://localhost:5000")
    assert "Attack Path Visualization" in selenium.title
    
    # Check form elements
    textarea = selenium.find_element(By.NAME, "technique_ids")
    assert textarea.is_displayed()
    
    submit_button = selenium.find_element(By.CSS_SELECTOR, "button[type='submit']")
    assert submit_button.is_displayed()
    assert "Analyze Path" in submit_button.text

def test_analyze_endpoint(selenium):
    """Test the analyze endpoint with a valid technique ID."""
    selenium.get("http://localhost:5000")
    
    # Enter technique ID
    textarea = selenium.find_element(By.NAME, "technique_ids")
    textarea.send_keys("T1566")
    
    # Submit form
    submit_button = selenium.find_element(By.CSS_SELECTOR, "button[type='submit']")
    submit_button.click()
    
    # Wait for graph to appear
    graph_div = WebDriverWait(selenium, 10).until(
        EC.presence_of_element_located((By.ID, "graph"))
    )
    assert graph_div.is_displayed()
    
    # Check for technique details
    technique_details = selenium.find_element(By.CLASS_NAME, "technique-details")
    assert "Phishing" in technique_details.text
    assert "T1566" in technique_details.text

def test_dark_mode_toggle(selenium):
    """Test the dark mode toggle functionality."""
    selenium.get("http://localhost:5000")
    
    # Get initial theme
    html = selenium.find_element(By.TAG_NAME, "html")
    initial_theme = html.get_attribute("data-bs-theme")
    assert initial_theme == "dark"
    
    # Click toggle button
    toggle_button = selenium.find_element(By.CLASS_NAME, "theme-toggle").find_element(By.TAG_NAME, "button")
    toggle_button.click()
    
    # Check if theme changed
    new_theme = html.get_attribute("data-bs-theme")
    assert new_theme == "light"
    
    # Toggle back
    toggle_button.click()
    final_theme = html.get_attribute("data-bs-theme")
    assert final_theme == "dark"

def test_error_handling(selenium):
    """Test error handling for invalid input."""
    selenium.get("http://localhost:5000")
    
    # Submit empty form
    submit_button = selenium.find_element(By.CSS_SELECTOR, "button[type='submit']")
    submit_button.click()
    
    # Check for error message
    error_div = selenium.find_element(By.CLASS_NAME, "alert-danger")
    assert "Please enter at least one technique ID" in error_div.text
