"""
Tests for the advanced tagging system feature
"""
import pytest
from fastapi.testclient import TestClient
from api.main import app
from models.tag import (
    TagCreate, TagUpdate, TagCategoryCreate, TagCategoryUpdate,
    TagHierarchyCreate, TagRelationCreate, TagResourceAssociation,
    TagPropagationRuleCreate, TagBulkOperation
)
from datetime import datetime
import json

client = TestClient(app)

@pytest.fixture
def auth_headers():
    """Fixture for authentication headers"""
    # This is a placeholder - in a real test, you would get a valid token
    return {"Authorization": "Bearer test_token"}

@pytest.fixture
def sample_tag_category_data():
    """Fixture for sample tag category data"""
    return {
        "name": "Test Category",
        "description": "Test category description",
        "color": "#3498db",
        "icon": "tag"
    }

@pytest.fixture
def sample_tag_data():
    """Fixture for sample tag data"""
    return {
        "name": "Test Tag",
        "description": "Test description",
        "color": "#FF5733"
    }

@pytest.fixture
def sample_tag_propagation_rule_data():
    """Fixture for sample tag propagation rule data"""
    return {
        "source_type": "campaign",
        "target_type": "test_case",
        "relation_field": "campaign_id",
        "is_active": True,
        "description": "Propagate tags from campaigns to test cases"
    }

# Tag Category Tests
def test_get_tag_categories(auth_headers):
    """Test getting all tag categories"""
    response = client.get("/api/v2/tags/categories", headers=auth_headers)
    assert response.status_code == 200
    assert isinstance(response.json(), list)

def test_create_tag_category(sample_tag_category_data, auth_headers):
    """Test creating a tag category"""
    response = client.post("/api/v2/tags/categories", json=sample_tag_category_data, headers=auth_headers)
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == sample_tag_category_data["name"]
    assert data["description"] == sample_tag_category_data["description"]
    assert data["color"] == sample_tag_category_data["color"]
    assert data["icon"] == sample_tag_category_data["icon"]
    assert "id" in data
    assert "created_at" in data

def test_get_tag_category(auth_headers):
    """Test getting a specific tag category"""
    # First create a category
    category_data = {
        "name": "Category for Get Test",
        "description": "Test category for get test",
        "color": "#3498db",
        "icon": "tag"
    }
    create_response = client.post("/api/v2/tags/categories", json=category_data, headers=auth_headers)
    category_id = create_response.json()["id"]
    
    # Now get the category
    response = client.get(f"/api/v2/tags/categories/{category_id}", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == category_id
    assert data["name"] == category_data["name"]

def test_update_tag_category(auth_headers):
    """Test updating a tag category"""
    # First create a category
    category_data = {
        "name": "Category for Update Test",
        "description": "Test category for update test",
        "color": "#3498db",
        "icon": "tag"
    }
    create_response = client.post("/api/v2/tags/categories", json=category_data, headers=auth_headers)
    category_id = create_response.json()["id"]
    
    # Now update the category
    update_data = {
        "name": "Updated Category Name",
        "description": "Updated description",
        "color": "#e74c3c",
        "icon": "flag"
    }
    response = client.put(f"/api/v2/tags/categories/{category_id}", json=update_data, headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == category_id
    assert data["name"] == update_data["name"]
    assert data["description"] == update_data["description"]
    assert data["color"] == update_data["color"]
    assert data["icon"] == update_data["icon"]

def test_delete_tag_category(auth_headers):
    """Test deleting a tag category"""
    # First create a category
    category_data = {
        "name": "Category for Delete Test",
        "description": "Test category for delete test",
        "color": "#3498db",
        "icon": "tag"
    }
    create_response = client.post("/api/v2/tags/categories", json=category_data, headers=auth_headers)
    category_id = create_response.json()["id"]
    
    # Now delete the category
    response = client.delete(f"/api/v2/tags/categories/{category_id}", headers=auth_headers)
    assert response.status_code == 204
    
    # Verify it's deleted
    get_response = client.get(f"/api/v2/tags/categories/{category_id}", headers=auth_headers)
    assert get_response.status_code == 404

# Tag Tests
def test_get_tags(auth_headers):
    """Test getting all tags"""
    response = client.get("/api/v2/tags", headers=auth_headers)
    assert response.status_code == 200
    assert isinstance(response.json(), list)

def test_create_tag(sample_tag_data, auth_headers):
    """Test creating a tag"""
    response = client.post("/api/v2/tags", json=sample_tag_data, headers=auth_headers)
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == sample_tag_data["name"]
    assert data["description"] == sample_tag_data["description"]
    assert data["color"] == sample_tag_data["color"]
    assert "id" in data
    assert "created_at" in data

# Tag Hierarchy Tests
def test_create_tag_hierarchy(auth_headers):
    """Test creating a tag hierarchy"""
    # First create two tags
    tag1_data = {
        "name": "Parent Tag",
        "description": "Parent tag for hierarchy test",
        "color": "#3498db"
    }
    tag2_data = {
        "name": "Child Tag",
        "description": "Child tag for hierarchy test",
        "color": "#e74c3c"
    }
    tag1_response = client.post("/api/v2/tags", json=tag1_data, headers=auth_headers)
    tag2_response = client.post("/api/v2/tags", json=tag2_data, headers=auth_headers)
    tag1_id = tag1_response.json()["id"]
    tag2_id = tag2_response.json()["id"]
    
    # Now create a hierarchy
    hierarchy_data = {
        "parent_id": tag1_id,
        "child_id": tag2_id
    }
    response = client.post("/api/v2/tags/hierarchy", json=hierarchy_data, headers=auth_headers)
    assert response.status_code == 201
    data = response.json()
    assert "message" in data
    assert "successfully" in data["message"].lower()

# Tag Relation Tests
def test_create_tag_relation(auth_headers):
    """Test creating a tag relation"""
    # First create two tags
    tag1_data = {
        "name": "Related Tag 1",
        "description": "First tag for relation test",
        "color": "#3498db"
    }
    tag2_data = {
        "name": "Related Tag 2",
        "description": "Second tag for relation test",
        "color": "#e74c3c"
    }
    tag1_response = client.post("/api/v2/tags", json=tag1_data, headers=auth_headers)
    tag2_response = client.post("/api/v2/tags", json=tag2_data, headers=auth_headers)
    tag1_id = tag1_response.json()["id"]
    tag2_id = tag2_response.json()["id"]
    
    # Now create a relation
    relation_data = {
        "tag_id": tag1_id,
        "related_tag_id": tag2_id,
        "relation_type": "similar"
    }
    response = client.post("/api/v2/tags/relations", json=relation_data, headers=auth_headers)
    assert response.status_code == 201
    data = response.json()
    assert "message" in data
    assert "successfully" in data["message"].lower()

# Tag Resource Association Tests
def test_associate_tag_with_resource(auth_headers):
    """Test associating a tag with a resource"""
    # First create a tag
    tag_data = {
        "name": "Resource Tag",
        "description": "Tag for resource association test",
        "color": "#3498db"
    }
    tag_response = client.post("/api/v2/tags", json=tag_data, headers=auth_headers)
    tag_id = tag_response.json()["id"]
    
    # Now associate it with a resource
    association_data = {
        "tag_id": tag_id,
        "resource_type": "test_case",
        "resource_id": 123
    }
    response = client.post("/api/v2/tags/associate", json=association_data, headers=auth_headers)
    assert response.status_code == 201
    data = response.json()
    assert "message" in data
    assert "successfully" in data["message"].lower()

# Bulk Operations Tests
def test_bulk_tag_operation(auth_headers):
    """Test bulk tag operations"""
    # First create some tags
    tag1_data = {
        "name": "Bulk Tag 1",
        "description": "First tag for bulk operation test",
        "color": "#3498db"
    }
    tag2_data = {
        "name": "Bulk Tag 2",
        "description": "Second tag for bulk operation test",
        "color": "#e74c3c"
    }
    tag1_response = client.post("/api/v2/tags", json=tag1_data, headers=auth_headers)
    tag2_response = client.post("/api/v2/tags", json=tag2_data, headers=auth_headers)
    tag1_id = tag1_response.json()["id"]
    tag2_id = tag2_response.json()["id"]
    
    # Now perform a bulk operation
    bulk_data = {
        "operation_type": "associate",
        "tag_ids": [tag1_id, tag2_id],
        "resources": [
            {
                "resource_type": "test_case",
                "resource_id": 123
            },
            {
                "resource_type": "test_case",
                "resource_id": 124
            }
        ]
    }
    response = client.post("/api/v2/tags/bulk", json=bulk_data, headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "successfully" in data["message"].lower()

# Tag Analytics Tests
def test_get_tag_analytics(auth_headers):
    """Test getting tag analytics"""
    response = client.get("/api/v2/tags/analytics", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert "total_tags" in data
    assert "tags_by_category" in data
    assert "top_tags" in data
    assert "resource_types" in data

# Tag Propagation Rule Tests
def test_get_tag_propagation_rules(auth_headers):
    """Test getting all tag propagation rules"""
    response = client.get("/api/v2/tags/propagation-rules", headers=auth_headers)
    assert response.status_code == 200
    assert isinstance(response.json(), list)

def test_create_tag_propagation_rule(sample_tag_propagation_rule_data, auth_headers):
    """Test creating a tag propagation rule"""
    response = client.post("/api/v2/tags/propagation-rules", json=sample_tag_propagation_rule_data, headers=auth_headers)
    assert response.status_code == 201
    data = response.json()
    assert data["source_type"] == sample_tag_propagation_rule_data["source_type"]
    assert data["target_type"] == sample_tag_propagation_rule_data["target_type"]
    assert data["relation_field"] == sample_tag_propagation_rule_data["relation_field"]
    assert data["is_active"] == sample_tag_propagation_rule_data["is_active"]
    assert data["description"] == sample_tag_propagation_rule_data["description"]
    assert "id" in data
    assert "created_at" in data

def test_get_tag_propagation_rule(sample_tag_propagation_rule_data, auth_headers):
    """Test getting a specific tag propagation rule"""
    # First create a rule
    create_response = client.post("/api/v2/tags/propagation-rules", json=sample_tag_propagation_rule_data, headers=auth_headers)
    rule_id = create_response.json()["id"]
    
    # Now get the rule
    response = client.get(f"/api/v2/tags/propagation-rules/{rule_id}", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == rule_id
    assert data["source_type"] == sample_tag_propagation_rule_data["source_type"]
    assert data["target_type"] == sample_tag_propagation_rule_data["target_type"]

def test_update_tag_propagation_rule(sample_tag_propagation_rule_data, auth_headers):
    """Test updating a tag propagation rule"""
    # First create a rule
    create_response = client.post("/api/v2/tags/propagation-rules", json=sample_tag_propagation_rule_data, headers=auth_headers)
    rule_id = create_response.json()["id"]
    
    # Now update the rule
    update_data = {
        "source_type": "assessment",
        "target_type": "campaign",
        "relation_field": "assessment_id",
        "is_active": False,
        "description": "Updated description"
    }
    response = client.put(f"/api/v2/tags/propagation-rules/{rule_id}", json=update_data, headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == rule_id
    assert data["source_type"] == update_data["source_type"]
    assert data["target_type"] == update_data["target_type"]
    assert data["relation_field"] == update_data["relation_field"]
    assert data["is_active"] == update_data["is_active"]
    assert data["description"] == update_data["description"]

def test_delete_tag_propagation_rule(sample_tag_propagation_rule_data, auth_headers):
    """Test deleting a tag propagation rule"""
    # First create a rule
    create_response = client.post("/api/v2/tags/propagation-rules", json=sample_tag_propagation_rule_data, headers=auth_headers)
    rule_id = create_response.json()["id"]
    
    # Now delete the rule
    response = client.delete(f"/api/v2/tags/propagation-rules/{rule_id}", headers=auth_headers)
    assert response.status_code == 204
    
    # Verify it's deleted
    get_response = client.get(f"/api/v2/tags/propagation-rules/{rule_id}", headers=auth_headers)
    assert get_response.status_code == 404

def test_propagate_tags(auth_headers):
    """Test manually triggering tag propagation"""
    response = client.post("/api/v2/tags/propagate?resource_type=campaign&resource_id=123", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert "message" in data 