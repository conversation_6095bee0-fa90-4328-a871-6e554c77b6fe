"""
Tests for the Test Execution API.

This module contains tests specifically focused on test execution functionality
within the assessment management system.
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from api.main import app
from api.models.assessment import Assessment, TestExecution
from api.models.schemas.assessment import AssessmentCreate, TestExecutionCreate
from api.services.assessment import create_assessment, create_test_execution, get_test_execution_by_id
from api.auth.utils import create_access_token

client = TestClient(app)


@pytest.fixture
def db_session(monkeypatch):
    """Create a test database session."""
    # This would typically use a test database
    from api.database import get_db
    db = next(get_db())
    yield db


@pytest.fixture
def admin_token():
    """Create an admin token for testing."""
    access_token = create_access_token(
        data={"sub": "<EMAIL>", "role": "admin"},
        expires_delta=timedelta(minutes=30)
    )
    return access_token


@pytest.fixture
def test_assessment(db_session):
    """Create a test assessment."""
    assessment_data = AssessmentCreate(
        name="Test Assessment for Executions",
        description="Test assessment for execution API testing",
        target_system="Test System",
        assessment_type="vulnerability",
        status="in_progress",
        start_date=datetime.utcnow(),
        end_date=datetime.utcnow() + timedelta(days=7),
        campaign_id=1
    )
    
    assessment = create_assessment(db_session, assessment_data, user_id=1)
    return assessment


@pytest.fixture
def test_execution(db_session, test_assessment):
    """Create a test execution."""
    execution_data = TestExecutionCreate(
        test_case_id=1,
        assessment_id=test_assessment.id,
        result="pass",
        notes="Test execution notes",
        evidence="Test evidence"
    )
    
    execution = create_test_execution(db_session, execution_data, user_id=1)
    return execution


def test_create_test_execution(admin_token, test_assessment):
    """Test creating a new test execution."""
    execution_data = {
        "test_case_id": 2,
        "assessment_id": test_assessment.id,
        "result": "fail",
        "notes": "Failed test execution for testing",
        "evidence": "Evidence of failure"
    }
    
    response = client.post(
        "/api/v1/assessments/executions",
        json=execution_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 201
    assert response.json()["test_case_id"] == execution_data["test_case_id"]
    assert response.json()["result"] == execution_data["result"]
    assert response.json()["notes"] == execution_data["notes"]
    assert response.json()["evidence"] == execution_data["evidence"]


def test_create_multiple_executions(admin_token, test_assessment):
    """Test creating multiple test executions for the same test case."""
    # Create first execution (pass)
    execution_data_1 = {
        "test_case_id": 3,
        "assessment_id": test_assessment.id,
        "result": "pass",
        "notes": "First execution - passed",
        "evidence": "Evidence for first execution"
    }
    
    response = client.post(
        "/api/v1/assessments/executions",
        json=execution_data_1,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 201
    
    # Create second execution (fail) for the same test case
    execution_data_2 = {
        "test_case_id": 3,
        "assessment_id": test_assessment.id,
        "result": "fail",
        "notes": "Second execution - failed",
        "evidence": "Evidence for second execution"
    }
    
    response = client.post(
        "/api/v1/assessments/executions",
        json=execution_data_2,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 201
    
    # Get all executions for this assessment
    response = client.get(
        f"/api/v1/assessments/{test_assessment.id}/executions",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    
    # Find the executions for test case 3
    executions = [e for e in response.json() if e["test_case_id"] == 3]
    assert len(executions) == 2
    
    # Verify we have both a pass and fail result
    results = [e["result"] for e in executions]
    assert "pass" in results
    assert "fail" in results


def test_get_test_execution(admin_token, test_execution):
    """Test getting a specific test execution."""
    response = client.get(
        f"/api/v1/assessments/executions/{test_execution.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert response.json()["id"] == test_execution.id
    assert response.json()["test_case_id"] == test_execution.test_case_id
    assert response.json()["result"] == test_execution.result
    assert response.json()["notes"] == test_execution.notes
    assert response.json()["evidence"] == test_execution.evidence


def test_update_test_execution(admin_token, test_execution):
    """Test updating a test execution."""
    update_data = {
        "result": "partial",
        "notes": "Updated test execution notes",
        "evidence": "Updated evidence"
    }
    
    response = client.put(
        f"/api/v1/assessments/executions/{test_execution.id}",
        json=update_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert response.json()["id"] == test_execution.id
    assert response.json()["result"] == update_data["result"]
    assert response.json()["notes"] == update_data["notes"]
    assert response.json()["evidence"] == update_data["evidence"]


def test_delete_test_execution(admin_token, test_execution):
    """Test deleting a test execution."""
    response = client.delete(
        f"/api/v1/assessments/executions/{test_execution.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 204
    
    # Verify it's gone
    response = client.get(
        f"/api/v1/assessments/executions/{test_execution.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 404


def test_get_all_executions_for_assessment(admin_token, test_assessment):
    """Test getting all test executions for an assessment."""
    # Create several test executions
    for i in range(3):
        execution_data = {
            "test_case_id": i + 10,  # Use different test cases
            "assessment_id": test_assessment.id,
            "result": "pass" if i % 2 == 0 else "fail",
            "notes": f"Test execution {i+1}",
            "evidence": f"Evidence {i+1}"
        }
        
        response = client.post(
            "/api/v1/assessments/executions",
            json=execution_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 201
    
    # Get all executions for this assessment
    response = client.get(
        f"/api/v1/assessments/{test_assessment.id}/executions",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    
    # There should be at least 3 executions (potentially more from other tests)
    executions = response.json()
    assert len(executions) >= 3
    
    # Check for the test cases we just created
    test_case_ids = [e["test_case_id"] for e in executions]
    for i in range(3):
        assert i + 10 in test_case_ids


def test_get_executions_with_filtering(admin_token, test_assessment):
    """Test getting test executions with filters."""
    # Create some test executions with different results
    result_types = ["pass", "fail", "partial", "pass"]
    for i, result in enumerate(result_types):
        execution_data = {
            "test_case_id": i + 20,  # Use different test cases
            "assessment_id": test_assessment.id,
            "result": result,
            "notes": f"Test execution with {result} result",
            "evidence": f"Evidence for {result} test"
        }
        
        response = client.post(
            "/api/v1/assessments/executions",
            json=execution_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 201
    
    # Filter by pass result
    response = client.get(
        f"/api/v1/assessments/{test_assessment.id}/executions?result=pass",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    
    # All returned executions should have result "pass"
    pass_executions = response.json()
    for execution in pass_executions:
        assert execution["result"] == "pass"
    
    # Verify we have at least the 2 "pass" executions we created
    pass_test_cases = [e["test_case_id"] for e in pass_executions]
    assert 20 in pass_test_cases or 23 in pass_test_cases
    
    # Filter by fail result
    response = client.get(
        f"/api/v1/assessments/{test_assessment.id}/executions?result=fail",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    
    # All returned executions should have result "fail"
    fail_executions = response.json()
    for execution in fail_executions:
        assert execution["result"] == "fail"
    
    # Verify we have at least the "fail" execution we created
    fail_test_cases = [e["test_case_id"] for e in fail_executions]
    assert 21 in fail_test_cases


def test_get_latest_execution_for_test_case(admin_token, test_assessment):
    """Test getting the latest execution for a test case."""
    # Create multiple executions for the same test case
    test_case_id = 30
    results = ["fail", "partial", "pass"]
    
    for result in results:
        execution_data = {
            "test_case_id": test_case_id,
            "assessment_id": test_assessment.id,
            "result": result,
            "notes": f"Test execution with {result} result",
            "evidence": f"Evidence for {result} test"
        }
        
        response = client.post(
            "/api/v1/assessments/executions",
            json=execution_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 201
        # Small delay to ensure different timestamps
        import time
        time.sleep(0.1)
    
    # Get the latest execution for this test case
    response = client.get(
        f"/api/v1/assessments/{test_assessment.id}/test-cases/{test_case_id}/latest-execution",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    
    # The latest execution should have result "pass" (the last one we created)
    latest = response.json()
    assert latest["test_case_id"] == test_case_id
    assert latest["result"] == "pass"


def test_bulk_create_test_executions(admin_token, test_assessment):
    """Test creating multiple test executions in bulk."""
    bulk_data = {
        "executions": [
            {
                "test_case_id": 40,
                "assessment_id": test_assessment.id,
                "result": "pass",
                "notes": "Bulk execution 1",
                "evidence": "Bulk evidence 1"
            },
            {
                "test_case_id": 41,
                "assessment_id": test_assessment.id,
                "result": "fail",
                "notes": "Bulk execution 2",
                "evidence": "Bulk evidence 2"
            },
            {
                "test_case_id": 42,
                "assessment_id": test_assessment.id,
                "result": "partial",
                "notes": "Bulk execution 3",
                "evidence": "Bulk evidence 3"
            }
        ]
    }
    
    response = client.post(
        "/api/v1/assessments/executions/bulk",
        json=bulk_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 201
    
    # Verify the response contains information about the created executions
    results = response.json()
    assert "created" in results
    assert results["created"] == 3
    
    # Verify the executions were actually created
    for test_case_id in [40, 41, 42]:
        response = client.get(
            f"/api/v1/assessments/{test_assessment.id}/test-cases/{test_case_id}/latest-execution",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 200
        assert response.json()["test_case_id"] == test_case_id
``` 