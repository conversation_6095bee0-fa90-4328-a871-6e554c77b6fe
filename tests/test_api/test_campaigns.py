"""Tests for the campaign API endpoints.

This module contains tests for creating, retrieving, updating, and deleting
security testing campaigns through the API.
"""
import pytest
from typing import Dict, Any, List

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.models.base import CampaignDB, OrganizationDB, TestCaseDB
from tests.utils.assertions import (
    assert_json_response,
    assert_pagination_response
)
from tests.utils.factories import CampaignFactory, OrganizationFactory, TestCaseFactory


@pytest.mark.campaign
@pytest.mark.api
class TestCampaignsAPI:

    def test_create_campaign(
        self,
        client: TestClient,
        test_admin_headers: Dict[str, str],
        test_organization: OrganizationDB
    ):
        """Test creating a new campaign."""
        campaign_data = {
            "name": "Test Campaign Creation",
            "description": "Campaign created during test",
            "status": "active",
            "organization_ids": [test_organization.id]
        }

        response = client.post(
            "/api/v1/campaigns/",
            headers=test_admin_headers,
            json=campaign_data
        )
        assert response.status_code == 201
        data = response.json()

        assert data["name"] == campaign_data["name"]
        assert data["description"] == campaign_data["description"]
        assert data["status"] == campaign_data["status"]
        assert "id" in data
        assert "created_on" in data

        # Verify organizations are linked
        response = client.get(
            f"/api/v1/campaigns/{data['id']}/organizations",
            headers=test_admin_headers
        )
        assert response.status_code == 200
        orgs = response.json()

        assert isinstance(orgs, list)
        assert len(orgs) == 1
        assert orgs[0]["id"] == test_organization.id

    def test_get_campaigns(
        self,
        client: TestClient,
        test_admin_headers: Dict[str, str],
        test_campaign: CampaignDB
    ):
        """Test getting campaigns."""
        response = client.get(
            "/api/v1/campaigns/",
            headers=test_admin_headers
        )
        assert response.status_code == 200
        data = response.json()

        assert_pagination_response(data)
        assert data["total"] >= 1

        # Find our test campaign in the results
        found = False
        for campaign in data["items"]:
            if campaign["id"] == test_campaign.id:
                found = True
                assert campaign["name"] == test_campaign.name
                assert campaign["description"] == test_campaign.description
                assert campaign["status"] == test_campaign.status
                break

        assert found, f"Test campaign {test_campaign.id} not found in response"

    def test_get_campaign_by_id(
        self,
        client: TestClient,
        test_admin_headers: Dict[str, str],
        test_campaign: CampaignDB
    ):
        """Test getting a specific campaign by ID."""
        response = client.get(
            f"/api/v1/campaigns/{test_campaign.id}",
            headers=test_admin_headers
        )
        assert response.status_code == 200
        campaign = response.json()

        assert campaign["id"] == test_campaign.id
        assert campaign["name"] == test_campaign.name
        assert campaign["description"] == test_campaign.description
        assert campaign["status"] == test_campaign.status

    def test_update_campaign(
        self,
        client: TestClient,
        test_admin_headers: Dict[str, str],
        test_campaign: CampaignDB
    ):
        """Test updating a campaign."""
        update_data = {
            "name": "Updated Campaign Name",
            "description": "Updated campaign description",
            "status": "completed"
        }

        response = client.put(
            f"/api/v1/campaigns/{test_campaign.id}",
            headers=test_admin_headers,
            json=update_data
        )
        assert response.status_code == 200
        campaign = response.json()

        assert campaign["id"] == test_campaign.id
        assert campaign["name"] == update_data["name"]
        assert campaign["description"] == update_data["description"]
        assert campaign["status"] == update_data["status"]

    def test_delete_campaign(
        self,
        client: TestClient,
        test_admin_headers: Dict[str, str],
        db_session: Session
    ):
        """Test deleting a campaign."""
        # Create a campaign specifically for deletion
        campaign = CampaignFactory.create(
            db_session=db_session,
            name="Campaign to Delete"
        )

        response = client.delete(
            f"/api/v1/campaigns/{campaign.id}",
            headers=test_admin_headers
        )
        assert response.status_code == 204

        # Verify campaign is deleted
        response = client.get(
            f"/api/v1/campaigns/{campaign.id}",
            headers=test_admin_headers
        )
        assert response.status_code == 404

    def test_restore_campaign(
        self,
        client: TestClient,
        test_admin_headers: Dict[str, str],
        test_campaign: CampaignDB,
        db_session: Session
    ):
        """Test restoring a deleted campaign."""
        # First, soft-delete the campaign
        test_campaign.soft_delete(db_session)

        response = client.post(
            f"/api/v1/campaigns/{test_campaign.id}/restore",
            headers=test_admin_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_campaign.id

        # Verify the campaign was restored
        db_campaign = db_session.query(CampaignDB).filter(
            CampaignDB.id == test_campaign.id
        ).first()

        assert db_campaign.deleted_time is None

    def test_create_campaign_validation(self, client: TestClient):
        """Test campaign creation validation."""
        # Test missing name
        response = client.post(
            "/api/v1/campaigns/",
            json={
                "description": "Test Description",
                "status": "active"
            }
        )
        assert response.status_code == 422  # Validation error

        # Test invalid status
        response = client.post(
            "/api/v1/campaigns/",
            json={
                "name": "Test Campaign",
                "description": "Test Description",
                "status": "invalid_status"
            }
        )
        assert response.status_code == 422  # Validation error

    def test_campaign_with_organizations(
        self,
        client: TestClient,
        test_admin_headers: Dict[str, str],
        db_session: Session
    ):
        """Test campaign with organization associations."""
        # Create organizations
        organizations = [
            OrganizationFactory.create(
                db_session=db_session,
                name=f"Organization {i}",
                description=f"Description {i}"
            )
            for i in range(2)
        ]

        # Create campaign with organizations
        response = client.post(
            "/api/v1/campaigns/",
            headers=test_admin_headers,
            json={
                "name": "Test Campaign",
                "description": "Test Description",
                "status": "active",
                "organization_ids": [org.id for org in organizations]
            }
        )
        assert response.status_code == 201
        data = response.json()

        # Get campaign with organizations
        response = client.get(
            f"/api/v1/campaigns/{data['id']}/organizations",
            headers=test_admin_headers
        )
        assert response.status_code == 200
        orgs = response.json()

        # Verify organizations in response
        assert isinstance(orgs, list)
        assert len(orgs) == 2

        # Test updating organizations
        response = client.put(
            f"/api/v1/campaigns/{data['id']}",
            headers=test_admin_headers,
            json={
                "name": "Test Campaign",
                "description": "Test Description",
                "status": "active",
                "organization_ids": [organizations[0].id]  # Remove one organization
            }
        )
        assert response.status_code == 200

        # Verify organizations after update
        response = client.get(
            f"/api/v1/campaigns/{data['id']}/organizations",
            headers=test_admin_headers
        )
        assert response.status_code == 200
        orgs = response.json()

        assert isinstance(orgs, list)
        assert len(orgs) == 1
        assert orgs[0]["id"] == organizations[0].id

    def test_campaign_with_test_cases(
        self,
        client: TestClient,
        test_admin_headers: Dict[str, str],
        test_campaign: CampaignDB,
        db_session: Session
    ):
        """Test campaign with test cases."""
        # Create test cases for the campaign
        test_cases = [
            TestCaseFactory.create(
                db_session=db_session,
                name=f"Test Case {i}",
                description=f"Description {i}",
                campaign_id=test_campaign.id,
                expected_result=f"Expected {i}"
            )
            for i in range(3)
        ]

        # Get campaign with test cases
        response = client.get(
            f"/api/v1/campaigns/{test_campaign.id}",
            headers=test_admin_headers
        )
        assert response.status_code == 200
        data = response.json()

        # Verify test cases in response
        assert "test_cases" in data
        assert len(data["test_cases"]) == 3

        # Test getting test cases for a campaign
        response = client.get(
            f"/api/v1/campaigns/{test_campaign.id}/test-cases",
            headers=test_admin_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert_pagination_response(data)
        assert data["total"] == 3
        assert all(tc["campaign_id"] == test_campaign.id for tc in data["items"])

    def test_campaign_statistics(
        self,
        client: TestClient,
        test_admin_headers: Dict[str, str],
        test_campaign: CampaignDB,
        db_session: Session
    ):
        """Test campaign statistics endpoint."""
        # Create test cases with different statuses
        statuses = ["pending", "in-progress", "passed", "failed", "blocked"]
        for i, status in enumerate(statuses):
            TestCaseFactory.create(
                db_session=db_session,
                name=f"Test Case {i}",
                description=f"Description {i}",
                campaign_id=test_campaign.id,
                expected_result=f"Expected {i}",
                status=status
            )

        # Get campaign statistics
        response = client.get(
            f"/api/v1/campaigns/{test_campaign.id}/statistics",
            headers=test_admin_headers
        )
        assert response.status_code == 200
        data = response.json()

        # Verify statistics
        assert "total_test_cases" in data
        assert data["total_test_cases"] == 5
        assert "status_counts" in data
        assert len(data["status_counts"]) == 5
        assert data["status_counts"]["pending"] == 1
        assert data["status_counts"]["in-progress"] == 1
        assert data["status_counts"]["passed"] == 1
        assert data["status_counts"]["failed"] == 1
        assert data["status_counts"]["blocked"] == 1

    def test_add_organization_to_campaign(
        self,
        client: TestClient,
        test_admin_headers: Dict[str, str],
        test_campaign: CampaignDB,
        db_session: Session
    ):
        """Test adding an organization to a campaign."""
        # Create a new organization
        new_org = OrganizationFactory.create(
            db_session=db_session,
            name="New Organization"
        )

        response = client.post(
            f"/api/v1/campaigns/{test_campaign.id}/organizations",
            headers=test_admin_headers,
            json={"organization_id": new_org.id}
        )
        assert response.status_code == 200

        # Verify organization was added
        response = client.get(
            f"/api/v1/campaigns/{test_campaign.id}/organizations",
            headers=test_admin_headers
        )
        assert response.status_code == 200
        orgs = response.json()

        assert isinstance(orgs, list)

        # Find our new organization in the results
        found = False
        for org in orgs:
            if org["id"] == new_org.id:
                found = True
                assert org["name"] == new_org.name
                break

        assert found, f"New organization {new_org.id} not found in campaign organizations"

    def test_filter_campaigns_by_status(
        self,
        client: TestClient,
        test_admin_headers: Dict[str, str],
        db_session: Session
    ):
        """Test filtering campaigns by status."""
        # Create campaigns with different statuses
        active_campaign = CampaignFactory.create(
            db_session=db_session,
            name="Active Campaign",
            status="active"
        )

        completed_campaign = CampaignFactory.create(
            db_session=db_session,
            name="Completed Campaign",
            status="completed"
        )

        # Filter by active status
        response = client.get(
            "/api/v1/campaigns/?status=active",
            headers=test_admin_headers
        )
        assert response.status_code == 200
        data = response.json()

        assert_pagination_response(data)

        # Check that active campaign is in results
        active_found = False
        for campaign in data["items"]:
            if campaign["id"] == active_campaign.id:
                active_found = True
                assert campaign["status"] == "active"
            # Ensure completed campaign is not in results with active status
            if campaign["id"] == completed_campaign.id:
                assert campaign["status"] != "completed"

        assert active_found, "Active campaign not found in filtered results"

        # Filter by completed status
        response = client.get(
            "/api/v1/campaigns/?status=completed",
            headers=test_admin_headers
        )
        assert response.status_code == 200
        data = response.json()

        assert_pagination_response(data)

        # Check that completed campaign is in results
        completed_found = False
        for campaign in data["items"]:
            if campaign["id"] == completed_campaign.id:
                completed_found = True
                assert campaign["status"] == "completed"
            # Ensure active campaign is not in results with completed status
            if campaign["id"] == active_campaign.id:
                assert campaign["status"] != "active"

        assert completed_found, "Completed campaign not found in filtered results"

    def test_pagination(
        self,
        client: TestClient,
        test_admin_headers: Dict[str, str],
        db_session: Session
    ):
        """Test campaign pagination."""
        # Create multiple campaigns
        for i in range(5):
            CampaignFactory.create(
                db_session=db_session,
                name=f"Pagination Campaign {i+1}"
            )

        # Test first page with 2 items per page
        response = client.get(
            "/api/v1/campaigns/?page=1&size=2",
            headers=test_admin_headers
        )
        assert response.status_code == 200
        data = response.json()

        assert_pagination_response(data)
        assert len(data["items"]) == 2

        # Test second page
        response = client.get(
            "/api/v1/campaigns/?page=2&size=2",
            headers=test_admin_headers
        )
        assert response.status_code == 200
        data = response.json()

        assert_pagination_response(data)
        assert len(data["items"]) == 2

        # Ensure first and second page have different items
        first_page_response = client.get(
            "/api/v1/campaigns/?page=1&size=2",
            headers=test_admin_headers
        )
        first_page = first_page_response.json()

        second_page_response = client.get(
            "/api/v1/campaigns/?page=2&size=2",
            headers=test_admin_headers
        )
        second_page = second_page_response.json()

        first_page_ids = [campaign["id"] for campaign in first_page["items"]]
        second_page_ids = [campaign["id"] for campaign in second_page["items"]]

        # Check that there's no overlap between pages
        assert not any(campaign_id in second_page_ids for campaign_id in first_page_ids)
