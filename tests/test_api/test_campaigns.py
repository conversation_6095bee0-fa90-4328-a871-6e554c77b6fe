"""
Tests for the campaign API endpoints.

This module contains tests for creating, retrieving, updating, and deleting
security testing campaigns through the API.
"""
import pytest
from api.models import CampaignDB, OrganizationDB, TestCaseDB
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.main import app
from api.database import get_db

client = TestClient(app)

@pytest.fixture
def db_session(monkeypatch):
    """Create a test database session."""
    # This would typically use a test database
    # For simplicity, we'll use the same database but roll back changes
    from api.database import SessionLocal
    db = SessionLocal()
    try:
        yield db
    finally:
        db.rollback()
        db.close()

@pytest.fixture
def auth_headers():
    """Create authentication headers for API requests."""
    # In a real test, this would authenticate with the API
    # For now, we'll mock the authentication
    return {"Authorization": "Bearer test_token"}

@pytest.fixture
def test_campaign(db_session):
    """Create a test campaign in the database."""
    campaign = CampaignDB(
        name="Test Campaign",
        description="Test campaign for unit tests",
        status="active"
    )
    db_session.add(campaign)
    db_session.commit()
    db_session.refresh(campaign)
    
    yield campaign
    
    # Clean up
    db_session.query(CampaignDB).filter(CampaignDB.id == campaign.id).delete()
    db_session.commit()

def test_create_campaign(db_session, auth_headers, monkeypatch):
    """Test creating a new campaign."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    # Mock the authentication
    # In a real test, this would use a test user
    
    response = client.post(
        "/api/v1/campaigns/",
        json={
            "name": "New Test Campaign",
            "description": "Created during unit test",
            "status": "active"
        },
        headers=auth_headers
    )
    
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == "New Test Campaign"
    assert data["description"] == "Created during unit test"
    assert data["status"] == "active"
    assert "id" in data
    
    # Clean up
    db_session.query(CampaignDB).filter(CampaignDB.id == data["id"]).delete()
    db_session.commit()

def test_get_campaigns(db_session, test_campaign, auth_headers, monkeypatch):
    """Test retrieving all campaigns."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    response = client.get(
        "/api/v1/campaigns/",
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) >= 1
    
    # Check if our test campaign is in the list
    campaign_ids = [c["id"] for c in data]
    assert test_campaign.id in campaign_ids

def test_get_campaign(db_session, test_campaign, auth_headers, monkeypatch):
    """Test retrieving a specific campaign."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    response = client.get(
        f"/api/v1/campaigns/{test_campaign.id}",
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_campaign.id
    assert data["name"] == test_campaign.name
    assert data["description"] == test_campaign.description
    assert data["status"] == test_campaign.status

def test_update_campaign(db_session, test_campaign, auth_headers, monkeypatch):
    """Test updating a campaign."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    response = client.put(
        f"/api/v1/campaigns/{test_campaign.id}",
        json={
            "name": "Updated Campaign",
            "description": "Updated during unit test",
            "status": "inactive"
        },
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_campaign.id
    assert data["name"] == "Updated Campaign"
    assert data["description"] == "Updated during unit test"
    assert data["status"] == "inactive"
    
    # Verify the database was updated
    db_campaign = db_session.query(CampaignDB).filter(CampaignDB.id == test_campaign.id).first()
    assert db_campaign.name == "Updated Campaign"
    assert db_campaign.description == "Updated during unit test"
    assert db_campaign.status == "inactive"

def test_delete_campaign(db_session, test_campaign, auth_headers, monkeypatch):
    """Test deleting a campaign."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    response = client.delete(
        f"/api/v1/campaigns/{test_campaign.id}",
        headers=auth_headers
    )
    
    assert response.status_code == 204
    
    # Verify the campaign was soft-deleted
    db_campaign = db_session.query(CampaignDB).filter(
        CampaignDB.id == test_campaign.id
    ).first()
    
    assert db_campaign.deleted_time is not None

def test_restore_campaign(db_session, test_campaign, auth_headers, monkeypatch):
    """Test restoring a deleted campaign."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    # First, soft-delete the campaign
    test_campaign.soft_delete(db_session)
    
    response = client.post(
        f"/api/v1/campaigns/{test_campaign.id}/restore",
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_campaign.id
    
    # Verify the campaign was restored
    db_campaign = db_session.query(CampaignDB).filter(
        CampaignDB.id == test_campaign.id
    ).first()
    
    assert db_campaign.deleted_time is None

def test_create_campaign_validation(client):
    """Test campaign creation validation"""
    # Test missing name
    response = client.post(
        "/api/v1/campaigns",
        json={
            "description": "Test Description",
            "status": "active"
        }
    )
    assert response.status_code == 422  # Validation error

    # Test invalid status
    response = client.post(
        "/api/v1/campaigns",
        json={
            "name": "Test Campaign",
            "description": "Test Description",
            "status": "invalid_status"
        }
    )
    assert response.status_code == 422  # Validation error

def test_get_campaign_by_id(client, db_session):
    """Test getting a specific campaign by ID"""
    # Create a campaign
    campaign = CampaignDB(name="Test Campaign", description="Test Description")
    db_session.add(campaign)
    db_session.commit()
    db_session.refresh(campaign)
    
    response = client.get(f"/api/v1/campaigns/{campaign.id}")
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == campaign.id
    assert data["name"] == "Test Campaign"
    assert data["description"] == "Test Description"

def test_campaign_with_organizations(client, db_session):
    """Test campaign with organization associations"""
    # Create organizations
    organizations = [
        OrganizationDB(name=f"Organization {i}", description=f"Description {i}")
        for i in range(2)
    ]
    for org in organizations:
        db_session.add(org)
    db_session.commit()
    
    # Create campaign with organizations
    response = client.post(
        "/api/v1/campaigns",
        json={
            "name": "Test Campaign",
            "description": "Test Description",
            "status": "active",
            "organization_ids": [org.id for org in organizations]
        }
    )
    assert response.status_code == 200
    data = response.json()
    
    # Verify organizations in response
    assert "organizations" in data
    assert len(data["organizations"]) == 2
    
    # Verify database state
    campaign_id = data["id"]
    campaign = db_session.query(CampaignDB).filter(CampaignDB.id == campaign_id).first()
    assert len(campaign.organizations) == 2
    
    # Test updating organizations
    response = client.put(
        f"/api/v1/campaigns/{campaign_id}",
        json={
            "name": "Test Campaign",
            "description": "Test Description",
            "status": "active",
            "organization_ids": [organizations[0].id]  # Remove one organization
        }
    )
    assert response.status_code == 200
    
    # Verify database state after update
    db_session.refresh(campaign)
    assert len(campaign.organizations) == 1
    assert campaign.organizations[0].id == organizations[0].id

def test_campaign_with_test_cases(client, db_session):
    """Test campaign with test cases"""
    # Create a campaign
    campaign = CampaignDB(name="Test Campaign", description="Test Description")
    db_session.add(campaign)
    db_session.commit()
    db_session.refresh(campaign)
    
    # Create test cases for the campaign
    test_cases = [
        TestCaseDB(
            name=f"Test Case {i}",
            description=f"Description {i}",
            campaign_id=campaign.id,
            expected_result=f"Expected {i}"
        )
        for i in range(3)
    ]
    for test_case in test_cases:
        db_session.add(test_case)
    db_session.commit()
    
    # Get campaign with test cases
    response = client.get(f"/api/v1/campaigns/{campaign.id}")
    assert response.status_code == 200
    data = response.json()
    
    # Verify test cases in response
    assert "test_cases" in data
    assert len(data["test_cases"]) == 3
    
    # Test getting test cases for a campaign
    response = client.get(f"/api/v1/campaigns/{campaign.id}/test-cases")
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 3
    assert all(tc["campaign_id"] == campaign.id for tc in data)

def test_campaign_statistics(client, db_session):
    """Test campaign statistics endpoint"""
    # Create a campaign
    campaign = CampaignDB(name="Test Campaign", description="Test Description")
    db_session.add(campaign)
    db_session.commit()
    db_session.refresh(campaign)
    
    # Create test cases with different statuses
    statuses = ["pending", "in-progress", "passed", "failed", "blocked"]
    for i, status in enumerate(statuses):
        test_case = TestCaseDB(
            name=f"Test Case {i}",
            description=f"Description {i}",
            campaign_id=campaign.id,
            expected_result=f"Expected {i}",
            status=status
        )
        db_session.add(test_case)
    db_session.commit()
    
    # Get campaign statistics
    response = client.get(f"/api/v1/campaigns/{campaign.id}/statistics")
    assert response.status_code == 200
    data = response.json()
    
    # Verify statistics
    assert "total_test_cases" in data
    assert data["total_test_cases"] == 5
    assert "status_counts" in data
    assert len(data["status_counts"]) == 5
    assert data["status_counts"]["pending"] == 1
    assert data["status_counts"]["in-progress"] == 1
    assert data["status_counts"]["passed"] == 1
    assert data["status_counts"]["failed"] == 1
    assert data["status_counts"]["blocked"] == 1
