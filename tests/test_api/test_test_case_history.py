"""
Tests for the Test Case History API.

This module contains tests for the test case history endpoints.
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.main import app
from api.models.database.test_case import TestCase
from api.models.database.test_case_history import TestCaseHistory
from api.models.schemas.test_case import TestCaseCreate, TestCaseUpdate
from api.services.test_case import create_test_case, update_test_case, get_test_case_history
from api.auth.utils import create_access_token

client = TestClient(app)


@pytest.fixture
def db_session(monkeypatch):
    """Create a test database session."""
    # This would typically use a test database
    from api.database import get_db
    db = next(get_db())
    yield db


@pytest.fixture
def auth_headers():
    """Create authentication headers for API requests."""
    # In a real test, this would authenticate with the API
    # For now, we'll mock the authentication
    token = create_access_token({"sub": "test_user", "role": "admin"})
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def test_test_case(db_session):
    """Create a test test case in the database."""
    test_case_data = TestCaseCreate(
        name="Test SQL Injection",
        description="Test for SQL injection vulnerabilities",
        type="manual",
        status="draft",
        priority="high",
        complexity="moderate",
        steps=["Step 1", "Step 2"],
        expected_result="No vulnerabilities found",
        tags=["security", "injection"],
        mitre_techniques=["T1190"]
    )
    test_case = create_test_case(db_session, test_case_data, user_id=1)
    
    yield test_case
    
    # Clean up
    db_session.query(TestCaseHistory).filter(TestCaseHistory.test_case_id == test_case.id).delete()
    db_session.query(TestCase).filter(TestCase.id == test_case.id).delete()
    db_session.commit()


def test_get_test_case_history(auth_headers, test_test_case, db_session):
    """Test getting history for a test case."""
    # First, update the test case to create a history entry
    update_data = TestCaseUpdate(
        status="active",
        steps=["Step 1", "Step 2", "Step 3"]
    )
    update_test_case(db_session, test_test_case.id, update_data, user_id=1, is_admin=True)
    
    # Now get the history
    response = client.get(
        f"/api/v1/test-cases/{test_test_case.id}/history",
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "history" in data
    assert "total" in data
    assert data["total"] >= 2  # Should have at least 2 entries (create and update)
    
    # Check that the history entries have the expected fields
    for entry in data["history"]:
        assert "id" in entry
        assert "test_case_id" in entry
        assert "version" in entry
        assert "change_type" in entry
        assert "changed_by" in entry
        assert "changed_at" in entry
        assert "data" in entry


def test_get_test_case_history_by_version(auth_headers, test_test_case, db_session):
    """Test getting a specific version of a test case from history."""
    # First, update the test case to create a history entry
    update_data = TestCaseUpdate(
        status="active",
        steps=["Step 1", "Step 2", "Step 3"]
    )
    update_test_case(db_session, test_test_case.id, update_data, user_id=1, is_admin=True)
    
    # Get the history to find the version
    history = get_test_case_history(db_session, test_test_case.id)
    assert len(history) >= 2
    
    # Get the first version
    first_version = history[-1].version  # The oldest entry should be the first version
    
    # Now get the specific version
    response = client.get(
        f"/api/v1/test-cases/{test_test_case.id}/history/{first_version}",
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["version"] == first_version
    assert data["change_type"] == "create"
    assert data["data"]["name"] == "Test SQL Injection"


def test_unauthorized_access(auth_headers, test_test_case):
    """Test that unauthorized users cannot access test case history."""
    # Create a non-admin user token
    token = create_access_token({"sub": "regular_user", "role": "viewer"})
    headers = {"Authorization": f"Bearer {token}"}
    
    # Try to access history
    response = client.get(
        f"/api/v1/test-cases/{test_test_case.id}/history",
        headers=headers
    )
    
    # This should fail with 403 Forbidden since the test case was created by user_id=1
    assert response.status_code == 403
