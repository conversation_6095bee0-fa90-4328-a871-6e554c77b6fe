"""Tests for D3FEND data import functionality."""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from sqlalchemy import text
import json
import os
from datetime import datetime
import logging
from pathlib import Path
from rdflib import Graph, OWL, RDF, RDFS, URIRef, Literal

from api.models.d3fend import D3FENDVersion, D3FENDConcept, d3fend_relationships
from api.utils.d3fend_import import import_d3fend_ontology, import_d3fend_ontology_from_owl
from api.database import Base, engine

# Configure test logging
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_dir / 'test_d3fend_full_import.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@pytest.fixture(scope="function")
def test_data_dir(tmp_path):
    """Create and return a temporary directory for test data."""
    data_dir = tmp_path / "data" / "d3fend"
    data_dir.mkdir(parents=True, exist_ok=True)
    return data_dir

def create_test_dataset(path: Path, num_concepts: int = 3) -> int:
    """Create a test D3FEND dataset with specified number of concepts."""
    g = Graph()
    test_concepts = [
        f"TestConcept{i}" for i in range(num_concepts)
    ]

    # Add test concepts
    for concept in test_concepts:
        uri = f"http://d3fend.mitre.org/ontologies/d3fend.owl#{concept}"
        subject = URIRef(uri)
        g.add((subject, RDF.type, OWL.Class))
        g.add((subject, RDFS.label, Literal(concept)))
        g.add((subject, RDFS.comment, Literal(f"Test definition for {concept}")))

        # Add relationships between concepts for testing
        if concept != test_concepts[0]:  # Link to the first concept
            g.add((subject, RDFS.seeAlso, URIRef(f"http://d3fend.mitre.org/ontologies/d3fend.owl#{test_concepts[0]}")))

    # Create directory and save file
    path.parent.mkdir(parents=True, exist_ok=True)
    g.serialize(str(path), format="xml")
    logger.info(f"Created test dataset at {path} with {num_concepts} concepts")

    return num_concepts

def test_owl_file_validation(db_session: Session, test_data_dir: Path):
    """Test OWL file validation during import."""
    # Test with invalid OWL file
    invalid_path = test_data_dir / "invalid.owl"
    invalid_path.write_text("Invalid OWL content")

    with pytest.raises(Exception):
        import_d3fend_ontology(db_session, str(invalid_path))

    # Test with non-existent file
    with pytest.raises(FileNotFoundError):
        import_d3fend_ontology(db_session, "nonexistent.owl")

    # Test with empty OWL file
    empty_path = test_data_dir / "empty.owl"
    empty_path.write_text("")

    with pytest.raises(Exception):
        import_d3fend_ontology(db_session, str(empty_path))

def test_version_handling(db_session: Session, test_data_dir: Path):
    """Test version creation and updates."""
    dataset_path = test_data_dir / "test_d3fend.owl"

    # Initial import
    create_test_dataset(dataset_path, num_concepts=3)
    import_d3fend_ontology(db_session, str(dataset_path))
    db_session.commit()

    # Verify version created correctly
    version = db_session.query(D3FENDVersion).first()
    assert version is not None
    assert version.is_current is True
    assert version.version == datetime.utcnow().strftime('%Y.%m.%d')

    # Test version update handling
    create_test_dataset(dataset_path, num_concepts=5)
    import_d3fend_ontology(db_session, str(dataset_path))
    db_session.commit()

    # Verify version handling
    versions = db_session.query(D3FENDVersion).all()
    assert len(versions) == 1, "Expected only one version record"
    assert versions[0].is_current is True

def test_relationship_management(db_session: Session, test_data_dir: Path):
    """Test relationship creation and validation."""
    dataset_path = test_data_dir / "test_d3fend.owl"
    create_test_dataset(dataset_path, num_concepts=4)

    import_d3fend_ontology(db_session, str(dataset_path))
    db_session.commit()

    # Verify relationships created correctly
    relationships = db_session.execute(text("""
        SELECT source_id, target_id, relationship_type
        FROM d3fend_relationships
    """)).fetchall()

    assert len(relationships) > 0

    # Check for duplicates
    duplicates = db_session.execute(text("""
        SELECT source_id, target_id, relationship_type, COUNT(*)
        FROM d3fend_relationships
        GROUP BY source_id, target_id, relationship_type
        HAVING COUNT(*) > 1
    """)).fetchall()

    assert len(duplicates) == 0, "Found duplicate relationships"

def test_error_recovery(db_session: Session, test_data_dir: Path):
    """Test error recovery during import process."""
    dataset_path = test_data_dir / "test_d3fend.owl"
    create_test_dataset(dataset_path, num_concepts=3)

    # Use regular transaction to ensure proper rollback
    with pytest.raises(Exception), db_session.begin():
        import_d3fend_ontology(db_session, str(dataset_path))
        raise Exception("Simulated error")

    # Verify no partial data was committed
    assert db_session.query(D3FENDVersion).count() == 0
    assert db_session.query(D3FENDConcept).count() == 0
    assert db_session.execute(text("SELECT COUNT(*) FROM d3fend_relationships")).scalar() == 0

def test_invalid_file_format(db_session: Session, test_data_dir: Path):
    """Test handling of invalid file formats."""
    invalid_path = test_data_dir / "test.json"
    invalid_path.write_text("{}")

    with pytest.raises(ValueError, match="Only OWL file format is currently supported"):
        import_d3fend_ontology(db_session, str(invalid_path))

def test_concept_validation(db_session: Session, test_data_dir: Path):
    """Test concept validation during import."""
    dataset_path = test_data_dir / "test_d3fend.owl"
    g = Graph()

    # Add invalid concept (missing required fields)
    subject = URIRef("http://d3fend.mitre.org/ontologies/d3fend.owl#InvalidConcept")
    g.add((subject, RDF.type, OWL.Class))
    # Deliberately omit RDFS.label and RDFS.comment
    g.serialize(str(dataset_path), format="xml")

    with pytest.raises(ValueError):
        import_d3fend_ontology(db_session, str(dataset_path))

def test_full_d3fend_import(client: TestClient, db_session: Session, test_data_dir: Path):
    """Test importing the complete D3FEND dataset."""
    logger.info("Starting full D3FEND dataset import test")

    # Get initial database state
    initial_state = verify_database_state(db_session)
    logger.info(f"Initial database state: {initial_state}")

    # Get initial API stats
    response = client.get("/api/v1/d3fend/stats")
    assert response.status_code == 200
    initial_stats = response.json()
    logger.info(f"Initial API stats: {initial_stats}")

    # Create test dataset
    dataset_path = test_data_dir / "d3fend.owl"
    expected_concepts = create_test_dataset(dataset_path)
    logger.info(f"Created test dataset with {expected_concepts} concepts")

    try:
        # Import dataset
        logger.debug("Starting D3FEND dataset import")
        import_d3fend_ontology(db_session, str(dataset_path))
        db_session.commit()
        logger.info("Successfully completed D3FEND dataset import")

        # Verify through database queries
        post_import_state = verify_database_state(db_session)
        logger.info(f"Post-import database state: {post_import_state}")

        # Verify through API endpoint
        response = client.get("/api/v1/d3fend/stats")
        assert response.status_code == 200
        stats = response.json()
        logger.info(f"Post-import API stats: {stats}")

        # Verify counts
        assert stats["total_concepts"] == expected_concepts, \
            f"Expected {expected_concepts} concepts, got {stats['total_concepts']}"
        assert stats["total_versions"] >= 1, "Expected at least one version"
        assert stats["current_version"] is not None, "Expected a current version"
        assert stats["current_version_concepts"] == expected_concepts, \
            f"Expected {expected_concepts} concepts in current version"

        # Log verification details
        concept_types = db_session.query(D3FENDConcept.type).distinct().all()
        logger.info(f"Found concept types: {[t[0] for t in concept_types]}")

    except Exception as e:
        logger.error(f"Failed to import D3FEND dataset: {e}", exc_info=True)
        db_session.rollback()
        raise

def test_repeated_imports(client: TestClient, db_session: Session, test_data_dir: Path):
    """Test handling of repeated imports of the same dataset."""
    logger.info("Starting repeated imports test")

    # Create initial dataset with 3 concepts
    dataset_path = test_data_dir / "d3fend.owl"
    initial_concepts = create_test_dataset(dataset_path, num_concepts=3)

    # First import
    import_d3fend_ontology(db_session, str(dataset_path))
    db_session.commit()
    first_import_state = verify_database_state(db_session)
    logger.info(f"First import state: {first_import_state}")

    # Create new dataset with 5 concepts
    updated_concepts = create_test_dataset(dataset_path, num_concepts=5)

    # Second import
    import_d3fend_ontology(db_session, str(dataset_path))
    db_session.commit()
    second_import_state = verify_database_state(db_session)
    logger.info(f"Second import state: {second_import_state}")

    # Verify version handling
    versions = db_session.query(D3FENDVersion).all()
    assert len(versions) == 1, "Expected only one version record"

    # Verify concept updates
    current_concepts = db_session.query(D3FENDConcept).count()
    assert current_concepts == updated_concepts, \
        f"Expected {updated_concepts} concepts after update, got {current_concepts}"

    # Verify through API
    response = client.get("/api/v1/d3fend/stats")
    assert response.status_code == 200
    stats = response.json()

    assert stats["total_versions"] == 1, "Expected exactly one version"
    assert stats["total_concepts"] == updated_concepts, \
        f"Expected {updated_concepts} concepts in API stats"

def test_version_relationships(client: TestClient, db_session: Session, test_data_dir: Path):
    """Test relationship handling across version updates."""
    logger.info("Starting version relationships test")

    # Create and import initial dataset
    dataset_path = test_data_dir / "d3fend.owl"
    initial_concepts = create_test_dataset(dataset_path, num_concepts=3)

    import_d3fend_ontology(db_session, str(dataset_path))
    db_session.commit()

    # Get initial relationship count
    initial_state = verify_database_state(db_session)
    initial_relationships = initial_state["relationships"]

    # Create and import updated dataset
    updated_concepts = create_test_dataset(dataset_path, num_concepts=5)

    import_d3fend_ontology(db_session, str(dataset_path))
    db_session.commit()

    # Verify relationship handling
    final_state = verify_database_state(db_session)
    final_relationships = final_state["relationships"]

    # We expect more relationships with more concepts
    assert final_relationships > initial_relationships, \
        "Expected more relationships after adding concepts"

    # Verify no duplicate relationships
    duplicate_check = db_session.execute(text("""
        SELECT source_id, target_id, relationship_type, COUNT(*)
        FROM d3fend_relationships
        GROUP BY source_id, target_id, relationship_type
        HAVING COUNT(*) > 1
    """)).fetchall()

    assert len(duplicate_check) == 0, "Found duplicate relationships"

@pytest.fixture(autouse=True)
def setup_database():
    """Set up a fresh test database for each test."""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)

def verify_database_state(db: Session) -> dict:
    """Verify the current state of the database."""
    try:
        # First verify the session is working
        result = db.execute(text("SELECT 1")).scalar()
        assert result == 1, "Database connection check failed"
        logger.debug("Database session verified")

        # Query table counts
        version_count = db.query(D3FENDVersion).count()
        concept_count = db.query(D3FENDConcept).count()
        relationship_count = db.execute(
            text("SELECT COUNT(*) FROM d3fend_relationships")
        ).scalar()

        current_version = db.query(D3FENDVersion).filter_by(is_current=True).first()
        current_version_id = current_version.id if current_version else None

        state = {
            "versions": version_count,
            "concepts": concept_count,
            "relationships": relationship_count,
            "current_version_id": current_version_id
        }

        logger.info(f"Database state: {state}")
        return state

    except Exception as e:
        logger.error(f"Failed to verify database state: {e}", exc_info=True)
        raise