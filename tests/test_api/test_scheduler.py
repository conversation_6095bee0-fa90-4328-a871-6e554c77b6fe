"""
Tests for the scheduler API endpoints.

This module contains tests for the scheduler API endpoints, including
getting scheduler status and triggering scheduled tasks.
"""
import pytest
from fastapi.testclient import TestClient

from api.main import app
from api.tasks.scheduler import scheduler


client = TestClient(app)


@pytest.fixture
def admin_token():
    """Get an admin token for testing."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": "admin", "password": "adminpassword"}
    )
    return response.json()["access_token"]


def test_get_scheduler_status(client, admin_token):
    """Test getting scheduler status."""
    response = client.get(
        "/api/v1/scheduler/status",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    
    # Verify response data
    assert "running" in data
    assert "check_interval" in data
    assert "tasks" in data
    assert isinstance(data["tasks"], list)


def test_trigger_scheduled_reports(client, admin_token):
    """Test triggering scheduled reports."""
    response = client.post(
        "/api/v1/scheduler/trigger/reports",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    
    # Verify response data
    assert "message" in data
    assert "triggered successfully" in data["message"]


def test_trigger_scheduled_test_executions(client, admin_token):
    """Test triggering scheduled test executions."""
    response = client.post(
        "/api/v1/scheduler/trigger/test-executions",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    
    # Verify response data
    assert "message" in data
    assert "triggered successfully" in data["message"]


def test_trigger_cleanup_reports(client, admin_token):
    """Test triggering report cleanup."""
    response = client.post(
        "/api/v1/scheduler/trigger/cleanup?days_to_keep=30",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    
    # Verify response data
    assert "message" in data
    assert "triggered successfully" in data["message"]
    assert "30 days" in data["message"]


def test_unauthorized_access(client):
    """Test unauthorized access to scheduler endpoints."""
    # Test without token
    response = client.get("/api/v1/scheduler/status")
    assert response.status_code == 401
    
    # Test with non-admin token
    response = client.post(
        "/api/v1/auth/login",
        data={"username": "user", "password": "userpassword"}
    )
    user_token = response.json()["access_token"]
    
    response = client.get(
        "/api/v1/scheduler/status",
        headers={"Authorization": f"Bearer {user_token}"}
    )
    assert response.status_code == 403
