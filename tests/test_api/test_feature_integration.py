"""Integration tests for feature interactions."""
import pytest
import json
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime

from api.models.database.dashboard import Dashboard
from api.models.database.dashboard_widget import DashboardWidget
from api.models.database.dashboard_filter import DashboardFilter
from api.models.database.test_case import TestCase
from api.models.user import User, UserRole


@pytest.fixture
def integration_setup(db_session):
    """Create test data for integration tests."""
    # Create users
    user = User(
        username="integrationuser",
        email="<EMAIL>",
        role=UserRole.ANALYST
    )
    user.set_password("password123")
    
    admin = User(
        username="integrationadmin",
        email="<EMAIL>",
        role=UserRole.ADMIN
    )
    admin.set_password("adminpass123")
    
    db_session.add(user)
    db_session.add(admin)
    db_session.commit()
    db_session.refresh(user)
    db_session.refresh(admin)
    
    # Create test cases
    test_cases = [
        TestCase(
            name="Integration Test Case 1",
            description="Test case for integration testing",
            type="manual",
            status="active",
            priority="high",
            complexity="moderate",
            prerequisites="Access to the application",
            steps=["Step 1", "Step 2"],
            expected_result="Expected result",
            tags=["integration", "test"],
            mitre_techniques=["T1234"],
            created_by=user.id,
            version="1.0.0"
        ),
        TestCase(
            name="Integration Test Case 2",
            description="Another test case for integration testing",
            type="automated",
            status="draft",
            priority="medium",
            complexity="simple",
            prerequisites="None",
            steps=["Step 1", "Step 2", "Step 3"],
            expected_result="Expected result",
            tags=["integration", "automated"],
            mitre_techniques=["T5678"],
            created_by=admin.id,
            version="1.0.0"
        )
    ]
    
    for test_case in test_cases:
        db_session.add(test_case)
    
    db_session.commit()
    
    # Create dashboard
    dashboard = Dashboard(
        name="Integration Dashboard",
        description="Dashboard for integration testing",
        layout={"type": "grid", "columns": 12},
        is_public=True,
        created_by_id=user.id
    )
    
    db_session.add(dashboard)
    db_session.commit()
    db_session.refresh(dashboard)
    
    # Create dashboard widgets
    widgets = [
        DashboardWidget(
            dashboard_id=dashboard.id,
            widget_type="chart",
            title="Test Cases by Status",
            config={"chart_type": "pie", "data_source": "test_cases", "group_by": "status"},
            position={"x": 0, "y": 0, "w": 6, "h": 4}
        ),
        DashboardWidget(
            dashboard_id=dashboard.id,
            widget_type="table",
            title="Recent Test Cases",
            config={"columns": ["name", "status", "priority"], "data_source": "test_cases", "limit": 10},
            position={"x": 6, "y": 0, "w": 6, "h": 4}
        )
    ]
    
    for widget in widgets:
        db_session.add(widget)
    
    db_session.commit()
    
    # Create dashboard filters
    filters = [
        DashboardFilter(
            dashboard_id=dashboard.id,
            name="Status Filter",
            field="status",
            operator="equals",
            value="active",
            is_global=True,
            applies_to_widgets=None
        ),
        DashboardFilter(
            dashboard_id=dashboard.id,
            name="Priority Filter",
            field="priority",
            operator="in",
            value=["high", "critical"],
            is_global=False,
            applies_to_widgets=[widgets[0].id]
        )
    ]
    
    for filter_obj in filters:
        db_session.add(filter_obj)
    
    db_session.commit()
    
    return {
        "user": user,
        "admin": admin,
        "test_cases": test_cases,
        "dashboard": dashboard,
        "widgets": widgets,
        "filters": filters
    }


class TestFeatureIntegration:
    """Integration tests for feature interactions."""

    def test_dashboard_with_test_case_data(self, test_client, db_session, integration_setup):
        """Test dashboard with test case data."""
        user = integration_setup["user"]
        dashboard = integration_setup["dashboard"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Get dashboard
        response = test_client.get(
            f"/api/v1/dashboards/{dashboard.id}",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        dashboard_data = response.json()
        
        # Get test cases
        response = test_client.get(
            "/api/v1/test-cases",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        test_cases_data = response.json()
        
        # Apply dashboard filters to test case data
        response = test_client.post(
            f"/api/v1/dashboards/{dashboard.id}/apply-filters",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json={"items": test_cases_data}
        )
        
        assert response.status_code == 200
        filtered_data = response.json()["filtered_data"]["items"]
        
        # Only active test cases should remain due to the Status Filter
        assert all(tc["status"] == "active" for tc in filtered_data)
        
        # Export dashboard
        response = test_client.get(
            f"/api/v1/dashboards/{dashboard.id}/export?format=json",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        export_data = response.json()
        
        # Import dashboard with a new name
        response = test_client.post(
            "/api/v1/dashboards/import?new_name=Imported+Integration+Dashboard",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=export_data
        )
        
        assert response.status_code == 201
        imported_dashboard = response.json()
        
        assert imported_dashboard["name"] == "Imported Integration Dashboard"
        assert len(imported_dashboard["widgets"]) == len(integration_setup["widgets"])

    def test_test_case_lifecycle(self, test_client, db_session, integration_setup):
        """Test the complete lifecycle of a test case."""
        user = integration_setup["user"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Create a new test case
        test_case_data = {
            "name": "Lifecycle Test Case",
            "description": "Test case for lifecycle testing",
            "type": "manual",
            "status": "draft",
            "priority": "medium",
            "complexity": "moderate",
            "prerequisites": "None",
            "steps": ["Step 1", "Step 2"],
            "expected_result": "Expected result",
            "tags": ["lifecycle", "test"],
            "mitre_techniques": ["T1234"]
        }
        
        response = test_client.post(
            "/api/v1/test-cases",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=test_case_data
        )
        
        assert response.status_code == 201
        created_test_case = response.json()
        test_case_id = created_test_case["id"]
        
        # Update the test case
        update_data = {
            "status": "active",
            "priority": "high",
            "steps": ["Step 1", "Step 2", "Step 3"],
            "tags": ["lifecycle", "test", "updated"]
        }
        
        response = test_client.put(
            f"/api/v1/test-cases/{test_case_id}",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=update_data
        )
        
        assert response.status_code == 200
        updated_test_case = response.json()
        
        assert updated_test_case["status"] == "active"
        assert updated_test_case["priority"] == "high"
        assert len(updated_test_case["steps"]) == 3
        assert "updated" in updated_test_case["tags"]
        assert updated_test_case["version"] == "1.0.1"  # Version should be incremented
        
        # Create a dashboard to display the test case
        dashboard_data = {
            "name": "Lifecycle Dashboard",
            "description": "Dashboard for lifecycle testing",
            "layout": {"type": "grid", "columns": 12},
            "is_public": True
        }
        
        response = test_client.post(
            "/api/v1/dashboards",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=dashboard_data
        )
        
        assert response.status_code == 201
        dashboard = response.json()
        
        # Create a widget to display the test case
        widget_data = {
            "dashboard_id": dashboard["id"],
            "widget_type": "table",
            "title": "Lifecycle Test Case",
            "config": {
                "columns": ["name", "status", "priority"],
                "data_source": "test_cases",
                "filter": {"id": test_case_id}
            },
            "position": {"x": 0, "y": 0, "w": 12, "h": 4}
        }
        
        response = test_client.post(
            "/api/v1/dashboards/widgets",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=widget_data
        )
        
        assert response.status_code == 201
        widget = response.json()
        
        # Create a filter for the dashboard
        filter_data = {
            "dashboard_id": dashboard["id"],
            "name": "Lifecycle Filter",
            "field": "tags",
            "operator": "contains",
            "value": "lifecycle",
            "is_global": True
        }
        
        response = test_client.post(
            "/api/v1/dashboards/filters",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=filter_data
        )
        
        assert response.status_code == 201
        filter_obj = response.json()
        
        # Get test cases with the filter applied
        test_cases_data = {
            "items": [updated_test_case]
        }
        
        response = test_client.post(
            f"/api/v1/dashboards/{dashboard['id']}/apply-filters",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=test_cases_data
        )
        
        assert response.status_code == 200
        filtered_data = response.json()["filtered_data"]["items"]
        
        assert len(filtered_data) == 1
        assert filtered_data[0]["id"] == test_case_id
        
        # Export the dashboard
        response = test_client.get(
            f"/api/v1/dashboards/{dashboard['id']}/export?format=json",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        export_data = response.json()
        
        # Delete the test case
        response = test_client.delete(
            f"/api/v1/test-cases/{test_case_id}",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 204  # No content
        
        # Verify the test case is soft-deleted
        response = test_client.get(
            f"/api/v1/test-cases/{test_case_id}",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 404  # Not found
        
        # Import the dashboard with a new name
        response = test_client.post(
            "/api/v1/dashboards/import?new_name=Imported+Lifecycle+Dashboard",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=export_data
        )
        
        assert response.status_code == 201
        imported_dashboard = response.json()
        
        assert imported_dashboard["name"] == "Imported Lifecycle Dashboard"
        assert len(imported_dashboard["widgets"]) == 1

    def test_admin_user_permissions(self, test_client, db_session, integration_setup):
        """Test the different permissions between admin and regular users."""
        user = integration_setup["user"]
        admin = integration_setup["admin"]
        test_cases = integration_setup["test_cases"]
        
        # Create authentication tokens
        from api.auth.router import create_access_token
        user_token = create_access_token(data={"sub": user.username})
        admin_token = create_access_token(data={"sub": admin.username})
        
        # Get a test case created by the admin
        admin_test_case = test_cases[1]
        
        # Regular user should not be able to update admin's test case
        update_data = {
            "name": "Attempted Update",
            "description": "This should fail"
        }
        
        response = test_client.put(
            f"/api/v1/test-cases/{admin_test_case.id}",
            headers={
                "Authorization": f"Bearer {user_token}",
                "Content-Type": "application/json"
            },
            json=update_data
        )
        
        assert response.status_code == 403  # Forbidden
        
        # Admin should be able to update any test case
        user_test_case = test_cases[0]
        
        response = test_client.put(
            f"/api/v1/test-cases/{user_test_case.id}",
            headers={
                "Authorization": f"Bearer {admin_token}",
                "Content-Type": "application/json"
            },
            json=update_data
        )
        
        assert response.status_code == 200
        updated_test_case = response.json()
        assert updated_test_case["name"] == "Attempted Update"
        
        # Regular user should not be able to delete admin's test case
        response = test_client.delete(
            f"/api/v1/test-cases/{admin_test_case.id}",
            headers={"Authorization": f"Bearer {user_token}"}
        )
        
        assert response.status_code == 403  # Forbidden
        
        # Admin should be able to delete any test case
        response = test_client.delete(
            f"/api/v1/test-cases/{user_test_case.id}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 204  # No content
        
        # Admin should be able to see soft-deleted test cases
        response = test_client.get(
            "/api/v1/test-cases?include_deleted=true",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        test_cases_data = response.json()
        
        # Should include the deleted test case
        deleted_test_case_ids = [tc["id"] for tc in test_cases_data if tc.get("deleted_at") is not None]
        assert user_test_case.id in deleted_test_case_ids
        
        # Regular user should not be able to see soft-deleted test cases
        response = test_client.get(
            "/api/v1/test-cases?include_deleted=true",
            headers={"Authorization": f"Bearer {user_token}"}
        )
        
        assert response.status_code == 200
        test_cases_data = response.json()
        
        # Should not include the deleted test case
        test_case_ids = [tc["id"] for tc in test_cases_data]
        assert user_test_case.id not in test_case_ids
        
        # Admin should be able to restore a soft-deleted test case
        response = test_client.post(
            f"/api/v1/test-cases/{user_test_case.id}/restore",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 200
        restored_test_case = response.json()
        assert restored_test_case["id"] == user_test_case.id
        assert restored_test_case["deleted_at"] is None
