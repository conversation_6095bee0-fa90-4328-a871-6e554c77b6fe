"""Tests for D3FEND API endpoints."""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime
import logging

from api.models.d3fend import D3FENDVersion, D3FENDConcept, d3fend_relationships
from api.routes.v1_d3fend import router

# Configure logging
logger = logging.getLogger(__name__)

@pytest.fixture
def test_version(db_session: Session):
    """Create a test D3FEND version."""
    version = D3FENDVersion(
        version="2025.02.25",
        import_date=datetime.utcnow(),
        is_current=True
    )
    db_session.add(version)
    db_session.flush()
    return version

@pytest.fixture
def test_concepts(db_session: Session, test_version):
    """Create test D3FEND concepts."""
    concepts = []
    for i in range(3):
        concept = D3FENDConcept(
            uri=f"http://d3fend.test/concept{i}",
            name=f"TestConcept{i}",
            type="owl:Class",
            definition=f"Test definition {i}",
            version_id=test_version.id,
            created=datetime.utcnow(),
            external_references='{"source": "test"}',
            notes=f"Test notes {i}"
        )
        db_session.add(concept)
        concepts.append(concept)

    db_session.commit()
    return concepts

def test_get_d3fend_stats(client: TestClient, test_version, test_concepts):
    """Test getting D3FEND statistics."""
    response = client.get("/api/v1/d3fend/stats")
    assert response.status_code == 200
    data = response.json()

    assert data["total_versions"] == 1
    assert data["total_concepts"] == 3
    assert data["current_version"] == "2025.02.25"
    assert data["current_version_concepts"] == 3

def test_get_d3fend_version_info(client: TestClient, test_version):
    """Test getting D3FEND version information."""
    response = client.get("/api/v1/d3fend/version")
    assert response.status_code == 200
    data = response.json()
    assert data["current_version"] == "2025.02.25"

def test_get_d3fend_concepts(client: TestClient, test_concepts):
    """Test getting D3FEND concepts."""
    response = client.get("/api/v1/d3fend/concepts")
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 3
    assert data[0]["name"] == "TestConcept0"

    # Verify all fields are present
    concept = data[0]
    assert "id" in concept
    assert "uri" in concept
    assert "name" in concept
    assert "type" in concept
    assert "definition" in concept
    assert "version_id" in concept
    assert "external_references" in concept
    assert "notes" in concept

def test_get_concept_by_id(client: TestClient, test_concepts):
    """Test getting a specific D3FEND concept."""
    concept_id = test_concepts[0].id
    response = client.get(f"/api/v1/d3fend/concept/{concept_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "TestConcept0"
    assert data["definition"] == "Test definition 0"

def test_get_concept_by_uri(client: TestClient, test_concepts):
    """Test getting a concept by URI."""
    concept_uri = "http://d3fend.test/concept0"
    response = client.get("/api/v1/d3fend/concept", params={"uri": concept_uri})
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "TestConcept0"
    assert data["uri"] == concept_uri

def test_get_nonexistent_concept(client: TestClient):
    """Test getting a non-existent concept."""
    response = client.get("/api/v1/d3fend/concept/999999")
    assert response.status_code == 404
    assert "Concept not found" in response.json()["detail"]

def test_get_invalid_concept_uri(client: TestClient):
    """Test getting a concept with invalid URI."""
    response = client.get("/api/v1/d3fend/concept", params={"uri": "invalid_uri"})
    assert response.status_code == 404
    assert "Concept not found" in response.json()["detail"]

def test_get_concepts_with_filter(client: TestClient, test_concepts):
    """Test getting concepts with filter."""
    response = client.get("/api/v1/d3fend/concepts", params={"search": "TestConcept0"})
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 1
    assert data[0]["name"] == "TestConcept0"

def test_get_concepts_empty_search(client: TestClient, test_concepts):
    """Test getting concepts with empty search term."""
    response = client.get("/api/v1/d3fend/concepts", params={"search": ""})
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 3

def test_version_not_found(client: TestClient, db_session: Session):
    """Test error handling when no version exists."""
    # Clear any existing versions
    db_session.query(D3FENDVersion).delete()
    db_session.commit()

    response = client.get("/api/v1/d3fend/version")
    assert response.status_code == 404
    assert "No D3FEND version found" in response.json()["detail"]

def test_invalid_concept_id(client: TestClient):
    """Test error handling for invalid concept ID."""
    response = client.get("/api/v1/d3fend/concept/invalid")
    assert response.status_code == 422
    assert "value is not a valid integer" in str(response.json()["detail"])

def test_empty_concepts_list(client: TestClient, test_version):
    """Test getting concepts when none exist."""
    response = client.get("/api/v1/d3fend/concepts")
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 0