"""Enhanced tests for the Test Case Management API."""
import pytest
import json
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session
from datetime import datetime

from api.models.database.test_case import TestCase
from api.models.user import User, UserRole
from api.services import test_case as test_case_service
from api.models.schemas.test_case import TestCaseStatus, TestCaseType, TestCasePriority, TestCaseComplexity


@pytest.fixture
def test_user(db_session):
    """Create a test user."""
    user = User(
        username="testcaseuser",
        email="<EMAIL>",
        role=UserRole.ANALYST
    )
    user.set_password("password123")
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def test_admin(db_session):
    """Create a test admin user."""
    admin = User(
        username="testcaseadmin",
        email="<EMAIL>",
        role=UserRole.ADMIN
    )
    admin.set_password("adminpass123")
    db_session.add(admin)
    db_session.commit()
    db_session.refresh(admin)
    return admin


@pytest.fixture
def sample_test_cases(db_session, test_user, test_admin):
    """Create sample test cases for testing."""
    test_cases = [
        TestCase(
            name="SQL Injection Test",
            description="Test for SQL injection vulnerabilities",
            type="manual",
            status="active",
            priority="high",
            complexity="moderate",
            prerequisites="Access to the application",
            steps=["Step 1: Navigate to login page", "Step 2: Enter SQL injection payload"],
            expected_result="Application should sanitize input and prevent SQL injection",
            tags=["security", "injection", "sql"],
            mitre_techniques=["T1190", "T1212"],
            created_by=test_user.id,
            version="1.0.0"
        ),
        TestCase(
            name="XSS Vulnerability Test",
            description="Test for cross-site scripting vulnerabilities",
            type="manual",
            status="draft",
            priority="high",
            complexity="moderate",
            prerequisites="Access to the application",
            steps=["Step 1: Navigate to input form", "Step 2: Enter XSS payload"],
            expected_result="Application should sanitize input and prevent XSS",
            tags=["security", "injection", "xss"],
            mitre_techniques=["T1059", "T1189"],
            created_by=test_user.id,
            version="1.0.0"
        ),
        TestCase(
            name="Privilege Escalation Test",
            description="Test for privilege escalation vulnerabilities",
            type="manual",
            status="active",
            priority="critical",
            complexity="complex",
            prerequisites="User account with basic privileges",
            steps=["Step 1: Login as basic user", "Step 2: Attempt to access admin functions"],
            expected_result="Application should prevent access to admin functions",
            tags=["security", "privilege-escalation"],
            mitre_techniques=["T1078", "T1548"],
            created_by=test_admin.id,
            version="1.0.0"
        ),
        TestCase(
            name="Deprecated Test Case",
            description="This test case is deprecated",
            type="manual",
            status="deprecated",
            priority="low",
            complexity="simple",
            prerequisites="None",
            steps=["Step 1: Do something"],
            expected_result="Expected result",
            tags=["deprecated"],
            mitre_techniques=[],
            created_by=test_admin.id,
            version="1.0.0",
            is_deprecated=True
        ),
        TestCase(
            name="Deleted Test Case",
            description="This test case is soft-deleted",
            type="manual",
            status="active",
            priority="medium",
            complexity="moderate",
            prerequisites="None",
            steps=["Step 1: Do something"],
            expected_result="Expected result",
            tags=["deleted"],
            mitre_techniques=[],
            created_by=test_user.id,
            version="1.0.0",
            deleted_at=datetime.utcnow()
        )
    ]
    
    for test_case in test_cases:
        db_session.add(test_case)
    
    db_session.commit()
    
    # Refresh test cases to get their IDs
    for test_case in test_cases:
        db_session.refresh(test_case)
    
    return test_cases


class TestTestCaseManagement:
    """Test the Test Case Management API."""

    def test_get_test_cases(self, test_client, db_session, sample_test_cases, test_user):
        """Test getting a list of test cases."""
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": test_user.username})
        
        # Test getting all test cases (excluding deleted)
        response = test_client.get(
            "/api/v1/test-cases",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        
        # Should return 4 test cases (excluding the deleted one)
        assert len(data) == 4
        
        # Test filtering by status
        response = test_client.get(
            "/api/v1/test-cases?status=draft",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["status"] == "draft"
        
        # Test filtering by type
        response = test_client.get(
            "/api/v1/test-cases?type=manual",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 4
        
        # Test filtering by priority
        response = test_client.get(
            "/api/v1/test-cases?priority=high",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert all(tc["priority"] == "high" for tc in data)
        
        # Test filtering by complexity
        response = test_client.get(
            "/api/v1/test-cases?complexity=complex",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["complexity"] == "complex"
        
        # Test filtering by tags
        response = test_client.get(
            "/api/v1/test-cases?tags=injection",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        
        # Test filtering by MITRE techniques
        response = test_client.get(
            "/api/v1/test-cases?mitre_techniques=T1190",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert "T1190" in data[0]["mitre_techniques"]
        
        # Test search
        response = test_client.get(
            "/api/v1/test-cases?search=SQL",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert "SQL" in data[0]["name"]
        
        # Test pagination
        response = test_client.get(
            "/api/v1/test-cases?skip=1&limit=2",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2

    def test_get_test_case_by_id(self, test_client, db_session, sample_test_cases, test_user):
        """Test getting a test case by ID."""
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": test_user.username})
        
        # Get a test case
        test_case = sample_test_cases[0]
        
        # Test getting the test case
        response = test_client.get(
            f"/api/v1/test-cases/{test_case.id}",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_case.id
        assert data["name"] == test_case.name
        assert data["description"] == test_case.description
        assert data["type"] == test_case.type
        assert data["status"] == test_case.status
        assert data["priority"] == test_case.priority
        assert data["complexity"] == test_case.complexity
        assert data["prerequisites"] == test_case.prerequisites
        assert data["steps"] == test_case.steps
        assert data["expected_result"] == test_case.expected_result
        assert data["tags"] == test_case.tags
        assert data["mitre_techniques"] == test_case.mitre_techniques
        assert data["created_by"] == test_case.created_by
        assert data["version"] == test_case.version
        
        # Test getting a non-existent test case
        response = test_client.get(
            "/api/v1/test-cases/9999",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 404
        
        # Test getting a deleted test case (should fail for regular user)
        deleted_test_case = sample_test_cases[4]
        
        response = test_client.get(
            f"/api/v1/test-cases/{deleted_test_case.id}",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 404

    def test_create_test_case(self, test_client, db_session, test_user):
        """Test creating a test case."""
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": test_user.username})
        
        # Create test case data
        test_case_data = {
            "name": "New Test Case",
            "description": "Test case created through API",
            "type": "manual",
            "status": "draft",
            "priority": "medium",
            "complexity": "moderate",
            "prerequisites": "Test prerequisites",
            "steps": ["Step 1: Do something", "Step 2: Do something else"],
            "expected_result": "Expected test result",
            "tags": ["api", "test"],
            "mitre_techniques": ["T1234"]
        }
        
        # Test creating a test case
        response = test_client.post(
            "/api/v1/test-cases",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=test_case_data
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == test_case_data["name"]
        assert data["description"] == test_case_data["description"]
        assert data["type"] == test_case_data["type"]
        assert data["status"] == test_case_data["status"]
        assert data["priority"] == test_case_data["priority"]
        assert data["complexity"] == test_case_data["complexity"]
        assert data["prerequisites"] == test_case_data["prerequisites"]
        assert data["steps"] == test_case_data["steps"]
        assert data["expected_result"] == test_case_data["expected_result"]
        assert data["tags"] == test_case_data["tags"]
        assert data["mitre_techniques"] == test_case_data["mitre_techniques"]
        assert data["created_by"] == test_user.id
        assert data["version"] == "1.0.0"
        
        # Test creating a test case with invalid data
        invalid_data = {
            "name": "",  # Empty name should fail
            "description": "Invalid test case",
            "type": "manual",
            "status": "draft",
            "priority": "medium",
            "complexity": "moderate",
            "prerequisites": "Test prerequisites",
            "steps": ["Step 1: Do something"],
            "expected_result": "Expected test result"
        }
        
        response = test_client.post(
            "/api/v1/test-cases",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=invalid_data
        )
        
        assert response.status_code == 422  # Validation error

    def test_update_test_case(self, test_client, db_session, sample_test_cases, test_user):
        """Test updating a test case."""
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": test_user.username})
        
        # Get a test case created by the test user
        test_case = sample_test_cases[0]
        
        # Create update data
        update_data = {
            "name": "Updated Test Case",
            "description": "Updated description",
            "status": "active",
            "priority": "critical",
            "steps": ["Updated Step 1", "Updated Step 2"],
            "tags": ["updated", "security"]
        }
        
        # Test updating the test case
        response = test_client.put(
            f"/api/v1/test-cases/{test_case.id}",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=update_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_case.id
        assert data["name"] == update_data["name"]
        assert data["description"] == update_data["description"]
        assert data["status"] == update_data["status"]
        assert data["priority"] == update_data["priority"]
        assert data["steps"] == update_data["steps"]
        assert data["tags"] == update_data["tags"]
        assert data["version"] == "1.0.1"  # Version should be incremented
        
        # Test updating a test case created by another user (should fail)
        admin_test_case = sample_test_cases[2]  # Created by admin
        
        response = test_client.put(
            f"/api/v1/test-cases/{admin_test_case.id}",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=update_data
        )
        
        assert response.status_code == 403  # Forbidden
        
        # Test updating a non-existent test case
        response = test_client.put(
            "/api/v1/test-cases/9999",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=update_data
        )
        
        assert response.status_code == 404  # Not found

    def test_delete_test_case(self, test_client, db_session, sample_test_cases, test_user):
        """Test deleting a test case."""
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": test_user.username})
        
        # Get a test case created by the test user
        test_case = sample_test_cases[1]
        
        # Test deleting the test case
        response = test_client.delete(
            f"/api/v1/test-cases/{test_case.id}",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 204  # No content
        
        # Verify the test case is soft-deleted
        db_test_case = db_session.query(TestCase).filter(TestCase.id == test_case.id).first()
        assert db_test_case is not None
        assert db_test_case.deleted_at is not None
        
        # Test deleting a test case created by another user (should fail)
        admin_test_case = sample_test_cases[2]  # Created by admin
        
        response = test_client.delete(
            f"/api/v1/test-cases/{admin_test_case.id}",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 403  # Forbidden
        
        # Test deleting a non-existent test case
        response = test_client.delete(
            "/api/v1/test-cases/9999",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 404  # Not found

    def test_bulk_operations(self, test_client, db_session, test_user):
        """Test bulk operations for test cases."""
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": test_user.username})
        
        # Test bulk creation
        bulk_create_data = {
            "test_cases": [
                {
                    "name": f"Bulk Test Case {i}",
                    "description": f"Bulk test case {i}",
                    "type": "manual",
                    "status": "draft",
                    "priority": "medium",
                    "complexity": "moderate",
                    "prerequisites": f"Prerequisites {i}",
                    "steps": [f"Step {j}" for j in range(1, 4)],
                    "expected_result": f"Expected result {i}",
                    "tags": ["bulk", f"test-{i}"],
                    "mitre_techniques": [f"T{1000+i}"]
                }
                for i in range(1, 4)
            ]
        }
        
        response = test_client.post(
            "/api/v1/test-cases/bulk",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=bulk_create_data
        )
        
        assert response.status_code == 201
        data = response.json()
        assert "test_cases" in data
        assert len(data["test_cases"]) == 3
        
        # Get the created test case IDs
        test_case_ids = [tc["id"] for tc in data["test_cases"]]
        
        # Test bulk update
        bulk_update_data = {
            "test_cases": [
                {
                    "id": test_case_ids[0],
                    "status": "active",
                    "priority": "high"
                },
                {
                    "id": test_case_ids[1],
                    "status": "active",
                    "priority": "critical"
                },
                {
                    "id": test_case_ids[2],
                    "status": "active",
                    "priority": "low"
                }
            ]
        }
        
        response = test_client.put(
            "/api/v1/test-cases/bulk",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=bulk_update_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "test_cases" in data
        assert len(data["test_cases"]) == 3
        
        # Verify updates
        for i, tc in enumerate(data["test_cases"]):
            assert tc["id"] == test_case_ids[i]
            assert tc["status"] == "active"
            assert tc["priority"] == bulk_update_data["test_cases"][i]["priority"]
        
        # Test bulk delete
        bulk_delete_data = {
            "test_case_ids": test_case_ids
        }
        
        response = test_client.delete(
            "/api/v1/test-cases/bulk",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=bulk_delete_data
        )
        
        assert response.status_code == 204  # No content
        
        # Verify test cases are soft-deleted
        for test_case_id in test_case_ids:
            db_test_case = db_session.query(TestCase).filter(TestCase.id == test_case_id).first()
            assert db_test_case is not None
            assert db_test_case.deleted_at is not None

    def test_admin_operations(self, test_client, db_session, sample_test_cases, test_admin):
        """Test admin-specific operations for test cases."""
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": test_admin.username})
        
        # Test getting deleted test cases
        response = test_client.get(
            "/api/v1/test-cases?include_deleted=true",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 5  # All test cases including deleted
        
        # Test getting a specific deleted test case
        deleted_test_case = sample_test_cases[4]
        
        response = test_client.get(
            f"/api/v1/test-cases/{deleted_test_case.id}?include_deleted=true",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == deleted_test_case.id
        assert data["name"] == deleted_test_case.name
        
        # Test permanently deleting a test case
        test_case = sample_test_cases[3]  # Deprecated test case
        
        response = test_client.delete(
            f"/api/v1/test-cases/{test_case.id}?permanent=true",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 204  # No content
        
        # Verify the test case is permanently deleted
        db_test_case = db_session.query(TestCase).filter(TestCase.id == test_case.id).first()
        assert db_test_case is None
        
        # Test restoring a soft-deleted test case
        deleted_test_case = sample_test_cases[4]
        
        response = test_client.post(
            f"/api/v1/test-cases/{deleted_test_case.id}/restore",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == deleted_test_case.id
        assert data["deleted_at"] is None
        
        # Verify the test case is restored
        db_test_case = db_session.query(TestCase).filter(TestCase.id == deleted_test_case.id).first()
        assert db_test_case is not None
        assert db_test_case.deleted_at is None
        
        # Test deprecating a test case
        test_case = sample_test_cases[0]
        
        response = test_client.post(
            f"/api/v1/test-cases/{test_case.id}/deprecate",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_case.id
        assert data["is_deprecated"] is True
        
        # Verify the test case is deprecated
        db_test_case = db_session.query(TestCase).filter(TestCase.id == test_case.id).first()
        assert db_test_case is not None
        assert db_test_case.is_deprecated is True
        
        # Test revoking a test case
        test_case = sample_test_cases[1]
        
        response = test_client.post(
            f"/api/v1/test-cases/{test_case.id}/revoke",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json={"reason": "Security issue"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_case.id
        assert data["is_revoked"] is True
        assert data["revoked_by_id"] == test_admin.id
        
        # Verify the test case is revoked
        db_test_case = db_session.query(TestCase).filter(TestCase.id == test_case.id).first()
        assert db_test_case is not None
        assert db_test_case.is_revoked is True
        assert db_test_case.revoked_by_id == test_admin.id
