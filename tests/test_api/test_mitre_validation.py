
import pytest
from fastapi.testclient import TestClient
from api.models.mitre import Mi<PERSON>Technique, MitreRelationship
from api.utils.mitre_import import import_mitre_data

def test_technique_validation(db_session):
    """Test technique data validation."""
    technique = MitreTechnique(
        technique_id="T1234",
        name="Test Technique",
        description="Test Description",
        version="2023.01"
    )
    db_session.add(technique)
    db_session.commit()
    
    assert technique.technique_id == "T1234"
    assert technique.name == "Test Technique"

def test_relationship_validation(db_session):
    """Test relationship data validation."""
    source = MitreTechnique(
        technique_id="T1234",
        name="Source Technique",
        version="2023.01"
    )
    target = MitreTechnique(
        technique_id="T5678",
        name="Target Technique",
        version="2023.01"
    )
    db_session.add_all([source, target])
    db_session.commit()

    relationship = MitreRelationship(
        relationship_id="R1234",
        source_id=source.id,
        target_id=target.id,
        relationship_type="uses",
        version="2023.01"
    )
    db_session.add(relationship)
    db_session.commit()

    assert relationship.relationship_type == "uses"
    assert relationship.source_id == source.id
