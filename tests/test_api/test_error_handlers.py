"""Tests for error handling middleware."""
import pytest
import logging
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from api.models.mitre import MitreTechnique, MitreVersion
from api.database import DatabaseError
from api.middleware.error_handler import ErrorHandlerMiddleware

# Configure test logging
log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
logging.basicConfig(
    level=logging.DEBUG,
    format=log_format,
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

def test_http_exception_handler(client):
    """Test HTTP exception handling."""
    # Test 404 error handling
    response = client.get("/api/v1/mitre/technique/999999")
    logger.debug(f"HTTP Exception test response: {response.json()}")
    assert response.status_code == 404
    assert "detail" in response.json()
    assert response.json()["code"] == "HTTP_ERROR"

    # Test 403 error handling
    response = client.get("/api/v1/mitre/technique/-1")
    logger.debug(f"Invalid ID response: {response.json()}")
    assert response.status_code == 404
    assert response.json()["code"] == "HTTP_ERROR"

def test_database_error_handler(client, db_session):
    """Test database error handling."""
    try:
        # Create test version first
        version = MitreVersion(
            version="1.0",
            is_current=True
        )
        db_session.add(version)
        db_session.commit()
        logger.debug(f"Created test version with ID: {version.id}")

        # Create technique with POST request
        response = client.post("/api/v1/mitre/technique", json={
            "technique_id": "T1234",
            "name": "Test",
            "description": "Test description",
            "version_id": version.id
        })
        assert response.status_code == 200
        logger.debug("First technique created successfully")

        # Try to create duplicate technique to trigger integrity error
        response = client.post("/api/v1/mitre/technique", json={
            "technique_id": "T1234",  # Same ID to trigger unique constraint
            "name": "Test2",
            "version_id": version.id
        })
        logger.debug(f"Duplicate technique response: {response.json()}")
        assert response.status_code == 500
        assert response.json()["code"] == "DATABASE_ERROR"

        # Test non-existent version ID
        response = client.post("/api/v1/mitre/technique", json={
            "technique_id": "T5678",
            "name": "Test3",
            "version_id": 999999
        })
        logger.debug(f"Invalid version ID response: {response.json()}")
        assert response.status_code == 404
        assert response.json()["code"] == "HTTP_ERROR"

    finally:
        # Cleanup
        logger.debug("Cleaning up test data")
        db_session.rollback()

def test_validation_error_handler(client):
    """Test request validation error handling."""
    # Test with invalid path parameter
    response = client.get("/api/v1/mitre/technique/invalid")
    logger.debug(f"Validation error test response: {response.json()}")
    assert response.status_code == 422
    response_data = response.json()
    assert response_data["code"] == "VALIDATION_ERROR"
    assert "value is not a valid integer" in response_data["detail"]

    # Test with invalid query parameter
    response = client.get("/api/v1/mitre/techniques?page=invalid")
    logger.debug(f"Invalid query parameter response: {response.json()}")
    assert response.status_code == 422
    response_data = response.json()
    assert response_data["code"] == "VALIDATION_ERROR"
    assert "value is not a valid integer" in response_data["detail"]

    # Test with missing required parameter
    response = client.post("/api/v1/mitre/technique", json={})
    logger.debug(f"Missing parameter response: {response.json()}")
    assert response.status_code == 422
    response_data = response.json()
    assert response_data["code"] == "VALIDATION_ERROR"

def test_generic_error_handler(client, db_session):
    """Test handling of unhandled exceptions."""
    try:
        # Test with invalid JSON
        response = client.post("/api/v1/mitre/technique", data="invalid json")
        logger.debug(f"Invalid JSON response: {response.json()}")
        assert response.status_code == 422
        response_data = response.json()
        assert response_data["code"] == "VALIDATION_ERROR"

        # Test with malformed request
        headers = {"Content-Type": "application/json"}
        response = client.post("/api/v1/mitre/technique", headers=headers, content=b"invalid")
        logger.debug(f"Malformed request response: {response.json()}")
        assert response.status_code == 422
        response_data = response.json()
        assert response_data["code"] == "VALIDATION_ERROR"

    finally:
        db_session.rollback()

def test_database_connection_error(client, db_session):
    """Test handling of database connection errors."""
    # Close the session to simulate connection error
    db_session.close()

    response = client.get("/api/v1/mitre/techniques")
    logger.debug(f"Database connection error response: {response.json()}")
    assert response.status_code == 500
    response_data = response.json()
    assert response_data["code"] == "DATABASE_ERROR"