"""
Tests for the Assessment Metrics and Reporting API.

This module contains tests for the assessment metrics and reporting endpoints.
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.main import app
from api.models.assessment import Assessment
from api.models.schemas.assessment import AssessmentCreate
from api.services.assessment import create_assessment, get_assessment_metrics, generate_assessment_report
from api.auth.utils import create_access_token

client = TestClient(app)


@pytest.fixture
def db_session(monkeypatch):
    """Create a test database session."""
    # This would typically use a test database
    from api.database import get_db
    db = next(get_db())
    yield db


@pytest.fixture
def auth_headers():
    """Create authentication headers for API requests."""
    # In a real test, this would authenticate with the API
    # For now, we'll mock the authentication
    token = create_access_token({"sub": "test_user", "role": "admin"})
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def test_assessment(db_session):
    """Create a test assessment in the database."""
    assessment_data = AssessmentCreate(
        name="Test Assessment",
        description="Test assessment for unit tests",
        target_system="Test System",
        assessment_type="security",
        status="active"
    )
    assessment = create_assessment(db_session, assessment_data, user_id=1)
    
    yield assessment
    
    # Clean up
    db_session.query(Assessment).filter(Assessment.id == assessment.id).delete()
    db_session.commit()


def test_get_assessment_metrics(auth_headers, test_assessment):
    """Test getting metrics for an assessment."""
    response = client.get(
        f"/api/v1/assessments/{test_assessment.id}/metrics",
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "total_test_cases" in data
    assert "executed_test_cases" in data
    assert "passed_test_cases" in data
    assert "failed_test_cases" in data
    assert "partial_test_cases" in data
    assert "blocked_test_cases" in data
    assert "not_applicable_test_cases" in data
    assert "execution_rate" in data
    assert "success_rate" in data
    assert "risk_level" in data


def test_generate_assessment_report(auth_headers, test_assessment):
    """Test generating a report for an assessment."""
    response = client.get(
        f"/api/v1/assessments/{test_assessment.id}/report",
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "assessment" in data
    assert "summary" in data
    assert "metrics" in data
    assert "test_executions" in data
    assert "recommendations" in data


def test_get_assessment_summary(auth_headers, test_assessment):
    """Test getting a summary for an assessment."""
    response = client.get(
        f"/api/v1/assessments/{test_assessment.id}/summary",
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "total_tests" in data
    assert "passed" in data
    assert "failed" in data
    assert "partial" in data
    assert "pending" in data
    assert "blocked" in data
    assert "not_applicable" in data
    assert "completion_percentage" in data
    assert "pass_rate" in data
