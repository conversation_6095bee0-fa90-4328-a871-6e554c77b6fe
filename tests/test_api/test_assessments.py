"""
Tests for the assessment API endpoints.

This module contains tests for creating, retrieving, updating, and deleting
security assessments through the API.
"""
import pytest
from datetime import datetime, timedelta
from api.models import AssessmentDB, CampaignDB, User, UserRole
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.main import app
from api.database import get_db

client = TestClient(app)

@pytest.fixture
def db_session(monkeypatch):
    """Create a test database session."""
    # This would typically use a test database
    # For simplicity, we'll use the same database but roll back changes
    from api.database import SessionLocal
    db = SessionLocal()
    try:
        yield db
    finally:
        db.rollback()
        db.close()

@pytest.fixture
def auth_headers():
    """Create authentication headers for API requests."""
    # In a real test, this would authenticate with the API
    # For now, we'll mock the authentication
    return {"Authorization": "Bearer test_token"}

@pytest.fixture
def test_campaign(db_session):
    """Create a test campaign in the database."""
    campaign = CampaignDB(
        name="Test Campaign",
        description="Test campaign for unit tests",
        status="active"
    )
    db_session.add(campaign)
    db_session.commit()
    db_session.refresh(campaign)
    
    yield campaign
    
    # Clean up
    db_session.query(CampaignDB).filter(CampaignDB.id == campaign.id).delete()
    db_session.commit()

@pytest.fixture
def test_assessment(db_session):
    """Create a test assessment in the database."""
    assessment = AssessmentDB(
        name="Test Assessment",
        description="Test assessment for unit tests",
        target_system="Test System",
        assessment_type="vulnerability",
        start_date=datetime.utcnow(),
        end_date=datetime.utcnow() + timedelta(days=30),
        status="planned",
        created_by=1  # Assuming user ID 1 exists
    )
    db_session.add(assessment)
    db_session.commit()
    db_session.refresh(assessment)
    
    yield assessment
    
    # Clean up
    db_session.query(AssessmentDB).filter(AssessmentDB.id == assessment.id).delete()
    db_session.commit()

def test_create_assessment(db_session, test_campaign, auth_headers, monkeypatch):
    """Test creating a new assessment."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    start_date = datetime.utcnow().isoformat()
    end_date = (datetime.utcnow() + timedelta(days=30)).isoformat()
    
    response = client.post(
        "/api/v1/assessments/",
        json={
            "name": "New Test Assessment",
            "description": "Created during unit test",
            "target_system": "Test Target System",
            "assessment_type": "penetration",
            "start_date": start_date,
            "end_date": end_date,
            "status": "planned",
            "campaign_ids": [test_campaign.id]
        },
        headers=auth_headers
    )
    
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == "New Test Assessment"
    assert data["description"] == "Created during unit test"
    assert data["target_system"] == "Test Target System"
    assert data["assessment_type"] == "penetration"
    assert data["status"] == "planned"
    assert "id" in data
    assert "campaigns" in data
    assert len(data["campaigns"]) == 1
    assert data["campaigns"][0]["id"] == test_campaign.id
    
    # Clean up
    db_session.query(AssessmentDB).filter(AssessmentDB.id == data["id"]).delete()
    db_session.commit()

def test_get_assessments(db_session, test_assessment, auth_headers, monkeypatch):
    """Test retrieving all assessments."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    response = client.get(
        "/api/v1/assessments/",
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) >= 1
    
    # Check if our test assessment is in the list
    assessment_ids = [a["id"] for a in data]
    assert test_assessment.id in assessment_ids

def test_get_assessment(db_session, test_assessment, auth_headers, monkeypatch):
    """Test retrieving a specific assessment."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    response = client.get(
        f"/api/v1/assessments/{test_assessment.id}",
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_assessment.id
    assert data["name"] == test_assessment.name
    assert data["description"] == test_assessment.description
    assert data["target_system"] == test_assessment.target_system
    assert data["assessment_type"] == test_assessment.assessment_type
    assert data["status"] == test_assessment.status

def test_update_assessment(db_session, test_assessment, auth_headers, monkeypatch):
    """Test updating an assessment."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    start_date = datetime.utcnow().isoformat()
    end_date = (datetime.utcnow() + timedelta(days=60)).isoformat()
    
    response = client.put(
        f"/api/v1/assessments/{test_assessment.id}",
        json={
            "name": "Updated Assessment",
            "description": "Updated during unit test",
            "target_system": "Updated Target System",
            "assessment_type": "code_review",
            "start_date": start_date,
            "end_date": end_date,
            "status": "in-progress"
        },
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_assessment.id
    assert data["name"] == "Updated Assessment"
    assert data["description"] == "Updated during unit test"
    assert data["target_system"] == "Updated Target System"
    assert data["assessment_type"] == "code_review"
    assert data["status"] == "in-progress"
    
    # Verify the database was updated
    db_assessment = db_session.query(AssessmentDB).filter(AssessmentDB.id == test_assessment.id).first()
    assert db_assessment.name == "Updated Assessment"
    assert db_assessment.description == "Updated during unit test"
    assert db_assessment.target_system == "Updated Target System"
    assert db_assessment.assessment_type == "code_review"
    assert db_assessment.status == "in-progress"

def test_delete_assessment(db_session, test_assessment, auth_headers, monkeypatch):
    """Test deleting an assessment."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    response = client.delete(
        f"/api/v1/assessments/{test_assessment.id}",
        headers=auth_headers
    )
    
    assert response.status_code == 204
    
    # Verify the assessment was soft-deleted
    db_assessment = db_session.query(AssessmentDB).filter(
        AssessmentDB.id == test_assessment.id
    ).first()
    
    assert db_assessment.deleted_time is not None

def test_restore_assessment(db_session, test_assessment, auth_headers, monkeypatch):
    """Test restoring a deleted assessment."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    # First, soft-delete the assessment
    test_assessment.soft_delete(db_session)
    
    response = client.post(
        f"/api/v1/assessments/{test_assessment.id}/restore",
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_assessment.id
    
    # Verify the assessment was restored
    db_assessment = db_session.query(AssessmentDB).filter(
        AssessmentDB.id == test_assessment.id
    ).first()
    
    assert db_assessment.deleted_time is None

def test_add_campaign_to_assessment(db_session, test_assessment, test_campaign, auth_headers, monkeypatch):
    """Test adding a campaign to an assessment."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    response = client.post(
        f"/api/v1/assessments/{test_assessment.id}/campaigns/{test_campaign.id}",
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_assessment.id
    
    # Verify the campaign was added to the assessment
    db_assessment = db_session.query(AssessmentDB).filter(
        AssessmentDB.id == test_assessment.id
    ).first()
    
    campaign_ids = [c.id for c in db_assessment.campaigns]
    assert test_campaign.id in campaign_ids

def test_remove_campaign_from_assessment(db_session, test_assessment, test_campaign, auth_headers, monkeypatch):
    """Test removing a campaign from an assessment."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    # First, add the campaign to the assessment
    test_assessment.campaigns.append(test_campaign)
    db_session.add(test_assessment)
    db_session.commit()
    db_session.refresh(test_assessment)
    
    response = client.delete(
        f"/api/v1/assessments/{test_assessment.id}/campaigns/{test_campaign.id}",
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_assessment.id
    
    # Verify the campaign was removed from the assessment
    db_assessment = db_session.query(AssessmentDB).filter(
        AssessmentDB.id == test_assessment.id
    ).first()
    
    campaign_ids = [c.id for c in db_assessment.campaigns]
    assert test_campaign.id not in campaign_ids

def test_assessment_validation(client, test_user):
    """Test assessment creation validation"""
    # Test missing required fields
    response = client.post(
        "/api/v1/assessments",
        json={
            "description": "Test Description"
        },
        headers={"Authorization": f"Bearer {test_user_token(test_user, client)}"}
    )
    assert response.status_code == 422  # Validation error
    
    # Test invalid assessment type
    response = client.post(
        "/api/v1/assessments",
        json={
            "name": "Test Assessment",
            "description": "Test Description",
            "target_system": "Test System",
            "assessment_type": "invalid_type",
            "status": "planned"
        },
        headers={"Authorization": f"Bearer {test_user_token(test_user, client)}"}
    )
    assert response.status_code == 422  # Validation error
    
    # Test invalid status
    response = client.post(
        "/api/v1/assessments",
        json={
            "name": "Test Assessment",
            "description": "Test Description",
            "target_system": "Test System",
            "assessment_type": "vulnerability",
            "status": "invalid_status"
        },
        headers={"Authorization": f"Bearer {test_user_token(test_user, client)}"}
    )
    assert response.status_code == 422  # Validation error

def test_assessment_date_handling(client, db_session, test_user):
    """Test handling of assessment dates"""
    # Create an assessment with start and end dates
    start_date = datetime.utcnow()
    end_date = start_date + timedelta(days=30)
    
    response = client.post(
        "/api/v1/assessments",
        json={
            "name": "Test Assessment",
            "description": "Test Description",
            "target_system": "Test System",
            "assessment_type": "vulnerability",
            "status": "planned",
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        },
        headers={"Authorization": f"Bearer {test_user_token(test_user, client)}"}
    )
    assert response.status_code == 200
    data = response.json()
    
    # Verify date handling
    assert "start_date" in data
    assert "end_date" in data
    
    # Test invalid date format
    response = client.post(
        "/api/v1/assessments",
        json={
            "name": "Test Assessment",
            "description": "Test Description",
            "target_system": "Test System",
            "assessment_type": "vulnerability",
            "status": "planned",
            "start_date": "invalid-date-format"
        },
        headers={"Authorization": f"Bearer {test_user_token(test_user, client)}"}
    )
    assert response.status_code == 422  # Validation error
    
    # Test end date before start date
    response = client.post(
        "/api/v1/assessments",
        json={
            "name": "Test Assessment",
            "description": "Test Description",
            "target_system": "Test System",
            "assessment_type": "vulnerability",
            "status": "planned",
            "start_date": end_date.isoformat(),
            "end_date": start_date.isoformat()
        },
        headers={"Authorization": f"Bearer {test_user_token(test_user, client)}"}
    )
    assert response.status_code == 422  # Validation error 