"""Tests for the MITRE ATT&CK Technique Scoring System."""
import pytest
import json
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime

from api.models.database.mitre import <PERSON>treV<PERSON>ion, MitreTechnique, MitreTactic
from api.models.database.technique_score import TechniqueScore
from api.models.user import User, UserRole


@pytest.fixture
def technique_scoring_setup(db_session):
    """Create test data for technique scoring tests."""
    # Create users
    user = User(
        username="scoreuser",
        email="<EMAIL>",
        role=UserRole.ANALYST
    )
    user.set_password("password123")
    
    admin = User(
        username="scoreadmin",
        email="<EMAIL>",
        role=UserRole.ADMIN
    )
    admin.set_password("adminpass123")
    
    db_session.add(user)
    db_session.add(admin)
    db_session.commit()
    db_session.refresh(user)
    db_session.refresh(admin)
    
    # Create MITRE version
    version = MitreVersion(
        version="test-version",
        technology_domain="enterprise",
        is_current=True,
        created_on=datetime.utcnow()
    )
    db_session.add(version)
    db_session.commit()
    db_session.refresh(version)
    
    # Create techniques
    techniques = [
        MitreTechnique(
            technique_id="T1001",
            name="Data Obfuscation",
            description="Techniques used to hide data",
            version_id=version.id,
            created_on=datetime.utcnow()
        ),
        MitreTechnique(
            technique_id="T1002",
            name="Data Compression",
            description="Techniques used to compress data",
            version_id=version.id,
            created_on=datetime.utcnow()
        ),
        MitreTechnique(
            technique_id="T1003",
            name="OS Credential Dumping",
            description="Techniques used to dump credentials",
            version_id=version.id,
            created_on=datetime.utcnow()
        )
    ]
    
    for technique in techniques:
        db_session.add(technique)
    
    db_session.commit()
    
    # Create some initial scores
    scores = [
        TechniqueScore(
            technique_id=techniques[0].id,
            category="impact",
            score=8.5,
            weight=0.8,
            notes="High impact due to potential data exfiltration",
            created_by=user.id
        ),
        TechniqueScore(
            technique_id=techniques[0].id,
            category="likelihood",
            score=6.0,
            weight=1.0,
            notes="Moderate likelihood based on threat intelligence",
            created_by=user.id
        ),
        TechniqueScore(
            technique_id=techniques[1].id,
            category="impact",
            score=7.0,
            weight=0.8,
            notes="Moderate impact",
            created_by=admin.id
        )
    ]
    
    for score in scores:
        db_session.add(score)
    
    db_session.commit()
    
    return {
        "user": user,
        "admin": admin,
        "version": version,
        "techniques": techniques,
        "scores": scores
    }


class TestTechniqueScoring:
    """Test the MITRE ATT&CK Technique Scoring System."""

    def test_create_technique_score(self, test_client, db_session, technique_scoring_setup):
        """Test creating a technique score."""
        user = technique_scoring_setup["user"]
        technique = technique_scoring_setup["techniques"][2]  # OS Credential Dumping
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Create score data
        score_data = {
            "category": "detectability",
            "score": 4.5,
            "weight": 0.7,
            "notes": "Difficult to detect without proper monitoring"
        }
        
        # Test creating a score
        response = test_client.post(
            f"/api/v1/mitre/techniques/{technique.technique_id}/scores",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=score_data
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["technique_id"] == technique.id
        assert data["category"] == score_data["category"]
        assert data["score"] == score_data["score"]
        assert data["weight"] == score_data["weight"]
        assert data["notes"] == score_data["notes"]
        assert data["created_by"] == user.id
        
        # Verify score was created in the database
        db_score = db_session.query(TechniqueScore).filter(
            TechniqueScore.technique_id == technique.id,
            TechniqueScore.category == score_data["category"]
        ).first()
        
        assert db_score is not None
        assert db_score.score == score_data["score"]
        assert db_score.weight == score_data["weight"]
        assert db_score.notes == score_data["notes"]
        assert db_score.created_by == user.id

    def test_get_technique_scores(self, test_client, technique_scoring_setup):
        """Test getting scores for a technique."""
        user = technique_scoring_setup["user"]
        technique = technique_scoring_setup["techniques"][0]  # Data Obfuscation
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Test getting scores
        response = test_client.get(
            f"/api/v1/mitre/techniques/{technique.technique_id}/scores",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 2  # Two scores for this technique
        
        # Verify score details
        impact_score = next((s for s in data if s["category"] == "impact"), None)
        assert impact_score is not None
        assert impact_score["score"] == 8.5
        assert impact_score["weight"] == 0.8
        assert "High impact" in impact_score["notes"]
        
        likelihood_score = next((s for s in data if s["category"] == "likelihood"), None)
        assert likelihood_score is not None
        assert likelihood_score["score"] == 6.0
        assert likelihood_score["weight"] == 1.0
        assert "Moderate likelihood" in likelihood_score["notes"]

    def test_get_technique_with_scores(self, test_client, technique_scoring_setup):
        """Test getting a technique with its scores and overall score."""
        user = technique_scoring_setup["user"]
        technique = technique_scoring_setup["techniques"][0]  # Data Obfuscation
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Test getting technique with scores
        response = test_client.get(
            f"/api/v1/mitre/techniques/{technique.technique_id}/with-scores",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["technique_id"] == technique.technique_id
        assert data["name"] == technique.name
        assert "scores" in data
        assert len(data["scores"]) == 2
        
        # Verify overall score calculation
        assert "overall_score" in data
        # Overall score = (8.5 * 0.8 + 6.0 * 1.0) / (0.8 + 1.0) = 7.11
        expected_overall_score = (8.5 * 0.8 + 6.0 * 1.0) / (0.8 + 1.0)
        assert abs(data["overall_score"] - expected_overall_score) < 0.01

    def test_update_technique_score(self, test_client, db_session, technique_scoring_setup):
        """Test updating a technique score."""
        user = technique_scoring_setup["user"]
        technique = technique_scoring_setup["techniques"][0]  # Data Obfuscation
        score = technique_scoring_setup["scores"][0]  # Impact score
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Update score data
        update_data = {
            "score": 9.5,
            "weight": 0.9,
            "notes": "Updated impact assessment based on new intelligence"
        }
        
        # Test updating a score
        response = test_client.put(
            f"/api/v1/mitre/techniques/{technique.technique_id}/scores/{score.id}",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=update_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == score.id
        assert data["score"] == update_data["score"]
        assert data["weight"] == update_data["weight"]
        assert data["notes"] == update_data["notes"]
        
        # Verify score was updated in the database
        db_session.refresh(score)
        assert score.score == update_data["score"]
        assert score.weight == update_data["weight"]
        assert score.notes == update_data["notes"]

    def test_delete_technique_score(self, test_client, db_session, technique_scoring_setup):
        """Test deleting a technique score."""
        user = technique_scoring_setup["user"]
        technique = technique_scoring_setup["techniques"][0]  # Data Obfuscation
        score = technique_scoring_setup["scores"][1]  # Likelihood score
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Test deleting a score
        response = test_client.delete(
            f"/api/v1/mitre/techniques/{technique.technique_id}/scores/{score.id}",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 204  # No content
        
        # Verify score was deleted from the database
        db_score = db_session.query(TechniqueScore).filter(
            TechniqueScore.id == score.id
        ).first()
        
        assert db_score is None

    def test_bulk_create_scores(self, test_client, db_session, technique_scoring_setup):
        """Test bulk creation of technique scores."""
        user = technique_scoring_setup["user"]
        techniques = technique_scoring_setup["techniques"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Create bulk score data
        bulk_data = {
            "scores": [
                {
                    "technique_id": techniques[1].technique_id,  # Data Compression
                    "category": "likelihood",
                    "score": 5.0,
                    "weight": 0.7,
                    "notes": "Moderate likelihood"
                },
                {
                    "technique_id": techniques[2].technique_id,  # OS Credential Dumping
                    "category": "impact",
                    "score": 9.0,
                    "weight": 1.0,
                    "notes": "High impact"
                },
                {
                    "technique_id": techniques[2].technique_id,  # OS Credential Dumping
                    "category": "likelihood",
                    "score": 8.0,
                    "weight": 0.9,
                    "notes": "High likelihood"
                }
            ]
        }
        
        # Test bulk creating scores
        response = test_client.post(
            "/api/v1/mitre/techniques/scores/bulk",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=bulk_data
        )
        
        assert response.status_code == 201
        data = response.json()
        assert "scores" in data
        assert len(data["scores"]) == 3
        
        # Verify scores were created in the database
        for score_data in bulk_data["scores"]:
            technique_id = next(t.id for t in techniques if t.technique_id == score_data["technique_id"])
            db_score = db_session.query(TechniqueScore).filter(
                TechniqueScore.technique_id == technique_id,
                TechniqueScore.category == score_data["category"]
            ).first()
            
            assert db_score is not None
            assert db_score.score == score_data["score"]
            assert db_score.weight == score_data["weight"]
            assert db_score.notes == score_data["notes"]
            assert db_score.created_by == user.id

    def test_get_score_categories(self, test_client, technique_scoring_setup):
        """Test getting available score categories."""
        user = technique_scoring_setup["user"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Test getting score categories
        response = test_client.get(
            "/api/v1/mitre/techniques/scores/categories",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert "impact" in data
        assert "likelihood" in data
        assert "detectability" in data
        assert "exploitability" in data

    def test_get_top_scored_techniques(self, test_client, technique_scoring_setup):
        """Test getting top-scored techniques."""
        user = technique_scoring_setup["user"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Test getting top-scored techniques
        response = test_client.get(
            "/api/v1/mitre/techniques/top-scored?limit=10",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) > 0
        
        # Verify techniques are sorted by overall score (descending)
        for i in range(1, len(data)):
            assert data[i-1]["overall_score"] >= data[i]["overall_score"]

    def test_invalid_score_value(self, test_client, technique_scoring_setup):
        """Test validation of score values."""
        user = technique_scoring_setup["user"]
        technique = technique_scoring_setup["techniques"][0]  # Data Obfuscation
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Test with score below minimum
        invalid_data = {
            "category": "impact",
            "score": -1.0,  # Invalid: below minimum (0)
            "weight": 0.8,
            "notes": "Test invalid score"
        }
        
        response = test_client.post(
            f"/api/v1/mitre/techniques/{technique.technique_id}/scores",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=invalid_data
        )
        
        assert response.status_code == 422  # Validation error
        
        # Test with score above maximum
        invalid_data["score"] = 11.0  # Invalid: above maximum (10)
        
        response = test_client.post(
            f"/api/v1/mitre/techniques/{technique.technique_id}/scores",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=invalid_data
        )
        
        assert response.status_code == 422  # Validation error

    def test_invalid_weight_value(self, test_client, technique_scoring_setup):
        """Test validation of weight values."""
        user = technique_scoring_setup["user"]
        technique = technique_scoring_setup["techniques"][0]  # Data Obfuscation
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Test with weight below minimum
        invalid_data = {
            "category": "impact",
            "score": 5.0,
            "weight": -0.1,  # Invalid: below minimum (0)
            "notes": "Test invalid weight"
        }
        
        response = test_client.post(
            f"/api/v1/mitre/techniques/{technique.technique_id}/scores",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=invalid_data
        )
        
        assert response.status_code == 422  # Validation error
        
        # Test with weight above maximum
        invalid_data["weight"] = 1.1  # Invalid: above maximum (1)
        
        response = test_client.post(
            f"/api/v1/mitre/techniques/{technique.technique_id}/scores",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=invalid_data
        )
        
        assert response.status_code == 422  # Validation error

    def test_invalid_technique_id(self, test_client, technique_scoring_setup):
        """Test handling of invalid technique IDs."""
        user = technique_scoring_setup["user"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Test with non-existent technique ID
        score_data = {
            "category": "impact",
            "score": 5.0,
            "weight": 0.8,
            "notes": "Test invalid technique"
        }
        
        response = test_client.post(
            "/api/v1/mitre/techniques/T9999/scores",  # Non-existent technique ID
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=score_data
        )
        
        assert response.status_code == 404  # Not found
