"""Tests for ATLAS data importer functionality."""
import json
import pytest
import time
from pathlib import Path
from datetime import datetime
from sqlalchemy.orm import Session

from api.services.atlas_importer import (
    import_atlas_data,
    AtlasImportError,
    _is_valid_subtechnique_id,
    _parse_technique_data
)
from api.models.atlas import (
    AtlasVersion,
    AtlasTactic, 
    AtlasTechnique,
    AtlasMatrix,
    AtlasMatrixItem
)

@pytest.fixture
def test_atlas_data():
    """Create test ATLAS matrix data."""
    return {
        "name": "Test ATLAS Matrix",
        "description": "Test matrix for unit testing",
        "metadata": [
            {"name": "atlas_data_version", "value": "test.1.0"}
        ],
        "techniques": [
            {
                "techniqueID": "AML.T0001",
                "name": "Base Technique 1",
                "description": "A test base technique",
                "tactic": "Test Tactic"
            },
            {
                "techniqueID": "AML.T0001.001",
                "name": "Sub-Technique 1.1",
                "description": "A test sub-technique",
                "tactic": "Test Tactic"
            },
            {
                "techniqueID": "AML.T0002",
                "name": "Base Technique 2",
                "description": "Another test base technique",
                "color": "#FF0000",
                "showSubtechniques": False
            }
        ]
    }

@pytest.fixture
def test_matrix_file(tmp_path, test_atlas_data):
    """Create a temporary test matrix file."""
    matrix_file = tmp_path / "test_matrix.json"
    matrix_file.write_text(json.dumps(test_atlas_data))
    return str(matrix_file)

def test_valid_subtechnique_id():
    """Test subtechnique ID validation."""
    assert not _is_valid_subtechnique_id("AML.T0001")  # Base technique
    assert _is_valid_subtechnique_id("AML.T0001.001")  # Valid subtechnique
    assert not _is_valid_subtechnique_id("INVALID")  # Invalid format
    assert not _is_valid_subtechnique_id("AML.T0001.001.002")  # Too many segments

def test_parse_technique_data():
    """Test technique data parsing."""
    test_data = {
        "techniqueID": "AML.T0001.001",
        "name": "Test Technique",
        "description": "Test description",
        "color": "#FF0000",
        "showSubtechniques": False
    }

    result = _parse_technique_data(test_data)
    assert result["external_id"] == "AML.T0001.001"
    assert result["name"] == "Test Technique"
    assert result["is_subtechnique"] is True
    assert result["parent_technique_id"] == "AML.T0001"
    assert result["color"] == "#FF0000"
    assert result["show_subtechniques"] is False

def test_basic_import(db_session: Session, test_matrix_file: str):
    """Test basic ATLAS data import functionality."""
    import_atlas_data(db_session, test_matrix_file)

    # Verify version was created
    version = db_session.query(AtlasVersion).first()
    assert version is not None
    assert version.version == "test.1.0"
    assert version.is_current is True

    # Verify matrix was created
    matrix = db_session.query(AtlasMatrix).first()
    assert matrix is not None
    assert matrix.name == "Test ATLAS Matrix"

    # Verify techniques were created
    techniques = db_session.query(AtlasTechnique).all()
    assert len(techniques) == 3

    # Verify tactic was created
    tactic = db_session.query(AtlasTactic).first()
    assert tactic is not None
    assert tactic.name == "Test Tactic"

def test_parent_child_relationships(db_session: Session, test_matrix_file: str):
    """Test parent-child technique relationships."""
    import_atlas_data(db_session, test_matrix_file)

    # Get the base technique and its subtechnique
    base = db_session.query(AtlasTechnique).filter_by(external_id="AML.T0001").first()
    sub = db_session.query(AtlasTechnique).filter_by(external_id="AML.T0001.001").first()

    assert base is not None
    assert sub is not None
    assert sub.parent_technique_id == base.id
    assert sub.is_subtechnique is True
    assert base.is_subtechnique is False

def test_version_management(db_session: Session, test_matrix_file: str):
    """Test version management with force update."""
    # First import
    import_atlas_data(db_session, test_matrix_file)
    first_version = db_session.query(AtlasVersion).first()
    original_import_date = first_version.import_date
    first_technique_count = db_session.query(AtlasTechnique).count()

    # Try importing again without force update
    import_atlas_data(db_session, test_matrix_file)
    assert db_session.query(AtlasTechnique).count() == first_technique_count

    # Add delay to ensure different timestamps
    time.sleep(1)

    # Import again with force update
    import_atlas_data(db_session, test_matrix_file, force_update=True)

    # Refresh the session to get the latest data
    db_session.refresh(first_version)
    assert first_version.import_date > original_import_date

def test_invalid_file(db_session: Session, tmp_path: Path):
    """Test error handling for invalid files."""
    invalid_file = tmp_path / "invalid.json"
    invalid_file.write_text("invalid json")

    with pytest.raises(AtlasImportError) as exc:
        import_atlas_data(db_session, str(invalid_file))
    assert "Invalid JSON format" in str(exc.value)

    # Test non-existent file
    with pytest.raises(AtlasImportError) as exc:
        import_atlas_data(db_session, "nonexistent.json")
    assert "not found" in str(exc.value)