"""
Tests for the cron builder API endpoints.

This module contains tests for the cron builder API endpoints, including
building cron expressions from user-friendly inputs and presets.
"""
import pytest
from fastapi.testclient import TestClient

from api.main import app


client = TestClient(app)


@pytest.fixture
def admin_token():
    """Get an admin token for testing."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": "admin", "password": "adminpassword"}
    )
    return response.json()["access_token"]


def test_build_cron(client, admin_token):
    """Test building a cron expression from user-friendly inputs."""
    response = client.post(
        "/api/v1/cron/build",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "minutes": 0,
            "hours": 8,
            "days_of_month": None,
            "months": None,
            "days_of_week": "1-5"
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    
    # Verify response data
    assert data["cron_expression"] == "0 8 * * 1-5"
    assert "description" in data
    assert "Monday through Friday" in data["description"]


def test_build_cron_validation(client, admin_token):
    """Test validation in the build cron endpoint."""
    response = client.post(
        "/api/v1/cron/build",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "minutes": 60,  # Invalid minute
            "hours": 8
        }
    )
    
    assert response.status_code == 400
    assert "out of range" in response.json()["detail"]


def test_build_from_preset(client, admin_token):
    """Test building a cron expression from a preset."""
    response = client.post(
        "/api/v1/cron/preset",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "preset": "daily",
            "hour": 8
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    
    # Verify response data
    assert data["cron_expression"] == "0 8 * * *"
    assert "description" in data
    assert "Every day at 8 AM" in data["description"]


def test_build_from_preset_validation(client, admin_token):
    """Test validation in the build from preset endpoint."""
    response = client.post(
        "/api/v1/cron/preset",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "preset": "daily",
            "hour": 24  # Invalid hour
        }
    )
    
    assert response.status_code == 400
    assert "Invalid hour" in response.json()["detail"]


def test_validate_cron(client, admin_token):
    """Test validating a cron expression."""
    response = client.post(
        "/api/v1/cron/validate",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "cron_expression": "0 8 * * 1-5"
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    
    # Verify response data
    assert data["is_valid"] is True
    assert "description" in data
    assert "Monday through Friday" in data["description"]


def test_validate_cron_invalid(client, admin_token):
    """Test validating an invalid cron expression."""
    response = client.post(
        "/api/v1/cron/validate",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "cron_expression": "0 24 * * *"  # Invalid hour
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    
    # Verify response data
    assert data["is_valid"] is False
    assert "error_message" in data


def test_get_presets(client, admin_token):
    """Test getting a list of available presets."""
    response = client.get(
        "/api/v1/cron/presets",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    
    # Verify response data
    assert isinstance(data, list)
    assert len(data) > 0
    assert all("name" in preset for preset in data)
    assert all("cron_expression" in preset for preset in data)
    assert all("description" in preset for preset in data)


def test_get_description(client, admin_token):
    """Test getting a human-readable description of a cron expression."""
    response = client.get(
        "/api/v1/cron/description?cron_expression=0%208%20*%20*%201-5",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    
    # Verify response data
    assert data["cron_expression"] == "0 8 * * 1-5"
    assert "description" in data
    assert "Monday through Friday" in data["description"]


def test_get_description_invalid(client, admin_token):
    """Test getting a description of an invalid cron expression."""
    response = client.get(
        "/api/v1/cron/description?cron_expression=0%2024%20*%20*%20*",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 400
    assert "Invalid" in response.json()["detail"]


def test_unauthorized_access(client):
    """Test unauthorized access to cron builder endpoints."""
    # Test without token
    response = client.post("/api/v1/cron/build", json={"minutes": 0, "hours": 8})
    assert response.status_code == 401
    
    # Test with non-admin token
    response = client.post(
        "/api/v1/auth/login",
        data={"username": "user", "password": "userpassword"}
    )
    user_token = response.json()["access_token"]
    
    response = client.post(
        "/api/v1/cron/build",
        headers={"Authorization": f"Bearer {user_token}"},
        json={"minutes": 0, "hours": 8}
    )
    assert response.status_code == 200  # Regular users should be able to use the cron builder
