"""
Integration tests for assessments, campaigns, and test cases.

This module contains tests that verify the interactions between different
components of the security testing framework, focusing on the relationships
between assessments, campaigns, and test cases.
"""
import pytest
from api.models import AssessmentDB, CampaignDB, TestCaseDB, User, User<PERSON><PERSON>

def test_assessment_campaign_relationship(client, db_session, test_user):
    """Test the relationship between assessments and campaigns"""
    # Create campaigns
    campaigns = [
        CampaignDB(name=f"Campaign {i}", description=f"Description {i}")
        for i in range(2)
    ]
    for campaign in campaigns:
        db_session.add(campaign)
    db_session.commit()
    
    # Create an assessment with campaigns
    response = client.post(
        "/api/v1/assessments",
        json={
            "name": "Test Assessment",
            "description": "Test Description",
            "target_system": "Test System",
            "assessment_type": "vulnerability",
            "status": "planned",
            "campaign_ids": [campaign.id for campaign in campaigns]
        },
        headers={"Authorization": f"Bearer {test_user_token(test_user, client)}"}
    )
    assert response.status_code == 200
    assessment_data = response.json()
    
    # Verify assessment has campaigns
    assert len(assessment_data["campaigns"]) == 2
    
    # Get assessment by ID to verify relationship
    response = client.get(
        f"/api/v1/assessments/{assessment_data['id']}",
        headers={"Authorization": f"Bearer {test_user_token(test_user, client)}"}
    )
    assert response.status_code == 200
    assessment_data = response.json()
    assert len(assessment_data["campaigns"]) == 2
    
    # Get campaigns to verify they include the assessment
    for campaign in campaigns:
        response = client.get(f"/api/v1/campaigns/{campaign.id}")
        assert response.status_code == 200
        campaign_data = response.json()
        assert "assessments" in campaign_data
        assert any(a["id"] == assessment_data["id"] for a in campaign_data["assessments"])

def test_campaign_test_case_relationship(client, db_session):
    """Test the relationship between campaigns and test cases"""
    # Create a campaign
    response = client.post(
        "/api/v1/campaigns",
        json={
            "name": "Test Campaign",
            "description": "Test Description",
            "status": "active"
        }
    )
    assert response.status_code == 200
    campaign_data = response.json()
    
    # Create test cases for the campaign
    test_cases = []
    for i in range(3):
        response = client.post(
            "/api/v1/test-cases",
            json={
                "name": f"Test Case {i}",
                "description": f"Description {i}",
                "campaign_id": campaign_data["id"],
                "expected_result": f"Expected {i}",
                "status": "pending"
            }
        )
        assert response.status_code == 200
        test_cases.append(response.json())
    
    # Get campaign to verify it includes test cases
    response = client.get(f"/api/v1/campaigns/{campaign_data['id']}")
    assert response.status_code == 200
    campaign_data = response.json()
    assert "test_cases" in campaign_data
    assert len(campaign_data["test_cases"]) == 3
    
    # Get test cases for campaign
    response = client.get(f"/api/v1/campaigns/{campaign_data['id']}/test-cases")
    assert response.status_code == 200
    test_case_data = response.json()
    assert len(test_case_data) == 3
    
    # Update a test case
    response = client.put(
        f"/api/v1/test-cases/{test_cases[0]['id']}",
        json={
            "name": "Updated Test Case",
            "description": "Updated Description",
            "expected_result": "Updated Expected",
            "actual_result": "Actual Result",
            "status": "passed"
        }
    )
    assert response.status_code == 200
    
    # Get campaign statistics
    response = client.get(f"/api/v1/campaigns/{campaign_data['id']}/statistics")
    assert response.status_code == 200
    stats_data = response.json()
    assert stats_data["total_test_cases"] == 3
    assert stats_data["status_counts"]["passed"] == 1
    assert stats_data["status_counts"]["pending"] == 2

def test_full_assessment_workflow(client, db_session, test_user):
    """Test a complete workflow from assessment creation to test case execution"""
    # 1. Create a campaign
    response = client.post(
        "/api/v1/campaigns",
        json={
            "name": "Security Assessment Campaign",
            "description": "Comprehensive security testing",
            "status": "active"
        }
    )
    assert response.status_code == 200
    campaign_data = response.json()
    
    # 2. Create an assessment linked to the campaign
    response = client.post(
        "/api/v1/assessments",
        json={
            "name": "Q1 Security Assessment",
            "description": "First quarter security review",
            "target_system": "Payment Processing System",
            "assessment_type": "penetration",
            "status": "planned",
            "campaign_ids": [campaign_data["id"]]
        },
        headers={"Authorization": f"Bearer {test_user_token(test_user, client)}"}
    )
    assert response.status_code == 200
    assessment_data = response.json()
    
    # 3. Create test cases for the campaign
    test_case_ids = []
    test_cases = [
        {
            "name": "SQL Injection Test",
            "description": "Test for SQL injection vulnerabilities",
            "expected_result": "No SQL injection vulnerabilities found",
            "status": "pending"
        },
        {
            "name": "XSS Vulnerability Test",
            "description": "Test for cross-site scripting vulnerabilities",
            "expected_result": "No XSS vulnerabilities found",
            "status": "pending"
        },
        {
            "name": "Authentication Bypass Test",
            "description": "Test for authentication bypass vulnerabilities",
            "expected_result": "No authentication bypass vulnerabilities found",
            "status": "pending"
        }
    ]
    
    for test_case in test_cases:
        test_case["campaign_id"] = campaign_data["id"]
        response = client.post("/api/v1/test-cases", json=test_case)
        assert response.status_code == 200
        test_case_ids.append(response.json()["id"])
    
    # 4. Update assessment status to in-progress
    response = client.put(
        f"/api/v1/assessments/{assessment_data['id']}",
        json={
            "name": assessment_data["name"],
            "description": assessment_data["description"],
            "target_system": assessment_data["target_system"],
            "assessment_type": assessment_data["assessment_type"],
            "status": "in-progress",
            "campaign_ids": [campaign_data["id"]]
        },
        headers={"Authorization": f"Bearer {test_user_token(test_user, client)}"}
    )
    assert response.status_code == 200
    
    # 5. Update test case statuses as they are executed
    # First test case: passed
    response = client.patch(
        f"/api/v1/test-cases/{test_case_ids[0]}/status",
        json={"status": "passed", "actual_result": "No SQL injection vulnerabilities found"}
    )
    assert response.status_code == 200
    
    # Second test case: failed
    response = client.patch(
        f"/api/v1/test-cases/{test_case_ids[1]}/status",
        json={"status": "failed", "actual_result": "XSS vulnerability found in search form"}
    )
    assert response.status_code == 200
    
    # Third test case: in-progress
    response = client.patch(
        f"/api/v1/test-cases/{test_case_ids[2]}/status",
        json={"status": "in-progress"}
    )
    assert response.status_code == 200
    
    # 6. Get campaign statistics
    response = client.get(f"/api/v1/campaigns/{campaign_data['id']}/statistics")
    assert response.status_code == 200
    stats_data = response.json()
    assert stats_data["total_test_cases"] == 3
    assert stats_data["status_counts"]["passed"] == 1
    assert stats_data["status_counts"]["failed"] == 1
    assert stats_data["status_counts"]["in-progress"] == 1
    
    # 7. Complete the assessment
    # Update the last test case
    response = client.patch(
        f"/api/v1/test-cases/{test_case_ids[2]}/status",
        json={"status": "passed", "actual_result": "No authentication bypass vulnerabilities found"}
    )
    assert response.status_code == 200
    
    # Update assessment status to completed
    response = client.put(
        f"/api/v1/assessments/{assessment_data['id']}",
        json={
            "name": assessment_data["name"],
            "description": assessment_data["description"],
            "target_system": assessment_data["target_system"],
            "assessment_type": assessment_data["assessment_type"],
            "status": "completed",
            "campaign_ids": [campaign_data["id"]]
        },
        headers={"Authorization": f"Bearer {test_user_token(test_user, client)}"}
    )
    assert response.status_code == 200
    
    # 8. Verify final state
    # Get assessment
    response = client.get(
        f"/api/v1/assessments/{assessment_data['id']}",
        headers={"Authorization": f"Bearer {test_user_token(test_user, client)}"}
    )
    assert response.status_code == 200
    final_assessment = response.json()
    assert final_assessment["status"] == "completed"
    
    # Get campaign statistics
    response = client.get(f"/api/v1/campaigns/{campaign_data['id']}/statistics")
    assert response.status_code == 200
    final_stats = response.json()
    assert final_stats["total_test_cases"] == 3
    assert final_stats["status_counts"]["passed"] == 2
    assert final_stats["status_counts"]["failed"] == 1
    assert "in-progress" not in final_stats["status_counts"] or final_stats["status_counts"]["in-progress"] == 0

def test_assessment_campaign_test_case_deletion(client, db_session, test_user):
    """Test deletion behavior across related entities"""
    # Create a campaign
    campaign = CampaignDB(name="Test Campaign", description="Test Description")
    db_session.add(campaign)
    db_session.commit()
    db_session.refresh(campaign)
    
    # Create an assessment linked to the campaign
    assessment = AssessmentDB(
        name="Test Assessment",
        description="Test Description",
        target_system="Test System",
        assessment_type="vulnerability",
        status="planned",
        created_by=test_user.id
    )
    assessment.campaigns.append(campaign)
    db_session.add(assessment)
    db_session.commit()
    
    # Create test cases for the campaign
    test_cases = [
        TestCaseDB(
            name=f"Test Case {i}",
            description=f"Description {i}",
            campaign_id=campaign.id,
            expected_result=f"Expected {i}"
        )
        for i in range(3)
    ]
    for test_case in test_cases:
        db_session.add(test_case)
    db_session.commit()
    
    # Test deleting a test case
    response = client.delete(f"/api/v1/test-cases/{test_cases[0].id}")
    assert response.status_code == 200
    
    # Verify test case is soft deleted
    db_session.refresh(test_cases[0])
    assert test_cases[0].deleted_time is not None
    
    # Verify campaign still exists with remaining test cases
    response = client.get(f"/api/v1/campaigns/{campaign.id}/test-cases")
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 2
    
    # Test deleting the campaign
    response = client.delete(f"/api/v1/campaigns/{campaign.id}")
    assert response.status_code == 200
    
    # Verify campaign is soft deleted
    db_session.refresh(campaign)
    assert campaign.deleted_time is not None
    
    # Verify assessment still exists but without the campaign
    response = client.get(
        f"/api/v1/assessments/{assessment.id}",
        headers={"Authorization": f"Bearer {test_user_token(test_user, client)}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert len(data["campaigns"]) == 0
    
    # Test deleting the assessment
    response = client.delete(
        f"/api/v1/assessments/{assessment.id}",
        headers={"Authorization": f"Bearer {test_user_token(test_user, client)}"}
    )
    assert response.status_code == 200
    
    # Verify assessment is soft deleted
    db_session.refresh(assessment)
    assert assessment.deleted_time is not None 