"""Tests for custom middleware."""
import pytest
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.testclient import TestClient
from api.middleware.error_handler import ErrorHandlerMiddleware
from api.schemas import <PERSON>rrorResponse

def test_error_handler_middleware():
    """Test that the error handler middleware properly handles exceptions."""
    app = FastAPI()
    app.add_middleware(ErrorHandlerMiddleware)

    @app.get("/test-error")
    async def test_error():
        raise Exception("Test error")

    @app.get("/test-http-error")
    async def test_http_error():
        raise HTTPException(status_code=400, detail="Bad request")

    @app.get("/test-validation-error")
    async def test_validation_error():
        raise ValueError("Invalid input")

    client = TestClient(app)

    # Test handling of generic exceptions
    response = client.get("/test-error")
    assert response.status_code == 500
    data = response.json()
    assert data["detail"] == "Internal server error"
    assert data["code"] == "INTERNAL_ERROR"
    assert "timestamp" in data

    # Test that HTTP exceptions are passed through
    response = client.get("/test-http-error")
    assert response.status_code == 400
    assert response.json()["detail"] == "Bad request"

    # Test handling of validation errors
    response = client.get("/test-validation-error")
    assert response.status_code == 500
    data = response.json()
    assert data["detail"] == "Internal server error"
    assert data["code"] == "INTERNAL_ERROR"

    # Test normal responses are unaffected
    @app.get("/test-normal")
    async def test_normal():
        return {"message": "ok"}

    response = client.get("/test-normal")
    assert response.status_code == 200
    assert response.json() == {"message": "ok"}