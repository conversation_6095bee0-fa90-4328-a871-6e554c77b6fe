"""Tests for STIX bulk import/export functionality."""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime
import uuid

from api.models import StixDB, StixObject, StixBundle

def test_bulk_import_stix_objects(client: TestClient, db_session: Session):
    """Test bulk importing STIX objects."""
    # Create test bundle
    bundle = {
        "type": "bundle",
        "id": f"bundle--{str(uuid.uuid4())}",
        "objects": [
            {
                "type": "attack-pattern",
                "id": f"attack-pattern--{str(uuid.uuid4())}",
                "created": datetime.utcnow().isoformat(),
                "modified": datetime.utcnow().isoformat(),
                "name": "Test Attack Pattern 1",
                "description": "Test description 1"
            },
            {
                "type": "attack-pattern",
                "id": f"attack-pattern--{str(uuid.uuid4())}",
                "created": datetime.utcnow().isoformat(),
                "modified": datetime.utcnow().isoformat(),
                "name": "Test Attack Pattern 2",
                "description": "Test description 2"
            }
        ]
    }
    
    response = client.post("/api/v1/mitre/stix/bulk/import", json=bundle)
    assert response.status_code == 200
    result = response.json()
    assert result["status"] == "success"
    assert result["imported_count"] == 2
    assert result["failed_count"] == 0
    assert len(result["errors"]) == 0

    # Verify objects were imported
    db_objects = db_session.query(StixDB).all()
    assert len(db_objects) == 2

def test_bulk_import_invalid_stix_objects(client: TestClient):
    """Test bulk importing invalid STIX objects."""
    # Create invalid bundle
    bundle = {
        "type": "bundle",
        "id": f"bundle--{str(uuid.uuid4())}",
        "objects": [
            {
                "type": "invalid-type",
                "id": "invalid-id",
                "name": "Invalid Object"
            }
        ]
    }
    
    response = client.post("/api/v1/mitre/stix/bulk/import", json=bundle)
    assert response.status_code == 200  # Still succeeds but with errors
    result = response.json()
    assert result["imported_count"] == 0
    assert result["failed_count"] == 1
    assert len(result["errors"]) == 1

def test_bulk_export_stix_objects(client: TestClient, db_session: Session):
    """Test exporting STIX objects as a bundle."""
    # Create test objects in DB
    obj1 = StixDB.from_stix({
        "type": "attack-pattern",
        "id": f"attack-pattern--{str(uuid.uuid4())}",
        "created": datetime.utcnow().isoformat(),
        "modified": datetime.utcnow().isoformat(),
        "name": "Test Attack Pattern",
        "description": "Test description"
    })
    db_session.add(obj1)
    db_session.commit()
    
    # Test export
    response = client.get("/api/v1/mitre/stix/bulk/export")
    assert response.status_code == 200
    bundle = response.json()
    assert bundle["type"] == "bundle"
    assert len(bundle["objects"]) == 1
    assert bundle["objects"][0]["type"] == "attack-pattern"

def test_bulk_export_with_type_filter(client: TestClient, db_session: Session):
    """Test exporting STIX objects with type filter."""
    # Create objects of different types
    obj1 = StixDB.from_stix({
        "type": "attack-pattern",
        "id": f"attack-pattern--{str(uuid.uuid4())}",
        "created": datetime.utcnow().isoformat(),
        "modified": datetime.utcnow().isoformat(),
        "name": "Test Attack Pattern"
    })
    obj2 = StixDB.from_stix({
        "type": "malware",
        "id": f"malware--{str(uuid.uuid4())}",
        "created": datetime.utcnow().isoformat(),
        "modified": datetime.utcnow().isoformat(),
        "name": "Test Malware"
    })
    db_session.add_all([obj1, obj2])
    db_session.commit()
    
    # Test export with filter
    response = client.get("/api/v1/mitre/stix/bulk/export?type_filter=attack-pattern")
    assert response.status_code == 200
    bundle = response.json()
    assert len(bundle["objects"]) == 1
    assert bundle["objects"][0]["type"] == "attack-pattern"
