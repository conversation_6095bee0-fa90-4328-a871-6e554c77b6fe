
"""Tests for scoring functionality."""
import pytest
from api.models.mitre import MitreTechnique
from api.utils.scoring import calculate_technique_score

def test_technique_scoring(client, db_session):
    """Test technique scoring calculation"""
    technique = MitreTechnique(
        technique_id="T1234",
        name="Test Technique",
        description="Test Description"
    )
    db_session.add(technique)
    db_session.commit()
    
    score = calculate_technique_score(technique.id)
    assert isinstance(score, float)
    assert 0 <= score <= 100
import pytest
from api.utils.scoring import calculate_technique_score
from api.models.mitre import MitreTechnique

def test_calculate_technique_score():
    """Test technique scoring calculation."""
    technique = MitreTechnique(
        technique_id="T1234",
        name="Test",
        description="Test"
    )
    
    # Test default score
    assert calculate_technique_score(technique) == 1.0
    
    # Test with severity parameter
    score = calculate_technique_score(technique, {"severity": 2.0})
    assert score == 2.0
    
    # Test score capping at 10.0
    score = calculate_technique_score(technique, {"severity": 20.0})
    assert score == 10.0
