"""Unit tests for the ThreatDefenseMapper class."""
import pytest
from sqlalchemy.exc import SQLAlchemyError
from api.threat_defense.mapper import ThreatDefenseMapper
from api.models.mitre import MitreTechnique
from api.models.d3fend import D3fendMappingDB


@pytest.fixture
def sample_techniques(db_session):
    """Create sample MITRE techniques for testing."""
    techniques = [
        MitreTechnique(
            technique_id="T1566",
            name="Phishing",
            description="Phishing attack technique",
            version_id=1
        ),
        MitreTechnique(
            technique_id="T1078",
            name="Valid Accounts",
            description="Valid accounts technique",
            version_id=1
        )
    ]
    for technique in techniques:
        db_session.add(technique)
    db_session.commit()
    return techniques


@pytest.fixture
def sample_mappings(db_session, sample_techniques):
    """Create sample D3FEND mappings for testing."""
    mappings = [
        D3fendMappingDB(
            technique_id="T1566",
            name="Email Filter",
            effectiveness_score=0.8,
            description="Filter suspicious emails"
        ),
        D3fendMappingDB(
            technique_id="T1566",
            name="Link Analysis",
            effectiveness_score=0.6,
            description="Analyze suspicious links"
        ),
        D3fendMappingDB(
            technique_id="T1078",
            name="MFA",
            effectiveness_score=0.9,
            description="Multi-factor authentication"
        )
    ]
    for mapping in mappings:
        db_session.add(mapping)
    db_session.commit()
    return mappings


def test_get_attack_path_coverage(db_session, sample_mappings):
    """Test calculation of attack path coverage."""
    mapper = ThreatDefenseMapper(db_session)
    technique_sequence = ["T1566", "T1078"]

    coverage = mapper.get_attack_path_coverage(technique_sequence)

    assert "techniques" in coverage
    assert "overall_coverage" in coverage
    assert "average_coverage" in coverage

    assert 0 <= coverage["overall_coverage"] <= 1.0
    assert 0 <= coverage["average_coverage"] <= 1.0
    assert len(coverage["techniques"]) == 2

    # T1566 has 2 controls (0.4 coverage)
    assert coverage["techniques"]["T1566"] == 0.4
    # T1078 has 1 control (0.2 coverage)
    assert coverage["techniques"]["T1078"] == 0.2

    # Overall should be minimum of all techniques
    assert coverage["overall_coverage"] == 0.2
    # Average should be mean of all techniques
    assert coverage["average_coverage"] == 0.3


def test_get_attack_path_coverage_empty(db_session):
    """Test coverage calculation with empty technique sequence."""
    mapper = ThreatDefenseMapper(db_session)
    coverage = mapper.get_attack_path_coverage([])

    assert coverage["techniques"] == {}
    assert coverage["overall_coverage"] == 0.0
    assert coverage["average_coverage"] == 0.0


def test_get_attack_path_coverage_unknown_technique(db_session, sample_mappings):
    """Test coverage calculation with unknown technique ID."""
    mapper = ThreatDefenseMapper(db_session)
    coverage = mapper.get_attack_path_coverage(["T9999"])

    assert coverage["techniques"]["T9999"] == 0.0
    assert coverage["overall_coverage"] == 0.0
    assert coverage["average_coverage"] == 0.0


def test_find_d3fend_countermeasures(db_session, sample_mappings):
    """Test finding D3FEND countermeasures for techniques."""
    mapper = ThreatDefenseMapper(db_session)
    technique_sequence = ["T1566"]

    countermeasures = mapper.find_d3fend_countermeasures(technique_sequence)

    assert "T1566" in countermeasures
    assert len(countermeasures["T1566"]) == 2

    # Check first countermeasure
    assert countermeasures["T1566"][0]["name"] == "Email Filter"
    assert countermeasures["T1566"][0]["effectiveness"] == 0.8
    assert countermeasures["T1566"][0]["description"] == "Filter suspicious emails"

    # Check second countermeasure
    assert countermeasures["T1566"][1]["name"] == "Link Analysis"
    assert countermeasures["T1566"][1]["effectiveness"] == 0.6
    assert countermeasures["T1566"][1]["description"] == "Analyze suspicious links"


def test_find_d3fend_countermeasures_empty(db_session):
    """Test finding countermeasures with empty technique sequence."""
    mapper = ThreatDefenseMapper(db_session)
    countermeasures = mapper.find_d3fend_countermeasures([])

    assert countermeasures == {}


def test_analyze_defense_gaps(db_session, sample_mappings, sample_techniques):
    """Test identification of defense gaps."""
    mapper = ThreatDefenseMapper(db_session)
    technique_sequence = ["T1566", "T1078"]

    gaps = mapper.analyze_defense_gaps(technique_sequence)

    assert len(gaps) == 1  # T1078 has low coverage
    gap = gaps[0]
    assert gap["technique_id"] == "T1078"
    assert gap["technique_name"] == "Valid Accounts"
    assert gap["coverage"] == 0.2
    assert "Implement additional controls" in gap["recommendation"]


def test_attack_path_coverage_duplicate_techniques(db_session, sample_mappings):
    """Test coverage calculation with duplicate techniques in sequence."""
    mapper = ThreatDefenseMapper(db_session)
    technique_sequence = ["T1566", "T1566"]  # Duplicate technique

    coverage = mapper.get_attack_path_coverage(technique_sequence)

    assert len(coverage["techniques"]) == 1  # Should only count unique techniques
    assert coverage["techniques"]["T1566"] == 0.4
    assert coverage["overall_coverage"] == 0.4
    assert coverage["average_coverage"] == 0.4


def test_invalid_technique_id_format(db_session):
    """Test handling of invalid technique ID format."""
    mapper = ThreatDefenseMapper(db_session)

    with pytest.raises(ValueError, match="Invalid technique ID format"):
        mapper.get_attack_path_coverage(["invalid-id"])


def test_too_many_techniques(db_session):
    """Test handling of too many techniques."""
    mapper = ThreatDefenseMapper(db_session)
    too_many = ["T1234"] * (mapper.MAX_TECHNIQUES + 1)

    with pytest.raises(ValueError, match="Too many techniques"):
        mapper.get_attack_path_coverage(too_many)


def test_non_string_technique_id(db_session):
    """Test handling of non-string technique ID."""
    mapper = ThreatDefenseMapper(db_session)

    with pytest.raises(ValueError, match="Invalid technique ID type"):
        mapper.get_attack_path_coverage([123])


def test_sql_injection_attempt(db_session):
    """Test protection against SQL injection attempts."""
    mapper = ThreatDefenseMapper(db_session)
    malicious_id = "T1566' OR '1'='1"

    with pytest.raises(ValueError, match="Invalid technique ID format"):
        mapper.get_attack_path_coverage([malicious_id])


def test_db_error_handling(db_session):
    """Test handling of database errors."""
    mapper = ThreatDefenseMapper(db_session)

    # Force a database error by closing the session
    db_session.close()

    with pytest.raises(SQLAlchemyError):
        mapper.get_attack_path_coverage(["T1566"])