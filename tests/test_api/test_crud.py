"""Tests for CRUD operations."""
import pytest
from datetime import datetime
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session
from api.database import get_db
from api.models.base import Base
from api.models.user import User
from api.main import app

@pytest.fixture
def test_user():
    """Create a test user."""
    return {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpass123"
    }

def test_create_user(client: TestClient, test_user: dict):
    """Test user creation."""
    response = client.post("/api/v1/users/", json=test_user)
    assert response.status_code == 200
    data = response.json()
    assert data["username"] == test_user["username"]
    assert data["email"] == test_user["email"]
    assert "id" in data

def test_get_user(client: TestClient, test_user: dict):
    """Test getting a user."""
    # Create user first
    create_response = client.post("/api/v1/users/", json=test_user)
    user_id = create_response.json()["id"]
    
    # Get user
    response = client.get(f"/api/v1/users/{user_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["username"] == test_user["username"]
    assert data["email"] == test_user["email"]

def test_update_user(client: TestClient, test_user: dict):
    """Test updating a user."""
    # Create user first
    create_response = client.post("/api/v1/users/", json=test_user)
    user_id = create_response.json()["id"]
    
    # Update user
    update_data = {"username": "updateduser"}
    response = client.patch(f"/api/v1/users/{user_id}", json=update_data)
    assert response.status_code == 200
    data = response.json()
    assert data["username"] == "updateduser"
    assert data["email"] == test_user["email"]

def test_soft_delete_user(client: TestClient, test_user: dict):
    """Test soft deleting a user."""
    # Create user first
    create_response = client.post("/api/v1/users/", json=test_user)
    user_id = create_response.json()["id"]
    
    # Soft delete user
    response = client.delete(f"/api/v1/users/{user_id}")
    assert response.status_code == 200
    
    # Try to get user (should fail with default settings)
    response = client.get(f"/api/v1/users/{user_id}")
    assert response.status_code == 404
    
    # Get user including deleted
    response = client.get(f"/api/v1/users/{user_id}?include_deleted=true")
    assert response.status_code == 200
    data = response.json()
    assert data["deleted_at"] is not None

def test_restore_user(client: TestClient, test_user: dict):
    """Test restoring a soft-deleted user."""
    # Create and delete user first
    create_response = client.post("/api/v1/users/", json=test_user)
    user_id = create_response.json()["id"]
    client.delete(f"/api/v1/users/{user_id}")
    
    # Restore user
    response = client.post(f"/api/v1/users/{user_id}/restore")
    assert response.status_code == 200
    
    # Verify user is accessible again
    response = client.get(f"/api/v1/users/{user_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["deleted_at"] is None
