"""Tests for the advanced execution framework."""
import pytest
import json
from typing import Dict, Any, List
from datetime import datetime, timedelta

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.models.base import TestCaseDB, CampaignDB
from api.models.execution import ExecutionDB, ExecutionStatus, ExecutionResult
from tests.utils.assertions import (
    assert_json_response, 
    assert_pagination_response,
    assert_datetime_close
)
from tests.utils.factories import TestCaseFactory, CampaignFactory


@pytest.fixture
def test_execution(db_session: Session, test_case: TestCaseDB):
    """Create a test execution."""
    execution = ExecutionDB(
        test_case_id=test_case.id,
        status=ExecutionStatus.PENDING,
        parameters={"param1": "value1", "param2": "value2"},
        scheduled_time=datetime.utcnow() + timedelta(hours=1)
    )
    db_session.add(execution)
    db_session.commit()
    db_session.refresh(execution)
    return execution


@pytest.mark.execution
@pytest.mark.integration
class TestExecutionFramework:
    """Test the advanced execution framework."""

    def test_create_execution(self, client: TestClient, test_admin_headers: Dict[str, str], test_case: TestCaseDB):
        """Test creating a new execution."""
        execution_data = {
            "test_case_id": test_case.id,
            "parameters": {"param1": "value1", "param2": "value2"},
            "scheduled_time": (datetime.utcnow() + timedelta(hours=1)).isoformat(),
            "priority": 1
        }
        
        response = client.post(
            "/api/v1/executions/",
            headers=test_admin_headers,
            json=execution_data
        )
        assert response.status_code == 201
        data = response.json()
        
        assert data["test_case_id"] == test_case.id
        assert data["status"] == ExecutionStatus.PENDING.value
        assert data["parameters"] == execution_data["parameters"]
        assert "id" in data
        assert "created_on" in data

    def test_get_executions(
        self, 
        client: TestClient, 
        test_admin_headers: Dict[str, str], 
        test_execution: ExecutionDB
    ):
        """Test getting executions."""
        response = client.get(
            "/api/v1/executions/",
            headers=test_admin_headers
        )
        assert response.status_code == 200
        data = response.json()
        
        assert_pagination_response(data)
        assert data["total"] >= 1
        
        # Find our test execution in the results
        found = False
        for execution in data["items"]:
            if execution["id"] == test_execution.id:
                found = True
                assert execution["test_case_id"] == test_execution.test_case_id
                assert execution["status"] == test_execution.status.value
                assert execution["parameters"] == test_execution.parameters
                break
        
        assert found, f"Test execution {test_execution.id} not found in response"

    def test_get_execution_by_id(
        self, 
        client: TestClient, 
        test_admin_headers: Dict[str, str], 
        test_execution: ExecutionDB
    ):
        """Test getting a specific execution by ID."""
        response = client.get(
            f"/api/v1/executions/{test_execution.id}",
            headers=test_admin_headers
        )
        assert response.status_code == 200
        execution = response.json()
        
        assert execution["id"] == test_execution.id
        assert execution["test_case_id"] == test_execution.test_case_id
        assert execution["status"] == test_execution.status.value
        assert execution["parameters"] == test_execution.parameters

    def test_update_execution_status(
        self, 
        client: TestClient, 
        test_admin_headers: Dict[str, str], 
        test_execution: ExecutionDB
    ):
        """Test updating execution status."""
        update_data = {
            "status": ExecutionStatus.RUNNING.value
        }
        
        response = client.patch(
            f"/api/v1/executions/{test_execution.id}",
            headers=test_admin_headers,
            json=update_data
        )
        assert response.status_code == 200
        execution = response.json()
        
        assert execution["id"] == test_execution.id
        assert execution["status"] == ExecutionStatus.RUNNING.value

    def test_complete_execution(
        self, 
        client: TestClient, 
        test_admin_headers: Dict[str, str], 
        test_execution: ExecutionDB
    ):
        """Test completing an execution with results."""
        result_data = {
            "status": ExecutionStatus.COMPLETED.value,
            "result": {
                "outcome": ExecutionResult.PASSED.value,
                "details": {
                    "duration_ms": 1500,
                    "steps_passed": 5,
                    "steps_failed": 0,
                    "logs": ["Step 1 passed", "Step 2 passed", "Step 3 passed", "Step 4 passed", "Step 5 passed"]
                }
            }
        }
        
        response = client.patch(
            f"/api/v1/executions/{test_execution.id}",
            headers=test_admin_headers,
            json=result_data
        )
        assert response.status_code == 200
        execution = response.json()
        
        assert execution["id"] == test_execution.id
        assert execution["status"] == ExecutionStatus.COMPLETED.value
        assert execution["result"]["outcome"] == ExecutionResult.PASSED.value
        assert execution["result"]["details"]["duration_ms"] == 1500
        assert len(execution["result"]["details"]["logs"]) == 5

    def test_fail_execution(
        self, 
        client: TestClient, 
        test_admin_headers: Dict[str, str], 
        test_execution: ExecutionDB
    ):
        """Test failing an execution with results."""
        result_data = {
            "status": ExecutionStatus.COMPLETED.value,
            "result": {
                "outcome": ExecutionResult.FAILED.value,
                "details": {
                    "duration_ms": 1200,
                    "steps_passed": 3,
                    "steps_failed": 2,
                    "logs": [
                        "Step 1 passed", 
                        "Step 2 passed", 
                        "Step 3 passed", 
                        "Step 4 failed: Expected X but got Y", 
                        "Step 5 failed: Connection timeout"
                    ],
                    "error": "Test failed due to 2 failed steps"
                }
            }
        }
        
        response = client.patch(
            f"/api/v1/executions/{test_execution.id}",
            headers=test_admin_headers,
            json=result_data
        )
        assert response.status_code == 200
        execution = response.json()
        
        assert execution["id"] == test_execution.id
        assert execution["status"] == ExecutionStatus.COMPLETED.value
        assert execution["result"]["outcome"] == ExecutionResult.FAILED.value
        assert execution["result"]["details"]["steps_passed"] == 3
        assert execution["result"]["details"]["steps_failed"] == 2
        assert "error" in execution["result"]["details"]

    def test_cancel_execution(
        self, 
        client: TestClient, 
        test_admin_headers: Dict[str, str], 
        test_execution: ExecutionDB
    ):
        """Test canceling an execution."""
        update_data = {
            "status": ExecutionStatus.CANCELLED.value
        }
        
        response = client.patch(
            f"/api/v1/executions/{test_execution.id}",
            headers=test_admin_headers,
            json=update_data
        )
        assert response.status_code == 200
        execution = response.json()
        
        assert execution["id"] == test_execution.id
        assert execution["status"] == ExecutionStatus.CANCELLED.value

    def test_delete_execution(
        self, 
        client: TestClient, 
        test_admin_headers: Dict[str, str], 
        test_execution: ExecutionDB
    ):
        """Test deleting an execution."""
        response = client.delete(
            f"/api/v1/executions/{test_execution.id}",
            headers=test_admin_headers
        )
        assert response.status_code == 204
        
        # Verify execution is deleted
        response = client.get(
            f"/api/v1/executions/{test_execution.id}",
            headers=test_admin_headers
        )
        assert response.status_code == 404

    def test_bulk_execution(
        self, 
        client: TestClient, 
        test_admin_headers: Dict[str, str], 
        test_campaign: CampaignDB
    ):
        """Test bulk execution of all test cases in a campaign."""
        # Create multiple test cases for the campaign
        test_cases = []
        for i in range(3):
            test_case = TestCaseFactory.create(
                db_session=client.app.dependency_overrides[Session](),
                name=f"Bulk Test Case {i+1}",
                campaign_id=test_campaign.id
            )
            test_cases.append(test_case)
        
        # Trigger bulk execution
        response = client.post(
            f"/api/v1/campaigns/{test_campaign.id}/execute",
            headers=test_admin_headers
        )
        assert response.status_code == 200
        data = response.json()
        
        assert "executions" in data
        assert len(data["executions"]) == len(test_cases)
        
        # Verify all test cases have executions
        for execution in data["executions"]:
            assert execution["status"] == ExecutionStatus.PENDING.value
            assert execution["test_case_id"] in [tc.id for tc in test_cases]

    def test_execution_history(
        self, 
        client: TestClient, 
        test_admin_headers: Dict[str, str], 
        test_case: TestCaseDB
    ):
        """Test getting execution history for a test case."""
        # Create multiple executions for the test case
        executions = []
        for i in range(3):
            execution = ExecutionDB(
                test_case_id=test_case.id,
                status=ExecutionStatus.COMPLETED,
                parameters={"run": i+1},
                result={
                    "outcome": ExecutionResult.PASSED.value if i % 2 == 0 else ExecutionResult.FAILED.value,
                    "details": {"run": i+1}
                }
            )
            client.app.dependency_overrides[Session]().add(execution)
            client.app.dependency_overrides[Session]().commit()
            client.app.dependency_overrides[Session]().refresh(execution)
            executions.append(execution)
        
        # Get execution history
        response = client.get(
            f"/api/v1/test-cases/{test_case.id}/executions",
            headers=test_admin_headers
        )
        assert response.status_code == 200
        data = response.json()
        
        assert_pagination_response(data)
        assert data["total"] >= len(executions)
        
        # Verify all our executions are in the history
        execution_ids = [execution.id for execution in executions]
        for execution in data["items"]:
            if execution["id"] in execution_ids:
                assert execution["test_case_id"] == test_case.id

    def test_execution_statistics(
        self, 
        client: TestClient, 
        test_admin_headers: Dict[str, str], 
        test_campaign: CampaignDB
    ):
        """Test getting execution statistics for a campaign."""
        # Create test cases and executions with different statuses
        test_cases = []
        for i in range(5):
            test_case = TestCaseFactory.create(
                db_session=client.app.dependency_overrides[Session](),
                name=f"Stats Test Case {i+1}",
                campaign_id=test_campaign.id
            )
            test_cases.append(test_case)
        
        # Create executions with different statuses
        statuses = [
            ExecutionStatus.COMPLETED,  # PASSED
            ExecutionStatus.COMPLETED,  # FAILED
            ExecutionStatus.RUNNING,
            ExecutionStatus.PENDING,
            ExecutionStatus.CANCELLED
        ]
        
        results = [
            {"outcome": ExecutionResult.PASSED.value, "details": {}},
            {"outcome": ExecutionResult.FAILED.value, "details": {}},
            None,
            None,
            None
        ]
        
        for i, test_case in enumerate(test_cases):
            execution = ExecutionDB(
                test_case_id=test_case.id,
                status=statuses[i],
                parameters={},
                result=results[i]
            )
            client.app.dependency_overrides[Session]().add(execution)
            client.app.dependency_overrides[Session]().commit()
        
        # Get execution statistics
        response = client.get(
            f"/api/v1/campaigns/{test_campaign.id}/statistics",
            headers=test_admin_headers
        )
        assert response.status_code == 200
        stats = response.json()
        
        assert "total_test_cases" in stats
        assert stats["total_test_cases"] >= len(test_cases)
        
        assert "execution_status" in stats
        assert "completed" in stats["execution_status"]
        assert stats["execution_status"]["completed"] >= 2
        
        assert "running" in stats["execution_status"]
        assert stats["execution_status"]["running"] >= 1
        
        assert "pending" in stats["execution_status"]
        assert stats["execution_status"]["pending"] >= 1
        
        assert "cancelled" in stats["execution_status"]
        assert stats["execution_status"]["cancelled"] >= 1
        
        assert "execution_results" in stats
        assert "passed" in stats["execution_results"]
        assert stats["execution_results"]["passed"] >= 1
        
        assert "failed" in stats["execution_results"]
        assert stats["execution_results"]["failed"] >= 1
