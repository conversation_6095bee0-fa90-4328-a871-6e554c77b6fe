"""Tests for database configuration and management."""
import pytest
from sqlalchemy.orm import Session
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
import psycopg2
import logging
from unittest.mock import patch, MagicMock, PropertyMock

from api.database import get_db, engine, verify_db_connection, DatabaseError, SessionLocal
from api.models.mitre import MitreTechnique, MitreVersion

# Configure logging
logger = logging.getLogger(__name__)

def test_database_connection(db_session):
    """Test database connection."""
    assert db_session is not None
    result = db_session.execute(text("SELECT 1")).scalar()
    assert result == 1

def test_database_connection_verification():
    """Test database connection verification."""
    assert verify_db_connection() is True

def test_database_error_handling():
    """Test error handling during database operations."""
    with patch('sqlalchemy.orm.session.Session.execute') as mock_execute:
        mock_execute.side_effect = SQLAlchemyError("Test error")
        assert verify_db_connection() is False

def test_get_db_error():
    """Test error handling in get_db."""
    with patch('api.database.SessionLocal') as mock_session:
        mock_session.side_effect = DatabaseError("Test error")

        db_generator = get_db()
        with pytest.raises(DatabaseError) as exc_info:
            next(db_generator)
        assert "Test error" in str(exc_info.value)

def test_database_session_cleanup():
    """Test proper cleanup of database sessions."""
    with patch('api.database.SessionLocal') as mock_session_factory:
        mock_session = MagicMock()
        mock_session_factory.return_value = mock_session

        # Get a session from the generator
        session_gen = get_db()
        session = next(session_gen)

        # Try to get another session (should trigger cleanup)
        with pytest.raises(StopIteration):
            next(session_gen)

        # Verify cleanup was called
        mock_session.close.assert_called_once()

def test_connection_pool_configuration():
    """Test database connection pool configuration."""
    assert engine.pool._pre_ping is True
    assert engine.pool._recycle == 300

def test_database_url_parsing():
    """Test database URL parsing."""
    from api.database import SQLALCHEMY_DATABASE_URL
    assert "postgresql://" in SQLALCHEMY_DATABASE_URL
    assert "sslmode=require" in SQLALCHEMY_DATABASE_URL

def test_transaction_rollback():
    """Test transaction rollback on error."""
    db = next(get_db())
    try:
        with db.begin():
            # Execute invalid SQL to trigger rollback
            db.execute(text("SELECT * FROM nonexistent_table"))
    except SQLAlchemyError:
        # Verify session is still usable after rollback
        result = db.execute(text("SELECT 1")).scalar()
        assert result == 1
    finally:
        db.close()

def test_version_cascade_delete(db_session):
    """Test cascading delete from version to techniques."""
    version = MitreVersion(
        version="1.0",
        is_current=True
    )
    db_session.add(version)
    db_session.flush()

    technique = MitreTechnique(
        technique_id="T1234",
        name="Test",
        version_id=version.id
    )
    db_session.add(technique)
    db_session.commit()

    # Query to verify initial state
    assert db_session.query(MitreTechnique).count() == 1

    # Delete version and verify cascade
    db_session.delete(version)
    db_session.commit()

    # Verify technique was cascade deleted
    result = db_session.query(MitreTechnique).filter_by(technique_id="T1234").first()
    assert result is None

def test_database_session_cleanup_with_active_transaction():
    """Test database session cleanup with active transaction."""
    with patch('api.database.SessionLocal') as mock_session_factory:
        mock_session = MagicMock()

        # Use PropertyMock for is_active
        is_active_mock = PropertyMock(side_effect=[True, False])
        type(mock_session).is_active = is_active_mock

        mock_session_factory.return_value = mock_session

        session_generator = get_db()
        session = next(session_generator)

        # First check: should be active
        assert session.is_active

        # Try to get another session (will trigger cleanup)
        try:
            next(session_generator)
        except StopIteration:
            pass

        # Verify cleanup happened and session is no longer active
        mock_session.close.assert_called_once()
        is_active_mock.assert_called()
        assert not session.is_active

def test_test_database_creation():
    """Test test database creation and configuration."""
    test_db_url = 'postgresql://test:test@localhost/test_db_test'
    with patch('api.database.SQLALCHEMY_DATABASE_URL', new=test_db_url):
        # Set testing mode and verify URL
        import os
        os.environ["TESTING"] = "true"
        from api.database import SQLALCHEMY_DATABASE_URL
        assert "_test" in SQLALCHEMY_DATABASE_URL

        # Clean up
        os.environ["TESTING"] = "false"

def test_database_connection_error():
    """Test database connection error handling."""
    with patch('api.database.SessionLocal', side_effect=Exception("Connection error")):
        db_generator = get_db()
        with pytest.raises(Exception) as exc_info:
            next(db_generator)
        assert "Connection error" in str(exc_info.value)

def test_database_engine_configuration():
    """Test database engine configuration."""
    # Check pool configuration
    assert engine.pool._pre_ping
    assert engine.pool._recycle == 300

def test_get_db_connection_error():
    """Test get_db error handling."""
    with patch('api.database.SessionLocal', side_effect=Exception("Session error")):
        with pytest.raises(Exception) as exc_info:
            next(get_db())
        assert "Session error" in str(exc_info.value)

def test_database_error_handling_independent():
    """Test database error handling with transaction rollback using an independent session."""
    session = SessionLocal()
    try:
        # Create initial version and commit
        version1 = MitreVersion(version="1.0", is_current=True)
        session.add(version1)
        session.commit()

        # Verify first version was committed
        assert session.query(MitreVersion).count() == 1, "Expected 1 version after first commit"

        # Try to create duplicate version (should fail)
        version2 = MitreVersion(version="1.0", is_current=True)  # Same version number
        session.add(version2)
        with pytest.raises(IntegrityError):
            session.commit()

        # Rollback and verify state
        session.rollback()
        count = session.query(MitreVersion).count()
        assert count == 1, f"Expected 1 version after rollback, got {count}"

    finally:
        # Cleanup
        session.query(MitreVersion).delete()
        session.commit()
        session.close()