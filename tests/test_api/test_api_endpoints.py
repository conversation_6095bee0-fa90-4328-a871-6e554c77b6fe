"""Tests for API endpoints."""
import pytest
from fastapi.testclient import TestClient
from api.main import app

def test_health_check(client):
    """Test the health check endpoint."""
    response = client.get("/health/")
    assert response.status_code == 200
    assert response.json() == {"status": "healthy"}

def test_protected_root_endpoint_without_auth(client):
    """Test the root endpoint without authentication."""
    response = client.get("/")
    assert response.status_code == 401
    assert "Not authenticated" in response.json()["detail"]

@pytest.mark.parametrize("debug_mode", [True, False])
def test_debug_mode_authentication(client, monkeypatch, debug_mode):
    """Test authentication bypass in debug mode."""
    monkeypatch.setenv("DEBUG_MODE", str(debug_mode).lower())
    response = client.get("/")
    if debug_mode:
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["user"] == "debug_user"
    else:
        assert response.status_code == 401
        assert "Not authenticated" in response.json()["detail"]

def test_get_techniques(client):
    """Test GET /api/v1/mitre/techniques endpoint."""
    response = client.get("/api/v1/mitre/techniques")
    assert response.status_code == 200
    assert isinstance(response.json(), list)

def test_get_technique_by_id(client, db_session):
    """Test GET /api/v1/mitre/techniques/{technique_id} endpoint."""
    response = client.get("/api/v1/mitre/techniques/T1234")
    assert response.status_code in [200, 404]

def test_get_relationships(client):
    """Test GET /api/v1/mitre/relationships endpoint."""
    response = client.get("/api/v1/mitre/relationships")
    assert response.status_code == 200
    assert isinstance(response.json(), list)