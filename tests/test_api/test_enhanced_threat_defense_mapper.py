"""Enhanced tests for the Threat Defense Mapper."""
import pytest
import json
from fastapi.testclient import <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session
from datetime import datetime

from api.models.database.mitre import <PERSON>treVersion, MitreTechnique, MitreTactic
from api.models.database.d3fend import D3<PERSON><PERSON><PERSON>oncept, D3FENDVersion
from api.models.database.threat_defense_mapping import ThreatDefenseMapping
from api.models.user import User, UserRole
from api.services.threat_defense_mapper import ThreatDefenseMapper


@pytest.fixture
def threat_defense_setup(db_session):
    """Create test data for threat defense mapper tests."""
    # Create users
    user = User(
        username="defenseuser",
        email="<EMAIL>",
        role=UserRole.ANALYST
    )
    user.set_password("password123")
    
    admin = User(
        username="defenseadmin",
        email="<EMAIL>",
        role=UserRole.ADMIN
    )
    admin.set_password("adminpass123")
    
    db_session.add(user)
    db_session.add(admin)
    db_session.commit()
    db_session.refresh(user)
    db_session.refresh(admin)
    
    # Create MITRE version
    mitre_version = MitreVersion(
        version="test-mitre-version",
        technology_domain="enterprise",
        is_current=True,
        created_on=datetime.utcnow()
    )
    db_session.add(mitre_version)
    db_session.commit()
    db_session.refresh(mitre_version)
    
    # Create techniques
    techniques = [
        MitreTechnique(
            technique_id="T1566",
            name="Phishing",
            description="Phishing is a technique used by attackers to...",
            version_id=mitre_version.id,
            created_on=datetime.utcnow()
        ),
        MitreTechnique(
            technique_id="T1078",
            name="Valid Accounts",
            description="Adversaries may steal the credentials of...",
            version_id=mitre_version.id,
            created_on=datetime.utcnow()
        ),
        MitreTechnique(
            technique_id="T1110",
            name="Brute Force",
            description="Adversaries may use brute force techniques to...",
            version_id=mitre_version.id,
            created_on=datetime.utcnow()
        )
    ]
    
    for technique in techniques:
        db_session.add(technique)
    
    db_session.commit()
    
    # Create D3FEND version
    d3fend_version = D3FENDVersion(
        version="test-d3fend-version",
        is_current=True,
        created_on=datetime.utcnow()
    )
    db_session.add(d3fend_version)
    db_session.commit()
    db_session.refresh(d3fend_version)
    
    # Create D3FEND concepts
    concepts = [
        D3FENDConcept(
            concept_id="D3-EF",
            name="Email Filter",
            description="Filter suspicious emails",
            version_id=d3fend_version.id,
            created_on=datetime.utcnow()
        ),
        D3FENDConcept(
            concept_id="D3-LA",
            name="Link Analysis",
            description="Analyze suspicious links",
            version_id=d3fend_version.id,
            created_on=datetime.utcnow()
        ),
        D3FENDConcept(
            concept_id="D3-MFA",
            name="Multi-factor Authentication",
            description="Require multiple forms of authentication",
            version_id=d3fend_version.id,
            created_on=datetime.utcnow()
        ),
        D3FENDConcept(
            concept_id="D3-BFL",
            name="Brute Force Lockout",
            description="Lock accounts after failed login attempts",
            version_id=d3fend_version.id,
            created_on=datetime.utcnow()
        )
    ]
    
    for concept in concepts:
        db_session.add(concept)
    
    db_session.commit()
    
    # Create mappings
    mappings = [
        ThreatDefenseMapping(
            technique_id=techniques[0].id,  # T1566 (Phishing)
            defense_id=concepts[0].id,      # D3-EF (Email Filter)
            effectiveness=0.8,
            created_by=admin.id,
            created_on=datetime.utcnow()
        ),
        ThreatDefenseMapping(
            technique_id=techniques[0].id,  # T1566 (Phishing)
            defense_id=concepts[1].id,      # D3-LA (Link Analysis)
            effectiveness=0.6,
            created_by=admin.id,
            created_on=datetime.utcnow()
        ),
        ThreatDefenseMapping(
            technique_id=techniques[1].id,  # T1078 (Valid Accounts)
            defense_id=concepts[2].id,      # D3-MFA (Multi-factor Authentication)
            effectiveness=0.9,
            created_by=admin.id,
            created_on=datetime.utcnow()
        ),
        ThreatDefenseMapping(
            technique_id=techniques[2].id,  # T1110 (Brute Force)
            defense_id=concepts[2].id,      # D3-MFA (Multi-factor Authentication)
            effectiveness=0.7,
            created_by=admin.id,
            created_on=datetime.utcnow()
        ),
        ThreatDefenseMapping(
            technique_id=techniques[2].id,  # T1110 (Brute Force)
            defense_id=concepts[3].id,      # D3-BFL (Brute Force Lockout)
            effectiveness=0.9,
            created_by=admin.id,
            created_on=datetime.utcnow()
        )
    ]
    
    for mapping in mappings:
        db_session.add(mapping)
    
    db_session.commit()
    
    return {
        "user": user,
        "admin": admin,
        "mitre_version": mitre_version,
        "techniques": techniques,
        "d3fend_version": d3fend_version,
        "concepts": concepts,
        "mappings": mappings
    }


class TestEnhancedThreatDefenseMapper:
    """Enhanced tests for the Threat Defense Mapper."""

    def test_get_attack_path_coverage(self, db_session, threat_defense_setup):
        """Test calculation of attack path coverage."""
        techniques = threat_defense_setup["techniques"]
        
        # Create a sequence of technique IDs
        technique_sequence = [t.technique_id for t in techniques[:2]]  # T1566, T1078
        
        # Create mapper instance
        mapper = ThreatDefenseMapper(db_session)
        
        # Get attack path coverage
        coverage = mapper.get_attack_path_coverage(technique_sequence)
        
        # Verify coverage structure
        assert "techniques" in coverage
        assert "overall_coverage" in coverage
        assert "average_coverage" in coverage
        
        # Verify technique coverage values
        assert coverage["techniques"][techniques[0].technique_id] > 0  # T1566 has 2 controls
        assert coverage["techniques"][techniques[1].technique_id] > 0  # T1078 has 1 control
        
        # Verify overall and average coverage
        assert 0 <= coverage["overall_coverage"] <= 1.0
        assert 0 <= coverage["average_coverage"] <= 1.0
        
        # Verify coverage calculation
        # T1566 has 2 controls with effectiveness 0.8 and 0.6, so coverage should be (0.8 + 0.6) / 2 = 0.7
        # T1078 has 1 control with effectiveness 0.9, so coverage should be 0.9
        # Average coverage should be (0.7 + 0.9) / 2 = 0.8
        assert abs(coverage["techniques"][techniques[0].technique_id] - 0.7) < 0.01
        assert abs(coverage["techniques"][techniques[1].technique_id] - 0.9) < 0.01
        assert abs(coverage["average_coverage"] - 0.8) < 0.01

    def test_find_d3fend_countermeasures(self, db_session, threat_defense_setup):
        """Test finding D3FEND countermeasures for techniques."""
        techniques = threat_defense_setup["techniques"]
        concepts = threat_defense_setup["concepts"]
        
        # Create a sequence of technique IDs
        technique_sequence = [techniques[0].technique_id]  # T1566 (Phishing)
        
        # Create mapper instance
        mapper = ThreatDefenseMapper(db_session)
        
        # Find countermeasures
        countermeasures = mapper.find_d3fend_countermeasures(technique_sequence)
        
        # Verify countermeasures structure
        assert techniques[0].technique_id in countermeasures
        assert len(countermeasures[techniques[0].technique_id]) == 2
        
        # Verify countermeasure details
        email_filter = next((c for c in countermeasures[techniques[0].technique_id] if c["name"] == "Email Filter"), None)
        assert email_filter is not None
        assert email_filter["effectiveness"] == 0.8
        assert email_filter["description"] == "Filter suspicious emails"
        
        link_analysis = next((c for c in countermeasures[techniques[0].technique_id] if c["name"] == "Link Analysis"), None)
        assert link_analysis is not None
        assert link_analysis["effectiveness"] == 0.6
        assert link_analysis["description"] == "Analyze suspicious links"

    def test_analyze_defense_gaps(self, db_session, threat_defense_setup):
        """Test identification of defense gaps."""
        techniques = threat_defense_setup["techniques"]
        
        # Create a sequence of technique IDs
        technique_sequence = [t.technique_id for t in techniques]  # All techniques
        
        # Create mapper instance
        mapper = ThreatDefenseMapper(db_session)
        
        # Analyze defense gaps
        gaps = mapper.analyze_defense_gaps(technique_sequence)
        
        # Verify gaps structure
        assert isinstance(gaps, list)
        
        # Verify gap details
        if len(gaps) > 0:
            for gap in gaps:
                assert "technique_id" in gap
                assert "technique_name" in gap
                assert "coverage" in gap
                assert "recommendation" in gap
                assert 0 <= gap["coverage"] <= 1.0

    def test_generate_mermaid_visualization(self, db_session, threat_defense_setup):
        """Test generation of Mermaid visualization for attack path coverage."""
        techniques = threat_defense_setup["techniques"]
        
        # Create a sequence of technique IDs
        technique_sequence = [t.technique_id for t in techniques[:2]]  # T1566, T1078
        
        # Create mapper instance
        mapper = ThreatDefenseMapper(db_session)
        
        # Generate visualization
        visualization = mapper.generate_mermaid_visualization(technique_sequence)
        
        # Verify visualization structure
        assert isinstance(visualization, str)
        assert visualization.startswith("graph TD")
        
        # Verify technique nodes
        for technique in techniques[:2]:
            assert technique.technique_id in visualization
            assert technique.name in visualization
        
        # Verify defense nodes
        assert "Email Filter" in visualization
        assert "Link Analysis" in visualization
        assert "Multi-factor Authentication" in visualization
        
        # Verify connections
        assert "T1566 --> D3-EF" in visualization.replace(" ", "")
        assert "T1566 --> D3-LA" in visualization.replace(" ", "")
        assert "T1078 --> D3-MFA" in visualization.replace(" ", "")

    def test_get_attack_path_coverage_api(self, test_client, threat_defense_setup):
        """Test the API endpoint for attack path coverage."""
        user = threat_defense_setup["user"]
        techniques = threat_defense_setup["techniques"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Create request data
        request_data = {
            "technique_sequence": [techniques[0].technique_id, techniques[1].technique_id]  # T1566, T1078
        }
        
        # Test getting attack path coverage
        response = test_client.post(
            "/api/v1/threat-defense/coverage",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=request_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "techniques" in data
        assert "overall_coverage" in data
        assert "average_coverage" in data
        
        # Verify technique coverage
        assert techniques[0].technique_id in data["techniques"]
        assert techniques[1].technique_id in data["techniques"]
        
        # Verify coverage values
        assert 0 <= data["overall_coverage"] <= 1.0
        assert 0 <= data["average_coverage"] <= 1.0

    def test_get_attack_path_visualization_api(self, test_client, threat_defense_setup):
        """Test the API endpoint for attack path visualization."""
        user = threat_defense_setup["user"]
        techniques = threat_defense_setup["techniques"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Create request data
        request_data = {
            "technique_sequence": [techniques[0].technique_id, techniques[1].technique_id]  # T1566, T1078
        }
        
        # Test getting attack path visualization
        response = test_client.post(
            "/api/v1/threat-defense/coverage/visualization",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=request_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "mermaid_chart" in data
        assert "overall_coverage" in data
        assert "average_coverage" in data
        
        # Verify mermaid chart
        assert isinstance(data["mermaid_chart"], str)
        assert data["mermaid_chart"].startswith("graph TD")
        
        # Verify coverage values
        assert 0 <= data["overall_coverage"] <= 1.0
        assert 0 <= data["average_coverage"] <= 1.0

    def test_find_countermeasures_api(self, test_client, threat_defense_setup):
        """Test the API endpoint for finding countermeasures."""
        user = threat_defense_setup["user"]
        techniques = threat_defense_setup["techniques"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Create request data
        request_data = {
            "technique_sequence": [techniques[0].technique_id]  # T1566 (Phishing)
        }
        
        # Test finding countermeasures
        response = test_client.post(
            "/api/v1/threat-defense/countermeasures",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=request_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert techniques[0].technique_id in data
        assert len(data[techniques[0].technique_id]) == 2
        
        # Verify countermeasure details
        countermeasures = data[techniques[0].technique_id]
        assert any(c["name"] == "Email Filter" for c in countermeasures)
        assert any(c["name"] == "Link Analysis" for c in countermeasures)

    def test_analyze_gaps_api(self, test_client, threat_defense_setup):
        """Test the API endpoint for analyzing defense gaps."""
        user = threat_defense_setup["user"]
        techniques = threat_defense_setup["techniques"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Create request data
        request_data = {
            "technique_sequence": [t.technique_id for t in techniques]  # All techniques
        }
        
        # Test analyzing gaps
        response = test_client.post(
            "/api/v1/threat-defense/gaps",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=request_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        
        # Verify gap details if any
        if len(data) > 0:
            for gap in data:
                assert "technique_id" in gap
                assert "technique_name" in gap
                assert "coverage" in gap
                assert "recommendation" in gap

    def test_create_mapping_api(self, test_client, db_session, threat_defense_setup):
        """Test the API endpoint for creating a threat-defense mapping."""
        admin = threat_defense_setup["admin"]
        techniques = threat_defense_setup["techniques"]
        concepts = threat_defense_setup["concepts"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": admin.username})
        
        # Create mapping data
        mapping_data = {
            "technique_id": techniques[1].technique_id,  # T1078 (Valid Accounts)
            "defense_id": concepts[1].concept_id,        # D3-LA (Link Analysis)
            "effectiveness": 0.5,
            "notes": "Test mapping"
        }
        
        # Test creating a mapping
        response = test_client.post(
            "/api/v1/threat-defense/mappings",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=mapping_data
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["technique_id"] == techniques[1].id
        assert data["defense_id"] == concepts[1].id
        assert data["effectiveness"] == mapping_data["effectiveness"]
        assert data["notes"] == mapping_data["notes"]
        assert data["created_by"] == admin.id
        
        # Verify mapping was created in the database
        db_mapping = db_session.query(ThreatDefenseMapping).filter(
            ThreatDefenseMapping.technique_id == techniques[1].id,
            ThreatDefenseMapping.defense_id == concepts[1].id
        ).first()
        
        assert db_mapping is not None
        assert db_mapping.effectiveness == mapping_data["effectiveness"]
        assert db_mapping.notes == mapping_data["notes"]
        assert db_mapping.created_by == admin.id

    def test_update_mapping_api(self, test_client, db_session, threat_defense_setup):
        """Test the API endpoint for updating a threat-defense mapping."""
        admin = threat_defense_setup["admin"]
        mappings = threat_defense_setup["mappings"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": admin.username})
        
        # Create update data
        update_data = {
            "effectiveness": 0.95,
            "notes": "Updated test mapping"
        }
        
        # Test updating a mapping
        response = test_client.put(
            f"/api/v1/threat-defense/mappings/{mappings[0].id}",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=update_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == mappings[0].id
        assert data["effectiveness"] == update_data["effectiveness"]
        assert data["notes"] == update_data["notes"]
        
        # Verify mapping was updated in the database
        db_session.refresh(mappings[0])
        assert mappings[0].effectiveness == update_data["effectiveness"]
        assert mappings[0].notes == update_data["notes"]

    def test_delete_mapping_api(self, test_client, db_session, threat_defense_setup):
        """Test the API endpoint for deleting a threat-defense mapping."""
        admin = threat_defense_setup["admin"]
        mappings = threat_defense_setup["mappings"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": admin.username})
        
        # Test deleting a mapping
        response = test_client.delete(
            f"/api/v1/threat-defense/mappings/{mappings[0].id}",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 204  # No content
        
        # Verify mapping was deleted from the database
        db_mapping = db_session.query(ThreatDefenseMapping).filter(
            ThreatDefenseMapping.id == mappings[0].id
        ).first()
        
        assert db_mapping is None

    def test_get_mappings_api(self, test_client, threat_defense_setup):
        """Test the API endpoint for getting threat-defense mappings."""
        user = threat_defense_setup["user"]
        techniques = threat_defense_setup["techniques"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Test getting all mappings
        response = test_client.get(
            "/api/v1/threat-defense/mappings",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert data["total"] >= 5  # At least 5 mappings from setup
        
        # Test filtering by technique
        response = test_client.get(
            f"/api/v1/threat-defense/mappings?technique_id={techniques[0].technique_id}",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert data["total"] >= 2  # At least 2 mappings for T1566
