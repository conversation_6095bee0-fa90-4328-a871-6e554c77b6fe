"""
Tests for the bulk execution API endpoints.

This module contains tests for the bulk execution API endpoints, including
executing multiple test cases at once and executing all test cases in a campaign.
"""
import pytest
from fastapi.testclient import TestClient

from api.main import app
from api.models.database.test_case import TestCase
from api.models.database.campaign import Campaign
from api.models.database.test_execution import TestExecution


client = TestClient(app)


@pytest.fixture
def admin_token():
    """Get an admin token for testing."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": "admin", "password": "adminpassword"}
    )
    return response.json()["access_token"]


@pytest.fixture
def test_cases(db_session):
    """Create test cases for testing."""
    test_cases = []
    for i in range(3):
        test_case = TestCase(
            name=f"Test Case {i+1}",
            description=f"Description for test case {i+1}",
            type="manual",
            status="active",
            priority="high",
            complexity="moderate",
            prerequisites="Access to the application",
            steps=[f"Step 1 for test case {i+1}", f"Step 2 for test case {i+1}"],
            expected_result=f"Expected result for test case {i+1}",
            tags=["security", "test"],
            created_by="1",
            version="1.0.0"
        )
        db_session.add(test_case)
        test_cases.append(test_case)
    
    db_session.commit()
    for test_case in test_cases:
        db_session.refresh(test_case)
    
    yield test_cases
    
    # Clean up
    for test_case in test_cases:
        db_session.query(TestExecution).filter(TestExecution.test_case_id == test_case.id).delete()
    
    for test_case in test_cases:
        db_session.delete(test_case)
    
    db_session.commit()


@pytest.fixture
def test_campaign(db_session, test_cases):
    """Create a test campaign with test cases for testing."""
    campaign = Campaign(
        name="Test Campaign",
        description="Campaign for testing bulk execution",
        status="active",
        created_by="1"
    )
    db_session.add(campaign)
    db_session.commit()
    db_session.refresh(campaign)
    
    # Add test cases to campaign
    for test_case in test_cases:
        campaign.test_cases.append(test_case)
    
    db_session.add(campaign)
    db_session.commit()
    db_session.refresh(campaign)
    
    yield campaign
    
    # Clean up
    db_session.delete(campaign)
    db_session.commit()


def test_bulk_execute_test_cases(client, db_session, test_cases, admin_token):
    """Test executing multiple test cases at once."""
    test_case_ids = [test_case.id for test_case in test_cases]
    
    response = client.post(
        "/api/v1/bulk-execution/test-cases",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "test_case_ids": test_case_ids,
            "execution_data": {
                "status": "passed",
                "executed_by": 1,
                "actual_result": "All tests passed",
                "notes": "Bulk execution test",
                "environment": "Test",
                "duration_ms": 5000
            }
        }
    )
    
    assert response.status_code == 201
    data = response.json()
    
    # Verify response data
    assert "message" in data
    assert "successful_count" in data
    assert "failed_count" in data
    assert "results" in data
    assert data["successful_count"] == 3
    assert data["failed_count"] == 0
    assert data["results"]["total"] == 3
    assert data["results"]["by_status"]["passed"] == 3
    
    # Verify database state
    for test_case in test_cases:
        executions = db_session.query(TestExecution).filter(
            TestExecution.test_case_id == test_case.id
        ).all()
        assert len(executions) == 1
        assert executions[0].status == "passed"
        assert executions[0].actual_result == "All tests passed"


def test_bulk_execute_campaign(client, db_session, test_campaign, admin_token):
    """Test executing all test cases in a campaign."""
    response = client.post(
        f"/api/v1/bulk-execution/campaigns/{test_campaign.id}",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "status": "passed",
            "executed_by": 1,
            "actual_result": "Campaign execution successful",
            "notes": "Bulk campaign execution test",
            "environment": "Test",
            "duration_ms": 10000
        }
    )
    
    assert response.status_code == 201
    data = response.json()
    
    # Verify response data
    assert "message" in data
    assert "campaign_id" in data
    assert "successful_count" in data
    assert "failed_count" in data
    assert "results" in data
    assert data["campaign_id"] == test_campaign.id
    assert data["successful_count"] == 3
    assert data["failed_count"] == 0
    assert data["results"]["total"] == 3
    assert data["results"]["by_status"]["passed"] == 3
    
    # Verify database state
    for test_case in test_campaign.test_cases:
        executions = db_session.query(TestExecution).filter(
            TestExecution.test_case_id == test_case.id
        ).all()
        assert len(executions) == 1
        assert executions[0].status == "passed"
        assert executions[0].actual_result == "Campaign execution successful"


def test_bulk_execute_nonexistent_test_cases(client, db_session, admin_token):
    """Test executing nonexistent test cases."""
    response = client.post(
        "/api/v1/bulk-execution/test-cases",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "test_case_ids": [9999, 10000],
            "execution_data": {
                "status": "passed",
                "executed_by": 1,
                "actual_result": "All tests passed",
                "notes": "Bulk execution test",
                "environment": "Test",
                "duration_ms": 5000
            }
        }
    )
    
    assert response.status_code == 201
    data = response.json()
    
    # Verify response data
    assert "message" in data
    assert "successful_count" in data
    assert "failed_count" in data
    assert "failed_executions" in data
    assert data["successful_count"] == 0
    assert data["failed_count"] == 2
    assert len(data["failed_executions"]) == 2
    assert "Test case with ID 9999 not found" in data["failed_executions"][0]["error"]


def test_bulk_execute_nonexistent_campaign(client, db_session, admin_token):
    """Test executing a nonexistent campaign."""
    response = client.post(
        "/api/v1/bulk-execution/campaigns/9999",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "status": "passed",
            "executed_by": 1,
            "actual_result": "Campaign execution successful",
            "notes": "Bulk campaign execution test",
            "environment": "Test",
            "duration_ms": 10000
        }
    )
    
    assert response.status_code == 404
    data = response.json()
    assert "detail" in data
    assert "Campaign with ID 9999 not found" in data["detail"]
