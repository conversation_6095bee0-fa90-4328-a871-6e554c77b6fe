"""
Tests for the advanced test case API endpoints.

This module contains tests for the advanced test case API endpoints, including
test execution, cloning, export/import, and comparison.
"""
import json
import pytest
from datetime import datetime, timedelta
from fastapi.testclient import TestClient

from api.main import app
from api.models.database.test_case import TestCase
from api.models.database.test_execution import TestExecution
from api.models.schemas.test_case import TestExecutionStatus


client = TestClient(app)


@pytest.fixture
def admin_token():
    """Get an admin token for testing."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": "admin", "password": "adminpassword"}
    )
    return response.json()["access_token"]


@pytest.fixture
def test_case(db_session):
    """Create a test case for testing."""
    test_case = TestCase(
        name="Test SQL Injection",
        description="Test for SQL injection vulnerabilities",
        type="manual",
        status="active",
        priority="high",
        complexity="moderate",
        prerequisites="Access to the application",
        steps=["Step 1: Navigate to login page", "Step 2: Enter SQL injection payload"],
        expected_result="Application should sanitize input and prevent SQL injection",
        tags=["security", "injection", "sql"],
        mitre_techniques=["T1190", "T1212"],
        created_by="1",
        version="1.0.0"
    )
    db_session.add(test_case)
    db_session.commit()
    db_session.refresh(test_case)
    
    yield test_case
    
    # Clean up
    db_session.query(TestExecution).filter(TestExecution.test_case_id == test_case.id).delete()
    db_session.query(TestCase).filter(TestCase.id == test_case.id).delete()
    db_session.commit()


def test_create_test_execution(client, db_session, test_case, admin_token):
    """Test creating a test execution record."""
    response = client.post(
        f"/api/v1/test-cases/{test_case.id}/executions",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "status": "passed",
            "executed_by": 1,
            "actual_result": "No vulnerabilities found",
            "notes": "Test executed successfully",
            "environment": "Production",
            "duration_ms": 5000
        }
    )
    
    assert response.status_code == 201
    data = response.json()
    
    # Verify response data
    assert data["status"] == "passed"
    assert data["test_case_id"] == test_case.id
    assert data["actual_result"] == "No vulnerabilities found"
    assert data["environment"] == "Production"
    assert data["duration_ms"] == 5000
    
    # Verify database state
    db_execution = db_session.query(TestExecution).filter(TestExecution.id == data["id"]).first()
    assert db_execution is not None
    assert db_execution.status == "passed"
    assert db_execution.test_case_id == test_case.id


def test_get_test_executions(client, db_session, test_case, admin_token):
    """Test getting test execution records."""
    # Create test executions
    executions = [
        TestExecution(
            test_case_id=test_case.id,
            status=status,
            executed_by="1",
            execution_date=datetime.utcnow() - timedelta(days=i),
            actual_result=f"Result for {status}",
            environment="Test"
        )
        for i, status in enumerate(["passed", "failed", "blocked"])
    ]
    
    for execution in executions:
        db_session.add(execution)
    db_session.commit()
    
    # Test getting all executions
    response = client.get(
        f"/api/v1/test-cases/{test_case.id}/executions",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 3
    
    # Test filtering by status
    response = client.get(
        f"/api/v1/test-cases/{test_case.id}/executions?status=passed",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 1
    assert data[0]["status"] == "passed"


def test_get_test_execution(client, db_session, test_case, admin_token):
    """Test getting a specific test execution record."""
    # Create test execution
    execution = TestExecution(
        test_case_id=test_case.id,
        status="passed",
        executed_by="1",
        execution_date=datetime.utcnow(),
        actual_result="No vulnerabilities found",
        environment="Test"
    )
    db_session.add(execution)
    db_session.commit()
    db_session.refresh(execution)
    
    response = client.get(
        f"/api/v1/test-cases/{test_case.id}/executions/{execution.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == execution.id
    assert data["status"] == "passed"
    assert data["test_case"]["id"] == test_case.id


def test_update_test_execution(client, db_session, test_case, admin_token):
    """Test updating a test execution record."""
    # Create test execution
    execution = TestExecution(
        test_case_id=test_case.id,
        status="in_progress",
        executed_by="1",
        execution_date=datetime.utcnow(),
        actual_result="Test in progress",
        environment="Test"
    )
    db_session.add(execution)
    db_session.commit()
    db_session.refresh(execution)
    
    response = client.put(
        f"/api/v1/test-cases/{test_case.id}/executions/{execution.id}",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "status": "passed",
            "executed_by": 1,
            "actual_result": "No vulnerabilities found",
            "notes": "Test completed successfully",
            "environment": "Test",
            "duration_ms": 3000
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "passed"
    assert data["actual_result"] == "No vulnerabilities found"
    assert data["notes"] == "Test completed successfully"
    
    # Verify database state
    db_session.refresh(execution)
    assert execution.status == "passed"
    assert execution.actual_result == "No vulnerabilities found"


def test_delete_test_execution(client, db_session, test_case, admin_token):
    """Test deleting a test execution record."""
    # Create test execution
    execution = TestExecution(
        test_case_id=test_case.id,
        status="failed",
        executed_by="1",
        execution_date=datetime.utcnow(),
        actual_result="Vulnerability found",
        environment="Test"
    )
    db_session.add(execution)
    db_session.commit()
    db_session.refresh(execution)
    
    response = client.delete(
        f"/api/v1/test-cases/{test_case.id}/executions/{execution.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 204
    
    # Verify database state
    db_execution = db_session.query(TestExecution).filter(TestExecution.id == execution.id).first()
    assert db_execution is None


def test_get_latest_test_execution(client, db_session, test_case, admin_token):
    """Test getting the latest test execution record."""
    # Create test executions with different dates
    executions = [
        TestExecution(
            test_case_id=test_case.id,
            status=status,
            executed_by="1",
            execution_date=datetime.utcnow() - timedelta(days=i),
            actual_result=f"Result for {status}",
            environment="Test"
        )
        for i, status in enumerate(["passed", "failed", "blocked"])
    ]
    
    for execution in executions:
        db_session.add(execution)
    db_session.commit()
    
    response = client.get(
        f"/api/v1/test-cases/{test_case.id}/executions/latest",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "passed"  # The most recent one


def test_get_test_execution_stats(client, db_session, test_case, admin_token):
    """Test getting test execution statistics."""
    # Create test executions with different statuses
    statuses = ["passed", "passed", "failed", "blocked", "skipped"]
    executions = [
        TestExecution(
            test_case_id=test_case.id,
            status=status,
            executed_by="1",
            execution_date=datetime.utcnow() - timedelta(days=i),
            actual_result=f"Result for {status}",
            environment="Test",
            duration_ms=1000 * (i + 1)
        )
        for i, status in enumerate(statuses)
    ]
    
    for execution in executions:
        db_session.add(execution)
    db_session.commit()
    
    response = client.get(
        f"/api/v1/test-cases/{test_case.id}/executions/stats",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 5
    assert data["by_status"]["passed"] == 2
    assert data["by_status"]["failed"] == 1
    assert data["pass_rate"] == 2/3 * 100  # 2 passed out of 3 (passed + failed)


def test_clone_test_case(client, db_session, test_case, admin_token):
    """Test cloning a test case."""
    response = client.post(
        f"/api/v1/test-cases/{test_case.id}/clone",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "new_name": "Cloned SQL Injection Test",
            "include_history": False
        }
    )
    
    assert response.status_code == 201
    data = response.json()
    assert "message" in data
    assert "test_case" in data
    assert data["test_case"]["name"] == "Cloned SQL Injection Test"
    
    # Verify database state
    cloned_test_case = db_session.query(TestCase).filter(TestCase.name == "Cloned SQL Injection Test").first()
    assert cloned_test_case is not None
    assert cloned_test_case.description == test_case.description
    assert cloned_test_case.steps == test_case.steps
    assert cloned_test_case.status == "draft"  # Should always start as draft


def test_export_test_cases_json(client, db_session, test_case, admin_token):
    """Test exporting test cases to JSON."""
    response = client.post(
        "/api/v1/test-cases/export",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "format": "json",
            "test_case_ids": [test_case.id],
            "include_executions": False,
            "include_history": False
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "format" in data
    assert "data" in data
    assert data["format"] == "json"
    
    # Parse the JSON data
    exported_data = json.loads(data["data"])
    assert len(exported_data) == 1
    assert exported_data[0]["id"] == test_case.id
    assert exported_data[0]["name"] == test_case.name


def test_import_test_cases(client, db_session, admin_token):
    """Test importing test cases."""
    # Prepare import data
    import_data = [{
        "name": "Imported Test Case",
        "description": "This is an imported test case",
        "type": "manual",
        "status": "draft",
        "priority": "medium",
        "complexity": "moderate",
        "expected_result": "Expected result",
        "steps": ["Step 1", "Step 2"],
        "tags": ["imported", "test"]
    }]
    
    response = client.post(
        "/api/v1/test-cases/import",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "format": "json",
            "data": import_data,
            "conflict_strategy": "create_new"
        }
    )
    
    assert response.status_code == 201
    data = response.json()
    assert "message" in data
    assert "total_created" in data
    assert data["total_created"] == 1
    
    # Verify database state
    imported_test_case = db_session.query(TestCase).filter(TestCase.name == "Imported Test Case").first()
    assert imported_test_case is not None
    assert imported_test_case.description == "This is an imported test case"
    assert imported_test_case.tags == ["imported", "test"]


def test_compare_test_case_versions(client, db_session, test_case, admin_token):
    """Test comparing test case versions."""
    # Update the test case to create a new version
    test_case.status = "deprecated"
    test_case.steps = ["Step 1: Navigate to login page", "Step 2: Enter SQL injection payload", "Step 3: Submit form"]
    test_case.version = "1.0.1"
    db_session.add(test_case)
    db_session.commit()
    
    # Create history entries for both versions
    from api.models.database.test_case_history import TestCaseHistory
    
    # Version 1.0.0
    history_1 = TestCaseHistory(
        test_case_id=test_case.id,
        version="1.0.0",
        change_type="create",
        changed_by="1",
        changed_at=datetime.utcnow() - timedelta(days=1),
        data={
            "id": test_case.id,
            "name": test_case.name,
            "status": "active",
            "steps": ["Step 1: Navigate to login page", "Step 2: Enter SQL injection payload"]
        },
        change_summary="Initial creation"
    )
    
    # Version 1.0.1
    history_2 = TestCaseHistory(
        test_case_id=test_case.id,
        version="1.0.1",
        change_type="update",
        changed_by="1",
        changed_at=datetime.utcnow(),
        data={
            "id": test_case.id,
            "name": test_case.name,
            "status": "deprecated",
            "steps": ["Step 1: Navigate to login page", "Step 2: Enter SQL injection payload", "Step 3: Submit form"]
        },
        change_summary="Updated status and added step"
    )
    
    db_session.add(history_1)
    db_session.add(history_2)
    db_session.commit()
    
    response = client.post(
        f"/api/v1/test-cases/{test_case.id}/compare",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "test_case_id": test_case.id,
            "version_a": "1.0.0",
            "version_b": "1.0.1"
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["test_case_id"] == test_case.id
    assert data["version_a"] == "1.0.0"
    assert data["version_b"] == "1.0.1"
    assert "differences" in data
    assert "status" in data["differences"]
    assert "steps" in data["differences"]
    assert data["differences"]["status"]["old"] == "active"
    assert data["differences"]["status"]["new"] == "deprecated"
