
from datetime import datetime
import pytest
from sqlalchemy.orm import Session
from api.models.mitre import MitreTechnique
from api.models.relationships import MitreRelationship

def test_create_technique(db_session: Session):
    """Test creating a MITRE technique."""
    technique = MitreTechnique(
        technique_id="T1234",
        name="Test Technique",
        description="Test description",
        version_id=1,
        data={"key": "value"}
    )
    db_session.add(technique)
    db_session.commit()
    
    assert technique.id is not None
    assert technique.technique_id == "T1234"
    assert technique.name == "Test Technique"
    assert technique.created_at is not None

def test_create_relationship(db_session: Session):
    """Test creating a MITRE relationship."""
    relationship = MitreRelationship(
        relationship_id="R1234",
        source_id=1,
        target_id=2,
        relationship_type="uses"
    )
    db_session.add(relationship)
    db_session.commit()
    
    assert relationship.id is not None
    assert relationship.relationship_id == "R1234"
    assert relationship.relationship_type == "uses"
