"""
Tests for the test case API endpoints.

This module contains tests for creating, retrieving, updating, and deleting
security test cases through the API.
"""
import pytest
from api.models import CampaignDB, TestCaseDB

def test_create_test_case(client, db_session):
    """Test creating a new test case"""
    # Create a campaign first
    campaign = CampaignDB(name="Test Campaign", description="Test Description")
    db_session.add(campaign)
    db_session.commit()
    db_session.refresh(campaign)  # Refresh to ensure we have the latest state
    campaign_id = campaign.id  # Store ID in local variable

    response = client.post(
        "/api/v1/test-cases",
        json={
            "name": "Test Case 1",
            "description": "Test Description",
            "campaign_id": campaign_id,
            "expected_result": "Expected output",
            "status": "pending"
        }
    )
    assert response.status_code == 200
    data = response.json()

    # Verify response data
    assert data["name"] == "Test Case 1"
    assert data["campaign_id"] == campaign_id  # Use stored ID
    assert data["expected_result"] == "Expected output"
    assert data["status"] == "pending"
    assert "id" in data
    assert "created_time" in data
    assert "updated_time" in data

    # Verify database state
    db_test_case = db_session.query(TestCaseDB).filter(TestCaseDB.id == data["id"]).first()
    assert db_test_case is not None
    assert db_test_case.name == "Test Case 1"
    assert db_test_case.campaign_id == campaign_id  # Use stored ID

def test_get_campaign_test_cases(client, db_session):
    """Test getting test cases for a campaign"""
    # Create a campaign
    campaign = CampaignDB(name="Test Campaign", description="Test Description")
    db_session.add(campaign)
    db_session.commit()
    db_session.refresh(campaign)  # Refresh to ensure we have the latest state

    # Create test cases
    test_cases = [
        TestCaseDB(
            name=f"Test Case {i}",
            description=f"Description {i}",
            campaign_id=campaign.id,
            expected_result=f"Expected {i}"
        )
        for i in range(3)
    ]
    for test_case in test_cases:
        db_session.add(test_case)
    db_session.commit()

    response = client.get(f"/api/v1/campaigns/{campaign.id}/test-cases")
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 3
    assert all(tc["campaign_id"] == campaign.id for tc in data)

def test_create_test_case_validation(client):
    """Test test case creation validation"""
    # Test missing name
    response = client.post(
        "/api/v1/test-cases",
        json={
            "description": "Test Description",
            "campaign_id": 1,
            "expected_result": "Expected output",
            "status": "pending"
        }
    )
    assert response.status_code == 422  # Validation error

    # Test invalid status
    response = client.post(
        "/api/v1/test-cases",
        json={
            "name": "Test Case 1",
            "description": "Test Description",
            "campaign_id": 1,
            "expected_result": "Expected output",
            "status": "invalid_status"
        }
    )
    assert response.status_code == 422  # Validation error

def test_create_test_case_nonexistent_campaign(client):
    """Test creating a test case for a nonexistent campaign"""
    response = client.post(
        "/api/v1/test-cases",
        json={
            "name": "Test Case 1",
            "description": "Test Description",
            "campaign_id": 999,  # Nonexistent campaign ID
            "expected_result": "Expected output",
            "status": "pending"
        }
    )
    assert response.status_code == 404
    assert response.json()["detail"] == "Campaign not found"

def test_get_test_case_by_id(client, db_session):
    """Test getting a specific test case by ID"""
    # Create a campaign
    campaign = CampaignDB(name="Test Campaign", description="Test Description")
    db_session.add(campaign)
    db_session.commit()
    
    # Create a test case
    test_case = TestCaseDB(
        name="Test Case",
        description="Test Description",
        campaign_id=campaign.id,
        expected_result="Expected output"
    )
    db_session.add(test_case)
    db_session.commit()
    db_session.refresh(test_case)
    
    response = client.get(f"/api/v1/test-cases/{test_case.id}")
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_case.id
    assert data["name"] == "Test Case"
    assert data["campaign_id"] == campaign.id
    assert data["expected_result"] == "Expected output"

def test_update_test_case(client, db_session):
    """Test updating a test case"""
    # Create a campaign
    campaign = CampaignDB(name="Test Campaign", description="Test Description")
    db_session.add(campaign)
    db_session.commit()
    
    # Create a test case
    test_case = TestCaseDB(
        name="Original Test Case",
        description="Original Description",
        campaign_id=campaign.id,
        expected_result="Original Expected",
        status="pending"
    )
    db_session.add(test_case)
    db_session.commit()
    db_session.refresh(test_case)
    
    response = client.put(
        f"/api/v1/test-cases/{test_case.id}",
        json={
            "name": "Updated Test Case",
            "description": "Updated Description",
            "expected_result": "Updated Expected",
            "actual_result": "Actual Result",
            "status": "passed"
        }
    )
    assert response.status_code == 200
    data = response.json()
    
    # Verify response data
    assert data["name"] == "Updated Test Case"
    assert data["description"] == "Updated Description"
    assert data["expected_result"] == "Updated Expected"
    assert data["actual_result"] == "Actual Result"
    assert data["status"] == "passed"
    
    # Verify database state
    db_session.refresh(test_case)
    assert test_case.name == "Updated Test Case"
    assert test_case.expected_result == "Updated Expected"
    assert test_case.actual_result == "Actual Result"
    assert test_case.status == "passed"

def test_delete_test_case(client, db_session):
    """Test deleting a test case (soft delete)"""
    # Create a campaign
    campaign = CampaignDB(name="Test Campaign", description="Test Description")
    db_session.add(campaign)
    db_session.commit()
    
    # Create a test case
    test_case = TestCaseDB(
        name="Test Case",
        description="Test Description",
        campaign_id=campaign.id,
        expected_result="Expected output"
    )
    db_session.add(test_case)
    db_session.commit()
    db_session.refresh(test_case)
    
    response = client.delete(f"/api/v1/test-cases/{test_case.id}")
    assert response.status_code == 200
    
    # Verify soft delete
    db_session.refresh(test_case)
    assert test_case.deleted_time is not None
    
    # Verify it doesn't appear in listings
    response = client.get(f"/api/v1/campaigns/{campaign.id}/test-cases")
    assert response.status_code == 200
    data = response.json()
    assert all(item["id"] != test_case.id for item in data)

def test_bulk_create_test_cases(client, db_session):
    """Test bulk creation of test cases"""
    # Create a campaign
    campaign = CampaignDB(name="Test Campaign", description="Test Description")
    db_session.add(campaign)
    db_session.commit()
    db_session.refresh(campaign)
    
    response = client.post(
        "/api/v1/test-cases/bulk",
        json={
            "campaign_id": campaign.id,
            "test_cases": [
                {
                    "name": f"Bulk Test Case {i}",
                    "description": f"Description {i}",
                    "expected_result": f"Expected {i}",
                    "status": "pending"
                }
                for i in range(3)
            ]
        }
    )
    assert response.status_code == 200
    data = response.json()
    
    # Verify response data
    assert "created_count" in data
    assert data["created_count"] == 3
    assert "test_cases" in data
    assert len(data["test_cases"]) == 3
    
    # Verify database state
    test_cases = db_session.query(TestCaseDB).filter(TestCaseDB.campaign_id == campaign.id).all()
    assert len(test_cases) == 3

def test_update_test_case_status(client, db_session):
    """Test updating just the status of a test case"""
    # Create a campaign
    campaign = CampaignDB(name="Test Campaign", description="Test Description")
    db_session.add(campaign)
    db_session.commit()
    
    # Create a test case
    test_case = TestCaseDB(
        name="Test Case",
        description="Test Description",
        campaign_id=campaign.id,
        expected_result="Expected output",
        status="pending"
    )
    db_session.add(test_case)
    db_session.commit()
    db_session.refresh(test_case)
    
    response = client.patch(
        f"/api/v1/test-cases/{test_case.id}/status",
        json={"status": "in-progress"}
    )
    assert response.status_code == 200
    data = response.json()
    
    # Verify response data
    assert data["status"] == "in-progress"
    
    # Verify database state
    db_session.refresh(test_case)
    assert test_case.status == "in-progress"
    
    # Test invalid status
    response = client.patch(
        f"/api/v1/test-cases/{test_case.id}/status",
        json={"status": "invalid_status"}
    )
    assert response.status_code == 422  # Validation error

def test_test_case_filtering(client, db_session):
    """Test filtering test cases by status"""
    # Create a campaign
    campaign = CampaignDB(name="Test Campaign", description="Test Description")
    db_session.add(campaign)
    db_session.commit()
    
    # Create test cases with different statuses
    statuses = ["pending", "in-progress", "passed", "failed", "blocked"]
    for i, status in enumerate(statuses):
        test_case = TestCaseDB(
            name=f"Test Case {i}",
            description=f"Description {i}",
            campaign_id=campaign.id,
            expected_result=f"Expected {i}",
            status=status
        )
        db_session.add(test_case)
    db_session.commit()
    
    # Test filtering by status
    for status in statuses:
        response = client.get(f"/api/v1/campaigns/{campaign.id}/test-cases?status={status}")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["status"] == status
    
    # Test filtering by multiple statuses
    response = client.get(f"/api/v1/campaigns/{campaign.id}/test-cases?status=passed&status=failed")
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 2
    assert all(item["status"] in ["passed", "failed"] for item in data)