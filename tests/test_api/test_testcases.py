"""Unit tests for the test case API endpoints."""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.main import app
from api.models.base import TestCaseDB, CampaignDB
from api.database import get_db

client = TestClient(app)

@pytest.fixture
def db_session(monkeypatch):
    """Create a test database session."""
    # This would typically use a test database
    # For simplicity, we'll use the same database but roll back changes
    from api.database import SessionLocal
    db = SessionLocal()
    try:
        yield db
    finally:
        db.rollback()
        db.close()

@pytest.fixture
def auth_headers():
    """Create authentication headers for API requests."""
    # In a real test, this would authenticate with the API
    # For now, we'll mock the authentication
    return {"Authorization": "Bearer test_token"}

@pytest.fixture
def test_campaign(db_session):
    """Create a test campaign in the database."""
    campaign = CampaignDB(
        name="Test Campaign",
        description="Test campaign for unit tests",
        status="active"
    )
    db_session.add(campaign)
    db_session.commit()
    db_session.refresh(campaign)
    
    yield campaign
    
    # Clean up
    db_session.query(CampaignDB).filter(CampaignDB.id == campaign.id).delete()
    db_session.commit()

@pytest.fixture
def test_testcase(db_session, test_campaign):
    """Create a test case in the database."""
    testcase = TestCaseDB(
        name="Test Case",
        description="Test case for unit tests",
        campaign_id=test_campaign.id,
        expected_result="Expected test result",
        actual_result=None,
        status="pending"
    )
    db_session.add(testcase)
    db_session.commit()
    db_session.refresh(testcase)
    
    yield testcase
    
    # Clean up
    db_session.query(TestCaseDB).filter(TestCaseDB.id == testcase.id).delete()
    db_session.commit()

def test_create_testcase(db_session, test_campaign, auth_headers, monkeypatch):
    """Test creating a new test case."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    response = client.post(
        "/api/v1/testcases/",
        json={
            "name": "New Test Case",
            "description": "Created during unit test",
            "campaign_id": test_campaign.id,
            "expected_result": "Expected test result",
            "actual_result": None,
            "status": "pending"
        },
        headers=auth_headers
    )
    
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == "New Test Case"
    assert data["description"] == "Created during unit test"
    assert data["campaign_id"] == test_campaign.id
    assert data["expected_result"] == "Expected test result"
    assert data["actual_result"] is None
    assert data["status"] == "pending"
    assert "id" in data
    
    # Clean up
    db_session.query(TestCaseDB).filter(TestCaseDB.id == data["id"]).delete()
    db_session.commit()

def test_get_testcases(db_session, test_testcase, auth_headers, monkeypatch):
    """Test retrieving all test cases."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    response = client.get(
        "/api/v1/testcases/",
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) >= 1
    
    # Check if our test case is in the list
    testcase_ids = [tc["id"] for tc in data]
    assert test_testcase.id in testcase_ids

def test_get_testcase(db_session, test_testcase, auth_headers, monkeypatch):
    """Test retrieving a specific test case."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    response = client.get(
        f"/api/v1/testcases/{test_testcase.id}",
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_testcase.id
    assert data["name"] == test_testcase.name
    assert data["description"] == test_testcase.description
    assert data["campaign_id"] == test_testcase.campaign_id
    assert data["expected_result"] == test_testcase.expected_result
    assert data["status"] == test_testcase.status

def test_update_testcase(db_session, test_testcase, auth_headers, monkeypatch):
    """Test updating a test case."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    response = client.put(
        f"/api/v1/testcases/{test_testcase.id}",
        json={
            "name": "Updated Test Case",
            "description": "Updated during unit test",
            "campaign_id": test_testcase.campaign_id,
            "expected_result": "Updated expected result",
            "actual_result": "Actual test result",
            "status": "passed"
        },
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_testcase.id
    assert data["name"] == "Updated Test Case"
    assert data["description"] == "Updated during unit test"
    assert data["expected_result"] == "Updated expected result"
    assert data["actual_result"] == "Actual test result"
    assert data["status"] == "passed"
    
    # Verify the database was updated
    db_testcase = db_session.query(TestCaseDB).filter(TestCaseDB.id == test_testcase.id).first()
    assert db_testcase.name == "Updated Test Case"
    assert db_testcase.description == "Updated during unit test"
    assert db_testcase.expected_result == "Updated expected result"
    assert db_testcase.actual_result == "Actual test result"
    assert db_testcase.status == "passed"

def test_delete_testcase(db_session, test_testcase, auth_headers, monkeypatch):
    """Test deleting a test case."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    response = client.delete(
        f"/api/v1/testcases/{test_testcase.id}",
        headers=auth_headers
    )
    
    assert response.status_code == 204
    
    # Verify the test case was soft-deleted
    db_testcase = db_session.query(TestCaseDB).filter(
        TestCaseDB.id == test_testcase.id
    ).first()
    
    assert db_testcase.deleted_time is not None

def test_restore_testcase(db_session, test_testcase, auth_headers, monkeypatch):
    """Test restoring a deleted test case."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    # First, soft-delete the test case
    test_testcase.soft_delete(db_session)
    
    response = client.post(
        f"/api/v1/testcases/{test_testcase.id}/restore",
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_testcase.id
    
    # Verify the test case was restored
    db_testcase = db_session.query(TestCaseDB).filter(
        TestCaseDB.id == test_testcase.id
    ).first()
    
    assert db_testcase.deleted_time is None

def test_execute_testcase(db_session, test_testcase, auth_headers, monkeypatch):
    """Test executing a test case."""
    # Mock the database dependency
    monkeypatch.setattr(app, "dependency_overrides", {get_db: lambda: db_session})
    
    response = client.post(
        f"/api/v1/testcases/{test_testcase.id}/execute",
        json={
            "actual_result": "Test execution result",
            "status": "passed"
        },
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_testcase.id
    assert data["actual_result"] == "Test execution result"
    assert data["status"] == "passed"
    
    # Verify the database was updated
    db_testcase = db_session.query(TestCaseDB).filter(TestCaseDB.id == test_testcase.id).first()
    assert db_testcase.actual_result == "Test execution result"
    assert db_testcase.status == "passed" 