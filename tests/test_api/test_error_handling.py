"""Tests for API error handling."""
import pytest
import json
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session
from datetime import datetime

from api.models.user import User, UserRole


@pytest.fixture
def error_handling_setup(db_session):
    """Create test data for error handling tests."""
    # Create users
    user = User(
        username="erroruser",
        email="<EMAIL>",
        role=UserRole.ANALYST
    )
    user.set_password("password123")
    
    admin = User(
        username="erroradmin",
        email="<EMAIL>",
        role=UserRole.ADMIN
    )
    admin.set_password("adminpass123")
    
    db_session.add(user)
    db_session.add(admin)
    db_session.commit()
    db_session.refresh(user)
    db_session.refresh(admin)
    
    return {
        "user": user,
        "admin": admin
    }


class TestErrorHandling:
    """Test API error handling."""

    def test_not_found_error(self, test_client):
        """Test 404 Not Found error handling."""
        # Test non-existent endpoint
        response = test_client.get("/api/v1/non-existent-endpoint")
        
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data
        assert "not found" in data["detail"].lower()

    def test_method_not_allowed_error(self, test_client):
        """Test 405 Method Not Allowed error handling."""
        # Test invalid method on existing endpoint
        response = test_client.put("/api/v1/health")  # Assuming health endpoint only supports GET
        
        assert response.status_code == 405
        data = response.json()
        assert "detail" in data
        assert "method not allowed" in data["detail"].lower()

    def test_validation_error(self, test_client, error_handling_setup):
        """Test 422 Validation Error handling."""
        user = error_handling_setup["user"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Test invalid request body
        invalid_data = {
            "invalid_field": "invalid_value"
        }
        
        response = test_client.post(
            "/api/v1/mitre/techniques/scores/bulk",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=invalid_data
        )
        
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data
        assert isinstance(data["detail"], list)
        assert len(data["detail"]) > 0
        assert "scores" in data["detail"][0]["loc"]

    def test_unauthorized_error(self, test_client):
        """Test 401 Unauthorized error handling."""
        # Test protected endpoint without authentication
        response = test_client.get("/api/v1/users/me")
        
        assert response.status_code == 401
        data = response.json()
        assert "detail" in data
        assert "not authenticated" in data["detail"].lower()
        
        # Test with invalid token
        response = test_client.get(
            "/api/v1/users/me",
            headers={"Authorization": "Bearer invalid_token"}
        )
        
        assert response.status_code == 401
        data = response.json()
        assert "detail" in data
        assert "invalid" in data["detail"].lower() or "expired" in data["detail"].lower()

    def test_forbidden_error(self, test_client, error_handling_setup):
        """Test 403 Forbidden error handling."""
        user = error_handling_setup["user"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Test admin-only endpoint with regular user
        response = test_client.get(
            "/api/v1/admin/users",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 403
        data = response.json()
        assert "detail" in data
        assert "permission" in data["detail"].lower() or "forbidden" in data["detail"].lower()

    def test_rate_limit_error(self, test_client, error_handling_setup):
        """Test rate limit error handling."""
        # This test is more of a placeholder since we can't easily trigger rate limits in tests
        # In a real scenario, we would need to make many requests in quick succession
        pass

    def test_internal_server_error(self, test_client, monkeypatch):
        """Test 500 Internal Server Error handling."""
        # Monkeypatch a route to raise an exception
        from fastapi import APIRouter, Depends, HTTPException
        from api.routes.v1 import health
        
        original_health_check = health.health_check
        
        def mock_health_check():
            raise Exception("Test internal server error")
        
        monkeypatch.setattr(health, "health_check", mock_health_check)
        
        # Test the endpoint that now raises an exception
        response = test_client.get("/api/v1/health")
        
        # Restore the original function
        monkeypatch.setattr(health, "health_check", original_health_check)
        
        assert response.status_code == 500
        data = response.json()
        assert "detail" in data
        assert "internal server error" in data["detail"].lower()

    def test_bad_request_error(self, test_client, error_handling_setup):
        """Test 400 Bad Request error handling."""
        user = error_handling_setup["user"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Test malformed JSON
        response = test_client.post(
            "/api/v1/mitre/techniques/scores/bulk",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            data="{ invalid json }"
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "detail" in data
        assert "json" in data["detail"].lower()

    def test_conflict_error(self, test_client, error_handling_setup, db_session):
        """Test 409 Conflict error handling."""
        admin = error_handling_setup["admin"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": admin.username})
        
        # Create a user
        user_data = {
            "username": "conflictuser",
            "email": "<EMAIL>",
            "password": "Password123!",
            "role": "analyst"
        }
        
        response = test_client.post(
            "/api/v1/users",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=user_data
        )
        
        assert response.status_code == 201
        
        # Try to create the same user again
        response = test_client.post(
            "/api/v1/users",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=user_data
        )
        
        assert response.status_code == 409
        data = response.json()
        assert "detail" in data
        assert "already exists" in data["detail"].lower() or "conflict" in data["detail"].lower()

    def test_too_many_requests_error(self, test_client, monkeypatch):
        """Test 429 Too Many Requests error handling."""
        # This is a placeholder since we can't easily trigger rate limits in tests
        # In a real scenario, we would need to monkeypatch the rate limiter
        pass

    def test_service_unavailable_error(self, test_client, monkeypatch):
        """Test 503 Service Unavailable error handling."""
        # Monkeypatch a route to raise a service unavailable exception
        from fastapi import APIRouter, Depends, HTTPException
        from api.routes.v1 import health
        
        original_health_check = health.health_check
        
        def mock_health_check():
            raise HTTPException(status_code=503, detail="Service temporarily unavailable")
        
        monkeypatch.setattr(health, "health_check", mock_health_check)
        
        # Test the endpoint that now raises an exception
        response = test_client.get("/api/v1/health")
        
        # Restore the original function
        monkeypatch.setattr(health, "health_check", original_health_check)
        
        assert response.status_code == 503
        data = response.json()
        assert "detail" in data
        assert "unavailable" in data["detail"].lower()

    def test_error_response_format(self, test_client):
        """Test that all error responses follow a consistent format."""
        # Test various error scenarios and verify response format
        endpoints = [
            "/api/v1/non-existent-endpoint",  # 404
            "/api/v1/users/me"                # 401 (without auth)
        ]
        
        for endpoint in endpoints:
            response = test_client.get(endpoint)
            
            assert response.status_code >= 400
            data = response.json()
            assert "detail" in data
            assert isinstance(data["detail"], str) or isinstance(data["detail"], list)

    def test_error_internationalization(self, test_client):
        """Test that error messages are internationalized."""
        # Test error with different Accept-Language headers
        languages = ["en", "de", "es"]
        
        for lang in languages:
            response = test_client.get(
                "/api/v1/non-existent-endpoint",
                headers={"Accept-Language": lang}
            )
            
            assert response.status_code == 404
            data = response.json()
            assert "detail" in data
            
            # We can't easily verify the actual translation without knowing the translations
            # But we can verify that the response is returned successfully

    def test_custom_error_handler(self, test_client, monkeypatch):
        """Test custom error handler for specific exceptions."""
        # This would require knowledge of any custom exceptions and handlers in the application
        pass
