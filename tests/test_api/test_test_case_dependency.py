"""
Tests for the test case dependency API endpoints.

This module contains tests for the test case dependency API endpoints, including
creating, retrieving, updating, and deleting dependencies.
"""
import pytest
from fastapi.testclient import TestClient

from api.main import app
from api.models.database.test_case import TestCase
from api.models.database.test_case_dependency import TestCaseDependency, DependencyType


client = TestClient(app)


@pytest.fixture
def admin_token():
    """Get an admin token for testing."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": "admin", "password": "adminpassword"}
    )
    return response.json()["access_token"]


@pytest.fixture
def test_cases(db_session):
    """Create test cases for testing."""
    test_cases = []
    for i in range(3):
        test_case = TestCase(
            name=f"Test Case {i+1}",
            description=f"Description for test case {i+1}",
            type="manual",
            status="active",
            priority="high",
            complexity="moderate",
            prerequisites="Access to the application",
            steps=[f"Step 1 for test case {i+1}", f"Step 2 for test case {i+1}"],
            expected_result=f"Expected result for test case {i+1}",
            tags=["security", "test"],
            created_by="1",
            version="1.0.0"
        )
        db_session.add(test_case)
        test_cases.append(test_case)
    
    db_session.commit()
    for test_case in test_cases:
        db_session.refresh(test_case)
    
    yield test_cases
    
    # Clean up
    for test_case in test_cases:
        db_session.query(TestCaseDependency).filter(
            (TestCaseDependency.test_case_id == test_case.id) |
            (TestCaseDependency.dependency_test_case_id == test_case.id)
        ).delete()
    
    for test_case in test_cases:
        db_session.delete(test_case)
    
    db_session.commit()


@pytest.fixture
def test_dependency(db_session, test_cases):
    """Create a test dependency for testing."""
    dependency = TestCaseDependency(
        test_case_id=test_cases[0].id,
        dependency_test_case_id=test_cases[1].id,
        dependency_type=DependencyType.DEPENDS_ON,
        description="Test Case 1 depends on Test Case 2",
        created_by="1"
    )
    db_session.add(dependency)
    db_session.commit()
    db_session.refresh(dependency)
    
    yield dependency
    
    # Clean up is handled by test_cases fixture


def test_create_dependency(client, db_session, test_cases, admin_token):
    """Test creating a dependency."""
    response = client.post(
        f"/api/v1/test-cases/{test_cases[0].id}/dependencies",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "dependency_test_case_id": test_cases[2].id,
            "dependency_type": "blocks",
            "description": "Test Case 1 blocks Test Case 3"
        }
    )
    
    assert response.status_code == 201
    data = response.json()
    
    # Verify response data
    assert data["test_case_id"] == test_cases[0].id
    assert data["dependency_test_case_id"] == test_cases[2].id
    assert data["dependency_type"] == "blocks"
    assert data["description"] == "Test Case 1 blocks Test Case 3"
    
    # Verify database state
    db_dependency = db_session.query(TestCaseDependency).filter(
        TestCaseDependency.id == data["id"]
    ).first()
    assert db_dependency is not None
    assert db_dependency.test_case_id == test_cases[0].id
    assert db_dependency.dependency_test_case_id == test_cases[2].id
    
    # Verify inverse dependency was created
    inverse_dependency = db_session.query(TestCaseDependency).filter(
        TestCaseDependency.test_case_id == test_cases[2].id,
        TestCaseDependency.dependency_test_case_id == test_cases[0].id,
        TestCaseDependency.dependency_type == "depends_on"
    ).first()
    assert inverse_dependency is not None


def test_get_dependencies(client, db_session, test_cases, test_dependency, admin_token):
    """Test getting dependencies for a test case."""
    response = client.get(
        f"/api/v1/test-cases/{test_cases[0].id}/dependencies",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 1
    assert data[0]["id"] == test_dependency.id
    assert data[0]["test_case_id"] == test_cases[0].id
    assert data[0]["dependency_test_case_id"] == test_cases[1].id
    assert data[0]["dependency_type"] == "depends_on"


def test_get_dependent_test_cases(client, db_session, test_cases, test_dependency, admin_token):
    """Test getting test cases that depend on a test case."""
    response = client.get(
        f"/api/v1/test-cases/{test_cases[1].id}/dependent-test-cases",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 1
    assert data[0]["test_case_id"] == test_cases[0].id
    assert data[0]["dependency_test_case_id"] == test_cases[1].id
    assert data[0]["dependency_type"] == "depends_on"


def test_get_dependency(client, db_session, test_cases, test_dependency, admin_token):
    """Test getting a specific dependency."""
    response = client.get(
        f"/api/v1/test-cases/{test_cases[0].id}/dependencies/{test_dependency.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_dependency.id
    assert data["test_case_id"] == test_cases[0].id
    assert data["dependency_test_case_id"] == test_cases[1].id
    assert data["dependency_type"] == "depends_on"
    assert "test_case" in data
    assert "dependency_test_case" in data
    assert data["test_case"]["id"] == test_cases[0].id
    assert data["dependency_test_case"]["id"] == test_cases[1].id


def test_update_dependency(client, db_session, test_cases, test_dependency, admin_token):
    """Test updating a dependency."""
    response = client.put(
        f"/api/v1/test-cases/{test_cases[0].id}/dependencies/{test_dependency.id}",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "dependency_type": "related_to",
            "description": "Test Case 1 is related to Test Case 2"
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_dependency.id
    assert data["dependency_type"] == "related_to"
    assert data["description"] == "Test Case 1 is related to Test Case 2"
    
    # Verify database state
    db_session.refresh(test_dependency)
    assert test_dependency.dependency_type == "related_to"
    assert test_dependency.description == "Test Case 1 is related to Test Case 2"
    
    # Verify inverse dependency was updated
    inverse_dependency = db_session.query(TestCaseDependency).filter(
        TestCaseDependency.test_case_id == test_cases[1].id,
        TestCaseDependency.dependency_test_case_id == test_cases[0].id
    ).first()
    assert inverse_dependency is not None
    assert inverse_dependency.dependency_type == "related_to"


def test_delete_dependency(client, db_session, test_cases, test_dependency, admin_token):
    """Test deleting a dependency."""
    # Create inverse dependency for testing
    inverse_dependency = TestCaseDependency(
        test_case_id=test_cases[1].id,
        dependency_test_case_id=test_cases[0].id,
        dependency_type=DependencyType.BLOCKS,
        description="Test Case 2 blocks Test Case 1",
        created_by="1"
    )
    db_session.add(inverse_dependency)
    db_session.commit()
    db_session.refresh(inverse_dependency)
    
    response = client.delete(
        f"/api/v1/test-cases/{test_cases[0].id}/dependencies/{test_dependency.id}?delete_inverse=true",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 204
    
    # Verify database state
    db_dependency = db_session.query(TestCaseDependency).filter(
        TestCaseDependency.id == test_dependency.id
    ).first()
    assert db_dependency is None
    
    # Verify inverse dependency was deleted
    inverse_db_dependency = db_session.query(TestCaseDependency).filter(
        TestCaseDependency.test_case_id == test_cases[1].id,
        TestCaseDependency.dependency_test_case_id == test_cases[0].id
    ).first()
    assert inverse_db_dependency is None


def test_get_dependency_chain(client, db_session, test_cases, admin_token):
    """Test getting the dependency chain for a test case."""
    # Create a chain of dependencies: 1 -> 2 -> 3
    dependency1 = TestCaseDependency(
        test_case_id=test_cases[0].id,
        dependency_test_case_id=test_cases[1].id,
        dependency_type=DependencyType.DEPENDS_ON,
        description="Test Case 1 depends on Test Case 2",
        created_by="1"
    )
    dependency2 = TestCaseDependency(
        test_case_id=test_cases[1].id,
        dependency_test_case_id=test_cases[2].id,
        dependency_type=DependencyType.DEPENDS_ON,
        description="Test Case 2 depends on Test Case 3",
        created_by="1"
    )
    db_session.add(dependency1)
    db_session.add(dependency2)
    db_session.commit()
    
    response = client.get(
        f"/api/v1/test-cases/{test_cases[0].id}/dependency-chain?dependency_type=depends_on",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 2
    
    # Find the first level dependency
    first_level = next((d for d in data if d["test_case_id"] == test_cases[0].id), None)
    assert first_level is not None
    assert first_level["dependency_test_case_id"] == test_cases[1].id
    assert first_level["depth"] == 1
    
    # Find the second level dependency
    second_level = next((d for d in data if d["test_case_id"] == test_cases[1].id), None)
    assert second_level is not None
    assert second_level["dependency_test_case_id"] == test_cases[2].id
    assert second_level["depth"] == 2


def test_get_execution_order(client, db_session, test_cases, admin_token):
    """Test getting the optimal execution order for a set of test cases."""
    # Create dependencies: 1 -> 2 -> 3
    dependency1 = TestCaseDependency(
        test_case_id=test_cases[0].id,
        dependency_test_case_id=test_cases[1].id,
        dependency_type=DependencyType.DEPENDS_ON,
        description="Test Case 1 depends on Test Case 2",
        created_by="1"
    )
    dependency2 = TestCaseDependency(
        test_case_id=test_cases[1].id,
        dependency_test_case_id=test_cases[2].id,
        dependency_type=DependencyType.DEPENDS_ON,
        description="Test Case 2 depends on Test Case 3",
        created_by="1"
    )
    db_session.add(dependency1)
    db_session.add(dependency2)
    db_session.commit()
    
    response = client.post(
        "/api/v1/test-cases/execution-order",
        headers={"Authorization": f"Bearer {admin_token}"},
        json=[test_cases[0].id, test_cases[1].id, test_cases[2].id]
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 3
    
    # The order should be 3 -> 2 -> 1 (reverse of the dependency chain)
    assert data[0] == test_cases[2].id
    assert data[1] == test_cases[1].id
    assert data[2] == test_cases[0].id


def test_circular_dependency_prevention(client, db_session, test_cases, admin_token):
    """Test that circular dependencies are prevented."""
    # Create dependency: 1 -> 2
    dependency1 = TestCaseDependency(
        test_case_id=test_cases[0].id,
        dependency_test_case_id=test_cases[1].id,
        dependency_type=DependencyType.DEPENDS_ON,
        description="Test Case 1 depends on Test Case 2",
        created_by="1"
    )
    db_session.add(dependency1)
    db_session.commit()
    
    # Try to create circular dependency: 2 -> 1
    response = client.post(
        f"/api/v1/test-cases/{test_cases[1].id}/dependencies",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "dependency_test_case_id": test_cases[0].id,
            "dependency_type": "depends_on",
            "description": "Test Case 2 depends on Test Case 1"
        }
    )
    
    assert response.status_code == 400
    data = response.json()
    assert "circular dependency" in data["detail"].lower()


def test_self_dependency_prevention(client, db_session, test_cases, admin_token):
    """Test that self-dependencies are prevented."""
    response = client.post(
        f"/api/v1/test-cases/{test_cases[0].id}/dependencies",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "dependency_test_case_id": test_cases[0].id,
            "dependency_type": "depends_on",
            "description": "Test Case 1 depends on itself"
        }
    )
    
    assert response.status_code == 400
    data = response.json()
    assert "cannot depend on itself" in data["detail"].lower()
