"""Tests for the base framework importer."""
import pytest
from sqlalchemy.orm import Session
from api.utils.import_base import BaseFrameworkImporter, ImportError
from api.database import get_db

class MockImporter(BaseFrameworkImporter):
    """Mock importer for testing base functionality."""
    
    def extract_version(self, data):
        if data.get('fail_version'):
            raise ImportError("Version extraction failed")
        return data.get('version', 'test_version')
        
    def validate_data(self, data):
        if data.get('fail_validation'):
            return False
        return True
        
    def import_data(self, data, version=None, **kwargs):
        if data.get('fail_import'):
            raise ImportError("Import failed")
        return {"imported": True}

@pytest.fixture
def mock_importer(db: Session):
    return MockImporter(db)

def test_process_import_success(mock_importer):
    """Test successful import process."""
    data = {"version": "1.0"}
    result = mock_importer.process_import(data)
    assert result == {"imported": True}

def test_process_import_validation_failure(mock_importer):
    """Test import process with validation failure."""
    data = {"fail_validation": True}
    with pytest.raises(ImportError) as exc:
        mock_importer.process_import(data)
    assert "Data validation failed" in str(exc.value)

def test_process_import_version_failure(mock_importer):
    """Test import process with version extraction failure."""
    data = {"fail_version": True}
    with pytest.raises(ImportError) as exc:
        mock_importer.process_import(data)
    assert "Version extraction failed" in str(exc.value)

def test_process_import_import_failure(mock_importer):
    """Test import process with import failure."""
    data = {"fail_import": True}
    with pytest.raises(ImportError) as exc:
        mock_importer.process_import(data)
    assert "Import failed" in str(exc.value)

def test_transaction_rollback(mock_importer):
    """Test transaction rollback on failure."""
    data = {"fail_import": True}
    
    # Count DB operations before
    initial_count = mock_importer.db.execute("SELECT COUNT(*) FROM mitre_tactics").scalar()
    
    with pytest.raises(ImportError):
        mock_importer.process_import(data)
        
    # Count should be same after rollback
    final_count = mock_importer.db.execute("SELECT COUNT(*) FROM mitre_tactics").scalar()
    assert initial_count == final_count
