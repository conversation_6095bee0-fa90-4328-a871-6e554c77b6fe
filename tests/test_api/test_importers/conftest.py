"""Configuration file for importer tests."""
import pytest
from sqlalchemy import text
from api.database import SessionLocal, Base

@pytest.fixture(scope="function")
def db():
    """Create a fresh database session for testing."""
    # Create database connection
    session = SessionLocal()
    
    try:
        # Create all database tables
        Base.metadata.create_all(bind=session.get_bind())
        yield session
    finally:
        # Drop all tables
        Base.metadata.drop_all(bind=session.get_bind())
        session.close()
