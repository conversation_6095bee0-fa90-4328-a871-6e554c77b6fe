"""Tests for the ATLAS framework importer."""
import pytest
from api.utils.atlas_importer import AtlasImporter, ImportError
from api.database import get_db

@pytest.fixture
def sample_atlas_data():
    """Sample ATLAS data for testing."""
    return {
        "version": "2025.01",
        "name": "ATLAS Test Matrix",
        "description": "Test matrix for ATLAS import",
        "tactics": [
            {"id": "T1", "name": "Test Tactic"}
        ],
        "techniques": [
            {
                "id": "TECH1",
                "name": "Test Technique",
                "description": "Test technique description",
                "relationships": [
                    {"target": "TECH2", "type": "related-to"}
                ]
            },
            {
                "id": "TECH2",
                "name": "Related Technique",
                "description": "Related technique description"
            }
        ]
    }

def test_extract_version_success(db, sample_atlas_data):
    """Test successful version extraction."""
    importer = AtlasImporter(db)
    version = importer.extract_version(sample_atlas_data)
    assert version == "2025.01"

def test_extract_version_fallback(db):
    """Test version extraction fallback."""
    importer = AtlasImporter(db)
    data = {"name": "Test"}
    version = importer.extract_version(data)
    assert version.startswith("2025.")

def test_validate_data_success(db, sample_atlas_data):
    """Test successful data validation."""
    importer = AtlasImporter(db)
    assert importer.validate_data(sample_atlas_data) is True

def test_validate_data_failure(db):
    """Test data validation failure."""
    importer = AtlasImporter(db)
    invalid_data = {"name": "Invalid"}  # Missing required fields
    assert importer.validate_data(invalid_data) is False

def test_import_data_success(db, sample_atlas_data):
    """Test successful data import."""
    importer = AtlasImporter(db)
    results = importer.import_data(sample_atlas_data, version="2025.01")
    
    assert results["matrices"] == 1
    assert results["techniques"] == 2
    assert results["relationships"] == 1

def test_full_import_process(db, sample_atlas_data):
    """Test complete import process."""
    importer = AtlasImporter(db)
    results = importer.process_import(sample_atlas_data)
    
    # Verify database state
    matrices = db.execute("SELECT COUNT(*) FROM atlas_matrices").scalar()
    techniques = db.execute("SELECT COUNT(*) FROM atlas_techniques").scalar()
    relationships = db.execute("SELECT COUNT(*) FROM atlas_relationships").scalar()
    
    assert matrices == 1
    assert techniques == 2
    assert relationships == 1
