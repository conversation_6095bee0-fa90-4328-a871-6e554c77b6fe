"""Tests for the MITRE framework importer."""
import pytest
from datetime import datetime
from api.utils.mitre_importer import MITREImporter, ImportError
from api.database import get_db

@pytest.fixture
def sample_mitre_data():
    """Sample MITRE ATT&CK data for testing."""
    class Bundle:
        def __init__(self, objects):
            self.objects = objects

        def get_bundle(self):
            return self

    class Object:
        def __init__(self, type, **kwargs):
            self.type = type
            for k, v in kwargs.items():
                setattr(self, k, v)

    return Bundle([
        Object("x-mitre-matrix", 
               modified="2025-01-01T00:00:00.000Z"),
        Object("x-mitre-tactic",
               external_references=[Object(type="external-reference", external_id="TA0001")],
               name="Initial Access",
               description="Gaining initial access"),
        Object("attack-pattern",
               external_references=[Object(type="external-reference", external_id="T1190")],
               name="Exploit Public-Facing Application",
               description="Exploiting vulnerabilities",
               kill_chain_phases=[Object(type="kill-chain-phase", phase_name="TA0001")])
    ])

def test_extract_version(db, sample_mitre_data):
    """Test version extraction from MITRE data."""
    importer = MITREImporter(db)
    version = importer.extract_version(sample_mitre_data)
    assert version == "2025.01"

def test_validate_data_success(db, sample_mitre_data):
    """Test successful data validation."""
    importer = MITREImporter(db)
    assert importer.validate_data(sample_mitre_data) is True

def test_validate_data_failure(db):
    """Test data validation failure."""
    importer = MITREImporter(db)
    invalid_data = type('Bundle', (), {'objects': []})()
    assert importer.validate_data(invalid_data) is False

def test_import_data_success(db, sample_mitre_data):
    """Test successful data import."""
    importer = MITREImporter(db)
    results = importer.import_data(sample_mitre_data, version="2025.01")

    assert results["tactics"] == 1
    assert results["techniques"] == 1
    assert results["relationships"] >= 1

def test_full_import_process(db, sample_mitre_data):
    """Test complete import process."""
    importer = MITREImporter(db)
    results = importer.process_import(sample_mitre_data)

    # Verify database state
    tactics = db.execute("SELECT COUNT(*) FROM mitre_tactics").scalar()
    techniques = db.execute("SELECT COUNT(*) FROM mitre_techniques").scalar()

    assert tactics == 1
    assert techniques == 1