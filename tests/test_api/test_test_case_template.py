"""
Tests for the test case template API endpoints.

This module contains tests for the test case template API endpoints, including
creating, retrieving, updating, and deleting templates, as well as creating
test cases from templates.
"""
import pytest
from fastapi.testclient import TestClient

from api.main import app
from api.models.database.test_case_template import TestCaseTemplate
from api.models.database.test_case import TestCase


client = TestClient(app)


@pytest.fixture
def admin_token():
    """Get an admin token for testing."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": "admin", "password": "adminpassword"}
    )
    return response.json()["access_token"]


@pytest.fixture
def test_template(db_session):
    """Create a test template for testing."""
    template = TestCaseTemplate(
        name="SQL Injection Test Template",
        description="Template for SQL injection tests",
        type="manual",
        priority="high",
        complexity="moderate",
        prerequisites="Access to the application",
        steps=["Step 1: Navigate to login page", "Step 2: Enter SQL injection payload"],
        expected_result="Application should sanitize input and prevent SQL injection",
        tags=["security", "injection", "sql"],
        mitre_techniques=["T1190", "T1212"],
        is_public=True,
        category="Security",
        created_by="1",
        version="1.0.0"
    )
    db_session.add(template)
    db_session.commit()
    db_session.refresh(template)
    
    yield template
    
    # Clean up
    db_session.query(TestCase).filter(TestCase.template_id == template.id).delete()
    db_session.query(TestCaseTemplate).filter(TestCaseTemplate.id == template.id).delete()
    db_session.commit()


def test_create_template(client, db_session, admin_token):
    """Test creating a template."""
    response = client.post(
        "/api/v1/test-case-templates",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "name": "XSS Test Template",
            "description": "Template for cross-site scripting tests",
            "type": "manual",
            "priority": "high",
            "complexity": "moderate",
            "prerequisites": "Access to the application",
            "steps": ["Step 1: Navigate to input form", "Step 2: Enter XSS payload"],
            "expected_result": "Application should sanitize input and prevent XSS",
            "tags": ["security", "injection", "xss"],
            "mitre_techniques": ["T1059", "T1189"],
            "is_public": True,
            "category": "Security"
        }
    )
    
    assert response.status_code == 201
    data = response.json()
    
    # Verify response data
    assert data["name"] == "XSS Test Template"
    assert data["type"] == "manual"
    assert data["priority"] == "high"
    assert "id" in data
    
    # Verify database state
    db_template = db_session.query(TestCaseTemplate).filter(TestCaseTemplate.id == data["id"]).first()
    assert db_template is not None
    assert db_template.name == "XSS Test Template"
    assert db_template.steps == ["Step 1: Navigate to input form", "Step 2: Enter XSS payload"]
    
    # Clean up
    db_session.delete(db_template)
    db_session.commit()


def test_get_templates(client, db_session, test_template, admin_token):
    """Test getting templates."""
    response = client.get(
        "/api/v1/test-case-templates",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    
    # Find our test template
    template_data = next((t for t in data if t["id"] == test_template.id), None)
    assert template_data is not None
    assert template_data["name"] == test_template.name
    assert template_data["category"] == "Security"


def test_get_template(client, db_session, test_template, admin_token):
    """Test getting a specific template."""
    response = client.get(
        f"/api/v1/test-case-templates/{test_template.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_template.id
    assert data["name"] == test_template.name
    assert data["description"] == test_template.description
    assert data["steps"] == test_template.steps


def test_update_template(client, db_session, test_template, admin_token):
    """Test updating a template."""
    response = client.put(
        f"/api/v1/test-case-templates/{test_template.id}",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "name": "Updated SQL Injection Template",
            "steps": ["Step 1: Navigate to login page", "Step 2: Enter SQL injection payload", "Step 3: Submit form"]
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "Updated SQL Injection Template"
    assert len(data["steps"]) == 3
    assert data["steps"][2] == "Step 3: Submit form"
    
    # Verify database state
    db_session.refresh(test_template)
    assert test_template.name == "Updated SQL Injection Template"
    assert len(test_template.steps) == 3


def test_delete_template(client, db_session, test_template, admin_token):
    """Test deleting a template."""
    response = client.delete(
        f"/api/v1/test-case-templates/{test_template.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 204
    
    # Verify database state (soft delete)
    db_session.refresh(test_template)
    assert test_template.deleted_at is not None


def test_restore_template(client, db_session, test_template, admin_token):
    """Test restoring a deleted template."""
    # First delete the template
    test_template.soft_delete(db_session)
    db_session.commit()
    
    response = client.post(
        f"/api/v1/test-case-templates/{test_template.id}/restore",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_template.id
    
    # Verify database state
    db_session.refresh(test_template)
    assert test_template.deleted_at is None


def test_create_test_case_from_template(client, db_session, test_template, admin_token):
    """Test creating a test case from a template."""
    response = client.post(
        "/api/v1/test-case-templates/create-test-case",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "template_id": test_template.id,
            "name": "Custom SQL Injection Test",
            "override_fields": {
                "priority": "critical",
                "steps": ["Step 1: Navigate to login page", "Step 2: Enter custom SQL injection payload"]
            }
        }
    )
    
    assert response.status_code == 201
    data = response.json()
    assert "message" in data
    assert "test_case" in data
    assert data["test_case"]["name"] == "Custom SQL Injection Test"
    assert data["test_case"]["priority"] == "critical"
    
    # Verify database state
    test_case_id = data["test_case"]["id"]
    test_case = db_session.query(TestCase).filter(TestCase.id == test_case_id).first()
    assert test_case is not None
    assert test_case.name == "Custom SQL Injection Test"
    assert test_case.template_id == test_template.id
    assert test_case.steps == ["Step 1: Navigate to login page", "Step 2: Enter custom SQL injection payload"]
    
    # Clean up
    db_session.delete(test_case)
    db_session.commit()


def test_filter_templates(client, db_session, test_template, admin_token):
    """Test filtering templates."""
    # Create another template with different category
    other_template = TestCaseTemplate(
        name="Performance Test Template",
        description="Template for performance tests",
        type="automated",
        priority="medium",
        complexity="simple",
        prerequisites="Test environment",
        steps=["Step 1: Setup test data", "Step 2: Run performance test"],
        expected_result="Response time under threshold",
        tags=["performance", "load"],
        mitre_techniques=[],
        is_public=True,
        category="Performance",
        created_by="1",
        version="1.0.0"
    )
    db_session.add(other_template)
    db_session.commit()
    
    # Test filtering by category
    response = client.get(
        "/api/v1/test-case-templates?category=Security",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    assert all(t["category"] == "Security" for t in data)
    
    # Test filtering by type
    response = client.get(
        "/api/v1/test-case-templates?type=automated",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    assert all(t["type"] == "automated" for t in data)
    
    # Test search
    response = client.get(
        "/api/v1/test-case-templates?search=SQL",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    assert any("SQL" in t["name"] for t in data)
    
    # Clean up
    db_session.delete(other_template)
    db_session.commit()
