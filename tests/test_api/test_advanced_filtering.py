"""Tests for advanced filtering functionality."""
import pytest
import json
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.models.database.dashboard import Dashboard
from api.models.database.dashboard_filter import DashboardFilter
from api.models.user import User, UserRole
from api.services import dashboard_filters
from models.dashboard import FilterOperator


@pytest.fixture
def test_filter_setup(db_session):
    """Create test user, dashboard, and filters for testing."""
    # Create a test user
    user = User(
        username="filteruser",
        email="<EMAIL>",
        role=UserRole.ANALYST
    )
    user.set_password("password123")
    db_session.add(user)
    db_session.commit()

    # Create a dashboard
    dashboard = Dashboard(
        name="Filter Test Dashboard",
        description="Dashboard for filter testing",
        layout={"type": "grid", "columns": 12},
        is_public=False,
        created_by_id=user.id
    )
    db_session.add(dashboard)
    db_session.commit()

    # Create filters
    filters = [
        DashboardFilter(
            dashboard_id=dashboard.id,
            name="Status Filter",
            field="status",
            operator="equals",
            value="active",
            is_global=True,
            applies_to_widgets=None
        ),
        DashboardFilter(
            dashboard_id=dashboard.id,
            name="Priority Filter",
            field="priority",
            operator="in",
            value=["high", "critical"],
            is_global=False,
            applies_to_widgets=[1, 2]
        ),
        DashboardFilter(
            dashboard_id=dashboard.id,
            name="Date Filter",
            field="created_at",
            operator="greater_than",
            value="2023-01-01T00:00:00Z",
            is_global=True,
            applies_to_widgets=None
        )
    ]
    
    for filter_obj in filters:
        db_session.add(filter_obj)
    
    db_session.commit()
    
    return {"dashboard": dashboard, "user": user, "filters": filters}


class TestAdvancedFiltering:
    """Test advanced filtering functionality."""

    def test_create_filter(self, db_session, test_filter_setup):
        """Test creating a filter."""
        dashboard = test_filter_setup["dashboard"]
        user = test_filter_setup["user"]
        
        # Create filter data
        from models.dashboard import FilterCreate
        
        filter_data = FilterCreate(
            dashboard_id=dashboard.id,
            name="Test Filter",
            field="name",
            operator="contains",
            value="test",
            is_global=True,
            applies_to_widgets=None
        )
        
        # Create filter
        filter_obj = dashboard_filters.create_filter(db_session, filter_data)
        
        # Verify filter was created
        assert filter_obj is not None
        assert filter_obj.id is not None
        assert filter_obj.dashboard_id == dashboard.id
        assert filter_obj.name == "Test Filter"
        assert filter_obj.field == "name"
        assert filter_obj.operator == "contains"
        assert filter_obj.value == "test"
        assert filter_obj.is_global is True
        assert filter_obj.applies_to_widgets is None

    def test_get_filters(self, db_session, test_filter_setup):
        """Test getting filters for a dashboard."""
        dashboard = test_filter_setup["dashboard"]
        
        # Get filters
        filters = dashboard_filters.get_filters(db_session, dashboard.id)
        
        # Verify filters
        assert len(filters) == 3
        
        # Verify filter details
        status_filter = next((f for f in filters if f.name == "Status Filter"), None)
        assert status_filter is not None
        assert status_filter.field == "status"
        assert status_filter.operator == "equals"
        assert status_filter.value == "active"
        
        priority_filter = next((f for f in filters if f.name == "Priority Filter"), None)
        assert priority_filter is not None
        assert priority_filter.field == "priority"
        assert priority_filter.operator == "in"
        assert priority_filter.value == ["high", "critical"]
        assert priority_filter.is_global is False
        assert priority_filter.applies_to_widgets == [1, 2]

    def test_update_filter(self, db_session, test_filter_setup):
        """Test updating a filter."""
        filters = test_filter_setup["filters"]
        user = test_filter_setup["user"]
        
        # Get a filter to update
        filter_to_update = filters[0]
        
        # Create update data
        from models.dashboard import FilterUpdate
        
        update_data = FilterUpdate(
            name="Updated Filter",
            operator="not_equals",
            value="inactive"
        )
        
        # Update filter
        updated_filter = dashboard_filters.update_filter(
            db_session, filter_to_update.id, update_data, user.id
        )
        
        # Verify filter was updated
        assert updated_filter is not None
        assert updated_filter.id == filter_to_update.id
        assert updated_filter.name == "Updated Filter"
        assert updated_filter.field == "status"  # Unchanged
        assert updated_filter.operator == "not_equals"
        assert updated_filter.value == "inactive"
        assert updated_filter.is_global is True  # Unchanged

    def test_delete_filter(self, db_session, test_filter_setup):
        """Test deleting a filter."""
        filters = test_filter_setup["filters"]
        user = test_filter_setup["user"]
        
        # Get a filter to delete
        filter_to_delete = filters[0]
        filter_id = filter_to_delete.id
        
        # Delete filter
        success = dashboard_filters.delete_filter(db_session, filter_id, user.id)
        
        # Verify filter was deleted
        assert success is True
        
        # Verify filter no longer exists
        deleted_filter = db_session.query(DashboardFilter).filter(
            DashboardFilter.id == filter_id
        ).first()
        
        assert deleted_filter is None
        
        # Verify other filters still exist
        remaining_filters = dashboard_filters.get_filters(
            db_session, test_filter_setup["dashboard"].id
        )
        
        assert len(remaining_filters) == 2

    def test_apply_filter_equals(self, db_session):
        """Test applying an equals filter."""
        # Create test data
        test_data = [
            {"id": 1, "status": "active", "name": "Item 1"},
            {"id": 2, "status": "inactive", "name": "Item 2"},
            {"id": 3, "status": "active", "name": "Item 3"}
        ]
        
        # Create filter
        filter_obj = DashboardFilter(
            dashboard_id=1,  # Dummy ID
            name="Status Filter",
            field="status",
            operator="equals",
            value="active",
            is_global=True
        )
        
        # Apply filter
        filtered_data = dashboard_filters.apply_filter(test_data, filter_obj)
        
        # Verify filtered data
        assert len(filtered_data) == 2
        assert filtered_data[0]["id"] == 1
        assert filtered_data[1]["id"] == 3

    def test_apply_filter_not_equals(self, db_session):
        """Test applying a not_equals filter."""
        # Create test data
        test_data = [
            {"id": 1, "status": "active", "name": "Item 1"},
            {"id": 2, "status": "inactive", "name": "Item 2"},
            {"id": 3, "status": "active", "name": "Item 3"}
        ]
        
        # Create filter
        filter_obj = DashboardFilter(
            dashboard_id=1,  # Dummy ID
            name="Status Filter",
            field="status",
            operator="not_equals",
            value="active",
            is_global=True
        )
        
        # Apply filter
        filtered_data = dashboard_filters.apply_filter(test_data, filter_obj)
        
        # Verify filtered data
        assert len(filtered_data) == 1
        assert filtered_data[0]["id"] == 2

    def test_apply_filter_in(self, db_session):
        """Test applying an in filter."""
        # Create test data
        test_data = [
            {"id": 1, "priority": "low", "name": "Item 1"},
            {"id": 2, "priority": "medium", "name": "Item 2"},
            {"id": 3, "priority": "high", "name": "Item 3"},
            {"id": 4, "priority": "critical", "name": "Item 4"}
        ]
        
        # Create filter
        filter_obj = DashboardFilter(
            dashboard_id=1,  # Dummy ID
            name="Priority Filter",
            field="priority",
            operator="in",
            value=["high", "critical"],
            is_global=True
        )
        
        # Apply filter
        filtered_data = dashboard_filters.apply_filter(test_data, filter_obj)
        
        # Verify filtered data
        assert len(filtered_data) == 2
        assert filtered_data[0]["id"] == 3
        assert filtered_data[1]["id"] == 4

    def test_apply_filter_not_in(self, db_session):
        """Test applying a not_in filter."""
        # Create test data
        test_data = [
            {"id": 1, "priority": "low", "name": "Item 1"},
            {"id": 2, "priority": "medium", "name": "Item 2"},
            {"id": 3, "priority": "high", "name": "Item 3"},
            {"id": 4, "priority": "critical", "name": "Item 4"}
        ]
        
        # Create filter
        filter_obj = DashboardFilter(
            dashboard_id=1,  # Dummy ID
            name="Priority Filter",
            field="priority",
            operator="not_in",
            value=["high", "critical"],
            is_global=True
        )
        
        # Apply filter
        filtered_data = dashboard_filters.apply_filter(test_data, filter_obj)
        
        # Verify filtered data
        assert len(filtered_data) == 2
        assert filtered_data[0]["id"] == 1
        assert filtered_data[1]["id"] == 2

    def test_apply_filter_contains(self, db_session):
        """Test applying a contains filter."""
        # Create test data
        test_data = [
            {"id": 1, "name": "Test Item 1"},
            {"id": 2, "name": "Sample Item 2"},
            {"id": 3, "name": "Test Item 3"}
        ]
        
        # Create filter
        filter_obj = DashboardFilter(
            dashboard_id=1,  # Dummy ID
            name="Name Filter",
            field="name",
            operator="contains",
            value="Test",
            is_global=True
        )
        
        # Apply filter
        filtered_data = dashboard_filters.apply_filter(test_data, filter_obj)
        
        # Verify filtered data
        assert len(filtered_data) == 2
        assert filtered_data[0]["id"] == 1
        assert filtered_data[1]["id"] == 3

    def test_apply_filter_greater_than(self, db_session):
        """Test applying a greater_than filter."""
        # Create test data
        test_data = [
            {"id": 1, "value": 10, "name": "Item 1"},
            {"id": 2, "value": 20, "name": "Item 2"},
            {"id": 3, "value": 30, "name": "Item 3"}
        ]
        
        # Create filter
        filter_obj = DashboardFilter(
            dashboard_id=1,  # Dummy ID
            name="Value Filter",
            field="value",
            operator="greater_than",
            value=15,
            is_global=True
        )
        
        # Apply filter
        filtered_data = dashboard_filters.apply_filter(test_data, filter_obj)
        
        # Verify filtered data
        assert len(filtered_data) == 2
        assert filtered_data[0]["id"] == 2
        assert filtered_data[1]["id"] == 3

    def test_apply_filter_less_than(self, db_session):
        """Test applying a less_than filter."""
        # Create test data
        test_data = [
            {"id": 1, "value": 10, "name": "Item 1"},
            {"id": 2, "value": 20, "name": "Item 2"},
            {"id": 3, "value": 30, "name": "Item 3"}
        ]
        
        # Create filter
        filter_obj = DashboardFilter(
            dashboard_id=1,  # Dummy ID
            name="Value Filter",
            field="value",
            operator="less_than",
            value=25,
            is_global=True
        )
        
        # Apply filter
        filtered_data = dashboard_filters.apply_filter(test_data, filter_obj)
        
        # Verify filtered data
        assert len(filtered_data) == 2
        assert filtered_data[0]["id"] == 1
        assert filtered_data[1]["id"] == 2

    def test_apply_multiple_filters(self, db_session):
        """Test applying multiple filters."""
        # Create test data
        test_data = [
            {"id": 1, "status": "active", "priority": "low", "name": "Test Item 1"},
            {"id": 2, "status": "inactive", "priority": "medium", "name": "Sample Item 2"},
            {"id": 3, "status": "active", "priority": "high", "name": "Test Item 3"},
            {"id": 4, "status": "active", "priority": "critical", "name": "Sample Item 4"}
        ]
        
        # Create filters
        filters = [
            DashboardFilter(
                dashboard_id=1,  # Dummy ID
                name="Status Filter",
                field="status",
                operator="equals",
                value="active",
                is_global=True
            ),
            DashboardFilter(
                dashboard_id=1,  # Dummy ID
                name="Name Filter",
                field="name",
                operator="contains",
                value="Test",
                is_global=True
            )
        ]
        
        # Apply filters
        filtered_data = test_data
        for filter_obj in filters:
            filtered_data = dashboard_filters.apply_filter(filtered_data, filter_obj)
        
        # Verify filtered data
        assert len(filtered_data) == 2
        assert filtered_data[0]["id"] == 1
        assert filtered_data[1]["id"] == 3

    def test_apply_filters_with_widget_specific(self, db_session):
        """Test applying filters with widget-specific filters."""
        # Create test data
        test_data = [
            {"id": 1, "status": "active", "priority": "low", "name": "Item 1"},
            {"id": 2, "status": "inactive", "priority": "medium", "name": "Item 2"},
            {"id": 3, "status": "active", "priority": "high", "name": "Item 3"},
            {"id": 4, "status": "active", "priority": "critical", "name": "Item 4"}
        ]
        
        # Create filters
        filters = [
            DashboardFilter(
                dashboard_id=1,  # Dummy ID
                name="Status Filter",
                field="status",
                operator="equals",
                value="active",
                is_global=True
            ),
            DashboardFilter(
                dashboard_id=1,  # Dummy ID
                name="Priority Filter",
                field="priority",
                operator="in",
                value=["high", "critical"],
                is_global=False,
                applies_to_widgets=[1, 2]
            )
        ]
        
        # Apply filters for widget 1
        filtered_data = dashboard_filters.apply_filters(test_data, filters, 1)
        
        # Verify filtered data for widget 1
        assert len(filtered_data) == 2
        assert filtered_data[0]["id"] == 3
        assert filtered_data[1]["id"] == 4
        
        # Apply filters for widget 3 (priority filter doesn't apply)
        filtered_data = dashboard_filters.apply_filters(test_data, filters, 3)
        
        # Verify filtered data for widget 3 (only global filter applies)
        assert len(filtered_data) == 3
        assert filtered_data[0]["id"] == 1
        assert filtered_data[1]["id"] == 3
        assert filtered_data[2]["id"] == 4

    def test_filter_api_endpoints(self, test_client, test_filter_setup, db_session):
        """Test filter API endpoints."""
        dashboard = test_filter_setup["dashboard"]
        user = test_filter_setup["user"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Test get filters endpoint
        response = test_client.get(
            f"/api/v1/dashboards/{dashboard.id}/filters",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        filters_data = response.json()
        assert len(filters_data) == 3
        
        # Test create filter endpoint
        new_filter_data = {
            "dashboard_id": dashboard.id,
            "name": "API Test Filter",
            "field": "name",
            "operator": "contains",
            "value": "api",
            "is_global": True
        }
        
        response = test_client.post(
            "/api/v1/dashboards/filters",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=new_filter_data
        )
        
        assert response.status_code == 201
        created_filter = response.json()
        assert created_filter["name"] == "API Test Filter"
        assert created_filter["field"] == "name"
        
        # Test update filter endpoint
        filter_id = created_filter["id"]
        update_data = {
            "name": "Updated API Filter",
            "value": "updated"
        }
        
        response = test_client.put(
            f"/api/v1/dashboards/filters/{filter_id}",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=update_data
        )
        
        assert response.status_code == 200
        updated_filter = response.json()
        assert updated_filter["name"] == "Updated API Filter"
        assert updated_filter["value"] == "updated"
        
        # Test delete filter endpoint
        response = test_client.delete(
            f"/api/v1/dashboards/filters/{filter_id}",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        
        # Verify filter was deleted
        response = test_client.get(
            f"/api/v1/dashboards/{dashboard.id}/filters",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        filters_data = response.json()
        assert len(filters_data) == 3  # Back to original 3 filters
        assert not any(f["id"] == filter_id for f in filters_data)
        
        # Test apply filters endpoint
        test_data = {
            "items": [
                {"id": 1, "status": "active", "priority": "low", "name": "Item 1"},
                {"id": 2, "status": "inactive", "priority": "medium", "name": "Item 2"},
                {"id": 3, "status": "active", "priority": "high", "name": "Item 3"}
            ]
        }
        
        response = test_client.post(
            f"/api/v1/dashboards/{dashboard.id}/apply-filters",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=test_data
        )
        
        assert response.status_code == 200
        result = response.json()
        assert "filtered_data" in result
        
        # Only active status items should remain due to the Status Filter
        filtered_items = result["filtered_data"]["items"]
        assert len(filtered_items) == 2
        assert filtered_items[0]["id"] == 1
        assert filtered_items[1]["id"] == 3
