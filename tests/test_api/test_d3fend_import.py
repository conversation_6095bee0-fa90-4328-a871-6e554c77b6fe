"""Tests for D3FEND data import functionality."""
import pytest
from sqlalchemy.orm import Session
from sqlalchemy import text
import json
import os
from datetime import datetime
import logging
from pathlib import Path
from rdflib import Graph, OWL, RDF, RDFS, URIRef, Literal
from fastapi.testclient import TestClient

from api.models.d3fend import (
    D3FENDClass,
    D3FENDProperty,
    D3FENDCountermeasure,
    d3fend_class_hierarchy
)
from api.utils.d3fend_import import import_d3fend_ontology

# Configure test logging
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_dir / 'test_d3fend_import.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@pytest.fixture(scope="function")
def test_data_dir(tmp_path):
    """Create and return a temporary directory for test data."""
    data_dir = tmp_path / "data" / "d3fend"
    data_dir.mkdir(parents=True, exist_ok=True)
    return data_dir

def create_test_dataset(path: Path, num_concepts: int = 3) -> int:
    """Create a test D3FEND dataset with specified number of concepts."""
    g = Graph()
    test_concepts = [
        f"TestConcept{i}" for i in range(num_concepts)
    ]

    # Add test concepts
    for concept in test_concepts:
        uri = f"http://d3fend.mitre.org/ontologies/d3fend.owl#{concept}"
        subject = URIRef(uri)
        g.add((subject, RDF.type, OWL.Class))
        g.add((subject, RDFS.label, Literal(concept)))
        g.add((subject, RDFS.comment, Literal(f"Test definition for {concept}")))

        # Add relationships between concepts for testing
        if concept != test_concepts[0]:  # Link to the first concept
            g.add((subject, RDFS.seeAlso, URIRef(f"http://d3fend.mitre.org/ontologies/d3fend.owl#{test_concepts[0]}")))

    # Create directory and save file
    path.parent.mkdir(parents=True, exist_ok=True)
    g.serialize(str(path), format="xml")
    logger.info(f"Created test dataset at {path} with {num_concepts} concepts")

    return num_concepts

def verify_database_state(db_session: Session) -> dict:
    """Verify the current state of the database."""
    try:
        classes = db_session.query(D3FENDClass).count()
        properties = db_session.query(D3FENDProperty).count()
        hierarchies = db_session.execute(
            text("SELECT COUNT(*) FROM d3fend_class_hierarchy")
        ).scalar()
        countermeasures = db_session.query(D3FENDCountermeasure).count()

        return {
            "classes": classes,
            "properties": properties,
            "hierarchies": hierarchies,
            "countermeasures": countermeasures
        }
    except Exception as e:
        logger.error(f"Failed to verify database state: {e}", exc_info=True)
        raise

def test_owl_endpoint_success(client: TestClient, db_session: Session, test_data_dir):
    """Test successful OWL file upload and import via HTTP endpoint."""
    logger.info("Starting OWL endpoint success test")

    # Create test dataset
    dataset_path = test_data_dir / "test_d3fend.owl"
    num_concepts = create_test_dataset(dataset_path, num_concepts=3)

    try:
        # Test file upload and import
        with open(dataset_path, "rb") as f:
            response = client.post(
                "/api/v1/d3fend/import/owl",
                files={"file": ("test_d3fend.owl", f, "application/xml")}
            )

        assert response.status_code == 200
        result = response.json()
        assert result["status"] == "success"

        # Verify database state
        state = verify_database_state(db_session)
        logger.info(f"Database state after import: {state}")

        assert state["classes"] == num_concepts, f"Expected {num_concepts} classes"
        assert state["hierarchies"] >= num_concepts - 1, "Expected hierarchical relationships"

    except Exception as e:
        logger.error(f"Test failed: {e}", exc_info=True)
        db_session.rollback()
        raise

def test_owl_endpoint_invalid_file(client: TestClient, test_data_dir):
    """Test OWL endpoint with invalid file."""
    logger.info("Starting invalid file test")

    # Create invalid file
    invalid_path = test_data_dir / "invalid.owl"
    invalid_path.write_text("Invalid OWL content")

    # Test upload
    with open(invalid_path, "rb") as f:
        response = client.post(
            "/api/v1/d3fend/import/owl",
            files={"file": ("invalid.owl", f, "application/xml")}
        )

    assert response.status_code == 500
    result = response.json()
    assert "error" in result["detail"].lower()

def test_owl_endpoint_wrong_extension(client: TestClient, test_data_dir):
    """Test OWL endpoint with wrong file extension."""
    logger.info("Starting wrong extension test")

    # Create file with wrong extension
    wrong_ext_path = test_data_dir / "test.txt"
    wrong_ext_path.write_text("Some content")

    # Test upload
    with open(wrong_ext_path, "rb") as f:
        response = client.post(
            "/api/v1/d3fend/import/owl",
            files={"file": ("test.txt", f, "text/plain")}
        )

    assert response.status_code == 400
    result = response.json()
    assert "owl" in result["detail"].lower()

def test_retrieve_imported_data(client: TestClient, db_session: Session, test_data_dir):
    """Test retrieving imported D3FEND data through API endpoints."""
    logger.info("Starting data retrieval test")

    # First import some test data
    dataset_path = test_data_dir / "test_d3fend.owl"
    create_test_dataset(dataset_path)

    with open(dataset_path, "rb") as f:
        client.post(
            "/api/v1/d3fend/import/owl",
            files={"file": ("test_d3fend.owl", f, "application/xml")}
        )

    # Test classes endpoint
    response = client.get("/api/v1/d3fend/classes")
    assert response.status_code == 200
    classes = response.json()
    assert len(classes["items"]) > 0
    assert "total" in classes
    assert classes["total"] > 0

    # Test properties endpoint
    response = client.get("/api/v1/d3fend/properties")
    assert response.status_code == 200
    properties = response.json()
    assert "items" in properties
    assert "total" in properties

    # Test countermeasures endpoint
    response = client.get("/api/v1/d3fend/countermeasures")
    assert response.status_code == 200
    countermeasures = response.json()
    assert "items" in countermeasures
    assert "total" in countermeasures


def test_basic_owl_import(client: TestClient, db_session: Session, test_data_dir):
    """Test basic OWL file import functionality."""
    logger.info("Starting basic OWL import test")

    # Create test dataset
    dataset_path = test_data_dir / "test_d3fend.owl"
    num_concepts = create_test_dataset(dataset_path, num_concepts=3)

    try:
        # Import test data
        import_d3fend_ontology(db_session, str(dataset_path))
        db_session.commit()

        # Verify through database queries
        state = verify_database_state(db_session)
        logger.info(f"Database state after import: {state}")

        assert state["classes"] == num_concepts, f"Expected {num_concepts} classes"
        assert state["hierarchies"] >= num_concepts -1, "Expected relationships between concepts"


    except Exception as e:
        logger.error(f"Test failed: {e}", exc_info=True)
        db_session.rollback()
        raise

def test_version_update(client: TestClient, db_session: Session, test_data_dir):
    """Test version update handling."""
    logger.info("Starting version update test")

    # Create and import initial dataset
    dataset_path = test_data_dir / "test_d3fend.owl"
    create_test_dataset(dataset_path, num_concepts=3)

    # First import
    import_d3fend_ontology(db_session, str(dataset_path))
    db_session.commit()

    initial_state = verify_database_state(db_session)
    logger.info(f"Initial state: {initial_state}")

    # Create and import updated dataset
    create_test_dataset(dataset_path, num_concepts=5)
    import_d3fend_ontology(db_session, str(dataset_path))
    db_session.commit()

    final_state = verify_database_state(db_session)
    logger.info(f"Final state: {final_state}")

    # Verify version handling 
    assert final_state["classes"] == 5, "Expected updated number of classes"
    assert final_state["hierarchies"] >= 4, "Expected updated number of relationships"


def test_error_handling(client: TestClient, db_session: Session, test_data_dir):
    """Test error handling during import."""
    logger.info("Starting error handling test")

    # Create invalid OWL file
    invalid_path = test_data_dir / "invalid.owl"
    invalid_path.write_text("Invalid OWL content")

    # Test import with invalid file
    with pytest.raises(Exception):
        import_d3fend_ontology(db_session, str(invalid_path))

    # Verify no data was imported
    state = verify_database_state(db_session)
    assert state["classes"] == 0, "No classes should be created on error"
    assert state["properties"] == 0, "No properties should be created on error"
    assert state["hierarchies"] == 0, "No hierarchies should be created on error"
    assert state["countermeasures"] == 0, "No countermeasures should be created on error"