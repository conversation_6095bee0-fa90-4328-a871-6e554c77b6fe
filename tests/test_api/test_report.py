"""
Tests for the report API endpoints.

This module contains tests for the report API endpoints, including
creating, retrieving, updating, and deleting report schedules, as well as
generating reports and retrieving report generations.
"""
import os
import pytest
from datetime import datetime, timedelta
from fastapi.testclient import TestClient

from api.main import app
from api.models.database.report import (
    ReportFormat,
    ReportFrequency,
    ReportGeneration,
    ReportSchedule,
    ReportStatus,
    ReportType,
)


client = TestClient(app)


@pytest.fixture
def admin_token():
    """Get an admin token for testing."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": "admin", "password": "adminpassword"}
    )
    return response.json()["access_token"]


@pytest.fixture
def report_schedule(db_session):
    """Create a report schedule for testing."""
    schedule = ReportSchedule(
        name="Daily Test Execution Summary",
        description="Daily summary of test executions",
        report_type=ReportType.EXECUTION_SUMMARY,
        format=ReportFormat.HTML,
        frequency=ReportFrequency.DAILY,
        time_of_day="08:00",
        recipients=[{"email": "<EMAIL>", "name": "Test User"}],
        subject="Daily Test Execution Summary",
        body_template="<html><body><h1>Daily Test Execution Summary</h1><p>Please find the attached report.</p></body></html>",
        filter_params={"time_range": "day"},
        include_charts=True,
        include_raw_data=False,
        created_by="1",
        next_run_at=datetime.utcnow() + timedelta(days=1),
        is_active=True
    )
    db_session.add(schedule)
    db_session.commit()
    db_session.refresh(schedule)
    
    yield schedule
    
    # Clean up
    db_session.query(ReportGeneration).filter(
        ReportGeneration.report_schedule_id == schedule.id
    ).delete()
    
    db_session.delete(schedule)
    db_session.commit()


@pytest.fixture
def report_generation(db_session, report_schedule):
    """Create a report generation for testing."""
    generation = ReportGeneration(
        report_schedule_id=report_schedule.id,
        report_type=report_schedule.report_type,
        format=report_schedule.format,
        recipients=report_schedule.recipients,
        subject=report_schedule.subject,
        body_template=report_schedule.body_template,
        filter_params=report_schedule.filter_params,
        include_charts=report_schedule.include_charts,
        include_raw_data=report_schedule.include_raw_data,
        send_email=True,
        created_by="1",
        status=ReportStatus.COMPLETED,
        completed_at=datetime.utcnow(),
        file_path="/tmp/test_report.html"
    )
    db_session.add(generation)
    db_session.commit()
    db_session.refresh(generation)
    
    # Create a test file
    with open(generation.file_path, "w") as f:
        f.write("<html><body><h1>Test Report</h1></body></html>")
    
    yield generation
    
    # Clean up
    if os.path.exists(generation.file_path):
        os.remove(generation.file_path)
    
    db_session.delete(generation)
    db_session.commit()


def test_create_report_schedule(client, db_session, admin_token):
    """Test creating a report schedule."""
    response = client.post(
        "/api/v1/reports/schedules",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "name": "Weekly Test Case Summary",
            "description": "Weekly summary of test cases",
            "report_type": "test_case_summary",
            "format": "pdf",
            "frequency": "weekly",
            "day_of_week": 1,
            "time_of_day": "09:00",
            "recipients": [{"email": "<EMAIL>", "name": "Test User"}],
            "subject": "Weekly Test Case Summary",
            "body_template": "<html><body><h1>Weekly Test Case Summary</h1><p>Please find the attached report.</p></body></html>",
            "filter_params": {"time_range": "week"},
            "include_charts": True,
            "include_raw_data": False
        }
    )
    
    assert response.status_code == 201
    data = response.json()
    
    # Verify response data
    assert data["name"] == "Weekly Test Case Summary"
    assert data["report_type"] == "test_case_summary"
    assert data["format"] == "pdf"
    assert data["frequency"] == "weekly"
    assert data["day_of_week"] == 1
    assert data["is_active"] is True
    
    # Verify database state
    db_schedule = db_session.query(ReportSchedule).filter(
        ReportSchedule.id == data["id"]
    ).first()
    assert db_schedule is not None
    assert db_schedule.name == "Weekly Test Case Summary"
    assert db_schedule.report_type == "test_case_summary"
    
    # Clean up
    db_session.delete(db_schedule)
    db_session.commit()


def test_get_report_schedules(client, db_session, report_schedule, admin_token):
    """Test getting report schedules."""
    response = client.get(
        "/api/v1/reports/schedules",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    
    # Find our test schedule
    schedule_data = next((s for s in data if s["id"] == report_schedule.id), None)
    assert schedule_data is not None
    assert schedule_data["name"] == report_schedule.name
    assert schedule_data["report_type"] == report_schedule.report_type


def test_get_report_schedule(client, db_session, report_schedule, admin_token):
    """Test getting a specific report schedule."""
    response = client.get(
        f"/api/v1/reports/schedules/{report_schedule.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == report_schedule.id
    assert data["name"] == report_schedule.name
    assert data["report_type"] == report_schedule.report_type
    assert data["format"] == report_schedule.format
    assert data["frequency"] == report_schedule.frequency


def test_update_report_schedule(client, db_session, report_schedule, admin_token):
    """Test updating a report schedule."""
    response = client.put(
        f"/api/v1/reports/schedules/{report_schedule.id}",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "name": "Updated Test Execution Summary",
            "frequency": "weekly",
            "day_of_week": 2,
            "is_active": False
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "Updated Test Execution Summary"
    assert data["frequency"] == "weekly"
    assert data["day_of_week"] == 2
    assert data["is_active"] is False
    
    # Verify database state
    db_session.refresh(report_schedule)
    assert report_schedule.name == "Updated Test Execution Summary"
    assert report_schedule.frequency == "weekly"
    assert report_schedule.day_of_week == 2
    assert report_schedule.is_active is False


def test_delete_report_schedule(client, db_session, report_schedule, admin_token):
    """Test deleting a report schedule."""
    response = client.delete(
        f"/api/v1/reports/schedules/{report_schedule.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 204
    
    # Verify database state (soft delete)
    db_session.refresh(report_schedule)
    assert report_schedule.deleted_at is not None


def test_restore_report_schedule(client, db_session, report_schedule, admin_token):
    """Test restoring a deleted report schedule."""
    # First delete the schedule
    report_schedule.deleted_at = datetime.utcnow()
    db_session.add(report_schedule)
    db_session.commit()
    
    response = client.post(
        f"/api/v1/reports/schedules/{report_schedule.id}/restore",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == report_schedule.id
    
    # Verify database state
    db_session.refresh(report_schedule)
    assert report_schedule.deleted_at is None


def test_execute_report_schedule(client, db_session, report_schedule, admin_token):
    """Test executing a report schedule."""
    # Make sure schedule is active
    report_schedule.is_active = True
    db_session.add(report_schedule)
    db_session.commit()
    
    response = client.post(
        f"/api/v1/reports/schedules/{report_schedule.id}/execute",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["report_schedule_id"] == report_schedule.id
    assert data["report_type"] == report_schedule.report_type
    assert data["format"] == report_schedule.format
    
    # Verify database state
    generation = db_session.query(ReportGeneration).filter(
        ReportGeneration.id == data["id"]
    ).first()
    assert generation is not None
    
    # Clean up
    db_session.delete(generation)
    db_session.commit()


def test_get_report_generations(client, db_session, report_generation, admin_token):
    """Test getting report generations."""
    response = client.get(
        "/api/v1/reports/generations",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    
    # Find our test generation
    generation_data = next((g for g in data if g["id"] == report_generation.id), None)
    assert generation_data is not None
    assert generation_data["report_schedule_id"] == report_generation.report_schedule_id
    assert generation_data["report_type"] == report_generation.report_type
    assert generation_data["status"] == report_generation.status


def test_get_report_generation(client, db_session, report_generation, admin_token):
    """Test getting a specific report generation."""
    response = client.get(
        f"/api/v1/reports/generations/{report_generation.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == report_generation.id
    assert data["report_schedule_id"] == report_generation.report_schedule_id
    assert data["report_type"] == report_generation.report_type
    assert data["status"] == report_generation.status
    assert data["file_path"] == report_generation.file_path


def test_generate_report(client, db_session, admin_token):
    """Test generating a report."""
    response = client.post(
        "/api/v1/reports/generate",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "report_type": "dashboard",
            "format": "html",
            "filter_params": {"time_range": "day"},
            "include_charts": True,
            "include_raw_data": False,
            "send_email": False
        }
    )
    
    assert response.status_code == 201
    data = response.json()
    assert data["report_type"] == "dashboard"
    assert data["format"] == "html"
    assert data["status"] in ["pending", "generating", "completed"]
    
    # Verify database state
    generation = db_session.query(ReportGeneration).filter(
        ReportGeneration.id == data["id"]
    ).first()
    assert generation is not None
    
    # Clean up
    if generation.file_path and os.path.exists(generation.file_path):
        os.remove(generation.file_path)
    
    db_session.delete(generation)
    db_session.commit()


def test_download_report(client, db_session, report_generation, admin_token):
    """Test downloading a report."""
    response = client.get(
        f"/api/v1/reports/generations/{report_generation.id}/download",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    assert response.headers["content-type"] == "application/octet-stream"
    assert response.content == b"<html><body><h1>Test Report</h1></body></html>"


def test_get_schedule_generations(client, db_session, report_schedule, report_generation, admin_token):
    """Test getting generations for a schedule."""
    response = client.get(
        f"/api/v1/reports/schedules/{report_schedule.id}/generations",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    
    # Find our test generation
    generation_data = next((g for g in data if g["id"] == report_generation.id), None)
    assert generation_data is not None
    assert generation_data["report_schedule_id"] == report_generation.report_schedule_id
    assert generation_data["report_type"] == report_generation.report_type


def test_get_due_report_schedules(client, db_session, admin_token):
    """Test getting schedules that are due for execution."""
    # Create a due schedule
    due_schedule = ReportSchedule(
        name="Due Report Schedule",
        description="Schedule that is due for execution",
        report_type=ReportType.DASHBOARD,
        format=ReportFormat.HTML,
        frequency=ReportFrequency.DAILY,
        time_of_day="00:00",
        recipients=[{"email": "<EMAIL>", "name": "Test User"}],
        subject="Due Report",
        body_template="<html><body><h1>Due Report</h1></body></html>",
        filter_params={"time_range": "day"},
        include_charts=True,
        include_raw_data=False,
        created_by="1",
        next_run_at=datetime.utcnow() - timedelta(hours=1),
        is_active=True
    )
    db_session.add(due_schedule)
    db_session.commit()
    
    response = client.get(
        "/api/v1/reports/due",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    
    # Find our due schedule
    due_schedule_data = next((s for s in data if s["id"] == due_schedule.id), None)
    assert due_schedule_data is not None
    assert due_schedule_data["name"] == due_schedule.name
    
    # Clean up
    db_session.delete(due_schedule)
    db_session.commit()


def test_filter_report_schedules(client, db_session, report_schedule, admin_token):
    """Test filtering report schedules."""
    # Create another schedule with different report type
    other_schedule = ReportSchedule(
        name="Dashboard Report",
        description="Dashboard report schedule",
        report_type=ReportType.DASHBOARD,
        format=ReportFormat.PDF,
        frequency=ReportFrequency.WEEKLY,
        day_of_week=1,
        time_of_day="10:00",
        recipients=[{"email": "<EMAIL>", "name": "Test User"}],
        subject="Dashboard Report",
        body_template="<html><body><h1>Dashboard Report</h1></body></html>",
        filter_params={"time_range": "week"},
        include_charts=True,
        include_raw_data=False,
        created_by="1",
        next_run_at=datetime.utcnow() + timedelta(days=7),
        is_active=True
    )
    db_session.add(other_schedule)
    db_session.commit()
    
    # Test filtering by report type
    response = client.get(
        "/api/v1/reports/schedules?report_type=dashboard",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    assert all(s["report_type"] == "dashboard" for s in data)
    
    # Test search
    response = client.get(
        "/api/v1/reports/schedules?search=Dashboard",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    assert any("Dashboard" in s["name"] for s in data)
    
    # Clean up
    db_session.delete(other_schedule)
    db_session.commit()
