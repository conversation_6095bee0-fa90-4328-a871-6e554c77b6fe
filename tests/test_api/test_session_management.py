"""Tests for session management functionality."""

import pytest
from datetime import datetime, timedelta
from fastapi.testclient import Test<PERSON><PERSON>
from sqlalchemy.orm import Session

from api.models.session import UserSession
from api.models.user import User
from api.auth.session_utils import (
    hash_token,
    get_client_info,
    create_session,
    get_session_by_token,
    revoke_session,
    revoke_all_sessions,
    cleanup_expired_sessions
)

def test_hash_token():
    """Test token hashing."""
    token = "test_token"
    hashed = hash_token(token)
    assert hashed == hash_token(token)  # Same token should hash to same value
    assert hashed != hash_token("different_token")  # Different tokens should hash differently

def test_get_client_info(test_client: TestClient):
    """Test getting client info from request."""
    response = test_client.get("/test")
    ip_address, user_agent = get_client_info(response.request)
    assert ip_address == "testclient"
    assert "testclient" in user_agent.lower()

def test_create_session(db: Session, test_user: User):
    """Test session creation."""
    token = "test_token"
    request = type("Request", (), {
        "headers": {"User-Agent": "test_agent"},
        "client": type("Client", (), {"host": "127.0.0.1"})
    })()
    
    session = create_session(db, test_user, token, request)
    
    assert session.user_id == test_user.id
    assert session.token_hash == hash_token(token)
    assert session.ip_address == "127.0.0.1"
    assert session.user_agent == "test_agent"
    assert session.is_active
    assert session.expires_at > datetime.utcnow()

def test_get_session_by_token(db: Session, test_user: User):
    """Test getting session by token."""
    token = "test_token"
    request = type("Request", (), {
        "headers": {"User-Agent": "test_agent"},
        "client": type("Client", (), {"host": "127.0.0.1"})
    })()
    
    session = create_session(db, test_user, token, request)
    
    # Test valid token
    retrieved = get_session_by_token(db, token)
    assert retrieved.id == session.id
    
    # Test invalid token
    assert get_session_by_token(db, "invalid_token") is None
    
    # Test expired session
    session.expires_at = datetime.utcnow() - timedelta(hours=1)
    db.commit()
    assert get_session_by_token(db, token) is None

def test_revoke_session(db: Session, test_user: User):
    """Test session revocation."""
    token = "test_token"
    request = type("Request", (), {
        "headers": {"User-Agent": "test_agent"},
        "client": type("Client", (), {"host": "127.0.0.1"})
    })()
    
    session = create_session(db, test_user, token, request)
    
    # Test successful revocation
    assert revoke_session(db, session.id, test_user.id)
    db.refresh(session)
    assert not session.is_active
    assert session.revoked_at is not None
    
    # Test revoking non-existent session
    assert not revoke_session(db, "invalid_id", test_user.id)

def test_revoke_all_sessions(db: Session, test_user: User):
    """Test revoking all sessions."""
    request = type("Request", (), {
        "headers": {"User-Agent": "test_agent"},
        "client": type("Client", (), {"host": "127.0.0.1"})
    })()
    
    # Create multiple sessions
    session1 = create_session(db, test_user, "token1", request)
    session2 = create_session(db, test_user, "token2", request)
    
    # Test revoking all sessions
    revoked_count = revoke_all_sessions(db, test_user.id)
    assert revoked_count == 2
    
    db.refresh(session1)
    db.refresh(session2)
    assert not session1.is_active
    assert not session2.is_active
    
    # Test revoking all sessions except one
    session3 = create_session(db, test_user, "token3", request)
    revoked_count = revoke_all_sessions(db, test_user.id, exclude_session_id=session3.id)
    assert revoked_count == 0
    
    db.refresh(session3)
    assert session3.is_active

def test_cleanup_expired_sessions(db: Session, test_user: User):
    """Test cleaning up expired sessions."""
    request = type("Request", (), {
        "headers": {"User-Agent": "test_agent"},
        "client": type("Client", (), {"host": "127.0.0.1"})
    })()
    
    # Create active and expired sessions
    active_session = create_session(db, test_user, "active_token", request)
    expired_session = create_session(db, test_user, "expired_token", request)
    expired_session.expires_at = datetime.utcnow() - timedelta(hours=1)
    db.commit()
    
    # Test cleanup
    cleaned_count = cleanup_expired_sessions(db)
    assert cleaned_count == 1
    
    db.refresh(active_session)
    db.refresh(expired_session)
    assert active_session.is_active
    assert not expired_session.is_active
    assert expired_session.revoked_at is not None 