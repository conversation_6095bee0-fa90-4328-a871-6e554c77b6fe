"""Tests for MITRE Defense endpoints."""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime
import os
import json

from api.models.mitre_defense import MitreDefenseVersion, MitreControl

@pytest.fixture
def defense_version(db_session: Session):
    """Create a test MITRE Defense version."""
    version = MitreDefenseVersion(
        version="2.0.0",
        import_date=datetime.utcnow(),
        is_current=True
    )
    db_session.add(version)
    db_session.commit()
    return version

@pytest.fixture
def defense_control(db_session: Session, defense_version: MitreDefenseVersion):
    """Create a test MITRE Defense control."""
    control = MitreControl(
        external_id="M1001",
        name="Test Control",
        description="A test control for unit testing",
        version_id=defense_version.id,
        created=datetime.utcnow(),
        modified=datetime.utcnow()
    )
    db_session.add(control)
    db_session.commit()
    return control

def test_list_versions(client: TestClient, defense_version: MitreDefenseVersion):
    """Test listing MITRE Defense versions."""
    response = client.get("/api/v1/mitre/defense/versions")
    assert response.status_code == 200
    versions = response.json()
    assert len(versions) == 1
    assert versions[0]["version"] == "2.0.0"
    assert versions[0]["is_current"] is True

def test_list_versions_empty(client: TestClient):
    """Test listing versions when none exist."""
    response = client.get("/api/v1/mitre/defense/versions")
    assert response.status_code == 200
    assert response.json() == []

def test_list_controls_empty(client: TestClient):
    """Test listing controls when none exist."""
    response = client.get("/api/v1/mitre/defense/controls")
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 0
    assert len(data["items"]) == 0

def test_list_controls(client: TestClient, defense_control: MitreControl):
    """Test listing controls with data."""
    response = client.get("/api/v1/mitre/defense/controls")
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 1
    assert len(data["items"]) == 1
    assert data["items"][0]["external_id"] == "M1001"

def test_list_controls_pagination(client: TestClient, db_session: Session, defense_version: MitreDefenseVersion):
    """Test control listing pagination."""
    # Create multiple controls
    for i in range(15):
        control = MitreControl(
            external_id=f"M{1001 + i}",
            name=f"Test Control {i}",
            description=f"Test control {i} for pagination",
            version_id=defense_version.id,
            created=datetime.utcnow(),
            modified=datetime.utcnow()
        )
        db_session.add(control)
    db_session.commit()

    # Test first page
    response = client.get("/api/v1/mitre/defense/controls?page=1&size=10")
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 15
    assert len(data["items"]) == 10
    assert data["page"] == 1

    # Test second page
    response = client.get("/api/v1/mitre/defense/controls?page=2&size=10")
    assert response.status_code == 200
    data = response.json()
    assert len(data["items"]) == 5
    assert data["page"] == 2

def test_get_control(client: TestClient, defense_control: MitreControl):
    """Test getting a specific control."""
    response = client.get(f"/api/v1/mitre/defense/controls/{defense_control.external_id}")
    assert response.status_code == 200
    control = response.json()
    assert control["external_id"] == "M1001"
    assert control["name"] == "Test Control"

def test_get_control_not_found(client: TestClient):
    """Test getting a non-existent control."""
    response = client.get("/api/v1/mitre/defense/controls/M9999")
    assert response.status_code == 404
    assert response.json()["detail"] == "Control not found"

def test_import_defense_data(client: TestClient, tmp_path):
    """Test importing defense data."""
    # Create test data file
    data = {
        "controls": [
            {
                "id": "M2001",
                "name": "Test Import Control",
                "description": "A control for testing import"
            }
        ]
    }
    test_file = tmp_path / "test-defense-controls.json"
    test_file.write_text(json.dumps(data))

    # Test successful import
    response = client.post("/api/v1/mitre/defense/import")
    assert response.status_code == 200
    result = response.json()
    assert result["status"] == "success"
    assert "Successfully imported" in result["detail"]

    # Verify imported data
    response = client.get("/api/v1/mitre/defense/controls")
    assert response.status_code == 200
    controls = response.json()
    assert controls["total"] > 0

def test_import_duplicate_version(client: TestClient):
    """Test importing duplicate version without force flag."""
    # First import
    response = client.post("/api/v1/mitre/defense/import", params={"version": "1.0.0"})
    assert response.status_code == 200

    # Try importing same version
    response = client.post("/api/v1/mitre/defense/import", params={"version": "1.0.0"})
    assert response.status_code == 200
    result = response.json()
    assert result["status"] == "error"
    assert "Version already exists" in result["message"]

    # Import with force flag
    response = client.post(
        "/api/v1/mitre/defense/import",
        params={"version": "1.0.0", "force": True}
    )
    assert response.status_code == 200
    result = response.json()
    assert result["status"] == "success"

def test_import_missing_file(client: TestClient, monkeypatch):
    """Test import behavior when data file is missing."""
    # Temporarily rename/move the defense controls file
    data_file = os.path.join("data", "mitre", "defense-controls.json")
    if os.path.exists(data_file):
        os.rename(data_file, data_file + ".bak")

    try:
        response = client.post("/api/v1/mitre/defense/import")
        assert response.status_code == 404
        assert "Defense controls data file not found" in response.json()["detail"]
    finally:
        # Restore the file
        if os.path.exists(data_file + ".bak"):
            os.rename(data_file + ".bak", data_file)