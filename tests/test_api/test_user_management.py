"""Test suite for user management API endpoints."""

import pytest
from datetime import datetime, timedelta, timezone
from sqlalchemy.orm import Session
from api.models.user import User, UserRole
from api.models.user_preferences import UserPreference
from api.auth.utils import create_user

@pytest.fixture
def test_user_data():
    """Test user data fixture."""
    return {
        "username": "newuser",
        "email": "<EMAIL>",
        "password": "SecurePass123!",
        "role": UserRole.VIEWER,
        "full_name": "New User",
        "bio": "Test user bio"
    }

def test_create_user(client, test_user_data):
    """Test user creation with valid data."""
    response = client.post("/api/v1/users/", json=test_user_data)
    assert response.status_code == 201
    data = response.json()
    assert data["username"] == test_user_data["username"]
    assert data["email"] == test_user_data["email"]
    assert data["role"] == UserRole.VIEWER
    assert "password" not in data

def test_create_user_duplicate_email(client, test_user_data, test_user):
    """Test user creation with duplicate email."""
    test_user_data["email"] = test_user.email
    response = client.post("/api/v1/users/", json=test_user_data)
    assert response.status_code == 400
    assert "Email already exists" in response.json()["detail"]

def test_user_role_access(client, test_user_token, test_admin_token):
    """Test role-based access control."""
    # Test user access to admin endpoint
    headers = {"Authorization": f"Bearer {test_user_token}"}
    response = client.post("/api/v1/users/", headers=headers)
    assert response.status_code == 403

    # Test admin access
    headers = {"Authorization": f"Bearer {test_admin_token}"}
    response = client.post("/api/v1/users/", headers=headers)
    assert response.status_code == 200

def test_account_locking(client, test_user):
    """Test account locking after failed login attempts."""
    # Attempt multiple failed logins
    for _ in range(5):
        response = client.post(
            "/api/v1/auth/token/",
            data={"username": test_user.username, "password": "wrong_password"}
        )
        assert response.status_code == 401

    # Verify account is locked
    response = client.post(
        "/api/v1/auth/token/",
        data={"username": test_user.username, "password": "testpass123"}
    )
    assert response.status_code == 401
    assert "Account locked" in response.json()["detail"]

def test_password_reset_flow(client, test_user, db_session):
    """Test password reset request and confirmation."""
    # Request password reset
    response = client.post(
        "/api/v1/users/password-reset-request",
        json={"email": test_user.email}
    )
    assert response.status_code == 200

    # Get token from database
    db_session.refresh(test_user)
    assert test_user.password_reset_token is not None
    assert test_user.password_reset_expires > datetime.now(timezone.utc)

    # Reset password
    new_password = "NewSecurePass123!"
    response = client.post(
        "/api/v1/users/reset-password",
        json={
            "token": test_user.password_reset_token,
            "new_password": new_password
        }
    )
    assert response.status_code == 200

    # Verify new password works
    response = client.post(
        "/api/v1/auth/token/",
        data={"username": test_user.username, "password": new_password}
    )
    assert response.status_code == 200

def test_unlock_account(client, test_user, test_admin_token, db_session):
    """Test account unlocking by admin."""
    # Lock account
    test_user.account_locked = True
    test_user.failed_login_attempts = 5
    db_session.commit()

    # Try unlock without admin token
    response = client.post(
        "/api/v1/users/unlock-account",
        json={"email": test_user.email}
    )
    assert response.status_code == 401

    # Unlock with admin token
    headers = {"Authorization": f"Bearer {test_admin_token}"}
    response = client.post(
        "/api/v1/users/unlock-account",
        json={"email": test_user.email},
        headers=headers
    )
    assert response.status_code == 200

    # Verify account is unlocked
    db_session.refresh(test_user)
    assert not test_user.account_locked
    assert test_user.failed_login_attempts == 0

def test_user_preferences(client, test_user_token, test_user, db_session):
    """Test user preferences management."""
    headers = {"Authorization": f"Bearer {test_user_token}"}

    # Update preferences
    prefs = {
        "theme": "dark",
        "language": "es",
        "timezone": "America/New_York",
        "email_notifications": False
    }
    response = client.put(
        "/api/v1/users/me/preferences",
        json=prefs,
        headers=headers
    )
    assert response.status_code == 200

    # Verify preferences were updated
    db_session.refresh(test_user)
    assert test_user.preferences.theme == prefs["theme"]
    assert test_user.preferences.language == prefs["language"]
    assert test_user.preferences.timezone == prefs["timezone"]
    assert test_user.preferences.email_notifications == prefs["email_notifications"]

@pytest.fixture
def test_user(client, test_user_data, db_session):
    """Create a test user with default preferences."""
    response = client.post("/api/v1/users/", json=test_user_data)
    assert response.status_code == 201
    user_data = response.json()
    user = db_session.query(User).filter_by(id=user_data['id']).first()
    return user

@pytest.fixture
def test_user_token(client, test_user):
    response = client.post("/api/v1/auth/token/", data={"username": test_user.username, "password": "SecurePass123!"})
    assert response.status_code == 200
    return response.json()["access_token"]


@pytest.fixture
def test_admin_token(client, db_session):
    admin, _ = create_user(
        db=db_session,
        username="admin",
        email="<EMAIL>",
        password="AdminPass123!",
        role=UserRole.ADMIN
    )
    response = client.post("/api/v1/auth/token/", data={"username": admin.username, "password": "AdminPass123!"})
    assert response.status_code == 200
    return response.json()["access_token"]