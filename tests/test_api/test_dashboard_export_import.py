"""Tests for dashboard export and import functionality."""
import pytest
import json
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.models.database.dashboard import Dashboard
from api.models.database.dashboard_widget import DashboardWidget
from api.models.user import User, UserRole
from api.services import dashboard_export


@pytest.fixture
def test_dashboard(db_session):
    """Create a test dashboard with widgets."""
    # Create a test user
    user = User(
        username="exportuser",
        email="<EMAIL>",
        role=UserRole.ANALYST
    )
    user.set_password("password123")
    db_session.add(user)
    db_session.commit()

    # Create a dashboard
    dashboard = Dashboard(
        name="Export Test Dashboard",
        description="Dashboard for export/import testing",
        layout={"type": "grid", "columns": 12},
        is_public=False,
        created_by_id=user.id
    )
    db_session.add(dashboard)
    db_session.commit()

    # Create widgets
    widgets = [
        DashboardWidget(
            dashboard_id=dashboard.id,
            widget_type="chart",
            title="Chart Widget",
            config={"chart_type": "bar", "data_source": "test_data"},
            position={"x": 0, "y": 0, "w": 6, "h": 4}
        ),
        DashboardWidget(
            dashboard_id=dashboard.id,
            widget_type="metric",
            title="Metric Widget",
            config={"metric_name": "total_tests", "display_type": "number"},
            position={"x": 6, "y": 0, "w": 3, "h": 2}
        ),
        DashboardWidget(
            dashboard_id=dashboard.id,
            widget_type="table",
            title="Table Widget",
            config={"columns": ["name", "value"], "data_source": "test_table"},
            position={"x": 0, "y": 4, "w": 12, "h": 6}
        )
    ]
    
    for widget in widgets:
        db_session.add(widget)
    
    db_session.commit()
    db_session.refresh(dashboard)
    
    return {"dashboard": dashboard, "user": user, "widgets": widgets}


class TestDashboardExportImport:
    """Test dashboard export and import functionality."""

    def test_export_dashboard_to_json(self, db_session, test_dashboard):
        """Test exporting a dashboard to JSON."""
        dashboard = test_dashboard["dashboard"]
        user = test_dashboard["user"]
        
        # Export dashboard to JSON
        export_data = dashboard_export.export_dashboard_to_json(
            db_session, dashboard.id, user.id
        )
        
        # Verify export data
        assert export_data is not None
        assert "version" in export_data
        assert "exported_at" in export_data
        assert "exported_by" in export_data
        assert "dashboard" in export_data
        assert "widgets" in export_data
        
        # Verify dashboard data
        assert export_data["dashboard"]["name"] == dashboard.name
        assert export_data["dashboard"]["description"] == dashboard.description
        assert export_data["dashboard"]["layout"] == dashboard.layout
        assert export_data["dashboard"]["is_public"] == dashboard.is_public
        
        # Verify widgets data
        assert len(export_data["widgets"]) == 3
        
        # Verify each widget
        for i, widget in enumerate(test_dashboard["widgets"]):
            exported_widget = next(
                (w for w in export_data["widgets"] if w["title"] == widget.title),
                None
            )
            assert exported_widget is not None
            assert exported_widget["widget_type"] == widget.widget_type
            assert exported_widget["config"] == widget.config
            assert exported_widget["position"] == widget.position

    def test_export_dashboard_to_csv(self, db_session, test_dashboard):
        """Test exporting a dashboard to CSV."""
        dashboard = test_dashboard["dashboard"]
        user = test_dashboard["user"]
        
        # Export dashboard to CSV
        csv_data = dashboard_export.export_dashboard_to_csv(
            db_session, dashboard.id, user.id
        )
        
        # Verify CSV data
        assert csv_data is not None
        assert isinstance(csv_data, str)
        
        # Basic checks for CSV content
        assert "Dashboard Information" in csv_data
        assert dashboard.name in csv_data
        assert dashboard.description in csv_data
        assert "Widgets" in csv_data
        
        # Check for widget titles
        for widget in test_dashboard["widgets"]:
            assert widget.title in csv_data

    def test_export_dashboard_to_pdf(self, db_session, test_dashboard):
        """Test exporting a dashboard to PDF."""
        dashboard = test_dashboard["dashboard"]
        user = test_dashboard["user"]
        
        # Export dashboard to PDF
        pdf_data = dashboard_export.export_dashboard_to_pdf(
            db_session, dashboard.id, user.id
        )
        
        # Verify PDF data
        assert pdf_data is not None
        assert isinstance(pdf_data, bytes)
        assert len(pdf_data) > 0
        assert pdf_data.startswith(b'%PDF')  # PDF signature

    def test_import_dashboard_from_json(self, db_session, test_dashboard):
        """Test importing a dashboard from JSON."""
        dashboard = test_dashboard["dashboard"]
        user = test_dashboard["user"]
        
        # Export dashboard to JSON
        export_data = dashboard_export.export_dashboard_to_json(
            db_session, dashboard.id, user.id
        )
        
        # Import dashboard from JSON
        success, imported_dashboard = dashboard_export.import_dashboard_from_json(
            db_session, export_data, user.id, "Imported Dashboard"
        )
        
        # Verify import success
        assert success is True
        assert isinstance(imported_dashboard, Dashboard)
        
        # Verify imported dashboard
        assert imported_dashboard.id != dashboard.id  # New dashboard
        assert imported_dashboard.name == "Imported Dashboard"
        assert imported_dashboard.description == dashboard.description
        assert imported_dashboard.layout == dashboard.layout
        assert imported_dashboard.is_public == dashboard.is_public
        assert imported_dashboard.created_by_id == user.id
        
        # Get widgets for imported dashboard
        imported_widgets = db_session.query(DashboardWidget).filter(
            DashboardWidget.dashboard_id == imported_dashboard.id
        ).all()
        
        # Verify widgets
        assert len(imported_widgets) == 3
        
        # Verify each widget was imported correctly
        for original_widget in test_dashboard["widgets"]:
            imported_widget = next(
                (w for w in imported_widgets if w.title == original_widget.title),
                None
            )
            assert imported_widget is not None
            assert imported_widget.widget_type == original_widget.widget_type
            assert imported_widget.config == original_widget.config
            assert imported_widget.position == original_widget.position

    def test_import_dashboard_invalid_data(self, db_session, test_dashboard):
        """Test importing a dashboard with invalid data."""
        user = test_dashboard["user"]
        
        # Test with missing version
        invalid_data = {
            "dashboard": {"name": "Invalid Dashboard"},
            "widgets": []
        }
        
        success, error = dashboard_export.import_dashboard_from_json(
            db_session, invalid_data, user.id
        )
        
        assert success is False
        assert "missing version" in error
        
        # Test with missing dashboard
        invalid_data = {
            "version": "1.0",
            "widgets": []
        }
        
        success, error = dashboard_export.import_dashboard_from_json(
            db_session, invalid_data, user.id
        )
        
        assert success is False
        assert "missing dashboard" in error
        
        # Test with missing widgets
        invalid_data = {
            "version": "1.0",
            "dashboard": {"name": "Invalid Dashboard"}
        }
        
        success, error = dashboard_export.import_dashboard_from_json(
            db_session, invalid_data, user.id
        )
        
        assert success is False
        assert "missing widgets" in error

    def test_clone_dashboard(self, db_session, test_dashboard):
        """Test cloning a dashboard."""
        dashboard = test_dashboard["dashboard"]
        user = test_dashboard["user"]
        
        # Clone dashboard
        success, cloned_dashboard = dashboard_export.clone_dashboard(
            db_session, dashboard.id, user.id, "Cloned Dashboard"
        )
        
        # Verify clone success
        assert success is True
        assert isinstance(cloned_dashboard, Dashboard)
        
        # Verify cloned dashboard
        assert cloned_dashboard.id != dashboard.id  # New dashboard
        assert cloned_dashboard.name == "Cloned Dashboard"
        assert cloned_dashboard.description == dashboard.description
        assert cloned_dashboard.layout == dashboard.layout
        assert cloned_dashboard.is_public == dashboard.is_public
        assert cloned_dashboard.created_by_id == user.id
        
        # Get widgets for cloned dashboard
        cloned_widgets = db_session.query(DashboardWidget).filter(
            DashboardWidget.dashboard_id == cloned_dashboard.id
        ).all()
        
        # Verify widgets
        assert len(cloned_widgets) == 3
        
        # Verify each widget was cloned correctly
        for original_widget in test_dashboard["widgets"]:
            cloned_widget = next(
                (w for w in cloned_widgets if w.title == original_widget.title),
                None
            )
            assert cloned_widget is not None
            assert cloned_widget.widget_type == original_widget.widget_type
            assert cloned_widget.config == original_widget.config
            assert cloned_widget.position == original_widget.position

    def test_clone_dashboard_not_found(self, db_session, test_dashboard):
        """Test cloning a non-existent dashboard."""
        user = test_dashboard["user"]
        
        # Try to clone non-existent dashboard
        success, error = dashboard_export.clone_dashboard(
            db_session, 9999, user.id
        )
        
        # Verify clone failure
        assert success is False
        assert "not found" in error

    def test_export_dashboard_api_endpoint(self, test_client, test_dashboard, db_session):
        """Test the dashboard export API endpoint."""
        dashboard = test_dashboard["dashboard"]
        user = test_dashboard["user"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Test JSON export
        response = test_client.get(
            f"/api/v1/dashboards/{dashboard.id}/export?format=json",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        export_data = response.json()
        
        assert "version" in export_data
        assert "dashboard" in export_data
        assert export_data["dashboard"]["name"] == dashboard.name
        
        # Test CSV export
        response = test_client.get(
            f"/api/v1/dashboards/{dashboard.id}/export?format=csv",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/csv; charset=utf-8"
        assert dashboard.name in response.text
        
        # Test PDF export
        response = test_client.get(
            f"/api/v1/dashboards/{dashboard.id}/export?format=pdf",
            headers={"Authorization": f"Bearer {access_token}"}
        )
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/pdf"
        assert response.content.startswith(b'%PDF')

    def test_import_dashboard_api_endpoint(self, test_client, test_dashboard, db_session):
        """Test the dashboard import API endpoint."""
        dashboard = test_dashboard["dashboard"]
        user = test_dashboard["user"]
        
        # Create authentication token
        from api.auth.router import create_access_token
        access_token = create_access_token(data={"sub": user.username})
        
        # Export dashboard to JSON
        export_data = dashboard_export.export_dashboard_to_json(
            db_session, dashboard.id, user.id
        )
        
        # Import dashboard via API
        response = test_client.post(
            "/api/v1/dashboards/import?new_name=API+Imported+Dashboard",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=export_data
        )
        
        assert response.status_code == 201
        imported_data = response.json()
        
        assert imported_data["name"] == "API Imported Dashboard"
        assert imported_data["description"] == dashboard.description
        assert imported_data["created_by"]["id"] == user.id
        
        # Verify widgets were imported
        assert "widgets" in imported_data
        assert len(imported_data["widgets"]) == 3
        
        # Test import with invalid data
        invalid_data = {
            "dashboard": {"name": "Invalid Dashboard"},
            "widgets": []
        }
        
        response = test_client.post(
            "/api/v1/dashboards/import",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            },
            json=invalid_data
        )
        
        assert response.status_code == 400
        assert "missing version" in response.json()["detail"]
