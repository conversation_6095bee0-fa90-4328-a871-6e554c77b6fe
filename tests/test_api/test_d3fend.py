"""Tests for D3FEND ontology integration."""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime
import json
import os
from pathlib import Path
import urllib.parse
import threading
import time

from api.models.d3fend import D3FENDVersion, D3FENDConcept, d3fend_relationships
from api.utils.d3fend_import import import_d3fend_ontology

# Ensure test logs directory exists
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)

@pytest.fixture
def d3fend_version(db_session: Session):
    """Create a test D3FEND version."""
    version = D3FENDVersion(
        version="2025.02.25",
        import_date=datetime.utcnow(),
        is_current=True
    )
    db_session.add(version)
    db_session.commit()
    return version

@pytest.fixture
def d3fend_concept(db_session: Session, d3fend_version: D3FENDVersion):
    """Create a test D3FEND concept."""
    concept = D3FENDConcept(
        uri="http://d3fend.mitre.org/ontologies/d3fend.owl#TestConcept",
        name="TestConcept",
        type="owl:Class",
        definition="A test concept for unit testing",
        version_id=d3fend_version.id,
        external_references=json.dumps({"@id": "http://example.com/ref"}),
        created=datetime.utcnow()
    )
    db_session.add(concept)
    db_session.commit()
    return concept

@pytest.fixture
def related_concept(db_session: Session, d3fend_version: D3FENDVersion, d3fend_concept: D3FENDConcept):
    """Create a related test concept."""
    concept = D3FENDConcept(
        uri="http://d3fend.mitre.org/ontologies/d3fend.owl#RelatedConcept",
        name="RelatedConcept",
        type="owl:Class",
        definition="A related test concept",
        version_id=d3fend_version.id,
        created=datetime.utcnow()
    )
    db_session.add(concept)
    db_session.flush()

    # Create relationship
    db_session.execute(
        d3fend_relationships.insert().values(
            source_id=d3fend_concept.id,
            target_id=concept.id,
            relationship_type="rdfs:subClassOf"
        )
    )
    db_session.commit()
    return concept

def test_list_versions(client: TestClient, d3fend_version: D3FENDVersion):
    """Test listing D3FEND versions."""
    response = client.get("/api/v1/d3fend/versions")
    assert response.status_code == 200
    versions = response.json()
    assert len(versions) == 1
    assert versions[0]["version"] == "2025.02.25"
    assert versions[0]["is_current"] is True

def test_list_concepts_empty(client: TestClient):
    """Test listing concepts when none exist."""
    response = client.get("/api/v1/d3fend/concepts")
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 0
    assert len(data["items"]) == 0

def test_list_concepts(client: TestClient, d3fend_concept: D3FENDConcept):
    """Test listing concepts with data."""
    response = client.get("/api/v1/d3fend/concepts")
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 1
    assert len(data["items"]) == 1
    assert data["items"][0]["name"] == "TestConcept"

def test_get_concept(client: TestClient, d3fend_concept: D3FENDConcept):
    """Test getting a specific concept."""
    encoded_uri = urllib.parse.quote(d3fend_concept.uri, safe='')
    response = client.get(f"/api/v1/d3fend/concepts/{encoded_uri}")
    assert response.status_code == 200
    concept = response.json()
    assert concept["name"] == "TestConcept"
    assert concept["type"] == "owl:Class"

def test_get_concept_not_found(client: TestClient):
    """Test getting a non-existent concept."""
    encoded_uri = urllib.parse.quote("http://d3fend.mitre.org/ontologies/d3fend.owl#NonExistent", safe='')
    response = client.get(f"/api/v1/d3fend/concepts/{encoded_uri}")
    assert response.status_code == 404
    assert response.json()["detail"] == "Concept not found"

def test_get_concept_relationships(
    client: TestClient,
    d3fend_concept: D3FENDConcept,
    related_concept: D3FENDConcept
):
    """Test getting concept relationships."""
    encoded_uri = urllib.parse.quote(d3fend_concept.uri, safe='')
    response = client.get(f"/api/v1/d3fend/relationships/{encoded_uri}")
    assert response.status_code == 200
    relationships = response.json()
    assert len(relationships) == 1
    assert relationships[0]["source_uri"] == d3fend_concept.uri
    assert relationships[0]["target_uri"] == related_concept.uri
    assert relationships[0]["target_name"] == "RelatedConcept"
    assert relationships[0]["relationship_type"] == "rdfs:subClassOf"

def test_get_concept_relationships_filtered(
    client: TestClient,
    d3fend_concept: D3FENDConcept,
    related_concept: D3FENDConcept
):
    """Test getting filtered concept relationships."""
    encoded_uri = urllib.parse.quote(d3fend_concept.uri, safe='')
    response = client.get(
        f"/api/v1/d3fend/relationships/{encoded_uri}",
        params={"relationship_type": "rdfs:subClassOf"}
    )
    assert response.status_code == 200
    relationships = response.json()
    assert len(relationships) == 1
    assert relationships[0]["relationship_type"] == "rdfs:subClassOf"

    response = client.get(
        f"/api/v1/d3fend/relationships/{encoded_uri}",
        params={"relationship_type": "nonexistent"}
    )
    assert response.status_code == 200
    relationships = response.json()
    assert len(relationships) == 0

def test_import_d3fend_ontology(db_session: Session, tmp_path):
    """Test D3FEND ontology import functionality."""
    # Create a minimal test ontology
    test_ontology = {
        "@context": {},
        "@graph": [
            {
                "@id": "http://d3fend.mitre.org/ontologies/d3fend.owl#TestConcept",
                "@type": "owl:Class",
                "d3f:definition": "Test definition",
                "rdfs:isDefinedBy": {"@id": "http://example.com/ref"}
            }
        ]
    }

    json_path = tmp_path / "test_d3fend.json"
    with open(json_path, "w") as f:
        json.dump(test_ontology, f)

    # Import the test ontology
    import_d3fend_ontology(db_session, str(json_path))

    # Verify import results
    versions = db_session.query(D3FENDVersion).all()
    assert len(versions) == 1
    assert versions[0].is_current is True

    concepts = db_session.query(D3FENDConcept).all()
    assert len(concepts) == 1
    assert concepts[0].name == "TestConcept"
    assert concepts[0].type == "owl:Class"
    assert concepts[0].definition == "Test definition"