"""Tests for the health check endpoint."""
import pytest
from datetime import datetime
from fastapi.testclient import TestClient
from api.main import app

client = TestClient(app)

def test_health_check():
    """Test health check endpoint returns expected response."""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()

    # Check expected fields are present
    assert "status" in data
    assert "timestamp" in data

    # Verify status is 'healthy'
    assert data["status"] == "healthy"

    # Verify timestamp is valid ISO format
    try:
        timestamp = datetime.fromisoformat(data["timestamp"])
        assert isinstance(timestamp, datetime)
    except ValueError as e:
        pytest.fail(f"Invalid timestamp format: {e}")

def test_root_endpoint():
    """Test root endpoint returns expected response."""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()

    # Check expected fields
    assert "message" in data
    assert "health_check" in data

    # Verify content
    assert data["message"] == "API is running"
    assert data["health_check"] == "/health"