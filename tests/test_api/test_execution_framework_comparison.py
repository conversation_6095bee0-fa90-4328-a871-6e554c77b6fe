"""
Tests for the comparison reports API endpoints in the execution framework.

This module contains tests for the comparison reports API endpoints, including
campaign comparison and time period comparison reports.
"""
import io
import json
import pytest
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from api.main import app
from api.models.database.execution_framework.models import (
    Campaign,
    ExecutionLog,
    ExecutionStatus,
    MitreTechnique,
    TestCase,
)
from api.models.database.execution_framework.analytics_models import (
    ExecutionMetric,
    ExecutionTrend,
    MitreCoverageMetric,
)


client = TestClient(app)


@pytest.fixture
def admin_token():
    """Get an admin token for testing."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": "admin", "password": "adminpassword"}
    )
    return response.json()["access_token"]


@pytest.fixture
def mock_campaign():
    """Create a mock campaign for testing."""
    return Campaign(
        id=1,
        name="Test Campaign 1",
        description="Test campaign for comparison",
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by="test_user",
        test_cases=[MagicMock(test_case_id=1), MagicMock(test_case_id=2)]
    )


@pytest.fixture
def mock_campaign2():
    """Create a second mock campaign for testing."""
    return Campaign(
        id=2,
        name="Test Campaign 2",
        description="Second test campaign for comparison",
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by="test_user",
        test_cases=[MagicMock(test_case_id=2), MagicMock(test_case_id=3)]
    )


@pytest.fixture
def mock_test_case():
    """Create a mock test case for testing."""
    return TestCase(
        id=1,
        name="Test Case 1",
        description="Test case for comparison",
        command="echo 'test'",
        executor_type="bash",
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by="test_user",
        mitre_techniques=["T1059.001", "T1059.004"]
    )


@pytest.fixture
def mock_test_case2():
    """Create a second mock test case for testing."""
    return TestCase(
        id=2,
        name="Test Case 2",
        description="Second test case for comparison",
        command="Get-Process",
        executor_type="powershell",
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by="test_user",
        mitre_techniques=["T1057", "T1059.001"]
    )


@pytest.fixture
def mock_test_case3():
    """Create a third mock test case for testing."""
    return TestCase(
        id=3,
        name="Test Case 3",
        description="Third test case for comparison",
        command="netstat -ano",
        executor_type="cmd",
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by="test_user",
        mitre_techniques=["T1049", "T1057"]
    )


@pytest.fixture
def mock_mitre_technique():
    """Create a mock MITRE technique for testing."""
    return MitreTechnique(
        id=1,
        technique_id="T1059.001",
        name="PowerShell",
        description="Adversaries may abuse PowerShell commands and scripts for execution.",
        tactic="execution",
        platforms=["windows"],
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


@pytest.fixture
def mock_mitre_technique2():
    """Create a second mock MITRE technique for testing."""
    return MitreTechnique(
        id=2,
        technique_id="T1057",
        name="Process Discovery",
        description="Adversaries may attempt to get information about running processes on a system.",
        tactic="discovery",
        platforms=["windows", "macos", "linux"],
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


@pytest.fixture
def mock_mitre_technique3():
    """Create a third mock MITRE technique for testing."""
    return MitreTechnique(
        id=3,
        technique_id="T1049",
        name="System Network Connections Discovery",
        description="Adversaries may attempt to get a listing of network connections to or from the compromised system.",
        tactic="discovery",
        platforms=["windows", "macos", "linux"],
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


@pytest.fixture
def mock_mitre_technique4():
    """Create a fourth mock MITRE technique for testing."""
    return MitreTechnique(
        id=4,
        technique_id="T1059.004",
        name="Unix Shell",
        description="Adversaries may abuse Unix shell commands and scripts for execution.",
        tactic="execution",
        platforms=["macos", "linux"],
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


@pytest.fixture
def mock_execution_log():
    """Create a mock execution log for testing."""
    return ExecutionLog(
        id=1,
        test_case_id=1,
        campaign_id=1,
        status=ExecutionStatus.COMPLETED.value,
        start_time=datetime.now() - timedelta(days=1),
        end_time=datetime.now() - timedelta(days=1) + timedelta(minutes=5),
        output="Test output",
        created_at=datetime.now() - timedelta(days=1),
        updated_at=datetime.now() - timedelta(days=1)
    )


@pytest.fixture
def mock_execution_log2():
    """Create a second mock execution log for testing."""
    return ExecutionLog(
        id=2,
        test_case_id=2,
        campaign_id=1,
        status=ExecutionStatus.COMPLETED.value,
        start_time=datetime.now() - timedelta(days=1),
        end_time=datetime.now() - timedelta(days=1) + timedelta(minutes=3),
        output="Test output 2",
        created_at=datetime.now() - timedelta(days=1),
        updated_at=datetime.now() - timedelta(days=1)
    )


@pytest.fixture
def mock_execution_log3():
    """Create a third mock execution log for testing."""
    return ExecutionLog(
        id=3,
        test_case_id=2,
        campaign_id=2,
        status=ExecutionStatus.COMPLETED.value,
        start_time=datetime.now() - timedelta(hours=5),
        end_time=datetime.now() - timedelta(hours=5) + timedelta(minutes=4),
        output="Test output 3",
        created_at=datetime.now() - timedelta(hours=5),
        updated_at=datetime.now() - timedelta(hours=5)
    )


@pytest.fixture
def mock_execution_log4():
    """Create a fourth mock execution log for testing."""
    return ExecutionLog(
        id=4,
        test_case_id=3,
        campaign_id=2,
        status=ExecutionStatus.COMPLETED.value,
        start_time=datetime.now() - timedelta(hours=4),
        end_time=datetime.now() - timedelta(hours=4) + timedelta(minutes=6),
        output="Test output 4",
        created_at=datetime.now() - timedelta(hours=4),
        updated_at=datetime.now() - timedelta(hours=4)
    )


@pytest.fixture
def mock_execution_metric():
    """Create a mock execution metric for testing."""
    return ExecutionMetric(
        id=1,
        test_case_id=1,
        campaign_id=1,
        execution_count=10,
        success_count=8,
        failure_count=2,
        success_rate=80.0,
        execution_time=300.0,
        created_at=datetime.now() - timedelta(days=1),
        updated_at=datetime.now() - timedelta(days=1)
    )


@pytest.fixture
def mock_execution_metric2():
    """Create a second mock execution metric for testing."""
    return ExecutionMetric(
        id=2,
        test_case_id=2,
        campaign_id=1,
        execution_count=15,
        success_count=14,
        failure_count=1,
        success_rate=93.3,
        execution_time=180.0,
        created_at=datetime.now() - timedelta(days=1),
        updated_at=datetime.now() - timedelta(days=1)
    )


@pytest.fixture
def mock_execution_metric3():
    """Create a third mock execution metric for testing."""
    return ExecutionMetric(
        id=3,
        test_case_id=2,
        campaign_id=2,
        execution_count=12,
        success_count=10,
        failure_count=2,
        success_rate=83.3,
        execution_time=200.0,
        created_at=datetime.now() - timedelta(hours=5),
        updated_at=datetime.now() - timedelta(hours=5)
    )


@pytest.fixture
def mock_execution_metric4():
    """Create a fourth mock execution metric for testing."""
    return ExecutionMetric(
        id=4,
        test_case_id=3,
        campaign_id=2,
        execution_count=8,
        success_count=7,
        failure_count=1,
        success_rate=87.5,
        execution_time=250.0,
        created_at=datetime.now() - timedelta(hours=4),
        updated_at=datetime.now() - timedelta(hours=4)
    )


@pytest.fixture
def mock_db_session(
    mock_campaign,
    mock_campaign2,
    mock_test_case,
    mock_test_case2,
    mock_test_case3,
    mock_mitre_technique,
    mock_mitre_technique2,
    mock_mitre_technique3,
    mock_mitre_technique4,
    mock_execution_log,
    mock_execution_log2,
    mock_execution_log3,
    mock_execution_log4,
    mock_execution_metric,
    mock_execution_metric2,
    mock_execution_metric3,
    mock_execution_metric4,
):
    """Create a mock database session with test data."""
    db_session = MagicMock()
    
    # Configure query mocks
    campaign_query = MagicMock()
    campaign_query.filter.return_value.first.side_effect = lambda: mock_campaign if db_session.query.call_args[0][0] == Campaign and campaign_query.filter.call_args[0][0].right.value == 1 else mock_campaign2
    
    test_case_query = MagicMock()
    test_case_query.filter.return_value.all.return_value = [mock_test_case, mock_test_case2, mock_test_case3]
    
    mitre_technique_query = MagicMock()
    mitre_technique_query.filter.return_value.all.return_value = [
        mock_mitre_technique, 
        mock_mitre_technique2, 
        mock_mitre_technique3, 
        mock_mitre_technique4
    ]
    
    execution_log_query = MagicMock()
    execution_log_query.filter.return_value.all.return_value = [
        mock_execution_log, 
        mock_execution_log2, 
        mock_execution_log3, 
        mock_execution_log4
    ]
    
    execution_metric_query = MagicMock()
    execution_metric_query.filter.return_value.all.return_value = [
        mock_execution_metric, 
        mock_execution_metric2, 
        mock_execution_metric3, 
        mock_execution_metric4
    ]
    
    # Configure db_session.query to return appropriate mock query
    def query_side_effect(model):
        if model == Campaign:
            return campaign_query
        elif model == TestCase:
            return test_case_query
        elif model == MitreTechnique:
            return mitre_technique_query
        elif model == ExecutionLog:
            return execution_log_query
        elif model == ExecutionMetric:
            return execution_metric_query
        return MagicMock()
    
    db_session.query.side_effect = query_side_effect
    
    return db_session


def test_generate_campaign_comparison_report_xlsx(admin_token, mock_db_session):
    """Test generating a campaign comparison report in XLSX format."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}), \
         patch('api.services.execution_framework.comparison._generate_comparison_xlsx_report') as mock_generate_xlsx:
        
        # Mock the report generation function
        mock_output = io.BytesIO(b"test xlsx data")
        mock_filename = "test_comparison_report.xlsx"
        mock_generate_xlsx.return_value = (mock_output, mock_filename)
        
        # Make the API request
        response = client.get(
            "/api/v1/execution-framework/comparisons/campaigns",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "campaign_id1": 1,
                "campaign_id2": 2,
                "format": "xlsx"
            }
        )
        
        # Check the response
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        assert response.headers["content-disposition"] == f"attachment; filename={mock_filename}"
        assert response.content == b"test xlsx data"
        
        # Verify the service function was called with correct parameters
        mock_generate_xlsx.assert_called_once()


def test_generate_campaign_comparison_report_json(admin_token, mock_db_session):
    """Test generating a campaign comparison report in JSON format."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}), \
         patch('api.services.execution_framework.comparison._generate_comparison_json_report') as mock_generate_json:
        
        # Mock the report generation function
        mock_output = io.BytesIO(json.dumps({"test": "data"}).encode('utf-8'))
        mock_filename = "test_comparison_report.json"
        mock_generate_json.return_value = (mock_output, mock_filename)
        
        # Make the API request
        response = client.get(
            "/api/v1/execution-framework/comparisons/campaigns",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "campaign_id1": 1,
                "campaign_id2": 2,
                "format": "json"
            }
        )
        
        # Check the response
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"
        assert response.headers["content-disposition"] == f"attachment; filename={mock_filename}"
        assert json.loads(response.content) == {"test": "data"}
        
        # Verify the service function was called with correct parameters
        mock_generate_json.assert_called_once()


def test_generate_campaign_comparison_report_invalid_format(admin_token, mock_db_session):
    """Test generating a campaign comparison report with an invalid format."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}):
        
        # Make the API request with an invalid format
        response = client.get(
            "/api/v1/execution-framework/comparisons/campaigns",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "campaign_id1": 1,
                "campaign_id2": 2,
                "format": "pdf"  # Invalid format for campaign comparison
            }
        )
        
        # Check the response
        assert response.status_code == 400
        assert "Unsupported report format" in response.json()["detail"]


def test_generate_campaign_comparison_report_campaign_not_found(admin_token, mock_db_session):
    """Test generating a campaign comparison report with a non-existent campaign."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}), \
         patch('api.services.execution_framework.comparison.generate_campaign_comparison_report') as mock_generate:
        
        # Mock the report generation function to raise an HTTPException
        mock_generate.side_effect = HTTPException(
            status_code=404,
            detail="Campaign with ID 999 not found"
        )
        
        # Make the API request with a non-existent campaign
        response = client.get(
            "/api/v1/execution-framework/comparisons/campaigns",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "campaign_id1": 1,
                "campaign_id2": 999,  # Non-existent campaign
                "format": "xlsx"
            }
        )
        
        # Check the response
        assert response.status_code == 404
        assert "Campaign with ID 999 not found" in response.json()["detail"]


def test_generate_time_period_comparison_report_xlsx(admin_token, mock_db_session):
    """Test generating a time period comparison report in XLSX format."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}), \
         patch('api.services.execution_framework.comparison._generate_comparison_xlsx_report') as mock_generate_xlsx:
        
        # Mock the report generation function
        mock_output = io.BytesIO(b"test xlsx data")
        mock_filename = "test_time_period_comparison_report.xlsx"
        mock_generate_xlsx.return_value = (mock_output, mock_filename)
        
        # Make the API request
        start_date1 = (datetime.now() - timedelta(days=30)).isoformat()
        end_date1 = (datetime.now() - timedelta(days=15)).isoformat()
        start_date2 = (datetime.now() - timedelta(days=14)).isoformat()
        end_date2 = datetime.now().isoformat()
        
        response = client.get(
            "/api/v1/execution-framework/comparisons/time-periods",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "start_date1": start_date1,
                "end_date1": end_date1,
                "start_date2": start_date2,
                "end_date2": end_date2,
                "campaign_id": 1,
                "format": "xlsx"
            }
        )
        
        # Check the response
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        assert response.headers["content-disposition"] == f"attachment; filename={mock_filename}"
        assert response.content == b"test xlsx data"
        
        # Verify the service function was called with correct parameters
        mock_generate_xlsx.assert_called_once()


def test_generate_time_period_comparison_report_json(admin_token, mock_db_session):
    """Test generating a time period comparison report in JSON format."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}), \
         patch('api.services.execution_framework.comparison._generate_comparison_json_report') as mock_generate_json:
        
        # Mock the report generation function
        mock_output = io.BytesIO(json.dumps({"test": "data"}).encode('utf-8'))
        mock_filename = "test_time_period_comparison_report.json"
        mock_generate_json.return_value = (mock_output, mock_filename)
        
        # Make the API request
        start_date1 = (datetime.now() - timedelta(days=30)).isoformat()
        end_date1 = (datetime.now() - timedelta(days=15)).isoformat()
        start_date2 = (datetime.now() - timedelta(days=14)).isoformat()
        end_date2 = datetime.now().isoformat()
        
        response = client.get(
            "/api/v1/execution-framework/comparisons/time-periods",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "start_date1": start_date1,
                "end_date1": end_date1,
                "start_date2": start_date2,
                "end_date2": end_date2,
                "test_case_id": 1,
                "format": "json"
            }
        )
        
        # Check the response
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"
        assert response.headers["content-disposition"] == f"attachment; filename={mock_filename}"
        assert json.loads(response.content) == {"test": "data"}
        
        # Verify the service function was called with correct parameters
        mock_generate_json.assert_called_once()


def test_generate_time_period_comparison_report_invalid_format(admin_token, mock_db_session):
    """Test generating a time period comparison report with an invalid format."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}):
        
        # Make the API request with an invalid format
        start_date1 = (datetime.now() - timedelta(days=30)).isoformat()
        end_date1 = (datetime.now() - timedelta(days=15)).isoformat()
        start_date2 = (datetime.now() - timedelta(days=14)).isoformat()
        end_date2 = datetime.now().isoformat()
        
        response = client.get(
            "/api/v1/execution-framework/comparisons/time-periods",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "start_date1": start_date1,
                "end_date1": end_date1,
                "start_date2": start_date2,
                "end_date2": end_date2,
                "campaign_id": 1,
                "format": "csv"  # Invalid format for time period comparison
            }
        )
        
        # Check the response
        assert response.status_code == 400
        assert "Unsupported report format" in response.json()["detail"]


def test_generate_time_period_comparison_report_both_filters(admin_token, mock_db_session):
    """Test generating a time period comparison report with both test_case_id and campaign_id."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}):
        
        # Make the API request with both filters
        start_date1 = (datetime.now() - timedelta(days=30)).isoformat()
        end_date1 = (datetime.now() - timedelta(days=15)).isoformat()
        start_date2 = (datetime.now() - timedelta(days=14)).isoformat()
        end_date2 = datetime.now().isoformat()
        
        response = client.get(
            "/api/v1/execution-framework/comparisons/time-periods",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "start_date1": start_date1,
                "end_date1": end_date1,
                "start_date2": start_date2,
                "end_date2": end_date2,
                "test_case_id": 1,
                "campaign_id": 1,  # Both filters provided
                "format": "xlsx"
            }
        )
        
        # Check the response
        assert response.status_code == 400
        assert "Please provide either test_case_id or campaign_id, not both" in response.json()["detail"]


def test_generate_time_period_comparison_report_server_error(admin_token, mock_db_session):
    """Test handling server errors when generating a time period comparison report."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}), \
         patch('api.services.execution_framework.comparison.generate_time_period_comparison_report') as mock_generate:
        
        # Mock the report generation function to raise an exception
        mock_generate.side_effect = Exception("Unexpected server error")
        
        # Make the API request
        start_date1 = (datetime.now() - timedelta(days=30)).isoformat()
        end_date1 = (datetime.now() - timedelta(days=15)).isoformat()
        start_date2 = (datetime.now() - timedelta(days=14)).isoformat()
        end_date2 = datetime.now().isoformat()
        
        response = client.get(
            "/api/v1/execution-framework/comparisons/time-periods",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "start_date1": start_date1,
                "end_date1": end_date1,
                "start_date2": start_date2,
                "end_date2": end_date2,
                "campaign_id": 1,
                "format": "xlsx"
            }
        )
        
        # Check the response
        assert response.status_code == 500
        assert "Failed to generate time period comparison report" in response.json()["detail"]
