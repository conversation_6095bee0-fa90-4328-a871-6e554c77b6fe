"""Tests for password management functionality."""

import pytest
from datetime import datetime, timedelta
from fastapi.testclient import Test<PERSON><PERSON>
from sqlalchemy.orm import Session
from api.models.user import User
from api.auth.utils import validate_password

def test_password_validation():
    """Test password validation rules."""
    # Test minimum length
    is_valid, message = validate_password("short")
    assert not is_valid
    assert "at least" in message
    
    # Test special character requirement
    is_valid, message = validate_password("password123")
    assert not is_valid
    assert "special character" in message
    
    # Test number requirement
    is_valid, message = validate_password("Password!")
    assert not is_valid
    assert "number" in message
    
    # Test uppercase requirement
    is_valid, message = validate_password("password123!")
    assert not is_valid
    assert "uppercase" in message
    
    # Test common password
    is_valid, message = validate_password("password123!")
    assert not is_valid
    assert "too common" in message
    
    # Test valid password
    is_valid, message = validate_password("SecurePass123!")
    assert is_valid
    assert message == "Password is valid"

def test_password_change(client: TestClient, test_user: User, db_session: Session):
    """Test password change functionality."""
    # Login to get token
    response = client.post(
        "/api/v1/auth/token/",
        data={"username": test_user.username, "password": "testpass123"}
    )
    assert response.status_code == 200
    token = response.json()["access_token"]
    
    # Change password
    response = client.post(
        "/api/v1/auth/password/change/",
        headers={"Authorization": f"Bearer {token}"},
        json={
            "current_password": "testpass123",
            "new_password": "NewSecurePass123!"
        }
    )
    assert response.status_code == 200
    
    # Verify old password no longer works
    response = client.post(
        "/api/v1/auth/token/",
        data={"username": test_user.username, "password": "testpass123"}
    )
    assert response.status_code == 401
    
    # Verify new password works
    response = client.post(
        "/api/v1/auth/token/",
        data={"username": test_user.username, "password": "NewSecurePass123!"}
    )
    assert response.status_code == 200

def test_password_reset_flow(client: TestClient, test_user: User, db_session: Session):
    """Test password reset request and confirmation."""
    # Request password reset
    response = client.post(
        "/api/v1/auth/password/reset-request/",
        json={"email": test_user.email}
    )
    assert response.status_code == 200
    
    # Get token from database
    db_session.refresh(test_user)
    assert test_user.password_reset_token is not None
    assert test_user.password_reset_expires > datetime.utcnow()
    
    # Reset password
    response = client.post(
        "/api/v1/auth/password/reset/",
        json={
            "token": test_user.password_reset_token,
            "new_password": "NewSecurePass123!"
        }
    )
    assert response.status_code == 200
    
    # Verify new password works
    response = client.post(
        "/api/v1/auth/token/",
        data={"username": test_user.username, "password": "NewSecurePass123!"}
    )
    assert response.status_code == 200

def test_password_reset_expired_token(client: TestClient, test_user: User, db_session: Session):
    """Test password reset with expired token."""
    # Set expired token
    test_user.set_password_reset_token("test_token")
    test_user.password_reset_expires = datetime.utcnow() - timedelta(hours=1)
    db_session.commit()
    
    # Try to reset password
    response = client.post(
        "/api/v1/auth/password/reset/",
        json={
            "token": "test_token",
            "new_password": "NewSecurePass123!"
        }
    )
    assert response.status_code == 400
    assert "expired" in response.json()["detail"]

def test_password_reset_invalid_token(client: TestClient, test_user: User, db_session: Session):
    """Test password reset with invalid token."""
    response = client.post(
        "/api/v1/auth/password/reset/",
        json={
            "token": "invalid_token",
            "new_password": "NewSecurePass123!"
        }
    )
    assert response.status_code == 400
    assert "Invalid" in response.json()["detail"]

def test_password_change_validation(client: TestClient, test_user: User, db_session: Session):
    """Test password change validation."""
    # Login to get token
    response = client.post(
        "/api/v1/auth/token/",
        data={"username": test_user.username, "password": "testpass123"}
    )
    assert response.status_code == 200
    token = response.json()["access_token"]
    
    # Try to change password with incorrect current password
    response = client.post(
        "/api/v1/auth/password/change/",
        headers={"Authorization": f"Bearer {token}"},
        json={
            "current_password": "wrongpassword",
            "new_password": "NewSecurePass123!"
        }
    )
    assert response.status_code == 400
    assert "Incorrect current password" in response.json()["detail"]
    
    # Try to change password with weak new password
    response = client.post(
        "/api/v1/auth/password/change/",
        headers={"Authorization": f"Bearer {token}"},
        json={
            "current_password": "testpass123",
            "new_password": "weak"
        }
    )
    assert response.status_code == 400
    assert "at least" in response.json()["detail"] 