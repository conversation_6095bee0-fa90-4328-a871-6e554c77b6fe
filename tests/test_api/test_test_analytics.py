"""
Tests for the test analytics API endpoints.

This module contains tests for the test analytics API endpoints, including
dashboard data, execution trends, and test case impact and efficiency analysis.
"""
import pytest
from datetime import datetime, timedelta
from fastapi.testclient import TestClient

from api.main import app
from api.models.database.test_case import TestCase
from api.models.database.test_execution import TestExecution
from api.models.schemas.dashboard import TimeRange


client = TestClient(app)


@pytest.fixture
def admin_token():
    """Get an admin token for testing."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": "admin", "password": "adminpassword"}
    )
    return response.json()["access_token"]


@pytest.fixture
def test_cases_with_executions(db_session):
    """Create test cases with executions for testing."""
    test_cases = []
    executions = []
    
    # Create test cases
    for i in range(5):
        test_case = TestCase(
            name=f"Test Case {i+1}",
            description=f"Description for test case {i+1}",
            type="manual",
            status="active" if i < 3 else "draft" if i == 3 else "deprecated",
            priority="high" if i < 2 else "medium" if i < 4 else "low",
            complexity="moderate",
            prerequisites="Access to the application",
            steps=[f"Step 1 for test case {i+1}", f"Step 2 for test case {i+1}"],
            expected_result=f"Expected result for test case {i+1}",
            tags=["security", "test"],
            created_by="1",
            version="1.0.0"
        )
        db_session.add(test_case)
        test_cases.append(test_case)
    
    db_session.commit()
    for test_case in test_cases:
        db_session.refresh(test_case)
    
    # Create executions
    now = datetime.utcnow()
    for i, test_case in enumerate(test_cases):
        for j in range(3):  # 3 executions per test case
            execution_date = now - timedelta(days=j*2)
            status = "passed" if (i + j) % 3 != 0 else "failed"
            duration_ms = (i + 1) * 1000 + j * 500
            
            execution = TestExecution(
                test_case_id=test_case.id,
                status=status,
                executed_by="1",
                execution_date=execution_date,
                actual_result=f"Actual result for execution {j+1} of test case {i+1}",
                notes=f"Notes for execution {j+1} of test case {i+1}",
                environment="Test",
                duration_ms=duration_ms
            )
            db_session.add(execution)
            executions.append(execution)
    
    db_session.commit()
    for execution in executions:
        db_session.refresh(execution)
    
    yield test_cases, executions
    
    # Clean up
    for execution in executions:
        db_session.delete(execution)
    
    for test_case in test_cases:
        db_session.delete(test_case)
    
    db_session.commit()


def test_get_dashboard(client, db_session, test_cases_with_executions, admin_token):
    """Test getting dashboard data."""
    response = client.post(
        "/api/v1/test-analytics/dashboard",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "time_range": "month",
            "group_by": "day"
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    
    # Verify dashboard structure
    assert "summary" in data
    assert "charts" in data
    assert "filter" in data
    assert "generated_at" in data
    
    # Verify summary data
    summary = data["summary"]
    assert "test_cases" in summary
    assert "executions" in summary
    assert "campaigns" in summary
    assert "schedules" in summary
    assert "kpis" in summary
    
    # Verify test case summary
    test_case_summary = summary["test_cases"]
    assert test_case_summary["total"] >= 5
    assert test_case_summary["active"] >= 3
    assert test_case_summary["draft"] >= 1
    assert test_case_summary["deprecated"] >= 1
    
    # Verify execution summary
    execution_summary = summary["executions"]
    assert execution_summary["total"] >= 15  # 5 test cases * 3 executions
    assert execution_summary["passed"] > 0
    assert execution_summary["failed"] > 0
    assert "pass_rate" in execution_summary
    assert "avg_duration_ms" in execution_summary
    
    # Verify charts
    charts = data["charts"]
    assert len(charts) > 0
    
    # Verify first chart
    first_chart = charts[0]
    assert "title" in first_chart
    assert "type" in first_chart
    assert "data" in first_chart
    assert len(first_chart["data"]) > 0


def test_get_execution_trend(client, db_session, test_cases_with_executions, admin_token):
    """Test getting execution trend data."""
    response = client.get(
        "/api/v1/test-analytics/execution-trend?time_range=month&group_by=day",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    
    # Verify trend data structure
    assert "time_periods" in data
    assert "execution_counts" in data
    assert "pass_rates" in data
    assert "failure_rates" in data
    assert "avg_durations" in data
    
    # Verify data arrays
    assert len(data["time_periods"]) > 0
    assert len(data["execution_counts"]) == len(data["time_periods"])
    assert len(data["pass_rates"]) == len(data["time_periods"])
    assert len(data["failure_rates"]) == len(data["time_periods"])
    assert len(data["avg_durations"]) == len(data["time_periods"])


def test_get_test_case_impact(client, db_session, test_cases_with_executions, admin_token):
    """Test getting test case impact analysis."""
    response = client.get(
        "/api/v1/test-analytics/test-case-impact?time_range=month&limit=5",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    
    # Verify impact data structure
    assert isinstance(data, list)
    assert len(data) <= 5  # Limited to 5 results
    
    if len(data) > 0:
        first_impact = data[0]
        assert "test_case_id" in first_impact
        assert "name" in first_impact
        assert "impact_score" in first_impact
        assert "failure_rate" in first_impact
        assert "execution_count" in first_impact
        assert "dependencies_count" in first_impact
        assert "dependent_test_cases_count" in first_impact


def test_get_test_case_efficiency(client, db_session, test_cases_with_executions, admin_token):
    """Test getting test case efficiency analysis."""
    response = client.get(
        "/api/v1/test-analytics/test-case-efficiency?time_range=month&limit=5",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    
    # Verify efficiency data structure
    assert isinstance(data, list)
    assert len(data) <= 5  # Limited to 5 results
    
    if len(data) > 0:
        first_efficiency = data[0]
        assert "test_case_id" in first_efficiency
        assert "name" in first_efficiency
        assert "avg_execution_time_ms" in first_efficiency
        assert "defect_detection_rate" in first_efficiency
        assert "efficiency_score" in first_efficiency


def test_dashboard_filtering(client, db_session, test_cases_with_executions, admin_token):
    """Test dashboard filtering."""
    test_cases, _ = test_cases_with_executions
    test_case_ids = [test_cases[0].id, test_cases[1].id]
    
    response = client.post(
        "/api/v1/test-analytics/dashboard",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "time_range": "month",
            "test_case_ids": test_case_ids,
            "group_by": "day"
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    
    # Verify filtered data
    summary = data["summary"]
    execution_summary = summary["executions"]
    
    # Should only include executions for the specified test cases
    assert execution_summary["total"] <= 6  # 2 test cases * 3 executions


def test_execution_trend_grouping(client, db_session, test_cases_with_executions, admin_token):
    """Test execution trend grouping."""
    # Test with day grouping
    response_day = client.get(
        "/api/v1/test-analytics/execution-trend?time_range=month&group_by=day",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response_day.status_code == 200
    data_day = response_day.json()
    
    # Test with week grouping
    response_week = client.get(
        "/api/v1/test-analytics/execution-trend?time_range=month&group_by=week",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response_week.status_code == 200
    data_week = response_week.json()
    
    # Day grouping should have more time periods than week grouping
    assert len(data_day["time_periods"]) >= len(data_week["time_periods"])


def test_custom_date_range(client, db_session, test_cases_with_executions, admin_token):
    """Test custom date range filtering."""
    now = datetime.utcnow()
    start_date = (now - timedelta(days=7)).isoformat()
    end_date = now.isoformat()
    
    response = client.post(
        "/api/v1/test-analytics/dashboard",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "time_range": "custom",
            "start_date": start_date,
            "end_date": end_date,
            "group_by": "day"
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    
    # Verify filter parameters were applied
    filter_params = data["filter"]
    assert filter_params["time_range"] == "custom"
    assert filter_params["start_date"] is not None
    assert filter_params["end_date"] is not None
