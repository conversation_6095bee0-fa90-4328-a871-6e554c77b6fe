"""Tests for MITRE ATT&CK data import utilities."""
import pytest
from datetime import datetime
from unittest.mock import MagicMock, patch
from api.utils.mitre_import import (
    extract_version_from_data,
    extract_external_id,
    get_object_value,
    import_mitre_data
)

class MockBundle:
    def __init__(self, objects):
        self.objects = objects

class MockStixObject:
    def __init__(self, obj_type, modified=None, created=None):
        self.type = obj_type
        self.modified = modified
        self.created = created

def test_extract_version_from_modified_date():
    """Test version extraction from modified date."""
    mock_date = datetime(2025, 2, 25)
    mock_matrix = MockStixObject("x-mitre-matrix", modified=mock_date)
    mock_bundle = MockBundle([mock_matrix])

    mock_attack_data = MagicMock()
    mock_attack_data.get_bundle.return_value = mock_bundle

    version = extract_version_from_data(mock_attack_data)
    assert version == "2025.02"

def test_extract_version_from_created_date():
    """Test version extraction from created date when modified is not present."""
    mock_date = datetime(2025, 2, 25)
    mock_matrix = MockStixObject("x-mitre-matrix", created=mock_date)
    mock_bundle = MockBundle([mock_matrix])

    mock_attack_data = MagicMock()
    mock_attack_data.get_bundle.return_value = mock_bundle

    version = extract_version_from_data(mock_attack_data)
    assert version == "2025.02"

def test_extract_version_no_matrix():
    """Test version extraction when no matrix object is present."""
    mock_bundle = MockBundle([])

    mock_attack_data = MagicMock()
    mock_attack_data.get_bundle.return_value = mock_bundle

    version = extract_version_from_data(mock_attack_data)
    # Should return current date in YYYY.MM format
    current = datetime.utcnow()
    assert version == current.strftime("%Y.%m")

def test_extract_version_exception_handling():
    """Test version extraction error handling."""
    mock_attack_data = MagicMock()
    mock_attack_data.get_bundle.side_effect = Exception("Test error")

    version = extract_version_from_data(mock_attack_data)
    assert version == "unknown"

def test_get_object_value():
    """Test safe getter for object values."""
    # Test dictionary access
    test_dict = {"key": "value"}
    assert get_object_value(test_dict, "key") == "value"
    assert get_object_value(test_dict, "missing", "default") == "default"

    # Test object attribute access
    test_obj = MagicMock(attribute="value")
    assert get_object_value(test_obj, "attribute") == "value"
    assert get_object_value(test_obj, "missing", "default") == "default"

    # Test error handling
    assert get_object_value(None, "key", "default") == "default"
    assert get_object_value(test_obj, None, "default") == "default"

def test_extract_external_id():
    """Test external ID extraction."""
    # Test list of references
    refs = [{"external_id": "T1234"}, {"external_id": "T5678"}]
    assert extract_external_id(refs) == "T1234"

    # Test single reference
    ref = {"external_id": "T1234"}
    assert extract_external_id(ref) == "T1234"

    # Test empty references
    assert extract_external_id([]) is None
    assert extract_external_id(None) is None

    # Test object-style references
    mock_ref = MagicMock(external_id="T1234")
    assert extract_external_id([mock_ref]) == "T1234"

def test_version_from_iso8601():
    """Test version extraction from ISO8601 date strings."""
    mock_matrix = {
        "type": "x-mitre-matrix",
        "modified": "2025-02-25T00:00:00.000Z"
    }
    mock_bundle = MockBundle([mock_matrix])
    mock_attack_data = MagicMock()
    mock_attack_data.get_bundle.return_value = mock_bundle

    version = extract_version_from_data(mock_attack_data)
    assert version == "2025.02"

def test_import_data_validation():
    """Test MITRE data validation during import."""
    with patch('api.utils.mitre_import.open') as mock_open:
        # Test invalid JSON data
        mock_open.return_value.__enter__.return_value.read.return_value = "invalid json"

        with pytest.raises(Exception) as exc_info:
            import_mitre_data(MagicMock(), version="test")
        assert "Error importing MITRE data" in str(exc_info.value)

def test_import_data_rollback():
    """Test database rollback on import error."""
    mock_db = MagicMock()
    with patch('api.utils.mitre_import.open') as mock_open:
        mock_open.side_effect = Exception("File error")

        with pytest.raises(Exception):
            import_mitre_data(mock_db)

        # Verify rollback was called
        mock_db.rollback.assert_called_once()