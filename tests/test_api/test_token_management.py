"""Tests for token management functionality."""

import pytest
from datetime import datetime, timedelta
from fastapi.testclient import Test<PERSON><PERSON>
from sqlalchemy.orm import Session
import jwt
from typing import Dict, Any

from api.models.user import User
from api.models.token import TokenBlacklist
from api.auth.token_utils import (
    create_access_token,
    create_refresh_token,
    verify_token,
    decode_token,
    refresh_access_token,
    revoke_token,
    revoke_all_tokens,
    cleanup_blacklist
)
from api.config.token_config import token_settings

# Test configuration
TEST_SECRET_KEY = "test_secret_key"
TEST_ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 15
REFRESH_TOKEN_EXPIRE_DAYS = 7

@pytest.fixture
def token_config() -> Dict[str, Any]:
    """Fixture for token configuration."""
    return {
        "secret_key": TEST_SECRET_KEY,
        "algorithm": TEST_ALGORITHM,
        "access_token_expire_minutes": ACCESS_TOKEN_EXPIRE_MINUTES,
        "refresh_token_expire_days": REFRESH_TOKEN_EXPIRE_DAYS
    }

def test_create_access_token(test_user: User, token_config: Dict[str, Any]):
    """Test access token creation."""
    token = create_access_token(
        user_id=test_user.id,
        username=test_user.username,
        **token_config
    )
    
    # Verify token structure
    assert isinstance(token, str)
    assert len(token.split(".")) == 3  # JWT has 3 parts
    
    # Verify token contents
    decoded = decode_token(token, token_config["secret_key"], token_config["algorithm"])
    assert decoded["sub"] == test_user.id
    assert decoded["username"] == test_user.username
    assert decoded["type"] == "access"
    
    # Verify expiration
    exp_timestamp = decoded["exp"]
    exp_datetime = datetime.fromtimestamp(exp_timestamp)
    expected_exp = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    assert abs((exp_datetime - expected_exp).total_seconds()) < 60  # Allow 1 minute difference

def test_create_refresh_token(test_user: User, token_config: Dict[str, Any]):
    """Test refresh token creation."""
    token = create_refresh_token(
        user_id=test_user.id,
        username=test_user.username,
        **token_config
    )
    
    # Verify token structure
    assert isinstance(token, str)
    assert len(token.split(".")) == 3
    
    # Verify token contents
    decoded = decode_token(token, token_config["secret_key"], token_config["algorithm"])
    assert decoded["sub"] == test_user.id
    assert decoded["username"] == test_user.username
    assert decoded["type"] == "refresh"
    
    # Verify expiration
    exp_timestamp = decoded["exp"]
    exp_datetime = datetime.fromtimestamp(exp_timestamp)
    expected_exp = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    assert abs((exp_datetime - expected_exp).total_seconds()) < 60

def test_verify_token(test_user: User, token_config: Dict[str, Any]):
    """Test token verification."""
    # Test valid token
    token = create_access_token(
        user_id=test_user.id,
        username=test_user.username,
        **token_config
    )
    assert verify_token(token, token_config["secret_key"], token_config["algorithm"])
    
    # Test invalid token
    assert not verify_token("invalid.token.string", token_config["secret_key"], token_config["algorithm"])
    
    # Test expired token
    expired_token = jwt.encode(
        {
            "sub": test_user.id,
            "username": test_user.username,
            "type": "access",
            "exp": datetime.utcnow() - timedelta(minutes=1)
        },
        token_config["secret_key"],
        algorithm=token_config["algorithm"]
    )
    assert not verify_token(expired_token, token_config["secret_key"], token_config["algorithm"])

def test_refresh_access_token(test_user: User, token_config: Dict[str, Any]):
    """Test access token refresh."""
    # Create initial tokens
    refresh_token = create_refresh_token(
        user_id=test_user.id,
        username=test_user.username,
        **token_config
    )
    
    # Refresh access token
    new_access_token = refresh_access_token(refresh_token, **token_config)
    
    # Verify new access token
    assert isinstance(new_access_token, str)
    decoded = decode_token(new_access_token, token_config["secret_key"], token_config["algorithm"])
    assert decoded["sub"] == test_user.id
    assert decoded["username"] == test_user.username
    assert decoded["type"] == "access"
    
    # Verify expiration
    exp_timestamp = decoded["exp"]
    exp_datetime = datetime.fromtimestamp(exp_timestamp)
    expected_exp = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    assert abs((exp_datetime - expected_exp).total_seconds()) < 60
    
    # Test invalid refresh token
    with pytest.raises(ValueError):
        refresh_access_token("invalid.token", **token_config)

def test_revoke_token(db: Session, test_user: User, token_config: Dict[str, Any]):
    """Test token revocation."""
    # Create and revoke token
    token = create_access_token(
        user_id=test_user.id,
        username=test_user.username,
        **token_config
    )
    
    # Revoke token
    assert revoke_token(db, token, test_user.id)
    
    # Verify token is revoked
    assert not verify_token(token, token_config["secret_key"], token_config["algorithm"])
    
    # Test revoking non-existent token
    assert not revoke_token(db, "invalid.token", test_user.id)

def test_token_blacklist(db: Session, test_user: User, token_config: Dict[str, Any]):
    """Test token blacklist functionality."""
    # Create multiple tokens
    token1 = create_access_token(
        user_id=test_user.id,
        username=test_user.username,
        **token_config
    )
    token2 = create_access_token(
        user_id=test_user.id,
        username=test_user.username,
        **token_config
    )
    
    # Revoke first token
    revoke_token(db, token1, test_user.id)
    
    # Verify first token is blacklisted
    assert not verify_token(token1, token_config["secret_key"], token_config["algorithm"])
    
    # Verify second token is still valid
    assert verify_token(token2, token_config["secret_key"], token_config["algorithm"])

def test_token_payload_validation(test_user: User, token_config: Dict[str, Any]):
    """Test token payload validation."""
    # Test missing required fields
    invalid_payload = {
        "sub": test_user.id,
        "exp": datetime.utcnow() + timedelta(minutes=15)
    }
    invalid_token = jwt.encode(
        invalid_payload,
        token_config["secret_key"],
        algorithm=token_config["algorithm"]
    )
    assert not verify_token(invalid_token, token_config["secret_key"], token_config["algorithm"])
    
    # Test invalid token type
    invalid_type_payload = {
        "sub": test_user.id,
        "username": test_user.username,
        "type": "invalid_type",
        "exp": datetime.utcnow() + timedelta(minutes=15)
    }
    invalid_type_token = jwt.encode(
        invalid_type_payload,
        token_config["secret_key"],
        algorithm=token_config["algorithm"]
    )
    assert not verify_token(invalid_type_token, token_config["secret_key"], token_config["algorithm"])

def test_token_creation_and_verification(db: Session):
    """Test token creation and verification."""
    # Create test user
    user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        is_active=True
    )
    db.add(user)
    db.commit()
    
    # Create tokens
    access_token = create_access_token(
        data={"sub": user.email},
        secret_key=token_settings.SECRET_KEY,
        algorithm=token_settings.ALGORITHM,
        expires_delta=timedelta(minutes=token_settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    )
    refresh_token = create_refresh_token(
        data={"sub": user.email},
        secret_key=token_settings.SECRET_KEY,
        algorithm=token_settings.ALGORITHM,
        expires_delta=timedelta(days=token_settings.REFRESH_TOKEN_EXPIRE_DAYS)
    )
    
    # Verify tokens
    assert verify_token(access_token, token_settings.SECRET_KEY, token_settings.ALGORITHM)
    assert verify_token(refresh_token, token_settings.SECRET_KEY, token_settings.ALGORITHM)
    
    # Decode tokens
    access_payload = verify_token(access_token, token_settings.SECRET_KEY, token_settings.ALGORITHM)
    refresh_payload = verify_token(refresh_token, token_settings.SECRET_KEY, token_settings.ALGORITHM)
    
    assert access_payload["sub"] == user.email
    assert refresh_payload["sub"] == user.email

def test_token_refresh(db: Session):
    """Test token refresh functionality."""
    # Create test user
    user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        is_active=True
    )
    db.add(user)
    db.commit()
    
    # Create refresh token
    refresh_token = create_refresh_token(
        data={"sub": user.email},
        secret_key=token_settings.SECRET_KEY,
        algorithm=token_settings.ALGORITHM,
        expires_delta=timedelta(days=token_settings.REFRESH_TOKEN_EXPIRE_DAYS)
    )
    
    # Refresh token
    new_access_token = refresh_access_token(
        refresh_token=refresh_token,
        secret_key=token_settings.SECRET_KEY,
        algorithm=token_settings.ALGORITHM,
        access_token_expire_minutes=token_settings.ACCESS_TOKEN_EXPIRE_MINUTES
    )
    
    # Verify new token
    assert verify_token(new_access_token, token_settings.SECRET_KEY, token_settings.ALGORITHM)
    payload = verify_token(new_access_token, token_settings.SECRET_KEY, token_settings.ALGORITHM)
    assert payload["sub"] == user.email

def test_token_revocation(db: Session):
    """Test token revocation."""
    # Create test user
    user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        is_active=True
    )
    db.add(user)
    db.commit()
    
    # Create and revoke token
    token = create_access_token(
        data={"sub": user.email},
        secret_key=token_settings.SECRET_KEY,
        algorithm=token_settings.ALGORITHM,
        expires_delta=timedelta(minutes=token_settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    )
    
    assert revoke_token(db, token, user.id)
    
    # Verify token is blacklisted
    blacklisted = db.query(TokenBlacklist).filter(
        TokenBlacklist.token == token,
        TokenBlacklist.user_id == user.id
    ).first()
    assert blacklisted is not None

def test_revoke_all_tokens(db: Session):
    """Test revoking all tokens for a user."""
    # Create test user
    user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        is_active=True
    )
    db.add(user)
    db.commit()
    
    # Create multiple tokens
    tokens = []
    for _ in range(3):
        token = create_access_token(
            data={"sub": user.email},
            secret_key=token_settings.SECRET_KEY,
            algorithm=token_settings.ALGORITHM,
            expires_delta=timedelta(minutes=token_settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        )
        tokens.append(token)
    
    # Revoke all tokens
    revoked_count = revoke_all_tokens(db, user.id)
    assert revoked_count == 3
    
    # Verify all tokens are blacklisted
    blacklisted = db.query(TokenBlacklist).filter(
        TokenBlacklist.user_id == user.id
    ).all()
    assert len(blacklisted) == 3

def test_cleanup_blacklist(db: Session):
    """Test cleanup of old blacklisted tokens."""
    # Create test user
    user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        is_active=True
    )
    db.add(user)
    db.commit()
    
    # Create old and new blacklisted tokens
    old_token = TokenBlacklist(
        token="old_token",
        user_id=user.id,
        revoked_at=datetime.utcnow() - timedelta(days=2)
    )
    new_token = TokenBlacklist(
        token="new_token",
        user_id=user.id,
        revoked_at=datetime.utcnow()
    )
    db.add(old_token)
    db.add(new_token)
    db.commit()
    
    # Cleanup blacklist
    cleanup_blacklist(db, max_age_hours=24)
    
    # Verify only new token remains
    blacklisted = db.query(TokenBlacklist).filter(
        TokenBlacklist.user_id == user.id
    ).all()
    assert len(blacklisted) == 1
    assert blacklisted[0].token == "new_token" 