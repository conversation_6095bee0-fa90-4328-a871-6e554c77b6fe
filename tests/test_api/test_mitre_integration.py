"""Tests for MITRE ATT&CK integration."""
import pytest
from datetime import datetime
from typing import Dict, Any, List

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.models.mitre import <PERSON>treVersion, MitreTechnique, MitreTactic
from tests.utils.assertions import (
    assert_json_response, 
    assert_pagination_response,
    assert_datetime_close
)


@pytest.mark.mitre
@pytest.mark.integration
class TestMitreIntegration:
    """Test MITRE ATT&CK integration."""

    @pytest.mark.parametrize("domain", ["enterprise", "mobile", "ics"])
    def test_import_mitre_data_with_domain(
        self, client: TestClient, db_session: Session, domain: str
    ):
        """Test importing MITRE data with different technology domains."""
        # Import data for the specified domain
        response = client.post(f"/api/v1/mitre/import?domain={domain}")
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert "version" in data["data"]
        assert "domain" in data["data"]
        assert data["data"]["domain"] == domain

        # Verify domain-specific import in database
        version = db_session.query(MitreVersion).filter_by(technology_domain=domain).first()
        assert version is not None
        assert version.technology_domain == domain
        assert version.is_current is True
        assert version.created_on is not None

        # Verify techniques were imported
        techniques = db_session.query(MitreTechnique).filter_by(version_id=version.id).all()
        assert len(techniques) > 0

        # Verify tactics were imported
        tactics = db_session.query(MitreTactic).filter_by(version_id=version.id).all()
        assert len(tactics) > 0

    def test_list_versions(self, client: TestClient, test_mitre_version: MitreVersion):
        """Test listing MITRE versions."""
        response = client.get("/api/v1/mitre/versions")
        assert response.status_code == 200
        versions = response.json()

        assert isinstance(versions, list)
        assert len(versions) >= 1
        
        # Find our test version in the list
        found = False
        for version in versions:
            if version["version"] == test_mitre_version.version:
                found = True
                assert version["name"] == test_mitre_version.name
                assert version["is_current"] == test_mitre_version.is_current
                assert version["technology_domain"] == test_mitre_version.technology_domain
                break
        
        assert found, f"Test version {test_mitre_version.version} not found in response"

    def test_get_current_version(self, client: TestClient, test_mitre_version: MitreVersion):
        """Test getting the current MITRE version."""
        # Ensure our test version is set as current
        test_mitre_version.is_current = True
        
        response = client.get("/api/v1/mitre/versions/current")
        assert response.status_code == 200
        version = response.json()
        
        assert version["version"] == test_mitre_version.version
        assert version["name"] == test_mitre_version.name
        assert version["is_current"] is True

    def test_get_techniques(
        self, 
        client: TestClient, 
        test_mitre_version: MitreVersion,
        test_mitre_technique: MitreTechnique
    ):
        """Test getting MITRE techniques."""
        response = client.get(
            f"/api/v1/mitre/techniques?version_id={test_mitre_version.id}"
        )
        assert response.status_code == 200
        data = response.json()
        
        assert_pagination_response(data)
        assert data["total"] >= 1
        
        # Find our test technique in the results
        found = False
        for technique in data["items"]:
            if technique["technique_id"] == test_mitre_technique.technique_id:
                found = True
                assert technique["name"] == test_mitre_technique.name
                assert technique["description"] == test_mitre_technique.description
                break
        
        assert found, f"Test technique {test_mitre_technique.technique_id} not found in response"

    def test_get_technique_by_id(
        self, 
        client: TestClient, 
        test_mitre_technique: MitreTechnique
    ):
        """Test getting a specific MITRE technique by ID."""
        response = client.get(
            f"/api/v1/mitre/techniques/{test_mitre_technique.technique_id}"
        )
        assert response.status_code == 200
        technique = response.json()
        
        assert technique["technique_id"] == test_mitre_technique.technique_id
        assert technique["name"] == test_mitre_technique.name
        assert technique["description"] == test_mitre_technique.description
        assert technique["detection"] == test_mitre_technique.detection
        assert technique["platforms"] == test_mitre_technique.platforms

    def test_get_tactics(
        self, 
        client: TestClient, 
        test_mitre_version: MitreVersion,
        test_mitre_tactic: MitreTactic
    ):
        """Test getting MITRE tactics."""
        response = client.get(
            f"/api/v1/mitre/tactics?version_id={test_mitre_version.id}"
        )
        assert response.status_code == 200
        data = response.json()
        
        assert_pagination_response(data)
        assert data["total"] >= 1
        
        # Find our test tactic in the results
        found = False
        for tactic in data["items"]:
            if tactic["external_id"] == test_mitre_tactic.external_id:
                found = True
                assert tactic["name"] == test_mitre_tactic.name
                assert tactic["description"] == test_mitre_tactic.description
                break
        
        assert found, f"Test tactic {test_mitre_tactic.external_id} not found in response"

    def test_get_tactic_by_id(
        self, 
        client: TestClient, 
        test_mitre_tactic: MitreTactic
    ):
        """Test getting a specific MITRE tactic by ID."""
        response = client.get(
            f"/api/v1/mitre/tactics/{test_mitre_tactic.external_id}"
        )
        assert response.status_code == 200
        tactic = response.json()
        
        assert tactic["external_id"] == test_mitre_tactic.external_id
        assert tactic["name"] == test_mitre_tactic.name
        assert tactic["description"] == test_mitre_tactic.description

    def test_search_techniques(
        self, 
        client: TestClient, 
        test_mitre_technique: MitreTechnique
    ):
        """Test searching for MITRE techniques."""
        # Search by name
        response = client.get(
            f"/api/v1/mitre/techniques/search?q={test_mitre_technique.name}"
        )
        assert response.status_code == 200
        data = response.json()
        
        assert_pagination_response(data)
        assert data["total"] >= 1
        
        # Find our test technique in the results
        found = False
        for technique in data["items"]:
            if technique["technique_id"] == test_mitre_technique.technique_id:
                found = True
                break
        
        assert found, f"Test technique {test_mitre_technique.technique_id} not found in search results"

    def test_technique_relationships(
        self, 
        client: TestClient, 
        test_mitre_technique: MitreTechnique,
        test_mitre_tactic: MitreTactic
    ):
        """Test MITRE technique relationships."""
        # Get technique with relationships
        response = client.get(
            f"/api/v1/mitre/techniques/{test_mitre_technique.technique_id}?include_relationships=true"
        )
        assert response.status_code == 200
        technique = response.json()
        
        assert "tactics" in technique
        assert isinstance(technique["tactics"], list)
        assert len(technique["tactics"]) >= 1
        
        # Find our test tactic in the relationships
        found = False
        for tactic in technique["tactics"]:
            if tactic["external_id"] == test_mitre_tactic.external_id:
                found = True
                assert tactic["name"] == test_mitre_tactic.name
                break
        
        assert found, f"Test tactic {test_mitre_tactic.external_id} not found in technique relationships"

    def test_error_handling(self, client: TestClient):
        """Test error handling in MITRE endpoints."""
        # Test invalid version ID
        response = client.get("/api/v1/mitre/techniques?version_id=999999")
        assert response.status_code == 200
        data = response.json()
        assert data["total"] == 0
        assert len(data["items"]) == 0

        # Test invalid pagination
        response = client.get("/api/v1/mitre/techniques?page=0")
        assert response.status_code == 422

        response = client.get("/api/v1/mitre/techniques?size=0")
        assert response.status_code == 422

        response = client.get("/api/v1/mitre/techniques?size=101")
        assert response.status_code == 422

        # Test invalid technique ID
        response = client.get("/api/v1/mitre/techniques/INVALID_ID")
        assert response.status_code == 404

        # Test invalid tactic ID
        response = client.get("/api/v1/mitre/tactics/INVALID_ID")
        assert response.status_code == 404
