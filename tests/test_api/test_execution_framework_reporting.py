"""
Tests for the reporting API endpoints in the execution framework.

This module contains tests for the reporting API endpoints, including
execution summary reports, MITRE coverage reports, and visualization reports.
"""
import io
import json
import pytest
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from api.main import app
from api.models.database.execution_framework.models import (
    Campaign,
    ExecutionLog,
    ExecutionStatus,
    MitreTechnique,
    TestCase,
)
from api.models.database.execution_framework.analytics_models import (
    ExecutionMetric,
    ExecutionTrend,
    MitreCoverageMetric,
)


client = TestClient(app)


@pytest.fixture
def admin_token():
    """Get an admin token for testing."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": "admin", "password": "adminpassword"}
    )
    return response.json()["access_token"]


@pytest.fixture
def mock_campaign():
    """Create a mock campaign for testing."""
    return Campaign(
        id=1,
        name="Test Campaign",
        description="Test campaign for reporting",
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by="test_user",
        test_cases=[MagicMock(test_case_id=1), MagicMock(test_case_id=2)]
    )


@pytest.fixture
def mock_test_case():
    """Create a mock test case for testing."""
    return TestCase(
        id=1,
        name="Test Case",
        description="Test case for reporting",
        command="echo 'test'",
        executor_type="bash",
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by="test_user",
        mitre_techniques=["T1059.001", "T1059.004"]
    )


@pytest.fixture
def mock_mitre_technique():
    """Create a mock MITRE technique for testing."""
    return MitreTechnique(
        id=1,
        technique_id="T1059.001",
        name="PowerShell",
        description="Adversaries may abuse PowerShell commands and scripts for execution.",
        tactic="execution",
        platforms=["windows"],
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


@pytest.fixture
def mock_execution_log():
    """Create a mock execution log for testing."""
    return ExecutionLog(
        id=1,
        test_case_id=1,
        campaign_id=1,
        status=ExecutionStatus.COMPLETED.value,
        start_time=datetime.now() - timedelta(days=1),
        end_time=datetime.now() - timedelta(days=1) + timedelta(minutes=5),
        output="Test output",
        created_at=datetime.now() - timedelta(days=1),
        updated_at=datetime.now() - timedelta(days=1)
    )


@pytest.fixture
def mock_execution_metric():
    """Create a mock execution metric for testing."""
    return ExecutionMetric(
        id=1,
        test_case_id=1,
        campaign_id=1,
        execution_count=10,
        success_count=8,
        failure_count=2,
        success_rate=80.0,
        execution_time=300.0,
        created_at=datetime.now() - timedelta(days=1),
        updated_at=datetime.now() - timedelta(days=1)
    )


@pytest.fixture
def mock_db_session(
    mock_campaign,
    mock_test_case,
    mock_mitre_technique,
    mock_execution_log,
    mock_execution_metric,
):
    """Create a mock database session with test data."""
    db_session = MagicMock()
    
    # Configure query mocks
    campaign_query = MagicMock()
    campaign_query.filter.return_value.first.return_value = mock_campaign
    
    test_case_query = MagicMock()
    test_case_query.filter.return_value.all.return_value = [mock_test_case]
    
    mitre_technique_query = MagicMock()
    mitre_technique_query.filter.return_value.all.return_value = [mock_mitre_technique]
    
    execution_log_query = MagicMock()
    execution_log_query.filter.return_value.all.return_value = [mock_execution_log]
    
    execution_metric_query = MagicMock()
    execution_metric_query.filter.return_value.all.return_value = [mock_execution_metric]
    
    # Configure db_session.query to return appropriate mock query
    def query_side_effect(model):
        if model == Campaign:
            return campaign_query
        elif model == TestCase:
            return test_case_query
        elif model == MitreTechnique:
            return mitre_technique_query
        elif model == ExecutionLog:
            return execution_log_query
        elif model == ExecutionMetric:
            return execution_metric_query
        return MagicMock()
    
    db_session.query.side_effect = query_side_effect
    
    return db_session


def test_generate_execution_summary_report_xlsx(admin_token, mock_db_session):
    """Test generating an execution summary report in XLSX format."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}), \
         patch('api.services.execution_framework.reporting._generate_xlsx_report') as mock_generate_xlsx:
        
        # Mock the report generation function
        mock_output = io.BytesIO(b"test xlsx data")
        mock_filename = "execution_summary_report.xlsx"
        mock_generate_xlsx.return_value = (mock_output, mock_filename)
        
        # Make the API request
        start_date = (datetime.now() - timedelta(days=30)).isoformat()
        end_date = datetime.now().isoformat()
        
        response = client.get(
            "/api/v1/execution-framework/reports/execution-summary",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "start_date": start_date,
                "end_date": end_date,
                "campaign_id": 1,
                "format": "xlsx"
            }
        )
        
        # Check the response
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        assert response.headers["content-disposition"] == f"attachment; filename={mock_filename}"
        assert response.content == b"test xlsx data"


def test_generate_execution_summary_report_csv(admin_token, mock_db_session):
    """Test generating an execution summary report in CSV format."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}), \
         patch('api.services.execution_framework.reporting._generate_csv_report') as mock_generate_csv:
        
        # Mock the report generation function
        mock_output = io.BytesIO(b"test,csv,data\n1,2,3")
        mock_filename = "execution_summary_report.csv"
        mock_generate_csv.return_value = (mock_output, mock_filename)
        
        # Make the API request
        start_date = (datetime.now() - timedelta(days=30)).isoformat()
        end_date = datetime.now().isoformat()
        
        response = client.get(
            "/api/v1/execution-framework/reports/execution-summary",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "start_date": start_date,
                "end_date": end_date,
                "test_case_id": 1,
                "format": "csv"
            }
        )
        
        # Check the response
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/csv"
        assert response.headers["content-disposition"] == f"attachment; filename={mock_filename}"
        assert response.content == b"test,csv,data\n1,2,3"


def test_generate_execution_summary_report_json(admin_token, mock_db_session):
    """Test generating an execution summary report in JSON format."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}), \
         patch('api.services.execution_framework.reporting._generate_json_report') as mock_generate_json:
        
        # Mock the report generation function
        mock_output = io.BytesIO(json.dumps({"test": "data"}).encode('utf-8'))
        mock_filename = "execution_summary_report.json"
        mock_generate_json.return_value = (mock_output, mock_filename)
        
        # Make the API request
        start_date = (datetime.now() - timedelta(days=30)).isoformat()
        end_date = datetime.now().isoformat()
        
        response = client.get(
            "/api/v1/execution-framework/reports/execution-summary",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "start_date": start_date,
                "end_date": end_date,
                "format": "json"
            }
        )
        
        # Check the response
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"
        assert response.headers["content-disposition"] == f"attachment; filename={mock_filename}"
        assert json.loads(response.content) == {"test": "data"}


def test_generate_mitre_coverage_report_xlsx(admin_token, mock_db_session):
    """Test generating a MITRE coverage report in XLSX format."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}), \
         patch('api.services.execution_framework.reporting._generate_mitre_coverage_xlsx_report') as mock_generate_xlsx:
        
        # Mock the report generation function
        mock_output = io.BytesIO(b"test xlsx data")
        mock_filename = "mitre_coverage_report.xlsx"
        mock_generate_xlsx.return_value = (mock_output, mock_filename)
        
        # Make the API request
        response = client.get(
            "/api/v1/execution-framework/reports/mitre-coverage",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "campaign_id": 1,
                "format": "xlsx"
            }
        )
        
        # Check the response
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        assert response.headers["content-disposition"] == f"attachment; filename={mock_filename}"
        assert response.content == b"test xlsx data"


def test_generate_mitre_coverage_report_csv(admin_token, mock_db_session):
    """Test generating a MITRE coverage report in CSV format."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}), \
         patch('api.services.execution_framework.reporting._generate_csv_report') as mock_generate_csv:
        
        # Mock the report generation function
        mock_output = io.BytesIO(b"test,csv,data\n1,2,3")
        mock_filename = "mitre_coverage_report.csv"
        mock_generate_csv.return_value = (mock_output, mock_filename)
        
        # Make the API request
        response = client.get(
            "/api/v1/execution-framework/reports/mitre-coverage",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "test_case_id": 1,
                "format": "csv"
            }
        )
        
        # Check the response
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/csv"
        assert response.headers["content-disposition"] == f"attachment; filename={mock_filename}"
        assert response.content == b"test,csv,data\n1,2,3"


def test_generate_mitre_coverage_report_json(admin_token, mock_db_session):
    """Test generating a MITRE coverage report in JSON format."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}), \
         patch('api.services.execution_framework.reporting._generate_json_report') as mock_generate_json:
        
        # Mock the report generation function
        mock_output = io.BytesIO(json.dumps({"test": "data"}).encode('utf-8'))
        mock_filename = "mitre_coverage_report.json"
        mock_generate_json.return_value = (mock_output, mock_filename)
        
        # Make the API request
        response = client.get(
            "/api/v1/execution-framework/reports/mitre-coverage",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "format": "json"
            }
        )
        
        # Check the response
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"
        assert response.headers["content-disposition"] == f"attachment; filename={mock_filename}"
        assert json.loads(response.content) == {"test": "data"}


def test_generate_mitre_coverage_visualization_report_xlsx(admin_token, mock_db_session):
    """Test generating a MITRE coverage visualization report in XLSX format."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}), \
         patch('api.services.execution_framework.reporting._generate_mitre_coverage_xlsx_report') as mock_generate_xlsx:
        
        # Mock the report generation function
        mock_output = io.BytesIO(b"test xlsx data")
        mock_filename = "mitre_coverage_visualization_report.xlsx"
        mock_generate_xlsx.return_value = (mock_output, mock_filename)
        
        # Make the API request
        response = client.get(
            "/api/v1/execution-framework/reports/mitre-coverage-visualization",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "campaign_id": 1,
                "format": "xlsx"
            }
        )
        
        # Check the response
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        assert response.headers["content-disposition"] == f"attachment; filename={mock_filename}"
        assert response.content == b"test xlsx data"


def test_generate_mitre_coverage_visualization_report_pdf(admin_token, mock_db_session):
    """Test generating a MITRE coverage visualization report in PDF format."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}), \
         patch('api.services.execution_framework.reporting._generate_mitre_coverage_pdf_report') as mock_generate_pdf, \
         patch('api.services.execution_framework.reporting._generate_layer_visualization') as mock_generate_viz:
        
        # Mock the report generation function
        mock_output = io.BytesIO(b"test pdf data")
        mock_filename = "mitre_coverage_visualization_report.pdf"
        mock_generate_pdf.return_value = (mock_output, mock_filename)
        
        # Mock the visualization generation function
        mock_generate_viz.return_value = "/tmp/test_visualization.png"
        
        # Make the API request
        response = client.get(
            "/api/v1/execution-framework/reports/mitre-coverage-visualization",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "test_case_id": 1,
                "format": "pdf"
            }
        )
        
        # Check the response
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/pdf"
        assert response.headers["content-disposition"] == f"attachment; filename={mock_filename}"
        assert response.content == b"test pdf data"


def test_generate_mitre_coverage_visualization_report_invalid_format(admin_token, mock_db_session):
    """Test generating a MITRE coverage visualization report with an invalid format."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}):
        
        # Make the API request with an invalid format
        response = client.get(
            "/api/v1/execution-framework/reports/mitre-coverage-visualization",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "campaign_id": 1,
                "format": "json"  # Invalid format for visualization report
            }
        )
        
        # Check the response
        assert response.status_code == 400
        assert "Unsupported report format" in response.json()["detail"]


def test_generate_analytics_report_xlsx(admin_token, mock_db_session):
    """Test generating an analytics report in XLSX format."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}), \
         patch('api.services.execution_framework.reporting._generate_xlsx_report') as mock_generate_xlsx:
        
        # Mock the report generation function
        mock_output = io.BytesIO(b"test xlsx data")
        mock_filename = "analytics_report.xlsx"
        mock_generate_xlsx.return_value = (mock_output, mock_filename)
        
        # Make the API request
        start_date = (datetime.now() - timedelta(days=30)).isoformat()
        end_date = datetime.now().isoformat()
        
        response = client.get(
            "/api/v1/execution-framework/reports/analytics",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "start_date": start_date,
                "end_date": end_date,
                "campaign_id": 1,
                "format": "xlsx"
            }
        )
        
        # Check the response
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        assert response.headers["content-disposition"] == f"attachment; filename={mock_filename}"
        assert response.content == b"test xlsx data"


def test_generate_analytics_report_csv(admin_token, mock_db_session):
    """Test generating an analytics report in CSV format."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}), \
         patch('api.services.execution_framework.reporting._generate_csv_report') as mock_generate_csv:
        
        # Mock the report generation function
        mock_output = io.BytesIO(b"test,csv,data\n1,2,3")
        mock_filename = "analytics_report.csv"
        mock_generate_csv.return_value = (mock_output, mock_filename)
        
        # Make the API request
        start_date = (datetime.now() - timedelta(days=30)).isoformat()
        end_date = datetime.now().isoformat()
        
        response = client.get(
            "/api/v1/execution-framework/reports/analytics",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "start_date": start_date,
                "end_date": end_date,
                "test_case_id": 1,
                "format": "csv"
            }
        )
        
        # Check the response
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/csv"
        assert response.headers["content-disposition"] == f"attachment; filename={mock_filename}"
        assert response.content == b"test,csv,data\n1,2,3"


def test_generate_analytics_report_json(admin_token, mock_db_session):
    """Test generating an analytics report in JSON format."""
    with patch('api.database.get_db', return_value=iter([mock_db_session])), \
         patch('api.dependencies.get_current_user', return_value={"username": "admin"}), \
         patch('api.services.execution_framework.reporting._generate_json_report') as mock_generate_json:
        
        # Mock the report generation function
        mock_output = io.BytesIO(json.dumps({"test": "data"}).encode('utf-8'))
        mock_filename = "analytics_report.json"
        mock_generate_json.return_value = (mock_output, mock_filename)
        
        # Make the API request
        start_date = (datetime.now() - timedelta(days=30)).isoformat()
        end_date = datetime.now().isoformat()
        
        response = client.get(
            "/api/v1/execution-framework/reports/analytics",
            headers={"Authorization": f"Bearer {admin_token}"},
            params={
                "start_date": start_date,
                "end_date": end_date,
                "format": "json"
            }
        )
        
        # Check the response
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"
        assert response.headers["content-disposition"] == f"attachment; filename={mock_filename}"
        assert json.loads(response.content) == {"test": "data"}
