"""
Tests for the test schedule API endpoints.

This module contains tests for the test schedule API endpoints, including
creating, retrieving, updating, and deleting schedules, as well as executing
schedules and retrieving execution results.
"""
import pytest
from datetime import datetime, timedelta
from fastapi.testclient import TestClient

from api.main import app
from api.models.database.test_case import TestCase
from api.models.database.test_schedule import (
    ScheduleExecution,
    ScheduleFrequency,
    ScheduleStatus,
    TestSchedule,
    TestScheduleTestCase,
)


client = TestClient(app)


@pytest.fixture
def admin_token():
    """Get an admin token for testing."""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": "admin", "password": "adminpassword"}
    )
    return response.json()["access_token"]


@pytest.fixture
def test_cases(db_session):
    """Create test cases for testing."""
    test_cases = []
    for i in range(3):
        test_case = TestCase(
            name=f"Test Case {i+1}",
            description=f"Description for test case {i+1}",
            type="manual",
            status="active",
            priority="high",
            complexity="moderate",
            prerequisites="Access to the application",
            steps=[f"Step 1 for test case {i+1}", f"Step 2 for test case {i+1}"],
            expected_result=f"Expected result for test case {i+1}",
            tags=["security", "test"],
            created_by="1",
            version="1.0.0"
        )
        db_session.add(test_case)
        test_cases.append(test_case)
    
    db_session.commit()
    for test_case in test_cases:
        db_session.refresh(test_case)
    
    yield test_cases
    
    # Clean up
    for test_case in test_cases:
        db_session.delete(test_case)
    
    db_session.commit()


@pytest.fixture
def test_schedule(db_session, test_cases):
    """Create a test schedule for testing."""
    schedule = TestSchedule(
        name="Daily Security Tests",
        description="Daily execution of security test cases",
        frequency=ScheduleFrequency.DAILY,
        start_date=datetime.utcnow(),
        next_execution=datetime.utcnow() + timedelta(days=1),
        status=ScheduleStatus.ACTIVE,
        execution_parameters={"environment": "Test"},
        created_by="1"
    )
    db_session.add(schedule)
    db_session.commit()
    db_session.refresh(schedule)
    
    # Add test cases to schedule
    for test_case in test_cases:
        schedule_test_case = TestScheduleTestCase(
            schedule_id=schedule.id,
            test_case_id=test_case.id
        )
        db_session.add(schedule_test_case)
    
    db_session.commit()
    db_session.refresh(schedule)
    
    yield schedule
    
    # Clean up
    db_session.query(TestScheduleTestCase).filter(
        TestScheduleTestCase.schedule_id == schedule.id
    ).delete()
    
    db_session.query(ScheduleExecution).filter(
        ScheduleExecution.schedule_id == schedule.id
    ).delete()
    
    db_session.delete(schedule)
    db_session.commit()


def test_create_schedule(client, db_session, test_cases, admin_token):
    """Test creating a schedule."""
    start_date = datetime.utcnow().isoformat()
    
    response = client.post(
        "/api/v1/test-schedules",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "name": "Weekly Regression Tests",
            "description": "Weekly execution of regression test cases",
            "frequency": "weekly",
            "start_date": start_date,
            "execution_parameters": {"environment": "Staging"},
            "test_case_ids": [test_case.id for test_case in test_cases]
        }
    )
    
    assert response.status_code == 201
    data = response.json()
    
    # Verify response data
    assert data["name"] == "Weekly Regression Tests"
    assert data["frequency"] == "weekly"
    assert data["status"] == "pending"
    assert "id" in data
    
    # Verify database state
    db_schedule = db_session.query(TestSchedule).filter(TestSchedule.id == data["id"]).first()
    assert db_schedule is not None
    assert db_schedule.name == "Weekly Regression Tests"
    assert db_schedule.frequency == "weekly"
    
    # Verify test cases were added
    schedule_test_cases = db_session.query(TestScheduleTestCase).filter(
        TestScheduleTestCase.schedule_id == data["id"]
    ).all()
    assert len(schedule_test_cases) == len(test_cases)
    
    # Clean up
    db_session.query(TestScheduleTestCase).filter(
        TestScheduleTestCase.schedule_id == data["id"]
    ).delete()
    db_session.delete(db_schedule)
    db_session.commit()


def test_get_schedules(client, db_session, test_schedule, admin_token):
    """Test getting schedules."""
    response = client.get(
        "/api/v1/test-schedules",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    
    # Find our test schedule
    schedule_data = next((s for s in data if s["id"] == test_schedule.id), None)
    assert schedule_data is not None
    assert schedule_data["name"] == test_schedule.name
    assert schedule_data["frequency"] == test_schedule.frequency


def test_get_schedule(client, db_session, test_schedule, test_cases, admin_token):
    """Test getting a specific schedule."""
    response = client.get(
        f"/api/v1/test-schedules/{test_schedule.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_schedule.id
    assert data["name"] == test_schedule.name
    assert data["frequency"] == test_schedule.frequency
    assert "test_cases" in data
    assert len(data["test_cases"]) == len(test_cases)


def test_update_schedule(client, db_session, test_schedule, admin_token):
    """Test updating a schedule."""
    response = client.put(
        f"/api/v1/test-schedules/{test_schedule.id}",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "name": "Updated Security Tests",
            "frequency": "weekly",
            "status": "paused"
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "Updated Security Tests"
    assert data["frequency"] == "weekly"
    assert data["status"] == "paused"
    
    # Verify database state
    db_session.refresh(test_schedule)
    assert test_schedule.name == "Updated Security Tests"
    assert test_schedule.frequency == "weekly"
    assert test_schedule.status == "paused"


def test_delete_schedule(client, db_session, test_schedule, admin_token):
    """Test deleting a schedule."""
    response = client.delete(
        f"/api/v1/test-schedules/{test_schedule.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 204
    
    # Verify database state (soft delete)
    db_session.refresh(test_schedule)
    assert test_schedule.deleted_at is not None


def test_restore_schedule(client, db_session, test_schedule, admin_token):
    """Test restoring a deleted schedule."""
    # First delete the schedule
    test_schedule.deleted_at = datetime.utcnow()
    db_session.add(test_schedule)
    db_session.commit()
    
    response = client.post(
        f"/api/v1/test-schedules/{test_schedule.id}/restore",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_schedule.id
    
    # Verify database state
    db_session.refresh(test_schedule)
    assert test_schedule.deleted_at is None


def test_execute_schedule(client, db_session, test_schedule, admin_token):
    """Test executing a schedule."""
    # Make sure schedule is active
    test_schedule.status = "active"
    db_session.add(test_schedule)
    db_session.commit()
    
    response = client.post(
        f"/api/v1/test-schedules/{test_schedule.id}/execute",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "schedule_execution_id" in data
    assert data["successful_count"] == len(test_schedule.test_cases)
    
    # Verify database state
    schedule_execution = db_session.query(ScheduleExecution).filter(
        ScheduleExecution.id == data["schedule_execution_id"]
    ).first()
    assert schedule_execution is not None
    assert schedule_execution.schedule_id == test_schedule.id
    assert schedule_execution.status in ["completed", "in_progress"]
    
    # Verify schedule was updated
    db_session.refresh(test_schedule)
    assert test_schedule.last_execution is not None


def test_get_schedule_executions(client, db_session, test_schedule, admin_token):
    """Test getting executions for a schedule."""
    # Create a schedule execution
    execution = ScheduleExecution(
        schedule_id=test_schedule.id,
        execution_date=datetime.utcnow(),
        status="completed",
        result={"total": 3, "successful": 3, "failed": 0},
        metadata={"environment": "Test"}
    )
    db_session.add(execution)
    db_session.commit()
    
    response = client.get(
        f"/api/v1/test-schedules/{test_schedule.id}/executions",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    
    # Find our execution
    execution_data = next((e for e in data if e["id"] == execution.id), None)
    assert execution_data is not None
    assert execution_data["schedule_id"] == test_schedule.id
    assert execution_data["status"] == "completed"
    
    # Clean up
    db_session.delete(execution)
    db_session.commit()


def test_get_schedule_execution(client, db_session, test_schedule, admin_token):
    """Test getting a specific schedule execution."""
    # Create a schedule execution
    execution = ScheduleExecution(
        schedule_id=test_schedule.id,
        execution_date=datetime.utcnow(),
        status="completed",
        result={"total": 3, "successful": 3, "failed": 0},
        metadata={"environment": "Test"}
    )
    db_session.add(execution)
    db_session.commit()
    db_session.refresh(execution)
    
    response = client.get(
        f"/api/v1/test-schedules/{test_schedule.id}/executions/{execution.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == execution.id
    assert data["schedule_id"] == test_schedule.id
    assert data["status"] == "completed"
    assert "schedule" in data
    assert "test_executions" in data
    
    # Clean up
    db_session.delete(execution)
    db_session.commit()


def test_update_schedule_execution(client, db_session, test_schedule, admin_token):
    """Test updating a schedule execution."""
    # Create a schedule execution
    execution = ScheduleExecution(
        schedule_id=test_schedule.id,
        execution_date=datetime.utcnow(),
        status="in_progress",
        result={},
        metadata={}
    )
    db_session.add(execution)
    db_session.commit()
    db_session.refresh(execution)
    
    response = client.put(
        f"/api/v1/test-schedules/{test_schedule.id}/executions/{execution.id}",
        headers={"Authorization": f"Bearer {admin_token}"},
        json={
            "status": "completed",
            "result": {"total": 3, "successful": 2, "failed": 1},
            "metadata": {"environment": "Test", "duration_ms": 5000}
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == execution.id
    assert data["status"] == "completed"
    assert data["result"]["total"] == 3
    assert data["metadata"]["environment"] == "Test"
    
    # Verify database state
    db_session.refresh(execution)
    assert execution.status == "completed"
    assert execution.result["total"] == 3
    assert execution.metadata["environment"] == "Test"
    
    # Clean up
    db_session.delete(execution)
    db_session.commit()


def test_get_due_schedules(client, db_session, admin_token):
    """Test getting schedules that are due for execution."""
    # Create a due schedule
    due_schedule = TestSchedule(
        name="Due Schedule",
        description="Schedule that is due for execution",
        frequency=ScheduleFrequency.DAILY,
        start_date=datetime.utcnow() - timedelta(days=1),
        next_execution=datetime.utcnow() - timedelta(hours=1),
        status=ScheduleStatus.ACTIVE,
        execution_parameters={},
        created_by="1"
    )
    db_session.add(due_schedule)
    db_session.commit()
    
    response = client.get(
        "/api/v1/test-schedules/due",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    
    # Find our due schedule
    due_schedule_data = next((s for s in data if s["id"] == due_schedule.id), None)
    assert due_schedule_data is not None
    assert due_schedule_data["name"] == due_schedule.name
    
    # Clean up
    db_session.delete(due_schedule)
    db_session.commit()


def test_filter_schedules(client, db_session, test_schedule, admin_token):
    """Test filtering schedules."""
    # Create another schedule with different frequency
    other_schedule = TestSchedule(
        name="Monthly Performance Tests",
        description="Monthly execution of performance test cases",
        frequency=ScheduleFrequency.MONTHLY,
        start_date=datetime.utcnow(),
        next_execution=datetime.utcnow() + timedelta(days=30),
        status=ScheduleStatus.PENDING,
        execution_parameters={"environment": "Production"},
        created_by="1"
    )
    db_session.add(other_schedule)
    db_session.commit()
    
    # Test filtering by frequency
    response = client.get(
        "/api/v1/test-schedules?frequency=daily",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    assert all(s["frequency"] == "daily" for s in data)
    
    # Test filtering by status
    response = client.get(
        "/api/v1/test-schedules?status=active",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    assert all(s["status"] == "active" for s in data)
    
    # Test search
    response = client.get(
        "/api/v1/test-schedules?search=Security",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 1
    assert any("Security" in s["name"] for s in data)
    
    # Clean up
    db_session.delete(other_schedule)
    db_session.commit()
