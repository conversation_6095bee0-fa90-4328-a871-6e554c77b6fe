"""Test suite for API endpoints."""
import pytest
from fastapi.testclient import Test<PERSON>lient
from datetime import datetime
from typing import Dict

def test_health_check(test_client: TestClient):
    """Test health check endpoint returns expected response."""
    response = test_client.get("/health")
    assert response.status_code == 200
    data = response.json()

    # Check expected fields are present
    assert "status" in data
    assert "timestamp" in data

    # Verify status is 'healthy'
    assert data["status"] == "healthy"

    # Verify timestamp is valid ISO format
    try:
        timestamp = datetime.fromisoformat(data["timestamp"])
        assert isinstance(timestamp, datetime)
    except ValueError as e:
        pytest.fail(f"Invalid timestamp format: {e}")

def test_root_endpoint(test_client: TestClient):
    """Test root endpoint returns expected response."""
    response = test_client.get("/")
    assert response.status_code == 200
    data = response.json()

    # Check expected fields
    assert "name" in data
    assert "version" in data
    assert "status" in data
    assert "message" in data
    assert "api_docs" in data
    assert "timestamp" in data

    # Verify content
    assert data["name"] == "Cybersecurity Data Platform"
    assert data["version"] == "1.0.0"
    assert data["status"] == "operational"
    assert data["message"] == "Welcome to the Cybersecurity Data Platform API"
    assert data["api_docs"] == "/docs"

    # Verify timestamp is valid ISO format
    try:
        timestamp = datetime.fromisoformat(data["timestamp"])
        assert isinstance(timestamp, datetime)
    except ValueError as e:
        pytest.fail(f"Invalid timestamp format: {e}")

def test_invalid_endpoint(test_client: TestClient):
    """Test that invalid endpoints return 404."""
    response = test_client.get("/nonexistent")
    assert response.status_code == 404
    data = response.json()
    assert "detail" in data
    assert data["detail"] == "Not Found"