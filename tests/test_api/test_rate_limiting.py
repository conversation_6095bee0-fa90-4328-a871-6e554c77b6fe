"""Tests for rate limiting functionality."""

import pytest
import time
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.models.user import User
from api.auth.rate_limit.storage import MemoryStorage, RedisStorage
from api.config.rate_limit_config import rate_limit_settings

def test_rate_limit_configuration():
    """Test rate limit configuration settings."""
    assert rate_limit_settings.ENABLED is True
    assert rate_limit_settings.DEFAULT_LIMIT == 100
    assert rate_limit_settings.DEFAULT_WINDOW == 60
    assert "/api/auth/login" in rate_limit_settings.ENDPOINT_LIMITS
    assert rate_limit_settings.STORAGE_TYPE == "memory"

def test_memory_storage():
    """Test in-memory storage backend."""
    storage = MemoryStorage()
    
    # Test setting and getting data
    key = "test_key"
    data = {"count": 1, "reset_time": time.time() + 60}
    assert storage.set(key, data, 60) is True
    
    retrieved_data = storage.get(key)
    assert retrieved_data is not None
    assert retrieved_data["count"] == 1
    
    # Test expiration
    time.sleep(0.1)  # Small delay to ensure time has passed
    data = {"count": 1, "reset_time": time.time() - 1}  # Expired
    storage.set(key, data, 60)
    assert storage.get(key) is None
    
    # Test deletion
    data = {"count": 1, "reset_time": time.time() + 60}
    storage.set(key, data, 60)
    assert storage.delete(key) is True
    assert storage.get(key) is None

@pytest.mark.asyncio
async def test_rate_limit_middleware(client: TestClient):
    """Test rate limiting middleware."""
    # Test default rate limit
    for _ in range(rate_limit_settings.DEFAULT_LIMIT):
        response = client.get("/api/test")
        assert response.status_code == 200
    
    # Test rate limit exceeded
    response = client.get("/api/test")
    assert response.status_code == 429
    assert "Too many requests" in response.json()["detail"]
    
    # Test rate limit headers
    response = client.get("/api/test")
    assert "X-RateLimit-Limit" in response.headers
    assert "X-RateLimit-Remaining" in response.headers
    assert "X-RateLimit-Reset" in response.headers
    assert "Retry-After" in response.headers

@pytest.mark.asyncio
async def test_endpoint_specific_limits(client: TestClient):
    """Test endpoint-specific rate limits."""
    # Test login endpoint limit
    for _ in range(rate_limit_settings.ENDPOINT_LIMITS["/api/auth/login"]["limit"]):
        response = client.post("/api/auth/login", json={
            "email": "<EMAIL>",
            "password": "password123"
        })
        assert response.status_code in [200, 401]  # Either success or auth failure
    
    # Test rate limit exceeded
    response = client.post("/api/auth/login", json={
        "email": "<EMAIL>",
        "password": "password123"
    })
    assert response.status_code == 429

@pytest.mark.asyncio
async def test_rate_limit_status_endpoint(client: TestClient, db: Session):
    """Test rate limit status endpoint."""
    # Create test user
    user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        is_active=True
    )
    db.add(user)
    db.commit()
    
    # Login to get token
    response = client.post("/api/auth/login", json={
        "email": "<EMAIL>",
        "password": "password123"
    })
    token = response.json()["access_token"]
    
    # Test status endpoint
    response = client.get(
        "/api/rate-limit/status",
        headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert "global_limit" in data
    assert "global_window" in data
    assert "endpoint_limits" in data
    assert "current_usage" in data

@pytest.mark.asyncio
async def test_rate_limit_admin_config(client: TestClient, db: Session):
    """Test rate limit admin configuration endpoint."""
    # Create admin user
    admin = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        is_active=True,
        is_admin=True
    )
    db.add(admin)
    db.commit()
    
    # Login as admin
    response = client.post("/api/auth/login", json={
        "email": "<EMAIL>",
        "password": "password123"
    })
    token = response.json()["access_token"]
    
    # Test updating configuration
    new_config = {
        "DEFAULT_LIMIT": 200,
        "DEFAULT_WINDOW": 120,
        "ENDPOINT_LIMITS": {
            "/api/auth/login": {
                "limit": 10,
                "window": 600
            }
        }
    }
    
    response = client.post(
        "/api/admin/rate-limit/config",
        headers={"Authorization": f"Bearer {token}"},
        json=new_config
    )
    assert response.status_code == 200
    
    # Verify changes
    response = client.get(
        "/api/rate-limit/status",
        headers={"Authorization": f"Bearer {token}"}
    )
    data = response.json()
    assert data["global_limit"] == 200
    assert data["global_window"] == 120
    assert data["endpoint_limits"]["/api/auth/login"]["limit"] == 10

@pytest.mark.asyncio
async def test_rate_limit_cleanup(client: TestClient):
    """Test rate limit cleanup functionality."""
    storage = MemoryStorage()
    
    # Create expired entries
    for i in range(5):
        key = f"test_key_{i}"
        data = {"count": 1, "reset_time": time.time() - 1}
        storage.set(key, data, 60)
    
    # Run cleanup
    storage.cleanup()
    
    # Verify cleanup
    for i in range(5):
        key = f"test_key_{i}"
        assert storage.get(key) is None 