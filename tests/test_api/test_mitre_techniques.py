
"""Tests for MITRE technique endpoints."""
import pytest
from fastapi.testclient import TestClient

def test_create_technique(client):
    """Test creating a new technique."""
    response = client.post(
        "/api/v1/mitre/techniques",
        json={
            "technique_id": "T1001",
            "name": "Test Technique",
            "description": "Test Description"
        }
    )
    assert response.status_code == 201
    data = response.json()
    assert data["technique_id"] == "T1001"
    assert data["name"] == "Test Technique"

def test_list_techniques(client):
    """Test listing techniques."""
    response = client.get("/api/v1/mitre/techniques")
    assert response.status_code == 200
    data = response.json()
    assert "items" in data
    assert isinstance(data["items"], list)

def test_get_technique(client):
    """Test getting a specific technique."""
    # First create a technique
    client.post(
        "/api/v1/mitre/techniques",
        json={
            "technique_id": "T1002",
            "name": "Another Test",
            "description": "Another Description"
        }
    )
    
    response = client.get("/api/v1/mitre/techniques/T1002")
    assert response.status_code == 200
    data = response.json()
    assert data["technique_id"] == "T1002"
    assert data["name"] == "Another Test"
