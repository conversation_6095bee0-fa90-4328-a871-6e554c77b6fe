"""Test configuration and fixtures for API testing."""
import pytest
from fastapi.testclient import TestClient
from typing import Generator

from api.main import app

@pytest.fixture(scope="session")
def test_client() -> Generator:
    """Create a test client for making requests."""
    with TestClient(app) as client:
        yield client

@pytest.fixture
def test_headers():
    """Return headers for authenticated requests."""
    return {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }