"""Tests for internationalization utilities."""
import pytest
import os
from pathlib import Path
from unittest.mock import patch, MagicMock
import threading
import time
import json

# Set testing environment
os.environ["TESTING"] = "true"

from api.utils.i18n import _, load_translations, SUPPORTED_LOCALES, DEFAULT_LOCALE, clear_translation_cache

def test_translation_loading():
    """Test that translations are loaded correctly."""
    load_translations()

    # Test default locale
    assert _("Campaign name is required") == "Campaign name is required"

    # Test Spanish translation
    assert _("Campaign name is required", "es") == "El nombre de la campaña es obligatorio"

    # Test German translation
    assert _("Campaign name is required", "de") == "Kampagnenname ist erforderlich"

def test_locale_mapping():
    """Test locale code mapping."""
    # Test common language codes
    assert _("Internal server error", "en") == "Internal server error"
    assert _("Internal server error", "english") == "Internal server error"
    assert _("Internal server error", "eng") == "Internal server error"

    # Test British English
    assert _("Internal server error", "british") == "Internal server error"
    assert _("Internal server error", "uk") == "Internal server error"

    # Test German variations
    assert _("Internal server error", "german") == "Interner Serverfehler"
    assert _("Internal server error", "deutsch") == "Interner Serverfehler"

def test_unsupported_locale():
    """Test handling of unsupported locales."""
    # Should fallback to message string
    assert _("Test message", "unsupported") == "Test message"
    assert _("Test message", "fr") == "Test message"

def test_missing_message():
    """Test handling of missing translation messages."""
    # Should return original message
    assert _("Non-existent message", "es") == "Non-existent message"
    assert _("Non-existent message", "de") == "Non-existent message"

def test_translation_loading_error():
    """Test error handling during translation loading."""
    with patch('babel.support.Translations.load') as mock_load:
        mock_load.side_effect = Exception("Loading error")

        # Should handle error gracefully and continue
        load_translations()

        # Verify translations still work with fallback
        assert _("Test message", "es") == "Test message"

def test_invalid_locale_format():
    """Test handling of invalid locale format."""
    assert _("Test message", "") == "Test message"
    assert _("Test message", None) == "Test message"
    assert _("Test message", "en-US") == "Test message"  # Invalid format

def test_translation_loading_missing_files():
    """Test handling of missing translation files."""
    with patch('os.path.exists') as mock_exists:
        mock_exists.return_value = False

        # Should handle missing files gracefully
        load_translations()

        # Verify translations fallback to original message
        assert _("Test message", "es") == "Test message"

def test_locale_normalization():
    """Test locale code normalization."""
    # Test various locale code formats
    assert _("Test message", "EN") == "Test message"  # Upper case
    assert _("Test message", "en_US") == "Test message"  # With underscore
    assert _("Test message", "en-us") == "Test message"  # With hyphen

def test_performance_caching():
    """Test translation caching performance."""
    # Test production mode caching
    with patch.dict(os.environ, {"TESTING": "false"}):
        with patch('babel.support.Translations.load') as mock_load:
            # First load should hit the loader
            load_translations()
            assert mock_load.called

            # Reset mock
            mock_load.reset_mock()

            # Second load should use cache
            load_translations()
            assert not mock_load.called

def test_supported_locales_consistency():
    """Test SUPPORTED_LOCALES configuration."""
    # Verify all supported locales are properly configured
    for locale in SUPPORTED_LOCALES:
        assert isinstance(locale, str)
        assert len(locale) >= 2

    # Verify DEFAULT_LOCALE is in SUPPORTED_LOCALES
    assert DEFAULT_LOCALE in SUPPORTED_LOCALES

def test_concurrent_translation_access():
    """Test concurrent access to translations."""
    # Simulate concurrent access
    messages = ["Test message", "Another message", "Third message"]
    locales = ["en", "es", "de", None]

    from concurrent.futures import ThreadPoolExecutor
    import random

    def random_translate():
        msg = random.choice(messages)
        locale = random.choice(locales)
        return _(msg, locale)

    with ThreadPoolExecutor(max_workers=4) as executor:
        results = list(executor.map(lambda _: random_translate(), range(100)))

    # Verify all translations completed without errors
    assert len(results) == 100
    assert all(isinstance(r, str) for r in results)

def test_malformed_translation_files():
    """Test handling of malformed translation files."""
    # Create a mock translation file with invalid JSON
    malformed_content = b'{"key": "value", invalid json}'

    with patch('builtins.open', create=True) as mock_open:
        mock_open.return_value.__enter__ = lambda s: s
        mock_open.return_value.__exit__ = lambda s, *args, **kwargs: None
        mock_open.return_value.read.return_value = malformed_content

        # Should handle malformed files gracefully
        load_translations()
        assert _("Test message", "es") == "Test message"

def test_cache_invalidation():
    """Test translation cache invalidation."""
    with patch.dict(os.environ, {"TESTING": "false"}):
        # Load initial translations
        load_translations()

        # Mock translation file modification
        with patch('babel.support.Translations.load') as mock_load:
            mock_load.return_value = MagicMock(gettext=lambda x: "New translation")

            # Clear cache and reload
            clear_translation_cache()
            load_translations()

            # Should get new translations
            assert mock_load.called
            assert _("Test message", "es") != "Test message"

def test_thread_safety():
    """Test thread safety of translation loading."""
    def load_and_translate():
        load_translations()
        return _("Test message", "en")

    # Create multiple threads to load translations simultaneously
    threads = []
    results = []

    for _ in range(10):
        thread = threading.Thread(target=lambda: results.append(load_and_translate()))
        threads.append(thread)
        thread.start()

    # Wait for all threads to complete
    for thread in threads:
        thread.join()

    # All results should be consistent
    assert len(set(results)) == 1
    assert all(r == "Test message" for r in results)