"""Tests for MITRE ATT&CK Framework endpoints."""
import pytest
from datetime import datetime
from sqlalchemy.orm import Session
from api.models.mitre import <PERSON><PERSON><PERSON><PERSON><PERSON>, MitreTechnique, MitreTactic
from api.utils.mitre_import import import_mitre_data
from api.database import init_db

@pytest.fixture(scope="session", autouse=True)
def setup_test_database():
    """Initialize test database with fresh schema."""
    # Initialize fresh schema using clean parameter
    try:
        init_db(clean=True)
    except Exception as e:
        pytest.fail(f"Failed to initialize test database: {str(e)}")

@pytest.fixture
def test_data(db_session):
    """Create a test version and verify it exists."""
    try:
        version = MitreVersion(
            version="test_version",
            name="Test Version",
            description="Test version description",
            import_date=datetime.utcnow(),
            is_current=True,
            technology_domain='enterprise'
        )
        db_session.add(version)
        db_session.commit()
        db_session.refresh(version)

        technique = MitreTechnique(
            technique_id="T1566",
            name="Phishing",
            description="Test technique",
            version_id=version.id,
            detection="Monitoring for suspicious email patterns",
            platforms=["Windows", "macOS", "Linux"]
        )
        db_session.add(technique)
        db_session.commit()
        db_session.refresh(technique)

        return {"version": version, "technique": technique}
    except Exception as e:
        pytest.fail(f"Failed to create test data: {str(e)}")

def test_soft_delete_functionality(db_session, test_data):
    """Test soft delete functionality."""
    technique = test_data["technique"]

    # Verify initial state
    assert technique.deleted_at is None
    assert technique.created_on is not None
    assert technique.updated_at is not None

    # Mark as deleted
    technique.deleted_at = datetime.utcnow()
    db_session.commit()

    # Verify technique is marked as deleted but still exists
    db_technique = db_session.query(MitreTechnique).filter_by(id=technique.id).first()
    assert db_technique is not None
    assert db_technique.deleted_at is not None
    assert db_technique.created_on is not None
    assert db_technique.updated_at is not None

def test_mitre_import(client, db_session):
    """Test MITRE data import functionality"""
    response = client.post("/api/v1/mitre/import")
    assert response.status_code == 200

    versions = db_session.query(MitreVersion).all()
    assert len(versions) > 0

    techniques = db_session.query(MitreTechnique).all() 
    assert len(techniques) > 0

    tactics = db_session.query(MitreTactic).all()
    assert len(tactics) > 0

def test_import_mitre_data(client, db_session):
    """Test importing MITRE data."""
    response = client.post("/api/v1/mitre/import")
    assert response.status_code == 200
    data = response.json()
    assert "version" in data["data"]

    # Verify database state
    version = db_session.query(MitreVersion).first()
    assert version is not None
    assert version.is_current is True

    # Check if techniques were imported
    techniques = db_session.query(MitreTechnique).all()
    assert len(techniques) > 0
    for technique in techniques:
        assert technique.technique_id.startswith("T")
        assert technique.created_on is not None
        assert technique.deleted_at is None

def test_import_mitre_data_with_version(client, db_session):
    """Test importing MITRE data with specific version."""
    response = client.post("/api/v1/mitre/import?version=custom_version")
    assert response.status_code == 200
    data = response.json()
    assert data["data"]["version"] == "custom_version"

    version = db_session.query(MitreVersion).filter_by(version="custom_version").first()
    assert version is not None
    assert version.is_current is True
    assert version.created_on is not None

def test_list_versions(client, db_session):
    """Test listing MITRE versions."""
    # Import test data
    v1 = import_mitre_data(db_session, version="test_v1", set_as_current=False)
    db_session.commit()
    v2 = import_mitre_data(db_session, version="test_v2", set_as_current=True)
    db_session.commit()

    response = client.get("/api/v1/mitre/versions")
    assert response.status_code == 200
    versions = response.json()

    assert len(versions) >= 2
    assert any(v["version"] == "test_v1" for v in versions)
    assert any(v["version"] == "test_v2" for v in versions)
    assert sum(v["is_current"] for v in versions) == 1

def test_error_handling(client):
    """Test error handling in MITRE endpoints."""
    # Test invalid version ID
    response = client.get("/api/v1/mitre/techniques?version_id=999999")
    assert response.status_code == 200
    data = response.json()
    assert data["total"] == 0
    assert len(data["items"]) == 0

    # Test invalid pagination
    response = client.get("/api/v1/mitre/techniques?page=0")
    assert response.status_code == 422

    response = client.get("/api/v1/mitre/techniques?size=0")
    assert response.status_code == 422

    response = client.get("/api/v1/mitre/techniques?size=101")
    assert response.status_code == 422

def test_version_technology_domain(db_session, test_data):
    """Test technology domain enum field."""
    version = test_data["version"]
    assert version.technology_domain == 'enterprise'

    # Test creating version with different domain
    mobile_version = MitreVersion(
        version="mobile_test",
        name="Mobile Test",
        description="Mobile test version",
        technology_domain='mobile'
    )
    db_session.add(mobile_version)
    db_session.commit()

    assert mobile_version.technology_domain == 'mobile'

def test_technique_detection_platforms(db_session, test_data):
    """Test new technique fields."""
    technique = test_data["technique"]

    # Test detection field
    assert technique.detection == "Monitoring for suspicious email patterns"

    # Test platforms JSON field
    assert isinstance(technique.platforms, list)
    assert "Windows" in technique.platforms
    assert len(technique.platforms) == 3

def test_version_relationships(db_session, test_data):
    """Test version relationships and cascade deletes."""
    version = test_data["version"]
    technique = test_data["technique"]

    # Add a tactic
    tactic = MitreTactic(
        external_id="TA0001",
        name="Initial Access",
        description="Test tactic",
        version_id=version.id
    )
    db_session.add(tactic)
    db_session.commit()

    # Associate technique with tactic
    technique.tactic_id = tactic.id
    db_session.commit()

    # Verify relationships
    assert db_session.query(MitreTechnique).filter_by(tactic_id=tactic.id).first() is not None

    # Test cascade delete
    db_session.delete(version)
    db_session.commit()

    # Verify related records are deleted
    assert db_session.query(MitreTechnique).filter_by(id=technique.id).first() is None
    assert db_session.query(MitreTactic).filter_by(id=tactic.id).first() is None

def test_mitre_import_with_domains(client, db_session):
    """Test importing MITRE data with different technology domains."""
    # Import enterprise data
    response = client.post("/api/v1/mitre/import?domain=enterprise")
    assert response.status_code == 200
    data = response.json()
    assert "version" in data["data"]

    # Import mobile data
    response = client.post("/api/v1/mitre/import?domain=mobile")
    assert response.status_code == 200
    data = response.json()
    assert "version" in data["data"]

    # Verify domain-specific imports
    enterprise_version = db_session.query(MitreVersion).filter_by(technology_domain='enterprise').first()
    mobile_version = db_session.query(MitreVersion).filter_by(technology_domain='mobile').first()

    assert enterprise_version is not None
    assert mobile_version is not None
    assert enterprise_version.id != mobile_version.id

def test_technique_validation(db_session, test_data):
    """Test technique data validation."""
    version = test_data["version"]

    # Test invalid platforms format
    with pytest.raises(ValueError):
        invalid_technique = MitreTechnique(
            technique_id="T1567",
            name="Invalid Technique",
            description="Test invalid technique",
            version_id=version.id,
            platforms="not_a_list"  # Should be a list
        )
        db_session.add(invalid_technique)
        db_session.commit()

    # Test valid technique creation
    valid_technique = MitreTechnique(
        technique_id="T1567",
        name="Valid Technique",
        description="Test valid technique",
        version_id=version.id,
        platforms=["Windows"],
        detection="Valid detection string"
    )
    db_session.add(valid_technique)
    db_session.commit()

    assert valid_technique.id is not None