
"""Tests for MITRE relationship endpoints."""
import pytest
from fastapi.testclient import TestClient

def test_create_technique_relationship(client, db_session):
    """Test creating a relationship between techniques."""
    # Import test data first and get available technique IDs
    response = client.post("/api/v1/mitre/import")
    assert response.status_code == 200
    
    # Get techniques to use their actual IDs
    techniques = client.get("/api/v1/mitre/techniques").json()
    assert len(techniques["items"]) >= 2
    
    source_id = techniques["items"][0]["id"]
    target_id = techniques["items"][1]["id"]
    
    response = client.post(
        "/api/v1/mitre/techniques/relationships",
        json={
            "source_technique_id": source_id,
            "target_technique_id": target_id,
            "relationship_type": "related-to"
        }
    )
    assert response.status_code == 201
    data = response.json()
    assert data["source_technique_id"] == source_id
    assert data["target_technique_id"] == target_id

def test_get_technique_relationships(client, db_session):
    """Test getting relationships for a technique."""
    response = client.get("/api/v1/mitre/techniques/T1001/relationships")
    assert response.status_code == 200
    data = response.json()
    assert "relationships" in data
    assert len(data["relationships"]) > 0
    assert data["relationships"][0]["source_technique_id"] == "T1001"

def test_create_invalid_relationship(client):
    """Test creating relationship with invalid technique IDs."""
    response = client.post(
        "/api/v1/mitre/techniques/relationships",
        json={
            "source_technique_id": "INVALID",
            "target_technique_id": "T1002",
            "relationship_type": "related-to"
        }
    )
    assert response.status_code == 404
