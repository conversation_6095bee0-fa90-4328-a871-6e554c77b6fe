"""
API tests for the testcase chaining endpoints.

This module contains tests for the API endpoints related to the Enhanced
Testcase Chaining & Sequencing feature.
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.main import app
from api.models.base import TestCaseDB, CampaignDB
from api.models.testcase_chaining import TestcaseChainDB
from api.auth.router import create_access_token


@pytest.fixture
def client():
    """Create a test client for the FastAPI app."""
    return TestClient(app)


@pytest.fixture
def test_campaign(db_session: Session):
    """Create a test campaign for use in tests."""
    campaign = CampaignDB(name="Test Campaign", description="Test Campaign Description")
    db_session.add(campaign)
    db_session.commit()
    db_session.refresh(campaign)
    return campaign


@pytest.fixture
def test_case(db_session: Session, test_campaign):
    """Create a test case for use in tests."""
    test_case = TestCaseDB(
        name="Test Case",
        description="Test Case Description",
        campaign_id=test_campaign.id,
        expected_result="Expected Result",
        status="pending"
    )
    db_session.add(test_case)
    db_session.commit()
    db_session.refresh(test_case)
    return test_case


@pytest.fixture
def test_user_id():
    """Return a test user ID for use in tests."""
    return 1  # Assuming user with ID 1 exists


@pytest.fixture
def auth_headers(test_user_id):
    """Create authentication headers for API requests."""
    token = create_access_token({"sub": str(test_user_id), "role": "admin"})
    return {"Authorization": f"Bearer {token}"}


def test_create_chain(client, auth_headers, test_user_id):
    """Test creating a testcase chain via the API."""
    response = client.post(
        "/api/v1/testcase-chains/",
        json={
            "name": "API Test Chain",
            "description": "API Test Chain Description",
            "created_by": test_user_id,
            "status": "draft"
        },
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "API Test Chain"
    assert data["description"] == "API Test Chain Description"
    assert data["status"] == "draft"
    assert "id" in data
    assert "created_time" in data
    assert "updated_time" in data


def test_get_chains(client, db_session, auth_headers, test_user_id):
    """Test getting all testcase chains via the API."""
    # Create a chain in the database
    chain = TestcaseChainDB(
        name="Test Chain",
        description="Test Chain Description",
        created_by=test_user_id,
        status="draft"
    )
    db_session.add(chain)
    db_session.commit()
    
    response = client.get(
        "/api/v1/testcase-chains/",
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) >= 1
    assert any(c["name"] == "Test Chain" for c in data)


def test_get_chain(client, db_session, auth_headers, test_user_id):
    """Test getting a specific testcase chain via the API."""
    # Create a chain in the database
    chain = TestcaseChainDB(
        name="Test Chain",
        description="Test Chain Description",
        created_by=test_user_id,
        status="draft"
    )
    db_session.add(chain)
    db_session.commit()
    db_session.refresh(chain)
    
    response = client.get(
        f"/api/v1/testcase-chains/{chain.id}",
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == chain.id
    assert data["name"] == "Test Chain"
    assert data["description"] == "Test Chain Description"
    assert data["status"] == "draft"


def test_update_chain(client, db_session, auth_headers, test_user_id):
    """Test updating a testcase chain via the API."""
    # Create a chain in the database
    chain = TestcaseChainDB(
        name="Test Chain",
        description="Test Chain Description",
        created_by=test_user_id,
        status="draft"
    )
    db_session.add(chain)
    db_session.commit()
    db_session.refresh(chain)
    
    response = client.put(
        f"/api/v1/testcase-chains/{chain.id}",
        json={
            "name": "Updated Chain",
            "status": "active"
        },
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == chain.id
    assert data["name"] == "Updated Chain"
    assert data["description"] == "Test Chain Description"  # Unchanged
    assert data["status"] == "active"  # Changed


def test_delete_chain(client, db_session, auth_headers, test_user_id):
    """Test deleting a testcase chain via the API."""
    # Create a chain in the database
    chain = TestcaseChainDB(
        name="Test Chain",
        description="Test Chain Description",
        created_by=test_user_id,
        status="draft"
    )
    db_session.add(chain)
    db_session.commit()
    db_session.refresh(chain)
    
    response = client.delete(
        f"/api/v1/testcase-chains/{chain.id}",
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "Chain deleted successfully"
    
    # Verify the chain is soft-deleted
    db_chain = db_session.query(TestcaseChainDB).filter(TestcaseChainDB.id == chain.id).first()
    assert db_chain.deleted_time is not None


def test_create_node(client, db_session, auth_headers, test_user_id, test_case):
    """Test creating a testcase chain node via the API."""
    # Create a chain in the database
    chain = TestcaseChainDB(
        name="Test Chain",
        description="Test Chain Description",
        created_by=test_user_id,
        status="draft"
    )
    db_session.add(chain)
    db_session.commit()
    db_session.refresh(chain)
    
    response = client.post(
        "/api/v1/testcase-chains/nodes",
        json={
            "chain_id": chain.id,
            "testcase_id": test_case.id,
            "node_type": "standard",
            "position_x": 100.0,
            "position_y": 100.0,
            "execution_order": 1
        },
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["chain_id"] == chain.id
    assert data["testcase_id"] == test_case.id
    assert data["node_type"] == "standard"
    assert data["position_x"] == 100.0
    assert data["position_y"] == 100.0
    assert data["execution_order"] == 1
    assert "id" in data
    assert "created_time" in data
    assert "updated_time" in data


def test_create_edge(client, db_session, auth_headers, test_user_id, test_case):
    """Test creating a testcase chain edge via the API."""
    # Create a chain in the database
    chain = TestcaseChainDB(
        name="Test Chain",
        description="Test Chain Description",
        created_by=test_user_id,
        status="draft"
    )
    db_session.add(chain)
    db_session.commit()
    db_session.refresh(chain)
    
    # Create two nodes
    from api.models.testcase_chaining import TestcaseChainNodeDB
    node1 = TestcaseChainNodeDB(
        chain_id=chain.id,
        testcase_id=test_case.id,
        node_type="standard",
        position_x=100.0,
        position_y=100.0,
        execution_order=1
    )
    node2 = TestcaseChainNodeDB(
        chain_id=chain.id,
        testcase_id=test_case.id,
        node_type="standard",
        position_x=200.0,
        position_y=200.0,
        execution_order=2
    )
    db_session.add(node1)
    db_session.add(node2)
    db_session.commit()
    db_session.refresh(node1)
    db_session.refresh(node2)
    
    response = client.post(
        "/api/v1/testcase-chains/edges",
        json={
            "source_node_id": node1.id,
            "target_node_id": node2.id,
            "edge_type": "standard"
        },
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["source_node_id"] == node1.id
    assert data["target_node_id"] == node2.id
    assert data["edge_type"] == "standard"
    assert "id" in data
    assert "created_time" in data
    assert "updated_time" in data


def test_create_condition(client, db_session, auth_headers, test_case):
    """Test creating a testcase condition via the API."""
    response = client.post(
        "/api/v1/testcase-chains/conditions",
        json={
            "testcase_id": test_case.id,
            "condition_type": "precondition",
            "name": "API Test Condition",
            "description": "API Test Condition Description",
            "validation_script": "return True",
            "required": True
        },
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["testcase_id"] == test_case.id
    assert data["condition_type"] == "precondition"
    assert data["name"] == "API Test Condition"
    assert data["description"] == "API Test Condition Description"
    assert data["validation_script"] == "return True"
    assert data["required"] is True
    assert "id" in data
    assert "created_time" in data
    assert "updated_time" in data 