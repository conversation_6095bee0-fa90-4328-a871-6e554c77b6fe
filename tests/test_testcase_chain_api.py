"""
Tests for the Testcase Chain API endpoints.

This module contains comprehensive tests for the testcase chain REST API,
including chain management, node operations, edge creation, and execution control.
"""

import pytest
from datetime import datetime
from unittest.mock import Mock, patch

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.main import app
from api.models.testcase_chain import Testcase<PERSON>hain, TestcaseChainNode, TestcaseChainEdge
from api.models.user import User


@pytest.fixture
def client():
    """Create a test client."""
    return TestClient(app)


@pytest.fixture
def mock_db():
    """Create a mock database session."""
    return Mock(spec=Session)


@pytest.fixture
def mock_user():
    """Create a mock user."""
    user = Mock(spec=User)
    user.id = 1
    user.username = "testuser"
    user.is_admin = False
    return user


@pytest.fixture
def sample_chain_data():
    """Sample chain data for testing."""
    return {
        "name": "Test Attack Chain",
        "description": "Sample attack chain for testing",
        "chain_type": "sequential",
        "max_execution_time_minutes": 90,
        "retry_on_failure": True,
        "auto_cleanup": False
    }


@pytest.fixture
def sample_chain():
    """Create a sample testcase chain."""
    return TestcaseChain(
        id=1,
        name="Test Attack Chain",
        description="Sample attack chain for testing",
        status="draft",
        chain_type="sequential",
        max_execution_time_minutes=90,
        retry_on_failure=True,
        auto_cleanup=False,
        created_by=1,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )


@pytest.fixture
def sample_node_data():
    """Sample node data for testing."""
    return {
        "testcase_id": 101,
        "node_type": "start",
        "execution_order": 1,
        "position_x": 100.0,
        "position_y": 100.0,
        "timeout_minutes": 30,
        "retry_count": 2,
        "continue_on_failure": True,
        "required_for_completion": False
    }


@pytest.fixture
def sample_edge_data():
    """Sample edge data for testing."""
    return {
        "source_node_id": 1,
        "target_node_id": 2,
        "edge_type": "success_path",
        "condition": "result == 'success'",
        "weight": 2,
        "label": "Success Path",
        "description": "Path taken on success"
    }


class TestTestcaseChainAPI:
    """Test cases for testcase chain API endpoints."""
    
    @patch('api.routes.v1_testcase_chain.get_db')
    @patch('api.routes.v1_testcase_chain.get_current_user')
    def test_list_chains(self, mock_get_user, mock_get_db, client, mock_user, mock_db, sample_chain):
        """Test listing testcase chains."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.order_by.return_value.offset.return_value.limit.return_value.all.return_value = [sample_chain]
        
        response = client.get("/testcase-chains/chains")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["name"] == "Test Attack Chain"
        assert data[0]["chain_type"] == "sequential"
    
    @patch('api.routes.v1_testcase_chain.get_db')
    @patch('api.routes.v1_testcase_chain.get_current_user')
    def test_list_chains_with_filters(self, mock_get_user, mock_get_db, client, mock_user, mock_db):
        """Test listing chains with filters."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.order_by.return_value.offset.return_value.limit.return_value.all.return_value = []
        
        response = client.get("/testcase-chains/chains?status=active&chain_type=sequential&created_by=1")
        
        assert response.status_code == 200
        # Verify filters were applied
        assert mock_db.query.return_value.filter.call_count >= 3
    
    @patch('api.routes.v1_testcase_chain.get_db')
    @patch('api.routes.v1_testcase_chain.get_current_user')
    @patch('api.routes.v1_testcase_chain.TestcaseChainService')
    def test_create_chain_success(self, mock_service_class, mock_get_user, mock_get_db, 
                                  client, mock_user, mock_db, sample_chain_data, sample_chain):
        """Test successful chain creation."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.create_chain.return_value = sample_chain
        
        response = client.post("/testcase-chains/chains", json=sample_chain_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "Test Attack Chain"
        assert data["chain_type"] == "sequential"
        assert data["max_execution_time_minutes"] == 90
        
        mock_service.create_chain.assert_called_once()
    
    @patch('api.routes.v1_testcase_chain.get_db')
    @patch('api.routes.v1_testcase_chain.get_current_user')
    @patch('api.routes.v1_testcase_chain.TestcaseChainService')
    def test_create_chain_failure(self, mock_service_class, mock_get_user, mock_get_db, 
                                  client, mock_user, mock_db, sample_chain_data):
        """Test chain creation failure."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.create_chain.side_effect = Exception("Database error")
        
        response = client.post("/testcase-chains/chains", json=sample_chain_data)
        
        assert response.status_code == 400
        assert "Failed to create testcase chain" in response.json()["detail"]
    
    @patch('api.routes.v1_testcase_chain.get_db')
    @patch('api.routes.v1_testcase_chain.get_current_user')
    def test_get_chain_success(self, mock_get_user, mock_get_db, client, mock_user, mock_db, sample_chain):
        """Test getting a specific chain."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = sample_chain
        mock_db.query.return_value.filter.return_value.order_by.return_value.all.return_value = []  # No nodes
        mock_db.query.return_value.join.return_value.filter.return_value.all.return_value = []  # No edges
        
        response = client.get("/testcase-chains/chains/1")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == 1
        assert data["name"] == "Test Attack Chain"
        assert "nodes" in data
        assert "edges" in data
    
    @patch('api.routes.v1_testcase_chain.get_db')
    @patch('api.routes.v1_testcase_chain.get_current_user')
    def test_get_chain_not_found(self, mock_get_user, mock_get_db, client, mock_user, mock_db):
        """Test getting a non-existent chain."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        response = client.get("/testcase-chains/chains/999")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    @patch('api.routes.v1_testcase_chain.get_db')
    @patch('api.routes.v1_testcase_chain.get_current_user')
    def test_update_chain_success(self, mock_get_user, mock_get_db, client, mock_user, mock_db, sample_chain):
        """Test successful chain update."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = sample_chain
        
        update_data = {"name": "Updated Chain Name", "description": "Updated description"}
        response = client.put("/testcase-chains/chains/1", json=update_data)
        
        assert response.status_code == 200
        assert sample_chain.name == "Updated Chain Name"
        assert sample_chain.description == "Updated description"
        mock_db.add.assert_called_once_with(sample_chain)
        mock_db.commit.assert_called_once()
    
    @patch('api.routes.v1_testcase_chain.get_db')
    @patch('api.routes.v1_testcase_chain.get_current_user')
    def test_delete_chain_success(self, mock_get_user, mock_get_db, client, mock_user, mock_db, sample_chain):
        """Test successful chain deletion."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = sample_chain
        
        response = client.delete("/testcase-chains/chains/1")
        
        assert response.status_code == 204
        mock_db.add.assert_called_once_with(sample_chain)
        mock_db.commit.assert_called_once()
    
    @patch('api.routes.v1_testcase_chain.get_db')
    @patch('api.routes.v1_testcase_chain.get_current_user')
    @patch('api.routes.v1_testcase_chain.TestcaseChainService')
    def test_validate_chain_success(self, mock_service_class, mock_get_user, mock_get_db, 
                                    client, mock_user, mock_db):
        """Test successful chain validation."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.validate_chain.return_value = Mock(
            is_valid=True,
            errors=[],
            warnings=[],
            cycle_detected=False,
            unreachable_nodes=[]
        )
        
        response = client.get("/testcase-chains/chains/1/validate")
        
        assert response.status_code == 200
        data = response.json()
        assert data["is_valid"] is True
        assert len(data["errors"]) == 0
        assert data["cycle_detected"] is False
    
    @patch('api.routes.v1_testcase_chain.get_db')
    @patch('api.routes.v1_testcase_chain.get_current_user')
    @patch('api.routes.v1_testcase_chain.TestcaseChainService')
    def test_add_node_to_chain_success(self, mock_service_class, mock_get_user, mock_get_db, 
                                       client, mock_user, mock_db, sample_node_data):
        """Test successfully adding a node to a chain."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_node = TestcaseChainNode(
            id=1,
            chain_id=1,
            testcase_id=101,
            node_type="start",
            execution_order=1
        )
        mock_service.add_node_to_chain.return_value = mock_node
        
        response = client.post("/testcase-chains/chains/1/nodes", json=sample_node_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["testcase_id"] == 101
        assert data["node_type"] == "start"
        
        mock_service.add_node_to_chain.assert_called_once()
    
    @patch('api.routes.v1_testcase_chain.get_db')
    @patch('api.routes.v1_testcase_chain.get_current_user')
    @patch('api.routes.v1_testcase_chain.TestcaseChainService')
    def test_add_node_to_chain_failure(self, mock_service_class, mock_get_user, mock_get_db, 
                                       client, mock_user, mock_db, sample_node_data):
        """Test adding a node to a chain failure."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.add_node_to_chain.side_effect = ValueError("Chain not found")
        
        response = client.post("/testcase-chains/chains/1/nodes", json=sample_node_data)
        
        assert response.status_code == 400
        assert "Chain not found" in response.json()["detail"]
    
    @patch('api.routes.v1_testcase_chain.get_db')
    @patch('api.routes.v1_testcase_chain.get_current_user')
    def test_update_node_success(self, mock_get_user, mock_get_db, client, mock_user, mock_db):
        """Test successful node update."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        
        mock_node = TestcaseChainNode(
            id=1,
            chain_id=1,
            testcase_id=101,
            node_type="start",
            execution_order=1
        )
        mock_db.query.return_value.filter.return_value.first.return_value = mock_node
        
        update_data = {"node_type": "standard", "execution_order": 2}
        response = client.put("/testcase-chains/nodes/1", json=update_data)
        
        assert response.status_code == 200
        assert mock_node.node_type == "standard"
        assert mock_node.execution_order == 2
        mock_db.add.assert_called_once_with(mock_node)
        mock_db.commit.assert_called_once()
    
    @patch('api.routes.v1_testcase_chain.get_db')
    @patch('api.routes.v1_testcase_chain.get_current_user')
    def test_delete_node_success(self, mock_get_user, mock_get_db, client, mock_user, mock_db):
        """Test successful node deletion."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        
        mock_node = TestcaseChainNode(id=1, chain_id=1, testcase_id=101)
        mock_db.query.return_value.filter.return_value.first.return_value = mock_node
        
        response = client.delete("/testcase-chains/nodes/1")
        
        assert response.status_code == 204
        mock_db.add.assert_called_once_with(mock_node)
        mock_db.commit.assert_called_once()
    
    @patch('api.routes.v1_testcase_chain.get_db')
    @patch('api.routes.v1_testcase_chain.get_current_user')
    @patch('api.routes.v1_testcase_chain.TestcaseChainService')
    def test_create_edge_success(self, mock_service_class, mock_get_user, mock_get_db, 
                                 client, mock_user, mock_db, sample_edge_data):
        """Test successfully creating an edge."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_edge = TestcaseChainEdge(
            id=1,
            source_node_id=1,
            target_node_id=2,
            edge_type="success_path"
        )
        mock_service.add_edge_to_chain.return_value = mock_edge
        
        response = client.post("/testcase-chains/edges", json=sample_edge_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["source_node_id"] == 1
        assert data["target_node_id"] == 2
        assert data["edge_type"] == "success_path"
        
        mock_service.add_edge_to_chain.assert_called_once()
    
    @patch('api.routes.v1_testcase_chain.get_db')
    @patch('api.routes.v1_testcase_chain.get_current_user')
    @patch('api.routes.v1_testcase_chain.TestcaseChainService')
    def test_create_edge_failure(self, mock_service_class, mock_get_user, mock_get_db, 
                                 client, mock_user, mock_db, sample_edge_data):
        """Test edge creation failure."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.add_edge_to_chain.side_effect = ValueError("Nodes in different chains")
        
        response = client.post("/testcase-chains/edges", json=sample_edge_data)
        
        assert response.status_code == 400
        assert "Nodes in different chains" in response.json()["detail"]
