# Enhanced Testcase Chaining & Sequencing - Test Suite

## Overview

This document describes the comprehensive test suite for the Enhanced Testcase Chaining & Sequencing feature. The test suite includes unit tests, integration tests, behavioral tests, end-to-end tests, and performance tests to ensure the reliability and scalability of the chaining system.

## Test Structure

### Test Files

1. **`test_testcase_chain_service.py`** - Unit tests for the service layer
2. **`test_testcase_chain_api.py`** - API endpoint tests
3. **`test_testcase_chain_integration.py`** - Integration tests with real database
4. **`test_testcase_chain_behavior.py`** - Behavioral tests for attack scenarios
5. **`test_testcase_chain_e2e.py`** - End-to-end workflow tests
6. **`test_testcase_chain_performance.py`** - Performance and load tests
7. **`conftest.py`** - Shared fixtures and test configuration
8. **`run_chain_tests.py`** - Test runner script

### Test Categories

#### 🧪 Unit Tests (`test_testcase_chain_service.py`)
- **Purpose**: Test individual service methods in isolation
- **Coverage**: TestcaseChainService class methods
- **Mocking**: Database operations are mocked
- **Focus**: Business logic validation, error handling, edge cases

**Key Test Cases:**
- Chain creation and validation
- Node addition with various configurations
- Edge creation with conditional logic
- Cycle detection algorithms
- Dependency resolution
- Validation error scenarios

#### 🌐 API Tests (`test_testcase_chain_api.py`)
- **Purpose**: Test REST API endpoints
- **Coverage**: All chain management endpoints
- **Mocking**: Service layer and database
- **Focus**: HTTP responses, request validation, error handling

**Key Test Cases:**
- Chain CRUD operations
- Node management endpoints
- Edge creation and updates
- Chain validation endpoint
- Execution control endpoints
- Error response scenarios

#### 🔗 Integration Tests (`test_testcase_chain_integration.py`)
- **Purpose**: Test complete workflows with real database
- **Coverage**: End-to-end chain lifecycle
- **Database**: Real PostgreSQL test container
- **Focus**: Data persistence, transaction handling, complex scenarios

**Key Test Cases:**
- Complete chain lifecycle (create → add nodes → add edges → validate → execute)
- Parallel chain execution
- Conditional chain execution
- Complex dependency resolution
- Database transaction integrity

#### 🎭 Behavioral Tests (`test_testcase_chain_behavior.py`)
- **Purpose**: Test real-world attack scenarios
- **Coverage**: Attack pattern modeling
- **Focus**: Realistic use cases, complex workflows

**Key Test Cases:**
- Advanced Persistent Threat (APT) scenarios
- Red Team vs Blue Team exercises
- Compliance testing workflows
- Incident response simulations
- Penetration testing methodologies

#### 🔄 End-to-End Tests (`test_testcase_chain_e2e.py`)
- **Purpose**: Test complete user workflows via API
- **Coverage**: Full API interaction patterns
- **Focus**: User experience, workflow completion

**Key Test Cases:**
- Complete attack chain creation workflow
- Chain management lifecycle
- Error handling scenarios
- Multi-user interactions
- Concurrent operations

#### ⚡ Performance Tests (`test_testcase_chain_performance.py`)
- **Purpose**: Test system performance and scalability
- **Coverage**: Large-scale operations
- **Focus**: Response times, memory usage, concurrent operations

**Key Test Cases:**
- Large chain creation (1000+ nodes)
- Chain validation performance
- Cycle detection in complex graphs
- Concurrent chain operations
- Memory usage optimization
- Dependency resolution performance

## Running Tests

### Prerequisites

```bash
# Install test dependencies
pip install pytest pytest-asyncio pytest-cov testcontainers psutil

# Ensure PostgreSQL is available for integration tests
docker pull postgres:13
```

### Quick Start

```bash
# Run all tests
python tests/run_chain_tests.py all

# Run quick tests (unit + API)
python tests/run_chain_tests.py quick

# Run specific test category
python tests/run_chain_tests.py unit
python tests/run_chain_tests.py integration
python tests/run_chain_tests.py behavior
```

### Test Runner Options

The `run_chain_tests.py` script provides several test execution modes:

#### Test Types
- **`unit`** - Unit tests only (fast)
- **`api`** - API tests only (fast)
- **`integration`** - Integration tests with database (medium)
- **`behavior`** - Behavioral tests for scenarios (medium)
- **`e2e`** - End-to-end workflow tests (slow)
- **`performance`** - Performance and load tests (slow)
- **`all`** - All tests with coverage report (comprehensive)
- **`quick`** - Unit and API tests only (fastest)
- **`slow`** - Integration, behavior, e2e, and performance (thorough)

#### Usage Examples

```bash
# Quick development testing
python tests/run_chain_tests.py quick

# Full test suite with coverage
python tests/run_chain_tests.py all

# Performance testing only
python tests/run_chain_tests.py performance

# Integration testing for CI/CD
python tests/run_chain_tests.py integration
```

### Manual Test Execution

You can also run tests manually using pytest:

```bash
# Run specific test file
pytest tests/test_testcase_chain_service.py -v

# Run with coverage
pytest tests/test_testcase_chain_*.py --cov=api.services.testcase_chain_service --cov-report=html

# Run specific test method
pytest tests/test_testcase_chain_service.py::TestTestcaseChainService::test_create_chain -v

# Run tests with specific markers
pytest -m "unit and not slow" -v
pytest -m "integration" -v
pytest -m "performance" -v
```

## Test Fixtures

### Core Fixtures (from `conftest.py`)

#### Database Fixtures
- **`db_session`** - Real database session for integration tests
- **`mock_db_session`** - Mocked database session for unit tests

#### User Fixtures
- **`test_user`** - Standard test user
- **`test_admin`** - Admin test user
- **`test_user_token`** - Authentication token for test user

#### Chain Fixtures
- **`sample_chain`** - Basic testcase chain
- **`sample_nodes`** - Chain nodes with different types
- **`sample_edges`** - Chain edges with various conditions
- **`sample_execution`** - Chain execution instance
- **`sample_node_executions`** - Node execution instances

#### Scenario Fixtures
- **`complex_chain_scenario`** - Multi-path conditional chain
- **`parallel_chain_scenario`** - Parallel execution chain

#### Service Fixtures
- **`testcase_chain_service`** - Service instance with real database
- **`api_client`** - FastAPI test client

## Test Data Patterns

### Chain Creation Patterns

```python
# Simple sequential chain
chain = service.create_chain(
    name="Simple Chain",
    chain_type="sequential",
    created_by=user.id
)

# Complex conditional chain
chain = service.create_chain(
    name="Complex Chain",
    chain_type="conditional",
    max_execution_time_minutes=180,
    retry_on_failure=True,
    created_by=user.id
)
```

### Node Creation Patterns

```python
# Start node
start_node = service.add_node_to_chain(
    chain_id=chain.id,
    testcase_id=testcase.id,
    node_type="start",
    execution_order=1
)

# Conditional node
conditional_node = service.add_node_to_chain(
    chain_id=chain.id,
    testcase_id=testcase.id,
    node_type="conditional",
    condition_expression="previous_result.status == 'success'",
    execution_order=2
)
```

### Edge Creation Patterns

```python
# Standard edge
edge = service.add_edge_to_chain(
    source_node_id=node1.id,
    target_node_id=node2.id,
    edge_type="standard"
)

# Conditional edge
conditional_edge = service.add_edge_to_chain(
    source_node_id=node1.id,
    target_node_id=node2.id,
    edge_type="conditional",
    condition="result.status == 'success'"
)
```

## Test Scenarios

### Attack Scenario Testing

#### APT (Advanced Persistent Threat)
```python
apt_stages = [
    "initial_access", "execution", "persistence", 
    "privilege_escalation", "defense_evasion", 
    "credential_access", "discovery", "lateral_movement", 
    "collection", "exfiltration"
]
```

#### Red Team Exercise
```python
red_team_phases = [
    "reconnaissance", "initial_access", "establish_foothold",
    "escalate_privileges", "move_laterally", "achieve_objectives"
]
```

#### Compliance Testing
```python
soc2_controls = [
    "security", "availability", "processing_integrity",
    "confidentiality", "privacy"
]
```

## Performance Benchmarks

### Expected Performance Metrics

- **Chain Creation**: < 100ms for simple chains
- **Large Chain Validation**: < 5 seconds for 500 nodes
- **Cycle Detection**: < 2 seconds for 100 nodes with cycles
- **Concurrent Operations**: 10 concurrent chains in < 5 seconds
- **Memory Usage**: < 100MB additional for large chains

### Performance Test Categories

1. **Scalability Tests**: Large chains (1000+ nodes)
2. **Concurrency Tests**: Multiple simultaneous operations
3. **Memory Tests**: Memory usage optimization
4. **Algorithm Tests**: Cycle detection and dependency resolution

## Continuous Integration

### CI/CD Pipeline Integration

```yaml
# Example GitHub Actions workflow
- name: Run Chain Tests
  run: |
    python tests/run_chain_tests.py quick
    python tests/run_chain_tests.py integration
```

### Test Coverage Goals

- **Unit Tests**: > 95% coverage
- **Integration Tests**: > 90% coverage
- **API Tests**: 100% endpoint coverage
- **Overall**: > 90% combined coverage

## Troubleshooting

### Common Issues

1. **Database Connection**: Ensure PostgreSQL is running for integration tests
2. **Test Isolation**: Each test should clean up its data
3. **Mock Configuration**: Verify mock setups in unit tests
4. **Performance Variance**: Performance tests may vary by system

### Debug Commands

```bash
# Run with verbose output
pytest tests/test_testcase_chain_service.py -v -s

# Run specific failing test
pytest tests/test_testcase_chain_service.py::test_method_name -v --tb=long

# Run with pdb debugger
pytest tests/test_testcase_chain_service.py --pdb
```

## Contributing

### Adding New Tests

1. **Follow naming conventions**: `test_feature_scenario`
2. **Use appropriate fixtures**: Leverage existing fixtures
3. **Add proper markers**: Mark tests with appropriate categories
4. **Document complex scenarios**: Add docstrings for complex test cases
5. **Update this README**: Document new test patterns

### Test Quality Guidelines

- **Isolation**: Tests should not depend on each other
- **Clarity**: Test names should clearly describe what is being tested
- **Coverage**: Aim for comprehensive scenario coverage
- **Performance**: Keep unit tests fast (< 1 second each)
- **Reliability**: Tests should be deterministic and repeatable

## Summary

This comprehensive test suite ensures the Enhanced Testcase Chaining & Sequencing system is:

- ✅ **Functionally Correct**: Unit and integration tests verify all functionality
- ✅ **API Compliant**: API tests ensure proper REST interface behavior
- ✅ **Scenario Ready**: Behavioral tests validate real-world use cases
- ✅ **User Friendly**: E2E tests verify complete user workflows
- ✅ **Performance Optimized**: Performance tests ensure scalability
- ✅ **Production Ready**: Comprehensive coverage for enterprise deployment

The test suite provides confidence in the system's reliability, performance, and readiness for production deployment in enterprise environments.
