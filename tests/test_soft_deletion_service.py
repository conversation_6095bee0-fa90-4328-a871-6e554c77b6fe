"""
Tests for the Advanced Soft Deletion Service.

This module contains comprehensive tests for the soft deletion service,
including policy management, audit logging, and cascade operations.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from sqlalchemy.orm import Session

from api.models.soft_deletion import (
    SoftDeletionAudit,
    SoftDeletionNotification,
    SoftDeletionPolicy,
    SoftDeletionSchedule,
)
from api.services.soft_deletion_service import SoftDeletionService
from api.utils.soft_delete import AdvancedSoftDeleteMixin


class MockEntity(AdvancedSoftDeleteMixin):
    """Mock entity for testing soft deletion."""
    
    def __init__(self, entity_id: int):
        self.id = entity_id
        self.deleted_at = None
        self.deleted_by = None
        self.deletion_reason = None
        self.cascade_strategy = None


@pytest.fixture
def mock_db():
    """Create a mock database session."""
    return Mock(spec=Session)


@pytest.fixture
def soft_deletion_service(mock_db):
    """Create a soft deletion service instance."""
    return SoftDeletionService(mock_db)


@pytest.fixture
def sample_policy():
    """Create a sample soft deletion policy."""
    return SoftDeletionPolicy(
        id=1,
        entity_type="testcase",
        retention_period_days=30,
        auto_purge_enabled=True,
        cascade_deletion=True,
        notification_enabled=True,
        notification_days_before=7,
        description="Test policy"
    )


@pytest.fixture
def sample_entity():
    """Create a sample entity for testing."""
    return MockEntity(entity_id=123)


class TestSoftDeletionService:
    """Test cases for the SoftDeletionService class."""
    
    def test_get_policy_existing(self, soft_deletion_service, mock_db, sample_policy):
        """Test getting an existing policy."""
        mock_db.query.return_value.filter.return_value.first.return_value = sample_policy
        
        result = soft_deletion_service.get_policy("testcase")
        
        assert result == sample_policy
        mock_db.query.assert_called_once_with(SoftDeletionPolicy)
    
    def test_get_policy_not_found(self, soft_deletion_service, mock_db):
        """Test getting a non-existent policy."""
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        result = soft_deletion_service.get_policy("nonexistent")
        
        assert result is None
    
    def test_create_policy(self, soft_deletion_service, mock_db):
        """Test creating a new soft deletion policy."""
        result = soft_deletion_service.create_policy(
            entity_type="campaign",
            retention_period_days=60,
            auto_purge_enabled=True,
            cascade_deletion=False,
            notification_enabled=True,
            notification_days_before=14,
            description="Campaign policy",
            created_by=1
        )
        
        assert result.entity_type == "campaign"
        assert result.retention_period_days == 60
        assert result.auto_purge_enabled is True
        assert result.cascade_deletion is False
        assert result.notification_enabled is True
        assert result.notification_days_before == 14
        assert result.description == "Campaign policy"
        assert result.created_by == 1
        
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()
    
    def test_soft_delete_entity_success(self, soft_deletion_service, mock_db, sample_entity, sample_policy):
        """Test successful soft deletion of an entity."""
        mock_db.query.return_value.filter.return_value.first.return_value = sample_policy
        
        with patch.object(soft_deletion_service, '_schedule_purge') as mock_schedule_purge, \
             patch.object(soft_deletion_service, '_schedule_notifications') as mock_schedule_notifications:
            
            result = soft_deletion_service.soft_delete_entity(
                entity=sample_entity,
                entity_type="testcase",
                user_id=1,
                reason="Test deletion",
                cascade=True,
                ip_address="***********",
                user_agent="Test Agent"
            )
        
        # Verify entity was soft deleted
        assert sample_entity.is_deleted is True
        assert sample_entity.deleted_by == 1
        assert sample_entity.deletion_reason == "Test deletion"
        assert sample_entity.cascade_strategy == "cascade"
        
        # Verify audit record was created
        assert isinstance(result, SoftDeletionAudit)
        assert result.entity_type == "testcase"
        assert result.entity_id == 123
        assert result.operation_type == "soft_delete"
        assert result.performed_by == 1
        assert result.reason == "Test deletion"
        assert result.cascade_triggered is True
        assert result.ip_address == "***********"
        assert result.user_agent == "Test Agent"
        
        # Verify scheduling was called
        mock_schedule_purge.assert_called_once()
        mock_schedule_notifications.assert_called_once()
        
        mock_db.add.assert_called()
        mock_db.commit.assert_called()
    
    def test_soft_delete_entity_already_deleted(self, soft_deletion_service, sample_entity):
        """Test soft deleting an already deleted entity."""
        sample_entity.deleted_at = datetime.utcnow()
        
        with pytest.raises(ValueError, match="Entity testcase is already deleted"):
            soft_deletion_service.soft_delete_entity(
                entity=sample_entity,
                entity_type="testcase",
                user_id=1
            )
    
    def test_restore_entity_success(self, soft_deletion_service, mock_db, sample_entity):
        """Test successful restoration of a soft-deleted entity."""
        # Set up entity as deleted
        sample_entity.deleted_at = datetime.utcnow()
        sample_entity.deleted_by = 1
        sample_entity.deletion_reason = "Original deletion"
        
        with patch.object(soft_deletion_service, '_cancel_scheduled_operations') as mock_cancel:
            result = soft_deletion_service.restore_entity(
                entity=sample_entity,
                entity_type="testcase",
                user_id=2,
                reason="Restoration needed",
                ip_address="***********",
                user_agent="Restore Agent"
            )
        
        # Verify entity was restored
        assert sample_entity.is_deleted is False
        assert sample_entity.deleted_by is None
        assert sample_entity.deletion_reason is None
        
        # Verify audit record was created
        assert isinstance(result, SoftDeletionAudit)
        assert result.entity_type == "testcase"
        assert result.entity_id == 123
        assert result.operation_type == "restore"
        assert result.performed_by == 2
        assert result.reason == "Restoration needed"
        assert result.ip_address == "***********"
        
        # Verify scheduled operations were cancelled
        mock_cancel.assert_called_once_with("testcase", 123)
        
        mock_db.add.assert_called()
        mock_db.commit.assert_called()
    
    def test_restore_entity_not_deleted(self, soft_deletion_service, sample_entity):
        """Test restoring an entity that is not deleted."""
        with pytest.raises(ValueError, match="Entity testcase is not deleted"):
            soft_deletion_service.restore_entity(
                entity=sample_entity,
                entity_type="testcase",
                user_id=1
            )
    
    def test_permanently_delete_entity_success(self, soft_deletion_service, mock_db, sample_entity):
        """Test successful permanent deletion of a soft-deleted entity."""
        # Set up entity as deleted
        sample_entity.deleted_at = datetime.utcnow()
        
        with patch.object(soft_deletion_service, '_cancel_scheduled_operations') as mock_cancel:
            result = soft_deletion_service.permanently_delete_entity(
                entity=sample_entity,
                entity_type="testcase",
                user_id=1,
                reason="Permanent cleanup",
                ip_address="***********",
                user_agent="Admin Agent"
            )
        
        # Verify audit record was created
        assert isinstance(result, SoftDeletionAudit)
        assert result.entity_type == "testcase"
        assert result.entity_id == 123
        assert result.operation_type == "permanent_delete"
        assert result.performed_by == 1
        assert result.reason == "Permanent cleanup"
        
        # Verify scheduled operations were cancelled
        mock_cancel.assert_called_once_with("testcase", 123)
        
        # Verify entity was permanently deleted
        mock_db.delete.assert_called_once_with(sample_entity)
        mock_db.add.assert_called()
        mock_db.commit.assert_called()
    
    def test_permanently_delete_entity_not_soft_deleted(self, soft_deletion_service, sample_entity):
        """Test permanent deletion of an entity that is not soft-deleted."""
        with pytest.raises(ValueError, match="Entity testcase must be soft-deleted before permanent deletion"):
            soft_deletion_service.permanently_delete_entity(
                entity=sample_entity,
                entity_type="testcase",
                user_id=1
            )
    
    def test_schedule_purge(self, soft_deletion_service, mock_db, sample_entity, sample_policy):
        """Test scheduling a purge operation."""
        soft_deletion_service._schedule_purge(sample_entity, "testcase", sample_policy)
        
        # Verify schedule was created
        mock_db.add.assert_called_once()
        schedule_call = mock_db.add.call_args[0][0]
        
        assert isinstance(schedule_call, SoftDeletionSchedule)
        assert schedule_call.entity_type == "testcase"
        assert schedule_call.entity_id == 123
        assert schedule_call.operation_type == "purge"
        assert schedule_call.policy_id == 1
        
        # Verify scheduled date is correct (30 days from now)
        expected_date = datetime.utcnow() + timedelta(days=30)
        time_diff = abs((schedule_call.scheduled_for - expected_date).total_seconds())
        assert time_diff < 60  # Within 1 minute
    
    def test_schedule_notifications(self, soft_deletion_service, mock_db, sample_entity, sample_policy):
        """Test scheduling notifications."""
        soft_deletion_service._schedule_notifications(sample_entity, "testcase", sample_policy, user_id=1)
        
        # Verify notification was created
        mock_db.add.assert_called_once()
        notification_call = mock_db.add.call_args[0][0]
        
        assert isinstance(notification_call, SoftDeletionNotification)
        assert notification_call.entity_type == "testcase"
        assert notification_call.entity_id == 123
        assert notification_call.recipient_id == 1
        assert notification_call.notification_type == "purge_warning"
        assert notification_call.days_until_purge == 7
    
    def test_cancel_scheduled_operations(self, soft_deletion_service, mock_db):
        """Test cancelling scheduled operations."""
        # Mock scheduled operations
        mock_schedule1 = Mock()
        mock_schedule1.status = "pending"
        mock_schedule2 = Mock()
        mock_schedule2.status = "pending"
        
        mock_db.query.return_value.filter.return_value.all.return_value = [mock_schedule1, mock_schedule2]
        
        soft_deletion_service._cancel_scheduled_operations("testcase", 123)
        
        # Verify both schedules were cancelled
        assert mock_schedule1.status == "cancelled"
        assert mock_schedule2.status == "cancelled"
        
        # Verify they were added to the session
        assert mock_db.add.call_count == 2


class TestAdvancedSoftDeleteMixin:
    """Test cases for the AdvancedSoftDeleteMixin class."""

    def test_advanced_soft_delete_basic(self):
        """Test basic advanced soft delete functionality."""
        entity = MockEntity(1)

        entity.advanced_soft_delete(user_id=123, reason="Test deletion", cascade=True)

        assert entity.is_deleted is True
        assert entity.deleted_by == 123
        assert entity.deletion_reason == "Test deletion"
        assert entity.cascade_strategy == "cascade"

    def test_advanced_soft_delete_no_cascade(self):
        """Test advanced soft delete without cascade."""
        entity = MockEntity(1)

        entity.advanced_soft_delete(user_id=123, reason="Test deletion", cascade=False)

        assert entity.is_deleted is True
        assert entity.cascade_strategy == "none"

    def test_advanced_restore(self):
        """Test advanced restore functionality."""
        entity = MockEntity(1)
        entity.advanced_soft_delete(user_id=123, reason="Test deletion")

        entity.advanced_restore(user_id=456, reason="Test restoration")

        assert entity.is_deleted is False
        assert entity.deleted_by is None
        assert entity.deletion_reason is None
        assert entity.cascade_strategy is None

    def test_deletion_info(self):
        """Test deletion info property."""
        entity = MockEntity(1)

        # Test active entity
        info = entity.deletion_info
        assert info["is_deleted"] is False
        assert info["deleted_at"] is None
        assert info["deleted_by"] is None
        assert info["deletion_reason"] is None
        assert info["cascade_strategy"] is None

        # Test deleted entity
        entity.advanced_soft_delete(user_id=123, reason="Test deletion", cascade=True)
        info = entity.deletion_info
        assert info["is_deleted"] is True
        assert info["deleted_at"] is not None
        assert info["deleted_by"] == 123
        assert info["deletion_reason"] == "Test deletion"
        assert info["cascade_strategy"] == "cascade"
