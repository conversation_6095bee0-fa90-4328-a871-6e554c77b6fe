"""
Tests for error-handling feature
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from api.main import app
from models.error_handling import <PERSON><PERSON><PERSON><PERSON><PERSON>ling<PERSON><PERSON>, ErrorHandlingUpdate
from datetime import datetime
import json

client = TestClient(app)

@pytest.fixture
def sample_error_handling_data():
    """Fixture for sample error-handling data"""
    return {
        "name": "Test Error Handling",
        "description": "Test description"
    }

def test_get_all_error_handlings():
    """Test getting all error-handlings"""
    response = client.get("/api/v1/error-handling/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)

def test_get_error_handling_not_found():
    """Test getting a non-existent error-handling"""
    error_handling_id = 9999  # Assuming this ID doesn't exist
    response = client.get(f"/api/v1/error-handling/{error_handling_id}")
    assert response.status_code == 404

def test_create_error_handling(sample_error_handling_data):
    """Test creating a error-handling"""
    response = client.post("/api/v1/error-handling/", json=sample_error_handling_data)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == sample_error_handling_data["name"]
    assert data["description"] == sample_error_handling_data["description"]
    assert "id" in data
    assert "created_at" in data
    
    # Store the created ID for other tests
    return data["id"]

def test_update_error_handling(sample_error_handling_data):
    """Test updating a error-handling"""
    # First create a error-handling
    create_response = client.post("/api/v1/error-handling/", json=sample_error_handling_data)
    error_handling_id = create_response.json()["id"]
    
    # Now update it
    update_data = {
        "name": "Updated Error Handling",
        "description": "Updated description"
    }
    response = client.put(f"/api/v1/error-handling/{error_handling_id}", json=update_data)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == update_data["name"]
    assert data["description"] == update_data["description"]
    assert "updated_at" in data
    
    # Verify the update with a GET request
    get_response = client.get(f"/api/v1/error-handling/{error_handling_id}")
    assert get_response.status_code == 200
    assert get_response.json()["name"] == update_data["name"]

def test_delete_error_handling(sample_error_handling_data):
    """Test deleting a error-handling"""
    # First create a error-handling
    create_response = client.post("/api/v1/error-handling/", json=sample_error_handling_data)
    error_handling_id = create_response.json()["id"]
    
    # Now delete it
    response = client.delete(f"/api/v1/error-handling/{error_handling_id}")
    assert response.status_code == 200
    assert response.json()["message"] == "Error-handling deleted"
    
    # Verify it's gone with a GET request
    get_response = client.get(f"/api/v1/error-handling/{error_handling_id}")
    assert get_response.status_code == 404

def test_validation_errors():
    """Test validation errors when creating a error-handling"""
    # Missing required field
    invalid_data = {
        "description": "Missing name field"
    }
    response = client.post("/api/v1/error-handling/", json=invalid_data)
    assert response.status_code == 422
    
    # Empty name
    invalid_data = {
        "name": "",
        "description": "Empty name"
    }
    response = client.post("/api/v1/error-handling/", json=invalid_data)
    assert response.status_code == 422
