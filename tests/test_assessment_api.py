"""
Tests for the Assessment Management API.

This module contains tests for the assessment and test execution endpoints.
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from api.main import app
from api.models.assessment import Assessment, TestExecution
from api.models.schemas.assessment import AssessmentCreate, TestExecutionCreate
from api.services.assessment import create_assessment, create_test_execution
from api.auth.utils import create_access_token

client = TestClient(app)


@pytest.fixture
def db_session(monkeypatch):
    """Create a test database session."""
    # This would typically use a test database
    from api.database import get_db
    db = next(get_db())
    yield db


@pytest.fixture
def admin_token():
    """Create an admin token for testing."""
    access_token = create_access_token(
        data={"sub": "<EMAIL>", "role": "admin"},
        expires_delta=timedelta(minutes=30)
    )
    return access_token


@pytest.fixture
def user_token():
    """Create a regular user token for testing."""
    access_token = create_access_token(
        data={"sub": "<EMAIL>", "role": "user"},
        expires_delta=timedelta(minutes=30)
    )
    return access_token


@pytest.fixture
def test_assessment(db_session):
    """Create a test assessment."""
    assessment_data = AssessmentCreate(
        name="Test Assessment",
        description="Test assessment for API testing",
        target_system="Test System",
        assessment_type="vulnerability",
        status="planned",
        start_date=datetime.utcnow(),
        end_date=datetime.utcnow() + timedelta(days=7),
        campaign_id=1
    )
    
    assessment = create_assessment(db_session, assessment_data, user_id=1)
    return assessment


@pytest.fixture
def test_execution(db_session, test_assessment):
    """Create a test execution."""
    execution_data = TestExecutionCreate(
        test_case_id=1,
        assessment_id=test_assessment.id,
        result="pass",
        notes="Test execution notes",
        evidence="Test evidence"
    )
    
    execution = create_test_execution(db_session, execution_data, user_id=1)
    return execution


def test_get_assessments(admin_token):
    """Test getting a list of assessments."""
    response = client.get(
        "/api/v1/assessments/",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert isinstance(response.json(), list)


def test_get_assessment(admin_token, test_assessment):
    """Test getting a specific assessment."""
    response = client.get(
        f"/api/v1/assessments/{test_assessment.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert response.json()["id"] == test_assessment.id
    assert response.json()["name"] == test_assessment.name


def test_create_assessment(admin_token):
    """Test creating a new assessment."""
    assessment_data = {
        "name": "New Test Assessment",
        "description": "New test assessment for API testing",
        "target_system": "New Test System",
        "assessment_type": "vulnerability",
        "status": "planned",
        "start_date": datetime.utcnow().isoformat(),
        "end_date": (datetime.utcnow() + timedelta(days=7)).isoformat(),
        "campaign_id": 1
    }
    
    response = client.post(
        "/api/v1/assessments/",
        json=assessment_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 201
    assert response.json()["name"] == assessment_data["name"]


def test_update_assessment(admin_token, test_assessment):
    """Test updating an assessment."""
    update_data = {
        "name": "Updated Test Assessment",
        "status": "in-progress"
    }
    
    response = client.put(
        f"/api/v1/assessments/{test_assessment.id}",
        json=update_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert response.json()["name"] == update_data["name"]
    assert response.json()["status"] == update_data["status"]


def test_delete_assessment(admin_token, test_assessment):
    """Test deleting an assessment."""
    response = client.delete(
        f"/api/v1/assessments/{test_assessment.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 204


def test_get_test_executions(admin_token, test_assessment, test_execution):
    """Test getting test executions for an assessment."""
    response = client.get(
        f"/api/v1/assessments/{test_assessment.id}/executions",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0


def test_get_test_execution(admin_token, test_execution):
    """Test getting a specific test execution."""
    response = client.get(
        f"/api/v1/assessments/executions/{test_execution.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert response.json()["id"] == test_execution.id
    assert response.json()["result"] == test_execution.result


def test_create_test_execution(admin_token, test_assessment):
    """Test creating a new test execution."""
    execution_data = {
        "test_case_id": 2,
        "assessment_id": test_assessment.id,
        "result": "fail",
        "notes": "New test execution notes",
        "evidence": "New test evidence"
    }
    
    response = client.post(
        "/api/v1/assessments/executions",
        json=execution_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 201
    assert response.json()["test_case_id"] == execution_data["test_case_id"]
    assert response.json()["result"] == execution_data["result"]


def test_update_test_execution(admin_token, test_execution):
    """Test updating a test execution."""
    update_data = {
        "result": "partial",
        "notes": "Updated test execution notes"
    }
    
    response = client.put(
        f"/api/v1/assessments/executions/{test_execution.id}",
        json=update_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert response.json()["result"] == update_data["result"]
    assert response.json()["notes"] == update_data["notes"]


def test_delete_test_execution(admin_token, test_execution):
    """Test deleting a test execution."""
    response = client.delete(
        f"/api/v1/assessments/executions/{test_execution.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 204


def test_get_assessment_summary(admin_token, test_assessment, test_execution):
    """Test getting an assessment summary."""
    response = client.get(
        f"/api/v1/assessments/{test_assessment.id}/summary",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert "total_tests" in response.json()
    assert "passed" in response.json()
    assert "failed" in response.json()


def test_generate_assessment_report(admin_token, test_assessment, test_execution):
    """Test generating an assessment report."""
    response = client.get(
        f"/api/v1/assessments/{test_assessment.id}/report",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert "assessment" in response.json()
    assert "summary" in response.json()
    assert "test_executions" in response.json()
    assert "recommendations" in response.json()


def test_unauthorized_access(user_token):
    """Test unauthorized access to admin-only endpoints."""
    # Regular users can't restore assessments
    response = client.post(
        "/api/v1/assessments/1/restore",
        headers={"Authorization": f"Bearer {user_token}"}
    )
    assert response.status_code == 403 