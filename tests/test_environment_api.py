"""
Tests for the Environment Management API.

This module contains tests for the environment endpoints.
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from api.main import app
from api.models.environment import Environment, EnvironmentType, EnvironmentStatus
from api.models.schemas.environment import EnvironmentCreate
from api.services.environment import create_environment
from api.auth.utils import create_access_token

client = TestClient(app)


@pytest.fixture
def db_session(monkeypatch):
    """Create a test database session."""
    # This would typically use a test database
    from api.database import get_db
    db = next(get_db())
    yield db


@pytest.fixture
def admin_token():
    """Create an admin token for testing."""
    access_token = create_access_token(
        data={"sub": "<EMAIL>", "role": "admin"},
        expires_delta=timedelta(minutes=30)
    )
    return access_token


@pytest.fixture
def user_token():
    """Create a regular user token for testing."""
    access_token = create_access_token(
        data={"sub": "<EMAIL>", "role": "user"},
        expires_delta=timedelta(minutes=30)
    )
    return access_token


@pytest.fixture
def test_environment(db_session):
    """Create a test environment."""
    environment_data = EnvironmentCreate(
        name="Test Environment",
        description="Test environment for API testing",
        type=EnvironmentType.TEST,
        status=EnvironmentStatus.ACTIVE
    )
    
    environment = create_environment(db_session, environment_data, user_id=1)
    return environment


def test_get_environments(admin_token):
    """Test getting a list of environments."""
    response = client.get(
        "/api/v1/environments/",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert isinstance(response.json(), list)


def test_get_environment(admin_token, test_environment):
    """Test getting a specific environment."""
    response = client.get(
        f"/api/v1/environments/{test_environment.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert response.json()["id"] == test_environment.id
    assert response.json()["name"] == test_environment.name


def test_create_environment(admin_token):
    """Test creating a new environment."""
    environment_data = {
        "name": "New Test Environment",
        "description": "New test environment for API testing",
        "type": "test",
        "status": "active"
    }
    
    response = client.post(
        "/api/v1/environments/",
        json=environment_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 201
    assert response.json()["name"] == environment_data["name"]


def test_update_environment(admin_token, test_environment):
    """Test updating an environment."""
    update_data = {
        "name": "Updated Test Environment",
        "status": "inactive"
    }
    
    response = client.put(
        f"/api/v1/environments/{test_environment.id}",
        json=update_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert response.json()["name"] == update_data["name"]
    assert response.json()["status"] == update_data["status"]


def test_delete_environment(admin_token, test_environment):
    """Test deleting an environment."""
    response = client.delete(
        f"/api/v1/environments/{test_environment.id}",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 204


def test_restore_environment(admin_token, test_environment, db_session):
    """Test restoring a soft-deleted environment."""
    # First, delete the environment
    test_environment.soft_delete(db_session)
    
    response = client.post(
        f"/api/v1/environments/{test_environment.id}/restore",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert response.json()["id"] == test_environment.id
    assert response.json()["deleted_at"] is None


def test_get_environment_assessments(admin_token, test_environment):
    """Test getting assessments for an environment."""
    response = client.get(
        f"/api/v1/environments/{test_environment.id}/assessments",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert isinstance(response.json(), list)


def test_deprecate_environment(admin_token, test_environment):
    """Test deprecating an environment."""
    response = client.post(
        f"/api/v1/environments/{test_environment.id}/deprecate",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert response.json()["id"] == test_environment.id
    assert response.json()["is_deprecated"] is True


def test_revoke_environment(admin_token, test_environment):
    """Test revoking an environment."""
    response = client.post(
        f"/api/v1/environments/{test_environment.id}/revoke",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    assert response.status_code == 200
    assert response.json()["id"] == test_environment.id
    assert response.json()["is_revoked"] is True


def test_unauthorized_access(user_token):
    """Test unauthorized access to admin-only endpoints."""
    # Regular users can't restore environments
    response = client.post(
        "/api/v1/environments/1/restore",
        headers={"Authorization": f"Bearer {user_token}"}
    )
    assert response.status_code == 403 