"""
Tests for the Advanced Soft Deletion API endpoints.

This module contains comprehensive tests for the soft deletion REST API,
including policy management, audit log access, and scheduled operations.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.main import app
from api.models.soft_deletion import SoftDeletionPolicy, SoftDeletionAudit, SoftDeletionSchedule
from api.models.user import User


@pytest.fixture
def client():
    """Create a test client."""
    return TestClient(app)


@pytest.fixture
def mock_db():
    """Create a mock database session."""
    return Mock(spec=Session)


@pytest.fixture
def mock_user():
    """Create a mock user."""
    user = Mock(spec=User)
    user.id = 1
    user.username = "testuser"
    user.is_admin = False
    return user


@pytest.fixture
def mock_admin_user():
    """Create a mock admin user."""
    user = Mock(spec=User)
    user.id = 2
    user.username = "admin"
    user.is_admin = True
    return user


@pytest.fixture
def sample_policy_data():
    """Sample policy data for testing."""
    return {
        "entity_type": "testcase",
        "retention_period_days": 30,
        "auto_purge_enabled": True,
        "cascade_deletion": True,
        "notification_enabled": True,
        "notification_days_before": 7,
        "description": "Test policy for test cases"
    }


@pytest.fixture
def sample_policy():
    """Create a sample soft deletion policy."""
    return SoftDeletionPolicy(
        id=1,
        entity_type="testcase",
        retention_period_days=30,
        auto_purge_enabled=True,
        cascade_deletion=True,
        notification_enabled=True,
        notification_days_before=7,
        description="Test policy",
        created_by=1,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )


class TestSoftDeletionPolicyAPI:
    """Test cases for soft deletion policy API endpoints."""
    
    @patch('api.routes.v1_soft_deletion.get_db')
    @patch('api.routes.v1_soft_deletion.get_current_user')
    def test_list_policies(self, mock_get_user, mock_get_db, client, mock_user, mock_db, sample_policy):
        """Test listing soft deletion policies."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.offset.return_value.limit.return_value.all.return_value = [sample_policy]
        
        response = client.get("/soft-deletion/policies")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["entity_type"] == "testcase"
        assert data[0]["retention_period_days"] == 30
    
    @patch('api.routes.v1_soft_deletion.get_db')
    @patch('api.routes.v1_soft_deletion.get_current_user')
    def test_list_policies_with_filter(self, mock_get_user, mock_get_db, client, mock_user, mock_db):
        """Test listing policies with entity type filter."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.offset.return_value.limit.return_value.all.return_value = []
        
        response = client.get("/soft-deletion/policies?entity_type=campaign")
        
        assert response.status_code == 200
        mock_db.query.return_value.filter.assert_called_once()
    
    @patch('api.routes.v1_soft_deletion.get_db')
    @patch('api.routes.v1_soft_deletion.get_current_user')
    @patch('api.routes.v1_soft_deletion.SoftDeletionService')
    def test_create_policy_success(self, mock_service_class, mock_get_user, mock_get_db, 
                                   client, mock_user, mock_db, sample_policy_data, sample_policy):
        """Test successful policy creation."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = None  # No existing policy
        
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.create_policy.return_value = sample_policy
        
        response = client.post("/soft-deletion/policies", json=sample_policy_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["entity_type"] == "testcase"
        assert data["retention_period_days"] == 30
        
        mock_service.create_policy.assert_called_once()
    
    @patch('api.routes.v1_soft_deletion.get_db')
    @patch('api.routes.v1_soft_deletion.get_current_user')
    def test_create_policy_conflict(self, mock_get_user, mock_get_db, 
                                    client, mock_user, mock_db, sample_policy_data, sample_policy):
        """Test policy creation when policy already exists."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = sample_policy  # Existing policy
        
        response = client.post("/soft-deletion/policies", json=sample_policy_data)
        
        assert response.status_code == 409
        assert "already exists" in response.json()["detail"]
    
    @patch('api.routes.v1_soft_deletion.get_db')
    @patch('api.routes.v1_soft_deletion.get_current_user')
    def test_get_policy_success(self, mock_get_user, mock_get_db, client, mock_user, mock_db, sample_policy):
        """Test getting a specific policy."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = sample_policy
        
        response = client.get("/soft-deletion/policies/1")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == 1
        assert data["entity_type"] == "testcase"
    
    @patch('api.routes.v1_soft_deletion.get_db')
    @patch('api.routes.v1_soft_deletion.get_current_user')
    def test_get_policy_not_found(self, mock_get_user, mock_get_db, client, mock_user, mock_db):
        """Test getting a non-existent policy."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        response = client.get("/soft-deletion/policies/999")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    @patch('api.routes.v1_soft_deletion.get_db')
    @patch('api.routes.v1_soft_deletion.get_current_user')
    def test_update_policy_success(self, mock_get_user, mock_get_db, client, mock_user, mock_db, sample_policy):
        """Test successful policy update."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = sample_policy
        
        update_data = {"retention_period_days": 60, "description": "Updated description"}
        response = client.put("/soft-deletion/policies/1", json=update_data)
        
        assert response.status_code == 200
        assert sample_policy.retention_period_days == 60
        assert sample_policy.description == "Updated description"
        mock_db.add.assert_called_once_with(sample_policy)
        mock_db.commit.assert_called_once()
    
    @patch('api.routes.v1_soft_deletion.get_db')
    @patch('api.routes.v1_soft_deletion.get_current_user')
    def test_delete_policy_success(self, mock_get_user, mock_get_db, client, mock_user, mock_db, sample_policy):
        """Test successful policy deletion."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = sample_policy
        mock_db.query.return_value.filter.return_value.all.return_value = []  # No schedules
        
        response = client.delete("/soft-deletion/policies/1")
        
        assert response.status_code == 204
        mock_db.delete.assert_called_once_with(sample_policy)
        mock_db.commit.assert_called_once()


class TestSoftDeletionAuditAPI:
    """Test cases for soft deletion audit API endpoints."""
    
    @patch('api.routes.v1_soft_deletion.get_db')
    @patch('api.routes.v1_soft_deletion.get_current_user')
    def test_list_audit_logs(self, mock_get_user, mock_get_db, client, mock_user, mock_db):
        """Test listing audit logs."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        
        sample_audit = SoftDeletionAudit(
            id=1,
            entity_type="testcase",
            entity_id=123,
            operation_type="soft_delete",
            performed_by=1,
            reason="Test deletion",
            operation_time=datetime.utcnow()
        )
        
        mock_db.query.return_value.order_by.return_value.offset.return_value.limit.return_value.all.return_value = [sample_audit]
        
        response = client.get("/soft-deletion/audit")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["entity_type"] == "testcase"
        assert data[0]["operation_type"] == "soft_delete"
    
    @patch('api.routes.v1_soft_deletion.get_db')
    @patch('api.routes.v1_soft_deletion.get_current_user')
    def test_list_audit_logs_with_filters(self, mock_get_user, mock_get_db, client, mock_user, mock_db):
        """Test listing audit logs with filters."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.filter.return_value.order_by.return_value.offset.return_value.limit.return_value.all.return_value = []
        
        response = client.get("/soft-deletion/audit?entity_type=testcase&operation_type=soft_delete")
        
        assert response.status_code == 200
        # Verify filters were applied
        assert mock_db.query.return_value.filter.call_count >= 2
    
    @patch('api.routes.v1_soft_deletion.get_db')
    @patch('api.routes.v1_soft_deletion.get_current_user')
    def test_get_audit_log_success(self, mock_get_user, mock_get_db, client, mock_user, mock_db):
        """Test getting a specific audit log."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        
        sample_audit = SoftDeletionAudit(
            id=1,
            entity_type="testcase",
            entity_id=123,
            operation_type="soft_delete",
            performed_by=1,
            operation_time=datetime.utcnow()
        )
        
        mock_db.query.return_value.filter.return_value.first.return_value = sample_audit
        
        response = client.get("/soft-deletion/audit/1")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == 1
        assert data["entity_type"] == "testcase"


class TestSoftDeletionScheduleAPI:
    """Test cases for soft deletion schedule API endpoints."""
    
    @patch('api.routes.v1_soft_deletion.get_db')
    @patch('api.routes.v1_soft_deletion.get_current_user')
    def test_list_schedules(self, mock_get_user, mock_get_db, client, mock_user, mock_db):
        """Test listing scheduled operations."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        
        sample_schedule = SoftDeletionSchedule(
            id=1,
            entity_type="testcase",
            entity_id=123,
            operation_type="purge",
            scheduled_for=datetime.utcnow() + timedelta(days=30),
            policy_id=1,
            status="pending"
        )
        
        mock_db.query.return_value.order_by.return_value.offset.return_value.limit.return_value.all.return_value = [sample_schedule]
        
        response = client.get("/soft-deletion/schedules")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["entity_type"] == "testcase"
        assert data[0]["operation_type"] == "purge"
        assert data[0]["status"] == "pending"


class TestSoftDeletionNotificationAPI:
    """Test cases for soft deletion notification API endpoints."""
    
    @patch('api.routes.v1_soft_deletion.get_db')
    @patch('api.routes.v1_soft_deletion.get_current_user')
    def test_list_notifications_user(self, mock_get_user, mock_get_db, client, mock_user, mock_db):
        """Test listing notifications for regular user."""
        mock_get_user.return_value = mock_user
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.order_by.return_value.offset.return_value.limit.return_value.all.return_value = []
        
        response = client.get("/soft-deletion/notifications")
        
        assert response.status_code == 200
        # Verify user can only see their own notifications
        mock_db.query.return_value.filter.assert_called()
    
    @patch('api.routes.v1_soft_deletion.get_db')
    @patch('api.routes.v1_soft_deletion.get_current_user')
    def test_list_notifications_admin(self, mock_get_user, mock_get_db, client, mock_admin_user, mock_db):
        """Test listing notifications for admin user."""
        mock_get_user.return_value = mock_admin_user
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.order_by.return_value.offset.return_value.limit.return_value.all.return_value = []
        
        response = client.get("/soft-deletion/notifications")
        
        assert response.status_code == 200
        # Admin should be able to see all notifications
