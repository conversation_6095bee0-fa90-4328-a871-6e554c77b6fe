"""Test configuration and fixtures."""
import os
import sys
import pytest
import logging
from typing import Dict, Generator, Any, List
from pathlib import Path
from datetime import datetime, timedelta

from fastapi.testclient import TestClient
from sqlalchemy import create_engine, text
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.exc import SQLAlchemyError

# Configure logging with more verbose output for test debugging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Add project root to Python path
project_root = str(Path(__file__).parent.parent)
sys.path.insert(0, project_root)

# Import all necessary models and components
from api.database import Base, engine, get_db, init_db
from api.main import app as db_app
from api.models.base import CampaignDB, TestCaseDB, OrganizationDB, CampaignToOrganizationDB
from api.models.user import User, UserRole
from api.models.mitre import MitreVersion, MitreTechnique, MitreTactic
from api.models.testcase_chain import (
    ChainExecution,
    NodeExecution,
    TestcaseChain,
    TestcaseChainEdge,
    TestcaseChainNode,
    TestcaseCondition,
)
from api.auth.router import create_access_token
from api.services.testcase_chain_service import TestcaseChainService

# Import test utilities
from tests.utils.factories import (
    UserFactory, CampaignFactory, TestCaseFactory,
    MitreFactory, OrganizationFactory
)
from tests.utils.helpers import auth_headers, get_auth_token, create_test_data


# Define pytest markers
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line("markers", "unit: mark a test as a unit test")
    config.addinivalue_line("markers", "integration: mark a test as an integration test")
    config.addinivalue_line("markers", "slow: mark a test as slow")
    config.addinivalue_line("markers", "api: mark a test as an API test")
    config.addinivalue_line("markers", "mitre: mark a test as a MITRE test")
    config.addinivalue_line("markers", "campaign: mark a test as a campaign test")
    config.addinivalue_line("markers", "testcase: mark a test as a test case test")
    config.addinivalue_line("markers", "execution: mark a test as an execution framework test")
    config.addinivalue_line("markers", "behavior: mark a test as a behavioral test")
    config.addinivalue_line("markers", "e2e: mark a test as an end-to-end test")
    config.addinivalue_line("markers", "performance: mark a test as a performance test")
    config.addinivalue_line("markers", "chain: mark a test as a testcase chain test")


def clear_db():
    """Clear all database objects and recreate tables in correct order."""
    try:
        logger.info("Starting database cleanup")
        init_db(clean=True)  # Use the enhanced init_db with clean parameter
        logger.info("Successfully reset database")
    except SQLAlchemyError as e:
        logger.error(f"Database error during cleanup: {str(e)}")
        logger.error("Stack trace:", exc_info=True)
        raise
    except Exception as e:
        logger.error(f"Unexpected error during cleanup: {str(e)}")
        logger.error("Stack trace:", exc_info=True)
        raise


@pytest.fixture(scope="session")
def db_engine():
    """Create test database engine."""
    try:
        # Clear database first
        logger.info("Setting up test database engine")
        clear_db()
        yield engine
        # Cleanup after all tests
        clear_db()
    except Exception as e:
        logger.error(f"Error in db_engine fixture: {e}", exc_info=True)
        raise


@pytest.fixture(scope="function")
def db_session(db_engine) -> Session:
    """Create a fresh database session for each test."""
    logger.debug("Creating new test database session")
    connection = db_engine.connect()
    transaction = connection.begin()

    session_factory = sessionmaker(
        bind=connection,
        autocommit=False,
        autoflush=False,
        expire_on_commit=False
    )
    session = session_factory()

    try:
        yield session
    finally:
        logger.debug("Cleaning up test database session")
        session.close()
        transaction.rollback()
        connection.close()


@pytest.fixture(scope="session")
def test_app():
    """Create a test FastAPI application instance."""
    logger.debug("Creating test FastAPI application")
    return db_app


@pytest.fixture(scope="function")
def client(test_app, db_session) -> TestClient:
    """Create a test client with database session override."""
    logger.debug("Creating test client")

    def override_get_db():
        try:
            yield db_session
        finally:
            pass  # Session cleanup handled by db_session fixture

    test_app.dependency_overrides[get_db] = override_get_db
    with TestClient(test_app) as test_client:
        yield test_client

    test_app.dependency_overrides.clear()
    logger.debug("Test client cleanup complete")


@pytest.fixture
def test_user(db_session):
    """Create a test user."""
    logger.debug("Creating test user")
    return UserFactory.create(
        db_session,
        username="testuser",
        email="<EMAIL>",
        role=UserRole.ANALYST,
        password="testpass123"
    )


@pytest.fixture
def test_admin(db_session):
    """Create a test admin user."""
    logger.debug("Creating test admin user")
    return UserFactory.create(
        db_session,
        username="admin",
        email="<EMAIL>",
        role=UserRole.ADMIN,
        password="adminpass123"
    )


@pytest.fixture
def test_user_token(test_user, client):
    """Get authentication token for test user."""
    logger.debug("Getting test user token")
    return get_auth_token(client, "testuser", "testpass123")


@pytest.fixture
def test_admin_token(test_admin, client):
    """Get authentication token for test admin."""
    logger.debug("Getting test admin token")
    return get_auth_token(client, "admin", "adminpass123")


@pytest.fixture
def test_user_headers(test_user_token):
    """Get headers for authenticated test user requests."""
    return auth_headers(test_user_token)


@pytest.fixture
def test_admin_headers(test_admin_token):
    """Get headers for authenticated test admin requests."""
    return auth_headers(test_admin_token)


@pytest.fixture
def test_organization(db_session):
    """Create a test organization."""
    logger.debug("Creating test organization")
    return OrganizationFactory.create(
        db_session,
        name="Test Organization",
        description="Test organization for unit tests"
    )


@pytest.fixture
def test_campaign(db_session, test_organization):
    """Create a test campaign."""
    logger.debug("Creating test campaign")
    return CampaignFactory.create(
        db_session,
        name="Test Campaign",
        description="Test campaign for unit tests",
        status="active",
        organizations=[test_organization]
    )


@pytest.fixture
def test_mitre_version(db_session):
    """Create a test MITRE version."""
    logger.debug("Creating test MITRE version")
    return MitreFactory.create_version(
        db_session,
        version="test_version",
        name="Test Version",
        description="Test version for unit tests",
        is_current=True
    )


@pytest.fixture
def test_mitre_tactic(db_session, test_mitre_version):
    """Create a test MITRE tactic."""
    logger.debug("Creating test MITRE tactic")
    return MitreFactory.create_tactic(
        db_session,
        external_id="TA0001",
        name="Initial Access",
        description="Test tactic for unit tests",
        version_id=test_mitre_version.id
    )


@pytest.fixture
def test_mitre_technique(db_session, test_mitre_version, test_mitre_tactic):
    """Create a test MITRE technique."""
    logger.debug("Creating test MITRE technique")
    return MitreFactory.create_technique(
        db_session,
        technique_id="T1190",
        name="Exploit Public-Facing Application",
        description="Test technique for unit tests",
        version_id=test_mitre_version.id,
        detection="Test detection methods",
        platforms=["Windows", "macOS", "Linux"],
        tactics=[test_mitre_tactic]
    )


@pytest.fixture
def test_case(db_session, test_campaign, test_mitre_technique):
    """Create a test case."""
    logger.debug("Creating test case")
    return TestCaseFactory.create(
        db_session,
        name="Test Case",
        description="Test case for unit tests",
        campaign_id=test_campaign.id,
        expected_result="Expected test result",
        mitre_techniques=[test_mitre_technique]
    )


@pytest.fixture
def complete_test_data(db_session):
    """Create a complete set of test data."""
    logger.debug("Creating complete test data")
    return create_test_data(db_session)


# Testcase Chain Fixtures

@pytest.fixture
def sample_chain(db_session, test_user):
    """Create a sample testcase chain."""
    logger.debug("Creating sample testcase chain")
    chain = TestcaseChain(
        name="Sample Test Chain",
        description="A sample chain for testing",
        status="draft",
        chain_type="sequential",
        max_execution_time_minutes=60,
        retry_on_failure=True,
        auto_cleanup=True,
        created_by=test_user.id,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    db_session.add(chain)
    db_session.commit()
    db_session.refresh(chain)
    return chain


@pytest.fixture
def sample_nodes(db_session, sample_chain, test_case):
    """Create sample chain nodes."""
    logger.debug("Creating sample chain nodes")
    nodes = []
    for i in range(3):
        node = TestcaseChainNode(
            chain_id=sample_chain.id,
            testcase_id=test_case.id,
            node_type="start" if i == 0 else ("end" if i == 2 else "standard"),
            execution_order=i + 1,
            position_x=100.0 + (i * 100),
            position_y=100.0,
            timeout_minutes=30,
            retry_count=1,
            continue_on_failure=False,
            required_for_completion=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db_session.add(node)
        nodes.append(node)

    db_session.commit()
    for node in nodes:
        db_session.refresh(node)
    return nodes


@pytest.fixture
def sample_edges(db_session, sample_nodes):
    """Create sample chain edges."""
    logger.debug("Creating sample chain edges")
    edges = []
    for i in range(len(sample_nodes) - 1):
        edge = TestcaseChainEdge(
            source_node_id=sample_nodes[i].id,
            target_node_id=sample_nodes[i + 1].id,
            edge_type="standard",
            weight=1,
            label=f"Edge {i + 1}",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db_session.add(edge)
        edges.append(edge)

    db_session.commit()
    for edge in edges:
        db_session.refresh(edge)
    return edges


@pytest.fixture
def sample_execution(db_session, sample_chain, test_user):
    """Create a sample chain execution."""
    logger.debug("Creating sample chain execution")
    execution = ChainExecution(
        chain_id=sample_chain.id,
        started_by=test_user.id,
        start_time=datetime.utcnow(),
        status="running",
        execution_context={"environment": "test"},
        total_nodes=3,
        completed_nodes=0,
        failed_nodes=0,
        skipped_nodes=0,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    db_session.add(execution)
    db_session.commit()
    db_session.refresh(execution)
    return execution


@pytest.fixture
def sample_node_executions(db_session, sample_execution, sample_nodes):
    """Create sample node executions."""
    logger.debug("Creating sample node executions")
    node_executions = []
    for i, node in enumerate(sample_nodes):
        node_execution = NodeExecution(
            chain_execution_id=sample_execution.id,
            node_id=node.id,
            status="pending",
            attempt_number=1,
            max_attempts=3,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        db_session.add(node_execution)
        node_executions.append(node_execution)

    db_session.commit()
    for node_execution in node_executions:
        db_session.refresh(node_execution)
    return node_executions


@pytest.fixture
def testcase_chain_service(db_session):
    """Create a testcase chain service with real database."""
    logger.debug("Creating testcase chain service")
    return TestcaseChainService(db_session)


@pytest.fixture
def complex_chain_scenario(db_session, test_user, test_case):
    """Create a complex chain scenario for testing."""
    logger.debug("Creating complex chain scenario")

    # Create a chain with multiple paths and conditions
    chain = TestcaseChain(
        name="Complex Attack Scenario",
        description="Multi-path attack chain with conditions",
        status="active",
        chain_type="conditional",
        max_execution_time_minutes=180,
        retry_on_failure=True,
        auto_cleanup=False,
        created_by=test_user.id
    )
    db_session.add(chain)
    db_session.commit()
    db_session.refresh(chain)

    # Create nodes for different attack paths
    nodes = [
        TestcaseChainNode(
            chain_id=chain.id, testcase_id=test_case.id,
            node_type="start", execution_order=1, position_x=100, position_y=100
        ),
        TestcaseChainNode(
            chain_id=chain.id, testcase_id=test_case.id,
            node_type="conditional", execution_order=2, position_x=200, position_y=50,
            condition_expression="previous_result.access_level == 'user'"
        ),
        TestcaseChainNode(
            chain_id=chain.id, testcase_id=test_case.id,
            node_type="conditional", execution_order=2, position_x=200, position_y=150,
            condition_expression="previous_result.network_access == true"
        ),
        TestcaseChainNode(
            chain_id=chain.id, testcase_id=test_case.id,
            node_type="end", execution_order=3, position_x=300, position_y=100
        )
    ]

    for node in nodes:
        db_session.add(node)
    db_session.commit()
    for node in nodes:
        db_session.refresh(node)

    # Create edges for conditional flow
    edges = [
        TestcaseChainEdge(
            source_node_id=nodes[0].id, target_node_id=nodes[1].id,
            edge_type="conditional", condition="result.user_access == true"
        ),
        TestcaseChainEdge(
            source_node_id=nodes[0].id, target_node_id=nodes[2].id,
            edge_type="conditional", condition="result.network_discovered == true"
        ),
        TestcaseChainEdge(
            source_node_id=nodes[1].id, target_node_id=nodes[3].id,
            edge_type="success_path"
        ),
        TestcaseChainEdge(
            source_node_id=nodes[2].id, target_node_id=nodes[3].id,
            edge_type="success_path"
        )
    ]

    for edge in edges:
        db_session.add(edge)
    db_session.commit()
    for edge in edges:
        db_session.refresh(edge)

    return {
        "chain": chain,
        "nodes": nodes,
        "edges": edges
    }