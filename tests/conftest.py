"""Test configuration and fixtures."""
import os
import sys
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine, text, Table, Column, Integer, String, DateTime, MetaData
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.exc import SQLAlchemyError
from pathlib import Path
import logging
from datetime import datetime

# Configure logging with more verbose output for test debugging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Add project root to Python path
project_root = str(Path(__file__).parent.parent)
sys.path.insert(0, project_root)

# Import all necessary models and components
from api.database import Base, engine, get_db, init_db
from api.main import app as db_app
from api.models.base import CampaignDB, TestCaseDB, OrganizationDB, CampaignToOrganizationDB
from api.models.user import User, UserRole
from api.auth.router import create_access_token

def create_flask_users_table():
    """Create a temporary flask_users table for testing."""
    try:
        logger.info("Creating temporary flask_users table")
        metadata = MetaData()
        flask_users = Table(
            'flask_users', 
            metadata,
            Column('id', Integer, primary_key=True),
            Column('username', String(64), unique=True, nullable=False),
            Column('email', String(120), unique=True, nullable=False),
            Column('password_hash', String(256)),
            Column('created_at', DateTime, default=datetime.now)
        )
        metadata.create_all(engine)
        
        # Insert a test user
        with engine.connect() as conn:
            conn.execute(
                flask_users.insert().values(
                    id=1,
                    username="flask_test_user",
                    email="<EMAIL>",
                    password_hash="test_hash"
                )
            )
            conn.commit()
        
        logger.info("Successfully created flask_users table")
    except SQLAlchemyError as e:
        logger.error(f"Database error during flask_users table creation: {str(e)}")
        logger.error("Stack trace:", exc_info=True)
    except Exception as e:
        logger.error(f"Unexpected error during flask_users table creation: {str(e)}")
        logger.error("Stack trace:", exc_info=True)

def clear_db():
    """Clear all database objects and recreate tables in correct order."""
    try:
        logger.info("Starting database cleanup")
        init_db(clean=True)  # Use the enhanced init_db with clean parameter
        # Create the flask_users table after initializing the database
        create_flask_users_table()
        logger.info("Successfully reset database")
    except SQLAlchemyError as e:
        logger.error(f"Database error during cleanup: {str(e)}")
        logger.error("Stack trace:", exc_info=True)
        raise
    except Exception as e:
        logger.error(f"Unexpected error during cleanup: {str(e)}")
        logger.error("Stack trace:", exc_info=True)
        raise

@pytest.fixture(scope="session")
def db_engine():
    """Create test database engine."""
    try:
        # Clear database first
        logger.info("Setting up test database engine")
        clear_db()
        yield engine
        # Cleanup after all tests
        clear_db()
    except Exception as e:
        logger.error(f"Error in db_engine fixture: {e}", exc_info=True)
        raise

@pytest.fixture(scope="function")
def db_session(db_engine) -> Session:
    """Create a fresh database session for each test."""
    logger.debug("Creating new test database session")
    connection = db_engine.connect()
    transaction = connection.begin()

    session_factory = sessionmaker(
        bind=connection,
        autocommit=False,
        autoflush=False,
        expire_on_commit=False
    )
    session = session_factory()

    try:
        yield session
    finally:
        logger.debug("Cleaning up test database session")
        session.close()
        transaction.rollback()
        connection.close()

@pytest.fixture(scope="session")
def test_app():
    """Create a test FastAPI application instance."""
    logger.debug("Creating test FastAPI application")
    return db_app

@pytest.fixture(scope="function")
def client(test_app, db_session) -> TestClient:
    """Create a test client with database session override."""
    logger.debug("Creating test client")

    def override_get_db():
        try:
            yield db_session
        finally:
            pass  # Session cleanup handled by db_session fixture

    test_app.dependency_overrides[get_db] = override_get_db
    with TestClient(test_app) as test_client:
        yield test_client

    test_app.dependency_overrides.clear()
    logger.debug("Test client cleanup complete")

@pytest.fixture
def test_user(db_session):
    """Create a test user."""
    logger.debug("Creating test user")
    user = User(
        username="testuser",
        email="<EMAIL>",
        role=UserRole.ANALYST
    )
    user.set_password("testpass123")  # Uses the User model's password hashing method
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user

@pytest.fixture
def test_admin(db_session):
    """Create a test admin user."""
    logger.debug("Creating test admin user")
    admin = User(
        username="admin",
        email="<EMAIL>",
        role=UserRole.ADMIN
    )
    admin.set_password("adminpass123")
    db_session.add(admin)
    db_session.commit()
    db_session.refresh(admin)
    return admin

@pytest.fixture
def test_user_token(test_user, client):
    """Get authentication token for test user."""
    logger.debug("Getting test user token")
    response = client.post(
        "/api/v1/auth/token/",
        data={"username": "testuser", "password": "testpass123"}
    )
    return response.json()["access_token"]

@pytest.fixture
def test_admin_token(test_admin, client):
    """Get authentication token for test admin."""
    logger.debug("Getting test admin token")
    response = client.post(
        "/api/v1/auth/token/",
        data={"username": "admin", "password": "adminpass123"}
    )
    return response.json()["access_token"]