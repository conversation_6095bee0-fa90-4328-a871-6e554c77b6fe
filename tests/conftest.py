"""Test configuration and fixtures."""
import os
import sys
import pytest
import logging
from typing import Dict, Generator, Any, List
from pathlib import Path
from datetime import datetime, timedelta

from fastapi.testclient import TestClient
from sqlalchemy import create_engine, text
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.exc import SQLAlchemyError

# Configure logging with more verbose output for test debugging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Add project root to Python path
project_root = str(Path(__file__).parent.parent)
sys.path.insert(0, project_root)

# Import all necessary models and components
from api.database import Base, engine, get_db, init_db
from api.main import app as db_app
from api.models.base import CampaignDB, TestCaseDB, OrganizationDB, CampaignToOrganizationDB
from api.models.user import User, UserRole
from api.models.mitre import MitreVersion, MitreTechnique, MitreTactic
from api.auth.router import create_access_token

# Import test utilities
from tests.utils.factories import (
    UserFactory, CampaignFactory, TestCaseFactory,
    MitreFactory, OrganizationFactory
)
from tests.utils.helpers import auth_headers, get_auth_token, create_test_data


# Define pytest markers
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line("markers", "unit: mark a test as a unit test")
    config.addinivalue_line("markers", "integration: mark a test as an integration test")
    config.addinivalue_line("markers", "slow: mark a test as slow")
    config.addinivalue_line("markers", "api: mark a test as an API test")
    config.addinivalue_line("markers", "mitre: mark a test as a MITRE test")
    config.addinivalue_line("markers", "campaign: mark a test as a campaign test")
    config.addinivalue_line("markers", "testcase: mark a test as a test case test")
    config.addinivalue_line("markers", "execution: mark a test as an execution framework test")


def clear_db():
    """Clear all database objects and recreate tables in correct order."""
    try:
        logger.info("Starting database cleanup")
        init_db(clean=True)  # Use the enhanced init_db with clean parameter
        logger.info("Successfully reset database")
    except SQLAlchemyError as e:
        logger.error(f"Database error during cleanup: {str(e)}")
        logger.error("Stack trace:", exc_info=True)
        raise
    except Exception as e:
        logger.error(f"Unexpected error during cleanup: {str(e)}")
        logger.error("Stack trace:", exc_info=True)
        raise


@pytest.fixture(scope="session")
def db_engine():
    """Create test database engine."""
    try:
        # Clear database first
        logger.info("Setting up test database engine")
        clear_db()
        yield engine
        # Cleanup after all tests
        clear_db()
    except Exception as e:
        logger.error(f"Error in db_engine fixture: {e}", exc_info=True)
        raise


@pytest.fixture(scope="function")
def db_session(db_engine) -> Session:
    """Create a fresh database session for each test."""
    logger.debug("Creating new test database session")
    connection = db_engine.connect()
    transaction = connection.begin()

    session_factory = sessionmaker(
        bind=connection,
        autocommit=False,
        autoflush=False,
        expire_on_commit=False
    )
    session = session_factory()

    try:
        yield session
    finally:
        logger.debug("Cleaning up test database session")
        session.close()
        transaction.rollback()
        connection.close()


@pytest.fixture(scope="session")
def test_app():
    """Create a test FastAPI application instance."""
    logger.debug("Creating test FastAPI application")
    return db_app


@pytest.fixture(scope="function")
def client(test_app, db_session) -> TestClient:
    """Create a test client with database session override."""
    logger.debug("Creating test client")

    def override_get_db():
        try:
            yield db_session
        finally:
            pass  # Session cleanup handled by db_session fixture

    test_app.dependency_overrides[get_db] = override_get_db
    with TestClient(test_app) as test_client:
        yield test_client

    test_app.dependency_overrides.clear()
    logger.debug("Test client cleanup complete")


@pytest.fixture
def test_user(db_session):
    """Create a test user."""
    logger.debug("Creating test user")
    return UserFactory.create(
        db_session,
        username="testuser",
        email="<EMAIL>",
        role=UserRole.ANALYST,
        password="testpass123"
    )


@pytest.fixture
def test_admin(db_session):
    """Create a test admin user."""
    logger.debug("Creating test admin user")
    return UserFactory.create(
        db_session,
        username="admin",
        email="<EMAIL>",
        role=UserRole.ADMIN,
        password="adminpass123"
    )


@pytest.fixture
def test_user_token(test_user, client):
    """Get authentication token for test user."""
    logger.debug("Getting test user token")
    return get_auth_token(client, "testuser", "testpass123")


@pytest.fixture
def test_admin_token(test_admin, client):
    """Get authentication token for test admin."""
    logger.debug("Getting test admin token")
    return get_auth_token(client, "admin", "adminpass123")


@pytest.fixture
def test_user_headers(test_user_token):
    """Get headers for authenticated test user requests."""
    return auth_headers(test_user_token)


@pytest.fixture
def test_admin_headers(test_admin_token):
    """Get headers for authenticated test admin requests."""
    return auth_headers(test_admin_token)


@pytest.fixture
def test_organization(db_session):
    """Create a test organization."""
    logger.debug("Creating test organization")
    return OrganizationFactory.create(
        db_session,
        name="Test Organization",
        description="Test organization for unit tests"
    )


@pytest.fixture
def test_campaign(db_session, test_organization):
    """Create a test campaign."""
    logger.debug("Creating test campaign")
    return CampaignFactory.create(
        db_session,
        name="Test Campaign",
        description="Test campaign for unit tests",
        status="active",
        organizations=[test_organization]
    )


@pytest.fixture
def test_mitre_version(db_session):
    """Create a test MITRE version."""
    logger.debug("Creating test MITRE version")
    return MitreFactory.create_version(
        db_session,
        version="test_version",
        name="Test Version",
        description="Test version for unit tests",
        is_current=True
    )


@pytest.fixture
def test_mitre_tactic(db_session, test_mitre_version):
    """Create a test MITRE tactic."""
    logger.debug("Creating test MITRE tactic")
    return MitreFactory.create_tactic(
        db_session,
        external_id="TA0001",
        name="Initial Access",
        description="Test tactic for unit tests",
        version_id=test_mitre_version.id
    )


@pytest.fixture
def test_mitre_technique(db_session, test_mitre_version, test_mitre_tactic):
    """Create a test MITRE technique."""
    logger.debug("Creating test MITRE technique")
    return MitreFactory.create_technique(
        db_session,
        technique_id="T1190",
        name="Exploit Public-Facing Application",
        description="Test technique for unit tests",
        version_id=test_mitre_version.id,
        detection="Test detection methods",
        platforms=["Windows", "macOS", "Linux"],
        tactics=[test_mitre_tactic]
    )


@pytest.fixture
def test_case(db_session, test_campaign, test_mitre_technique):
    """Create a test case."""
    logger.debug("Creating test case")
    return TestCaseFactory.create(
        db_session,
        name="Test Case",
        description="Test case for unit tests",
        campaign_id=test_campaign.id,
        expected_result="Expected test result",
        mitre_techniques=[test_mitre_technique]
    )


@pytest.fixture
def complete_test_data(db_session):
    """Create a complete set of test data."""
    logger.debug("Creating complete test data")
    return create_test_data(db_session)