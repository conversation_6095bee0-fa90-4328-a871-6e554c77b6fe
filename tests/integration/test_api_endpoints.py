"""
Integration tests for API endpoints.
"""

import pytest
import json
from fastapi import status
from api.models.user import User, User<PERSON><PERSON>

def test_api_authentication(client, db_session):
    """Test API authentication."""
    # Create a user
    user_data = {
        "username": "apiuser",
        "email": "<EMAIL>",
        "password": "ApiPass123!",
        "role": "ANALYST"
    }

    # Register the user
    response = client.post("/api/v1/auth/register", json=user_data)
    assert response.status_code == status.HTTP_201_CREATED

    # Get API token
    response = client.post("/api/v1/auth/token", data={
        "username": "apiuser",
        "password": "ApiPass123!"
    })
    assert response.status_code == status.HTTP_200_OK
    token_data = response.json()
    assert "access_token" in token_data

    # Use token to access protected API endpoint
    headers = {
        "Authorization": f"Bearer {token_data['access_token']}"
    }
    response = client.get("/api/v1/users/me", headers=headers)
    assert response.status_code == status.HTTP_200_OK
    profile_data = response.json()
    assert profile_data["username"] == "apiuser"

    # Try to access without token
    response = client.get("/api/v1/users/me")
    assert response.status_code == status.HTTP_401_UNAUTHORIZED

def test_campaign_endpoints(client, test_admin_token, db_session):
    """Test campaign API endpoints."""
    headers = {
        "Authorization": f"Bearer {test_admin_token}"
    }

    # Test creating a campaign
    new_campaign = {
        "name": "Test Campaign",
        "description": "This is a test campaign",
        "status": "ACTIVE"
    }
    response = client.post("/api/v1/campaigns", json=new_campaign, headers=headers)
    assert response.status_code == status.HTTP_201_CREATED
    campaign_data = response.json()
    assert "id" in campaign_data
    campaign_id = campaign_data["id"]

    # Test getting the campaign
    response = client.get(f"/api/v1/campaigns/{campaign_id}", headers=headers)
    assert response.status_code == status.HTTP_200_OK
    campaign_data = response.json()
    assert campaign_data["name"] == "Test Campaign"

    # Test getting all campaigns
    response = client.get("/api/v1/campaigns", headers=headers)
    assert response.status_code == status.HTTP_200_OK
    campaigns = response.json()
    assert isinstance(campaigns, list)
    assert len(campaigns) >= 1

    # Test updating the campaign
    update_data = {
        "name": "Updated Campaign",
        "description": "This campaign has been updated"
    }
    response = client.patch(f"/api/v1/campaigns/{campaign_id}", json=update_data, headers=headers)
    assert response.status_code == status.HTTP_200_OK

    # Verify the update
    response = client.get(f"/api/v1/campaigns/{campaign_id}", headers=headers)
    campaign_data = response.json()
    assert campaign_data["name"] == "Updated Campaign"

    # Test deleting the campaign
    response = client.delete(f"/api/v1/campaigns/{campaign_id}", headers=headers)
    assert response.status_code == status.HTTP_204_NO_CONTENT

    # Verify the deletion
    response = client.get(f"/api/v1/campaigns/{campaign_id}", headers=headers)
    assert response.status_code == status.HTTP_404_NOT_FOUND

def test_testcase_endpoints(client, test_admin_token, db_session):
    """Test testcase API endpoints."""
    headers = {
        "Authorization": f"Bearer {test_admin_token}"
    }

    # First create a campaign for the testcases
    campaign = {
        "name": "Testcase Campaign",
        "description": "Campaign for testcase tests",
        "status": "ACTIVE"
    }
    response = client.post("/api/v1/campaigns", json=campaign, headers=headers)
    campaign_id = response.json()["id"]

    # Test creating a testcase
    new_testcase = {
        "name": "Test Case",
        "description": "This is a test case",
        "campaign_id": campaign_id,
        "status": "ACTIVE",
        "priority": "HIGH"
    }
    response = client.post("/api/v1/testcases", json=new_testcase, headers=headers)
    assert response.status_code == status.HTTP_201_CREATED
    testcase_data = response.json()
    assert "id" in testcase_data
    testcase_id = testcase_data["id"]

    # Test getting the testcase
    response = client.get(f"/api/v1/testcases/{testcase_id}", headers=headers)
    assert response.status_code == status.HTTP_200_OK
    testcase_data = response.json()
    assert testcase_data["name"] == "Test Case"

    # Test updating the testcase
    update_data = {
        "name": "Updated Test Case",
        "priority": "MEDIUM"
    }
    response = client.patch(f"/api/v1/testcases/{testcase_id}", json=update_data, headers=headers)
    assert response.status_code == status.HTTP_200_OK

    # Verify the update
    response = client.get(f"/api/v1/testcases/{testcase_id}", headers=headers)
    testcase_data = response.json()
    assert testcase_data["name"] == "Updated Test Case"
    assert testcase_data["priority"] == "MEDIUM"

    # Test getting testcases by campaign
    response = client.get(f"/api/v1/campaigns/{campaign_id}/testcases", headers=headers)
    assert response.status_code == status.HTTP_200_OK
    testcases = response.json()
    assert isinstance(testcases, list)
    assert len(testcases) >= 1

    # Clean up - delete the testcase and campaign
    client.delete(f"/api/v1/testcases/{testcase_id}", headers=headers)
    client.delete(f"/api/v1/campaigns/{campaign_id}", headers=headers)
