"""Integration tests for the reporting workflow."""
import json
import os
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

import pytest
from fastapi.testclient import TestClient

from api.main import app
from api.models.campaign import Campaign
from api.models.testcase import TestCase
from api.database import db


@pytest.fixture
def client():
    """Create a test client for the app."""
    return TestClient(app)


@pytest.fixture
def test_campaign():
    """Create a test campaign in the database."""
    with db.session_scope() as session:
        campaign = Campaign(
            name="Test Campaign",
            description="Test campaign for integration tests",
            created_by=1,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        session.add(campaign)
        session.commit()
        session.refresh(campaign)
        return campaign


@pytest.fixture
def test_cases(test_campaign):
    """Create test cases in the database."""
    with db.session_scope() as session:
        test_cases = []
        for i in range(3):
            test_case = TestCase(
                name=f"Test Case {i+1}",
                description=f"Test case {i+1} for integration tests",
                campaign_id=test_campaign.id,
                expected_result="Pass",
                status="completed",
                result="pass" if i < 2 else "fail",
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            session.add(test_case)
            test_cases.append(test_case)
        session.commit()
        for tc in test_cases:
            session.refresh(tc)
        return test_cases


def test_generate_campaign_report(client, test_campaign, test_cases):
    """Test generating a campaign report."""
    # Mock the report generation service
    with patch("api.services.report.generate_campaign_report") as mock_generate:
        mock_generate.return_value = {
            "report_id": "test-report-id",
            "status": "generating"
        }
        
        # Request a report
        response = client.post(
            f"/api/campaigns/{test_campaign.id}/report",
            json={"format": "pdf", "include_details": True}
        )
        
        # Check response
        assert response.status_code == 200
        assert response.json()["report_id"] == "test-report-id"
        assert response.json()["status"] == "generating"
        
        # Check that the service was called with correct parameters
        mock_generate.assert_called_once_with(
            test_campaign.id,
            format="pdf",
            include_details=True
        )


def test_get_report_status(client):
    """Test checking the status of a report."""
    # Mock the report status service
    with patch("api.services.report.get_report_status") as mock_status:
        mock_status.return_value = {
            "status": "completed",
            "download_url": "/api/reports/test-report-id/download"
        }
        
        # Check report status
        response = client.get("/api/reports/test-report-id/status")
        
        # Check response
        assert response.status_code == 200
        assert response.json()["status"] == "completed"
        assert response.json()["download_url"] == "/api/reports/test-report-id/download"
        
        # Check that the service was called with correct parameters
        mock_status.assert_called_once_with("test-report-id")


def test_download_report(client):
    """Test downloading a report."""
    # Mock the report file service
    with patch("api.services.report.get_report_file") as mock_file:
        mock_file.return_value = (b"test report content", "application/pdf")
        
        # Download report
        response = client.get("/api/reports/test-report-id/download")
        
        # Check response
        assert response.status_code == 200
        assert response.content == b"test report content"
        assert response.headers["content-type"] == "application/pdf"
        
        # Check that the service was called with correct parameters
        mock_file.assert_called_once_with("test-report-id")


def test_generate_test_case_report(client, test_cases):
    """Test generating a test case report."""
    test_case = test_cases[0]
    
    # Mock the report generation service
    with patch("api.services.report.generate_test_case_report") as mock_generate:
        mock_generate.return_value = {
            "report_id": "test-case-report-id",
            "status": "generating"
        }
        
        # Request a report
        response = client.post(
            f"/api/test-cases/{test_case.id}/report",
            json={"format": "pdf", "include_logs": True}
        )
        
        # Check response
        assert response.status_code == 200
        assert response.json()["report_id"] == "test-case-report-id"
        assert response.json()["status"] == "generating"
        
        # Check that the service was called with correct parameters
        mock_generate.assert_called_once_with(
            test_case.id,
            format="pdf",
            include_logs=True
        )


def test_export_campaign_results(client, test_campaign):
    """Test exporting campaign results."""
    # Mock the export service
    with patch("api.services.report.export_campaign_results") as mock_export:
        mock_export.return_value = {
            "export_id": "test-export-id",
            "status": "generating"
        }
        
        # Request an export
        response = client.post(
            f"/api/campaigns/{test_campaign.id}/export",
            json={"format": "csv"}
        )
        
        # Check response
        assert response.status_code == 200
        assert response.json()["export_id"] == "test-export-id"
        assert response.json()["status"] == "generating"
        
        # Check that the service was called with correct parameters
        mock_export.assert_called_once_with(
            test_campaign.id,
            format="csv"
        )


def test_download_export(client):
    """Test downloading an export."""
    # Mock the export file service
    with patch("api.services.report.get_export_file") as mock_file:
        mock_file.return_value = (b"test,export,content", "text/csv")
        
        # Download export
        response = client.get("/api/exports/test-export-id/download")
        
        # Check response
        assert response.status_code == 200
        assert response.content == b"test,export,content"
        assert response.headers["content-type"] == "text/csv"
        
        # Check that the service was called with correct parameters
        mock_file.assert_called_once_with("test-export-id")


def test_generate_dashboard_report(client):
    """Test generating a dashboard report."""
    # Mock the dashboard report service
    with patch("api.services.dashboard.generate_dashboard_report") as mock_generate:
        mock_generate.return_value = {
            "report_id": "dashboard-report-id",
            "status": "generating"
        }
        
        # Request a dashboard report
        response = client.post(
            "/api/dashboards/reports",
            json={
                "dashboard_id": "test-dashboard",
                "format": "pdf",
                "time_range": "last_30_days"
            }
        )
        
        # Check response
        assert response.status_code == 200
        assert response.json()["report_id"] == "dashboard-report-id"
        assert response.json()["status"] == "generating"
        
        # Check that the service was called with correct parameters
        mock_generate.assert_called_once_with(
            dashboard_id="test-dashboard",
            format="pdf",
            time_range="last_30_days"
        )


def test_scheduled_reports(client):
    """Test scheduling a report."""
    # Mock the scheduled report service
    with patch("api.services.report.schedule_report") as mock_schedule:
        mock_schedule.return_value = {
            "schedule_id": "test-schedule-id",
            "status": "active"
        }
        
        # Schedule a report
        response = client.post(
            "/api/reports/schedules",
            json={
                "report_type": "campaign",
                "entity_id": 1,
                "format": "pdf",
                "frequency": "weekly",
                "day_of_week": "monday",
                "recipients": ["<EMAIL>"]
            }
        )
        
        # Check response
        assert response.status_code == 200
        assert response.json()["schedule_id"] == "test-schedule-id"
        assert response.json()["status"] == "active"
        
        # Check that the service was called with correct parameters
        mock_schedule.assert_called_once_with(
            report_type="campaign",
            entity_id=1,
            format="pdf",
            frequency="weekly",
            day_of_week="monday",
            recipients=["<EMAIL>"]
        )


def test_get_scheduled_reports(client):
    """Test getting scheduled reports."""
    # Mock the scheduled reports service
    with patch("api.services.report.get_scheduled_reports") as mock_get:
        mock_get.return_value = [
            {
                "schedule_id": "test-schedule-id-1",
                "report_type": "campaign",
                "entity_id": 1,
                "format": "pdf",
                "frequency": "weekly",
                "status": "active"
            },
            {
                "schedule_id": "test-schedule-id-2",
                "report_type": "dashboard",
                "entity_id": 2,
                "format": "csv",
                "frequency": "monthly",
                "status": "active"
            }
        ]
        
        # Get scheduled reports
        response = client.get("/api/reports/schedules")
        
        # Check response
        assert response.status_code == 200
        assert len(response.json()) == 2
        assert response.json()[0]["schedule_id"] == "test-schedule-id-1"
        assert response.json()[1]["schedule_id"] == "test-schedule-id-2"
        
        # Check that the service was called
        mock_get.assert_called_once()


def test_delete_scheduled_report(client):
    """Test deleting a scheduled report."""
    # Mock the delete scheduled report service
    with patch("api.services.report.delete_scheduled_report") as mock_delete:
        mock_delete.return_value = True
        
        # Delete scheduled report
        response = client.delete("/api/reports/schedules/test-schedule-id")
        
        # Check response
        assert response.status_code == 204
        
        # Check that the service was called with correct parameters
        mock_delete.assert_called_once_with("test-schedule-id")
