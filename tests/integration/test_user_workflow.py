"""
Integration tests for user workflows.
"""

import pytest
from fastapi import status
from api.models.user import User, User<PERSON><PERSON>

def test_register_login_workflow(client, db_session):
    """Test the complete user registration and login workflow."""
    # Step 1: Register a new user
    register_data = {
        "username": "integrationuser",
        "email": "<EMAIL>",
        "password": "Password123!",
        "role": "ANALYST"
    }
    response = client.post("/api/v1/auth/register", json=register_data)
    assert response.status_code == status.HTTP_201_CREATED

    # Step 2: Verify the user was created in the database
    user = db_session.query(User).filter(User.username == "integrationuser").first()
    assert user is not None
    assert user.email == "<EMAIL>"
    assert user.role == UserRole.ANALYST

    # Step 3: Login with the new user
    login_data = {
        "username": "integrationuser",
        "password": "Password123!"
    }
    response = client.post("/api/v1/auth/token", data=login_data)
    assert response.status_code == status.HTTP_200_OK
    token_data = response.json()
    assert "access_token" in token_data
    token = token_data["access_token"]

    # Step 4: Access a protected endpoint with the token
    headers = {"Authorization": f"Bearer {token}"}
    response = client.get("/api/v1/users/me", headers=headers)
    assert response.status_code == status.HTTP_200_OK
    user_data = response.json()
    assert user_data["username"] == "integrationuser"

    # Step 5: Update user profile
    update_data = {
        "email": "<EMAIL>"
    }
    response = client.patch("/api/v1/users/me", json=update_data, headers=headers)
    assert response.status_code == status.HTTP_200_OK

    # Step 6: Verify the update
    response = client.get("/api/v1/users/me", headers=headers)
    user_data = response.json()
    assert user_data["email"] == "<EMAIL>"

def test_admin_workflow(client, db_session, test_admin_token):
    """Test the admin user workflow."""
    # Step 1: Access admin endpoint with admin token
    headers = {"Authorization": f"Bearer {test_admin_token}"}
    response = client.get("/api/v1/admin/users", headers=headers)
    assert response.status_code == status.HTTP_200_OK

    # Step 2: Create a new user through admin interface
    new_user_data = {
        "username": "createdbyadmin",
        "email": "<EMAIL>",
        "password": "UserPass123!",
        "role": "ANALYST"
    }
    response = client.post("/api/v1/admin/users", json=new_user_data, headers=headers)
    assert response.status_code == status.HTTP_201_CREATED

    # Step 3: Verify the new user was created
    user = db_session.query(User).filter(User.username == "createdbyadmin").first()
    assert user is not None
    assert user.email == "<EMAIL>"
    assert user.role == UserRole.ANALYST

    # Step 4: Update user role
    update_data = {
        "role": "ADMIN"
    }
    response = client.patch(f"/api/v1/admin/users/{user.id}", json=update_data, headers=headers)
    assert response.status_code == status.HTTP_200_OK

    # Step 5: Verify the role update
    db_session.refresh(user)
    assert user.role == UserRole.ADMIN

    # Step 6: Delete the user
    response = client.delete(f"/api/v1/admin/users/{user.id}", headers=headers)
    assert response.status_code == status.HTTP_204_NO_CONTENT

    # Step 7: Verify the user was deleted
    deleted_user = db_session.query(User).filter(User.username == "createdbyadmin").first()
    assert deleted_user is None
