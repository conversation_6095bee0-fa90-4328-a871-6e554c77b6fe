"""Integration tests for the authentication workflow."""
import pytest
from unittest.mock import patch
from flask import Flask, session, url_for
from api.routes.auth import auth_bp, login_manager
from api.models.user import User
from api.database import db


@pytest.fixture
def app():
    """Create a Flask app with test configuration."""
    app = Flask(__name__)
    app.config.update({
        'TESTING': True,
        'WTF_CSRF_ENABLED': False,
        'SECRET_KEY': 'test-key',
        'SQLALCHEMY_DATABASE_URI': 'sqlite:///:memory:',
        'SQLALCHEMY_TRACK_MODIFICATIONS': False,
    })
    
    # Register the auth blueprint
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    # Initialize login manager
    login_manager.init_app(app)
    
    # Initialize database
    db.init_app(app)
    
    # Create routes for testing
    @app.route('/')
    def index():
        return 'Home Page'
    
    @app.route('/dashboard')
    def dashboard():
        return 'Dashboard Page'
    
    # Create database tables
    with app.app_context():
        db.create_all()
    
    return app


@pytest.fixture
def client(app):
    """Create a test client for the app."""
    return app.test_client()


@pytest.fixture
def test_user(app):
    """Create a test user in the database."""
    with app.app_context():
        user = User(
            username='testuser',
            email='<EMAIL>',
            full_name='Test User'
        )
        user.set_password('password123')
        db.session.add(user)
        db.session.commit()
        return user


def test_registration_workflow(app, client):
    """Test the complete user registration workflow."""
    # Step 1: Visit the registration page
    response = client.get('/auth/register')
    assert response.status_code == 200
    assert b'register.html' in response.data
    
    # Step 2: Submit registration form
    with patch('api.routes.auth.RegisterForm') as mock_form:
        form_instance = mock_form.return_value
        form_instance.validate_on_submit.return_value = True
        form_instance.username.data = 'newuser'
        form_instance.email.data = '<EMAIL>'
        form_instance.password.data = 'newpassword'
        form_instance.confirm_password.data = 'newpassword'
        
        response = client.post('/auth/register', data={
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': 'newpassword',
            'confirm_password': 'newpassword'
        }, follow_redirects=True)
        
        # Check that we're redirected to login page
        assert response.status_code == 200
        assert b'login.html' in response.data
    
    # Step 3: Verify user was created in database
    with app.app_context():
        user = User.query.filter_by(username='newuser').first()
        assert user is not None
        assert user.email == '<EMAIL>'
        assert user.check_password('newpassword')


def test_login_logout_workflow(app, client, test_user):
    """Test the complete login and logout workflow."""
    # Step 1: Visit the login page
    response = client.get('/auth/login')
    assert response.status_code == 200
    assert b'login.html' in response.data
    
    # Step 2: Submit login form with valid credentials
    with patch('api.routes.auth.LoginForm') as mock_form:
        form_instance = mock_form.return_value
        form_instance.validate_on_submit.return_value = True
        form_instance.username.data = 'testuser'
        form_instance.password.data = 'password123'
        form_instance.remember_me.data = False
        
        with patch('api.routes.auth.login_user') as mock_login:
            response = client.post('/auth/login', data={
                'username': 'testuser',
                'password': 'password123',
                'remember_me': False
            }, follow_redirects=True)
            
            # Check that login_user was called with the test user
            mock_login.assert_called_once()
            
            # Check that we're redirected to the index page
            assert response.status_code == 200
            assert b'Home Page' in response.data
    
    # Step 3: Visit a protected page
    with patch('api.routes.auth.current_user') as mock_current_user:
        mock_current_user.is_authenticated = True
        mock_current_user.username = 'testuser'
        
        response = client.get('/dashboard')
        assert response.status_code == 200
        assert b'Dashboard Page' in response.data
    
    # Step 4: Logout
    with patch('api.routes.auth.logout_user') as mock_logout:
        with patch('api.routes.auth.login_required', lambda x: x):
            response = client.get('/auth/logout', follow_redirects=True)
            
            # Check that logout_user was called
            mock_logout.assert_called_once()
            
            # Check that we're redirected to the login page
            assert response.status_code == 200
            assert b'login.html' in response.data


def test_password_reset_workflow(app, client, test_user):
    """Test the complete password reset workflow."""
    # Step 1: Visit the forgot password page
    response = client.get('/auth/forgot-password')
    assert response.status_code == 200
    assert b'forgot_password.html' in response.data
    
    # Step 2: Submit email for password reset
    with patch('api.routes.auth.ForgotPasswordForm') as mock_form:
        form_instance = mock_form.return_value
        form_instance.validate_on_submit.return_value = True
        form_instance.email.data = '<EMAIL>'
        
        with patch('api.routes.auth.send_password_reset_email') as mock_send_email:
            response = client.post('/auth/forgot-password', data={
                'email': '<EMAIL>'
            }, follow_redirects=True)
            
            # Check that send_password_reset_email was called
            mock_send_email.assert_called_once_with(test_user)
            
            # Check that we're redirected to the login page with a message
            assert response.status_code == 200
            assert b'login.html' in response.data
            assert b'password reset instructions' in response.data
    
    # Step 3: Visit the reset password page with token
    with patch('api.routes.auth.verify_reset_token') as mock_verify_token:
        mock_verify_token.return_value = test_user
        
        response = client.get('/auth/reset-password/fake-token')
        assert response.status_code == 200
        assert b'reset_password.html' in response.data
    
    # Step 4: Submit new password
    with patch('api.routes.auth.ResetPasswordForm') as mock_form:
        form_instance = mock_form.return_value
        form_instance.validate_on_submit.return_value = True
        form_instance.password.data = 'newpassword123'
        form_instance.confirm_password.data = 'newpassword123'
        
        with patch('api.routes.auth.verify_reset_token') as mock_verify_token:
            mock_verify_token.return_value = test_user
            
            response = client.post('/auth/reset-password/fake-token', data={
                'password': 'newpassword123',
                'confirm_password': 'newpassword123'
            }, follow_redirects=True)
            
            # Check that we're redirected to the login page
            assert response.status_code == 200
            assert b'login.html' in response.data
            assert b'password has been reset' in response.data
    
    # Step 5: Verify password was updated in database
    with app.app_context():
        user = User.query.filter_by(username='testuser').first()
        assert user.check_password('newpassword123')


def test_account_settings_workflow(app, client, test_user):
    """Test the account settings workflow."""
    # Step 1: Visit the account settings page
    with patch('api.routes.auth.current_user') as mock_current_user:
        mock_current_user.is_authenticated = True
        mock_current_user.id = test_user.id
        mock_current_user.username = test_user.username
        
        response = client.get('/auth/settings')
        assert response.status_code == 200
        assert b'settings.html' in response.data
    
    # Step 2: Update account settings
    with patch('api.routes.auth.current_user') as mock_current_user:
        mock_current_user.is_authenticated = True
        mock_current_user.id = test_user.id
        mock_current_user.username = test_user.username
        
        with patch('api.routes.auth.AccountSettingsForm') as mock_form:
            form_instance = mock_form.return_value
            form_instance.validate_on_submit.return_value = True
            form_instance.email.data = '<EMAIL>'
            form_instance.full_name.data = 'Updated User'
            
            response = client.post('/auth/settings', data={
                'email': '<EMAIL>',
                'full_name': 'Updated User'
            }, follow_redirects=True)
            
            # Check that we're redirected back to settings with success message
            assert response.status_code == 200
            assert b'settings.html' in response.data
            assert b'account settings updated' in response.data
    
    # Step 3: Verify account was updated in database
    with app.app_context():
        user = User.query.filter_by(username='testuser').first()
        assert user.email == '<EMAIL>'
        assert user.full_name == 'Updated User'
