"""Integration tests for the test case execution workflow."""
import pytest
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta
from flask import Flask
from api.routes.testcase import testcase_bp
from api.routes.campaign import campaign_bp
from api.models.campaign import Campaign
from api.models.testcase import TestCase
from api.database import db


@pytest.fixture
def app():
    """Create a Flask app with test configuration."""
    app = Flask(__name__)
    app.config.update({
        'TESTING': True,
        'WTF_CSRF_ENABLED': False,
        'SECRET_KEY': 'test-key',
        'SQLALCHEMY_DATABASE_URI': 'sqlite:///:memory:',
        'SQLALCHEMY_TRACK_MODIFICATIONS': False,
    })
    
    # Register the blueprints
    app.register_blueprint(testcase_bp, url_prefix='/api/testcase')
    app.register_blueprint(campaign_bp, url_prefix='/api/campaign')
    
    # Initialize database
    db.init_app(app)
    
    # Create database tables
    with app.app_context():
        db.create_all()
    
    return app


@pytest.fixture
def client(app):
    """Create a test client for the app."""
    return app.test_client()


@pytest.fixture
def test_campaign(app):
    """Create a test campaign in the database."""
    with app.app_context():
        campaign = Campaign(
            name='Test Campaign',
            description='Test campaign for integration tests',
            created_by=1,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.session.add(campaign)
        db.session.commit()
        return campaign


@pytest.fixture
def test_cases(app, test_campaign):
    """Create test cases in the database."""
    with app.app_context():
        test_cases = []
        for i in range(3):
            test_case = TestCase(
                name=f'Test Case {i+1}',
                description=f'Test case {i+1} for integration tests',
                campaign_id=test_campaign.id,
                expected_result='Pass',
                status='pending',
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            db.session.add(test_case)
            test_cases.append(test_case)
        db.session.commit()
        return test_cases


def test_create_campaign_workflow(app, client):
    """Test the workflow for creating a new campaign."""
    # Step 1: Create a new campaign
    with patch('api.routes.campaign.CampaignCreateForm') as mock_form:
        form_instance = mock_form.return_value
        form_instance.validate_on_submit.return_value = True
        form_instance.name.data = 'New Campaign'
        form_instance.description.data = 'New campaign for testing'
        
        response = client.post('/api/campaign/create', data={
            'name': 'New Campaign',
            'description': 'New campaign for testing'
        }, follow_redirects=True)
        
        # Check response
        assert response.status_code == 200
        assert b'campaign created successfully' in response.data
    
    # Step 2: Verify campaign was created in database
    with app.app_context():
        campaign = Campaign.query.filter_by(name='New Campaign').first()
        assert campaign is not None
        assert campaign.description == 'New campaign for testing'


def test_create_test_case_workflow(app, client, test_campaign):
    """Test the workflow for creating a new test case."""
    # Step 1: Create a new test case
    with patch('api.routes.testcase.TestCaseCreateForm') as mock_form:
        form_instance = mock_form.return_value
        form_instance.validate_on_submit.return_value = True
        form_instance.name.data = 'New Test Case'
        form_instance.description.data = 'New test case for testing'
        form_instance.campaign_id.data = test_campaign.id
        form_instance.expected_result.data = 'Pass'
        
        response = client.post('/api/testcase/create', data={
            'name': 'New Test Case',
            'description': 'New test case for testing',
            'campaign_id': test_campaign.id,
            'expected_result': 'Pass'
        }, follow_redirects=True)
        
        # Check response
        assert response.status_code == 200
        assert b'test case created successfully' in response.data
    
    # Step 2: Verify test case was created in database
    with app.app_context():
        test_case = TestCase.query.filter_by(name='New Test Case').first()
        assert test_case is not None
        assert test_case.description == 'New test case for testing'
        assert test_case.campaign_id == test_campaign.id
        assert test_case.expected_result == 'Pass'
        assert test_case.status == 'pending'


def test_execute_test_case_workflow(app, client, test_cases):
    """Test the workflow for executing a test case."""
    test_case = test_cases[0]
    
    # Step 1: Execute the test case
    with patch('api.routes.testcase.execute_test_case') as mock_execute:
        mock_execute.return_value = {
            'status': 'running',
            'execution_id': 'test-execution-id'
        }
        
        response = client.post(f'/api/testcase/{test_case.id}/execute')
        
        # Check response
        assert response.status_code == 200
        assert response.json['status'] == 'running'
        assert response.json['execution_id'] == 'test-execution-id'
        
        # Check that execute_test_case was called
        mock_execute.assert_called_once_with(test_case.id)
    
    # Step 2: Check execution status
    with patch('api.routes.testcase.get_execution_status') as mock_status:
        mock_status.return_value = {
            'status': 'completed',
            'result': 'passed',
            'execution_time': 5.2,
            'logs': 'Test execution logs...'
        }
        
        response = client.get(f'/api/testcase/execution/test-execution-id/status')
        
        # Check response
        assert response.status_code == 200
        assert response.json['status'] == 'completed'
        assert response.json['result'] == 'passed'
        
        # Check that get_execution_status was called
        mock_status.assert_called_once_with('test-execution-id')
    
    # Step 3: Verify test case status was updated in database
    with app.app_context():
        with patch('api.models.testcase.TestCase.update_status') as mock_update:
            mock_update.return_value = True
            
            # Update the test case status
            test_case = TestCase.query.get(test_case.id)
            test_case.update_status('passed', 'Test passed successfully')
            
            # Check that update_status was called
            mock_update.assert_called_once()


def test_execute_campaign_workflow(app, client, test_campaign, test_cases):
    """Test the workflow for executing a campaign."""
    # Step 1: Execute the campaign
    with patch('api.routes.campaign.execute_campaign') as mock_execute:
        mock_execute.return_value = {
            'status': 'running',
            'execution_id': 'test-campaign-execution-id',
            'test_cases': [{'id': tc.id, 'name': tc.name} for tc in test_cases]
        }
        
        response = client.post(f'/api/campaign/{test_campaign.id}/execute')
        
        # Check response
        assert response.status_code == 200
        assert response.json['status'] == 'running'
        assert response.json['execution_id'] == 'test-campaign-execution-id'
        assert len(response.json['test_cases']) == 3
        
        # Check that execute_campaign was called
        mock_execute.assert_called_once_with(test_campaign.id)
    
    # Step 2: Check campaign execution status
    with patch('api.routes.campaign.get_campaign_execution_status') as mock_status:
        mock_status.return_value = {
            'status': 'completed',
            'progress': 100,
            'test_cases': [
                {'id': test_cases[0].id, 'status': 'passed', 'result': 'Pass'},
                {'id': test_cases[1].id, 'status': 'passed', 'result': 'Pass'},
                {'id': test_cases[2].id, 'status': 'failed', 'result': 'Fail'}
            ],
            'summary': {
                'total': 3,
                'passed': 2,
                'failed': 1,
                'pending': 0,
                'running': 0
            }
        }
        
        response = client.get(f'/api/campaign/execution/test-campaign-execution-id/status')
        
        # Check response
        assert response.status_code == 200
        assert response.json['status'] == 'completed'
        assert response.json['progress'] == 100
        assert response.json['summary']['total'] == 3
        assert response.json['summary']['passed'] == 2
        assert response.json['summary']['failed'] == 1
        
        # Check that get_campaign_execution_status was called
        mock_status.assert_called_once_with('test-campaign-execution-id')


def test_generate_report_workflow(app, client, test_campaign):
    """Test the workflow for generating a campaign report."""
    # Step 1: Generate the report
    with patch('api.routes.campaign.generate_campaign_report') as mock_generate:
        mock_generate.return_value = {
            'report_id': 'test-report-id',
            'status': 'generating'
        }
        
        response = client.post(f'/api/campaign/{test_campaign.id}/report', json={
            'format': 'pdf',
            'include_details': True
        })
        
        # Check response
        assert response.status_code == 200
        assert response.json['report_id'] == 'test-report-id'
        assert response.json['status'] == 'generating'
        
        # Check that generate_campaign_report was called
        mock_generate.assert_called_once_with(
            test_campaign.id, 
            format='pdf', 
            include_details=True
        )
    
    # Step 2: Check report generation status
    with patch('api.routes.campaign.get_report_status') as mock_status:
        mock_status.return_value = {
            'status': 'completed',
            'download_url': '/api/campaign/report/test-report-id/download'
        }
        
        response = client.get(f'/api/campaign/report/test-report-id/status')
        
        # Check response
        assert response.status_code == 200
        assert response.json['status'] == 'completed'
        assert response.json['download_url'] == '/api/campaign/report/test-report-id/download'
        
        # Check that get_report_status was called
        mock_status.assert_called_once_with('test-report-id')
    
    # Step 3: Download the report
    with patch('api.routes.campaign.get_report_file') as mock_file:
        mock_file.return_value = ('report content', 'application/pdf')
        
        response = client.get(f'/api/campaign/report/test-report-id/download')
        
        # Check response
        assert response.status_code == 200
        assert response.data == b'report content'
        assert response.content_type == 'application/pdf'
        
        # Check that get_report_file was called
        mock_file.assert_called_once_with('test-report-id')
