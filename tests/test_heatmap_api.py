"""
Integration tests for heat map API endpoints.

This module provides comprehensive integration tests for the heat map API,
testing the full request-response cycle with proper authentication and
validation.
"""

import pytest
from datetime import datetime, timezone
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from unittest.mock import Mock, patch

from api.main import app
from api.models import (
    AttackTechniqueResult,
    Campaign,
    Assessment,
    MitreTechnique,
    MitreTactic,
    User
)
from api.schemas.heatmap import (
    AttackTechniqueResultCreate,
    AttackTechniqueResultUpdate,
    HeatMapType
)


class TestHeatMapAPI:
    """Test cases for heat map API endpoints."""
    
    @pytest.fixture
    def client(self) -> TestClient:
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    def mock_user(self) -> User:
        """Create mock authenticated user."""
        user = User()
        user.id = 1
        user.username = "testuser"
        user.email = "<EMAIL>"
        user.is_active = True
        return user
    
    @pytest.fixture
    def auth_headers(self) -> dict:
        """Create authentication headers."""
        return {"Authorization": "Bearer test-token"}
    
    @pytest.fixture
    def sample_technique_result_data(self) -> dict:
        """Create sample technique result data for API requests."""
        return {
            "technique_id": "T1566",
            "campaign_id": 1,
            "assessment_id": None,
            "effectiveness_score": 0.8,
            "detection_rate": 0.7,
            "prevention_rate": 0.6,
            "execution_count": 10,
            "success_count": 8,
            "detection_count": 7,
            "prevention_count": 6,
            "test_environment": "test",
            "notes": "Test notes"
        }
    
    @patch('api.routes.v1_heatmap.get_current_user')
    @patch('api.routes.v1_heatmap.get_db')
    def test_create_technique_result_success(
        self,
        mock_get_db: Mock,
        mock_get_current_user: Mock,
        client: TestClient,
        mock_user: User,
        auth_headers: dict,
        sample_technique_result_data: dict
    ) -> None:
        """Test successful technique result creation."""
        # Setup mocks
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_get_current_user.return_value = mock_user
        
        # Mock database queries
        mock_campaign = Mock()
        mock_campaign.id = 1
        mock_technique = Mock()
        mock_technique.technique_id = "T1566"
        
        mock_db.query.return_value.filter.return_value.first.side_effect = [
            mock_campaign,  # Campaign query
            mock_technique,  # Technique query
            None  # Existing result query (should be None)
        ]
        
        # Mock new result creation
        mock_result = Mock()
        mock_result.id = 1
        mock_result.technique_id = "T1566"
        mock_result.campaign_id = 1
        mock_result.effectiveness_score = 0.8
        mock_result.created_at = datetime.now(timezone.utc)
        mock_result.updated_at = datetime.now(timezone.utc)
        
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        
        # Mock AttackTechniqueResult constructor
        with patch('api.routes.v1_heatmap.AttackTechniqueResult', return_value=mock_result):
            # Execute request
            response = client.post(
                "/api/v1/heatmap/technique-results",
                json=sample_technique_result_data,
                headers=auth_headers
            )
        
        # Assertions
        assert response.status_code == 201
        data = response.json()
        assert data["technique_id"] == "T1566"
        assert data["campaign_id"] == 1
        assert data["effectiveness_score"] == 0.8
    
    @patch('api.routes.v1_heatmap.get_current_user')
    @patch('api.routes.v1_heatmap.get_db')
    def test_create_technique_result_campaign_not_found(
        self,
        mock_get_db: Mock,
        mock_get_current_user: Mock,
        client: TestClient,
        mock_user: User,
        auth_headers: dict,
        sample_technique_result_data: dict
    ) -> None:
        """Test technique result creation with non-existent campaign."""
        # Setup mocks
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_get_current_user.return_value = mock_user
        
        # Mock campaign not found
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Execute request
        response = client.post(
            "/api/v1/heatmap/technique-results",
            json=sample_technique_result_data,
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 404
        assert "Campaign 1 not found" in response.json()["detail"]
    
    @patch('api.routes.v1_heatmap.get_current_user')
    @patch('api.routes.v1_heatmap.get_db')
    def test_create_technique_result_validation_error(
        self,
        mock_get_db: Mock,
        mock_get_current_user: Mock,
        client: TestClient,
        mock_user: User,
        auth_headers: dict
    ) -> None:
        """Test technique result creation with validation errors."""
        # Setup mocks
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_get_current_user.return_value = mock_user
        
        # Invalid data (success_count > execution_count)
        invalid_data = {
            "technique_id": "T1566",
            "campaign_id": 1,
            "effectiveness_score": 0.8,
            "execution_count": 5,
            "success_count": 10  # Invalid: greater than execution_count
        }
        
        # Execute request
        response = client.post(
            "/api/v1/heatmap/technique-results",
            json=invalid_data,
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 422
        assert "validation error" in response.json()["detail"][0]["type"]
    
    @patch('api.routes.v1_heatmap.get_current_user')
    @patch('api.routes.v1_heatmap.get_db')
    def test_list_technique_results_success(
        self,
        mock_get_db: Mock,
        mock_get_current_user: Mock,
        client: TestClient,
        mock_user: User,
        auth_headers: dict
    ) -> None:
        """Test successful technique results listing."""
        # Setup mocks
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_get_current_user.return_value = mock_user
        
        # Mock query results
        mock_result = Mock()
        mock_result.id = 1
        mock_result.technique_id = "T1566"
        mock_result.campaign_id = 1
        mock_result.effectiveness_score = 0.8
        mock_result.created_at = datetime.now(timezone.utc)
        mock_result.updated_at = datetime.now(timezone.utc)
        
        # Mock query chain
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [mock_result]
        
        mock_db.query.return_value = mock_query
        
        # Mock pagination
        with patch('api.routes.v1_heatmap.apply_soft_delete_filter', return_value=mock_query):
            with patch('api.routes.v1_heatmap.paginate_query', return_value=[mock_result]):
                # Execute request
                response = client.get(
                    "/api/v1/heatmap/technique-results?campaign_id=1",
                    headers=auth_headers
                )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["technique_id"] == "T1566"
    
    @patch('api.routes.v1_heatmap.get_current_user')
    @patch('api.routes.v1_heatmap.get_db')
    def test_get_technique_result_success(
        self,
        mock_get_db: Mock,
        mock_get_current_user: Mock,
        client: TestClient,
        mock_user: User,
        auth_headers: dict
    ) -> None:
        """Test successful technique result retrieval."""
        # Setup mocks
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_get_current_user.return_value = mock_user
        
        # Mock result
        mock_result = Mock()
        mock_result.id = 1
        mock_result.technique_id = "T1566"
        mock_result.campaign_id = 1
        mock_result.effectiveness_score = 0.8
        
        mock_db.query.return_value.filter.return_value.first.return_value = mock_result
        
        # Execute request
        response = client.get(
            "/api/v1/heatmap/technique-results/1",
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == 1
        assert data["technique_id"] == "T1566"
    
    @patch('api.routes.v1_heatmap.get_current_user')
    @patch('api.routes.v1_heatmap.get_db')
    def test_get_technique_result_not_found(
        self,
        mock_get_db: Mock,
        mock_get_current_user: Mock,
        client: TestClient,
        mock_user: User,
        auth_headers: dict
    ) -> None:
        """Test technique result retrieval with non-existent ID."""
        # Setup mocks
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_get_current_user.return_value = mock_user
        
        # Mock result not found
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Execute request
        response = client.get(
            "/api/v1/heatmap/technique-results/999",
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 404
        assert "Technique result 999 not found" in response.json()["detail"]
    
    @patch('api.routes.v1_heatmap.get_current_user')
    @patch('api.routes.v1_heatmap.get_db')
    def test_update_technique_result_success(
        self,
        mock_get_db: Mock,
        mock_get_current_user: Mock,
        client: TestClient,
        mock_user: User,
        auth_headers: dict
    ) -> None:
        """Test successful technique result update."""
        # Setup mocks
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_get_current_user.return_value = mock_user
        
        # Mock existing result
        mock_result = Mock()
        mock_result.id = 1
        mock_result.technique_id = "T1566"
        mock_result.effectiveness_score = 0.8
        
        mock_db.query.return_value.filter.return_value.first.return_value = mock_result
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        
        # Update data
        update_data = {
            "effectiveness_score": 0.9,
            "notes": "Updated notes"
        }
        
        # Execute request
        response = client.put(
            "/api/v1/heatmap/technique-results/1",
            json=update_data,
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        # Verify setattr was called for effectiveness_score
        assert mock_result.effectiveness_score == 0.8  # Mock doesn't actually update
    
    @patch('api.routes.v1_heatmap.get_current_user')
    @patch('api.routes.v1_heatmap.get_db')
    def test_delete_technique_result_success(
        self,
        mock_get_db: Mock,
        mock_get_current_user: Mock,
        client: TestClient,
        mock_user: User,
        auth_headers: dict
    ) -> None:
        """Test successful technique result soft deletion."""
        # Setup mocks
        mock_db = Mock(spec=Session)
        mock_get_db.return_value = mock_db
        mock_get_current_user.return_value = mock_user
        
        # Mock existing result
        mock_result = Mock()
        mock_result.id = 1
        mock_result.deleted_at = None
        
        mock_db.query.return_value.filter.return_value.first.return_value = mock_result
        mock_db.commit.return_value = None
        
        # Execute request
        response = client.delete(
            "/api/v1/heatmap/technique-results/1",
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 204
        # Verify deleted_at was set (mock doesn't actually update)
        mock_db.commit.assert_called_once()
