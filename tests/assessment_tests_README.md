# Assessment Testing Framework

This directory contains tests for the Assessment Management feature of the Regression Rigor system. The tests are organized into several modules that cover different aspects of the assessment functionality.

## Test Files

- **test_assessment_api.py**: Basic API tests for the assessment endpoints
- **test_assessment_api_extended.py**: Extended API tests covering edge cases, validation, and error handling
- **test_execution_api.py**: Tests focused on test execution functionality
- **test_bulk_assessment_operations.py**: Tests for bulk operations on assessments 

## UI Testing

UI tests using <PERSON>wright are located in the `ui/tests/assessments/` directory:

- **assessment-list.spec.js**: Tests for the assessment list page
- **assessment-form.spec.js**: Tests for the assessment creation/editing form
- **assessment-detail.spec.js**: Tests for the assessment detail page
- **assessment-report.spec.js**: Tests for assessment reports

## Running the Tests

### API Tests

To run the API tests:

```bash
# Run all assessment API tests
pytest tests/test_assessment_api.py tests/test_assessment_api_extended.py tests/test_execution_api.py tests/test_bulk_assessment_operations.py -v

# Run a specific test file
pytest tests/test_assessment_api.py -v

# Run a specific test
pytest tests/test_assessment_api.py::test_get_assessments -v
```

### UI Tests

To run the UI tests:

```bash
# Run all UI tests
cd ui && npm run test:e2e

# Run a specific UI test file
cd ui && npx playwright test tests/assessments/assessment-list.spec.js

# Run a specific test with debugging
cd ui && npx playwright test tests/assessments/assessment-list.spec.js --debug
```

## Test Coverage

The test suite covers:

1. **CRUD Operations**:
   - Create, read, update, and delete assessments
   - Create, read, update, and delete test executions

2. **Filtering, Sorting, and Pagination**:
   - Filtering assessments by status, type, etc.
   - Sorting assessments by various attributes
   - Pagination of assessment lists

3. **Validation**:
   - Input validation for assessments and test executions
   - Date validation (end date after start date)
   - Status validation

4. **Authorization**:
   - Role-based access control testing
   - Admin, operator, and viewer role permissions

5. **Bulk Operations**:
   - Bulk creation of assessments
   - Bulk updating of assessments
   - Bulk deletion of assessments
   - Bulk assignment of campaigns

6. **UI Functionality**:
   - Assessment list display and filtering
   - Assessment creation and editing forms
   - Assessment detail view with tabs
   - Assessment reports with charts and findings

## Test Data Management

Tests create their own test data for isolation. Many tests also clean up after themselves by deleting created resources. Some common test fixtures are shared between test files:

- `admin_token`: JWT token with admin permissions
- `operator_token`: JWT token with operator permissions
- `viewer_token`: JWT token with viewer permissions
- `test_assessment`: A sample assessment for testing
- `test_execution`: A sample test execution for testing

## Adding New Tests

When adding new tests:

1. Use the existing fixtures when possible
2. Clean up any test data you create
3. Keep tests isolated so they can run independently
4. Follow the naming convention of existing tests
5. Add detailed docstrings explaining what the test covers 