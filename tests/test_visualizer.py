"""Unit tests for the attack path visualization component."""
import pytest
from src.components.visualizer import create_attack_path_graph

def test_create_attack_path_graph_empty():
    """Test visualization with empty techniques list."""
    result = create_attack_path_graph([])
    assert isinstance(result, dict)
    assert 'data' in result
    assert 'layout' in result
    assert len(result['data']) == 3  # Edges, techniques, countermeasures

def test_create_attack_path_graph_single_technique():
    """Test visualization with a single technique."""
    techniques = [{
        'technique': {
            'id': 'T1566',
            'name': 'Phishing',
            'description': 'Test phishing technique'
        },
        'coverage': 0.85,
        'countermeasures': [
            {
                'd3fend_id': 'D3-HTTP',
                'name': 'HTTP Traffic Analysis',
                'confidence': 0.9
            }
        ]
    }]
    
    result = create_attack_path_graph(techniques)
    assert isinstance(result, dict)
    assert 'data' in result
    assert 'layout' in result
    
    # Check traces
    assert len(result['data']) == 3
    tech_trace = next(t for t in result['data'] if t['name'] == 'Attack Techniques')
    assert len(tech_trace['x']) == 1  # One technique
    assert len(tech_trace['text']) == 1
    assert tech_trace['text'][0] == 'Phishing'

def test_create_attack_path_graph_multiple_techniques():
    """Test visualization with multiple techniques."""
    techniques = [
        {
            'technique': {
                'id': 'T1566',
                'name': 'Phishing',
                'description': 'Test phishing'
            },
            'coverage': 0.85,
            'countermeasures': [
                {'d3fend_id': 'D3-1', 'name': 'CM1', 'confidence': 0.9}
            ]
        },
        {
            'technique': {
                'id': 'T1078',
                'name': 'Valid Accounts',
                'description': 'Test valid accounts'
            },
            'coverage': 0.75,
            'countermeasures': [
                {'d3fend_id': 'D3-2', 'name': 'CM2', 'confidence': 0.8}
            ]
        }
    ]
    
    result = create_attack_path_graph(techniques)
    tech_trace = next(t for t in result['data'] if t['name'] == 'Attack Techniques')
    assert len(tech_trace['x']) == 2  # Two techniques

def test_graph_layout_properties():
    """Test the graph layout configuration."""
    result = create_attack_path_graph([])
    layout = result['layout']
    
    assert layout['showlegend'] is True
    assert layout['hovermode'] == 'closest'
    assert all(key in layout['margin'] for key in ['b', 'l', 'r', 't'])
    assert not layout['xaxis']['showgrid']
    assert not layout['yaxis']['showgrid']
