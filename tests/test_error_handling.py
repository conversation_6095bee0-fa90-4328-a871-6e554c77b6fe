"""
Tests for the error handling feature.
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session
from api.main import app
from api.models.database.error_handling import ErrorHandling
from api.database import get_db

client = TestClient(app)

def test_create_error_handler(monkeypatch):
    """Test creating a new error handler."""
    # Mock the database session
    def mock_get_db():
        return MockDB()
    
    monkeypatch.setattr("api.endpoints.error_handling.get_db", mock_get_db)
    
    # Test data
    test_data = {
        "name": "Test Error Handler",
        "description": "Test error handler for unit tests",
        "error_type": "validation",
        "error_message": "Invalid input data",
        "is_user_facing": True,
        "http_status_code": 422,
        "severity": "low"
    }
    
    # Make the request
    response = client.post("/api/v1/errors/", json=test_data)
    
    # Check the response
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == test_data["name"]
    assert data["error_type"] == test_data["error_type"]
    assert data["error_message"] == test_data["error_message"]
    assert data["is_user_facing"] == test_data["is_user_facing"]
    assert data["http_status_code"] == test_data["http_status_code"]
    assert data["severity"] == test_data["severity"]
    assert "id" in data
    assert "created_at" in data

def test_get_error_handler(monkeypatch):
    """Test retrieving an error handler by ID."""
    # Mock the database session
    def mock_get_db():
        return MockDB()
    
    monkeypatch.setattr("api.endpoints.error_handling.get_db", mock_get_db)
    
    # Make the request
    response = client.get("/api/v1/errors/1")
    
    # Check the response
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == 1
    assert data["name"] == "Test Error Handler"
    assert data["error_type"] == "validation"

def test_get_all_error_handlers(monkeypatch):
    """Test retrieving all error handlers."""
    # Mock the database session
    def mock_get_db():
        return MockDB()
    
    monkeypatch.setattr("api.endpoints.error_handling.get_db", mock_get_db)
    
    # Make the request
    response = client.get("/api/v1/errors/")
    
    # Check the response
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) > 0
    assert data[0]["id"] == 1
    assert data[0]["name"] == "Test Error Handler"

def test_update_error_handler(monkeypatch):
    """Test updating an error handler."""
    # Mock the database session
    def mock_get_db():
        return MockDB()
    
    monkeypatch.setattr("api.endpoints.error_handling.get_db", mock_get_db)
    
    # Test data
    test_data = {
        "name": "Updated Error Handler",
        "severity": "high"
    }
    
    # Make the request
    response = client.put("/api/v1/errors/1", json=test_data)
    
    # Check the response
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == 1
    assert data["name"] == "Updated Error Handler"
    assert data["severity"] == "high"
    assert data["error_type"] == "validation"  # Unchanged field

def test_delete_error_handler(monkeypatch):
    """Test deleting an error handler."""
    # Mock the database session
    def mock_get_db():
        return MockDB()
    
    monkeypatch.setattr("api.endpoints.error_handling.get_db", mock_get_db)
    
    # Make the request
    response = client.delete("/api/v1/errors/1")
    
    # Check the response
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "Error handler deleted"

def test_error_handler_not_found(monkeypatch):
    """Test error handling when an error handler is not found."""
    # Mock the database session
    def mock_get_db():
        db = MockDB()
        db.error_handler_exists = False
        return db
    
    monkeypatch.setattr("api.endpoints.error_handling.get_db", mock_get_db)
    
    # Make the request
    response = client.get("/api/v1/errors/999")
    
    # Check the response
    assert response.status_code == 404
    data = response.json()
    assert "detail" in data
    assert data["detail"] == "Error handler not found"

def test_middleware_captures_errors(monkeypatch):
    """Test that the middleware captures errors."""
    # Mock the database session
    def mock_get_db():
        return MockDB()
    
    monkeypatch.setattr("api.middleware.error_handler.get_db", mock_get_db)
    
    # Make a request to a non-existent endpoint to trigger a 404 error
    response = client.get("/api/v1/non-existent-endpoint")
    
    # Check the response
    assert response.status_code == 404
    
    # The middleware should have captured the error and stored it in the database
    # We can't easily verify this in a unit test, but we can check that the response
    # has the expected format
    data = response.json()
    assert "detail" in data

# Mock database class for testing
class MockDB:
    """Mock database session for testing."""
    
    def __init__(self):
        """Initialize the mock database."""
        self.error_handler_exists = True
        self.deleted = False
        self.error_handler = ErrorHandling(
            id=1,
            name="Test Error Handler",
            description="Test error handler for unit tests",
            error_type="validation",
            error_message="Invalid input data",
            is_user_facing=True,
            http_status_code=422,
            severity="low"
        )
    
    def query(self, model):
        """Mock query method."""
        return self
    
    def filter(self, condition):
        """Mock filter method."""
        return self
    
    def first(self):
        """Mock first method."""
        if not self.error_handler_exists:
            return None
        return self.error_handler
    
    def all(self):
        """Mock all method."""
        return [self.error_handler]
    
    def offset(self, skip):
        """Mock offset method."""
        return self
    
    def limit(self, limit):
        """Mock limit method."""
        return self
    
    def add(self, obj):
        """Mock add method."""
        if isinstance(obj, ErrorHandling):
            obj.id = 1
            obj.created_at = "2025-03-10T12:00:00"
            obj.updated_at = None
            self.error_handler = obj
    
    def delete(self, obj):
        """Mock delete method."""
        self.deleted = True
    
    def commit(self):
        """Mock commit method."""
        pass
    
    def refresh(self, obj):
        """Mock refresh method."""
        pass 