=================== Coverage for /home/<USER>/workspace/api/ ===================
----------------------------------- Summary ------------------------------------
| Name                             |    Total |    Miss |    Cover |    Cover% |
|----------------------------------|----------|---------|----------|-----------|
| __init__.py                      |        1 |       0 |        1 |      100% |
| database.py                      |        5 |       0 |        5 |      100% |
| main.py                          |        5 |       0 |        5 |      100% |
| models.py                        |        1 |       0 |        1 |      100% |
| schemas.py                       |        4 |       0 |        4 |      100% |
| auth/jwt.py                      |        3 |       3 |        0 |        0% |
| middleware/__init__.py           |        1 |       0 |        1 |      100% |
| middleware/error_handler.py      |        3 |       0 |        3 |      100% |
| middleware/rate_limit.py         |        3 |       3 |        0 |        0% |
| models/__init__.py               |        1 |       0 |        1 |      100% |
| models/base.py                   |        3 |       2 |        1 |       33% |
| models/d3fend.py                 |        4 |       0 |        4 |      100% |
| models/mitre.py                  |        4 |       0 |        4 |      100% |
| models/mitre_defense.py          |        4 |       0 |        4 |      100% |
| models/relationships.py          |        2 |       0 |        2 |      100% |
| models/schemas.py                |       15 |      10 |        5 |       33% |
| models/user.py                   |        2 |       2 |        0 |        0% |
| routes/__init__.py               |        1 |       0 |        1 |      100% |
| routes/v1.py                     |        5 |       0 |        5 |      100% |
| routes/v1_d3fend.py              |        9 |       0 |        9 |      100% |
| routes/v1_mitre.py               |        9 |       0 |        9 |      100% |
| routes/v1_mitre_defense.py       |        5 |       0 |        5 |      100% |
| utils/__init__.py                |        1 |       0 |        1 |      100% |
| utils/d3fend_import.py           |        5 |       0 |        5 |      100% |
| utils/i18n.py                    |        6 |       0 |        6 |      100% |
| utils/mitre_import.py            |        6 |       0 |        6 |      100% |
| utils/scoring.py                 |        2 |       0 |        2 |      100% |
|----------------------------------|----------|---------|----------|-----------|
| TOTAL                            |      110 |      20 |       90 |     81.8% |
---------------- RESULT: FAILED (minimum: 95.0%, actual: 81.8%) ----------------
