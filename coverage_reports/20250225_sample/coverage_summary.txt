Coverage Reports Summary (2025-02-25 19:56)
==================================================

Test Coverage Report
--------------------
Status: FAILED
Location: coverage_reports/20250225_sample/test_coverage

Documentation Coverage Report
------------------------------
Status: WARNING: Below Threshold
Overall Coverage: 81.8%
Files Analyzed: 28
Fully Documented Files: 23

Module-level Coverage:
  auth/jwt.py: 0.0%
  middleware/rate_limit.py: 0.0%
  models/user.py: 0.0%
  models/base.py: 33.0%
  models/schemas.py: 33.0%
  __init__.py: 100.0%
  database.py: 100.0%
  main.py: 100.0%
  models.py: 100.0%
  schemas.py: 100.0%
  models/d3fend.py: 100.0%
  models/mitre.py: 100.0%
  routes/v1_d3fend.py: 100.0%
  routes/v1_mitre.py: 100.0%
  utils/i18n.py: 100.0%
  utils/mitre_import.py: 100.0%

Location: coverage_reports/20250225_sample/doc_coverage

Recommendations:
- Fix failing tests to improve test coverage
- Improve documentation coverage to meet 95% threshold
  Priority: auth/jwt.py (0.0%)
