"""Flask application module for user management and authentication."""
import os
import logging
from flask import Flask, render_template, flash, redirect, url_for, request, jsonify
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, current_user, login_user, login_required, logout_user
from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, PasswordField, EmailField, TextAreaField, SelectField, DateField
from wtforms.validators import DataRequired, Email, Length, EqualTo, Optional
from datetime import datetime, timezone
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from api.models.base import AssessmentDB, CampaignDB, TestCaseDB
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import desc
import psycopg2
from psycopg2.extras import RealDictCursor
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.secret_key = os.environ.get("SESSION_SECRET", "default_secret_key")

# Configure database
DATABASE_URL = os.environ.get("DATABASE_URL", "postgresql://regrigor:regrigor_password@localhost:5440/regrigor_db")

def get_db_connection():
    conn = psycopg2.connect(DATABASE_URL)
    conn.autocommit = True
    return conn

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

@login_manager.user_loader
def load_user(user_id):
    """Load user by ID for Flask-Login."""
    return User.get_by_id(user_id)

# Create all tables
with app.app_context():
    try:
        db.create_all()
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error creating database tables: {str(e)}")
        raise

class SettingsForm(FlaskForm):
    """Form for user settings."""
    username = StringField('Username', validators=[
        DataRequired(),
        Length(min=3, max=64, message='Username must be between 3 and 64 characters')
    ])
    email = EmailField('Email', validators=[
        DataRequired(),
        Email(message='Invalid email address'),
        Length(max=120)
    ])
    current_password = PasswordField('Current Password', validators=[DataRequired()])
    new_password = PasswordField('New Password', validators=[
        Optional(),
        Length(min=8, message='Password must be at least 8 characters long')
    ])

@app.route('/settings', methods=['GET', 'POST'])
@login_required
def settings():
    """Handle user settings updates."""
    form = SettingsForm(obj=current_user)

    if form.validate_on_submit():
        if not current_user.check_password(form.current_password.data):
            flash('Current password is incorrect.', 'danger')
            return render_template('settings.html', form=form)

        try:
            # Check if username is being changed and is not taken
            if form.username.data != current_user.username:
                existing_user = User.get_by_username(form.username.data)
                if existing_user:
                    flash('Username already exists.', 'danger')
                    return render_template('settings.html', form=form)
                current_user.username = form.username.data

            # Check if email is being changed and is not taken
            if form.email.data != current_user.email:
                existing_email = User.get_by_username(form.email.data)
                if existing_email:
                    flash('Email already registered.', 'danger')
                    return render_template('settings.html', form=form)
                current_user.email = form.email.data

            # Update password if provided
            if form.new_password.data:
                current_user.password_hash = generate_password_hash(form.new_password.data)

            db.session.commit()
            flash('Settings updated successfully.', 'success')
            return redirect(url_for('settings'))

        except Exception as e:
            logger.error(f"Error updating settings: {str(e)}")
            db.session.rollback()
            flash('An error occurred while updating settings. Please try again.', 'danger')

    return render_template('settings.html', form=form)

@app.route('/')
def home():
    return jsonify({
        "message": "Regression Rigor Web App",
        "status": "running"
    })

@app.route('/db-status')
def db_status():
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute('SELECT version();')
        version = cursor.fetchone()['version']
        cursor.close()
        conn.close()
        return jsonify({
            "status": "connected",
            "db_version": version
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/init-db')
def init_db():
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Create a simple users table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS flask_users (
            id SERIAL PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password_hash VARCHAR(128) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Insert some test data
        cursor.execute('''
        INSERT INTO flask_users (username, email, password_hash) 
        VALUES ('testuser', '<EMAIL>', $1$)
        ON CONFLICT (username) DO NOTHING
        ''', (generate_password_hash('testpassword'),))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return jsonify({
            "status": "success",
            "message": "Database initialized"
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/users')
def get_users():
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute('SELECT * FROM flask_users')
        users = cursor.fetchall()
        cursor.close()
        conn.close()
        
        return jsonify({
            "status": "success",
            "users": users
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Handle user login."""
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        user = User.get_by_username(username)
        
        if user and user.check_password(password):
            login_user(user)
            next_page = request.args.get('next')
            return redirect(next_page or url_for('index'))
        
        flash('Invalid username or password', 'danger')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """Handle user logout."""
    logout_user()
    return redirect(url_for('login'))

@app.route('/register', methods=['GET', 'POST'])
def register():
    """Handle user registration."""
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        
        # Check if username or email already exists
        existing_user = User.get_by_username(username)
        if existing_user:
            flash('Username already exists. Please choose a different one.', 'danger')
            return render_template('registration.html')
        
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Hash the password
            password_hash = generate_password_hash(password)
            
            # Insert the new user
            cursor.execute(
                'INSERT INTO flask_users (username, email, password_hash) VALUES (%s, %s, %s)',
                (username, email, password_hash)
            )
            
            conn.commit()
            cursor.close()
            conn.close()
            
            flash('Registration successful! Please login.', 'success')
            return redirect(url_for('login'))
        except Exception as e:
            logger.error(f"Error registering user: {e}")
            flash('An error occurred during registration. Please try again.', 'danger')
    
    return render_template('registration.html')

@app.route('/health')
def health():
    """Health check endpoint."""
    try:
        # Test database connection
        db.session.execute("SELECT 1")
        db.session.commit()
        return {"status": "healthy", "database": "connected"}
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {"status": "unhealthy", "error": str(e)}, 503

class LoginForm(FlaskForm):
    """Form for user login."""
    email = EmailField('Email', validators=[
        DataRequired(),
        Email(message='Invalid email address')
    ])
    password = PasswordField('Password', validators=[DataRequired()])

class RegistrationForm(FlaskForm):
    """Form for user registration."""
    username = StringField('Username', validators=[
        DataRequired(),
        Length(min=3, max=64, message='Username must be between 3 and 64 characters')
    ])
    email = EmailField('Email', validators=[
        DataRequired(),
        Email(message='Invalid email address'),
        Length(max=120)
    ])
    password = PasswordField('Password', validators=[
        DataRequired(),
        Length(min=8, message='Password must be at least 8 characters long')
    ])
    confirm_password = PasswordField('Confirm Password', validators=[
        DataRequired(),
        EqualTo('password', message='Passwords must match')
    ])

class AssessmentForm(FlaskForm):
    """Form for creating and editing assessments."""
    name = StringField('Assessment Name', validators=[
        DataRequired(),
        Length(min=3, max=100, message='Name must be between 3 and 100 characters')
    ])
    description = TextAreaField('Description')
    target_system = StringField('Target System', validators=[
        DataRequired(),
        Length(min=3, max=100, message='Target system must be between 3 and 100 characters')
    ])
    assessment_type = SelectField('Assessment Type', choices=[
        ('vulnerability', 'Vulnerability Assessment'),
        ('penetration', 'Penetration Test'),
        ('code_review', 'Code Review'),
        ('compliance', 'Compliance Audit')
    ], validators=[DataRequired()])
    start_date = DateField('Start Date', format='%Y-%m-%d', validators=[DataRequired()])
    end_date = DateField('End Date', format='%Y-%m-%d')
    status = SelectField('Status', choices=[
        ('planned', 'Planned'),
        ('in-progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled')
    ], validators=[DataRequired()])

class CampaignForm(FlaskForm):
    """Form for creating and editing campaigns."""
    name = StringField('Campaign Name', validators=[
        DataRequired(),
        Length(min=3, max=100, message='Name must be between 3 and 100 characters')
    ])
    description = TextAreaField('Description')
    status = SelectField('Status', choices=[
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('completed', 'Completed')
    ], validators=[DataRequired()])

class TestCaseForm(FlaskForm):
    """Form for creating and editing test cases."""
    name = StringField('Test Case Name', validators=[
        DataRequired(),
        Length(min=3, max=100, message='Name must be between 3 and 100 characters')
    ])
    description = TextAreaField('Description')
    campaign_id = SelectField('Campaign', coerce=int, validators=[DataRequired()])
    expected_result = TextAreaField('Expected Result', validators=[DataRequired()])
    actual_result = TextAreaField('Actual Result')
    status = SelectField('Status', choices=[
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('passed', 'Passed'),
        ('failed', 'Failed')
    ], validators=[DataRequired()])

# Assessment routes
@app.route('/assessments')
@login_required
def assessments():
    """List all assessments."""
    assessments = db.session.query(AssessmentDB).filter(
        AssessmentDB.deleted_time.is_(None)
    ).order_by(desc(AssessmentDB.created_time)).all()
    return render_template('assessments/index.html', assessments=assessments)

@app.route('/assessments/new', methods=['GET', 'POST'])
@login_required
def new_assessment():
    """Create a new assessment."""
    form = AssessmentForm()
    
    if form.validate_on_submit():
        try:
            assessment = AssessmentDB(
                name=form.name.data,
                description=form.description.data,
                target_system=form.target_system.data,
                assessment_type=form.assessment_type.data,
                start_date=form.start_date.data,
                end_date=form.end_date.data,
                status=form.status.data,
                created_by=current_user.id
            )
            db.session.add(assessment)
            db.session.commit()
            flash('Assessment created successfully!', 'success')
            return redirect(url_for('assessments'))
        except SQLAlchemyError as e:
            db.session.rollback()
            flash(f'Error creating assessment: {str(e)}', 'danger')
    
    return render_template('assessments/new.html', form=form)

@app.route('/assessments/<int:id>')
@login_required
def view_assessment(id):
    """View a specific assessment."""
    assessment = db.session.query(AssessmentDB).filter_by(id=id).first_or_404()
    return render_template('assessments/view.html', assessment=assessment)

@app.route('/assessments/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_assessment(id):
    """Edit an assessment."""
    assessment = db.session.query(AssessmentDB).filter_by(id=id).first_or_404()
    form = AssessmentForm(obj=assessment)
    
    if form.validate_on_submit():
        try:
            assessment.name = form.name.data
            assessment.description = form.description.data
            assessment.target_system = form.target_system.data
            assessment.assessment_type = form.assessment_type.data
            assessment.start_date = form.start_date.data
            assessment.end_date = form.end_date.data
            assessment.status = form.status.data
            db.session.commit()
            flash('Assessment updated successfully!', 'success')
            return redirect(url_for('view_assessment', id=assessment.id))
        except SQLAlchemyError as e:
            db.session.rollback()
            flash(f'Error updating assessment: {str(e)}', 'danger')
    
    return render_template('assessments/edit.html', form=form, assessment=assessment)

@app.route('/assessments/<int:id>/delete', methods=['POST'])
@login_required
def delete_assessment(id):
    """Delete an assessment."""
    assessment = db.session.query(AssessmentDB).filter_by(id=id).first_or_404()
    try:
        assessment.soft_delete(db.session)
        flash('Assessment deleted successfully!', 'success')
    except SQLAlchemyError as e:
        db.session.rollback()
        flash(f'Error deleting assessment: {str(e)}', 'danger')
    
    return redirect(url_for('assessments'))

# Campaign routes
@app.route('/campaigns')
@login_required
def campaigns():
    """List all campaigns."""
    campaigns = db.session.query(CampaignDB).filter(
        CampaignDB.deleted_time.is_(None)
    ).order_by(desc(CampaignDB.created_time)).all()
    return render_template('campaigns/index.html', campaigns=campaigns)

@app.route('/campaigns/new', methods=['GET', 'POST'])
@login_required
def new_campaign():
    """Create a new campaign."""
    form = CampaignForm()
    
    if form.validate_on_submit():
        try:
            campaign = CampaignDB(
                name=form.name.data,
                description=form.description.data,
                status=form.status.data
            )
            db.session.add(campaign)
            db.session.commit()
            flash('Campaign created successfully!', 'success')
            return redirect(url_for('campaigns'))
        except SQLAlchemyError as e:
            db.session.rollback()
            flash(f'Error creating campaign: {str(e)}', 'danger')
    
    return render_template('campaigns/new.html', form=form)

@app.route('/campaigns/<int:id>')
@login_required
def view_campaign(id):
    """View a specific campaign."""
    campaign = db.session.query(CampaignDB).filter_by(id=id).first_or_404()
    return render_template('campaigns/view.html', campaign=campaign)

@app.route('/campaigns/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_campaign(id):
    """Edit a campaign."""
    campaign = db.session.query(CampaignDB).filter_by(id=id).first_or_404()
    form = CampaignForm(obj=campaign)
    
    if form.validate_on_submit():
        try:
            campaign.name = form.name.data
            campaign.description = form.description.data
            campaign.status = form.status.data
            db.session.commit()
            flash('Campaign updated successfully!', 'success')
            return redirect(url_for('view_campaign', id=campaign.id))
        except SQLAlchemyError as e:
            db.session.rollback()
            flash(f'Error updating campaign: {str(e)}', 'danger')
    
    return render_template('campaigns/edit.html', form=form, campaign=campaign)

@app.route('/campaigns/<int:id>/delete', methods=['POST'])
@login_required
def delete_campaign(id):
    """Delete a campaign."""
    campaign = db.session.query(CampaignDB).filter_by(id=id).first_or_404()
    try:
        campaign.soft_delete(db.session)
        flash('Campaign deleted successfully!', 'success')
    except SQLAlchemyError as e:
        db.session.rollback()
        flash(f'Error deleting campaign: {str(e)}', 'danger')
    
    return redirect(url_for('campaigns'))

# TestCase routes
@app.route('/testcases')
@login_required
def testcases():
    """List all test cases."""
    testcases = db.session.query(TestCaseDB).filter(
        TestCaseDB.deleted_time.is_(None)
    ).order_by(desc(TestCaseDB.created_time)).all()
    return render_template('testcases/index.html', testcases=testcases)

@app.route('/testcases/new', methods=['GET', 'POST'])
@login_required
def new_testcase():
    """Create a new test case."""
    form = TestCaseForm()
    # Populate campaign choices
    form.campaign_id.choices = [
        (c.id, c.name) for c in db.session.query(CampaignDB).filter(
            CampaignDB.deleted_time.is_(None)
        ).order_by(CampaignDB.name).all()
    ]
    
    if form.validate_on_submit():
        try:
            testcase = TestCaseDB(
                name=form.name.data,
                description=form.description.data,
                campaign_id=form.campaign_id.data,
                expected_result=form.expected_result.data,
                actual_result=form.actual_result.data,
                status=form.status.data
            )
            db.session.add(testcase)
            db.session.commit()
            flash('Test case created successfully!', 'success')
            return redirect(url_for('testcases'))
        except SQLAlchemyError as e:
            db.session.rollback()
            flash(f'Error creating test case: {str(e)}', 'danger')
    
    return render_template('testcases/new.html', form=form)

@app.route('/testcases/<int:id>')
@login_required
def view_testcase(id):
    """View a specific test case."""
    testcase = db.session.query(TestCaseDB).filter_by(id=id).first_or_404()
    return render_template('testcases/view.html', testcase=testcase)

@app.route('/testcases/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_testcase(id):
    """Edit a test case."""
    testcase = db.session.query(TestCaseDB).filter_by(id=id).first_or_404()
    form = TestCaseForm(obj=testcase)
    # Populate campaign choices
    form.campaign_id.choices = [
        (c.id, c.name) for c in db.session.query(CampaignDB).filter(
            CampaignDB.deleted_time.is_(None)
        ).order_by(CampaignDB.name).all()
    ]
    
    if form.validate_on_submit():
        try:
            testcase.name = form.name.data
            testcase.description = form.description.data
            testcase.campaign_id = form.campaign_id.data
            testcase.expected_result = form.expected_result.data
            testcase.actual_result = form.actual_result.data
            testcase.status = form.status.data
            db.session.commit()
            flash('Test case updated successfully!', 'success')
            return redirect(url_for('view_testcase', id=testcase.id))
        except SQLAlchemyError as e:
            db.session.rollback()
            flash(f'Error updating test case: {str(e)}', 'danger')
    
    return render_template('testcases/edit.html', form=form, testcase=testcase)

@app.route('/testcases/<int:id>/delete', methods=['POST'])
@login_required
def delete_testcase(id):
    """Delete a test case."""
    testcase = db.session.query(TestCaseDB).filter_by(id=id).first_or_404()
    try:
        testcase.soft_delete(db.session)
        flash('Test case deleted successfully!', 'success')
    except SQLAlchemyError as e:
        db.session.rollback()
        flash(f'Error deleting test case: {str(e)}', 'danger')
    
    return redirect(url_for('testcases'))

# API routes for testing
@app.route('/api/status')
def api_status():
    """API status endpoint."""
    return jsonify({
        "status": "online",
        "timestamp": datetime.now(timezone.utc).isoformat()
    })

@app.route('/api/db-status')
def db_status():
    """Database status check endpoint."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute('SELECT version();')
        version = cursor.fetchone()['version']
        cursor.close()
        conn.close()
        return jsonify({
            "status": "connected",
            "db_version": version
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

if __name__ == '__main__':
    # Always serve the Flask UI on port 5000
    port = int(os.environ.get("PORT", 5000))
    logger.info(f"Starting Flask UI server on port {port}")
    app.run(host='0.0.0.0', port=port, debug=True)