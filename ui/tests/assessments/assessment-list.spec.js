const { test, expect } = require('@playwright/test');

/**
 * Tests for the Assessment List page
 */
test.describe('Assessment List Page', () => {
  test.beforeEach(async ({ page }) => {
    // Login and navigate to assessment list page
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('[data-testid="login-button"]');
    await page.goto('/assessments');
    await expect(page).toHaveURL('/assessments');
  });

  test('displays assessment list with expected columns', async ({ page }) => {
    // Check if the page has loaded with the correct title
    await expect(page.locator('h1')).toContainText('Assessments');
    
    // Verify the table headers are present
    const headers = [
      'Name', 
      'Status', 
      'Type', 
      'Start Date', 
      'End Date', 
      'Created By', 
      'Actions'
    ];
    
    for (const header of headers) {
      await expect(page.locator(`th:has-text("${header}")`)).toBeVisible();
    }
  });

  test('can search for assessments', async ({ page }) => {
    // Type in the search box
    await page.fill('[data-testid="search-input"]', 'Test Assessment');
    await page.press('[data-testid="search-input"]', 'Enter');
    
    // Wait for the search results to load
    await page.waitForLoadState('networkidle');
    
    // Check that the URL includes the search parameter
    expect(page.url()).toContain('search=Test%20Assessment');
  });

  test('can filter assessments by status', async ({ page }) => {
    // Open the status filter dropdown
    await page.click('[data-testid="status-filter"]');
    
    // Select the "In Progress" option
    await page.click('text=In Progress');
    
    // Wait for the filtered results to load
    await page.waitForLoadState('networkidle');
    
    // Check that the URL includes the status filter parameter
    expect(page.url()).toContain('status=in_progress');
    
    // Verify that all visible assessments have the "In Progress" status
    const statusCells = await page.locator('[data-status="in_progress"]').count();
    expect(statusCells).toBeGreaterThan(0);
  });

  test('can sort assessments by different columns', async ({ page }) => {
    // Click on the Name column header to sort by name
    await page.click('th:has-text("Name")');
    
    // Wait for the sorted results to load
    await page.waitForLoadState('networkidle');
    
    // Check that the URL includes the sort parameter
    expect(page.url()).toContain('sort=name');
    
    // Click again to reverse the sort order
    await page.click('th:has-text("Name")');
    
    // Wait for the re-sorted results to load
    await page.waitForLoadState('networkidle');
    
    // Check that the URL includes the reversed sort parameter
    expect(page.url()).toContain('sort=-name');
  });

  test('can paginate through assessment list', async ({ page }) => {
    // Check the current page indicator
    await expect(page.locator('[data-testid="pagination"]')).toContainText('Page 1');
    
    // Go to the next page
    await page.click('[data-testid="next-page"]');
    
    // Wait for the next page to load
    await page.waitForLoadState('networkidle');
    
    // Check that the URL includes the page parameter
    expect(page.url()).toContain('page=2');
    
    // Check the updated page indicator
    await expect(page.locator('[data-testid="pagination"]')).toContainText('Page 2');
  });

  test('can navigate to create new assessment', async ({ page }) => {
    // Click the "New Assessment" button
    await page.click('[data-testid="new-assessment-button"]');
    
    // Verify navigation to the create assessment page
    await expect(page).toHaveURL('/assessments/new');
    
    // Check if the form title is displayed
    await expect(page.locator('h1')).toContainText('Create Assessment');
  });

  test('can navigate to assessment details', async ({ page }) => {
    // Click on the first assessment row or its view button
    await page.click('[data-testid="view-assessment"]:first-child');
    
    // Wait for navigation to complete
    await page.waitForNavigation();
    
    // Check that we've navigated to a detail page with an ID in the URL
    expect(page.url()).toMatch(/\/assessments\/[a-f0-9-]+$/);
    
    // Verify the detail page has loaded
    await expect(page.locator('h1')).toBeVisible();
  });
}); 