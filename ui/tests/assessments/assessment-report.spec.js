const { test, expect } = require('@playwright/test');

/**
 * Tests for the Assessment Report page
 */
test.describe('Assessment Report Page', () => {
  let assessmentId;

  test.beforeEach(async ({ page }) => {
    // Login
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('[data-testid="login-button"]');
    
    // Navigate to assessments page and select the first assessment
    await page.goto('/assessments');
    await page.click('[data-testid="view-assessment"]:first-child');
    
    // Extract the assessment ID from the URL for later use
    const url = page.url();
    assessmentId = url.split('/').pop();
    
    // Navigate directly to the report page
    await page.goto(`/assessments/${assessmentId}/report`);
    
    // Ensure we're on the assessment report page
    await expect(page).toHaveURL(`/assessments/${assessmentId}/report`);
  });

  test('displays assessment report header correctly', async ({ page }) => {
    // Check for the presence of key information in the header
    await expect(page.locator('[data-testid="report-header"]')).toBeVisible();
    
    // Verify assessment name is displayed in the header
    await expect(page.locator('[data-testid="assessment-name"]')).toBeVisible();
    
    // Verify dates are displayed
    await expect(page.locator('[data-testid="assessment-dates"]')).toBeVisible();
  });

  test('shows executive summary section', async ({ page }) => {
    // Check for the executive summary section
    await expect(page.locator('[data-testid="executive-summary-section"]')).toBeVisible();
    await expect(page.locator('h2:has-text("Executive Summary")')).toBeVisible();
    
    // Verify the summary content is not empty
    const summaryText = await page.locator('[data-testid="executive-summary-content"]').textContent();
    expect(summaryText.trim().length).toBeGreaterThan(0);
  });

  test('displays key findings section', async ({ page }) => {
    // Check for the key findings section
    await expect(page.locator('[data-testid="key-findings-section"]')).toBeVisible();
    await expect(page.locator('h2:has-text("Key Findings")')).toBeVisible();
    
    // Check for the findings chart if it exists
    const chartExists = await page.isVisible('[data-testid="findings-chart"]');
    if (chartExists) {
      await expect(page.locator('[data-testid="findings-chart"]')).toBeVisible();
    }
    
    // Check for findings table
    await expect(page.locator('[data-testid="findings-table"]')).toBeVisible();
  });

  test('displays test results section', async ({ page }) => {
    // Check for the test results section
    await expect(page.locator('[data-testid="test-results-section"]')).toBeVisible();
    await expect(page.locator('h2:has-text("Test Results")')).toBeVisible();
    
    // Check for test results summary
    await expect(page.locator('[data-testid="test-results-summary"]')).toBeVisible();
    
    // Check for test cases list
    await expect(page.locator('[data-testid="test-cases-list"]')).toBeVisible();
  });

  test('can navigate to detailed finding', async ({ page }) => {
    // Look for finding items in the key findings section
    const findingExists = await page.isVisible('[data-testid="finding-item"]:first-child');
    
    if (findingExists) {
      // Click on the first finding
      await page.click('[data-testid="finding-item"]:first-child');
      
      // Check that the finding detail dialog is displayed
      await expect(page.locator('[data-testid="finding-detail-dialog"]')).toBeVisible();
      
      // Verify finding details are displayed
      await expect(page.locator('[data-testid="finding-title"]')).toBeVisible();
      await expect(page.locator('[data-testid="finding-severity"]')).toBeVisible();
      await expect(page.locator('[data-testid="finding-description"]')).toBeVisible();
      
      // Close the dialog
      await page.click('[data-testid="close-dialog-button"]');
    }
  });

  test('can export report to PDF', async ({ page }) => {
    // Check for export button
    await expect(page.locator('[data-testid="export-pdf-button"]')).toBeVisible();
    
    // Click export button
    const downloadPromise = page.waitForEvent('download');
    await page.click('[data-testid="export-pdf-button"]');
    const download = await downloadPromise;
    
    // Verify download started
    expect(download.suggestedFilename()).toContain('.pdf');
  });

  test('can print report', async ({ page }) => {
    // This test is a bit tricky since we can't fully test printing
    // But we can check if the print button exists and is clickable
    
    // Check for print button
    await expect(page.locator('[data-testid="print-report-button"]')).toBeVisible();
    
    // We can't fully test printing, but we can confirm the button is clickable
    // and would trigger the print dialog in a real browser
    const isPrintButtonDisabled = await page.isDisabled('[data-testid="print-report-button"]');
    expect(isPrintButtonDisabled).toBe(false);
  });

  test('displays remediation recommendations', async ({ page }) => {
    // Look for remediation section
    await expect(page.locator('[data-testid="remediation-section"]')).toBeVisible();
    await expect(page.locator('h2:has-text("Remediation Recommendations")')).toBeVisible();
    
    // Check for remediation items
    const remediationItemsCount = await page.locator('[data-testid="remediation-item"]').count();
    expect(remediationItemsCount).toBeGreaterThan(0);
  });

  test('displays correct metrics and statistics', async ({ page }) => {
    // Check for metrics section
    await expect(page.locator('[data-testid="metrics-section"]')).toBeVisible();
    
    // Check for specific metrics
    const metricElements = [
      'pass-rate',
      'risk-score',
      'coverage-percentage',
      'total-tests'
    ];
    
    for (const metricElement of metricElements) {
      const isMetricVisible = await page.isVisible(`[data-testid="${metricElement}"]`);
      if (isMetricVisible) {
        await expect(page.locator(`[data-testid="${metricElement}"]`)).toBeVisible();
      }
    }
  });

  test('can navigate back to assessment details', async ({ page }) => {
    // Check for back button
    await expect(page.locator('[data-testid="back-to-details-button"]')).toBeVisible();
    
    // Click back button
    await page.click('[data-testid="back-to-details-button"]');
    
    // Verify we navigate back to the details page
    await expect(page).toHaveURL(`/assessments/${assessmentId}`);
  });
}); 