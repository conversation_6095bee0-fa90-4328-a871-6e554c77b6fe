const { test, expect } = require('@playwright/test');

/**
 * Tests for the Assessment Detail page
 */
test.describe('Assessment Detail Page', () => {
  let assessmentId;

  test.beforeEach(async ({ page }) => {
    // Login
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('[data-testid="login-button"]');
    
    // Navigate to assessments page and select the first assessment
    await page.goto('/assessments');
    await page.click('[data-testid="view-assessment"]:first-child');
    
    // Extract the assessment ID from the URL for later use
    const url = page.url();
    assessmentId = url.split('/').pop();
    
    // Ensure we're on the assessment detail page
    await expect(page).toHaveURL(`/assessments/${assessmentId}`);
    await expect(page.locator('h1')).toBeVisible();
  });

  test('displays assessment header information correctly', async ({ page }) => {
    // Check for the presence of key information in the header
    await expect(page.locator('[data-testid="assessment-header"]')).toBeVisible();
    
    // Verify status chip is visible
    await expect(page.locator('[data-testid="status-chip"]')).toBeVisible();
    
    // Verify type badge is visible
    await expect(page.locator('[data-testid="type-badge"]')).toBeVisible();
    
    // Verify dates are displayed
    await expect(page.locator('[data-testid="assessment-dates"]')).toBeVisible();
  });

  test('can navigate between tabs', async ({ page }) => {
    // Default tab should be Overview
    await expect(page.locator('[data-testid="overview-tab"][aria-selected="true"]')).toBeVisible();
    
    // Navigate to Test Executions tab
    await page.click('[data-testid="executions-tab"]');
    await expect(page.locator('[data-testid="executions-tab"][aria-selected="true"]')).toBeVisible();
    await expect(page.locator('[data-testid="executions-content"]')).toBeVisible();
    
    // Navigate to Findings tab
    await page.click('[data-testid="findings-tab"]');
    await expect(page.locator('[data-testid="findings-tab"][aria-selected="true"]')).toBeVisible();
    await expect(page.locator('[data-testid="findings-content"]')).toBeVisible();
    
    // Navigate to Reports tab
    await page.click('[data-testid="reports-tab"]');
    await expect(page.locator('[data-testid="reports-tab"][aria-selected="true"]')).toBeVisible();
    await expect(page.locator('[data-testid="reports-content"]')).toBeVisible();
    
    // Navigate back to Overview tab
    await page.click('[data-testid="overview-tab"]');
    await expect(page.locator('[data-testid="overview-tab"][aria-selected="true"]')).toBeVisible();
    await expect(page.locator('[data-testid="overview-content"]')).toBeVisible();
  });

  test('overview tab shows correct information', async ({ page }) => {
    // Ensure we're on the overview tab
    await page.click('[data-testid="overview-tab"]');
    
    // Check for key information sections
    await expect(page.locator('[data-testid="description-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="timeline-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="attachments-section"]')).toBeVisible();
    
    // Check for the target systems section
    await expect(page.locator('[data-testid="target-systems-section"]')).toBeVisible();
  });

  test('can add a comment to the assessment', async ({ page }) => {
    // Navigate to overview tab
    await page.click('[data-testid="overview-tab"]');
    
    // Find the comments section
    await page.click('[data-testid="comments-section"]');
    
    // Add a new comment
    const commentText = `Test comment ${Date.now()}`;
    await page.fill('[data-testid="comment-input"]', commentText);
    await page.click('[data-testid="submit-comment"]');
    
    // Verify the comment was added
    await expect(page.locator(`[data-testid="comment-text"]:has-text("${commentText}")`)).toBeVisible();
  });

  test('executions tab shows test executions', async ({ page }) => {
    // Navigate to the executions tab
    await page.click('[data-testid="executions-tab"]');
    
    // Check for the executions table
    await expect(page.locator('[data-testid="executions-table"]')).toBeVisible();
    
    // Check for the add execution button
    await expect(page.locator('[data-testid="add-execution-button"]')).toBeVisible();
  });

  test('can add a new test execution', async ({ page }) => {
    // Navigate to the executions tab
    await page.click('[data-testid="executions-tab"]');
    
    // Click on add execution button
    await page.click('[data-testid="add-execution-button"]');
    
    // Check if the form dialog is displayed
    await expect(page.locator('[data-testid="execution-form-dialog"]')).toBeVisible();
    
    // Fill in the execution details
    const executionName = `Test Execution ${Date.now()}`;
    await page.fill('[data-testid="execution-name"]', executionName);
    await page.selectOption('[data-testid="execution-status"]', 'passed');
    await page.fill('[data-testid="execution-notes"]', 'This is a test execution created by automated tests');
    
    // Select today's date for execution date
    const today = new Date().toISOString().split('T')[0];
    await page.fill('[data-testid="execution-date"]', today);
    
    // Save the execution
    await page.click('[data-testid="save-execution-button"]');
    
    // Wait for the dialog to close
    await expect(page.locator('[data-testid="execution-form-dialog"]')).not.toBeVisible();
    
    // Verify the execution was added to the table
    await expect(page.locator(`[data-testid="execution-name"]:has-text("${executionName}")`)).toBeVisible();
  });

  test('can generate assessment report', async ({ page }) => {
    // Navigate to the reports tab
    await page.click('[data-testid="reports-tab"]');
    
    // Check for generate report button
    await expect(page.locator('[data-testid="generate-report-button"]')).toBeVisible();
    
    // Click on generate report
    await page.click('[data-testid="generate-report-button"]');
    
    // Check if the report options dialog is displayed
    await expect(page.locator('[data-testid="report-options-dialog"]')).toBeVisible();
    
    // Select report format (e.g., PDF)
    await page.selectOption('[data-testid="report-format"]', 'pdf');
    
    // Select report template
    await page.selectOption('[data-testid="report-template"]', 'standard');
    
    // Generate the report
    await page.click('[data-testid="confirm-generate-report"]');
    
    // Wait for report generation success message
    await expect(page.locator('[data-testid="report-success-message"]')).toBeVisible();
    
    // Check that the report is added to the reports list
    await expect(page.locator('[data-testid="reports-list"]')).toContainText('PDF');
  });

  test('can download assessment report', async ({ page }) => {
    // Navigate to the reports tab
    await page.click('[data-testid="reports-tab"]');
    
    // If there are no reports, generate one first
    const reportsExist = await page.isVisible('[data-testid="reports-list"] > *');
    if (!reportsExist) {
      await page.click('[data-testid="generate-report-button"]');
      await page.selectOption('[data-testid="report-format"]', 'pdf');
      await page.selectOption('[data-testid="report-template"]', 'standard');
      await page.click('[data-testid="confirm-generate-report"]');
      await expect(page.locator('[data-testid="report-success-message"]')).toBeVisible();
    }
    
    // Find the download button for the first report
    const downloadPromise = page.waitForEvent('download');
    await page.click('[data-testid="download-report-button"]:first-child');
    const download = await downloadPromise;
    
    // Verify download started
    expect(download.suggestedFilename()).toBeTruthy();
  });

  test('can update assessment status', async ({ page }) => {
    // Check current status
    const currentStatus = await page.textContent('[data-testid="status-chip"]');
    
    // Click on the status chip to open the status change dialog
    await page.click('[data-testid="status-change-button"]');
    
    // Wait for the status change dialog to appear
    await expect(page.locator('[data-testid="status-change-dialog"]')).toBeVisible();
    
    // Select a different status
    // Determine which status to select based on current status
    let newStatus;
    if (currentStatus.toLowerCase().includes('in progress')) {
      newStatus = 'completed';
    } else if (currentStatus.toLowerCase().includes('completed')) {
      newStatus = 'in_progress';
    } else {
      newStatus = 'in_progress';
    }
    
    await page.selectOption('[data-testid="new-status-select"]', newStatus);
    
    // Add a status change reason
    await page.fill('[data-testid="status-change-reason"]', 'Status updated by automated test');
    
    // Confirm status change
    await page.click('[data-testid="confirm-status-change"]');
    
    // Wait for page to update
    await page.waitForLoadState('networkidle');
    
    // Check that status has been updated
    const newStatusText = await page.textContent('[data-testid="status-chip"]');
    expect(newStatusText.toLowerCase()).not.toEqual(currentStatus.toLowerCase());
  });
}); 