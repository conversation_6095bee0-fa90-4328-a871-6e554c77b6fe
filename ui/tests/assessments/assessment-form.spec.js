const { test, expect } = require('@playwright/test');

/**
 * Tests for the Assessment Form (Create and Edit)
 */
test.describe('Assessment Form', () => {
  test.beforeEach(async ({ page }) => {
    // Login and navigate to create assessment page
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('[data-testid="login-button"]');
  });

  test('can create a new assessment with basic information', async ({ page }) => {
    // Navigate to the create assessment page
    await page.goto('/assessments/new');
    
    // Check if we're on the right page
    await expect(page.locator('h1')).toContainText('Create Assessment');
    
    // Fill in required fields
    const assessmentName = `Test Assessment ${Date.now()}`;
    await page.fill('[data-testid="assessment-name"]', assessmentName);
    await page.selectOption('[data-testid="assessment-type"]', 'penetration_test');
    
    // Select start date (today)
    const today = new Date();
    const formattedToday = today.toISOString().split('T')[0];
    await page.fill('[data-testid="start-date"]', formattedToday);
    
    // Select end date (today + 7 days)
    const endDate = new Date();
    endDate.setDate(today.getDate() + 7);
    const formattedEndDate = endDate.toISOString().split('T')[0];
    await page.fill('[data-testid="end-date"]', formattedEndDate);
    
    // Fill description
    await page.fill('[data-testid="assessment-description"]', 'This is a test assessment created by automated tests');
    
    // Submit the form
    await page.click('[data-testid="submit-button"]');
    
    // Wait for redirect to the assessment detail page
    await page.waitForURL(/\/assessments\/[a-f0-9-]+$/);
    
    // Verify we're on a detail page and it shows the correct assessment name
    await expect(page.locator('h1')).toContainText(assessmentName);
  });

  test('validates required fields when creating an assessment', async ({ page }) => {
    // Navigate to the create assessment page
    await page.goto('/assessments/new');
    
    // Try to submit an empty form
    await page.click('[data-testid="submit-button"]');
    
    // Check for validation error messages
    await expect(page.locator('[data-testid="name-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="type-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="start-date-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="end-date-error"]')).toBeVisible();
  });

  test('validates date range when creating an assessment', async ({ page }) => {
    // Navigate to the create assessment page
    await page.goto('/assessments/new');
    
    // Fill in required fields except dates
    await page.fill('[data-testid="assessment-name"]', 'Test Date Validation');
    await page.selectOption('[data-testid="assessment-type"]', 'penetration_test');
    
    // Set end date before start date
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    
    const formattedToday = today.toISOString().split('T')[0];
    const formattedYesterday = yesterday.toISOString().split('T')[0];
    
    await page.fill('[data-testid="start-date"]', formattedToday);
    await page.fill('[data-testid="end-date"]', formattedYesterday);
    
    // Submit the form
    await page.click('[data-testid="submit-button"]');
    
    // Check for date validation error message
    await expect(page.locator('[data-testid="date-range-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="date-range-error"]')).toContainText('End date must be after start date');
  });

  test('can cancel assessment creation and return to list', async ({ page }) => {
    // Navigate to the create assessment page
    await page.goto('/assessments/new');
    
    // Fill in some fields to ensure we're actually canceling an action
    await page.fill('[data-testid="assessment-name"]', 'Assessment to be canceled');
    
    // Click the cancel button
    await page.click('[data-testid="cancel-button"]');
    
    // Verify we're redirected back to the assessments list page
    await expect(page).toHaveURL('/assessments');
  });

  test('can edit an existing assessment', async ({ page }) => {
    // First, we need to create an assessment to edit
    // Let's navigate to the list and find an existing assessment
    await page.goto('/assessments');
    
    // Wait for the assessments to load and click on the first one
    await page.click('[data-testid="view-assessment"]:first-child');
    
    // On the details page, click the edit button
    await page.click('[data-testid="edit-assessment"]');
    
    // Verify we're on the edit page
    await expect(page.locator('h1')).toContainText('Edit Assessment');
    
    // Get the current name
    const currentName = await page.inputValue('[data-testid="assessment-name"]');
    
    // Update the name
    const newName = `Updated ${currentName} ${Date.now()}`;
    await page.fill('[data-testid="assessment-name"]', newName);
    
    // Update the description
    await page.fill('[data-testid="assessment-description"]', 'This description was updated by automated tests');
    
    // Submit the form
    await page.click('[data-testid="submit-button"]');
    
    // Wait for redirect to the assessment detail page
    await page.waitForURL(/\/assessments\/[a-f0-9-]+$/);
    
    // Verify that the update was successful
    await expect(page.locator('h1')).toContainText(newName);
  });

  test('can add participants to an assessment', async ({ page }) => {
    // Navigate to the create assessment page
    await page.goto('/assessments/new');
    
    // Fill in required fields
    const assessmentName = `Participant Test ${Date.now()}`;
    await page.fill('[data-testid="assessment-name"]', assessmentName);
    await page.selectOption('[data-testid="assessment-type"]', 'penetration_test');
    
    // Fill dates
    const today = new Date();
    const formattedToday = today.toISOString().split('T')[0];
    const endDate = new Date();
    endDate.setDate(today.getDate() + 7);
    const formattedEndDate = endDate.toISOString().split('T')[0];
    await page.fill('[data-testid="start-date"]', formattedToday);
    await page.fill('[data-testid="end-date"]', formattedEndDate);
    
    // Open the participants section
    await page.click('[data-testid="participants-section"]');
    
    // Add a participant
    await page.click('[data-testid="add-participant"]');
    await page.fill('[data-testid="participant-name"]', 'Test Participant');
    await page.fill('[data-testid="participant-email"]', '<EMAIL>');
    await page.selectOption('[data-testid="participant-role"]', 'tester');
    
    // Save the participant
    await page.click('[data-testid="save-participant"]');
    
    // Verify the participant was added to the list
    await expect(page.locator('[data-testid="participants-list"]')).toContainText('Test Participant');
    await expect(page.locator('[data-testid="participants-list"]')).toContainText('<EMAIL>');
    
    // Submit the form
    await page.click('[data-testid="submit-button"]');
    
    // Wait for redirect to the assessment detail page
    await page.waitForURL(/\/assessments\/[a-f0-9-]+$/);
    
    // Verify the participants section shows the added participant
    await page.click('[data-testid="participants-tab"]');
    await expect(page.locator('[data-testid="participants-table"]')).toContainText('Test Participant');
  });
}); 