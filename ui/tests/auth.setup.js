const { test: setup, expect } = require('@playwright/test');

const authFile = 'playwright/.auth/user.json';

setup('authenticate', async ({ page }) => {
  // Go to the login page
  await page.goto('/login');
  
  // Fill in login credentials - using test credentials
  await page.fill('[data-testid="email"]', '<EMAIL>');
  await page.fill('[data-testid="password"]', 'Password123!');
  
  // Click the login button and wait for navigation
  await Promise.all([
    page.waitForNavigation(),
    page.click('[data-testid="login-button"]')
  ]);
  
  // Verify that we're logged in by checking for dashboard or user menu
  await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
  
  // Save signed-in state
  await page.context().storageState({ path: authFile });
}); 