import React, { createContext, useContext, useState, useEffect, useMemo } from 'react';
import { createTheme } from '@mui/material/styles';
import { green, cyan, amber, grey } from '@mui/material/colors';

// Context for theme management
const ThemeContext = createContext();

// Custom hook to use the theme context
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Matrix/console inspired color palette
const matrixGreen = {
  main: '#00FF41',
  light: '#33FF65',
  dark: '#008F11',
  contrastText: '#000',
};

const matrixBlack = {
  main: '#0D0208',
  light: '#1A1625',
  dark: '#000000',
  contrastText: '#00FF41',
};

// Theme palettes
const darkPalette = {
  mode: 'dark',
  primary: matrixGreen,
  secondary: {
    main: cyan[500],
    light: cyan[300],
    dark: cyan[700],
  },
  background: {
    paper: '#121212',
    default: '#0D0208',
  },
  text: {
    primary: '#E5E5E5',
    secondary: '#A0A0A0',
    disabled: grey[600],
  },
  success: {
    main: green[500],
    light: green[300],
    dark: green[700],
  },
  warning: {
    main: amber[500],
    light: amber[300],
    dark: amber[700],
  },
  error: {
    main: '#FF5252',
    light: '#FF7F7F',
    dark: '#C50E29',
  },
};

const lightPalette = {
  mode: 'light',
  primary: {
    main: '#2E7D32',
    light: '#4CAF50',
    dark: '#1B5E20',
    contrastText: '#FFF',
  },
  secondary: {
    main: '#0288D1',
    light: '#03A9F4',
    dark: '#01579B',
    contrastText: '#FFF',
  },
  background: {
    paper: '#FFFFFF',
    default: '#F5F5F5',
  },
  text: {
    primary: '#212121',
    secondary: '#757575',
    disabled: grey[400],
  },
};

// Theme provider component
export const ThemeProvider = ({ children }) => {
  // Get saved theme from localStorage or use dark as default
  const [mode, setMode] = useState(() => {
    const savedMode = localStorage.getItem('themeMode');
    return savedMode || 'dark';
  });
  
  // Custom theme name stored in localStorage
  const [customTheme, setCustomTheme] = useState(() => {
    return localStorage.getItem('customTheme') || 'matrix';
  });
  
  // Toggle between light and dark mode
  const toggleColorMode = () => {
    const newMode = mode === 'dark' ? 'light' : 'dark';
    setMode(newMode);
    localStorage.setItem('themeMode', newMode);
  };
  
  // Change custom theme
  const changeCustomTheme = (themeName) => {
    setCustomTheme(themeName);
    localStorage.setItem('customTheme', themeName);
  };
  
  // Create theme based on current mode
  const theme = useMemo(() => {
    const baseTheme = {
      palette: mode === 'dark' ? darkPalette : lightPalette,
      typography: {
        fontFamily: mode === 'dark' && customTheme === 'matrix' 
          ? '"Share Tech Mono", "Roboto Mono", monospace'
          : '"Roboto", "Helvetica", "Arial", sans-serif',
      },
      components: {
        MuiCssBaseline: {
          styleOverrides: {
            body: {
              scrollbarColor: mode === 'dark' ? '#2e2e2e transparent' : '#c1c1c1 transparent',
              '&::-webkit-scrollbar, & *::-webkit-scrollbar': {
                width: '8px',
                height: '8px',
              },
              '&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb': {
                backgroundColor: mode === 'dark' ? '#2e2e2e' : '#c1c1c1',
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb:focus, & *::-webkit-scrollbar-thumb:focus': {
                backgroundColor: mode === 'dark' ? '#3e3e3e' : '#a1a1a1',
              },
              '&::-webkit-scrollbar-track, & *::-webkit-scrollbar-track': {
                backgroundColor: 'transparent',
              },
            },
          },
        },
        // Matrix theme styling
        ...(mode === 'dark' && customTheme === 'matrix' && {
          MuiCard: {
            styleOverrides: {
              root: {
                border: '1px solid #008F11',
                boxShadow: '0 0 10px rgba(0, 255, 65, 0.15)',
              },
            },
          },
          MuiButton: {
            styleOverrides: {
              containedPrimary: {
                background: 'linear-gradient(45deg, #008F11 30%, #00FF41 90%)',
                boxShadow: '0 3px 5px 2px rgba(0, 255, 65, 0.3)',
              },
            },
          },
          MuiTextField: {
            styleOverrides: {
              root: {
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: '#008F11',
                  },
                  '&:hover fieldset': {
                    borderColor: '#00FF41',
                  },
                },
              },
            },
          },
        }),
      },
    };
    
    // Apply custom border radius and shadows
    return createTheme({
      ...baseTheme,
      shape: {
        borderRadius: 8,
      },
      shadows: [
        'none',
        '0px 2px 1px -1px rgba(0,0,0,0.1),0px 1px 1px 0px rgba(0,0,0,0.04),0px 1px 3px 0px rgba(0,0,0,0.02)',
        ...Array(23).fill('').map((_, i) => {
          const intensity = (i + 2) * 0.02;
          return `0px ${Math.floor((i + 2) / 2)}px ${i + 2}px 0px rgba(0,0,0,${intensity})`;
        }),
      ],
    });
  }, [mode, customTheme]);
  
  // Set body background based on theme
  useEffect(() => {
    document.body.style.backgroundColor = theme.palette.background.default;
  }, [theme]);
  
  // Context value
  const contextValue = {
    theme,
    mode,
    toggleColorMode,
    customTheme,
    changeCustomTheme,
    availableThemes: ['matrix', 'standard'],
  };
  
  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeContext; 