import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import CircularProgress from '@mui/material/CircularProgress';
import Box from '@mui/material/Box';

// Theme
import theme from './styles/theme';

// Auth context
import { AuthProvider, useAuth } from './context/AuthContext';

// Pages
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Assessments from './pages/Assessments';
import AssessmentDetail from './pages/AssessmentDetail';
import AssessmentForm from './pages/AssessmentForm';
import Campaigns from './pages/Campaigns';
import CampaignDetail from './pages/CampaignDetail';
import CampaignForm from './pages/CampaignForm';
import Settings from './pages/Settings';
import NotFound from './pages/NotFound';

// Route guard for protected routes
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <Box 
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        minHeight="100vh"
      >
        <CircularProgress />
      </Box>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  return children;
};

// Router configuration
const AppRouter = () => {
  const { isAuthenticated } = useAuth();

  return (
    <Router>
      <Routes>
        {/* Authentication routes */}
        <Route 
          path="/login" 
          element={isAuthenticated ? <Navigate to="/" /> : <Login />} 
        />
        
        {/* Redirect to login if not authenticated, otherwise to dashboard */}
        <Route 
          path="/" 
          element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          } 
        />

        {/* Assessment routes */}
        <Route 
          path="/assessments" 
          element={
            <ProtectedRoute>
              <Assessments />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/assessments/new" 
          element={
            <ProtectedRoute>
              <AssessmentForm />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/assessments/:id" 
          element={
            <ProtectedRoute>
              <AssessmentDetail />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/assessments/:id/edit" 
          element={
            <ProtectedRoute>
              <AssessmentForm isEdit />
            </ProtectedRoute>
          } 
        />

        {/* Campaign routes */}
        <Route 
          path="/campaigns" 
          element={
            <ProtectedRoute>
              <Campaigns />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/campaigns/new" 
          element={
            <ProtectedRoute>
              <CampaignForm />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/campaigns/:id" 
          element={
            <ProtectedRoute>
              <CampaignDetail />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/campaigns/:id/edit" 
          element={
            <ProtectedRoute>
              <CampaignForm isEdit />
            </ProtectedRoute>
          } 
        />

        {/* Settings route */}
        <Route 
          path="/settings" 
          element={
            <ProtectedRoute>
              <Settings />
            </ProtectedRoute>
          } 
        />

        {/* Old UI redirect - will be handled by server configuration */}
        <Route 
          path="/old-ui/*" 
          element={<div>Redirecting to old UI...</div>} 
        />

        {/* 404 route */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Router>
  );
};

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <AppRouter />
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App; 