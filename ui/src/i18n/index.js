import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';

// Supported languages
// English UK, Afrikaans, German, Romanian, <PERSON><PERSON><PERSON>, Zulu
const supportedLanguages = ['en-GB', 'af', 'de', 'ro', 'gsw', 'zu'];

i18n
  // Load translations from /public/locales folder
  .use(Backend)
  // Detect user language
  .use(LanguageDetector)
  // Pass i18n instance to react-i18next
  .use(initReactI18next)
  // Initialize i18next
  .init({
    fallbackLng: 'en-GB',
    debug: process.env.NODE_ENV === 'development',
    
    supportedLngs: supportedLanguages,
    
    // If translation is missing, fallback to key
    saveMissing: true,
    
    // Timeout for loading translations
    timeout: 5000,
    
    detection: {
      // Order of detection methods
      order: ['localStorage', 'navigator', 'htmlTag'],
      
      // Cache language in localStorage
      caches: ['localStorage'],
      
      // Look for language via html lang attribute
      htmlTag: document.documentElement,
      
      // Language cookie options
      lookupCookie: 'i18n',
      cookieExpirationDate: new Date(new Date().getFullYear() + 1, 0, 1), // 1 year
    },
    
    interpolation: {
      // React already protects from XSS
      escapeValue: false,
    },
    
    // React.Suspense wrapper rendering
    react: {
      useSuspense: true,
    },
  });

// Map for displaying language names in their native form
export const languageNames = {
  'en-GB': 'English (UK)',
  'af': 'Afrikaans',
  'de': 'Deutsch',
  'ro': 'Română',
  'gsw': 'Schwiizertüütsch',
  'zu': 'isiZulu',
};

// Helper function to handle RTL languages if needed in the future
export const isRtl = (language) => {
  const rtlLanguages = []; // None of our current languages are RTL
  return rtlLanguages.includes(language);
};

// Helper function to change language
export const changeLanguage = async (language) => {
  if (supportedLanguages.includes(language)) {
    await i18n.changeLanguage(language);
    // Update document language attribute
    document.documentElement.lang = language;
    // Update document dir attribute for RTL languages
    document.documentElement.dir = isRtl(language) ? 'rtl' : 'ltr';
    return true;
  }
  return false;
};

export default i18n; 