import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@mui/material/styles';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Typography,
  CircularProgress,
  Tooltip,
  Button,
  Menu,
  MenuItem,
  Divider,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  ListItemIcon,
  ListItemText,
  Grid,
  TextField,
  Switch,
  FormControlLabel
} from '@mui/material';
import FilterListIcon from '@mui/icons-material/FilterList';
import RefreshIcon from '@mui/icons-material/Refresh';
import DownloadIcon from '@mui/icons-material/Download';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import ImageIcon from '@mui/icons-material/Image';
import Play<PERSON>rrowIcon from '@mui/icons-material/PlayArrow';
import <PERSON><PERSON><PERSON>cleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

// Mock data for techniques
const mockTactics = [
  { techniqueID: 'TA0001', name: 'Initial Access' },
  { techniqueID: 'TA0002', name: 'Execution' },
  { techniqueID: 'TA0003', name: 'Persistence' },
  { techniqueID: 'TA0004', name: 'Privilege Escalation' },
  { techniqueID: 'TA0005', name: 'Defense Evasion' },
  { techniqueID: 'TA0006', name: 'Credential Access' },
  { techniqueID: 'TA0007', name: 'Discovery' },
  { techniqueID: 'TA0008', name: 'Lateral Movement' },
  { techniqueID: 'TA0009', name: 'Collection' },
];

// Function to generate mock technique data
const generateMockTechniques = () => {
  const techniques = [];
  const statuses = ['covered', 'partial', 'not_covered', 'not_applicable'];
  
  for (let i = 0; i < 9; i++) {
    const tacticID = `TA000${i+1}`;
    const techniqueCount = Math.floor(Math.random() * 8) + 3; // 3-10 techniques per tactic
    
    for (let j = 0; j < techniqueCount; j++) {
      const id = j + 1;
      const techniqueID = `T${1000 + (i * 100) + id}`;
      const status = statuses[Math.floor(Math.random() * 3)]; // Random status excluding not_applicable
      const subTechniqueCount = Math.random() > 0.7 ? Math.floor(Math.random() * 3) + 1 : 0;
      
      const technique = {
        techniqueID,
        name: `Technique ${techniqueID}`,
        tacticID,
        status,
        score: status === 'covered' ? Math.floor(Math.random() * 30) + 70 : 
               status === 'partial' ? Math.floor(Math.random() * 40) + 30 : 
               Math.floor(Math.random() * 30),
        testCount: Math.floor(Math.random() * 5) + (status === 'not_covered' ? 0 : 1),
        subTechniques: []
      };
      
      // Add sub-techniques if any
      for (let k = 0; k < subTechniqueCount; k++) {
        const subStatus = statuses[Math.floor(Math.random() * 3)];
        technique.subTechniques.push({
          techniqueID: `${techniqueID}.00${k+1}`,
          name: `Sub-technique ${techniqueID}.00${k+1}`,
          status: subStatus,
          score: subStatus === 'covered' ? Math.floor(Math.random() * 30) + 70 : 
                 subStatus === 'partial' ? Math.floor(Math.random() * 40) + 30 : 
                 Math.floor(Math.random() * 30),
          testCount: Math.floor(Math.random() * 3) + (subStatus === 'not_covered' ? 0 : 1),
        });
      }
      
      techniques.push(technique);
    }
  }
  
  return techniques;
};

// Main Component
const MitreNavigator = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  const [loading, setLoading] = useState(true);
  const [techniques, setTechniques] = useState([]);
  const [filteredTechniques, setFilteredTechniques] = useState([]);
  const [selectedTechnique, setSelectedTechnique] = useState(null);
  const [filterAnchorEl, setFilterAnchorEl] = useState(null);
  const [exportAnchorEl, setExportAnchorEl] = useState(null);
  const [viewMode, setViewMode] = useState('matrix'); // 'matrix' or 'list'
  const [filters, setFilters] = useState({
    covered: true,
    partial: true,
    notCovered: false,
    notApplicable: false,
    search: '',
  });
  const [dialogOpen, setDialogOpen] = useState(false);
  const [fullscreenView, setFullscreenView] = useState(false);

  // Load mock data
  useEffect(() => {
    const timer = setTimeout(() => {
      const mockData = generateMockTechniques();
      setTechniques(mockData);
      applyFilters(mockData, filters);
      setLoading(false);
    }, 1500);
    
    return () => clearTimeout(timer);
  }, []);

  // Apply filters to techniques
  const applyFilters = (data, currentFilters) => {
    let filtered = [...data];
    
    // Filter by status
    filtered = filtered.filter(tech => {
      if (tech.status === 'covered' && !currentFilters.covered) return false;
      if (tech.status === 'partial' && !currentFilters.partial) return false;
      if (tech.status === 'not_covered' && !currentFilters.notCovered) return false;
      if (tech.status === 'not_applicable' && !currentFilters.notApplicable) return false;
      return true;
    });
    
    // Filter by search term
    if (currentFilters.search) {
      const searchTerm = currentFilters.search.toLowerCase();
      filtered = filtered.filter(tech => 
        tech.techniqueID.toLowerCase().includes(searchTerm) ||
        tech.name.toLowerCase().includes(searchTerm)
      );
    }
    
    setFilteredTechniques(filtered);
  };

  // Handle filter menu
  const handleFilterClick = (event) => {
    setFilterAnchorEl(event.currentTarget);
  };

  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };

  const handleFilterChange = (key) => {
    const newFilters = { ...filters, [key]: !filters[key] };
    setFilters(newFilters);
    applyFilters(techniques, newFilters);
  };
  
  // Handle export menu
  const handleExportClick = (event) => {
    setExportAnchorEl(event.currentTarget);
  };

  const handleExportClose = () => {
    setExportAnchorEl(null);
  };
  
  // Handle export options
  const handleExport = (format) => {
    console.log(`Exporting in ${format} format`);
    handleExportClose();
  };
  
  // Handle technique selection
  const handleTechniqueClick = (technique) => {
    setSelectedTechnique(technique);
    setDialogOpen(true);
  };
  
  // Handle technique dialog close
  const handleDialogClose = () => {
    setDialogOpen(false);
  };
  
  // Handle search
  const handleSearchChange = (event) => {
    const searchValue = event.target.value;
    const newFilters = { ...filters, search: searchValue };
    setFilters(newFilters);
    applyFilters(techniques, newFilters);
  };
  
  // Toggle fullscreen view
  const toggleFullscreen = () => {
    setFullscreenView(!fullscreenView);
  };
  
  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'covered': return theme.palette.success.main;
      case 'partial': return theme.palette.warning.main;
      case 'not_covered': return theme.palette.error.main;
      case 'not_applicable': return theme.palette.text.disabled;
      default: return theme.palette.text.primary;
    }
  };
  
  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case 'covered': return <CheckCircleIcon fontSize="small" sx={{ color: 'success.main' }} />;
      case 'partial': return <WarningIcon fontSize="small" sx={{ color: 'warning.main' }} />;
      case 'not_covered': return <ErrorIcon fontSize="small" sx={{ color: 'error.main' }} />;
      case 'not_applicable': return <HelpOutlineIcon fontSize="small" sx={{ color: 'text.disabled' }} />;
      default: return null;
    }
  };
  
  // Get status text
  const getStatusText = (status) => {
    switch (status) {
      case 'covered': return t('mitre.status.covered');
      case 'partial': return t('mitre.status.partial');
      case 'not_covered': return t('mitre.status.notCovered');
      case 'not_applicable': return t('mitre.status.notApplicable');
      default: return '';
    }
  };
  
  // Group techniques by tactic
  const techniquesByTactic = {};
  mockTactics.forEach((tactic) => {
    techniquesByTactic[tactic.techniqueID] = filteredTechniques.filter(
      (tech) => tech.tacticID === tactic.techniqueID
    );
  });

  return (
    <Box sx={{ height: fullscreenView ? '100vh' : 'auto', position: fullscreenView ? 'fixed' : 'relative', top: 0, left: 0, right: 0, bottom: 0, zIndex: fullscreenView ? 1300 : 'auto', bgcolor: 'background.paper', pt: fullscreenView ? 2 : 0 }}>
      {/* Header toolbar */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
          {t('mitre.title')}
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <TextField
            size="small"
            placeholder={t('mitre.searchPlaceholder')}
            variant="outlined"
            value={filters.search}
            onChange={handleSearchChange}
            sx={{ mr: 2, width: 200 }}
          />
          
          <Tooltip title={t('mitre.filters')}>
            <IconButton onClick={handleFilterClick} sx={{ mr: 1 }}>
              <FilterListIcon />
            </IconButton>
          </Tooltip>
          
          <Menu
            anchorEl={filterAnchorEl}
            open={Boolean(filterAnchorEl)}
            onClose={handleFilterClose}
          >
            <MenuItem dense>
              <FormControlLabel
                control={
                  <Switch 
                    checked={filters.covered}
                    onChange={() => handleFilterChange('covered')}
                    color="success"
                    size="small"
                  />
                }
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box component="span" sx={{ width: 12, height: 12, borderRadius: 1, backgroundColor: 'success.main', mr: 1 }} />
                    {t('mitre.status.covered')}
                  </Box>
                }
              />
            </MenuItem>
            <MenuItem dense>
              <FormControlLabel
                control={
                  <Switch 
                    checked={filters.partial}
                    onChange={() => handleFilterChange('partial')}
                    color="warning"
                    size="small"
                  />
                }
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box component="span" sx={{ width: 12, height: 12, borderRadius: 1, backgroundColor: 'warning.main', mr: 1 }} />
                    {t('mitre.status.partial')}
                  </Box>
                }
              />
            </MenuItem>
            <MenuItem dense>
              <FormControlLabel
                control={
                  <Switch 
                    checked={filters.notCovered}
                    onChange={() => handleFilterChange('notCovered')}
                    color="error"
                    size="small"
                  />
                }
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box component="span" sx={{ width: 12, height: 12, borderRadius: 1, backgroundColor: 'error.main', mr: 1 }} />
                    {t('mitre.status.notCovered')}
                  </Box>
                }
              />
            </MenuItem>
            <MenuItem dense>
              <FormControlLabel
                control={
                  <Switch 
                    checked={filters.notApplicable}
                    onChange={() => handleFilterChange('notApplicable')}
                    color="default"
                    size="small"
                  />
                }
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box component="span" sx={{ width: 12, height: 12, borderRadius: 1, backgroundColor: 'text.disabled', mr: 1 }} />
                    {t('mitre.status.notApplicable')}
                  </Box>
                }
              />
            </MenuItem>
          </Menu>
          
          <Tooltip title={t('mitre.export')}>
            <IconButton onClick={handleExportClick} sx={{ mr: 1 }}>
              <DownloadIcon />
            </IconButton>
          </Tooltip>
          
          <Menu
            anchorEl={exportAnchorEl}
            open={Boolean(exportAnchorEl)}
            onClose={handleExportClose}
          >
            <MenuItem onClick={() => handleExport('json')}>
              <ListItemIcon>
                <DownloadIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>JSON</ListItemText>
            </MenuItem>
            <MenuItem onClick={() => handleExport('pdf')}>
              <ListItemIcon>
                <PictureAsPdfIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>PDF</ListItemText>
            </MenuItem>
            <MenuItem onClick={() => handleExport('image')}>
              <ListItemIcon>
                <ImageIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>PNG</ListItemText>
            </MenuItem>
          </Menu>
          
          <Tooltip title={t('mitre.refresh')}>
            <IconButton onClick={() => {
              setLoading(true);
              setTimeout(() => {
                setTechniques(generateMockTechniques());
                setLoading(false);
              }, 1000);
            }} sx={{ mr: 1 }}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title={fullscreenView ? t('mitre.exitFullscreen') : t('mitre.fullscreen')}>
            <IconButton onClick={toggleFullscreen}>
              <FullscreenIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
      
      {/* Matrix View */}
      <Card sx={{ 
        overflow: 'auto', 
        height: fullscreenView ? 'calc(100vh - 80px)' : 'calc(100vh - 240px)',
        boxShadow: theme.shadows[fullscreenView ? 0 : 3],
        border: fullscreenView ? 'none' : `1px solid ${theme.palette.divider}`,
      }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <CircularProgress 
              sx={{ 
                color: theme.palette.mode === 'dark' ? 'primary.main' : 'primary.main',
              }}
            />
            <Typography variant="body1" sx={{ ml: 2 }}>
              {t('mitre.loading')}
            </Typography>
          </Box>
        ) : (
          <Box sx={{ display: 'flex', minWidth: 'max-content' }}>
            {/* Column headers (tactics) */}
            {mockTactics.map((tactic) => (
              <Box key={tactic.techniqueID} sx={{ width: 180, flexShrink: 0 }}>
                <Box 
                  sx={{ 
                    p: 2, 
                    height: 80,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    textAlign: 'center',
                    borderBottom: `1px solid ${theme.palette.divider}`,
                    borderRight: `1px solid ${theme.palette.divider}`,
                    backgroundColor: theme.palette.background.paper,
                    position: 'sticky',
                    top: 0,
                    zIndex: 10,
                  }}
                >
                  <Typography variant="subtitle1" fontWeight="medium">
                    {tactic.name}
                  </Typography>
                </Box>
                
                {/* Techniques */}
                <Box>
                  {techniquesByTactic[tactic.techniqueID]?.map((technique) => (
                    <React.Fragment key={technique.techniqueID}>
                      <Box 
                        sx={{ 
                          p: 1.5,
                          borderBottom: `1px solid ${theme.palette.divider}`,
                          borderRight: `1px solid ${theme.palette.divider}`,
                          backgroundColor: `${getStatusColor(technique.status)}10`,
                          '&:hover': {
                            backgroundColor: `${getStatusColor(technique.status)}20`,
                            cursor: 'pointer',
                          },
                          display: 'flex',
                          flexDirection: 'column',
                        }}
                        onClick={() => handleTechniqueClick(technique)}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
                          <Box>
                            <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                              {technique.techniqueID}
                            </Typography>
                            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                              {technique.name}
                            </Typography>
                          </Box>
                          <Box 
                            sx={{ 
                              ml: 1,
                              width: 7, 
                              height: 7,
                              borderRadius: '50%',
                              backgroundColor: getStatusColor(technique.status),
                              flexShrink: 0,
                              alignSelf: 'flex-start',
                              mt: 0.5,
                            }}
                          />
                        </Box>
                        
                        {technique.subTechniques.length > 0 && (
                          <Box sx={{ mt: 1 }}>
                            {technique.subTechniques.map(subTechnique => (
                              <Box 
                                key={subTechnique.techniqueID}
                                sx={{ 
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'space-between',
                                  p: 0.5,
                                  pl: 1.5,
                                  mt: 0.5,
                                  borderLeft: `2px solid ${theme.palette.divider}`,
                                  borderRadius: '0 4px 4px 0',
                                  backgroundColor: `${getStatusColor(subTechnique.status)}10`,
                                  '&:hover': {
                                    backgroundColor: `${getStatusColor(subTechnique.status)}20`,
                                  },
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleTechniqueClick(subTechnique);
                                }}
                              >
                                <Box>
                                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                                    {subTechnique.techniqueID}
                                  </Typography>
                                  <Typography variant="caption">
                                    {subTechnique.name}
                                  </Typography>
                                </Box>
                                <Box 
                                  sx={{ 
                                    ml: 1,
                                    width: 6, 
                                    height: 6,
                                    borderRadius: '50%',
                                    backgroundColor: getStatusColor(subTechnique.status),
                                    flexShrink: 0,
                                  }}
                                />
                              </Box>
                            ))}
                          </Box>
                        )}
                      </Box>
                    </React.Fragment>
                  ))}
                </Box>
              </Box>
            ))}
          </Box>
        )}
      </Card>
      
      {/* Technique Detail Dialog */}
      {selectedTechnique && (
        <Dialog 
          open={dialogOpen}
          onClose={handleDialogClose}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Typography variant="h6">
                  {selectedTechnique.techniqueID}
                </Typography>
                <Chip 
                  label={getStatusText(selectedTechnique.status)}
                  size="small"
                  icon={getStatusIcon(selectedTechnique.status)}
                  sx={{ 
                    ml: 2,
                    backgroundColor: `${getStatusColor(selectedTechnique.status)}20`,
                    color: getStatusColor(selectedTechnique.status),
                    borderColor: getStatusColor(selectedTechnique.status),
                    border: '1px solid',
                  }}
                />
              </Box>
              <IconButton onClick={handleDialogClose}>
                <InfoOutlinedIcon />
              </IconButton>
            </Box>
            <Typography variant="subtitle1">
              {selectedTechnique.name}
            </Typography>
          </DialogTitle>
          <DialogContent dividers>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  {t('mitre.detectionDetails')}
                </Typography>
                
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    {t('mitre.detectionScore')}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box sx={{ flexGrow: 1, mr: 2 }}>
                      <Box 
                        sx={{ 
                          height: 8, 
                          bgcolor: theme.palette.grey[200], 
                          borderRadius: 4,
                          overflow: 'hidden'
                        }}
                      >
                        <Box 
                          sx={{ 
                            height: '100%', 
                            width: `${selectedTechnique.score}%`,
                            bgcolor: selectedTechnique.score >= 70 ? 'success.main' : 
                                   selectedTechnique.score >= 30 ? 'warning.main' : 'error.main',
                            borderRadius: 4
                          }}
                        />
                      </Box>
                    </Box>
                    <Typography variant="body1" fontWeight="bold">
                      {selectedTechnique.score}%
                    </Typography>
                  </Box>
                </Box>
                
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    {t('mitre.testsAvailable')}
                  </Typography>
                  <Typography variant="body1">
                    {selectedTechnique.testCount}
                  </Typography>
                </Box>
                
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    {t('mitre.testsList')}
                  </Typography>
                  {selectedTechnique.testCount > 0 ? (
                    <Box>
                      {Array.from({ length: selectedTechnique.testCount }).map((_, index) => (
                        <Box 
                          key={index}
                          sx={{ 
                            p: 1.5, 
                            mb: 1,
                            border: `1px solid ${theme.palette.divider}`,
                            borderRadius: 1,
                            '&:hover': {
                              boxShadow: 1,
                              borderColor: theme.palette.primary.main,
                            }
                          }}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            <Typography variant="body2" fontWeight="medium">
                              Test {index + 1} for {selectedTechnique.techniqueID}
                            </Typography>
                            <Button 
                              size="small"
                              startIcon={<PlayArrowIcon />}
                              variant="outlined"
                            >
                              {t('mitre.runTest')}
                            </Button>
                          </Box>
                          <Typography variant="caption" color="text.secondary">
                            {t('mitre.lastRun')}: {Math.floor(Math.random() * 30) + 1} {t('mitre.daysAgo')}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      {t('mitre.noTestsAvailable')}
                    </Typography>
                  )}
                </Box>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  {t('mitre.techniqueInfo')}
                </Typography>
                
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    {t('mitre.description')}
                  </Typography>
                  <Typography variant="body2">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla facilisis, sapien non pharetra semper, sapien lacus molestie nunc, id hendrerit ex tellus eget metus. 
                    Vivamus ultricies laoreet lectus, vitae molestie nisl ultrices sed.
                  </Typography>
                </Box>
                
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    {t('mitre.platforms')}
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {['Windows', 'macOS', 'Linux'].map((platform) => (
                      <Chip 
                        key={platform}
                        label={platform}
                        size="small"
                        sx={{ 
                          backgroundColor: theme.palette.background.paper,
                          border: `1px solid ${theme.palette.divider}`,
                        }}
                      />
                    ))}
                  </Box>
                </Box>
                
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    {t('mitre.tactics')}
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {['Execution', 'Defense Evasion'].map((tactic) => (
                      <Chip 
                        key={tactic}
                        label={tactic}
                        size="small"
                        sx={{ 
                          backgroundColor: theme.palette.primary.main + '20',
                          color: theme.palette.primary.main,
                        }}
                      />
                    ))}
                  </Box>
                </Box>
                
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    {t('mitre.dataSource')}
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {['Process Monitoring', 'Command Execution', 'File Monitoring'].map((source) => (
                      <Chip 
                        key={source}
                        label={source}
                        size="small"
                        sx={{ 
                          backgroundColor: theme.palette.info.main + '20',
                          color: theme.palette.info.main,
                        }}
                      />
                    ))}
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button variant="text" onClick={handleDialogClose}>
              {t('mitre.close')}
            </Button>
            <Button 
              variant="contained"
              color="primary"
              startIcon={<PlayArrowIcon />}
            >
              {t('mitre.runAllTests')}
            </Button>
          </DialogActions>
        </Dialog>
      )}
    </Box>
  );
};

export default MitreNavigator; 