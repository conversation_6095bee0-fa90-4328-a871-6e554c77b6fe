import React from 'react';
import { Outlet } from 'react-router-dom';
import { styled } from '@mui/material/styles';
import { Box, Container, Typography, Link } from '@mui/material';
import LanguageSelector from '../common/LanguageSelector';

// Styled components
const StyledContainer = styled(Container)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  minHeight: '100vh',
  justifyContent: 'center',
  alignItems: 'center',
  padding: theme.spacing(3),
}));

const BackgroundOverlay = styled(Box)(({ theme }) => ({
  position: 'fixed',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  zIndex: -1,
  opacity: 0.5,
  backgroundImage: theme.palette.mode === 'dark' 
    ? 'url(/assets/matrix-bg.jpg)' 
    : 'url(/assets/light-bg.jpg)',
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: theme.palette.mode === 'dark' 
      ? 'rgba(0, 0, 0, 0.8)' 
      : 'rgba(255, 255, 255, 0.8)',
  }
}));

const Footer = styled(Box)(({ theme }) => ({
  marginTop: 'auto',
  padding: theme.spacing(3, 0),
  textAlign: 'center',
}));

const LanguageBox = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: theme.spacing(2),
  right: theme.spacing(2),
}));

const Logo = styled('img')(({ theme }) => ({
  height: 80,
  margin: theme.spacing(0, 0, 4),
}));

const MinimalLayout = () => {
  return (
    <StyledContainer maxWidth="sm">
      <BackgroundOverlay />
      
      <LanguageBox>
        <LanguageSelector />
      </LanguageBox>
      
      <Box 
        sx={{ 
          width: '100%', 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center'
        }}
      >
        <Logo src="/assets/logo.png" alt="Regression Rigor" />
        <Typography 
          variant="h4" 
          component="h1" 
          gutterBottom 
          sx={{ 
            fontWeight: 'bold',
            fontFamily: theme => theme.palette.mode === 'dark' 
              ? '"Share Tech Mono", monospace' 
              : undefined,
            color: theme => theme.palette.primary.main
          }}
        >
          Regression Rigor
        </Typography>
        
        <Box sx={{ width: '100%', mb: 4 }}>
          <Outlet />
        </Box>
      </Box>
      
      <Footer>
        <Typography variant="body2" color="text.secondary">
          © {new Date().getFullYear()} Regression Rigor. All rights reserved.
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          <Link href="#" color="inherit">Privacy Policy</Link>
          {' | '}
          <Link href="#" color="inherit">Terms of Service</Link>
        </Typography>
      </Footer>
    </StyledContainer>
  );
};

export default MinimalLayout; 