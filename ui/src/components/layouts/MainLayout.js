import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { styled, useTheme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../context/AuthContext';
import Box from '@mui/material/Box';
import MuiDrawer from '@mui/material/Drawer';
import MuiAppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import Container from '@mui/material/Container';
import Badge from '@mui/material/Badge';
import Avatar from '@mui/material/Avatar';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Tooltip from '@mui/material/Tooltip';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import Switch from '@mui/material/Switch';
import Collapse from '@mui/material/Collapse';
import InputBase from '@mui/material/InputBase';
import SearchIcon from '@mui/icons-material/Search';
import MenuIcon from '@mui/icons-material/Menu';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import DashboardIcon from '@mui/icons-material/Dashboard';
import AssessmentIcon from '@mui/icons-material/Assessment';
import CampaignIcon from '@mui/icons-material/Campaign';
import CodeIcon from '@mui/icons-material/Code';
import BugReportIcon from '@mui/icons-material/BugReport';
import DescriptionIcon from '@mui/icons-material/Description';
import SettingsIcon from '@mui/icons-material/Settings';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import NotificationsIcon from '@mui/icons-material/Notifications';
import DarkModeIcon from '@mui/icons-material/DarkMode';
import TranslateIcon from '@mui/icons-material/Translate';
import LogoutIcon from '@mui/icons-material/Logout';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import { useTheme as useCustomTheme } from '../../context/ThemeContext';
import LanguageSelector from '../common/LanguageSelector';

// Constants
const drawerWidth = 240;

// Styled components
const openedMixin = (theme) => ({
  width: drawerWidth,
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  overflowX: 'hidden',
});

const closedMixin = (theme) => ({
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  overflowX: 'hidden',
  width: `calc(${theme.spacing(7)} + 1px)`,
  [theme.breakpoints.up('sm')]: {
    width: `calc(${theme.spacing(8)} + 1px)`,
  },
});

const AppBar = styled(MuiAppBar, {
  shouldForwardProp: (prop) => prop !== 'open',
})(({ theme, open }) => ({
  zIndex: theme.zIndex.drawer + 1,
  boxShadow: theme.palette.mode === 'dark' 
    ? '0 4px 10px rgba(0, 255, 65, 0.15)' 
    : theme.shadows[4],
  transition: theme.transitions.create(['width', 'margin'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  ...(open && {
    marginLeft: drawerWidth,
    width: `calc(100% - ${drawerWidth}px)`,
    transition: theme.transitions.create(['width', 'margin'], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
  }),
}));

const Drawer = styled(MuiDrawer, { shouldForwardProp: (prop) => prop !== 'open' })(
  ({ theme, open }) => ({
    width: drawerWidth,
    flexShrink: 0,
    whiteSpace: 'nowrap',
    boxSizing: 'border-box',
    ...(open && {
      ...openedMixin(theme),
      '& .MuiDrawer-paper': openedMixin(theme),
    }),
    ...(!open && {
      ...closedMixin(theme),
      '& .MuiDrawer-paper': closedMixin(theme),
    }),
  }),
);

const DrawerHeader = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'flex-end',
  padding: theme.spacing(0, 1),
  ...theme.mixins.toolbar,
}));

const SearchBar = styled('div')(({ theme }) => ({
  position: 'relative',
  borderRadius: theme.shape.borderRadius,
  backgroundColor: theme.palette.mode === 'dark'
    ? 'rgba(255, 255, 255, 0.05)'
    : 'rgba(0, 0, 0, 0.05)',
  '&:hover': {
    backgroundColor: theme.palette.mode === 'dark'
      ? 'rgba(255, 255, 255, 0.1)'
      : 'rgba(0, 0, 0, 0.1)',
  },
  marginRight: theme.spacing(2),
  marginLeft: 0,
  width: '100%',
  [theme.breakpoints.up('sm')]: {
    marginLeft: theme.spacing(3),
    width: 'auto',
  },
}));

const SearchIconWrapper = styled('div')(({ theme }) => ({
  padding: theme.spacing(0, 2),
  height: '100%',
  position: 'absolute',
  pointerEvents: 'none',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
}));

const StyledInputBase = styled(InputBase)(({ theme }) => ({
  color: 'inherit',
  '& .MuiInputBase-input': {
    padding: theme.spacing(1, 1, 1, 0),
    paddingLeft: `calc(1em + ${theme.spacing(4)})`,
    transition: theme.transitions.create('width'),
    width: '100%',
    [theme.breakpoints.up('md')]: {
      width: '20ch',
    },
  },
}));

// Main layout component
const MainLayout = () => {
  const [open, setOpen] = useState(true);
  const [assessmentsOpen, setAssessmentsOpen] = useState(true);
  const [testsOpen, setTestsOpen] = useState(false);
  const [anchorElUser, setAnchorElUser] = useState(null);
  const [anchorElNotification, setAnchorElNotification] = useState(null);
  
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const { mode, toggleColorMode } = useCustomTheme();
  const { user, logout } = useAuth();
  
  // Handlers
  const handleDrawerToggle = () => {
    setOpen(!open);
  };
  
  const handleUserMenuOpen = (event) => {
    setAnchorElUser(event.currentTarget);
  };
  
  const handleUserMenuClose = () => {
    setAnchorElUser(null);
  };
  
  const handleNotificationOpen = (event) => {
    setAnchorElNotification(event.currentTarget);
  };
  
  const handleNotificationClose = () => {
    setAnchorElNotification(null);
  };
  
  const handleProfileClick = () => {
    handleUserMenuClose();
    navigate('/profile');
  };
  
  const handleSettingsClick = () => {
    handleUserMenuClose();
    navigate('/settings');
  };
  
  const handleLogout = () => {
    handleUserMenuClose();
    logout();
    navigate('/login');
  };
  
  const handleNavigate = (path) => {
    navigate(path);
  };
  
  const handleSearchSubmit = (e) => {
    e.preventDefault();
    const query = e.target.elements.search.value;
    if (query.trim()) {
      navigate(`/search?q=${encodeURIComponent(query)}`);
    }
  };
  
  // Check if a route is active
  const isActive = (path) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };
  
  return (
    <Box sx={{ display: 'flex' }}>
      <AppBar position="fixed" open={open}>
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            onClick={handleDrawerToggle}
            edge="start"
            sx={{
              marginRight: 5,
              ...(open && { display: 'none' }),
            }}
          >
            <MenuIcon />
          </IconButton>
          <Typography
            variant="h6"
            noWrap
            component="div"
            sx={{ 
              display: { xs: 'none', sm: 'block' },
              fontFamily: theme.palette.mode === 'dark' 
                ? '"Share Tech Mono", monospace' 
                : undefined,
            }}
          >
            Regression Rigor
          </Typography>
          
          <form onSubmit={handleSearchSubmit} style={{ flexGrow: 1 }}>
            <SearchBar>
              <SearchIconWrapper>
                <SearchIcon />
              </SearchIconWrapper>
              <StyledInputBase
                placeholder={t('search.placeholder')}
                inputProps={{ 'aria-label': 'search', name: 'search' }}
              />
            </SearchBar>
          </form>
          
          <Box sx={{ flexGrow: 0, display: 'flex', alignItems: 'center' }}>
            <Tooltip title={t('common.toggleTheme')}>
              <Switch
                checked={mode === 'dark'}
                onChange={toggleColorMode}
                icon={<DarkModeIcon fontSize="small" />}
                checkedIcon={<DarkModeIcon fontSize="small" />}
                sx={{ mr: 1 }}
              />
            </Tooltip>
            
            <Tooltip title={t('common.notifications')}>
              <IconButton onClick={handleNotificationOpen} color="inherit">
                <Badge badgeContent={3} color="error">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            </Tooltip>
            
            <Menu
              anchorEl={anchorElNotification}
              open={Boolean(anchorElNotification)}
              onClose={handleNotificationClose}
              PaperProps={{
                elevation: 0,
                sx: {
                  overflow: 'visible',
                  filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
                  mt: 1.5,
                  '& .MuiAvatar-root': {
                    width: 32,
                    height: 32,
                    ml: -0.5,
                    mr: 1,
                  },
                  '&:before': {
                    content: '""',
                    display: 'block',
                    position: 'absolute',
                    top: 0,
                    right: 14,
                    width: 10,
                    height: 10,
                    bgcolor: 'background.paper',
                    transform: 'translateY(-50%) rotate(45deg)',
                    zIndex: 0,
                  },
                },
              }}
              transformOrigin={{ horizontal: 'right', vertical: 'top' }}
              anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
            >
              <MenuItem onClick={handleNotificationClose}>
                <Typography variant="subtitle2">{t('notifications.newCampaign')}</Typography>
              </MenuItem>
              <MenuItem onClick={handleNotificationClose}>
                <Typography variant="subtitle2">{t('notifications.testResults')}</Typography>
              </MenuItem>
              <MenuItem onClick={handleNotificationClose}>
                <Typography variant="subtitle2">{t('notifications.systemUpdate')}</Typography>
              </MenuItem>
              <Divider />
              <MenuItem onClick={handleNotificationClose}>
                <Typography variant="body2">{t('notifications.viewAll')}</Typography>
              </MenuItem>
            </Menu>
            
            <Tooltip title={t('common.userMenu')}>
              <IconButton onClick={handleUserMenuOpen} sx={{ p: 0, ml: 2 }}>
                <Avatar 
                  alt={user?.username || 'User'} 
                  src={user?.avatar || '/assets/avatar.png'}
                  sx={{ 
                    border: theme.palette.mode === 'dark' 
                      ? '1px solid #008F11' 
                      : undefined 
                  }}
                />
              </IconButton>
            </Tooltip>
            
            <Menu
              anchorEl={anchorElUser}
              open={Boolean(anchorElUser)}
              onClose={handleUserMenuClose}
              PaperProps={{
                elevation: 0,
                sx: {
                  overflow: 'visible',
                  filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
                  mt: 1.5,
                  '& .MuiAvatar-root': {
                    width: 32,
                    height: 32,
                    ml: -0.5,
                    mr: 1,
                  },
                  '&:before': {
                    content: '""',
                    display: 'block',
                    position: 'absolute',
                    top: 0,
                    right: 14,
                    width: 10,
                    height: 10,
                    bgcolor: 'background.paper',
                    transform: 'translateY(-50%) rotate(45deg)',
                    zIndex: 0,
                  },
                },
              }}
              transformOrigin={{ horizontal: 'right', vertical: 'top' }}
              anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
            >
              <MenuItem onClick={handleProfileClick}>
                <ListItemIcon>
                  <AccountCircleIcon fontSize="small" />
                </ListItemIcon>
                <Typography variant="body2">{t('user.profile')}</Typography>
              </MenuItem>
              <MenuItem onClick={handleSettingsClick}>
                <ListItemIcon>
                  <SettingsIcon fontSize="small" />
                </ListItemIcon>
                <Typography variant="body2">{t('user.settings')}</Typography>
              </MenuItem>
              <MenuItem>
                <ListItemIcon>
                  <TranslateIcon fontSize="small" />
                </ListItemIcon>
                <LanguageSelector compact />
              </MenuItem>
              <Divider />
              <MenuItem onClick={handleLogout}>
                <ListItemIcon>
                  <LogoutIcon fontSize="small" />
                </ListItemIcon>
                <Typography variant="body2">{t('auth.logout')}</Typography>
              </MenuItem>
            </Menu>
          </Box>
        </Toolbar>
      </AppBar>
      
      <Drawer variant="permanent" open={open}>
        <DrawerHeader>
          <Box
            component="img"
            src="/assets/logo-small.png"
            alt="Logo"
            sx={{ 
              height: 40, 
              mr: 'auto', 
              ml: 1, 
              display: open ? 'block' : 'none'
            }}
          />
          <IconButton onClick={handleDrawerToggle}>
            <ChevronLeftIcon />
          </IconButton>
        </DrawerHeader>
        
        <Divider />
        
        <List>
          <ListItem disablePadding sx={{ display: 'block' }}>
            <ListItemButton
              sx={{
                minHeight: 48,
                justifyContent: open ? 'initial' : 'center',
                px: 2.5,
                backgroundColor: isActive('/dashboard') ? 'action.selected' : 'transparent',
              }}
              onClick={() => handleNavigate('/dashboard')}
            >
              <ListItemIcon
                sx={{
                  minWidth: 0,
                  mr: open ? 3 : 'auto',
                  justifyContent: 'center',
                  color: isActive('/dashboard') ? 'primary.main' : 'inherit',
                }}
              >
                <DashboardIcon />
              </ListItemIcon>
              <ListItemText 
                primary={t('navigation.dashboard')} 
                sx={{ opacity: open ? 1 : 0 }}
                primaryTypographyProps={{
                  color: isActive('/dashboard') ? 'primary' : 'textPrimary',
                  fontWeight: isActive('/dashboard') ? 'bold' : 'normal',
                }}
              />
            </ListItemButton>
          </ListItem>
          
          <ListItem disablePadding sx={{ display: 'block' }}>
            <ListItemButton
              sx={{
                minHeight: 48,
                justifyContent: open ? 'initial' : 'center',
                px: 2.5,
                backgroundColor: isActive('/assessments') ? 'action.selected' : 'transparent',
              }}
              onClick={() => setAssessmentsOpen(!assessmentsOpen)}
            >
              <ListItemIcon
                sx={{
                  minWidth: 0,
                  mr: open ? 3 : 'auto',
                  justifyContent: 'center',
                  color: isActive('/assessments') ? 'primary.main' : 'inherit',
                }}
              >
                <AssessmentIcon />
              </ListItemIcon>
              <ListItemText 
                primary={t('navigation.assessments')} 
                sx={{ opacity: open ? 1 : 0 }}
                primaryTypographyProps={{
                  color: isActive('/assessments') ? 'primary' : 'textPrimary',
                  fontWeight: isActive('/assessments') ? 'bold' : 'normal',
                }}
              />
              {open && (assessmentsOpen ? <ExpandLess /> : <ExpandMore />)}
            </ListItemButton>
            
            <Collapse in={open && assessmentsOpen} timeout="auto" unmountOnExit>
              <List component="div" disablePadding>
                <ListItemButton
                  sx={{ pl: 4 }}
                  onClick={() => handleNavigate('/assessments')}
                >
                  <ListItemText 
                    primary={t('navigation.allAssessments')} 
                    primaryTypographyProps={{
                      color: isActive('/assessments') && location.pathname === '/assessments' ? 'primary' : 'textPrimary',
                      fontSize: '0.875rem',
                    }}
                  />
                </ListItemButton>
                <ListItemButton
                  sx={{ pl: 4 }}
                  onClick={() => handleNavigate('/campaigns')}
                >
                  <ListItemText 
                    primary={t('navigation.campaigns')} 
                    primaryTypographyProps={{
                      color: isActive('/campaigns') ? 'primary' : 'textPrimary',
                      fontSize: '0.875rem',
                    }}
                  />
                </ListItemButton>
              </List>
            </Collapse>
          </ListItem>
          
          <ListItem disablePadding sx={{ display: 'block' }}>
            <ListItemButton
              sx={{
                minHeight: 48,
                justifyContent: open ? 'initial' : 'center',
                px: 2.5,
                backgroundColor: isActive('/testcases') || isActive('/mitre') ? 'action.selected' : 'transparent',
              }}
              onClick={() => setTestsOpen(!testsOpen)}
            >
              <ListItemIcon
                sx={{
                  minWidth: 0,
                  mr: open ? 3 : 'auto',
                  justifyContent: 'center',
                  color: isActive('/testcases') || isActive('/mitre') ? 'primary.main' : 'inherit',
                }}
              >
                <CodeIcon />
              </ListItemIcon>
              <ListItemText 
                primary={t('navigation.tests')} 
                sx={{ opacity: open ? 1 : 0 }}
                primaryTypographyProps={{
                  color: isActive('/testcases') || isActive('/mitre') ? 'primary' : 'textPrimary',
                  fontWeight: isActive('/testcases') || isActive('/mitre') ? 'bold' : 'normal',
                }}
              />
              {open && (testsOpen ? <ExpandLess /> : <ExpandMore />)}
            </ListItemButton>
            
            <Collapse in={open && testsOpen} timeout="auto" unmountOnExit>
              <List component="div" disablePadding>
                <ListItemButton
                  sx={{ pl: 4 }}
                  onClick={() => handleNavigate('/testcases')}
                >
                  <ListItemText 
                    primary={t('navigation.testCases')} 
                    primaryTypographyProps={{
                      color: isActive('/testcases') ? 'primary' : 'textPrimary',
                      fontSize: '0.875rem',
                    }}
                  />
                </ListItemButton>
                <ListItemButton
                  sx={{ pl: 4 }}
                  onClick={() => handleNavigate('/mitre')}
                >
                  <ListItemText 
                    primary={t('navigation.mitreMapping')} 
                    primaryTypographyProps={{
                      color: isActive('/mitre') ? 'primary' : 'textPrimary',
                      fontSize: '0.875rem',
                    }}
                  />
                </ListItemButton>
              </List>
            </Collapse>
          </ListItem>
          
          <ListItem disablePadding sx={{ display: 'block' }}>
            <ListItemButton
              sx={{
                minHeight: 48,
                justifyContent: open ? 'initial' : 'center',
                px: 2.5,
                backgroundColor: isActive('/documentation') ? 'action.selected' : 'transparent',
              }}
              onClick={() => handleNavigate('/documentation')}
            >
              <ListItemIcon
                sx={{
                  minWidth: 0,
                  mr: open ? 3 : 'auto',
                  justifyContent: 'center',
                  color: isActive('/documentation') ? 'primary.main' : 'inherit',
                }}
              >
                <DescriptionIcon />
              </ListItemIcon>
              <ListItemText 
                primary={t('navigation.documentation')} 
                sx={{ opacity: open ? 1 : 0 }}
                primaryTypographyProps={{
                  color: isActive('/documentation') ? 'primary' : 'textPrimary',
                  fontWeight: isActive('/documentation') ? 'bold' : 'normal',
                }}
              />
            </ListItemButton>
          </ListItem>
        </List>
        
        <Divider />
        
        <List>
          <ListItem disablePadding sx={{ display: 'block' }}>
            <ListItemButton
              sx={{
                minHeight: 48,
                justifyContent: open ? 'initial' : 'center',
                px: 2.5,
                backgroundColor: isActive('/settings') ? 'action.selected' : 'transparent',
              }}
              onClick={() => handleNavigate('/settings')}
            >
              <ListItemIcon
                sx={{
                  minWidth: 0,
                  mr: open ? 3 : 'auto',
                  justifyContent: 'center',
                  color: isActive('/settings') ? 'primary.main' : 'inherit',
                }}
              >
                <SettingsIcon />
              </ListItemIcon>
              <ListItemText 
                primary={t('navigation.settings')} 
                sx={{ opacity: open ? 1 : 0 }}
                primaryTypographyProps={{
                  color: isActive('/settings') ? 'primary' : 'textPrimary',
                  fontWeight: isActive('/settings') ? 'bold' : 'normal',
                }}
              />
            </ListItemButton>
          </ListItem>
        </List>
      </Drawer>
      
      <Box component="main" sx={{ flexGrow: 1, p: 3, width: '100%' }}>
        <DrawerHeader />
        <Container maxWidth="xl">
          <Outlet />
        </Container>
      </Box>
    </Box>
  );
};

export default MainLayout; 