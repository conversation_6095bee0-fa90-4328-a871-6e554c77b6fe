import React from 'react';
import { Chip } from '@mui/material';

// Status configuration with color mappings
const statusConfig = {
  active: {
    color: 'success',
    label: 'Active',
  },
  planned: {
    color: 'primary',
    label: 'Planned',
  },
  in_progress: {
    color: 'info',
    label: 'In Progress',
  },
  completed: {
    color: 'success',
    label: 'Completed',
  },
  cancelled: {
    color: 'error',
    label: 'Cancelled',
  },
  archived: {
    color: 'default',
    label: 'Archived',
  },
  draft: {
    color: 'warning',
    label: 'Draft',
  },
  pending: {
    color: 'warning',
    label: 'Pending',
  },
  failed: {
    color: 'error',
    label: 'Failed',
  },
  blocked: {
    color: 'error',
    label: 'Blocked',
  },
  on_hold: {
    color: 'warning',
    label: 'On Hold',
  },
  // Default will be used if status is not found
  default: {
    color: 'default',
    label: 'Unknown',
  }
};

/**
 * A chip component for displaying status indicators
 * 
 * @param {string} status - The status value to display
 * @param {string} size - The size of the chip (small, medium)
 * @param {Object} sx - Additional MUI sx styles to apply
 * @returns {JSX.Element} - The rendered component
 */
function StatusChip({ status, size = 'medium', sx = {} }) {
  // Normalize status to avoid case sensitivity issues and handle spaces/underscores
  const normalizedStatus = status?.toLowerCase().replace(/\s+/g, '_') || 'default';
  
  // Get status configuration or default if not found
  const { color, label } = statusConfig[normalizedStatus] || statusConfig.default;
  
  return (
    <Chip
      label={label}
      color={color}
      size={size}
      sx={{
        fontWeight: 500,
        ...sx,
      }}
    />
  );
}

export default StatusChip; 