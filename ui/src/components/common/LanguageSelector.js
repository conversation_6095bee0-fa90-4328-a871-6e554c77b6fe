import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { styled } from '@mui/material/styles';
import {
  Box,
  Button,
  IconButton,
  Menu,
  MenuItem,
  ListItemText,
  ListItemIcon,
  Tooltip
} from '@mui/material';
import TranslateIcon from '@mui/icons-material/Translate';
import CheckIcon from '@mui/icons-material/Check';
import { languageNames } from '../../i18n';

// Styled components
const StyledMenuItem = styled(MenuItem)(({ theme, selected }) => ({
  padding: theme.spacing(1, 2),
  backgroundColor: selected ? 
    theme.palette.mode === 'dark' ? 
      'rgba(0, 255, 65, 0.1)' : 
      'rgba(46, 125, 50, 0.1)' 
    : 'transparent',
  '&:hover': {
    backgroundColor: selected ? 
      theme.palette.mode === 'dark' ? 
        'rgba(0, 255, 65, 0.2)' : 
        'rgba(46, 125, 50, 0.2)' 
      : undefined,
  }
}));

// Language selector component
const LanguageSelector = ({ compact = false }) => {
  const { i18n } = useTranslation();
  const [anchorEl, setAnchorEl] = useState(null);
  
  // Current language
  const currentLanguage = i18n.language || window.localStorage.getItem('i18nextLng') || 'en-GB';
  
  // Handlers
  const handleOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };
  
  const handleClose = () => {
    setAnchorEl(null);
  };
  
  const handleLanguageChange = (language) => {
    i18n.changeLanguage(language);
    document.documentElement.lang = language;
    handleClose();
  };
  
  // Render compact version
  if (compact) {
    return (
      <>
        <ListItemText primary={languageNames[currentLanguage]} />
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleClose}
          PaperProps={{
            elevation: 0,
            sx: {
              maxHeight: 300,
              width: 200,
              overflow: 'auto',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
            },
          }}
        >
          {Object.entries(languageNames).map(([code, name]) => (
            <StyledMenuItem
              key={code}
              onClick={() => handleLanguageChange(code)}
              selected={currentLanguage === code}
            >
              <ListItemText primary={name} />
              {currentLanguage === code && (
                <ListItemIcon sx={{ minWidth: 'auto' }}>
                  <CheckIcon fontSize="small" />
                </ListItemIcon>
              )}
            </StyledMenuItem>
          ))}
        </Menu>
      </>
    );
  }
  
  // Render full version
  return (
    <Box>
      <Tooltip title="Change language">
        <IconButton 
          onClick={handleOpen}
          sx={{
            color: 'white',
            bgcolor: 'rgba(0, 0, 0, 0.2)',
            '&:hover': {
              bgcolor: 'rgba(0, 0, 0, 0.4)',
            },
            border: theme => theme.palette.mode === 'dark' ? 
              '1px solid rgba(255, 255, 255, 0.1)' : 
              'none',
          }}
        >
          <TranslateIcon />
        </IconButton>
      </Tooltip>
      
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          elevation: 0,
          sx: {
            maxHeight: 300,
            width: 200,
            overflow: 'auto',
            filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
          },
        }}
      >
        {Object.entries(languageNames).map(([code, name]) => (
          <StyledMenuItem
            key={code}
            onClick={() => handleLanguageChange(code)}
            selected={currentLanguage === code}
          >
            <ListItemText primary={name} />
            {currentLanguage === code && (
              <ListItemIcon sx={{ minWidth: 'auto' }}>
                <CheckIcon fontSize="small" />
              </ListItemIcon>
            )}
          </StyledMenuItem>
        ))}
      </Menu>
    </Box>
  );
};

export default LanguageSelector; 