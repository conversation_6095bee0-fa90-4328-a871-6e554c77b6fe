import React from 'react';
import { 
  Card, 
  CardContent, 
  Typography, 
  Box,
  Avatar,
} from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  cursor: 'pointer',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[6],
  },
}));

const IconAvatar = styled(Avatar)(({ theme, bgColor }) => ({
  width: 52,
  height: 52,
  backgroundColor: bgColor || theme.palette.primary.main,
  color: '#fff',
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.1)',
}));

function DashboardSummaryCard({ title, value, icon, color, onClick }) {
  return (
    <StyledCard elevation={3} onClick={onClick}>
      <CardContent>
        <Box display="flex" alignItems="center" mb={2}>
          <IconAvatar bgColor={color}>
            {icon}
          </IconAvatar>
          <Box ml={2}>
            <Typography 
              variant="body2" 
              color="text.secondary"
              gutterBottom
            >
              {title}
            </Typography>
            <Typography 
              variant="h4" 
              component="div"
              fontWeight="bold"
            >
              {value}
            </Typography>
          </Box>
        </Box>
      </CardContent>
    </StyledCard>
  );
}

export default DashboardSummaryCard; 