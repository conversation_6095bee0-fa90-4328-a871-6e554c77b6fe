import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8010/api/v1';

const assessmentService = {
  // Get all assessments with optional filters
  getAllAssessments: async (params = {}) => {
    try {
      const response = await axios.get(`${API_URL}/assessments`, {
        params,
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get assessment by ID
  getAssessmentById: async (id) => {
    try {
      const response = await axios.get(`${API_URL}/assessments/${id}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Create new assessment
  createAssessment: async (assessmentData) => {
    try {
      const response = await axios.post(`${API_URL}/assessments`, assessmentData, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Update assessment
  updateAssessment: async (id, assessmentData) => {
    try {
      const response = await axios.put(`${API_URL}/assessments/${id}`, assessmentData, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Delete assessment
  deleteAssessment: async (id) => {
    try {
      const response = await axios.delete(`${API_URL}/assessments/${id}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Restore assessment
  restoreAssessment: async (id) => {
    try {
      const response = await axios.post(`${API_URL}/assessments/${id}/restore`, {}, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get assessment results
  getAssessmentResults: async (id) => {
    try {
      const response = await axios.get(`${API_URL}/assessments/${id}/results`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get assessment report
  getAssessmentReport: async (id) => {
    try {
      const response = await axios.get(`${API_URL}/assessments/${id}/report`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Add campaign to assessment
  addCampaignToAssessment: async (assessmentId, campaignId) => {
    try {
      const response = await axios.post(`${API_URL}/assessments/${assessmentId}/campaigns/${campaignId}`, {}, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Remove campaign from assessment
  removeCampaignFromAssessment: async (assessmentId, campaignId) => {
    try {
      const response = await axios.delete(`${API_URL}/assessments/${assessmentId}/campaigns/${campaignId}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};

export default assessmentService; 