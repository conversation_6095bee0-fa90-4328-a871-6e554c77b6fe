import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import assessmentService from '../../services/assessmentService';
import { 
  Container, 
  Typography, 
  Box, 
  Button, 
  Paper, 
  Grid,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  List,
  ListItem,
  ListItemText,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip
} from '@mui/material';
import { 
  ArrowBack as ArrowBackIcon,
  ExpandMore as ExpandMoreIcon,
  Security as SecurityIcon,
  BugReport as BugReportIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Download as DownloadIcon
} from '@mui/icons-material';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import { Pie } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(ArcElement, <PERSON><PERSON><PERSON>, <PERSON>);

const AssessmentReport = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  
  const [assessment, setAssessment] = useState(null);
  const [report, setReport] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch assessment details
        const assessmentData = await assessmentService.getAssessmentById(id);
        setAssessment(assessmentData);
        
        // Fetch assessment report
        const reportData = await assessmentService.getAssessmentReport(id);
        setReport(reportData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching report data:', err);
        setError('Failed to load assessment report. Please try again later.');
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const getSeverityColor = (severity) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return '#d32f2f'; // red
      case 'high':
        return '#f44336'; // light red
      case 'medium':
        return '#ff9800'; // orange
      case 'low':
        return '#ffeb3b'; // yellow
      case 'info':
        return '#2196f3'; // blue
      default:
        return '#757575'; // gray
    }
  };

  const getSeverityIcon = (severity) => {
    switch (severity.toLowerCase()) {
      case 'critical':
      case 'high':
        return <ErrorIcon style={{ color: getSeverityColor(severity) }} />;
      case 'medium':
        return <WarningIcon style={{ color: getSeverityColor(severity) }} />;
      case 'low':
        return <BugReportIcon style={{ color: getSeverityColor(severity) }} />;
      case 'info':
        return <CheckCircleIcon style={{ color: getSeverityColor(severity) }} />;
      default:
        return null;
    }
  };

  const handleDownloadReport = () => {
    // Placeholder for report download functionality
    alert('Report download functionality will be implemented here');
  };

  // Prepare chart data if report exists
  const getChartData = () => {
    if (!report || !report.findings) return null;
    
    // Count findings by severity
    const severityCounts = {
      critical: 0,
      high: 0,
      medium: 0,
      low: 0,
      info: 0
    };
    
    report.findings.forEach(finding => {
      const severity = finding.severity.toLowerCase();
      if (severityCounts.hasOwnProperty(severity)) {
        severityCounts[severity]++;
      }
    });
    
    return {
      labels: ['Critical', 'High', 'Medium', 'Low', 'Info'],
      datasets: [
        {
          label: 'Findings by Severity',
          data: [
            severityCounts.critical,
            severityCounts.high,
            severityCounts.medium,
            severityCounts.low,
            severityCounts.info
          ],
          backgroundColor: [
            '#d32f2f', // Critical - red
            '#f44336', // High - light red
            '#ff9800', // Medium - orange
            '#ffeb3b', // Low - yellow
            '#2196f3'  // Info - blue
          ],
          borderWidth: 1
        }
      ]
    };
  };

  if (loading) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4 }}>
          <Button 
            startIcon={<ArrowBackIcon />} 
            onClick={() => navigate(`/assessments/${id}`)}
            sx={{ mb: 2 }}
          >
            Back to Assessment
          </Button>
          <Alert severity="error">{error}</Alert>
        </Box>
      </Container>
    );
  }

  if (!assessment || !report) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4 }}>
          <Button 
            startIcon={<ArrowBackIcon />} 
            onClick={() => navigate(`/assessments/${id}`)}
            sx={{ mb: 2 }}
          >
            Back to Assessment
          </Button>
          <Alert severity="warning">No report data is available for this assessment.</Alert>
        </Box>
      </Container>
    );
  }

  const chartData = getChartData();

  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Button 
              startIcon={<ArrowBackIcon />} 
              onClick={() => navigate(`/assessments/${id}`)}
              sx={{ mb: 2 }}
            >
              Back to Assessment
            </Button>
            <Typography variant="h4" component="h1">
              Assessment Report: {assessment.name}
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              {assessment.target_system} | {formatDate(assessment.start_date)} - {formatDate(assessment.end_date)}
            </Typography>
          </Box>
          <Button 
            variant="contained" 
            startIcon={<DownloadIcon />}
            onClick={handleDownloadReport}
          >
            Download Report
          </Button>
        </Box>
        
        {/* Executive Summary */}
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h5" gutterBottom>
            Executive Summary
          </Typography>
          <Typography variant="body1" paragraph>
            {report.executive_summary || 'No executive summary is available for this report.'}
          </Typography>
          
          {chartData && (
            <Grid container spacing={3} sx={{ mt: 2 }}>
              <Grid item xs={12} md={6}>
                <Box sx={{ maxWidth: 400, mx: 'auto' }}>
                  <Typography variant="h6" align="center" gutterBottom>
                    Findings by Severity
                  </Typography>
                  <Pie data={chartData} />
                </Box>
              </Grid>
              <Grid item xs={12} md={6}>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Severity</TableCell>
                        <TableCell align="right">Count</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {['Critical', 'High', 'Medium', 'Low', 'Info'].map((severity) => {
                        const count = chartData.datasets[0].data[chartData.labels.indexOf(severity)];
                        return (
                          <TableRow key={severity}>
                            <TableCell>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                {getSeverityIcon(severity)}
                                {severity}
                              </Box>
                            </TableCell>
                            <TableCell align="right">{count}</TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Grid>
            </Grid>
          )}
        </Paper>
        
        {/* Assessment Details */}
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h5" gutterBottom>
            Assessment Details
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Assessment Information
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText primary="Assessment Type" secondary={assessment.assessment_type || 'N/A'} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="Status" secondary={assessment.status || 'N/A'} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="Start Date" secondary={formatDate(assessment.start_date)} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="End Date" secondary={formatDate(assessment.end_date)} />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Target System
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText primary="System Name" secondary={assessment.target_system || 'N/A'} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="Environment" secondary={assessment.environment ? assessment.environment.name : 'N/A'} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="Scope" secondary={report.scope || 'N/A'} />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Paper>
        
        {/* Findings */}
        {report.findings && report.findings.length > 0 ? (
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h5" gutterBottom>
              Findings
            </Typography>
            
            {report.findings.map((finding, index) => (
              <Accordion key={index} sx={{ mb: 2 }}>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                    {getSeverityIcon(finding.severity)}
                    <Typography sx={{ ml: 1, flexGrow: 1 }}>
                      {finding.title}
                    </Typography>
                    <Chip 
                      label={finding.severity} 
                      size="small"
                      sx={{ 
                        backgroundColor: getSeverityColor(finding.severity),
                        color: 'white',
                        ml: 2
                      }} 
                    />
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Typography variant="subtitle1">Description</Typography>
                      <Typography variant="body2" paragraph>
                        {finding.description}
                      </Typography>
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle1">Impact</Typography>
                      <Typography variant="body2" paragraph>
                        {finding.impact}
                      </Typography>
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle1">Affected Components</Typography>
                      <Typography variant="body2" paragraph>
                        {finding.affected_components || 'N/A'}
                      </Typography>
                    </Grid>
                    
                    <Grid item xs={12}>
                      <Divider sx={{ my: 1 }} />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <Typography variant="subtitle1">Remediation</Typography>
                      <Typography variant="body2" paragraph>
                        {finding.remediation}
                      </Typography>
                    </Grid>
                    
                    {finding.references && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle1">References</Typography>
                        <List dense>
                          {finding.references.map((reference, idx) => (
                            <ListItem key={idx}>
                              <ListItemText primary={reference} />
                            </ListItem>
                          ))}
                        </List>
                      </Grid>
                    )}
                  </Grid>
                </AccordionDetails>
              </Accordion>
            ))}
          </Paper>
        ) : (
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h5" gutterBottom>
              Findings
            </Typography>
            <Alert severity="info">No findings are available in this report.</Alert>
          </Paper>
        )}
        
        {/* Recommendations */}
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h5" gutterBottom>
            Recommendations
          </Typography>
          {report.recommendations && report.recommendations.length > 0 ? (
            <List>
              {report.recommendations.map((recommendation, index) => (
                <ListItem key={index} alignItems="flex-start">
                  <ListItemText
                    primary={recommendation.title}
                    secondary={recommendation.description}
                  />
                </ListItem>
              ))}
            </List>
          ) : (
            <Alert severity="info">No recommendations are available in this report.</Alert>
          )}
        </Paper>
        
        {/* Test Results Summary */}
        <Paper sx={{ p: 3 }}>
          <Typography variant="h5" gutterBottom>
            Test Results Summary
          </Typography>
          {report.test_results ? (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Test Name</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Execution Date</TableCell>
                    <TableCell align="right">Pass Rate</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {report.test_results.map((test, index) => (
                    <TableRow key={index}>
                      <TableCell>{test.name}</TableCell>
                      <TableCell>
                        <Chip 
                          label={test.status} 
                          color={test.status === 'Passed' ? 'success' : test.status === 'Failed' ? 'error' : 'warning'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{formatDate(test.execution_date)}</TableCell>
                      <TableCell align="right">{test.pass_rate}%</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Alert severity="info">No test results are available in this report.</Alert>
          )}
        </Paper>
      </Box>
    </Container>
  );
};

export default AssessmentReport; 