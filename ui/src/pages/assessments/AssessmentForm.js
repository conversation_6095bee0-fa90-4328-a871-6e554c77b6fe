import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import assessmentService from '../../services/assessmentService';
import { 
  Container, 
  Typography, 
  Box, 
  Button, 
  Paper, 
  Grid,
  TextField,
  MenuItem,
  FormControl,
  FormHelperText,
  InputLabel,
  Select,
  Alert,
  CircularProgress,
  Divider,
  Chip
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ArrowBack as ArrowBackIcon, Save as SaveIcon } from '@mui/icons-material';

const assessmentTypes = [
  { value: 'vulnerability', label: 'Vulnerability Assessment' },
  { value: 'penetration', label: 'Penetration Testing' },
  { value: 'code_review', label: 'Code Review' },
  { value: 'red_team', label: 'Red Team Exercise' },
  { value: 'compliance', label: 'Compliance Audit' },
  { value: 'custom', label: 'Custom Assessment' }
];

const assessmentStatuses = [
  { value: 'pending', label: 'Pending' },
  { value: 'in_progress', label: 'In Progress' },
  { value: 'completed', label: 'Completed' },
  { value: 'reviewed', label: 'Reviewed' },
  { value: 'cancelled', label: 'Cancelled' }
];

const AssessmentForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditMode = Boolean(id);

  const [loading, setLoading] = useState(isEditMode);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    target_system: '',
    assessment_type: 'vulnerability',
    status: 'pending',
    start_date: null,
    end_date: null,
    environment_id: '',
    campaign_ids: []
  });

  // Form validation errors
  const [errors, setErrors] = useState({});

  // Available campaigns for selection
  const [availableCampaigns, setAvailableCampaigns] = useState([]);

  useEffect(() => {
    // Fetch campaigns for selection
    const fetchCampaigns = async () => {
      try {
        // This is a placeholder assuming there's a campaignService
        // const campaigns = await campaignService.getAllCampaigns();
        // setAvailableCampaigns(campaigns);
        
        // For now, use mock data
        setAvailableCampaigns([
          { id: 1, name: 'Q1 Security Assessment' },
          { id: 2, name: 'Web Application Security' },
          { id: 3, name: 'Cloud Infrastructure Audit' }
        ]);
      } catch (err) {
        console.error('Error fetching campaigns:', err);
      }
    };

    fetchCampaigns();

    // If in edit mode, fetch the assessment data
    if (isEditMode) {
      fetchAssessment();
    }
  }, [isEditMode, id]);

  const fetchAssessment = async () => {
    try {
      const data = await assessmentService.getAssessmentById(id);
      
      // Format dates for form inputs
      const formattedData = {
        ...data,
        start_date: data.start_date ? new Date(data.start_date) : null,
        end_date: data.end_date ? new Date(data.end_date) : null,
        campaign_ids: data.campaigns ? data.campaigns.map(c => c.id) : []
      };
      
      setFormData(formattedData);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching assessment:', err);
      setError('Failed to load assessment. Please try again later.');
      setLoading(false);
    }
  };

  const handleChange = (event) => {
    const { name, value } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear validation error when field is changed
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  const handleDateChange = (name, date) => {
    setFormData(prev => ({
      ...prev,
      [name]: date
    }));
    
    // Clear validation error when field is changed
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.length < 3) {
      newErrors.name = 'Name must be at least 3 characters';
    }
    
    if (!formData.target_system.trim()) {
      newErrors.target_system = 'Target system is required';
    }
    
    if (formData.start_date && formData.end_date && formData.start_date > formData.end_date) {
      newErrors.end_date = 'End date must be after start date';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    
    // Validate form
    if (!validateForm()) {
      return;
    }
    
    setSaving(true);
    setError(null);
    setSuccess(false);
    
    try {
      // Prepare data for API
      const apiData = {
        ...formData,
        // Format dates for API
        start_date: formData.start_date ? formData.start_date.toISOString() : null,
        end_date: formData.end_date ? formData.end_date.toISOString() : null
      };
      
      let result;
      if (isEditMode) {
        result = await assessmentService.updateAssessment(id, apiData);
      } else {
        result = await assessmentService.createAssessment(apiData);
      }
      
      setSuccess(true);
      setSaving(false);
      
      // Navigate back after a short delay
      setTimeout(() => {
        if (isEditMode) {
          navigate(`/assessments/${id}`);
        } else {
          navigate(`/assessments/${result.id}`);
        }
      }, 1500);
    } catch (err) {
      console.error('Error saving assessment:', err);
      setError(isEditMode ? 'Failed to update assessment. Please try again.' : 'Failed to create assessment. Please try again.');
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Container maxWidth="md">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Box sx={{ mb: 4 }}>
          <Button 
            startIcon={<ArrowBackIcon />} 
            onClick={() => navigate(isEditMode ? `/assessments/${id}` : '/assessments')}
            sx={{ mb: 2 }}
          >
            {isEditMode ? 'Back to Assessment' : 'Back to Assessments'}
          </Button>
          <Typography variant="h4" component="h1">
            {isEditMode ? 'Edit Assessment' : 'Create New Assessment'}
          </Typography>
        </Box>
        
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}
        
        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            Assessment {isEditMode ? 'updated' : 'created'} successfully!
          </Alert>
        )}
        
        <Paper sx={{ p: 3 }}>
          <form onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Assessment Details
                </Typography>
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  name="name"
                  label="Assessment Name"
                  value={formData.name}
                  onChange={handleChange}
                  fullWidth
                  required
                  error={Boolean(errors.name)}
                  helperText={errors.name}
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  name="description"
                  label="Description"
                  value={formData.description || ''}
                  onChange={handleChange}
                  fullWidth
                  multiline
                  rows={4}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={Boolean(errors.assessment_type)}>
                  <InputLabel>Assessment Type</InputLabel>
                  <Select
                    name="assessment_type"
                    value={formData.assessment_type}
                    onChange={handleChange}
                    label="Assessment Type"
                  >
                    {assessmentTypes.map(type => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.assessment_type && (
                    <FormHelperText>{errors.assessment_type}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  name="target_system"
                  label="Target System"
                  value={formData.target_system || ''}
                  onChange={handleChange}
                  fullWidth
                  required
                  error={Boolean(errors.target_system)}
                  helperText={errors.target_system}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={Boolean(errors.status)}>
                  <InputLabel>Status</InputLabel>
                  <Select
                    name="status"
                    value={formData.status}
                    onChange={handleChange}
                    label="Status"
                  >
                    {assessmentStatuses.map(status => (
                      <MenuItem key={status.value} value={status.value}>
                        {status.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.status && (
                    <FormHelperText>{errors.status}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Environment</InputLabel>
                  <Select
                    name="environment_id"
                    value={formData.environment_id || ''}
                    onChange={handleChange}
                    label="Environment"
                  >
                    <MenuItem value="">None</MenuItem>
                    <MenuItem value="1">Production</MenuItem>
                    <MenuItem value="2">Staging</MenuItem>
                    <MenuItem value="3">Development</MenuItem>
                    <MenuItem value="4">Testing</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <Divider sx={{ my: 1 }} />
                <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                  Schedule
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="Start Date"
                    value={formData.start_date}
                    onChange={(date) => handleDateChange('start_date', date)}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        error: Boolean(errors.start_date),
                        helperText: errors.start_date
                      }
                    }}
                  />
                </LocalizationProvider>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="End Date"
                    value={formData.end_date}
                    onChange={(date) => handleDateChange('end_date', date)}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        error: Boolean(errors.end_date),
                        helperText: errors.end_date
                      }
                    }}
                  />
                </LocalizationProvider>
              </Grid>
              
              <Grid item xs={12}>
                <Divider sx={{ my: 1 }} />
                <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                  Campaigns
                </Typography>
              </Grid>
              
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Associated Campaigns</InputLabel>
                  <Select
                    name="campaign_ids"
                    multiple
                    value={formData.campaign_ids || []}
                    onChange={handleChange}
                    label="Associated Campaigns"
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => {
                          const campaign = availableCampaigns.find(c => c.id === value);
                          return (
                            <Chip key={value} label={campaign ? campaign.name : `ID: ${value}`} />
                          );
                        })}
                      </Box>
                    )}
                  >
                    {availableCampaigns.map(campaign => (
                      <MenuItem key={campaign.id} value={campaign.id}>
                        {campaign.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sx={{ mt: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                  <Button 
                    variant="outlined"
                    onClick={() => navigate(isEditMode ? `/assessments/${id}` : '/assessments')}
                  >
                    Cancel
                  </Button>
                  <Button 
                    type="submit"
                    variant="contained" 
                    color="primary"
                    startIcon={<SaveIcon />}
                    disabled={saving}
                  >
                    {saving ? 'Saving...' : (isEditMode ? 'Update Assessment' : 'Create Assessment')}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </form>
        </Paper>
      </Box>
    </Container>
  );
};

export default AssessmentForm; 