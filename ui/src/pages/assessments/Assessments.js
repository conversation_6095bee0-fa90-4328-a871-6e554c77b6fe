import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import assessmentService from '../../services/assessmentService';
import { 
  Container, 
  Typography, 
  Box, 
  Button, 
  Paper, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  TextField,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Grid,
  Divider,
  Alert,
  CircularProgress
} from '@mui/material';
import { 
  Add as AddIcon, 
  Edit as EditIcon, 
  Delete as DeleteIcon, 
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  Sort as SortIcon,
  Visibility as VisibilityIcon,
  Assignment as AssignmentIcon
} from '@mui/icons-material';
import { format } from 'date-fns';

const statusColors = {
  'pending': 'info',
  'in_progress': 'warning',
  'completed': 'success',
  'reviewed': 'secondary',
  'cancelled': 'error'
};

const assessmentTypes = [
  'vulnerability',
  'penetration',
  'code_review',
  'red_team',
  'compliance',
  'custom'
];

const Assessments = () => {
  const navigate = useNavigate();
  const [assessments, setAssessments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  
  // Filter and sort state
  const [filters, setFilters] = useState({
    name: '',
    status: '',
    assessment_type: '',
    target_system: ''
  });
  const [sortField, setSortField] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const [openFilters, setOpenFilters] = useState(false);
  
  // Delete confirmation dialog
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [assessmentToDelete, setAssessmentToDelete] = useState(null);

  useEffect(() => {
    fetchAssessments();
  }, [page, rowsPerPage, sortField, sortOrder]);

  const fetchAssessments = async () => {
    setLoading(true);
    setError(null);
    try {
      const params = {
        page: page + 1,
        limit: rowsPerPage,
        sort_by: sortField,
        sort_order: sortOrder,
        ...filters
      };
      
      const data = await assessmentService.getAllAssessments(params);
      setAssessments(data.items || data);
      setTotalCount(data.total || data.length);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching assessments:', err);
      setError('Failed to load assessments. Please try again later.');
      setLoading(false);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSort = (field) => {
    if (field === sortField) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
  };

  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    setFilters({
      ...filters,
      [name]: value
    });
  };

  const applyFilters = () => {
    setPage(0);
    fetchAssessments();
    setOpenFilters(false);
  };

  const resetFilters = () => {
    setFilters({
      name: '',
      status: '',
      assessment_type: '',
      target_system: ''
    });
  };

  const handleDeleteClick = (assessment) => {
    setAssessmentToDelete(assessment);
    setOpenDeleteDialog(true);
  };

  const handleConfirmDelete = async () => {
    if (!assessmentToDelete) return;
    
    try {
      await assessmentService.deleteAssessment(assessmentToDelete.id);
      setOpenDeleteDialog(false);
      fetchAssessments();
    } catch (err) {
      console.error('Error deleting assessment:', err);
      setError('Failed to delete assessment. Please try again later.');
      setOpenDeleteDialog(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (e) {
      return 'Invalid date';
    }
  };

  return (
    <Container maxWidth="xl">
      <Box sx={{ my: 4 }}>
        <Grid container spacing={2} alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
          <Grid item>
            <Typography variant="h4" component="h1" gutterBottom>
              Assessments
            </Typography>
          </Grid>
          <Grid item>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button 
                variant="outlined" 
                startIcon={<FilterIcon />}
                onClick={() => setOpenFilters(!openFilters)}
              >
                Filters
              </Button>
              <Button 
                variant="outlined" 
                startIcon={<RefreshIcon />}
                onClick={fetchAssessments}
              >
                Refresh
              </Button>
              <Button 
                variant="contained" 
                color="primary" 
                startIcon={<AddIcon />}
                onClick={() => navigate('/assessments/new')}
              >
                New Assessment
              </Button>
            </Box>
          </Grid>
        </Grid>

        {openFilters && (
          <Paper sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>Filters</Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  label="Name"
                  name="name"
                  value={filters.name}
                  onChange={handleFilterChange}
                  fullWidth
                  margin="normal"
                  variant="outlined"
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  label="Status"
                  name="status"
                  select
                  value={filters.status}
                  onChange={handleFilterChange}
                  fullWidth
                  margin="normal"
                  variant="outlined"
                  size="small"
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="in_progress">In Progress</MenuItem>
                  <MenuItem value="completed">Completed</MenuItem>
                  <MenuItem value="reviewed">Reviewed</MenuItem>
                  <MenuItem value="cancelled">Cancelled</MenuItem>
                </TextField>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  label="Assessment Type"
                  name="assessment_type"
                  select
                  value={filters.assessment_type}
                  onChange={handleFilterChange}
                  fullWidth
                  margin="normal"
                  variant="outlined"
                  size="small"
                >
                  <MenuItem value="">All</MenuItem>
                  {assessmentTypes.map(type => (
                    <MenuItem key={type} value={type}>
                      {type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' ')}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  label="Target System"
                  name="target_system"
                  value={filters.target_system}
                  onChange={handleFilterChange}
                  fullWidth
                  margin="normal"
                  variant="outlined"
                  size="small"
                />
              </Grid>
            </Grid>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2, gap: 1 }}>
              <Button onClick={resetFilters} color="inherit">
                Reset
              </Button>
              <Button onClick={applyFilters} variant="contained" color="primary">
                Apply Filters
              </Button>
            </Box>
          </Paper>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <Paper sx={{ width: '100%', overflow: 'hidden' }}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : assessments.length === 0 ? (
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <Typography variant="body1">
                No assessments found. Create a new assessment to get started.
              </Typography>
              <Button 
                variant="contained" 
                color="primary" 
                startIcon={<AddIcon />}
                sx={{ mt: 2 }}
                onClick={() => navigate('/assessments/new')}
              >
                New Assessment
              </Button>
            </Box>
          ) : (
            <>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell 
                        sx={{ cursor: 'pointer' }}
                        onClick={() => handleSort('name')}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          Name
                          {sortField === 'name' && (
                            <SortIcon 
                              fontSize="small" 
                              sx={{ 
                                ml: 0.5, 
                                transform: sortOrder === 'desc' ? 'rotate(180deg)' : 'none'
                              }} 
                            />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell 
                        sx={{ cursor: 'pointer' }}
                        onClick={() => handleSort('status')}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          Status
                          {sortField === 'status' && (
                            <SortIcon 
                              fontSize="small" 
                              sx={{ 
                                ml: 0.5, 
                                transform: sortOrder === 'desc' ? 'rotate(180deg)' : 'none'
                              }} 
                            />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell 
                        sx={{ cursor: 'pointer' }}
                        onClick={() => handleSort('assessment_type')}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          Type
                          {sortField === 'assessment_type' && (
                            <SortIcon 
                              fontSize="small" 
                              sx={{ 
                                ml: 0.5, 
                                transform: sortOrder === 'desc' ? 'rotate(180deg)' : 'none'
                              }} 
                            />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell 
                        sx={{ cursor: 'pointer' }}
                        onClick={() => handleSort('target_system')}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          Target System
                          {sortField === 'target_system' && (
                            <SortIcon 
                              fontSize="small" 
                              sx={{ 
                                ml: 0.5, 
                                transform: sortOrder === 'desc' ? 'rotate(180deg)' : 'none'
                              }} 
                            />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell 
                        sx={{ cursor: 'pointer' }}
                        onClick={() => handleSort('start_date')}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          Start Date
                          {sortField === 'start_date' && (
                            <SortIcon 
                              fontSize="small" 
                              sx={{ 
                                ml: 0.5, 
                                transform: sortOrder === 'desc' ? 'rotate(180deg)' : 'none'
                              }} 
                            />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell 
                        sx={{ cursor: 'pointer' }}
                        onClick={() => handleSort('end_date')}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          End Date
                          {sortField === 'end_date' && (
                            <SortIcon 
                              fontSize="small" 
                              sx={{ 
                                ml: 0.5, 
                                transform: sortOrder === 'desc' ? 'rotate(180deg)' : 'none'
                              }} 
                            />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {assessments.map((assessment) => (
                      <TableRow key={assessment.id} hover>
                        <TableCell>
                          <Link 
                            to={`/assessments/${assessment.id}`}
                            style={{ textDecoration: 'none', color: 'inherit', fontWeight: 'bold' }}
                          >
                            {assessment.name}
                          </Link>
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={assessment.status.replace('_', ' ').toUpperCase()} 
                            color={statusColors[assessment.status] || 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {assessment.assessment_type.replace('_', ' ').charAt(0).toUpperCase() + 
                            assessment.assessment_type.replace('_', ' ').slice(1)}
                        </TableCell>
                        <TableCell>{assessment.target_system}</TableCell>
                        <TableCell>{formatDate(assessment.start_date)}</TableCell>
                        <TableCell>{formatDate(assessment.end_date)}</TableCell>
                        <TableCell align="right">
                          <IconButton 
                            size="small" 
                            onClick={() => navigate(`/assessments/${assessment.id}`)}
                            title="View Assessment"
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                          <IconButton 
                            size="small" 
                            onClick={() => navigate(`/assessments/${assessment.id}/edit`)}
                            title="Edit Assessment"
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                          <IconButton 
                            size="small" 
                            onClick={() => navigate(`/assessments/${assessment.id}/report`)}
                            title="View Report"
                          >
                            <AssignmentIcon fontSize="small" />
                          </IconButton>
                          <IconButton 
                            size="small" 
                            onClick={() => handleDeleteClick(assessment)}
                            title="Delete Assessment"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              <TablePagination
                rowsPerPageOptions={[5, 10, 25, 50]}
                component="div"
                count={totalCount}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
              />
            </>
          )}
        </Paper>
      </Box>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={() => setOpenDeleteDialog(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the assessment <strong>{assessmentToDelete?.name}</strong>? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)} color="inherit">
            Cancel
          </Button>
          <Button onClick={handleConfirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default Assessments; 