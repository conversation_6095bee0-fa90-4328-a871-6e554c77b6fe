import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import assessmentService from '../../services/assessmentService';
import { 
  Container, 
  Typography, 
  Box, 
  Button, 
  Paper, 
  Grid, 
  Chip,
  Divider, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  Tab,
  Tabs,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import { 
  Edit as EditIcon, 
  Delete as DeleteIcon, 
  ArrowBack as ArrowBackIcon,
  Assessment as AssessmentIcon,
  CalendarToday as CalendarIcon,
  Computer as ComputerIcon,
  Description as DescriptionIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  HourglassEmpty as HourglassEmptyIcon,
  Info as InfoIcon,
  Assignment as AssignmentIcon,
  Download as DownloadIcon,
  Timeline as TimelineIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { Chart } from 'react-google-charts';

const statusColors = {
  'pending': 'info',
  'in_progress': 'warning',
  'completed': 'success',
  'reviewed': 'secondary',
  'cancelled': 'error'
};

const resultColors = {
  'pass': 'success',
  'fail': 'error',
  'partial': 'warning',
  'pending': 'info',
  'blocked': 'default',
  'not_applicable': 'default'
};

const TabPanel = (props) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`assessment-tabpanel-${index}`}
      aria-labelledby={`assessment-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const AssessmentDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [assessment, setAssessment] = useState(null);
  const [results, setResults] = useState([]);
  const [report, setReport] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);

  useEffect(() => {
    fetchAssessment();
  }, [id]);

  const fetchAssessment = async () => {
    setLoading(true);
    setError(null);
    try {
      const assessmentData = await assessmentService.getAssessmentById(id);
      setAssessment(assessmentData);
      
      // Fetch test results
      const resultsData = await assessmentService.getAssessmentResults(id);
      setResults(resultsData);
      
      setLoading(false);
    } catch (err) {
      console.error('Error fetching assessment:', err);
      setError('Failed to load assessment. Please try again later.');
      setLoading(false);
    }
  };

  const fetchReport = async () => {
    try {
      const reportData = await assessmentService.getAssessmentReport(id);
      setReport(reportData);
      setTabValue(3); // Switch to the report tab
    } catch (err) {
      console.error('Error fetching report:', err);
      setError('Failed to load assessment report. Please try again later.');
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    
    // Fetch report data if switching to report tab and not already loaded
    if (newValue === 3 && !report) {
      fetchReport();
    }
  };

  const handleDelete = () => {
    setOpenDeleteDialog(true);
  };

  const handleConfirmDelete = async () => {
    try {
      await assessmentService.deleteAssessment(id);
      setOpenDeleteDialog(false);
      navigate('/assessments');
    } catch (err) {
      console.error('Error deleting assessment:', err);
      setError('Failed to delete assessment. Please try again later.');
      setOpenDeleteDialog(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'MMMM d, yyyy');
    } catch (e) {
      return 'Invalid date';
    }
  };

  if (loading) {
    return (
      <Container maxWidth="xl">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="xl">
        <Box sx={{ my: 4 }}>
          <Button 
            startIcon={<ArrowBackIcon />} 
            onClick={() => navigate('/assessments')}
            sx={{ mb: 2 }}
          >
            Back to Assessments
          </Button>
          <Alert severity="error">{error}</Alert>
        </Box>
      </Container>
    );
  }

  if (!assessment) {
    return (
      <Container maxWidth="xl">
        <Box sx={{ my: 4 }}>
          <Button 
            startIcon={<ArrowBackIcon />} 
            onClick={() => navigate('/assessments')}
            sx={{ mb: 2 }}
          >
            Back to Assessments
          </Button>
          <Alert severity="warning">Assessment not found.</Alert>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box sx={{ my: 4 }}>
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Button 
              startIcon={<ArrowBackIcon />} 
              onClick={() => navigate('/assessments')}
              sx={{ mb: 1 }}
            >
              Back to Assessments
            </Button>
            <Typography variant="h4" component="h1">
              {assessment.name}
            </Typography>
            <Box sx={{ mt: 1, display: 'flex', alignItems: 'center' }}>
              <Chip 
                label={assessment.status.replace('_', ' ').toUpperCase()} 
                color={statusColors[assessment.status] || 'default'}
                sx={{ mr: 1 }}
              />
              <Typography variant="body2" color="text.secondary">
                Created on {formatDate(assessment.created_time)}
              </Typography>
            </Box>
          </Box>

          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button 
              variant="outlined" 
              startIcon={<AssignmentIcon />}
              onClick={fetchReport}
            >
              Generate Report
            </Button>
            <Button 
              variant="contained" 
              color="primary"
              startIcon={<DescriptionIcon />}
              onClick={() => navigate(`/assessments/${id}/report`)}
            >
              View Full Report
            </Button>
            <Button 
              variant="outlined" 
              startIcon={<EditIcon />}
              onClick={() => navigate(`/assessments/${id}/edit`)}
            >
              Edit
            </Button>
            <Button 
              variant="outlined" 
              color="error" 
              startIcon={<DeleteIcon />}
              onClick={handleDelete}
            >
              Delete
            </Button>
          </Box>
        </Box>

        <Paper sx={{ mb: 3 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="assessment tabs">
              <Tab label="Overview" id="assessment-tab-0" aria-controls="assessment-tabpanel-0" />
              <Tab label="Test Executions" id="assessment-tab-1" aria-controls="assessment-tabpanel-1" />
              <Tab label="Campaigns" id="assessment-tab-2" aria-controls="assessment-tabpanel-2" />
              <Tab label="Report" id="assessment-tab-3" aria-controls="assessment-tabpanel-3" />
            </Tabs>
          </Box>

          {/* Overview Tab */}
          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Assessment Details
                    </Typography>
                    <Box sx={{ mt: 2 }}>
                      <Grid container spacing={2}>
                        <Grid item xs={12}>
                          <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                            <DescriptionIcon color="primary" sx={{ mr: 1, mt: 0.5 }} />
                            <Box>
                              <Typography variant="subtitle2" color="text.secondary">
                                Description
                              </Typography>
                              <Typography variant="body1">
                                {assessment.description || 'No description provided.'}
                              </Typography>
                            </Box>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <ComputerIcon color="primary" sx={{ mr: 1 }} />
                            <Box>
                              <Typography variant="subtitle2" color="text.secondary">
                                Target System
                              </Typography>
                              <Typography variant="body1">
                                {assessment.target_system}
                              </Typography>
                            </Box>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <AssessmentIcon color="primary" sx={{ mr: 1 }} />
                            <Box>
                              <Typography variant="subtitle2" color="text.secondary">
                                Assessment Type
                              </Typography>
                              <Typography variant="body1">
                                {assessment.assessment_type.replace('_', ' ').charAt(0).toUpperCase() + 
                                  assessment.assessment_type.replace('_', ' ').slice(1)}
                              </Typography>
                            </Box>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <CalendarIcon color="primary" sx={{ mr: 1 }} />
                            <Box>
                              <Typography variant="subtitle2" color="text.secondary">
                                Start Date
                              </Typography>
                              <Typography variant="body1">
                                {formatDate(assessment.start_date)}
                              </Typography>
                            </Box>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <CalendarIcon color="primary" sx={{ mr: 1 }} />
                            <Box>
                              <Typography variant="subtitle2" color="text.secondary">
                                End Date
                              </Typography>
                              <Typography variant="body1">
                                {formatDate(assessment.end_date)}
                              </Typography>
                            </Box>
                          </Box>
                        </Grid>
                      </Grid>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card variant="outlined" sx={{ height: '100%' }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Test Results Summary
                    </Typography>
                    {results.length === 0 ? (
                      <Box sx={{ textAlign: 'center', py: 3 }}>
                        <InfoIcon color="info" sx={{ fontSize: 40, mb: 1 }} />
                        <Typography variant="body1">
                          No test executions found for this assessment.
                        </Typography>
                      </Box>
                    ) : (
                      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
                        <Chart
                          width={'100%'}
                          height={'200px'}
                          chartType="PieChart"
                          loader={<CircularProgress />}
                          data={[
                            ['Result', 'Count'],
                            ['Pass', results.filter(r => r.result === 'pass').length],
                            ['Fail', results.filter(r => r.result === 'fail').length],
                            ['Partial', results.filter(r => r.result === 'partial').length],
                            ['Pending', results.filter(r => r.result === 'pending').length],
                            ['Blocked', results.filter(r => r.result === 'blocked').length],
                            ['N/A', results.filter(r => r.result === 'not_applicable').length],
                          ]}
                          options={{
                            pieHole: 0.4,
                            legend: { position: 'right' },
                            colors: ['#4caf50', '#f44336', '#ff9800', '#2196f3', '#9e9e9e', '#e0e0e0'],
                            chartArea: { width: '80%', height: '80%' }
                          }}
                        />
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Test Executions Tab */}
          <TabPanel value={tabValue} index={1}>
            {results.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 3 }}>
                <InfoIcon color="info" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="body1">
                  No test executions found for this assessment.
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  sx={{ mt: 2 }}
                  onClick={() => navigate(`/assessments/${id}/add-test-execution`)}
                >
                  Add Test Execution
                </Button>
              </Box>
            ) : (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Test Case</TableCell>
                      <TableCell>Result</TableCell>
                      <TableCell>Notes</TableCell>
                      <TableCell>Executed By</TableCell>
                      <TableCell>Executed At</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {results.map((execution) => (
                      <TableRow key={execution.id} hover>
                        <TableCell>{execution.test_case_name}</TableCell>
                        <TableCell>
                          <Chip 
                            label={execution.result.replace('_', ' ').toUpperCase()} 
                            color={resultColors[execution.result] || 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {execution.notes ? 
                            execution.notes.length > 50 ? 
                              `${execution.notes.substring(0, 50)}...` : 
                              execution.notes 
                            : 'No notes'
                          }
                        </TableCell>
                        <TableCell>User ID: {execution.executed_by}</TableCell>
                        <TableCell>{formatDate(execution.executed_at)}</TableCell>
                        <TableCell align="right">
                          <Tooltip title="View Details">
                            <IconButton 
                              size="small" 
                              onClick={() => navigate(`/assessments/${id}/executions/${execution.id}`)}
                            >
                              <InfoIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Edit">
                            <IconButton 
                              size="small" 
                              onClick={() => navigate(`/assessments/${id}/executions/${execution.id}/edit`)}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </TabPanel>

          {/* Campaigns Tab */}
          <TabPanel value={tabValue} index={2}>
            {!assessment.campaigns || assessment.campaigns.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 3 }}>
                <InfoIcon color="info" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="body1">
                  No campaigns associated with this assessment.
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  sx={{ mt: 2 }}
                  onClick={() => navigate(`/assessments/${id}/add-campaign`)}
                >
                  Add Campaign
                </Button>
              </Box>
            ) : (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Name</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Start Date</TableCell>
                      <TableCell>End Date</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {assessment.campaigns.map((campaign) => (
                      <TableRow key={campaign.id} hover>
                        <TableCell>{campaign.name}</TableCell>
                        <TableCell>
                          <Chip 
                            label={campaign.status.replace('_', ' ').toUpperCase()} 
                            color={statusColors[campaign.status] || 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{formatDate(campaign.start_date)}</TableCell>
                        <TableCell>{formatDate(campaign.end_date)}</TableCell>
                        <TableCell align="right">
                          <Tooltip title="View Campaign">
                            <IconButton 
                              size="small" 
                              onClick={() => navigate(`/campaigns/${campaign.id}`)}
                            >
                              <InfoIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Remove from Assessment">
                            <IconButton 
                              size="small" 
                              onClick={() => {/* TODO: implement remove campaign */}}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </TabPanel>

          {/* Report Tab */}
          <TabPanel value={tabValue} index={3}>
            {!report ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                  <Button 
                    variant="outlined" 
                    startIcon={<DescriptionIcon />}
                    onClick={() => navigate(`/assessments/${id}/report`)}
                  >
                    View Full Report
                  </Button>
                  <Button 
                    variant="contained" 
                    color="primary" 
                    startIcon={<DownloadIcon />}
                    onClick={() => {/* TODO: implement export report */}}
                  >
                    Export Report
                  </Button>
                </Box>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={8}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          Summary
                        </Typography>
                        <Grid container spacing={2}>
                          <Grid item xs={6} sm={4} md={2}>
                            <Box sx={{ textAlign: 'center', py: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                Total Tests
                              </Typography>
                              <Typography variant="h5">
                                {report.summary.total_tests}
                              </Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={6} sm={4} md={2}>
                            <Box sx={{ textAlign: 'center', py: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                Passed
                              </Typography>
                              <Typography variant="h5" color="success.main">
                                {report.summary.passed}
                              </Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={6} sm={4} md={2}>
                            <Box sx={{ textAlign: 'center', py: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                Failed
                              </Typography>
                              <Typography variant="h5" color="error.main">
                                {report.summary.failed}
                              </Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={6} sm={4} md={2}>
                            <Box sx={{ textAlign: 'center', py: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                Partial
                              </Typography>
                              <Typography variant="h5" color="warning.main">
                                {report.summary.partial}
                              </Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={6} sm={4} md={2}>
                            <Box sx={{ textAlign: 'center', py: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                Pending
                              </Typography>
                              <Typography variant="h5" color="info.main">
                                {report.summary.pending}
                              </Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={6} sm={4} md={2}>
                            <Box sx={{ textAlign: 'center', py: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                Blocked
                              </Typography>
                              <Typography variant="h5" color="text.secondary">
                                {report.summary.blocked}
                              </Typography>
                            </Box>
                          </Grid>
                        </Grid>

                        <Divider sx={{ my: 2 }} />

                        <Grid container spacing={2}>
                          <Grid item xs={12} sm={6}>
                            <Box sx={{ textAlign: 'center', py: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                Completion Rate
                              </Typography>
                              <Typography variant="h5">
                                {report.summary.completion_percentage.toFixed(1)}%
                              </Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <Box sx={{ textAlign: 'center', py: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                Pass Rate
                              </Typography>
                              <Typography variant="h5">
                                {report.summary.pass_rate.toFixed(1)}%
                              </Typography>
                            </Box>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>

                    {Object.keys(report.mitre_coverage).length > 0 && (
                      <Card variant="outlined" sx={{ mt: 3 }}>
                        <CardContent>
                          <Typography variant="h6" gutterBottom>
                            MITRE ATT&CK Coverage
                          </Typography>
                          <TableContainer>
                            <Table size="small">
                              <TableHead>
                                <TableRow>
                                  <TableCell>Tactic</TableCell>
                                  <TableCell align="right">Covered</TableCell>
                                  <TableCell align="right">Total</TableCell>
                                  <TableCell align="right">Coverage</TableCell>
                                </TableRow>
                              </TableHead>
                              <TableBody>
                                {Object.entries(report.mitre_coverage).map(([tactic, data]) => (
                                  <TableRow key={tactic} hover>
                                    <TableCell>{tactic}</TableCell>
                                    <TableCell align="right">{data.covered}</TableCell>
                                    <TableCell align="right">{data.total}</TableCell>
                                    <TableCell align="right">
                                      {((data.covered / data.total) * 100).toFixed(1)}%
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </TableContainer>
                        </CardContent>
                      </Card>
                    )}
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          Recommendations
                        </Typography>
                        {report.recommendations && report.recommendations.length > 0 ? (
                          <List>
                            {report.recommendations.map((recommendation, index) => (
                              <ListItem key={index}>
                                <ListItemIcon>
                                  <InfoIcon color="primary" />
                                </ListItemIcon>
                                <ListItemText primary={recommendation} />
                              </ListItem>
                            ))}
                          </List>
                        ) : (
                          <Typography variant="body1" color="text.secondary">
                            No recommendations available.
                          </Typography>
                        )}
                      </CardContent>
                    </Card>

                    {report.campaigns && report.campaigns.length > 0 && (
                      <Card variant="outlined" sx={{ mt: 3 }}>
                        <CardContent>
                          <Typography variant="h6" gutterBottom>
                            Associated Campaigns
                          </Typography>
                          <List>
                            {report.campaigns.map((campaign) => (
                              <ListItem 
                                key={campaign.id}
                                button
                                onClick={() => navigate(`/campaigns/${campaign.id}`)}
                              >
                                <ListItemIcon>
                                  <TimelineIcon color="primary" />
                                </ListItemIcon>
                                <ListItemText 
                                  primary={campaign.name} 
                                  secondary={`Status: ${campaign.status}`} 
                                />
                              </ListItem>
                            ))}
                          </List>
                        </CardContent>
                      </Card>
                    )}
                  </Grid>
                </Grid>
              </>
            )}
          </TabPanel>
        </Paper>
      </Box>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={() => setOpenDeleteDialog(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the assessment <strong>{assessment.name}</strong>? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)} color="inherit">
            Cancel
          </Button>
          <Button onClick={handleConfirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default AssessmentDetail; 