import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '@mui/material/styles';
import {
  Box,
  TextField,
  Button,
  Typography,
  Paper,
  FormControlLabel,
  Checkbox,
  IconButton,
  InputAdornment,
  Divider,
  Alert,
  CircularProgress
} from '@mui/material';
import LockOutlinedIcon from '@mui/icons-material/LockOutlined';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';

// Matrix rain animation component
const MatrixRain = () => {
  const theme = useTheme();
  
  // Only render in dark mode
  if (theme.palette.mode !== 'dark') return null;
  
  return (
    <Box
      sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        overflow: 'hidden',
        pointerEvents: 'none',
        zIndex: 0,
        opacity: 0.2,
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'linear-gradient(to bottom, rgba(0,0,0,0) 50%, rgba(0,0,0,1) 100%)',
          zIndex: 2,
        }
      }}
    >
      <Box
        component="canvas"
        id="matrix-canvas"
        sx={{ 
          display: 'block',
          width: '100%',
          height: '100%'
        }}
        ref={(canvas) => {
          if (!canvas) return;
          
          const ctx = canvas.getContext('2d');
          canvas.width = canvas.offsetWidth;
          canvas.height = canvas.offsetHeight;
          
          // Matrix characters
          const characters = '01';
          const fontSize = 14;
          const columns = Math.floor(canvas.width / fontSize);
          const drops = Array(columns).fill(1);
          
          // Draw matrix rain
          const draw = () => {
            ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            ctx.fillStyle = '#00FF41';
            ctx.font = `${fontSize}px monospace`;
            
            for (let i = 0; i < drops.length; i++) {
              const text = characters.charAt(Math.floor(Math.random() * characters.length));
              ctx.fillText(text, i * fontSize, drops[i] * fontSize);
              
              if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                drops[i] = 0;
              }
              
              drops[i]++;
            }
          };
          
          // Animate
          const intervalId = setInterval(draw, 33);
          
          // Cleanup
          return () => clearInterval(intervalId);
        }}
      />
    </Box>
  );
};

// Login form component
const Login = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { login, error } = useAuth();
  const theme = useTheme();
  
  // Form state
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formError, setFormError] = useState('');
  
  // Handle login submit
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form
    if (!username.trim() || !password.trim()) {
      setFormError(t('auth.errors.missingFields'));
      return;
    }
    
    setFormError('');
    setIsLoading(true);
    
    try {
      // In real app, this would send credentials to the server
      // For demo, we'll simulate a successful login after a delay
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock successful login
        await login({ username, password });
        navigate('/dashboard');
      } else {
        // Real login
        await login({ username, password });
        navigate('/dashboard');
      }
    } catch (err) {
      setFormError(err.message || t('auth.errors.invalidCredentials'));
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <Box sx={{ position: 'relative' }}>
      <MatrixRain />
      
      <Paper
        elevation={theme.palette.mode === 'dark' ? 0 : 3}
        sx={{
          padding: 4,
          background: theme.palette.mode === 'dark' 
            ? 'rgba(0, 0, 0, 0.7)'
            : 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(10px)',
          border: theme.palette.mode === 'dark' 
            ? '1px solid rgba(0, 255, 65, 0.3)' 
            : undefined,
          boxShadow: theme.palette.mode === 'dark'
            ? '0 0 20px rgba(0, 255, 65, 0.2)'
            : undefined,
          position: 'relative',
          zIndex: 1,
          maxWidth: 450,
          width: '100%',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <Box 
            sx={{ 
              bgcolor: 'primary.main', 
              p: 2, 
              borderRadius: '50%',
              mb: 2,
              color: 'primary.contrastText',
              boxShadow: theme.palette.mode === 'dark'
                ? '0 0 15px rgba(0, 255, 65, 0.5)'
                : undefined,
            }}
          >
            <LockOutlinedIcon fontSize="large" />
          </Box>
          
          <Typography 
            component="h1" 
            variant="h4" 
            gutterBottom
            sx={{
              fontFamily: theme.palette.mode === 'dark' 
                ? '"Share Tech Mono", monospace' 
                : undefined,
              fontWeight: 'bold',
              letterSpacing: theme.palette.mode === 'dark' ? 1 : undefined,
              color: 'primary.main',
            }}
          >
            {t('auth.login')}
          </Typography>
          
          {(formError || error) && (
            <Alert 
              severity="error" 
              sx={{ width: '100%', mb: 2 }}
              onClose={() => setFormError('')}
            >
              {formError || error}
            </Alert>
          )}
          
          <Box component="form" onSubmit={handleSubmit} sx={{ width: '100%' }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="username"
              label={t('auth.username')}
              name="username"
              autoComplete="username"
              autoFocus
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              InputProps={{
                sx: {
                  fontFamily: theme.palette.mode === 'dark' 
                    ? '"Share Tech Mono", monospace' 
                    : undefined,
                },
              }}
            />
            
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label={t('auth.password')}
              type={showPassword ? 'text' : 'password'}
              id="password"
              autoComplete="current-password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </IconButton>
                  </InputAdornment>
                ),
                sx: {
                  fontFamily: theme.palette.mode === 'dark' 
                    ? '"Share Tech Mono", monospace' 
                    : undefined,
                },
              }}
            />
            
            <FormControlLabel
              control={
                <Checkbox 
                  value="remember" 
                  color="primary" 
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                />
              }
              label={t('auth.rememberMe')}
              sx={{ mt: 1 }}
            />
            
            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={isLoading}
              sx={{ 
                mt: 3, 
                mb: 2, 
                py: 1.5,
                position: 'relative',
                overflow: 'hidden',
                '&::after': theme.palette.mode === 'dark' ? {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent)',
                  animation: 'ripple 1.5s infinite',
                  '@keyframes ripple': {
                    '0%': { transform: 'translateX(-100%)' },
                    '100%': { transform: 'translateX(100%)' },
                  },
                } : {},
              }}
            >
              {isLoading ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                t('auth.signIn')
              )}
            </Button>
            
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
              <Typography 
                variant="body2" 
                color="text.secondary"
                component="a" 
                href="#"
                sx={{ textDecoration: 'none', '&:hover': { textDecoration: 'underline' } }}
              >
                {t('auth.forgotPassword')}
              </Typography>
            </Box>
            
            <Divider sx={{ my: 3 }}>
              <Typography variant="body2" color="text.secondary">
                {t('auth.or')}
              </Typography>
            </Divider>
            
            <Box sx={{ display: 'flex', justifyContent: 'center' }}>
              <Button
                variant="outlined"
                sx={{ mr: 1 }}
                disabled={isLoading}
              >
                {t('auth.ssoSignIn')}
              </Button>
              <Button
                variant="outlined"
                disabled={isLoading}
              >
                {t('auth.register')}
              </Button>
            </Box>
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

export default Login; 