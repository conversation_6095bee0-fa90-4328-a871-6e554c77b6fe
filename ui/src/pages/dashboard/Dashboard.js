import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@mui/material/styles';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Divider,
  List,
  ListItem,
  ListItemText,
  LinearProgress,
  Button,
  Chip,
  Tooltip
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import SecurityIcon from '@mui/icons-material/Security';
import BugReportIcon from '@mui/icons-material/BugReport';
import AssessmentIcon from '@mui/icons-material/Assessment';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  ChartTooltip,
  Legend,
  Filler
);

// Stat Card Component
const StatCard = ({ title, value, icon, color, trend, trendValue, onClick }) => {
  const theme = useTheme();
  
  return (
    <Card 
      sx={{ 
        height: '100%',
        position: 'relative',
        overflow: 'hidden',
        '&:hover': {
          boxShadow: theme.shadows[8],
          transform: 'translateY(-2px)',
          transition: 'all 0.3s ease',
        },
        cursor: onClick ? 'pointer' : 'default',
        borderLeft: `4px solid ${color}`,
      }}
      onClick={onClick}
    >
      <CardContent sx={{ pt: 2.5, px: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="subtitle2" color="text.secondary">
              {title}
            </Typography>
            <Typography variant="h4" sx={{ mt: 1, fontWeight: 'bold' }}>
              {value}
            </Typography>
            
            {trend && (
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                {trend === 'up' ? (
                  <TrendingUpIcon fontSize="small" sx={{ color: 'success.main' }} />
                ) : (
                  <TrendingDownIcon fontSize="small" sx={{ color: 'error.main' }} />
                )}
                <Typography 
                  variant="body2" 
                  sx={{ 
                    ml: 0.5,
                    color: trend === 'up' ? 'success.main' : 'error.main'
                  }}
                >
                  {trendValue}
                </Typography>
              </Box>
            )}
          </Box>
          
          <Box 
            sx={{ 
              p: 1.5, 
              borderRadius: '50%',
              backgroundColor: `${color}20`,
              color: color
            }}
          >
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

// Chart Card Component
const ChartCard = ({ title, chart, actions, height = 300 }) => {
  const theme = useTheme();
  
  return (
    <Card 
      sx={{ 
        height: '100%',
        '&:hover': {
          boxShadow: theme.shadows[4],
        },
      }}
    >
      <CardHeader
        title={
          <Typography variant="h6" sx={{ fontWeight: 'medium' }}>
            {title}
          </Typography>
        }
        action={
          <IconButton aria-label="settings">
            <MoreVertIcon />
          </IconButton>
        }
        sx={{ pb: 0 }}
      />
      <CardContent sx={{ height: height }}>
        {chart}
      </CardContent>
      {actions && (
        <>
          <Divider />
          <Box sx={{ p: 1.5, display: 'flex', justifyContent: 'flex-end' }}>
            {actions}
          </Box>
        </>
      )}
    </Card>
  );
};

// Recent Activity Component
const RecentActivity = () => {
  const { t } = useTranslation();
  
  // Mock data
  const activities = [
    {
      id: 1,
      type: 'campaign',
      action: 'created',
      name: 'Windows Lateral Movement',
      user: 'john.doe',
      time: '2h ago',
      status: 'success'
    },
    {
      id: 2,
      type: 'testcase',
      action: 'modified',
      name: 'T1059.001 PowerShell',
      user: 'alice.smith',
      time: '4h ago',
      status: 'info'
    },
    {
      id: 3,
      type: 'assessment',
      action: 'executed',
      name: 'Full Coverage Assessment',
      user: 'admin',
      time: '1d ago',
      status: 'warning'
    },
    {
      id: 4,
      type: 'testcase',
      action: 'failed',
      name: 'T1027 Obfuscation',
      user: 'bob.johnson',
      time: '1d ago',
      status: 'error'
    }
  ];
  
  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'success.main';
      case 'info': return 'info.main';
      case 'warning': return 'warning.main';
      case 'error': return 'error.main';
      default: return 'text.primary';
    }
  };
  
  const getActivityIcon = (type) => {
    switch (type) {
      case 'campaign': return <PlayArrowIcon fontSize="small" />;
      case 'testcase': return <BugReportIcon fontSize="small" />;
      case 'assessment': return <AssessmentIcon fontSize="small" />;
      default: return null;
    }
  };
  
  return (
    <Card sx={{ height: '100%' }}>
      <CardHeader
        title={
          <Typography variant="h6" sx={{ fontWeight: 'medium' }}>
            {t('dashboard.recentActivity')}
          </Typography>
        }
        action={
          <IconButton aria-label="refresh">
            <RefreshIcon />
          </IconButton>
        }
      />
      <Divider />
      <List sx={{ p: 0 }}>
        {activities.map((activity) => (
          <React.Fragment key={activity.id}>
            <ListItem 
              alignItems="flex-start"
              sx={{ 
                py: 1.5,
                px: 2,
                '&:hover': {
                  backgroundColor: 'action.hover',
                },
              }}
            >
              <Box 
                sx={{ 
                  mr: 2, 
                  display: 'flex', 
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 36,
                  height: 36,
                  borderRadius: '50%',
                  backgroundColor: `${getStatusColor(activity.status)}20`,
                  color: getStatusColor(activity.status)
                }}
              >
                {getActivityIcon(activity.type)}
              </Box>
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                    <Typography variant="subtitle2">
                      {activity.name}
                    </Typography>
                    <Chip 
                      size="small" 
                      label={t(`dashboard.actions.${activity.action}`)}
                      sx={{ 
                        ml: 1, 
                        height: 20,
                        fontSize: '0.7rem',
                        backgroundColor: `${getStatusColor(activity.status)}20`,
                        color: getStatusColor(activity.status)
                      }}
                    />
                  </Box>
                }
                secondary={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography 
                      variant="body2" 
                      color="text.secondary"
                      sx={{ fontSize: '0.75rem' }}
                    >
                      {activity.user}
                    </Typography>
                    <Box 
                      component="span" 
                      sx={{ 
                        display: 'inline-block',
                        mx: 0.5,
                        width: 4,
                        height: 4,
                        borderRadius: '50%',
                        backgroundColor: 'text.disabled'
                      }} 
                    />
                    <Typography 
                      variant="body2" 
                      color="text.secondary"
                      sx={{ fontSize: '0.75rem' }}
                    >
                      {activity.time}
                    </Typography>
                  </Box>
                }
              />
            </ListItem>
            <Divider variant="inset" component="li" sx={{ ml: 7 }} />
          </React.Fragment>
        ))}
      </List>
      <Box sx={{ p: 2, display: 'flex', justifyContent: 'center' }}>
        <Button variant="text" sx={{ textTransform: 'none' }}>
          {t('dashboard.viewAll')}
        </Button>
      </Box>
    </Card>
  );
};

// Dashboard Page Component
const Dashboard = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  
  // Mock data loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);
  
  // Chart options and data
  const lineChartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: t('dashboard.charts.passedTests'),
        data: [65, 59, 80, 81, 56, 75],
        fill: true,
        backgroundColor: `${theme.palette.success.main}20`,
        borderColor: theme.palette.success.main,
        tension: 0.4,
      },
      {
        label: t('dashboard.charts.failedTests'),
        data: [28, 48, 40, 19, 33, 27],
        fill: true,
        backgroundColor: `${theme.palette.error.main}20`,
        borderColor: theme.palette.error.main,
        tension: 0.4,
      },
    ],
  };
  
  const lineChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          display: true,
          color: theme.palette.divider,
        },
      },
      x: {
        grid: {
          display: false,
        },
      },
    },
  };
  
  const barChartData = {
    labels: ['T1059', 'T1021', 'T1027', 'T1083', 'T1018'],
    datasets: [
      {
        label: t('dashboard.charts.coverage'),
        data: [75, 50, 90, 40, 65],
        backgroundColor: theme.palette.mode === 'dark' 
          ? theme.palette.primary.main 
          : theme.palette.primary.light,
        barThickness: 20,
        borderRadius: 4,
      },
    ],
  };
  
  const barChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        ticks: {
          callback: function(value) {
            return value + '%';
          },
        },
        grid: {
          display: true,
          color: theme.palette.divider,
        },
      },
      x: {
        grid: {
          display: false,
        },
      },
    },
  };
  
  const doughnutChartData = {
    labels: [
      t('dashboard.charts.detected'),
      t('dashboard.charts.partial'),
      t('dashboard.charts.missed')
    ],
    datasets: [
      {
        data: [65, 20, 15],
        backgroundColor: [
          theme.palette.success.main,
          theme.palette.warning.main,
          theme.palette.error.main,
        ],
        borderWidth: 0,
        hoverOffset: 5,
      },
    ],
  };
  
  const doughnutChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
      },
    },
    cutout: '70%',
    animation: {
      animateScale: true,
    },
  };
  
  return (
    <Box>
      {/* Page Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
          {t('dashboard.title')}
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          {t('dashboard.subtitle')}
        </Typography>
      </Box>
      
      {/* Loading State */}
      {loading && (
        <LinearProgress 
          sx={{ 
            mb: 4,
            height: 6, 
            borderRadius: 3,
            backgroundColor: theme.palette.mode === 'dark' 
              ? 'rgba(0, 255, 65, 0.2)' 
              : undefined 
          }} 
        />
      )}
      
      {/* Stats Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title={t('dashboard.stats.testsTotal')}
            value="247"
            icon={<BugReportIcon />}
            color={theme.palette.primary.main}
            trend="up"
            trendValue="+12% vs last month"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title={t('dashboard.stats.detectionRate')}
            value="78%"
            icon={<SecurityIcon />}
            color={theme.palette.success.main}
            trend="up"
            trendValue="+5% vs last month"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title={t('dashboard.stats.activeCampaigns')}
            value="12"
            icon={<PlayArrowIcon />}
            color={theme.palette.info.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title={t('dashboard.stats.mitreCoverage')}
            value="53%"
            icon={<AssessmentIcon />}
            color={theme.palette.warning.main}
            trend="up"
            trendValue="+8% vs last month"
          />
        </Grid>
      </Grid>
      
      {/* Charts */}
      <Grid container spacing={3}>
        <Grid item xs={12} lg={8}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <ChartCard
                title={t('dashboard.charts.testResultsOverTime')}
                chart={
                  <Line data={lineChartData} options={lineChartOptions} />
                }
                actions={
                  <Button variant="text" size="small">
                    {t('dashboard.viewDetails')}
                  </Button>
                }
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <ChartCard
                title={t('dashboard.charts.topTechniquesDetection')}
                chart={
                  <Bar data={barChartData} options={barChartOptions} />
                }
                actions={
                  <Button variant="text" size="small">
                    {t('dashboard.viewAllTechniques')}
                  </Button>
                }
                height={250}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <ChartCard
                title={t('dashboard.charts.detectionBreakdown')}
                chart={
                  <Box sx={{ position: 'relative', height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <Doughnut data={doughnutChartData} options={doughnutChartOptions} />
                    <Box sx={{ position: 'absolute', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                      <Typography variant="h4" sx={{ fontWeight: 'bold' }}>65%</Typography>
                      <Typography variant="caption" color="text.secondary">{t('dashboard.charts.detected')}</Typography>
                    </Box>
                  </Box>
                }
                height={250}
              />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} lg={4}>
          <RecentActivity />
        </Grid>
        
        {/* Upcoming Tests Section */}
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title={
                <Typography variant="h6" sx={{ fontWeight: 'medium' }}>
                  {t('dashboard.upcomingTests')}
                </Typography>
              }
              action={
                <Box>
                  <Button 
                    variant="contained" 
                    size="small"
                    startIcon={<PlayArrowIcon />}
                    sx={{ mr: 1 }}
                  >
                    {t('dashboard.runTests')}
                  </Button>
                  <IconButton>
                    <MoreVertIcon />
                  </IconButton>
                </Box>
              }
            />
            <Divider />
            <Box sx={{ p: 2 }}>
              <Grid container spacing={2}>
                {['T1055', 'T1059.001', 'T1027', 'T1018'].map((technique) => (
                  <Grid item xs={12} sm={6} md={3} key={technique}>
                    <Paper
                      sx={{
                        p: 2,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        border: `1px solid ${theme.palette.divider}`,
                        '&:hover': {
                          boxShadow: theme.shadows[2],
                          borderColor: theme.palette.primary.main,
                        },
                      }}
                    >
                      <Box>
                        <Typography variant="subtitle2">
                          {technique}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {technique === 'T1055' && 'Process Injection'}
                          {technique === 'T1059.001' && 'PowerShell'}
                          {technique === 'T1027' && 'Obfuscated Files'}
                          {technique === 'T1018' && 'Remote System Discovery'}
                        </Typography>
                      </Box>
                      <Tooltip title={t('dashboard.runTest')}>
                        <IconButton 
                          size="small"
                          sx={{ 
                            backgroundColor: `${theme.palette.primary.main}20`,
                            color: theme.palette.primary.main,
                            '&:hover': {
                              backgroundColor: `${theme.palette.primary.main}40`,
                            }
                          }}
                        >
                          <PlayArrowIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard; 