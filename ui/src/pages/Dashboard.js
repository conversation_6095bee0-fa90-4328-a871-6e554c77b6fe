import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

// MUI components
import {
  Box,
  Button,
  Card,
  CardContent,
  Container,
  Divider,
  Grid,
  IconButton,
  List,
  ListItem,
  ListItemText,
  Paper,
  Typography,
  CircularProgress,
} from '@mui/material';

// MUI icons
import AddIcon from '@mui/icons-material/Add';
import ViewListIcon from '@mui/icons-material/ViewList';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import AssignmentIcon from '@mui/icons-material/Assignment';
import CampaignIcon from '@mui/icons-material/Campaign';
import SecurityIcon from '@mui/icons-material/Security';

// Components
import MainLayout from '../components/layout/MainLayout';
import DashboardSummaryCard from '../components/dashboard/SummaryCard';
import StatusChip from '../components/common/StatusChip';

function Dashboard() {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState({
    assessmentCount: 0,
    campaignCount: 0,
    recentAssessments: [],
    recentCampaigns: [],
    activityLog: [],
  });

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // In a real implementation, you would fetch data from your API
        // For now, we'll simulate a delay and use mock data
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock data
        setDashboardData({
          assessmentCount: 28,
          campaignCount: 5,
          recentAssessments: [
            { id: 1, name: 'Q1 Web Application Assessment', status: 'active', updatedAt: new Date().toISOString() },
            { id: 2, name: 'API Security Review', status: 'completed', updatedAt: new Date(Date.now() - 86400000).toISOString() },
            { id: 3, name: 'Infrastructure Penetration Test', status: 'planned', updatedAt: new Date(Date.now() - 172800000).toISOString() },
          ],
          recentCampaigns: [
            { id: 1, name: 'Q1 2024 Security Campaign', status: 'active', updatedAt: new Date().toISOString() },
            { id: 2, name: 'Compliance Verification', status: 'completed', updatedAt: new Date(Date.now() - 259200000).toISOString() },
          ],
          activityLog: [
            { id: 1, action: 'Assessment Updated', target: 'Q1 Web Application Assessment', user: 'John Doe', timestamp: new Date().toISOString() },
            { id: 2, action: 'Campaign Created', target: 'Q2 2024 Audit', user: 'Jane Smith', timestamp: new Date(Date.now() - 43200000).toISOString() },
            { id: 3, action: 'Test Case Completed', target: 'SQLi Testing', user: 'John Doe', timestamp: new Date(Date.now() - 129600000).toISOString() },
            { id: 4, action: 'Report Generated', target: 'API Security Review', user: 'Admin User', timestamp: new Date(Date.now() - 172800000).toISOString() },
          ],
        });
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  if (loading) {
    return (
      <MainLayout>
        <Box 
          display="flex" 
          justifyContent="center" 
          alignItems="center" 
          minHeight="80vh"
        >
          <CircularProgress />
        </Box>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Box mb={4}>
          <Typography variant="h4" gutterBottom>
            Dashboard
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Welcome back! Here's an overview of your security testing activities.
          </Typography>
        </Box>

        {/* Summary Cards */}
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} sm={6} md={3}>
            <DashboardSummaryCard
              title="Assessments"
              value={dashboardData.assessmentCount}
              icon={<AssignmentIcon fontSize="large" />}
              color="#1976d2"
              onClick={() => navigate('/assessments')}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <DashboardSummaryCard
              title="Campaigns"
              value={dashboardData.campaignCount}
              icon={<CampaignIcon fontSize="large" />}
              color="#ff5722"
              onClick={() => navigate('/campaigns')}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <DashboardSummaryCard
              title="Security Score"
              value="83%"
              icon={<SecurityIcon fontSize="large" />}
              color="#4caf50"
              onClick={() => {}}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <DashboardSummaryCard
              title="Trend"
              value="+12%"
              icon={<TrendingUpIcon fontSize="large" />}
              color="#9c27b0"
              onClick={() => {}}
            />
          </Grid>
        </Grid>

        {/* Main Content */}
        <Grid container spacing={3}>
          {/* Recent Assessments */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6">Recent Assessments</Typography>
                  <Box>
                    <Button 
                      variant="outlined" 
                      size="small" 
                      startIcon={<AddIcon />}
                      onClick={() => navigate('/assessments/new')}
                      sx={{ mr: 1 }}
                    >
                      New
                    </Button>
                    <Button
                      variant="text"
                      size="small"
                      startIcon={<ViewListIcon />}
                      onClick={() => navigate('/assessments')}
                    >
                      View All
                    </Button>
                  </Box>
                </Box>
                <Divider sx={{ mb: 2 }} />
                <List>
                  {dashboardData.recentAssessments.map((assessment) => (
                    <ListItem
                      key={assessment.id}
                      secondaryAction={
                        <IconButton edge="end" aria-label="options">
                          <MoreVertIcon />
                        </IconButton>
                      }
                      sx={{ 
                        px: 2, 
                        '&:hover': { 
                          backgroundColor: 'rgba(0, 0, 0, 0.04)',
                          cursor: 'pointer',
                        },
                      }}
                      onClick={() => navigate(`/assessments/${assessment.id}`)}
                    >
                      <ListItemText
                        primary={
                          <Typography variant="subtitle1" component="div">
                            {assessment.name}
                          </Typography>
                        }
                        secondary={
                          <Box display="flex" alignItems="center" mt={0.5}>
                            <StatusChip status={assessment.status} size="small" />
                            <Typography variant="caption" sx={{ ml: 1 }}>
                              Updated {formatDate(assessment.updatedAt)}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Recent Campaigns */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6">Recent Campaigns</Typography>
                  <Box>
                    <Button 
                      variant="outlined" 
                      size="small" 
                      startIcon={<AddIcon />}
                      onClick={() => navigate('/campaigns/new')}
                      sx={{ mr: 1 }}
                    >
                      New
                    </Button>
                    <Button
                      variant="text"
                      size="small"
                      startIcon={<ViewListIcon />}
                      onClick={() => navigate('/campaigns')}
                    >
                      View All
                    </Button>
                  </Box>
                </Box>
                <Divider sx={{ mb: 2 }} />
                <List>
                  {dashboardData.recentCampaigns.map((campaign) => (
                    <ListItem
                      key={campaign.id}
                      secondaryAction={
                        <IconButton edge="end" aria-label="options">
                          <MoreVertIcon />
                        </IconButton>
                      }
                      sx={{ 
                        px: 2, 
                        '&:hover': { 
                          backgroundColor: 'rgba(0, 0, 0, 0.04)',
                          cursor: 'pointer',
                        },
                      }}
                      onClick={() => navigate(`/campaigns/${campaign.id}`)}
                    >
                      <ListItemText
                        primary={
                          <Typography variant="subtitle1" component="div">
                            {campaign.name}
                          </Typography>
                        }
                        secondary={
                          <Box display="flex" alignItems="center" mt={0.5}>
                            <StatusChip status={campaign.status} size="small" />
                            <Typography variant="caption" sx={{ ml: 1 }}>
                              Updated {formatDate(campaign.updatedAt)}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Activity Log */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6">Recent Activity</Typography>
                </Box>
                <Divider sx={{ mb: 2 }} />
                <List>
                  {dashboardData.activityLog.map((activity) => (
                    <ListItem
                      key={activity.id}
                      sx={{ 
                        px: 2, 
                        '&:hover': { 
                          backgroundColor: 'rgba(0, 0, 0, 0.04)',
                        },
                      }}
                    >
                      <ListItemText
                        primary={
                          <Typography variant="body1" component="div">
                            <strong>{activity.action}</strong>: {activity.target}
                          </Typography>
                        }
                        secondary={
                          <Box display="flex" alignItems="center" mt={0.5}>
                            <Typography variant="caption">
                              By {activity.user} on {formatDate(activity.timestamp)}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </MainLayout>
  );
}

export default Dashboard; 