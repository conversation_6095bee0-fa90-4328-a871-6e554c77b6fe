# Regression Rigor UI

## Overview
The Regression Rigor UI provides an interface for creating testcases, running campaigns as subsets of assessments, and tracking detection coverage for a range of MITRE TTPs (offensive testcases).

## Key Users
- Red teamer
- CTI team 
- Detection engineer
- Administrator
- Dashboard users

## Architecture
- Multi-page React application
- Mobile-first responsive design
- Material Design system with dark mode as default
- Server-side user preferences
- Offline functionality with syncing capability
- Full PostgreSQL text search integration

## Core Functionality
1. Authentication (progressive implementation)
   - Simple login initially
   - SSO integration
   - MFA implementation

2. Assessment Management
   - Create, view, update, delete assessments
   - Campaign organization within assessments
   - Testcase library management

3. Dashboards
   - MITRE navigator integration
   - Detection coverage visualization
   - Backlog burndown tracking
   - Performance metrics

4. User Experience
   - Theming: Dark mode (default) and light mode
   - Custom CSS theme options
   - i18n with multiple language support:
     - English UK
     - Afrikaans
     - German
     - Romanian
     - Swiitzer Tüütsch
     - Zulu

5. Navigation Structure
   - CRUD operations for all entity types
   - Dashboard views (approximately 4)
   - Search capability
   - Documentation

## Design Aesthetic
- Console/matrix inspired
- Low-key retro styling
- Fun interactive elements
- Dark mode primary

## Additional Features
- Integrated help and tutorial system
- Debug mode for logging and monitoring
- Error handling with unique error codes
- JIRA ticketing integration
- Guided workflows (wizards/stepper components)
- Comprehensive feedback mechanisms

## ACL Structure
- Role-based access control for all areas
- Read/Write/Execute permissions managed through ACLs

## Testing
- UI testing implemented with Playwright

## Developer Setup
(To be added: setup instructions, npm commands, etc.) 