{"name": "regression-rigor-ui", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.10", "@mui/lab": "^5.0.0-alpha.165", "@mui/material": "^5.15.10", "axios": "^1.6.7", "chart.js": "^4.4.1", "date-fns": "^3.3.1", "i18next": "^23.8.2", "i18next-browser-languagedetector": "^7.2.0", "i18next-http-backend": "^2.4.2", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-i18next": "^14.0.5", "react-router-dom": "^6.22.1", "react-scripts": "5.0.1", "uuid": "^9.0.1", "web-vitals": "^3.5.2", "yup": "^1.3.3"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@playwright/test": "^1.41.2", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^9.0.11", "lint-staged": "^15.2.2", "prettier": "^3.2.5", "sass": "^1.70.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "test:e2e": "playwright test", "eject": "react-scripts eject", "lint": "eslint src", "lint:fix": "eslint src --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,json,css,scss}", "prepare": "husky install"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "src/**/*.{json,css,scss,md}": ["prettier --write"]}, "description": "## Overview The Regression Rigor UI provides an interface for creating testcases, running campaigns as subsets of assessments, and tracking detection coverage for a range of MITRE TTPs (offensive testcases).", "main": "playwright.config.js", "directories": {"test": "tests"}, "keywords": [], "author": "", "license": "ISC"}