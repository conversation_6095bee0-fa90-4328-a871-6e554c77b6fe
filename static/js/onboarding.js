// Onboarding tour configuration
const tourSteps = [
  {
    element: '#main-content',
    title: 'Security Dashboard',
    intro: 'This is your security analysis dashboard. Let\'s take a quick tour of the main features.',
    position: 'bottom'
  },
  {
    element: '#nav-threats',
    title: 'Threat Analysis',
    intro: 'View and analyze potential security threats using MITRE ATT&CK framework integration.',
    position: 'right'
  },
  {
    element: '#nav-defense',
    title: 'Defense Mapping',
    intro: 'Map your defensive controls against known attack patterns using D3FEND.',
    position: 'left'
  },
  {
    element: '#nav-monitoring',
    title: 'System Monitoring',
    intro: 'Monitor your system\'s security status in real-time.',
    position: 'right'
  },
  {
    element: '#nav-settings',
    title: 'User Settings',
    intro: 'Customize your experience and manage your security preferences here.',
    position: 'left'
  }
];

// Initialize onboarding tour
function startOnboardingTour() {
  console.log('Starting onboarding tour...');
  introJs()
    .setOptions({
      steps: tourSteps,
      showProgress: true,
      showBullets: true,
      exitOnOverlayClick: false,
      disableInteraction: true,
      scrollToElement: true,
      highlightClass: 'highlight-tour-element',
      prevLabel: 'Back',
      nextLabel: 'Next',
      doneLabel: 'Finish',
      overlayOpacity: 0.8,
      tooltipClass: 'dark-theme'
    })
    .onbeforechange(function(targetElement) {
      console.log('Moving to element:', targetElement.id);
      // Ensure the element is in view
      targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    })
    .onexit(function() {
      console.log('Tour ended');
      // Mark tour as completed in localStorage
      localStorage.setItem('onboardingComplete', 'true');
    })
    .start();
}

// Check if this is the user's first visit
function checkFirstVisit() {
  if (!localStorage.getItem('onboardingComplete')) {
    startOnboardingTour();
  }
}

// Add event listener for manual tour start
document.addEventListener('DOMContentLoaded', () => {
  const startTourButton = document.getElementById('start-tour');
  if (startTourButton) {
    startTourButton.addEventListener('click', startOnboardingTour);
  }

  // Check for first visit
  checkFirstVisit();
});