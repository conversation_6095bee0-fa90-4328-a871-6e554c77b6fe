#!/usr/bin/env python3

import re

# Read the file
with open('models/tag.py', 'r') as f:
    content = f.read()

# Add @classmethod after @field_validator
pattern = r'@field_validator\([\'"][a-zA-Z_]+[\'"]\)\n    def'
replacement = r'@field_validator(\'\\1\')\n    @classmethod\n    def'

# Use re.sub with a function to handle the replacement
def replace_validator(match):
    return match.group(0).replace('def', '@classmethod\n    def')

# Apply the replacement
modified_content = re.sub(pattern, replace_validator, content)

# Write the modified content back to the file
with open('models/tag.py', 'w') as f:
    f.write(modified_content)

print("File updated successfully.")
