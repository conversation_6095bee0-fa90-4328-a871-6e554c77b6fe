#!/bin/bash
set -e

# Make sure we're on the integration branch
git checkout integration/all-features

# Function to merge a branch while ignoring pycache files
merge_branch() {
  branch=$1
  echo "Merging $branch..."
  
  # Try to merge
  if git merge $branch -X theirs --no-commit; then
    echo "Merge succeeded without conflicts."
  else
    echo "Merge had conflicts. Resolving..."
    
    # Remove all pycache files from the index
    git rm -rf --cached "**/__pycache__" "**/*.pyc" 2>/dev/null || true
    
    # Add all other files
    git add -A .
    
    # Check if there are still conflicts
    if git diff --cached --name-only --diff-filter=U | grep -q .; then
      echo "There are still unresolved conflicts. Aborting merge."
      git merge --abort
      return 1
    else
      echo "Conflicts resolved."
    fi
  fi
  
  # Commit the merge
  git commit -m "Merge $branch into integration/all-features"
  return 0
}

# Merge each feature branch
branches=(
  "feature/comprehensive-tagging-system"
  "feature/complete-core-crud-operations"
  "feature/campaign-management-api"
  "feature/testcase-management-api"
)

for branch in "${branches[@]}"; do
  if ! merge_branch $branch; then
    echo "Failed to merge $branch. Exiting."
    exit 1
  fi
done

echo "All branches merged successfully!" 