```typescript
import React, { useState, useEffect } from 'react';
import { AlertCircle, Check, Shield, ShieldAlert, ShieldX } from 'lucide-react';

interface Technique {
  id: string;
  name: string;
  description?: string;
}

interface Countermeasure {
  d3fend_id: string;
  name: string;
  confidence: number;
  type?: string;
}

interface TechniqueNode {
  technique: Technique;
  countermeasures: Countermeasure[];
  coverage: number;
}

interface AttackPathVisualizerProps {
  techniques: TechniqueNode[];
  showDescription?: boolean;
  onTechniqueClick?: (techniqueId: string) => void;
  onCountermeasureClick?: (countermeasureId: string) => void;
}

const getCoverageColor = (coverage: number): string => {
  if (coverage >= 0.8) return 'bg-green-100 border-green-500';
  if (coverage >= 0.5) return 'bg-yellow-100 border-yellow-500';
  return 'bg-red-100 border-red-500';
};

const getCoverageIcon = (coverage: number) => {
  if (coverage >= 0.8) return <Shield className="text-green-600" />;
  if (coverage >= 0.5) return <ShieldAlert className="text-yellow-600" />;
  return <ShieldX className="text-red-600" />;
};

const AttackPathNode: React.FC<{
  node: TechniqueNode;
  showDescription?: boolean;
  onTechniqueClick?: (techniqueId: string) => void;
  onCountermeasureClick?: (countermeasureId: string) => void;
}> = ({ node, showDescription, onTechniqueClick, onCountermeasureClick }) => {
  const [expanded, setExpanded] = useState(false);
  const colorClass = getCoverageColor(node.coverage);
  const icon = getCoverageIcon(node.coverage);

  return (
    <div className={`rounded-lg border-2 p-4 mb-4 ${colorClass}`}>
      <div className="flex justify-between items-center">
        <div 
          className="flex items-center gap-2 cursor-pointer"
          onClick={() => onTechniqueClick?.(node.technique.id)}
        >
          {icon}
          <div>
            <h3 className="font-bold">{node.technique.id}: {node.technique.name}</h3>
            {showDescription && node.technique.description && (
              <p className="text-sm text-gray-600">{node.technique.description}</p>
            )}
          </div>
        </div>
        <div className="text-right">
          <span className="text-lg font-semibold">{Math.round(node.coverage * 100)}%</span>
          <button 
            onClick={() => setExpanded(!expanded)}
            className="ml-2 text-blue-600 hover:text-blue-800"
          >
            {expanded ? 'Hide' : 'Show'} Countermeasures
          </button>
        </div>
      </div>
      
      {expanded && (
        <div className="mt-4 pl-8 border-l-2 border-gray-300">
          <h4 className="font-medium mb-2">D3FEND Countermeasures:</h4>
          {node.countermeasures.length === 0 ? (
            <p className="text-red-600 flex items-center gap-1">
              <AlertCircle size={16} />
              No countermeasures available
            </p>
          ) : (
            <ul className="space-y-2">
              {node.countermeasures.map(cm => (
                <li 
                  key={cm.d3fend_id} 
                  className="flex items-start gap-2 cursor-pointer hover:bg-gray-50 p-1 rounded"
                  onClick={() => onCountermeasureClick?.(cm.d3fend_id)}
                >
                  <Check size={16} className="text-green-600 mt-1 flex-shrink-0" />
                  <div>
                    <span className="font-medium">{cm.name}</span>
                    <span className="text-sm text-gray-600 ml-2">
                      (Confidence: {Math.round(cm.confidence * 100)}%)
                    </span>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  );
};

export const AttackPathVisualizer: React.FC<AttackPathVisualizerProps> = ({
  techniques,
  showDescription = false,
  onTechniqueClick,
  onCountermeasureClick
}) => {
  const [overallCoverage, setOverallCoverage] = useState(0);
  const [averageCoverage, setAverageCoverage] = useState(0);

  useEffect(() => {
    // Calculate overall coverage (weakest link)
    const minCoverage = Math.min(...techniques.map(t => t.coverage));
    setOverallCoverage(minCoverage);

    // Calculate average coverage
    const avgCoverage = techniques.reduce((sum, t) => sum + t.coverage, 0) / techniques.length;
    setAverageCoverage(avgCoverage);
  }, [techniques]);

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        <h2 className="text-2xl font-bold mb-4">Attack Path Coverage Analysis</h2>
        
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="border rounded-lg p-4 bg-gray-50">
            <h3 className="text-lg font-medium mb-2">Overall Path Coverage</h3>
            <div className="flex items-center">
              {getCoverageIcon(overallCoverage)}
              <div className="ml-2">
                <div className="text-3xl font-bold">{Math.round(overallCoverage * 100)}%</div>
                <div className="text-sm text-gray-600">Weakest link approach</div>
              </div>
            </div>
          </div>
          
          <div className="border rounded-lg p-4 bg-gray-50">
            <h3 className="text-lg font-medium mb-2">Average Coverage</h3>
            <div className="flex items-center">
              {getCoverageIcon(averageCoverage)}
              <div className="ml-2">
                <div className="text-3xl font-bold">{Math.round(averageCoverage * 100)}%</div>
                <div className="text-sm text-gray-600">Across all techniques</div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex items-center mb-4">
          <div className="flex-grow h-1 bg-gray-200 relative">
            <div className="absolute inset-y-0 left-0 bg-green-500" style={{width: '33.3%'}}></div>
            <div className="absolute inset-y-0 left-1/3 bg-yellow-500" style={{width: '33.3%'}}></div>
            <div className="absolute inset-y-0 left-2/3 bg-red-500" style={{width: '33.4%'}}></div>
          </div>
          <div className="grid grid-cols-3 text-xs text-center w-44 ml-2">
            <div>Low</div>
            <div>Medium</div>
            <div>High</div>
          </div>
        </div>
      </div>
      
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-xl font-bold mb-4">Attack Sequence</h3>
        
        <div className="space-y-2">
          {techniques.map((node, index) => (
            <div key={node.technique.id} className="relative">
              {index > 0 && (
                <div className="absolute top-0 left-6 h-6 w-0.5 bg-gray-400 -mt-6"></div>
              )}
              <AttackPathNode 
                node={node}
                showDescription={showDescription}
                onTechniqueClick={onTechniqueClick}
                onCountermeasureClick={onCountermeasureClick}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AttackPathVisualizer;
```
