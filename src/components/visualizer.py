import networkx as nx
import plotly.graph_objects as go
from typing import List, Dict, Any
import json

def create_attack_path_graph(techniques: List[Dict[str, Any]]) -> Dict:
    """
    Create an interactive attack path visualization using Plotly
    Returns a dictionary containing the graph data and layout for Plotly
    """
    # Create a directed graph
    G = nx.DiGraph()

    # Add nodes and edges
    for technique in techniques:
        # Add technique node
        tech_id = technique['technique']['id']
        G.add_node(tech_id, 
                  name=technique['technique']['name'],
                  type='technique',
                  description=technique['technique']['description'])

        # Add countermeasure nodes and edges
        for cm in technique['countermeasures']:
            cm_id = cm['d3fend_id']
            G.add_node(cm_id,
                      name=cm['name'],
                      type='countermeasure',
                      confidence=cm['confidence'])
            G.add_edge(tech_id, cm_id)

    # Create layout for the graph
    pos = nx.spring_layout(G)

    # Create nodes for techniques
    tech_nodes = [node for node in G.nodes() if G.nodes[node]['type'] == 'technique']
    tech_x = [pos[node][0] for node in tech_nodes]
    tech_y = [pos[node][1] for node in tech_nodes]
    tech_text = [f"{G.nodes[node]['name']}<br>ID: {node}<br>{G.nodes[node]['description']}" 
                for node in tech_nodes]

    # Create nodes for countermeasures
    cm_nodes = [node for node in G.nodes() if G.nodes[node]['type'] == 'countermeasure']
    cm_x = [pos[node][0] for node in cm_nodes]
    cm_y = [pos[node][1] for node in cm_nodes]
    cm_text = [f"{G.nodes[node]['name']}<br>Confidence: {G.nodes[node]['confidence']:.2%}" 
               for node in cm_nodes]

    # Create edges
    edge_x = []
    edge_y = []
    for edge in G.edges():
        x0, y0 = pos[edge[0]]
        x1, y1 = pos[edge[1]]
        edge_x.extend([x0, x1, None])
        edge_y.extend([y0, y1, None])

    # Create the Plotly traces as dictionaries
    traces = [
        # Edges
        {
            'type': 'scatter',
            'x': edge_x,
            'y': edge_y,
            'line': {'width': 0.5, 'color': '#888'},
            'hoverinfo': 'none',
            'mode': 'lines',
            'name': 'Connections'
        },
        # Technique nodes
        {
            'type': 'scatter',
            'x': tech_x,
            'y': tech_y,
            'mode': 'markers+text',
            'marker': {
                'size': 30,
                'color': 'red',
                'line': {'width': 2}
            },
            'text': [G.nodes[node]['name'] for node in tech_nodes],
            'hovertext': tech_text,
            'hoverinfo': 'text',
            'name': 'Attack Techniques'
        },
        # Countermeasure nodes
        {
            'type': 'scatter',
            'x': cm_x,
            'y': cm_y,
            'mode': 'markers+text',
            'marker': {
                'size': 25,
                'color': 'blue',
                'line': {'width': 2}
            },
            'text': [G.nodes[node]['name'] for node in cm_nodes],
            'hovertext': cm_text,
            'hoverinfo': 'text',
            'name': 'Countermeasures'
        }
    ]

    # Define the layout
    layout = {
        'title': 'Attack Path with Countermeasures',
        'showlegend': True,
        'hovermode': 'closest',
        'margin': {'b': 20, 'l': 5, 'r': 5, 't': 40},
        'xaxis': {'showgrid': False, 'zeroline': False, 'showticklabels': False},
        'yaxis': {'showgrid': False, 'zeroline': False, 'showticklabels': False},
        'plot_bgcolor': 'white'
    }

    return {
        'data': traces,
        'layout': layout
    }