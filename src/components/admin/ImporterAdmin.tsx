import React, { useState, useEffect } from 'react';
import { AlertCircle, CheckCircle, Loader, X } from 'lucide-react';

// Framework options for the dropdown
const FRAMEWORKS = [
  { id: 'mitre', name: 'MITRE ATT&CK' },
  { id: 'atlas', name: 'MITRE ATLAS' },
  { id: 'd3fend', name: 'D3FEND Ontology' }
];

// Import job status type
type ImportStatus = 'idle' | 'uploading' | 'processing' | 'success' | 'error';

// Import result type
interface ImportResult {
  status: 'success' | 'error';
  message: string;
  details?: Record<string, any>;
  timestamp?: string;
}

// Import job type
interface ImportJob {
  id: string;
  framework: string;
  filename: string;
  status: ImportStatus;
  progress: number;
  result?: ImportResult;
  startTime: Date;
  endTime?: Date;
}

const ImporterAdmin = () => {
  const [framework, setFramework] = useState('mitre');
  const [file, setFile] = useState<File | null>(null);
  const [status, setStatus] = useState<ImportStatus>('idle');
  const [progress, setProgress] = useState(0);
  const [result, setResult] = useState<ImportResult | null>(null);
  const [jobs, setJobs] = useState<ImportJob[]>([]);

  // Load previous import jobs
  useEffect(() => {
    const fetchJobs = async () => {
      try {
        const response = await fetch('/api/v1/admin/import/jobs');
        if (response.ok) {
          const data = await response.json();
          setJobs(data.jobs);
        }
      } catch (error) {
        console.error('Failed to fetch import jobs:', error);
      }
    };

    fetchJobs();
  }, []);

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0] || null;
    setFile(selectedFile);
    setResult(null);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!file) return;

    try {
      setStatus('uploading');
      setProgress(0);
      setResult(null);

      // Create FormData
      const formData = new FormData();
      formData.append('file', file);
      formData.append('framework', framework);

      // Create a job in the jobs list
      const jobId = crypto.randomUUID();
      const newJob: ImportJob = {
        id: jobId,
        framework,
        filename: file.name,
        status: 'uploading',
        progress: 0,
        startTime: new Date()
      };
      setJobs(prev => [newJob, ...prev]);

      // Upload the file
      const response = await fetch('/api/v1/admin/import', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Import failed');
      }

      // Start monitoring progress with EventSource
      setStatus('processing');
      const eventSource = new EventSource(`/api/v1/admin/import/status/${jobId}`);
      
      eventSource.onmessage = (event) => {
        const data = JSON.parse(event.data);
        setProgress(data.progress);
        
        // Update job in the list
        setJobs(prev => prev.map(job => 
          job.id === jobId 
            ? { ...job, status: 'processing', progress: data.progress } 
            : job
        ));

        if (data.status === 'completed') {
          eventSource.close();
          setStatus('success');
          setResult({ 
            status: 'success',
            message: 'Import completed successfully',
            details: data.details,
            timestamp: new Date().toISOString()
          });

          // Update job in the list
          setJobs(prev => prev.map(job => 
            job.id === jobId 
              ? { 
                  ...job, 
                  status: 'success', 
                  progress: 100,
                  result: { 
                    status: 'success',
                    message: 'Import completed successfully',
                    details: data.details
                  },
                  endTime: new Date()
                } 
              : job
          ));
        }
      };

      eventSource.onerror = () => {
        eventSource.close();
        setStatus('error');
        setResult({ 
          status: 'error',
          message: 'Error during import process',
          timestamp: new Date().toISOString()
        });

        // Update job in the list
        setJobs(prev => prev.map(job => 
          job.id === jobId 
            ? { 
                ...job, 
                status: 'error', 
                result: { 
                  status: 'error',
                  message: 'Error during import process'
                },
                endTime: new Date()
              } 
            : job
        ));
      };

    } catch (error) {
      setStatus('error');
      setResult({ 
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      });
    }
  };

  // Handle job cancellation
  const cancelJob = async (jobId: string) => {
    try {
      const response = await fetch(`/api/v1/admin/import/cancel/${jobId}`, {
        method: 'POST'
      });

      if (response.ok) {
        // Update job in the list
        setJobs(prev => prev.map(job => 
          job.id === jobId 
            ? { 
                ...job, 
                status: 'idle', 
                result: { 
                  status: 'error',
                  message: 'Import cancelled by user'
                },
                endTime: new Date()
              } 
            : job
        ));
      }
    } catch (error) {
      console.error('Failed to cancel import job:', error);
    }
  };

  return (
    <div className="w-full max-w-6xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-2xl font-bold mb-4">Framework Data Import</h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block font-medium mb-1">Framework Type</label>
            <select 
              value={framework} 
              onChange={(e) => setFramework(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
              disabled={status === 'uploading' || status === 'processing'}
            >
              {FRAMEWORKS.map(fw => (
                <option key={fw.id} value={fw.id}>{fw.name}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block font-medium mb-1">Import File (JSON/OWL)</label>
            <input 
              type="file" 
              accept=".json,.owl"
              onChange={handleFileChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              disabled={status === 'uploading' || status === 'processing'}
            />
          </div>
          
          {(status === 'uploading' || status === 'processing') && (
            <div className="mt-4">
              <div className="flex items-center">
                <Loader className="animate-spin h-5 w-5 mr-2" />
                <span>
                  {status === 'uploading' ? 'Uploading file...' : `Processing import (${progress.toFixed(0)}%)...`}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                <div 
                  className="bg-blue-600 h-2.5 rounded-full" 
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>
          )}
          
          <button 
            type="submit" 
            disabled={!file || status === 'uploading' || status === 'processing'}
            className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            Import Data
          </button>
        </form>
        
        {result && (
          <div className={`mt-6 p-4 rounded-md ${result.status === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
            <div className="flex items-start">
              {result.status === 'success' ? (
                <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
              ) : (
                <AlertCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5" />
              )}
              <div>
                <p className="font-medium">{result.status === 'success' ? 'Success!' : 'Error'}</p>
                <p className="text-sm mt-1">{result.message}</p>
                {result.details && (
                  <details className="mt-2">
                    <summary className="cursor-pointer text-sm font-medium">View Details</summary>
                    <pre className="mt-2 bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40">
                      {JSON.stringify(result.details, null, 2)}
                    </pre>
                  </details>
                )}
                <p className="text-xs text-gray-500 mt-2">
                  {result.timestamp ? new Date(result.timestamp).toLocaleString() : ''}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Recent Import Jobs */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-xl font-bold mb-4">Recent Import Jobs</h3>
        
        {jobs.length === 0 ? (
          <p className="text-gray-500">No import jobs yet</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Framework</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Filename</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Started</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {jobs.map(job => (
                  <tr key={job.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      {FRAMEWORKS.find(fw => fw.id === job.framework)?.name || job.framework}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {job.filename}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {job.status === 'processing' && (
                          <Loader className="animate-spin h-4 w-4 mr-2 text-blue-500" />
                        )}
                        {job.status === 'success' && (
                          <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                        )}
                        {job.status === 'error' && (
                          <AlertCircle className="h-4 w-4 mr-2 text-red-500" />
                        )}
                        <span>
                          {job.status === 'processing' ? `Processing (${job.progress.toFixed(0)}%)` : job.status}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {job.startTime.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {job.status === 'processing' && (
                        <button
                          onClick={() => cancelJob(job.id)}
                          className="inline-flex items-center text-red-600 hover:text-red-800"
                        >
                          <X className="h-4 w-4 mr-1" />
                          Cancel
                        </button>
                      )}
                      {job.result && (
                        <details className="cursor-pointer">
                          <summary className="text-blue-600 hover:text-blue-800">View Result</summary>
                          <div className={`mt-2 p-2 rounded text-xs ${job.status === 'success' ? 'bg-green-50' : 'bg-red-50'}`}>
                            <p>{job.result.message}</p>
                            {job.result.details && (
                              <pre className="mt-1 bg-gray-100 p-1 rounded max-h-20 overflow-auto">
                                {JSON.stringify(job.result.details, null, 2)}
                              </pre>
                            )}
                          </div>
                        </details>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImporterAdmin;