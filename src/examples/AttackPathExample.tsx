```typescript
import React from 'react';
import AttackPathVisualizer from '../components/AttackPathVisualizer';

const ExampleUsage: React.FC = () => {
  const sampleData = [
    {
      technique: {
        id: 'T1566',
        name: 'Phishing',
        description: 'Adversaries may send phishing messages to gain access to victim systems.'
      },
      coverage: 0.85,
      countermeasures: [
        { d3fend_id: 'D3-HTTP', name: 'HTTP Traffic Analysis', confidence: 0.9 },
        { d3fend_id: 'D3-DECOY', name: 'Decoy Environment', confidence: 0.8 }
      ]
    },
    {
      technique: {
        id: 'T1078',
        name: 'Valid Accounts',
        description: 'Adversaries may obtain and abuse credentials of existing accounts.'
      },
      coverage: 0.60,
      countermeasures: [
        { d3fend_id: 'D3-UAM', name: 'User Account Monitoring', confidence: 0.75 },
        { d3fend_id: 'D3-ACI', name: 'Authentication Cache Invalidation', confidence: 0.65 }
      ]
    },
    // Add more techniques as needed
  ];

  const handleTechniqueClick = (techniqueId: string) => {
    console.log(`Technique clicked: ${techniqueId}`);
    // Add your click handler logic here
  };

  const handleCountermeasureClick = (countermeasureId: string) => {
    console.log(`Countermeasure clicked: ${countermeasureId}`);
    // Add your click handler logic here
  };

  return (
    <AttackPathVisualizer
      techniques={sampleData}
      showDescription={true}
      onTechniqueClick={handleTechniqueClick}
      onCountermeasureClick={handleCountermeasureClick}
    />
  );
};

export default ExampleUsage;
```
