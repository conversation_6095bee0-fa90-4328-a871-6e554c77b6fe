<!DOCTYPE html>
<html data-bs-theme="dark">
<head>
    <title>Attack Path Visualization</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://bootswatch.com/5/darkly/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-2.27.0.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .technique-details {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            background-color: rgba(255, 255, 255, 0.05);
        }
        #graph {
            width: 100%;
            height: 600px;
            margin-top: 20px;
            background-color: rgba(255, 255, 255, 0.02);
            border-radius: 8px;
            padding: 15px;
        }
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
        }
    </style>
</head>
<body class="bg-dark text-light">
    <div class="theme-toggle">
        <button class="btn btn-outline-light" onclick="toggleTheme()">
            <span id="theme-icon">🌙</span>
        </button>
    </div>
    <div class="container">
        <h1 class="mb-4">Attack Path Visualization</h1>
        <div class="card bg-dark border-secondary">
            <div class="card-body">
                <h3>Enter MITRE ATT&CK Technique IDs (one per line):</h3>
                <form method="POST" action="/analyze" class="mt-3">
                    <div class="form-group">
                        <textarea 
                            name="technique_ids" 
                            class="form-control bg-dark text-light" 
                            rows="5" 
                            placeholder="Example:&#10;T1566&#10;T1078&#10;T1021"></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary mt-3">Analyze Path</button>
                </form>
            </div>
        </div>

        {% if error %}
        <div class="alert alert-danger mt-4">
            {{ error }}
        </div>
        {% endif %}

        {% if graph_data %}
        <div id="graph"></div>
        <script>
            var data = {{ graph_data | safe }};
            var layout = data.layout;
            // Update layout for dark theme
            layout.paper_bgcolor = 'rgba(0,0,0,0)';
            layout.plot_bgcolor = 'rgba(0,0,0,0)';
            layout.font = {
                color: '#ffffff'
            };
            Plotly.newPlot('graph', data.data, layout);
        </script>
        {% endif %}

        {% if techniques %}
        <div class="technique-details">
            <h2>Attack Techniques Details</h2>
            {% for tech in techniques %}
            <div class="card bg-dark border-secondary mb-3">
                <div class="card-body">
                    <h3>{{ tech.technique.name }} ({{ tech.technique.id }})</h3>
                    <p><strong>Description:</strong> {{ tech.technique.description }}</p>
                    <p><strong>Coverage:</strong> {{ "%.1f"|format(tech.coverage * 100) }}%</p>
                    <p><strong>Countermeasures:</strong></p>
                    <ul>
                    {% for cm in tech.countermeasures %}
                        <li>{{ cm.name }} (Confidence: {{ "%.1f"|format(cm.confidence * 100) }}%)</li>
                    {% endfor %}
                    </ul>
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}
    </div>

    <script>
        function toggleTheme() {
            const html = document.documentElement;
            const themeIcon = document.getElementById('theme-icon');
            const currentTheme = html.getAttribute('data-bs-theme');

            if (currentTheme === 'dark') {
                html.setAttribute('data-bs-theme', 'light');
                themeIcon.textContent = '☀️';
                document.body.classList.remove('bg-dark', 'text-light');
            } else {
                html.setAttribute('data-bs-theme', 'dark');
                themeIcon.textContent = '🌙';
                document.body.classList.add('bg-dark', 'text-light');
            }
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>