# Hierarchical Data Model

## Overview

The Hierarchical Data Model is a core component of the RegressionRigor platform, providing a structured approach to organizing security testing activities. This model establishes a clear hierarchy of entities, from environments at the top level down to individual test cases, enabling comprehensive organization and management of security testing activities.

## Hierarchy Structure

The hierarchical structure consists of the following levels:

1. **Environments (Top Level)**
   - Represent testing environments (e.g., Production, Staging, Development, Test)
   - Contain multiple Assessments
   - Provide high-level organization for security testing activities

2. **Assessments (Middle Level)**
   - Represent specific security testing scopes
   - Belong to an Environment
   - Contain multiple Campaigns
   - Track overall testing progress and outcomes

3. **Campaigns (Lower Level)**
   - Represent groups of related security tests
   - Belong to an Assessment
   - Contain multiple Test Cases
   - Organize tests by purpose or objective

4. **Test Cases (Bottom Level)**
   - Represent individual security tests
   - Belong to one or more Campaigns
   - Define specific testing activities and expected outcomes

## Implementation Details

The Hierarchical Data Model is implemented using SQLAlchemy models and FastAPI endpoints:

1. **Database Models**:
   - `Environment`: Defined in `api/models/environment.py`
   - `Assessment`: Defined in `api/models/assessment.py`
   - `Campaign`: Defined in `api/models/campaign.py`
   - `TestCase`: Defined in `api/models/testcase.py`

2. **Pydantic Schemas**:
   - Environment schemas: Defined in `api/models/schemas/environment.py`
   - Assessment schemas: Defined in `api/models/schemas/assessment.py`
   - Campaign schemas: Defined in `api/models/schemas/campaign.py`
   - TestCase schemas: Defined in `api/models/schemas/testcase.py`

3. **Database Migrations**:
   - Hierarchical model migration: `migrations/versions/20240316_hierarchical_data_model.py`
   - Circular dependency fix: `migrations/versions/20240319_remove_circular_dependencies.py`

4. **API Endpoints**:
   - Environment API: Defined in `api/routes/environment.py`
   - Assessment API: Defined in `api/routes/assessment.py`
   - Campaign API: Defined in `api/routes/campaign.py`
   - TestCase API: Defined in `api/routes/testcase.py`

## Relationships

The hierarchical model establishes the following relationships:

- **Environment to Assessment**: One-to-Many (One Environment can have multiple Assessments)
- **Assessment to Campaign**: One-to-Many (One Assessment can have multiple Campaigns)
- **Campaign to TestCase**: Many-to-Many (One Campaign can have multiple TestCases, and one TestCase can belong to multiple Campaigns)
- **Assessment to TestExecution**: One-to-Many (One Assessment can have multiple TestExecutions)

## Circular Dependency Resolution

The initial implementation had circular dependencies between Assessment and Campaign models:
- Assessment had a `campaign_id` field and a `campaign` relationship
- Campaign had an `assessment_id` field and an `assessments` relationship

These circular dependencies have been resolved by:
1. Removing the `campaign_id` field from the Assessment model
2. Removing the `campaign` relationship from the Assessment model
3. Keeping only the `assessment_id` field in the Campaign model
4. Keeping only the proper hierarchical relationships:
   - Assessment has a `campaigns` relationship (One Assessment has many Campaigns)
   - Campaign has an `assessment` relationship (Many Campaigns belong to one Assessment)

This ensures a clean, maintainable data model that properly reflects the hierarchical structure.

## API Documentation

Detailed API documentation is available for each level of the hierarchy:

- [Environment API Documentation](docs/environment_api.md)
- [Assessment API Documentation](docs/assessment_api.md)
- [Campaign API Documentation](docs/campaign_api.md)
- [TestCase API Documentation](docs/testcase_api.md)

## Usage Examples

### Creating a Complete Hierarchy

```python
import requests

# Authentication
token = "your_jwt_token"
headers = {"Authorization": f"Bearer {token}"}
base_url = "https://api.regressionrigor.com/api/v1"

# 1. Create an Environment
environment_data = {
    "name": "Production Environment",
    "description": "Production systems environment",
    "type": "production",
    "status": "active"
}
environment_response = requests.post(
    f"{base_url}/environments/",
    json=environment_data,
    headers=headers
)
environment_id = environment_response.json()["id"]

# 2. Create an Assessment within the Environment
assessment_data = {
    "name": "Q2 Security Assessment",
    "description": "Comprehensive security assessment for Q2",
    "target_system": "Payment Processing System",
    "assessment_type": "penetration",
    "status": "in_progress",
    "start_date": "2024-04-01T00:00:00",
    "end_date": "2024-06-30T23:59:59",
    "environment_id": environment_id
}
assessment_response = requests.post(
    f"{base_url}/assessments/",
    json=assessment_data,
    headers=headers
)
assessment_id = assessment_response.json()["id"]

# 3. Create a Campaign within the Assessment
campaign_data = {
    "name": "Payment API Security Testing",
    "description": "Security testing for the payment API",
    "status": "active",
    "start_date": "2024-04-15T00:00:00",
    "end_date": "2024-05-15T23:59:59",
    "assessment_id": assessment_id
}
campaign_response = requests.post(
    f"{base_url}/campaigns/",
    json=campaign_data,
    headers=headers
)
campaign_id = campaign_response.json()["id"]

# 4. Assign Test Cases to the Campaign
test_case_assignment = {
    "test_case_ids": [1, 2, 3]
}
requests.post(
    f"{base_url}/campaigns/{campaign_id}/test-cases",
    json=test_case_assignment,
    headers=headers
)
```

### Navigating the Hierarchy

```python
import requests

# Authentication
token = "your_jwt_token"
headers = {"Authorization": f"Bearer {token}"}
base_url = "https://api.regressionrigor.com/api/v1"

# Get all environments
environments = requests.get(
    f"{base_url}/environments/",
    headers=headers
).json()

# For each environment, get its assessments
for environment in environments:
    assessments = requests.get(
        f"{base_url}/environments/{environment['id']}/assessments",
        headers=headers
    ).json()
    
    # For each assessment, get its campaigns
    for assessment in assessments:
        campaigns = requests.get(
            f"{base_url}/assessments/{assessment['id']}/campaigns",
            headers=headers
        ).json()
        
        # For each campaign, get its test cases
        for campaign in campaigns:
            test_cases = requests.get(
                f"{base_url}/campaigns/{campaign['id']}/test-cases",
                headers=headers
            ).json()
```

## Benefits

The Hierarchical Data Model provides several benefits:

1. **Structured Organization**: Clear organization of security testing activities
2. **Improved Navigation**: Easy navigation between related entities
3. **Flexible Grouping**: Multiple ways to group and categorize tests
4. **Comprehensive Reporting**: Ability to generate reports at different levels of the hierarchy
5. **Access Control**: Granular control over who can access different parts of the hierarchy
6. **Clean Data Model**: No circular dependencies, making the codebase more maintainable

## Future Enhancements

Planned enhancements for the Hierarchical Data Model include:

1. **Hierarchical Dashboards**: Visual dashboards showing the status of entities at each level
2. **Inheritance Mechanisms**: Ability for settings and properties to cascade down the hierarchy
3. **Bulk Operations**: Support for operations that affect multiple levels of the hierarchy
4. **Advanced Filtering**: Enhanced filtering options based on hierarchical relationships
5. **Visualization Tools**: Tools for visualizing the hierarchy and relationships between entities 