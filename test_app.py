from flask import Flask, jsonify
import os
import psycopg2
from psycopg2.extras import RealDictCursor

app = Flask(__name__)

# Use direct IP address for testing since DNS resolution is failing
DATABASE_URL = os.environ.get("DATABASE_URL", "postgresql://regrigor:regrigor_password@localhost:3330/regrigor_db")

def get_db_connection():
    conn = psycopg2.connect(DATABASE_URL)
    conn.autocommit = True
    return conn

@app.route('/')
def home():
    return jsonify({
        "message": "Regression Rigor Test App",
        "status": "running"
    })

@app.route('/db-status')
def db_status():
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute('SELECT version();')
        version = cursor.fetchone()['version']
        cursor.close()
        conn.close()
        return jsonify({
            "status": "connected",
            "db_version": version
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/init-db')
def init_db():
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Create a simple users table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS test_users (
            id SERIAL PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Insert some test data
        cursor.execute('''
        INSERT INTO test_users (username, email) 
        VALUES ('testuser', '<EMAIL>')
        ON CONFLICT (username) DO NOTHING
        ''')
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return jsonify({
            "status": "success",
            "message": "Database initialized"
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/users')
def get_users():
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute('SELECT * FROM test_users')
        users = cursor.fetchall()
        cursor.close()
        conn.close()
        
        return jsonify({
            "status": "success",
            "users": users
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000) 