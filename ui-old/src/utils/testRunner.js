/**
 * testRunner.js
 * 
 * UI Testing utility that connects to the backend testing system.
 * Provides methods for creating, executing, and managing test scenarios.
 */

import axios from 'axios';
import logger from './logger';

// Current test run state
let currentRun = {
  running: false,
  scenarioId: null,
  abortController: null,
  statusCallback: null
};

/**
 * Build a step for navigating to a URL
 */
const buildNavigateStep = (url, description) => ({
  type: 'navigate',
  target: url,
  description: description || `Navigate to ${url}`
});

/**
 * Build a step for clicking an element
 */
const buildClickStep = (selector, description) => ({
  type: 'click',
  target: selector,
  description: description || `Click on ${selector}`
});

/**
 * Build a step for entering text
 */
const buildInputStep = (selector, value, description) => ({
  type: 'input',
  target: selector,
  value: value,
  description: description || `Enter "${value}" into ${selector}`
});

/**
 * Build a step for waiting (delay or condition)
 */
const buildWaitStep = (timeOrCondition, description) => {
  if (typeof timeOrCondition === 'number') {
    return {
      type: 'wait',
      duration: timeOrCondition,
      description: description || `Wait for ${timeOrCondition}ms`
    };
  } else {
    return {
      type: 'wait_for',
      condition: timeOrCondition,
      description: description || `Wait for condition: ${timeOrCondition}`
    };
  }
};

/**
 * Build a step for assertion
 */
const buildAssertStep = (assertion, description) => ({
  type: 'assert',
  assertion: assertion,
  description: description || `Assert: ${assertion}`
});

/**
 * Build a step for executing custom JavaScript
 */
const buildScriptStep = (script, description) => ({
  type: 'script',
  script: script,
  description: description || 'Execute custom script'
});

/**
 * Create a UI test scenario
 */
const createUITestScenario = async (name, description, steps) => {
  try {
    logger.info('Creating UI test scenario', 'TestRunner', {
      name,
      description,
      stepCount: steps.length
    });
    
    const response = await axios.post('/api/testing/scenarios', {
      name,
      description,
      scenario_data: {
        type: 'ui_test',
        steps: steps
      }
    });
    
    logger.info('Test scenario created successfully', 'TestRunner', {
      scenarioId: response.data.id
    });
    
    return response.data;
  } catch (error) {
    logger.exception(error, 'TestRunner', {
      operation: 'createUITestScenario',
      name,
      description
    });
    throw new Error(`Failed to create test scenario: ${error.message}`);
  }
};

/**
 * Create an API test scenario
 */
const createAPITestScenario = async (name, description, requests) => {
  try {
    logger.info('Creating API test scenario', 'TestRunner', {
      name,
      description,
      requestCount: requests.length
    });
    
    const response = await axios.post('/api/testing/scenarios', {
      name,
      description,
      scenario_data: {
        type: 'api_test',
        requests: requests
      }
    });
    
    logger.info('API test scenario created successfully', 'TestRunner', {
      scenarioId: response.data.id
    });
    
    return response.data;
  } catch (error) {
    logger.exception(error, 'TestRunner', {
      operation: 'createAPITestScenario',
      name,
      description
    });
    throw new Error(`Failed to create API test scenario: ${error.message}`);
  }
};

/**
 * Create an integration test scenario that combines UI and API tests
 */
const createIntegrationTestScenario = async (name, description, steps) => {
  try {
    logger.info('Creating integration test scenario', 'TestRunner', {
      name,
      description,
      stepCount: steps.length
    });
    
    const response = await axios.post('/api/testing/scenarios', {
      name,
      description,
      scenario_data: {
        type: 'integration_test',
        steps: steps
      }
    });
    
    logger.info('Integration test scenario created successfully', 'TestRunner', {
      scenarioId: response.data.id
    });
    
    return response.data;
  } catch (error) {
    logger.exception(error, 'TestRunner', {
      operation: 'createIntegrationTestScenario',
      name,
      description
    });
    throw new Error(`Failed to create integration test scenario: ${error.message}`);
  }
};

/**
 * Fetch test scenarios
 */
const fetchScenarios = async (filters = {}) => {
  try {
    let url = '/api/testing/scenarios';
    
    // Add query parameters for filters
    if (Object.keys(filters).length > 0) {
      const params = new URLSearchParams();
      for (const [key, value] of Object.entries(filters)) {
        params.append(key, value);
      }
      url += `?${params.toString()}`;
    }
    
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    logger.exception(error, 'TestRunner', {
      operation: 'fetchScenarios',
      filters
    });
    throw new Error(`Failed to fetch test scenarios: ${error.message}`);
  }
};

/**
 * Fetch a specific test scenario
 */
const fetchScenario = async (scenarioId) => {
  try {
    const response = await axios.get(`/api/testing/scenarios/${scenarioId}`);
    return response.data;
  } catch (error) {
    logger.exception(error, 'TestRunner', {
      operation: 'fetchScenario',
      scenarioId
    });
    throw new Error(`Failed to fetch test scenario: ${error.message}`);
  }
};

/**
 * Run a test scenario
 */
const runTest = async (scenarioId, statusCallback) => {
  // Prevent multiple concurrent test runs
  if (currentRun.running) {
    throw new Error('A test is already running. Abort the current test first.');
  }
  
  try {
    logger.info('Starting test run', 'TestRunner', { scenarioId });
    
    // Create an abort controller for cancellation
    const abortController = new AbortController();
    
    // Update current run state
    currentRun = {
      running: true,
      scenarioId,
      abortController,
      statusCallback
    };
    
    // Call the status callback with initial state
    if (statusCallback) {
      statusCallback({ status: 'starting', message: 'Test is starting...' });
    }
    
    // Start the test run
    const response = await axios.post(
      `/api/testing/scenarios/${scenarioId}/run`,
      {},
      { signal: abortController.signal }
    );
    
    const runId = response.data.run_id;
    
    // Poll for test status
    let completed = false;
    let result = null;
    
    while (!completed && currentRun.running) {
      // Wait between polls
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check if the test has been aborted
      if (!currentRun.running) {
        logger.warning('Test run was aborted', 'TestRunner', { 
          scenarioId, 
          runId 
        });
        
        if (statusCallback) {
          statusCallback({ 
            status: 'aborted', 
            message: 'Test was aborted by user' 
          });
        }
        
        break;
      }
      
      // Get the current status
      try {
        const statusResponse = await axios.get(
          `/api/testing/runs/${runId}`,
          { signal: abortController.signal }
        );
        
        result = statusResponse.data;
        completed = ['completed', 'failed', 'error'].includes(result.status);
        
        // Call the status callback
        if (statusCallback) {
          statusCallback({
            status: result.status,
            message: result.message || `Test status: ${result.status}`,
            step: result.current_step,
            progress: result.progress,
            result: completed ? result : null
          });
        }
      } catch (error) {
        if (error.name === 'AbortError') {
          // This is expected when the test is aborted
          break;
        }
        
        logger.error('Failed to check test status', 'TestRunner', {
          scenarioId,
          runId,
          error: error.message
        });
        
        if (statusCallback) {
          statusCallback({
            status: 'error',
            message: `Failed to check test status: ${error.message}`
          });
        }
      }
    }
    
    // Reset current run state
    currentRun = {
      running: false,
      scenarioId: null,
      abortController: null,
      statusCallback: null
    };
    
    // Log the test result
    if (result && completed) {
      const logLevel = result.status === 'completed' ? 'info' : 'warning';
      logger[logLevel](`Test run ${result.status}`, 'TestRunner', {
        scenarioId,
        runId,
        duration: result.duration,
        steps_total: result.steps_total,
        steps_completed: result.steps_completed
      });
    }
    
    return result;
  } catch (error) {
    // Reset current run state
    currentRun = {
      running: false,
      scenarioId: null,
      abortController: null,
      statusCallback: null
    };
    
    logger.exception(error, 'TestRunner', {
      operation: 'runTest',
      scenarioId
    });
    
    if (statusCallback) {
      statusCallback({
        status: 'error',
        message: `Test execution failed: ${error.message}`
      });
    }
    
    throw new Error(`Failed to run test: ${error.message}`);
  }
};

/**
 * Abort the current test run
 */
const abortTest = () => {
  if (!currentRun.running) {
    return false;
  }
  
  logger.warning('Aborting test run', 'TestRunner', {
    scenarioId: currentRun.scenarioId
  });
  
  if (currentRun.abortController) {
    currentRun.abortController.abort();
  }
  
  // Reset current run state
  currentRun = {
    running: false,
    scenarioId: null,
    abortController: null,
    statusCallback: null
  };
  
  return true;
};

/**
 * Check if a test is currently running
 */
const isRunning = () => {
  return currentRun.running;
};

/**
 * Delete a test scenario
 */
const deleteScenario = async (scenarioId) => {
  try {
    await axios.delete(`/api/testing/scenarios/${scenarioId}`);
    logger.info('Test scenario deleted', 'TestRunner', { scenarioId });
    return true;
  } catch (error) {
    logger.exception(error, 'TestRunner', {
      operation: 'deleteScenario',
      scenarioId
    });
    throw new Error(`Failed to delete test scenario: ${error.message}`);
  }
};

/**
 * Get test run history for a scenario
 */
const getTestHistory = async (scenarioId) => {
  try {
    const response = await axios.get(`/api/testing/scenarios/${scenarioId}/history`);
    return response.data;
  } catch (error) {
    logger.exception(error, 'TestRunner', {
      operation: 'getTestHistory',
      scenarioId
    });
    throw new Error(`Failed to get test history: ${error.message}`);
  }
};

/**
 * Get details of a specific test run
 */
const getTestRunDetails = async (runId) => {
  try {
    const response = await axios.get(`/api/testing/runs/${runId}`);
    return response.data;
  } catch (error) {
    logger.exception(error, 'TestRunner', {
      operation: 'getTestRunDetails',
      runId
    });
    throw new Error(`Failed to get test run details: ${error.message}`);
  }
};

/**
 * Update a test scenario
 */
const updateScenario = async (scenarioId, updates) => {
  try {
    const response = await axios.put(`/api/testing/scenarios/${scenarioId}`, updates);
    logger.info('Test scenario updated', 'TestRunner', { 
      scenarioId,
      updates: Object.keys(updates)
    });
    return response.data;
  } catch (error) {
    logger.exception(error, 'TestRunner', {
      operation: 'updateScenario',
      scenarioId,
      updates: Object.keys(updates)
    });
    throw new Error(`Failed to update test scenario: ${error.message}`);
  }
};

// Public API
const testRunner = {
  // Step builders
  buildNavigateStep,
  buildClickStep,
  buildInputStep,
  buildWaitStep,
  buildAssertStep,
  buildScriptStep,
  
  // Scenario creation
  createUITestScenario,
  createAPITestScenario,
  createIntegrationTestScenario,
  
  // Scenario management
  fetchScenarios,
  fetchScenario,
  updateScenario,
  deleteScenario,
  
  // Test execution
  runTest,
  abortTest,
  isRunning,
  
  // Test results
  getTestHistory,
  getTestRunDetails
};

export default testRunner; 