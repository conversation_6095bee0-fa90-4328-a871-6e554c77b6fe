/**
 * logger.js
 * 
 * UI Logging utility that connects to the backend logging system.
 * Provides methods for logging at different levels and measuring performance.
 */

import axios from 'axios';

// Log levels
const LOG_LEVELS = {
  DEBUG: 'DEBUG',
  INFO: 'INFO',
  WARNING: 'WARNING',
  ERROR: 'ERROR',
  CRITICAL: 'CRITICAL'
};

// Configuration
const config = {
  // Minimum level to send to API
  apiLevel: LOG_LEVELS.INFO,
  // Minimum level to log to console
  consoleLevel: LOG_LEVELS.DEBUG,
  // Enable batch logging
  enableBatchLogging: true,
  // Maximum batch size
  maxBatchSize: 10,
  // Maximum time to wait before sending batch (ms)
  maxBatchWait: 5000,
  // Enable tracking of correlation ID
  trackCorrelationId: true,
  // Enable tracking of session ID
  trackSessionId: true
};

// Store for logs to be sent in batch
let logBatch = [];
let batchTimeoutId = null;

// Level priority for filtering
const LEVEL_PRIORITY = {
  [LOG_LEVELS.DEBUG]: 0,
  [LOG_LEVELS.INFO]: 1,
  [LOG_LEVELS.WARNING]: 2,
  [LOG_LEVELS.ERROR]: 3,
  [LOG_LEVELS.CRITICAL]: 4
};

/**
 * Generate a correlation ID if one doesn't exist
 */
const getCorrelationId = () => {
  if (!config.trackCorrelationId) return null;
  
  // Check if we have a correlation ID in sessionStorage
  let correlationId = sessionStorage.getItem('correlationId');
  
  // Generate a new one if not found
  if (!correlationId) {
    correlationId = 'ui-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    sessionStorage.setItem('correlationId', correlationId);
  }
  
  return correlationId;
};

/**
 * Get or create a session ID
 */
const getSessionId = () => {
  if (!config.trackSessionId) return null;
  
  // Check if we have a session ID in localStorage
  let sessionId = localStorage.getItem('sessionId');
  
  // Generate a new one if not found
  if (!sessionId) {
    sessionId = 'session-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    localStorage.setItem('sessionId', sessionId);
  }
  
  return sessionId;
};

/**
 * Format and output log to console
 */
const logToConsole = (level, message, component, metadata = {}) => {
  if (LEVEL_PRIORITY[level] < LEVEL_PRIORITY[config.consoleLevel]) {
    return;
  }
  
  const timestamp = new Date().toISOString();
  const correlationId = getCorrelationId();
  const sessionId = getSessionId();
  
  // Build log object
  const logObject = {
    timestamp,
    level,
    message,
    component,
    correlationId,
    sessionId,
    ...metadata
  };
  
  // Choose console method based on level
  switch (level) {
    case LOG_LEVELS.DEBUG:
      console.debug(`[${timestamp}] [${level}] [${component}]:`, message, metadata);
      break;
    case LOG_LEVELS.INFO:
      console.info(`[${timestamp}] [${level}] [${component}]:`, message, metadata);
      break;
    case LOG_LEVELS.WARNING:
      console.warn(`[${timestamp}] [${level}] [${component}]:`, message, metadata);
      break;
    case LOG_LEVELS.ERROR:
    case LOG_LEVELS.CRITICAL:
      console.error(`[${timestamp}] [${level}] [${component}]:`, message, metadata);
      break;
    default:
      console.log(`[${timestamp}] [${level}] [${component}]:`, message, metadata);
  }
  
  return logObject;
};

/**
 * Send log to API
 */
const sendLogToApi = async (level, message, component, metadata = {}) => {
  if (LEVEL_PRIORITY[level] < LEVEL_PRIORITY[config.apiLevel]) {
    return;
  }
  
  try {
    const logEntry = {
      level,
      message,
      source: 'UI',
      component,
      correlation_id: getCorrelationId(),
      session_id: getSessionId(),
      metadata: JSON.stringify(metadata)
    };
    
    if (config.enableBatchLogging) {
      // Add to batch
      addToBatch(logEntry);
    } else {
      // Send immediately
      await axios.post('/api/logs', logEntry);
    }
  } catch (error) {
    // If we can't log to API, at least log the error to console
    console.error('Failed to send log to API:', error);
  }
};

/**
 * Add log to batch
 */
const addToBatch = (logEntry) => {
  logBatch.push(logEntry);
  
  // Send batch immediately if we've reached max size
  if (logBatch.length >= config.maxBatchSize) {
    sendBatch();
    return;
  }
  
  // Set up timeout to send batch if we don't reach max size
  if (!batchTimeoutId) {
    batchTimeoutId = setTimeout(sendBatch, config.maxBatchWait);
  }
};

/**
 * Send batch of logs to API
 */
const sendBatch = async () => {
  if (batchTimeoutId) {
    clearTimeout(batchTimeoutId);
    batchTimeoutId = null;
  }
  
  if (logBatch.length === 0) {
    return;
  }
  
  const batch = [...logBatch];
  logBatch = [];
  
  try {
    await axios.post('/api/logs/batch', { logs: batch });
  } catch (error) {
    console.error('Failed to send log batch to API:', error);
    // Put logs back in batch to retry
    logBatch = [...batch, ...logBatch];
    
    // Set timer to retry
    if (!batchTimeoutId) {
      batchTimeoutId = setTimeout(sendBatch, config.maxBatchWait);
    }
  }
};

/**
 * Update logger configuration
 */
const updateConfig = async (newConfig) => {
  Object.assign(config, newConfig);
  
  // Fetch config from server if we need to sync
  if (newConfig.syncWithServer) {
    try {
      const response = await axios.get('/api/logs/config');
      const serverConfig = response.data;
      
      // Update local config with server values
      if (serverConfig.console_level) {
        config.consoleLevel = serverConfig.console_level;
      }
      
      if (serverConfig.db_level) {
        config.apiLevel = serverConfig.db_level;
      }
    } catch (error) {
      console.error('Failed to fetch logging config from server:', error);
    }
  }
  
  return config;
};

/**
 * Create a function that records the execution time of the provided function
 */
const timeFunction = async (fn, metricType, component, metadata = {}) => {
  const start = performance.now();
  try {
    const result = await fn();
    const end = performance.now();
    const duration = end - start;
    
    // Send metric to API
    try {
      const metricEntry = {
        metric_type: metricType,
        component,
        value: duration,
        unit: 'ms',
        correlation_id: getCorrelationId(),
        session_id: getSessionId(),
        metadata: JSON.stringify({ ...metadata, success: true })
      };
      
      await axios.post('/api/logs/metrics', metricEntry);
    } catch (error) {
      console.error('Failed to record performance metric:', error);
    }
    
    return result;
  } catch (error) {
    const end = performance.now();
    const duration = end - start;
    
    // Send error metric to API
    try {
      const metricEntry = {
        metric_type: metricType,
        component,
        value: duration,
        unit: 'ms',
        correlation_id: getCorrelationId(),
        session_id: getSessionId(),
        metadata: JSON.stringify({ 
          ...metadata, 
          success: false, 
          error: error.message 
        })
      };
      
      await axios.post('/api/logs/metrics', metricEntry);
    } catch (metricError) {
      console.error('Failed to record performance metric for error:', metricError);
    }
    
    throw error;
  }
};

/**
 * Log and trace exceptions
 */
const logException = (error, component, metadata = {}) => {
  const errorMessage = error.message || 'Unknown error';
  const stackTrace = error.stack || 'No stack trace available';
  
  const enrichedMetadata = {
    ...metadata,
    stackTrace,
    errorType: error.name || error.constructor.name,
    errorCode: error.code || null,
    url: window.location.href
  };
  
  // Log to console with stack trace
  logToConsole(LOG_LEVELS.ERROR, errorMessage, component, enrichedMetadata);
  
  // Send to API
  sendLogToApi(LOG_LEVELS.ERROR, errorMessage, component, enrichedMetadata);
};

/**
 * Router change logger for integration with React Router
 */
const logRouteChange = (location, action) => {
  const metadata = {
    path: location.pathname,
    search: location.search,
    hash: location.hash,
    action: action
  };
  
  logToConsole(LOG_LEVELS.INFO, 'Route changed', 'Router', metadata);
  sendLogToApi(LOG_LEVELS.INFO, 'Route changed', 'Router', metadata);
};

/**
 * Log HTTP requests (for use with axios interceptors)
 */
const setupAxiosInterceptors = (axiosInstance = axios) => {
  // Request interceptor
  axiosInstance.interceptors.request.use(config => {
    // Add correlation ID and session ID to headers
    const correlationId = getCorrelationId();
    const sessionId = getSessionId();
    
    if (correlationId) {
      config.headers['X-Correlation-ID'] = correlationId;
    }
    
    if (sessionId) {
      config.headers['X-Session-ID'] = sessionId;
    }
    
    // Log the request
    logToConsole(
      LOG_LEVELS.DEBUG,
      `HTTP ${config.method.toUpperCase()} ${config.url}`,
      'HTTP',
      {
        method: config.method.toUpperCase(),
        url: config.url,
        data: config.data
      }
    );
    
    // Add request timestamp for performance tracking
    config._requestTime = performance.now();
    
    return config;
  });
  
  // Response interceptor
  axiosInstance.interceptors.response.use(
    response => {
      // Calculate duration
      const duration = performance.now() - (response.config._requestTime || 0);
      const status = response.status;
      
      // Log the response
      logToConsole(
        LOG_LEVELS.DEBUG,
        `HTTP ${response.config.method.toUpperCase()} ${response.config.url} ${status}`,
        'HTTP',
        {
          method: response.config.method.toUpperCase(),
          url: response.config.url,
          status,
          duration: `${duration.toFixed(2)}ms`,
          dataSize: JSON.stringify(response.data).length
        }
      );
      
      // Record performance metric
      if (duration > 0) {
        const metricEntry = {
          metric_type: 'http_request',
          component: 'HTTP',
          value: duration,
          unit: 'ms',
          correlation_id: getCorrelationId(),
          session_id: getSessionId(),
          metadata: JSON.stringify({
            method: response.config.method.toUpperCase(),
            url: response.config.url,
            status,
            success: true
          })
        };
        
        axios.post('/api/logs/metrics', metricEntry).catch(error => {
          console.error('Failed to record HTTP performance metric:', error);
        });
      }
      
      return response;
    },
    error => {
      // Calculate duration
      const duration = performance.now() - (error.config?._requestTime || 0);
      const status = error.response?.status || 0;
      
      // Log the error
      const metadata = {
        method: error.config?.method?.toUpperCase() || 'UNKNOWN',
        url: error.config?.url || 'UNKNOWN',
        status,
        duration: `${duration.toFixed(2)}ms`,
        error: error.message
      };
      
      logToConsole(
        LOG_LEVELS.ERROR,
        `HTTP ${metadata.method} ${metadata.url} ${status}`,
        'HTTP',
        metadata
      );
      
      // Record performance metric for failed request
      if (duration > 0) {
        const metricEntry = {
          metric_type: 'http_request',
          component: 'HTTP',
          value: duration,
          unit: 'ms',
          correlation_id: getCorrelationId(),
          session_id: getSessionId(),
          metadata: JSON.stringify({
            ...metadata,
            success: false
          })
        };
        
        axios.post('/api/logs/metrics', metricEntry).catch(err => {
          console.error('Failed to record HTTP error performance metric:', err);
        });
      }
      
      return Promise.reject(error);
    }
  );
  
  return axiosInstance;
};

// Set up interceptors for default axios instance
setupAxiosInterceptors();

// Send pending logs before window unloads
window.addEventListener('beforeunload', () => {
  if (logBatch.length > 0) {
    // Use sendBeacon for more reliable delivery during page unload
    if (navigator.sendBeacon) {
      const data = new Blob(
        [JSON.stringify({ logs: logBatch })], 
        { type: 'application/json' }
      );
      navigator.sendBeacon('/api/logs/batch', data);
    } else {
      // Fallback to synchronous XHR
      const xhr = new XMLHttpRequest();
      xhr.open('POST', '/api/logs/batch', false);  // false makes it synchronous
      xhr.setRequestHeader('Content-Type', 'application/json');
      xhr.send(JSON.stringify({ logs: logBatch }));
    }
    
    logBatch = [];
  }
});

// Logger object with public methods
const logger = {
  // Log level methods
  debug: (message, component, metadata = {}) => {
    const logObject = logToConsole(LOG_LEVELS.DEBUG, message, component, metadata);
    sendLogToApi(LOG_LEVELS.DEBUG, message, component, metadata);
    return logObject;
  },
  
  info: (message, component, metadata = {}) => {
    const logObject = logToConsole(LOG_LEVELS.INFO, message, component, metadata);
    sendLogToApi(LOG_LEVELS.INFO, message, component, metadata);
    return logObject;
  },
  
  warning: (message, component, metadata = {}) => {
    const logObject = logToConsole(LOG_LEVELS.WARNING, message, component, metadata);
    sendLogToApi(LOG_LEVELS.WARNING, message, component, metadata);
    return logObject;
  },
  
  error: (message, component, metadata = {}) => {
    const logObject = logToConsole(LOG_LEVELS.ERROR, message, component, metadata);
    sendLogToApi(LOG_LEVELS.ERROR, message, component, metadata);
    return logObject;
  },
  
  critical: (message, component, metadata = {}) => {
    const logObject = logToConsole(LOG_LEVELS.CRITICAL, message, component, metadata);
    sendLogToApi(LOG_LEVELS.CRITICAL, message, component, metadata);
    return logObject;
  },
  
  // Special methods
  exception: logException,
  
  // User action logging
  userAction: (actionType, component, metadata = {}) => {
    const message = `User action: ${actionType}`;
    return logger.info(message, component, { actionType, ...metadata });
  },
  
  // Performance measurement
  timeFunction,
  
  // Router integration
  logRouteChange,
  
  // HTTP interceptors
  setupAxiosInterceptors,
  
  // Configuration
  getConfig: () => ({ ...config }),
  updateConfig,
  
  // Correlation and session management
  getCorrelationId,
  setCorrelationId: (id) => {
    sessionStorage.setItem('correlationId', id);
  },
  
  getSessionId,
  setSessionId: (id) => {
    localStorage.setItem('sessionId', id);
  },
  
  // Useful for testing
  _flushBatch: sendBatch
};

export default logger; 