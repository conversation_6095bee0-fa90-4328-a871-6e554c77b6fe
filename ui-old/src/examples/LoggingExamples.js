/**
 * LoggingExamples.js
 * 
 * This file contains examples of how to use the logging and testing framework
 * in UI code. It's for demonstration purposes and is not used in production.
 */

import React, { useEffect, useState } from 'react';
import logger from '../utils/logger';
import testRunner from '../utils/testRunner';

/**
 * Example component demonstrating logger usage
 */
export const LoggingExampleComponent = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Log component mount
    logger.debug('Component mounted', 'LoggingExampleComponent');

    // Log lifecycle events
    return () => {
      logger.debug('Component unmounted', 'LoggingExampleComponent');
    };
  }, []);

  const fetchData = async () => {
    // Reset state
    setLoading(true);
    setError(null);

    // Log user action with metadata
    logger.info('User initiated data fetch', 'LoggingExampleComponent', {
      timestamp: new Date().toISOString(),
      requestParams: { limit: 10 }
    });

    try {
      // Use the timeFunction utility to measure performance
      const result = await logger.timeFunction(
        async () => {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // Randomly succeed or fail
          if (Math.random() > 0.7) {
            throw new Error('Random API error');
          }
          
          return { items: [1, 2, 3, 4, 5] };
        },
        'api_request',
        'LoggingExampleComponent'
      );

      // Log success
      logger.info('Data fetch successful', 'LoggingExampleComponent', {
        itemCount: result.items.length
      });

      setData(result);
    } catch (error) {
      // Log exception with context
      logger.exception(error, 'LoggingExampleComponent', {
        operation: 'fetchData',
        retry: false
      });

      setError(error);
    } finally {
      setLoading(false);
    }
  };

  const simulateWarning = () => {
    // Log a warning with metadata
    logger.warning(
      'Resource usage approaching limit',
      'LoggingExampleComponent',
      {
        currentUsage: '85%',
        threshold: '90%',
        resource: 'memory'
      }
    );
  };

  const simulateError = () => {
    // Log an error with metadata
    logger.error(
      'Operation failed',
      'LoggingExampleComponent',
      {
        operation: 'processData',
        details: 'Invalid input format'
      }
    );
  };

  const simulateCritical = () => {
    // Log a critical error with metadata
    logger.critical(
      'System unavailable',
      'LoggingExampleComponent',
      {
        reason: 'Database connection lost',
        retryCount: 3,
        timeElapsed: '00:05:23'
      }
    );
  };

  return (
    <div>
      <h2>Logging Example Component</h2>
      <div>
        <button onClick={fetchData} disabled={loading}>
          {loading ? 'Loading...' : 'Fetch Data'}
        </button>
        <button onClick={simulateWarning}>Simulate Warning</button>
        <button onClick={simulateError}>Simulate Error</button>
        <button onClick={simulateCritical}>Simulate Critical</button>
      </div>
      
      {error && <div>Error: {error.message}</div>}
      
      {data && (
        <div>
          <h3>Data Loaded:</h3>
          <pre>{JSON.stringify(data, null, 2)}</pre>
        </div>
      )}
    </div>
  );
};

/**
 * Example functions demonstrating test runner usage
 */
export const TestingExamples = {
  /**
   * Create a test scenario for login flow
   */
  createLoginTest: async () => {
    logger.info('Creating login test scenario', 'TestingExamples');
    
    const steps = [
      testRunner.buildNavigateStep('/login', 'Navigate to login page'),
      testRunner.buildInputStep('#username', 'testuser', 'Enter username'),
      testRunner.buildInputStep('#password', 'password123', 'Enter password'),
      testRunner.buildClickStep('#login-button', 'Click login button'),
      testRunner.buildWaitStep(1000, 'Wait for redirect'),
      testRunner.buildAssertStep('contains(#welcome-message, "Welcome")', 'Verify login success')
    ];
    
    try {
      const scenario = await testRunner.createUITestScenario(
        'Login Test',
        'Tests the user login functionality',
        steps
      );
      
      logger.info('Login test scenario created', 'TestingExamples', {
        scenarioId: scenario.id,
        stepCount: steps.length
      });
      
      return scenario;
    } catch (error) {
      logger.exception(error, 'TestingExamples', {
        operation: 'createLoginTest'
      });
      throw error;
    }
  },
  
  /**
   * Create a test scenario for dashboard flow
   */
  createDashboardTest: async () => {
    logger.info('Creating dashboard test scenario', 'TestingExamples');
    
    const steps = [
      testRunner.buildNavigateStep('/dashboard', 'Navigate to dashboard'),
      testRunner.buildWaitStep(500, 'Wait for dashboard to load'),
      testRunner.buildClickStep('#add-button', 'Click add button'),
      testRunner.buildInputStep('#item-name', 'New Item', 'Enter item name'),
      testRunner.buildInputStep('#item-description', 'This is a new item', 'Enter description'),
      testRunner.buildClickStep('#save-button', 'Click save button'),
      testRunner.buildWaitStep(500, 'Wait for item to be added'),
      testRunner.buildAssertStep('contains(#items-list, "New Item")', 'Verify item was added')
    ];
    
    try {
      const scenario = await testRunner.createUITestScenario(
        'Dashboard Test',
        'Tests adding a new item on the dashboard',
        steps
      );
      
      logger.info('Dashboard test scenario created', 'TestingExamples', {
        scenarioId: scenario.id,
        stepCount: steps.length
      });
      
      return scenario;
    } catch (error) {
      logger.exception(error, 'TestingExamples', {
        operation: 'createDashboardTest'
      });
      throw error;
    }
  },
  
  /**
   * Run a test scenario
   */
  runTest: async (scenarioId) => {
    logger.info('Running test scenario', 'TestingExamples', {
      scenarioId
    });
    
    try {
      // Run the test with status updates
      await testRunner.runTest(scenarioId, (status) => {
        logger.info(`Test status updated: ${status.status}`, 'TestingExamples', { status });
      });
      
      logger.info('Test scenario completed', 'TestingExamples', {
        scenarioId
      });
    } catch (error) {
      logger.exception(error, 'TestingExamples', {
        operation: 'runTest',
        scenarioId
      });
      throw error;
    }
  },
  
  /**
   * Run all tests
   */
  runAllTests: async () => {
    logger.info('Running all test scenarios', 'TestingExamples');
    
    try {
      // Get all available scenarios
      const scenarios = await testRunner.fetchScenarios();
      
      logger.info(`Found ${scenarios.length} scenarios to run`, 'TestingExamples');
      
      // Run each scenario sequentially
      for (const scenario of scenarios) {
        logger.info(`Running scenario: ${scenario.name}`, 'TestingExamples', {
          scenarioId: scenario.id
        });
        
        await TestingExamples.runTest(scenario.id);
      }
      
      logger.info('All test scenarios completed', 'TestingExamples');
    } catch (error) {
      logger.exception(error, 'TestingExamples', {
        operation: 'runAllTests'
      });
      throw error;
    }
  }
};

/**
 * Example component to demonstrate test runner UI
 */
export const TestRunnerComponent = () => {
  const [scenarios, setScenarios] = useState([]);
  const [loading, setLoading] = useState(false);
  const [testStatus, setTestStatus] = useState(null);
  
  useEffect(() => {
    // Load scenarios when component mounts
    loadScenarios();
  }, []);
  
  const loadScenarios = async () => {
    setLoading(true);
    try {
      const data = await testRunner.fetchScenarios();
      setScenarios(data);
      logger.info(`Loaded ${data.length} test scenarios`, 'TestRunnerComponent');
    } catch (error) {
      logger.exception(error, 'TestRunnerComponent', {
        operation: 'loadScenarios'
      });
    } finally {
      setLoading(false);
    }
  };
  
  const createSampleTest = async () => {
    try {
      await TestingExamples.createLoginTest();
      await loadScenarios();
    } catch (error) {
      console.error('Error creating test:', error);
    }
  };
  
  const runTest = async (scenarioId) => {
    if (testRunner.isRunning()) {
      logger.warning('A test is already running', 'TestRunnerComponent');
      return;
    }
    
    setTestStatus({ status: 'running', message: 'Test starting...' });
    
    try {
      await testRunner.runTest(scenarioId, (status) => {
        setTestStatus(status);
        logger.info(`Test status: ${status.status}`, 'TestRunnerComponent', status);
      });
      
      // Refresh scenarios after test completes
      setTimeout(() => {
        loadScenarios();
      }, 1000);
    } catch (error) {
      logger.exception(error, 'TestRunnerComponent', {
        operation: 'runTest',
        scenarioId
      });
      setTestStatus({ 
        status: 'error', 
        message: `Error running test: ${error.message}` 
      });
    }
  };
  
  return (
    <div>
      <h2>Test Runner Example</h2>
      
      <div>
        <button onClick={createSampleTest} disabled={loading}>
          Create Sample Test
        </button>
        <button onClick={loadScenarios} disabled={loading}>
          Refresh Scenarios
        </button>
      </div>
      
      {testStatus && (
        <div style={{ 
          padding: '10px', 
          margin: '10px 0', 
          backgroundColor: testStatus.status === 'error' ? '#ffdddd' : '#ddffdd',
          border: '1px solid #ccc' 
        }}>
          <strong>Test Status:</strong> {testStatus.message || testStatus.status}
          
          {testStatus.status === 'running' && (
            <button 
              onClick={() => testRunner.abortTest()} 
              style={{ marginLeft: '10px' }}
            >
              Abort Test
            </button>
          )}
        </div>
      )}
      
      <h3>Available Test Scenarios</h3>
      {loading ? (
        <div>Loading scenarios...</div>
      ) : scenarios.length === 0 ? (
        <div>No test scenarios available</div>
      ) : (
        <ul>
          {scenarios.map(scenario => (
            <li key={scenario.id}>
              <div>
                <strong>{scenario.name}</strong> - {scenario.description}
                <button 
                  onClick={() => runTest(scenario.id)} 
                  disabled={testRunner.isRunning()}
                  style={{ marginLeft: '10px' }}
                >
                  Run Test
                </button>
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

// Export a default component that shows all examples
const LoggingExamples = () => {
  return (
    <div>
      <h1>Logging and Testing Framework Examples</h1>
      <p>
        This page demonstrates how to use the unified logging and testing framework
        in UI components. Open the browser console to see the log output.
      </p>
      
      <hr />
      <LoggingExampleComponent />
      
      <hr />
      <TestRunnerComponent />
    </div>
  );
};

export default LoggingExamples; 