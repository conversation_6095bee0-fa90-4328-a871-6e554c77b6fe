import React from 'react';
import { BrowserRouter as Router, Route, Routes, Link } from 'react-router-dom';
import './App.css';
import Dashboard from './components/Dashboard';
import LoginForm from './components/LoginForm';
import TodoList from './components/TodoList';
import AdminPanel from './components/AdminPanel';
import LoggingDashboard from './components/LoggingDashboard';
import LoggingExamples from './examples/LoggingExamples';

function App() {
  return (
    <Router>
      <div className="App">
        <header className="App-header">
          <h1>Regression Rigor App</h1>
          <nav>
            <ul>
              <li><Link to="/">Home</Link></li>
              <li><Link to="/login">Login</Link></li>
              <li><Link to="/todos">Todo List</Link></li>
              <li><Link to="/admin">Admin Panel</Link></li>
              <li><Link to="/logging">Logging Dashboard</Link></li>
              <li><Link to="/examples">Logging Examples</Link></li>
            </ul>
          </nav>
        </header>
        <main>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/login" element={<LoginForm />} />
            <Route path="/todos" element={<TodoList />} />
            <Route path="/admin" element={<AdminPanel />} />
            <Route path="/logging" element={<LoggingDashboard />} />
            <Route path="/examples" element={<LoggingExamples />} />
          </Routes>
        </main>
        <footer>
          <p>&copy; 2023 Regression Rigor</p>
        </footer>
      </div>
    </Router>
  );
}

export default App; 