import React, { useState, useEffect } from 'react';
import axios from 'axios';
import logger from '../utils/logger';
import testRunner from '../utils/testRunner';

// Simple style objects
const styles = {
  container: {
    padding: '20px',
    maxWidth: '1200px',
    margin: '0 auto',
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '20px',
  },
  tabs: {
    display: 'flex',
    marginBottom: '20px',
    borderBottom: '1px solid #ddd',
  },
  tab: {
    padding: '10px 20px',
    cursor: 'pointer',
    marginRight: '10px',
  },
  activeTab: {
    padding: '10px 20px',
    cursor: 'pointer',
    marginRight: '10px',
    borderBottom: '2px solid #007bff',
    fontWeight: 'bold',
  },
  card: {
    border: '1px solid #ddd',
    borderRadius: '4px',
    padding: '15px',
    marginBottom: '15px',
    backgroundColor: '#fff',
    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
  },
  filters: {
    display: 'flex',
    marginBottom: '20px',
    flexWrap: 'wrap',
    gap: '10px',
  },
  select: {
    padding: '8px 12px',
    borderRadius: '4px',
    border: '1px solid #ddd',
  },
  input: {
    padding: '8px 12px',
    borderRadius: '4px',
    border: '1px solid #ddd',
    minWidth: '200px',
  },
  button: {
    padding: '8px 16px',
    backgroundColor: '#007bff',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
  },
  secondaryButton: {
    padding: '8px 16px',
    backgroundColor: '#6c757d',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    marginRight: '8px',
  },
  dangerButton: {
    padding: '8px 16px',
    backgroundColor: '#dc3545',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
  },
  successButton: {
    padding: '8px 16px',
    backgroundColor: '#28a745',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
  },
  badge: {
    padding: '4px 8px',
    borderRadius: '4px',
    fontSize: '12px',
    fontWeight: 'bold',
  },
  table: {
    width: '100%',
    borderCollapse: 'collapse',
  },
  th: {
    textAlign: 'left',
    padding: '10px',
    borderBottom: '1px solid #ddd',
    backgroundColor: '#f8f9fa',
  },
  td: {
    padding: '10px',
    borderBottom: '1px solid #ddd',
  },
  pre: {
    backgroundColor: '#f8f9fa',
    padding: '10px',
    borderRadius: '4px',
    overflow: 'auto',
    maxHeight: '400px',
  }
};

// Level badge colors
const levelColors = {
  debug: '#6c757d',
  info: '#17a2b8',
  warning: '#ffc107',
  error: '#dc3545',
  critical: '#721c24'
};

// Status badge colors
const statusColors = {
  passed: '#28a745',
  failed: '#dc3545',
  running: '#17a2b8',
  pending: '#6c757d',
  aborted: '#ffc107'
};

// Log entry component
const LogEntry = ({ log }) => {
  const [expanded, setExpanded] = useState(false);
  
  const toggleExpand = () => setExpanded(!expanded);
  
  return (
    <div style={styles.card}>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <div>
          <span style={{ ...styles.badge, backgroundColor: levelColors[log.log_level] || '#6c757d', marginRight: '10px' }}>
            {log.log_level.toUpperCase()}
          </span>
          <strong>{log.component}</strong> - {log.message}
        </div>
        <div>
          <span style={{ color: '#6c757d', fontSize: '0.9em', marginRight: '10px' }}>
            {new Date(log.timestamp).toLocaleString()}
          </span>
          <button 
            onClick={toggleExpand} 
            style={{ border: 'none', background: 'none', cursor: 'pointer' }}
          >
            {expanded ? '▼' : '▶'}
          </button>
        </div>
      </div>
      
      {expanded && (
        <div style={{ marginTop: '10px' }}>
          <div style={{ marginBottom: '5px' }}>
            <strong>Correlation ID:</strong> {log.correlation_id}
          </div>
          <div style={{ marginBottom: '5px' }}>
            <strong>Session ID:</strong> {log.session_id}
          </div>
          {log.user_id && (
            <div style={{ marginBottom: '5px' }}>
              <strong>User ID:</strong> {log.user_id}
            </div>
          )}
          {log.metadata && Object.keys(log.metadata).length > 0 && (
            <div>
              <strong>Metadata:</strong>
              <pre style={styles.pre}>
                {JSON.stringify(log.metadata, null, 2)}
              </pre>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Test scenario component
const TestScenario = ({ scenario, onRunTest }) => {
  const [expanded, setExpanded] = useState(false);
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  
  const toggleExpand = () => {
    setExpanded(!expanded);
    if (!expanded && results.length === 0) {
      loadResults();
    }
  };
  
  const loadResults = async () => {
    setLoading(true);
    try {
      const data = await testRunner.fetchResults(scenario.id);
      setResults(data);
    } catch (error) {
      logger.error(`Failed to load results for scenario ${scenario.id}`, 'LoggingDashboard', { error: error.message });
    } finally {
      setLoading(false);
    }
  };
  
  const handleRunTest = () => {
    onRunTest(scenario.id);
  };
  
  return (
    <div style={styles.card}>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <div>
          {scenario.status && (
            <span style={{ 
              ...styles.badge, 
              backgroundColor: statusColors[scenario.status] || '#6c757d', 
              marginRight: '10px' 
            }}>
              {scenario.status.toUpperCase()}
            </span>
          )}
          <strong>{scenario.name}</strong>
        </div>
        <div>
          <button 
            onClick={handleRunTest}
            style={styles.successButton}
          >
            Run Test
          </button>
          <button 
            onClick={toggleExpand} 
            style={{ border: 'none', background: 'none', cursor: 'pointer', marginLeft: '10px' }}
          >
            {expanded ? '▼' : '▶'}
          </button>
        </div>
      </div>
      
      {expanded && (
        <div style={{ marginTop: '10px' }}>
          <div style={{ marginBottom: '10px' }}>
            <strong>Description:</strong> {scenario.description}
          </div>
          
          {scenario.last_run && (
            <div style={{ marginBottom: '10px' }}>
              <strong>Last Run:</strong> {new Date(scenario.last_run).toLocaleString()}
            </div>
          )}
          
          <div style={{ marginBottom: '10px' }}>
            <strong>Test Results:</strong>
            {loading ? (
              <p>Loading results...</p>
            ) : results.length === 0 ? (
              <p>No test results found</p>
            ) : (
              <table style={styles.table}>
                <thead>
                  <tr>
                    <th style={styles.th}>Status</th>
                    <th style={styles.th}>Run Date</th>
                    <th style={styles.th}>Duration</th>
                    <th style={styles.th}>Details</th>
                  </tr>
                </thead>
                <tbody>
                  {results.map(result => (
                    <tr key={result.id}>
                      <td style={styles.td}>
                        <span style={{ 
                          ...styles.badge, 
                          backgroundColor: statusColors[result.status] || '#6c757d'
                        }}>
                          {result.status.toUpperCase()}
                        </span>
                      </td>
                      <td style={styles.td}>{new Date(result.run_timestamp).toLocaleString()}</td>
                      <td style={styles.td}>{result.duration.toFixed(2)}s</td>
                      <td style={styles.td}>
                        <button
                          style={styles.secondaryButton}
                          onClick={() => alert(JSON.stringify(result.result_data, null, 2))}
                        >
                          View Details
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
          
          <div>
            <strong>Scenario Data:</strong>
            <pre style={styles.pre}>
              {JSON.stringify(scenario.scenario_data, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
};

// Performance metrics chart (simplified)
const MetricsChart = ({ metrics }) => {
  if (!metrics || metrics.length === 0) {
    return <div style={styles.card}>No metrics data available</div>;
  }
  
  // Group metrics by type
  const metricsByType = {};
  metrics.forEach(metric => {
    if (!metricsByType[metric.metric_type]) {
      metricsByType[metric.metric_type] = [];
    }
    metricsByType[metric.metric_type].push(metric);
  });
  
  return (
    <div>
      {Object.keys(metricsByType).map(type => (
        <div key={type} style={styles.card}>
          <h3>{type}</h3>
          <div style={{ overflowX: 'auto' }}>
            <table style={styles.table}>
              <thead>
                <tr>
                  <th style={styles.th}>Timestamp</th>
                  <th style={styles.th}>Component</th>
                  <th style={styles.th}>Value</th>
                  <th style={styles.th}>Unit</th>
                </tr>
              </thead>
              <tbody>
                {metricsByType[type].map(metric => (
                  <tr key={metric.id}>
                    <td style={styles.td}>{new Date(metric.timestamp).toLocaleString()}</td>
                    <td style={styles.td}>{metric.component}</td>
                    <td style={styles.td}>{metric.value}</td>
                    <td style={styles.td}>{metric.unit}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ))}
    </div>
  );
};

// Test results detail component
const TestResultDetail = ({ result, onClose }) => {
  if (!result) return null;
  
  return (
    <div style={{ 
      position: 'fixed', 
      top: 0, 
      left: 0, 
      right: 0, 
      bottom: 0, 
      backgroundColor: 'rgba(0,0,0,0.5)', 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center',
      zIndex: 1000
    }}>
      <div style={{ 
        backgroundColor: 'white', 
        borderRadius: '4px', 
        padding: '20px', 
        maxWidth: '800px', 
        width: '90%',
        maxHeight: '90vh',
        overflow: 'auto'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '20px' }}>
          <h2>Test Result Details</h2>
          <button onClick={onClose} style={{ border: 'none', background: 'none', fontSize: '20px', cursor: 'pointer' }}>
            &times;
          </button>
        </div>
        
        <div style={{ marginBottom: '15px' }}>
          <strong>Status:</strong>
          <span style={{ 
            ...styles.badge, 
            backgroundColor: statusColors[result.status] || '#6c757d',
            marginLeft: '10px'
          }}>
            {result.status.toUpperCase()}
          </span>
        </div>
        
        <div style={{ marginBottom: '15px' }}>
          <strong>Duration:</strong> {result.duration.toFixed(2)}s
        </div>
        
        <div style={{ marginBottom: '15px' }}>
          <strong>Run Date:</strong> {new Date(result.run_timestamp).toLocaleString()}
        </div>
        
        <div style={{ marginBottom: '15px' }}>
          <h3>Test Steps</h3>
          {result.result_data && result.result_data.steps ? (
            <table style={styles.table}>
              <thead>
                <tr>
                  <th style={styles.th}>#</th>
                  <th style={styles.th}>Name</th>
                  <th style={styles.th}>Action</th>
                  <th style={styles.th}>Status</th>
                  <th style={styles.th}>Error</th>
                </tr>
              </thead>
              <tbody>
                {result.result_data.steps.map((step, index) => (
                  <tr key={index}>
                    <td style={styles.td}>{step.step}</td>
                    <td style={styles.td}>{step.name}</td>
                    <td style={styles.td}>{step.action}</td>
                    <td style={styles.td}>
                      <span style={{ 
                        ...styles.badge, 
                        backgroundColor: statusColors[step.status] || '#6c757d'
                      }}>
                        {step.status.toUpperCase()}
                      </span>
                    </td>
                    <td style={styles.td}>{step.error || '-'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <p>No step data available</p>
          )}
        </div>
        
        {result.result_data && result.result_data.message && (
          <div style={{ marginBottom: '15px' }}>
            <strong>Message:</strong> {result.result_data.message}
          </div>
        )}
        
        <div>
          <strong>Raw Data:</strong>
          <pre style={styles.pre}>
            {JSON.stringify(result.result_data, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

// Main dashboard component
const LoggingDashboard = () => {
  // Tab state
  const [activeTab, setActiveTab] = useState('logs');
  
  // Data state
  const [logs, setLogs] = useState([]);
  const [scenarios, setScenarios] = useState([]);
  const [metrics, setMetrics] = useState([]);
  
  // UI state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedResult, setSelectedResult] = useState(null);
  const [testStatus, setTestStatus] = useState(null);
  
  // Filter state
  const [logFilters, setLogFilters] = useState({
    level: '',
    component: '',
    search: '',
    correlationId: ''
  });
  
  // Load initial data
  useEffect(() => {
    if (activeTab === 'logs') {
      fetchLogs();
    } else if (activeTab === 'tests') {
      fetchScenarios();
    } else if (activeTab === 'metrics') {
      fetchMetrics();
    }
  }, [activeTab]);
  
  // Fetch logs from API
  const fetchLogs = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Build query parameters
      const params = {};
      if (logFilters.level) params.level = logFilters.level;
      if (logFilters.component) params.component = logFilters.component;
      if (logFilters.search) params.search = logFilters.search;
      if (logFilters.correlationId) params.correlation_id = logFilters.correlationId;
      
      const response = await axios.get('/api/logs', { params });
      setLogs(response.data);
    } catch (error) {
      setError('Failed to fetch logs');
      logger.error('Failed to fetch logs', 'LoggingDashboard', { error: error.message });
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch test scenarios
  const fetchScenarios = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await testRunner.fetchScenarios();
      setScenarios(data);
    } catch (error) {
      setError('Failed to fetch test scenarios');
      logger.error('Failed to fetch test scenarios', 'LoggingDashboard', { error: error.message });
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch performance metrics
  const fetchMetrics = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await axios.get('/api/logs/metrics');
      setMetrics(response.data);
    } catch (error) {
      setError('Failed to fetch metrics');
      logger.error('Failed to fetch metrics', 'LoggingDashboard', { error: error.message });
    } finally {
      setLoading(false);
    }
  };
  
  // Handle running a test
  const handleRunTest = async (scenarioId) => {
    if (testRunner.isRunning()) {
      alert('A test is already running');
      return;
    }
    
    setTestStatus({ status: 'running', message: 'Starting test...' });
    
    try {
      await testRunner.runTest(scenarioId, (status) => {
        setTestStatus(status);
        
        // If test completed, refresh scenarios
        if (status.status === 'completed' || status.status === 'error') {
          setTimeout(() => {
            fetchScenarios();
          }, 1000);
        }
      });
    } catch (error) {
      setTestStatus({ status: 'error', message: `Error: ${error.message}` });
      logger.error('Failed to run test', 'LoggingDashboard', { error: error.message, scenarioId });
    }
  };
  
  // Handle filter changes
  const handleFilterChange = (e) => {
    setLogFilters({
      ...logFilters,
      [e.target.name]: e.target.value
    });
  };
  
  // Apply filters
  const applyFilters = () => {
    fetchLogs();
  };
  
  // Reset filters
  const resetFilters = () => {
    setLogFilters({
      level: '',
      component: '',
      search: '',
      correlationId: ''
    });
    
    // Fetch logs without filters
    setLogFilters({
      level: '',
      component: '',
      search: '',
      correlationId: ''
    }, fetchLogs);
  };
  
  // Create sample test
  const createSampleTest = async () => {
    try {
      const steps = [
        testRunner.buildNavigateStep('/dashboard', 'Go to dashboard'),
        testRunner.buildWaitStep(1000, 'Wait for page to load'),
        testRunner.buildClickStep('#add-button', 'Click add button'),
        testRunner.buildInputStep('#name-input', 'Test Item', 'Enter item name'),
        testRunner.buildClickStep('#save-button', 'Save item'),
        testRunner.buildAssertStep('contains(#items-list, "Test Item")', 'Verify item was added')
      ];
      
      await testRunner.createUITestScenario(
        'Sample Dashboard Test',
        'A sample test that adds an item to the dashboard',
        steps
      );
      
      alert('Sample test created successfully');
      fetchScenarios();
    } catch (error) {
      logger.error('Failed to create sample test', 'LoggingDashboard', { error: error.message });
      alert(`Failed to create sample test: ${error.message}`);
    }
  };
  
  return (
    <div style={styles.container}>
      <div style={styles.header}>
        <h1>Logging & Testing Dashboard</h1>
        
        {activeTab === 'tests' && (
          <button onClick={createSampleTest} style={styles.successButton}>
            Create Sample Test
          </button>
        )}
      </div>
      
      {/* Tabs */}
      <div style={styles.tabs}>
        <div 
          style={activeTab === 'logs' ? styles.activeTab : styles.tab} 
          onClick={() => setActiveTab('logs')}
        >
          Logs
        </div>
        <div 
          style={activeTab === 'tests' ? styles.activeTab : styles.tab} 
          onClick={() => setActiveTab('tests')}
        >
          Test Scenarios
        </div>
        <div 
          style={activeTab === 'metrics' ? styles.activeTab : styles.tab} 
          onClick={() => setActiveTab('metrics')}
        >
          Performance Metrics
        </div>
      </div>
      
      {/* Error message */}
      {error && (
        <div style={{ 
          padding: '10px', 
          backgroundColor: '#f8d7da', 
          color: '#721c24', 
          borderRadius: '4px', 
          marginBottom: '20px' 
        }}>
          {error}
        </div>
      )}
      
      {/* Test status */}
      {testStatus && (
        <div style={{ 
          padding: '10px', 
          backgroundColor: testStatus.status === 'error' ? '#f8d7da' : '#d4edda', 
          color: testStatus.status === 'error' ? '#721c24' : '#155724', 
          borderRadius: '4px', 
          marginBottom: '20px' 
        }}>
          <strong>Test Status:</strong> {testStatus.message}
          
          {testStatus.status === 'running' && (
            <button 
              onClick={() => testRunner.abortTest()} 
              style={{ ...styles.dangerButton, marginLeft: '10px' }}
            >
              Abort Test
            </button>
          )}
        </div>
      )}
      
      {/* Logs tab content */}
      {activeTab === 'logs' && (
        <div>
          <div style={styles.filters}>
            <select 
              name="level" 
              value={logFilters.level}
              onChange={handleFilterChange}
              style={styles.select}
            >
              <option value="">All Levels</option>
              <option value="debug">Debug</option>
              <option value="info">Info</option>
              <option value="warning">Warning</option>
              <option value="error">Error</option>
              <option value="critical">Critical</option>
            </select>
            
            <input 
              type="text"
              name="component"
              placeholder="Component"
              value={logFilters.component}
              onChange={handleFilterChange}
              style={styles.input}
            />
            
            <input 
              type="text"
              name="search"
              placeholder="Search logs..."
              value={logFilters.search}
              onChange={handleFilterChange}
              style={styles.input}
            />
            
            <input 
              type="text"
              name="correlationId"
              placeholder="Correlation ID"
              value={logFilters.correlationId}
              onChange={handleFilterChange}
              style={styles.input}
            />
            
            <button onClick={applyFilters} style={styles.button}>
              Apply Filters
            </button>
            
            <button onClick={resetFilters} style={styles.secondaryButton}>
              Reset
            </button>
          </div>
          
          {loading ? (
            <p>Loading logs...</p>
          ) : logs.length === 0 ? (
            <p>No logs found</p>
          ) : (
            <div>
              {logs.map(log => (
                <LogEntry key={log.id} log={log} />
              ))}
            </div>
          )}
        </div>
      )}
      
      {/* Test scenarios tab content */}
      {activeTab === 'tests' && (
        <div>
          {loading ? (
            <p>Loading test scenarios...</p>
          ) : scenarios.length === 0 ? (
            <p>No test scenarios found</p>
          ) : (
            <div>
              {scenarios.map(scenario => (
                <TestScenario key={scenario.id} scenario={scenario} onRunTest={handleRunTest} />
              ))}
            </div>
          )}
          
          {selectedResult && (
            <TestResultDetail result={selectedResult} onClose={() => setSelectedResult(null)} />
          )}
        </div>
      )}
      
      {/* Metrics tab content */}
      {activeTab === 'metrics' && (
        <div>
          {loading ? (
            <p>Loading metrics...</p>
          ) : (
            <MetricsChart metrics={metrics} />
          )}
        </div>
      )}
    </div>
  );
};

export default LoggingDashboard; 