#!/usr/bin/env python3
"""
<PERSON>ript to standardize docstrings in the codebase.

This script analyzes Python files and standardizes docstrings to follow
a consistent format (Google style by default).
"""

import os
import sys
import re
import argparse
import ast
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union


def install_dependencies():
    """Install required dependencies if not already installed."""
    try:
        import docstring_parser
    except ImportError:
        print("Installing required dependencies...")
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "docstring-parser"])


class DocstringStandardizer:
    """Class to standardize docstrings in Python files."""

    def __init__(self, style="google"):
        """
        Initialize the DocstringStandardizer.
        
        Args:
            style: The docstring style to use (google, numpy, or sphinx)
        """
        self.style = style
        self.import_docstring_parser()
        
    def import_docstring_parser(self):
        """Import the docstring_parser module."""
        import docstring_parser
        self.docstring_parser = docstring_parser
        
    def parse_docstring(self, docstring: str) -> Dict:
        """
        Parse a docstring into its components.
        
        Args:
            docstring: The docstring to parse
            
        Returns:
            Dict containing the parsed docstring components
        """
        if self.style == "google":
            parsed = self.docstring_parser.parse(docstring, style=self.docstring_parser.DocstringStyle.GOOGLE)
        elif self.style == "numpy":
            parsed = self.docstring_parser.parse(docstring, style=self.docstring_parser.DocstringStyle.NUMPYDOC)
        elif self.style == "sphinx":
            parsed = self.docstring_parser.parse(docstring, style=self.docstring_parser.DocstringStyle.SPHINX)
        else:
            raise ValueError(f"Unsupported style: {self.style}")
            
        return {
            "short_description": parsed.short_description,
            "long_description": parsed.long_description,
            "params": [(p.arg_name, p.type_name, p.description) for p in parsed.params],
            "returns": [(r.type_name, r.description) for r in parsed.returns],
            "raises": [(e.type_name, e.description) for e in parsed.raises],
            "examples": parsed.examples,
            "deprecation": parsed.deprecation,
        }
        
    def format_docstring(self, parsed: Dict) -> str:
        """
        Format a parsed docstring according to the specified style.
        
        Args:
            parsed: The parsed docstring components
            
        Returns:
            Formatted docstring string
        """
        if self.style == "google":
            return self.format_google_style(parsed)
        elif self.style == "numpy":
            return self.format_numpy_style(parsed)
        elif self.style == "sphinx":
            return self.format_sphinx_style(parsed)
        else:
            raise ValueError(f"Unsupported style: {self.style}")
            
    def format_google_style(self, parsed: Dict) -> str:
        """
        Format a docstring in Google style.
        
        Args:
            parsed: The parsed docstring components
            
        Returns:
            Formatted docstring string
        """
        lines = []
        
        # Short description
        if parsed["short_description"]:
            lines.append(parsed["short_description"])
            
        # Add blank line if we have both short and long descriptions
        if parsed["short_description"] and parsed["long_description"]:
            lines.append("")
            
        # Long description
        if parsed["long_description"]:
            lines.append(parsed["long_description"])
            
        # Add blank line before sections if we have any content
        if (parsed["short_description"] or parsed["long_description"]) and (
            parsed["params"] or parsed["returns"] or parsed["raises"] or parsed["examples"]
        ):
            lines.append("")
            
        # Parameters
        if parsed["params"]:
            lines.append("Args:")
            for name, type_name, description in parsed["params"]:
                if type_name:
                    lines.append(f"    {name}: {type_name}, {description}")
                else:
                    lines.append(f"    {name}: {description}")
                    
        # Returns
        if parsed["returns"]:
            if parsed["params"]:
                lines.append("")
            lines.append("Returns:")
            for type_name, description in parsed["returns"]:
                if type_name:
                    lines.append(f"    {type_name}: {description}")
                else:
                    lines.append(f"    {description}")
                    
        # Raises
        if parsed["raises"]:
            if parsed["params"] or parsed["returns"]:
                lines.append("")
            lines.append("Raises:")
            for type_name, description in parsed["raises"]:
                if type_name:
                    lines.append(f"    {type_name}: {description}")
                else:
                    lines.append(f"    {description}")
                    
        # Examples
        if parsed["examples"]:
            if parsed["params"] or parsed["returns"] or parsed["raises"]:
                lines.append("")
            lines.append("Examples:")
            for example in parsed["examples"]:
                lines.append(f"    {example}")
                
        # Deprecation
        if parsed["deprecation"]:
            if parsed["params"] or parsed["returns"] or parsed["raises"] or parsed["examples"]:
                lines.append("")
            lines.append(f"Deprecated: {parsed['deprecation']}")
            
        return "\n".join(lines)
        
    def format_numpy_style(self, parsed: Dict) -> str:
        """
        Format a docstring in NumPy style.
        
        Args:
            parsed: The parsed docstring components
            
        Returns:
            Formatted docstring string
        """
        # Implementation for NumPy style
        # This is a simplified version
        lines = []
        
        # Short description
        if parsed["short_description"]:
            lines.append(parsed["short_description"])
            
        # Add blank line if we have both short and long descriptions
        if parsed["short_description"] and parsed["long_description"]:
            lines.append("")
            
        # Long description
        if parsed["long_description"]:
            lines.append(parsed["long_description"])
            
        # Add blank line before sections if we have any content
        if (parsed["short_description"] or parsed["long_description"]) and (
            parsed["params"] or parsed["returns"] or parsed["raises"] or parsed["examples"]
        ):
            lines.append("")
            
        # Parameters
        if parsed["params"]:
            lines.append("Parameters")
            lines.append("----------")
            for name, type_name, description in parsed["params"]:
                if type_name:
                    lines.append(f"{name} : {type_name}")
                else:
                    lines.append(f"{name}")
                lines.append(f"    {description}")
                
        # Returns
        if parsed["returns"]:
            if parsed["params"]:
                lines.append("")
            lines.append("Returns")
            lines.append("-------")
            for type_name, description in parsed["returns"]:
                if type_name:
                    lines.append(f"{type_name}")
                lines.append(f"    {description}")
                
        # Raises
        if parsed["raises"]:
            if parsed["params"] or parsed["returns"]:
                lines.append("")
            lines.append("Raises")
            lines.append("------")
            for type_name, description in parsed["raises"]:
                if type_name:
                    lines.append(f"{type_name}")
                lines.append(f"    {description}")
                
        # Examples
        if parsed["examples"]:
            if parsed["params"] or parsed["returns"] or parsed["raises"]:
                lines.append("")
            lines.append("Examples")
            lines.append("--------")
            for example in parsed["examples"]:
                lines.append(f"{example}")
                
        return "\n".join(lines)
        
    def format_sphinx_style(self, parsed: Dict) -> str:
        """
        Format a docstring in Sphinx style.
        
        Args:
            parsed: The parsed docstring components
            
        Returns:
            Formatted docstring string
        """
        # Implementation for Sphinx style
        # This is a simplified version
        lines = []
        
        # Short description
        if parsed["short_description"]:
            lines.append(parsed["short_description"])
            
        # Add blank line if we have both short and long descriptions
        if parsed["short_description"] and parsed["long_description"]:
            lines.append("")
            
        # Long description
        if parsed["long_description"]:
            lines.append(parsed["long_description"])
            
        # Add blank line before sections if we have any content
        if (parsed["short_description"] or parsed["long_description"]) and (
            parsed["params"] or parsed["returns"] or parsed["raises"] or parsed["examples"]
        ):
            lines.append("")
            
        # Parameters
        for name, type_name, description in parsed["params"]:
            if type_name:
                lines.append(f":param {name}: {description}")
                lines.append(f":type {name}: {type_name}")
            else:
                lines.append(f":param {name}: {description}")
                
        # Returns
        for type_name, description in parsed["returns"]:
            lines.append(f":return: {description}")
            if type_name:
                lines.append(f":rtype: {type_name}")
                
        # Raises
        for type_name, description in parsed["raises"]:
            if type_name:
                lines.append(f":raises {type_name}: {description}")
            else:
                lines.append(f":raises: {description}")
                
        return "\n".join(lines)
        
    def standardize_file(self, file_path: str, dry_run: bool = False) -> Tuple[int, int]:
        """
        Standardize docstrings in a Python file.
        
        Args:
            file_path: Path to the Python file
            dry_run: If True, don't modify the file
            
        Returns:
            Tuple of (number of docstrings found, number of docstrings modified)
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        tree = ast.parse(content)
        docstrings_found = 0
        docstrings_modified = 0
        
        # Process module docstring
        if (
            len(tree.body) > 0 and 
            isinstance(tree.body[0], ast.Expr) and 
            isinstance(tree.body[0].value, ast.Str)
        ):
            docstrings_found += 1
            module_docstring = tree.body[0].value.s
            try:
                parsed = self.parse_docstring(module_docstring)
                new_docstring = self.format_docstring(parsed)
                if new_docstring != module_docstring:
                    docstrings_modified += 1
                    if not dry_run:
                        content = content.replace(
                            f'"""{module_docstring}"""', 
                            f'"""{new_docstring}"""', 
                            1
                        )
            except Exception as e:
                print(f"Error processing module docstring in {file_path}: {e}")
                
        # Process function and class docstrings
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.ClassDef, ast.AsyncFunctionDef)):
                if (
                    node.body and 
                    isinstance(node.body[0], ast.Expr) and 
                    isinstance(node.body[0].value, ast.Str)
                ):
                    docstrings_found += 1
                    docstring = node.body[0].value.s
                    try:
                        parsed = self.parse_docstring(docstring)
                        new_docstring = self.format_docstring(parsed)
                        if new_docstring != docstring:
                            docstrings_modified += 1
                            if not dry_run:
                                content = content.replace(
                                    f'"""{docstring}"""', 
                                    f'"""{new_docstring}"""', 
                                    1
                                )
                    except Exception as e:
                        print(f"Error processing docstring for {node.name} in {file_path}: {e}")
                        
        if not dry_run and docstrings_modified > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
                
        return docstrings_found, docstrings_modified
        
    def standardize_directory(self, directory: str, dry_run: bool = False) -> Tuple[int, int, int]:
        """
        Standardize docstrings in all Python files in a directory.
        
        Args:
            directory: Directory to process
            dry_run: If True, don't modify any files
            
        Returns:
            Tuple of (number of files processed, number of docstrings found, number of docstrings modified)
        """
        files_processed = 0
        total_docstrings_found = 0
        total_docstrings_modified = 0
        
        for root, _, files in os.walk(directory):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    files_processed += 1
                    docstrings_found, docstrings_modified = self.standardize_file(file_path, dry_run)
                    total_docstrings_found += docstrings_found
                    total_docstrings_modified += docstrings_modified
                    
        return files_processed, total_docstrings_found, total_docstrings_modified


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Standardize docstrings in Python code")
    parser.add_argument("--path", help="Path to file or directory to process")
    parser.add_argument("--style", choices=["google", "numpy", "sphinx"], default="google", help="Docstring style to use")
    parser.add_argument("--dry-run", action="store_true", help="Don't modify files, just report what would be changed")
    
    args = parser.parse_args()
    
    # Install dependencies if needed
    install_dependencies()
    
    standardizer = DocstringStandardizer(style=args.style)
    
    if args.path:
        path = args.path
    else:
        path = "api"  # Default to the api directory
        
    if os.path.isfile(path):
        docstrings_found, docstrings_modified = standardizer.standardize_file(path, args.dry_run)
        print(f"Processed 1 file")
        print(f"Found {docstrings_found} docstrings")
        print(f"Modified {docstrings_modified} docstrings")
    elif os.path.isdir(path):
        files_processed, docstrings_found, docstrings_modified = standardizer.standardize_directory(path, args.dry_run)
        print(f"Processed {files_processed} files")
        print(f"Found {docstrings_found} docstrings")
        print(f"Modified {docstrings_modified} docstrings")
    else:
        print(f"Path not found: {path}")
        return 1
        
    return 0


if __name__ == "__main__":
    sys.exit(main())
