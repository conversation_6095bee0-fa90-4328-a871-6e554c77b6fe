# Code Cleanup Tools

This directory contains tools to help with the Code Cleanup Initiative. These tools are designed to analyze the codebase, identify issues, and generate reports to guide the cleanup process.

## Available Tools

### Circular Dependency Analysis

The `analyze_circular_dependencies.py` script identifies circular dependencies in the codebase, which can lead to import errors, maintenance challenges, and code that's difficult to test.

**Usage:**

```bash
# Analyze all modules
python tools/analyze_circular_dependencies.py --all

# Analyze a specific module
python tools/analyze_circular_dependencies.py --module api/routes

# Limit analysis depth
python tools/analyze_circular_dependencies.py --depth 5

# Specify output directory
python tools/analyze_circular_dependencies.py --output-dir reports/dependencies
```

**Output:**

- SVG dependency graphs
- Text files with detailed dependency information
- JSON report with summary of circular dependencies

### Test Coverage Analysis

The `analyze_test_coverage.py` script runs the test suite with coverage analysis to identify areas of the codebase that need additional test coverage.

**Usage:**

```bash
# Analyze all modules
python tools/analyze_test_coverage.py

# Analyze specific modules
python tools/analyze_test_coverage.py --modules api/routes api/models

# Skip HTML report generation
python tools/analyze_test_coverage.py --no-html

# Set coverage threshold
python tools/analyze_test_coverage.py --threshold 90

# Specify output directory
python tools/analyze_test_coverage.py --output-dir reports/coverage
```

**Output:**

- Text file with coverage output
- HTML report with detailed coverage information
- XML report for CI/CD integration
- JSON file with parsed coverage data
- Markdown summary report

## Adding New Tools

When adding new tools to this directory, please follow these guidelines:

1. Include a clear docstring explaining the purpose of the tool
2. Add command-line arguments with help text
3. Generate structured output (JSON, CSV, etc.) for further processing
4. Create human-readable reports (Markdown, HTML, etc.)
5. Update this README with usage instructions

## Integration with CI/CD

These tools can be integrated into the CI/CD pipeline to automatically analyze the codebase and generate reports. Example GitHub Actions workflow:

```yaml
name: Code Quality Analysis

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  analyze:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest pytest-cov coverage pydeps
          
      - name: Analyze circular dependencies
        run: python tools/analyze_circular_dependencies.py --all
        
      - name: Analyze test coverage
        run: python tools/analyze_test_coverage.py
        
      - name: Upload reports
        uses: actions/upload-artifact@v3
        with:
          name: analysis-reports
          path: |
            dependency_analysis/
            coverage_reports/
            *.md
            *.json
```
