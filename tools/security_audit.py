#!/usr/bin/env python3
"""
<PERSON><PERSON>t to perform a security audit of the codebase.

This script analyzes the codebase for common security vulnerabilities such as
SQL injection, XSS, CSRF, insecure authentication, and more.
"""

import os
import sys
import re
import ast
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple
import json
from datetime import datetime


class SecurityAuditor:
    """Class to perform security audit of the codebase."""

    def __init__(self):
        """Initialize the SecurityAuditor."""
        self.vulnerabilities = []
        self.stats = {
            "files_analyzed": 0,
            "sql_injection": 0,
            "xss": 0,
            "csrf": 0,
            "insecure_auth": 0,
            "hardcoded_secrets": 0,
            "insecure_deserialization": 0,
            "total_vulnerabilities": 0,
        }
        
    def audit_file(self, file_path: str) -> List[Dict]:
        """
        Audit a Python file for security vulnerabilities.
        
        Args:
            file_path: Path to the Python file
            
        Returns:
            List of dictionaries containing vulnerability information
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            self.stats["files_analyzed"] += 1
            
            # Parse the file with AST
            tree = ast.parse(content)
            
            # Find vulnerabilities
            vulnerabilities = []
            
            # Check for SQL injection
            sql_injections = self._find_sql_injection(tree, content)
            vulnerabilities.extend(sql_injections)
            self.stats["sql_injection"] += len(sql_injections)
            
            # Check for XSS
            xss_vulnerabilities = self._find_xss(tree, content)
            vulnerabilities.extend(xss_vulnerabilities)
            self.stats["xss"] += len(xss_vulnerabilities)
            
            # Check for CSRF
            csrf_vulnerabilities = self._find_csrf(tree, content)
            vulnerabilities.extend(csrf_vulnerabilities)
            self.stats["csrf"] += len(csrf_vulnerabilities)
            
            # Check for insecure authentication
            auth_vulnerabilities = self._find_insecure_auth(tree, content)
            vulnerabilities.extend(auth_vulnerabilities)
            self.stats["insecure_auth"] += len(auth_vulnerabilities)
            
            # Check for hardcoded secrets
            secret_vulnerabilities = self._find_hardcoded_secrets(tree, content)
            vulnerabilities.extend(secret_vulnerabilities)
            self.stats["hardcoded_secrets"] += len(secret_vulnerabilities)
            
            # Check for insecure deserialization
            deser_vulnerabilities = self._find_insecure_deserialization(tree, content)
            vulnerabilities.extend(deser_vulnerabilities)
            self.stats["insecure_deserialization"] += len(deser_vulnerabilities)
            
            # Add file path to vulnerabilities
            for vuln in vulnerabilities:
                vuln["file"] = file_path
            
            self.vulnerabilities.extend(vulnerabilities)
            self.stats["total_vulnerabilities"] += len(vulnerabilities)
            
            return vulnerabilities
            
        except Exception as e:
            print(f"Error auditing {file_path}: {e}")
            return []
    
    def _find_sql_injection(self, tree: ast.AST, content: str) -> List[Dict]:
        """
        Find potential SQL injection vulnerabilities.
        
        Args:
            tree: AST of the file
            content: Content of the file
            
        Returns:
            List of dictionaries containing vulnerability information
        """
        vulnerabilities = []
        
        for node in ast.walk(tree):
            # Look for string formatting in SQL queries
            if isinstance(node, ast.BinOp) and isinstance(node.op, ast.Mod):
                if isinstance(node.left, ast.Str) and self._is_sql_query(node.left.s):
                    vulnerabilities.append({
                        "type": "sql_injection",
                        "line": node.lineno,
                        "code": ast.unparse(node),
                        "severity": "high",
                        "description": "Potential SQL injection vulnerability using string formatting",
                        "recommendation": "Use parameterized queries or ORM instead of string formatting"
                    })
            
            # Look for string concatenation in SQL queries
            elif isinstance(node, ast.BinOp) and isinstance(node.op, ast.Add):
                if (isinstance(node.left, ast.Str) and self._is_sql_query(node.left.s)) or \
                   (isinstance(node.right, ast.Str) and self._is_sql_query(node.right.s)):
                    vulnerabilities.append({
                        "type": "sql_injection",
                        "line": node.lineno,
                        "code": ast.unparse(node),
                        "severity": "high",
                        "description": "Potential SQL injection vulnerability using string concatenation",
                        "recommendation": "Use parameterized queries or ORM instead of string concatenation"
                    })
            
            # Look for f-strings in SQL queries
            elif isinstance(node, ast.JoinedStr) and any(isinstance(val, ast.FormattedValue) for val in node.values):
                node_str = ast.unparse(node)
                if self._is_sql_query(node_str):
                    vulnerabilities.append({
                        "type": "sql_injection",
                        "line": node.lineno,
                        "code": node_str,
                        "severity": "high",
                        "description": "Potential SQL injection vulnerability using f-strings",
                        "recommendation": "Use parameterized queries or ORM instead of f-strings"
                    })
        
        return vulnerabilities
    
    def _is_sql_query(self, text: str) -> bool:
        """
        Check if a string is likely an SQL query.
        
        Args:
            text: String to check
            
        Returns:
            True if the string is likely an SQL query
        """
        # Simple heuristic: contains SQL keywords
        sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'FROM', 'WHERE', 'JOIN']
        text_upper = text.upper()
        return any(keyword in text_upper for keyword in sql_keywords)
    
    def _find_xss(self, tree: ast.AST, content: str) -> List[Dict]:
        """
        Find potential XSS vulnerabilities.
        
        Args:
            tree: AST of the file
            content: Content of the file
            
        Returns:
            List of dictionaries containing vulnerability information
        """
        vulnerabilities = []
        
        # Look for unescaped variables in templates
        template_patterns = [
            r'<.*?>\s*\{\{\s*(.+?)\s*\}\}\s*</.*?>',  # Jinja2/Flask style
            r'<.*?>\s*<%=\s*(.+?)\s*%>\s*</.*?>',     # ERB style
            r'<.*?>\s*{\s*(.+?)\s*}\s*</.*?>',        # React/JSX style
        ]
        
        for pattern in template_patterns:
            for match in re.finditer(pattern, content):
                line_no = content[:match.start()].count('\n') + 1
                vulnerabilities.append({
                    "type": "xss",
                    "line": line_no,
                    "code": match.group(0),
                    "severity": "high",
                    "description": "Potential XSS vulnerability with unescaped template variable",
                    "recommendation": "Use template escaping functions or sanitize user input"
                })
        
        # Look for direct HTML rendering of user input
        for node in ast.walk(tree):
            if isinstance(node, ast.Call) and hasattr(node, 'func'):
                if hasattr(node.func, 'attr') and node.func.attr in ['render', 'render_template', 'html']:
                    vulnerabilities.append({
                        "type": "xss",
                        "line": node.lineno,
                        "code": ast.unparse(node),
                        "severity": "medium",
                        "description": "Potential XSS vulnerability in template rendering",
                        "recommendation": "Ensure all variables are properly escaped in templates"
                    })
        
        return vulnerabilities
    
    def _find_csrf(self, tree: ast.AST, content: str) -> List[Dict]:
        """
        Find potential CSRF vulnerabilities.
        
        Args:
            tree: AST of the file
            content: Content of the file
            
        Returns:
            List of dictionaries containing vulnerability information
        """
        vulnerabilities = []
        
        # Look for form submissions without CSRF tokens
        form_patterns = [
            r'<form.*?>\s*(?!.*csrf).*?</form>',  # HTML forms without csrf
            r'@csrf_exempt',                       # Django CSRF exempt decorator
            r'csrf_exempt',                        # Flask WTF CSRF exempt
        ]
        
        for pattern in form_patterns:
            for match in re.finditer(pattern, content):
                line_no = content[:match.start()].count('\n') + 1
                vulnerabilities.append({
                    "type": "csrf",
                    "line": line_no,
                    "code": match.group(0),
                    "severity": "high",
                    "description": "Potential CSRF vulnerability with unprotected form",
                    "recommendation": "Add CSRF protection to all forms"
                })
        
        # Look for CSRF protection disabled
        for node in ast.walk(tree):
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    if hasattr(target, 'attr') and target.attr in ['WTF_CSRF_ENABLED', 'CSRF_ENABLED']:
                        if isinstance(node.value, ast.Constant) and node.value.value is False:
                            vulnerabilities.append({
                                "type": "csrf",
                                "line": node.lineno,
                                "code": ast.unparse(node),
                                "severity": "high",
                                "description": "CSRF protection disabled",
                                "recommendation": "Enable CSRF protection"
                            })
        
        return vulnerabilities
    
    def _find_insecure_auth(self, tree: ast.AST, content: str) -> List[Dict]:
        """
        Find potential insecure authentication vulnerabilities.
        
        Args:
            tree: AST of the file
            content: Content of the file
            
        Returns:
            List of dictionaries containing vulnerability information
        """
        vulnerabilities = []
        
        # Look for plaintext password storage
        for node in ast.walk(tree):
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    if hasattr(target, 'id') and 'password' in target.id.lower():
                        if not self._is_hashed_password(node.value, content):
                            vulnerabilities.append({
                                "type": "insecure_auth",
                                "line": node.lineno,
                                "code": ast.unparse(node),
                                "severity": "high",
                                "description": "Potential plaintext password storage",
                                "recommendation": "Use password hashing with salt"
                            })
        
        # Look for weak password hashing
        weak_hash_patterns = [
            r'md5\(', r'sha1\(', r'hashlib\.md5', r'hashlib\.sha1'
        ]
        
        for pattern in weak_hash_patterns:
            for match in re.finditer(pattern, content):
                line_no = content[:match.start()].count('\n') + 1
                vulnerabilities.append({
                    "type": "insecure_auth",
                    "line": line_no,
                    "code": match.group(0),
                    "severity": "high",
                    "description": "Weak password hashing algorithm",
                    "recommendation": "Use strong password hashing like bcrypt, Argon2, or PBKDF2"
                })
        
        return vulnerabilities
    
    def _is_hashed_password(self, node: ast.AST, content: str) -> bool:
        """
        Check if a password is likely hashed.
        
        Args:
            node: AST node of the password value
            content: Content of the file
            
        Returns:
            True if the password is likely hashed
        """
        # Check for common hashing functions
        if isinstance(node, ast.Call):
            if hasattr(node.func, 'id') and node.func.id in ['hash', 'hashpw', 'generate_password_hash']:
                return True
            if hasattr(node.func, 'attr') and node.func.attr in ['hash', 'hashpw', 'generate_password_hash']:
                return True
        
        return False
    
    def _find_hardcoded_secrets(self, tree: ast.AST, content: str) -> List[Dict]:
        """
        Find potential hardcoded secrets.
        
        Args:
            tree: AST of the file
            content: Content of the file
            
        Returns:
            List of dictionaries containing vulnerability information
        """
        vulnerabilities = []
        
        # Look for hardcoded API keys, tokens, passwords, etc.
        secret_patterns = [
            r'api[_-]?key\s*=\s*["\']([^"\']+)["\']',
            r'secret[_-]?key\s*=\s*["\']([^"\']+)["\']',
            r'password\s*=\s*["\']([^"\']+)["\']',
            r'token\s*=\s*["\']([^"\']+)["\']',
            r'auth[_-]?token\s*=\s*["\']([^"\']+)["\']',
        ]
        
        for pattern in secret_patterns:
            for match in re.finditer(pattern, content, re.IGNORECASE):
                # Skip if it's an environment variable
                if 'os.getenv' in match.group(0) or 'os.environ' in match.group(0):
                    continue
                
                line_no = content[:match.start()].count('\n') + 1
                vulnerabilities.append({
                    "type": "hardcoded_secrets",
                    "line": line_no,
                    "code": match.group(0),
                    "severity": "high",
                    "description": "Hardcoded secret detected",
                    "recommendation": "Use environment variables or a secure vault for secrets"
                })
        
        return vulnerabilities
    
    def _find_insecure_deserialization(self, tree: ast.AST, content: str) -> List[Dict]:
        """
        Find potential insecure deserialization vulnerabilities.
        
        Args:
            tree: AST of the file
            content: Content of the file
            
        Returns:
            List of dictionaries containing vulnerability information
        """
        vulnerabilities = []
        
        # Look for pickle, yaml.load, etc.
        for node in ast.walk(tree):
            if isinstance(node, ast.Call) and hasattr(node, 'func'):
                if hasattr(node.func, 'id') and node.func.id in ['pickle', 'loads', 'load']:
                    vulnerabilities.append({
                        "type": "insecure_deserialization",
                        "line": node.lineno,
                        "code": ast.unparse(node),
                        "severity": "high",
                        "description": "Potential insecure deserialization vulnerability",
                        "recommendation": "Use safer alternatives like JSON or yaml.safe_load"
                    })
                elif hasattr(node.func, 'attr') and node.func.attr in ['loads', 'load']:
                    if hasattr(node.func, 'value') and hasattr(node.func.value, 'id'):
                        if node.func.value.id in ['pickle', 'yaml']:
                            vulnerabilities.append({
                                "type": "insecure_deserialization",
                                "line": node.lineno,
                                "code": ast.unparse(node),
                                "severity": "high",
                                "description": "Potential insecure deserialization vulnerability",
                                "recommendation": "Use safer alternatives like JSON or yaml.safe_load"
                            })
        
        return vulnerabilities
    
    def audit_directory(self, directory: str) -> List[Dict]:
        """
        Audit all Python files in a directory for security vulnerabilities.
        
        Args:
            directory: Directory to audit
            
        Returns:
            List of dictionaries containing vulnerability information
        """
        for root, _, files in os.walk(directory):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    self.audit_file(file_path)
        
        return self.vulnerabilities
    
    def generate_report(self, output_file: str = "security_audit_report.md") -> None:
        """
        Generate a Markdown report of the audit results.
        
        Args:
            output_file: Path to the output file
        """
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# Security Audit Report\n\n")
            f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## Summary\n\n")
            f.write(f"- Files analyzed: {self.stats['files_analyzed']}\n")
            f.write(f"- Total vulnerabilities: {self.stats['total_vulnerabilities']}\n\n")
            
            f.write("## Vulnerabilities by Type\n\n")
            f.write(f"- SQL Injection: {self.stats['sql_injection']}\n")
            f.write(f"- Cross-Site Scripting (XSS): {self.stats['xss']}\n")
            f.write(f"- Cross-Site Request Forgery (CSRF): {self.stats['csrf']}\n")
            f.write(f"- Insecure Authentication: {self.stats['insecure_auth']}\n")
            f.write(f"- Hardcoded Secrets: {self.stats['hardcoded_secrets']}\n")
            f.write(f"- Insecure Deserialization: {self.stats['insecure_deserialization']}\n\n")
            
            # Group vulnerabilities by file
            vulns_by_file = {}
            for vuln in self.vulnerabilities:
                file_path = vuln["file"]
                if file_path not in vulns_by_file:
                    vulns_by_file[file_path] = []
                vulns_by_file[file_path].append(vuln)
            
            f.write("## Vulnerabilities by File\n\n")
            for file_path, vulns in vulns_by_file.items():
                f.write(f"### {file_path}\n\n")
                
                for vuln in vulns:
                    severity_marker = "🔴" if vuln["severity"] == "high" else "🟠" if vuln["severity"] == "medium" else "🟡"
                    f.write(f"#### {severity_marker} {vuln['type']} (Line {vuln['line']})\n\n")
                    f.write(f"**Severity:** {vuln['severity']}\n\n")
                    f.write(f"**Description:** {vuln['description']}\n\n")
                    f.write("**Code:**\n\n")
                    f.write("```python\n")
                    f.write(vuln['code'])
                    f.write("\n```\n\n")
                    f.write(f"**Recommendation:** {vuln['recommendation']}\n\n")
            
            f.write("## Remediation Recommendations\n\n")
            f.write("### SQL Injection\n\n")
            f.write("1. Use parameterized queries or prepared statements\n")
            f.write("2. Use an ORM (Object-Relational Mapping) library\n")
            f.write("3. Validate and sanitize all user inputs\n")
            f.write("4. Apply the principle of least privilege to database accounts\n\n")
            
            f.write("### Cross-Site Scripting (XSS)\n\n")
            f.write("1. Use template escaping functions\n")
            f.write("2. Implement Content Security Policy (CSP)\n")
            f.write("3. Sanitize user input\n")
            f.write("4. Use frameworks that automatically escape output\n\n")
            
            f.write("### Cross-Site Request Forgery (CSRF)\n\n")
            f.write("1. Implement anti-CSRF tokens\n")
            f.write("2. Use the SameSite cookie attribute\n")
            f.write("3. Verify the origin header\n")
            f.write("4. Implement proper session management\n\n")
            
            f.write("### Insecure Authentication\n\n")
            f.write("1. Use strong password hashing algorithms (bcrypt, Argon2, PBKDF2)\n")
            f.write("2. Implement multi-factor authentication\n")
            f.write("3. Use secure session management\n")
            f.write("4. Implement account lockout policies\n\n")
            
            f.write("### Hardcoded Secrets\n\n")
            f.write("1. Use environment variables for secrets\n")
            f.write("2. Use a secure vault or key management service\n")
            f.write("3. Implement proper secret rotation\n")
            f.write("4. Use configuration files that are not checked into version control\n\n")
            
            f.write("### Insecure Deserialization\n\n")
            f.write("1. Use safer alternatives like JSON\n")
            f.write("2. Validate and sanitize all inputs\n")
            f.write("3. Implement integrity checks\n")
            f.write("4. Use safe deserialization libraries\n")
    
    def save_json_report(self, output_file: str = "security_audit_report.json") -> None:
        """
        Save the audit results as JSON.
        
        Args:
            output_file: Path to the output file
        """
        report = {
            "timestamp": datetime.now().isoformat(),
            "stats": self.stats,
            "vulnerabilities": self.vulnerabilities,
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2)


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Perform a security audit of the codebase")
    parser.add_argument("--path", help="Path to file or directory to audit")
    parser.add_argument("--output", default="security_audit_report.md", help="Output report file")
    parser.add_argument("--json", action="store_true", help="Generate JSON report")
    
    args = parser.parse_args()
    
    auditor = SecurityAuditor()
    
    if args.path:
        path = args.path
    else:
        path = "api"  # Default to the api directory
        
    if os.path.isfile(path):
        auditor.audit_file(path)
    elif os.path.isdir(path):
        auditor.audit_directory(path)
    else:
        print(f"Path not found: {path}")
        return 1
    
    auditor.generate_report(args.output)
    print(f"Report saved to {args.output}")
    
    if args.json:
        json_output = os.path.splitext(args.output)[0] + ".json"
        auditor.save_json_report(json_output)
        print(f"JSON report saved to {json_output}")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
