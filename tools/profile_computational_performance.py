#!/usr/bin/env python3
"""
Script to profile computational performance of the codebase.

This script analyzes the codebase to identify computationally expensive
operations and functions that could benefit from optimization.
"""

import os
import sys
import re
import ast
import argparse
import time
import cProfile
import pstats
import io
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple
import json
from datetime import datetime


def install_dependencies():
    """Install required dependencies if not already installed."""
    try:
        import line_profiler
        import memory_profiler
    except ImportError:
        print("Installing required dependencies...")
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "line_profiler", "memory_profiler"])


class ComputationalProfiler:
    """Class to profile computational performance of the codebase."""

    def __init__(self):
        """Initialize the ComputationalProfiler."""
        self.hotspots = []
        self.stats = {
            "files_analyzed": 0,
            "functions_analyzed": 0,
            "time_complexity_issues": 0,
            "memory_usage_issues": 0,
            "cpu_intensive_operations": 0,
            "total_hotspots": 0,
        }
        
    def analyze_file(self, file_path: str) -> List[Dict]:
        """
        Analyze a Python file for computational hotspots.
        
        Args:
            file_path: Path to the Python file
            
        Returns:
            List of dictionaries containing hotspot information
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            self.stats["files_analyzed"] += 1
            
            # Parse the file with AST
            tree = ast.parse(content)
            
            # Find computational hotspots
            hotspots = []
            
            # Check for time complexity issues
            time_complexity_issues = self._find_time_complexity_issues(tree)
            hotspots.extend(time_complexity_issues)
            self.stats["time_complexity_issues"] += len(time_complexity_issues)
            
            # Check for memory usage issues
            memory_usage_issues = self._find_memory_usage_issues(tree)
            hotspots.extend(memory_usage_issues)
            self.stats["memory_usage_issues"] += len(memory_usage_issues)
            
            # Check for CPU intensive operations
            cpu_intensive_operations = self._find_cpu_intensive_operations(tree)
            hotspots.extend(cpu_intensive_operations)
            self.stats["cpu_intensive_operations"] += len(cpu_intensive_operations)
            
            # Add file path to hotspots
            for hotspot in hotspots:
                hotspot["file"] = file_path
            
            self.hotspots.extend(hotspots)
            self.stats["total_hotspots"] += len(hotspots)
            
            return hotspots
            
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
            return []
    
    def _find_time_complexity_issues(self, tree: ast.AST) -> List[Dict]:
        """
        Find potential time complexity issues.
        
        Args:
            tree: AST of the file
            
        Returns:
            List of dictionaries containing hotspot information
        """
        hotspots = []
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                self.stats["functions_analyzed"] += 1
                
                # Check for nested loops
                nested_loops = self._find_nested_loops(node)
                if nested_loops:
                    hotspots.append({
                        "type": "time_complexity",
                        "function": node.name,
                        "line": node.lineno,
                        "issue": "nested_loops",
                        "severity": "high",
                        "description": f"Function contains {len(nested_loops)} nested loops, which may have O(n²) or worse time complexity",
                        "code": ast.unparse(node),
                        "recommendation": "Consider restructuring the algorithm to avoid nested loops or use more efficient data structures"
                    })
                
                # Check for inefficient list operations
                inefficient_list_ops = self._find_inefficient_list_operations(node)
                if inefficient_list_ops:
                    hotspots.append({
                        "type": "time_complexity",
                        "function": node.name,
                        "line": node.lineno,
                        "issue": "inefficient_list_operations",
                        "severity": "medium",
                        "description": f"Function contains {len(inefficient_list_ops)} inefficient list operations",
                        "code": ast.unparse(node),
                        "recommendation": "Use more efficient data structures like sets or dictionaries for lookups"
                    })
                
                # Check for recursive functions without memoization
                if self._is_recursive_without_memoization(node):
                    hotspots.append({
                        "type": "time_complexity",
                        "function": node.name,
                        "line": node.lineno,
                        "issue": "recursive_without_memoization",
                        "severity": "medium",
                        "description": "Recursive function without memoization may recalculate the same values multiple times",
                        "code": ast.unparse(node),
                        "recommendation": "Implement memoization to cache previously computed results"
                    })
        
        return hotspots
    
    def _find_nested_loops(self, node: ast.AST) -> List[Dict]:
        """
        Find nested loops in a function.
        
        Args:
            node: AST node of the function
            
        Returns:
            List of dictionaries containing nested loop information
        """
        nested_loops = []
        
        for child_node in ast.walk(node):
            if isinstance(child_node, (ast.For, ast.While)):
                # Check if this loop contains another loop
                inner_loops = []
                for inner_node in ast.walk(child_node):
                    if inner_node != child_node and isinstance(inner_node, (ast.For, ast.While)):
                        inner_loops.append(inner_node)
                
                if inner_loops:
                    nested_loops.append({
                        "outer_loop": child_node,
                        "inner_loops": inner_loops,
                        "line": child_node.lineno,
                        "code": ast.unparse(child_node)
                    })
        
        return nested_loops
    
    def _find_inefficient_list_operations(self, node: ast.AST) -> List[Dict]:
        """
        Find inefficient list operations in a function.
        
        Args:
            node: AST node of the function
            
        Returns:
            List of dictionaries containing inefficient list operation information
        """
        inefficient_ops = []
        
        for child_node in ast.walk(node):
            # Check for 'in' operator on lists
            if isinstance(child_node, ast.Compare):
                if any(isinstance(op, ast.In) for op in child_node.ops):
                    # Check if the right operand is a list
                    if isinstance(child_node.comparators[0], (ast.List, ast.ListComp)):
                        inefficient_ops.append({
                            "line": child_node.lineno,
                            "code": ast.unparse(child_node),
                            "issue": "in_operator_on_list"
                        })
            
            # Check for repeated list.append in a loop
            if isinstance(child_node, ast.For):
                append_calls = []
                for stmt in child_node.body:
                    if isinstance(stmt, ast.Expr) and isinstance(stmt.value, ast.Call):
                        if hasattr(stmt.value.func, 'attr') and stmt.value.func.attr == 'append':
                            append_calls.append(stmt)
                
                if len(append_calls) > 1:
                    inefficient_ops.append({
                        "line": child_node.lineno,
                        "code": ast.unparse(child_node),
                        "issue": "repeated_append_in_loop"
                    })
        
        return inefficient_ops
    
    def _is_recursive_without_memoization(self, node: ast.FunctionDef) -> bool:
        """
        Check if a function is recursive without memoization.
        
        Args:
            node: AST node of the function
            
        Returns:
            True if the function is recursive without memoization
        """
        # Check if the function calls itself
        function_name = node.name
        calls_self = False
        
        for child_node in ast.walk(node):
            if isinstance(child_node, ast.Call) and hasattr(child_node, 'func'):
                if hasattr(child_node.func, 'id') and child_node.func.id == function_name:
                    calls_self = True
                    break
        
        if not calls_self:
            return False
        
        # Check for memoization patterns
        has_memoization = False
        
        # Check for dictionary used for caching
        for child_node in ast.walk(node):
            if isinstance(child_node, ast.Dict):
                has_memoization = True
                break
            
            # Check for lru_cache decorator
            if isinstance(child_node, ast.Call) and hasattr(child_node, 'func'):
                if hasattr(child_node.func, 'attr') and child_node.func.attr == 'lru_cache':
                    has_memoization = True
                    break
        
        return calls_self and not has_memoization
    
    def _find_memory_usage_issues(self, tree: ast.AST) -> List[Dict]:
        """
        Find potential memory usage issues.
        
        Args:
            tree: AST of the file
            
        Returns:
            List of dictionaries containing hotspot information
        """
        hotspots = []
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                # Check for large data structures
                large_data_structures = self._find_large_data_structures(node)
                if large_data_structures:
                    hotspots.append({
                        "type": "memory_usage",
                        "function": node.name,
                        "line": node.lineno,
                        "issue": "large_data_structures",
                        "severity": "medium",
                        "description": f"Function creates potentially large data structures",
                        "code": ast.unparse(node),
                        "recommendation": "Consider using generators or iterators instead of creating large lists or dictionaries"
                    })
                
                # Check for memory leaks
                potential_memory_leaks = self._find_potential_memory_leaks(node)
                if potential_memory_leaks:
                    hotspots.append({
                        "type": "memory_usage",
                        "function": node.name,
                        "line": node.lineno,
                        "issue": "potential_memory_leak",
                        "severity": "high",
                        "description": "Function may have memory leaks due to circular references or unclosed resources",
                        "code": ast.unparse(node),
                        "recommendation": "Ensure resources are properly closed and circular references are broken"
                    })
        
        return hotspots
    
    def _find_large_data_structures(self, node: ast.AST) -> List[Dict]:
        """
        Find potentially large data structures in a function.
        
        Args:
            node: AST node of the function
            
        Returns:
            List of dictionaries containing large data structure information
        """
        large_structures = []
        
        for child_node in ast.walk(node):
            # Check for list/dict comprehensions
            if isinstance(child_node, (ast.ListComp, ast.DictComp, ast.SetComp)):
                large_structures.append({
                    "line": child_node.lineno,
                    "code": ast.unparse(child_node),
                    "type": type(child_node).__name__
                })
            
            # Check for large list/dict literals
            if isinstance(child_node, ast.List) and len(child_node.elts) > 100:
                large_structures.append({
                    "line": child_node.lineno,
                    "code": ast.unparse(child_node),
                    "type": "List"
                })
            
            if isinstance(child_node, ast.Dict) and len(child_node.keys) > 100:
                large_structures.append({
                    "line": child_node.lineno,
                    "code": ast.unparse(child_node),
                    "type": "Dict"
                })
        
        return large_structures
    
    def _find_potential_memory_leaks(self, node: ast.AST) -> List[Dict]:
        """
        Find potential memory leaks in a function.
        
        Args:
            node: AST node of the function
            
        Returns:
            List of dictionaries containing memory leak information
        """
        memory_leaks = []
        
        # Check for unclosed resources
        for child_node in ast.walk(node):
            if isinstance(child_node, ast.Call):
                if hasattr(child_node, 'func') and hasattr(child_node.func, 'attr'):
                    # Check for file operations without 'with' statement
                    if child_node.func.attr == 'open':
                        # Check if this open call is not inside a 'with' statement
                        if not self._is_inside_with_statement(node, child_node):
                            memory_leaks.append({
                                "line": child_node.lineno,
                                "code": ast.unparse(child_node),
                                "issue": "unclosed_file"
                            })
                    
                    # Check for other resource operations
                    if child_node.func.attr in ['connect', 'cursor', 'socket']:
                        if not self._is_inside_with_statement(node, child_node):
                            memory_leaks.append({
                                "line": child_node.lineno,
                                "code": ast.unparse(child_node),
                                "issue": f"unclosed_{child_node.func.attr}"
                            })
        
        return memory_leaks
    
    def _is_inside_with_statement(self, function_node: ast.AST, node: ast.AST) -> bool:
        """
        Check if a node is inside a 'with' statement.
        
        Args:
            function_node: AST node of the function
            node: AST node to check
            
        Returns:
            True if the node is inside a 'with' statement
        """
        for child_node in ast.walk(function_node):
            if isinstance(child_node, ast.With):
                # Check if the node is inside this 'with' statement
                for stmt in ast.walk(child_node):
                    if stmt == node:
                        return True
        
        return False
    
    def _find_cpu_intensive_operations(self, tree: ast.AST) -> List[Dict]:
        """
        Find CPU intensive operations.
        
        Args:
            tree: AST of the file
            
        Returns:
            List of dictionaries containing hotspot information
        """
        hotspots = []
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                # Check for expensive operations
                expensive_operations = self._find_expensive_operations(node)
                if expensive_operations:
                    hotspots.append({
                        "type": "cpu_intensive",
                        "function": node.name,
                        "line": node.lineno,
                        "issue": "expensive_operations",
                        "severity": "high",
                        "description": f"Function contains {len(expensive_operations)} potentially expensive operations",
                        "code": ast.unparse(node),
                        "recommendation": "Optimize expensive operations or consider caching results"
                    })
                
                # Check for repeated calculations
                repeated_calculations = self._find_repeated_calculations(node)
                if repeated_calculations:
                    hotspots.append({
                        "type": "cpu_intensive",
                        "function": node.name,
                        "line": node.lineno,
                        "issue": "repeated_calculations",
                        "severity": "medium",
                        "description": "Function may perform the same calculations multiple times",
                        "code": ast.unparse(node),
                        "recommendation": "Store results of expensive calculations in variables"
                    })
        
        return hotspots
    
    def _find_expensive_operations(self, node: ast.AST) -> List[Dict]:
        """
        Find expensive operations in a function.
        
        Args:
            node: AST node of the function
            
        Returns:
            List of dictionaries containing expensive operation information
        """
        expensive_ops = []
        
        for child_node in ast.walk(node):
            # Check for expensive function calls
            if isinstance(child_node, ast.Call):
                if hasattr(child_node, 'func'):
                    if hasattr(child_node.func, 'id') and child_node.func.id in [
                        'sorted', 'sort', 'filter', 'map', 'reduce', 'zip', 'enumerate',
                        'min', 'max', 'sum', 'any', 'all'
                    ]:
                        expensive_ops.append({
                            "line": child_node.lineno,
                            "code": ast.unparse(child_node),
                            "operation": child_node.func.id
                        })
                    
                    if hasattr(child_node.func, 'attr') and child_node.func.attr in [
                        'sort', 'sorted', 'filter', 'map', 'reduce',
                        'intersection', 'union', 'difference'
                    ]:
                        expensive_ops.append({
                            "line": child_node.lineno,
                            "code": ast.unparse(child_node),
                            "operation": child_node.func.attr
                        })
            
            # Check for regular expressions
            if isinstance(child_node, ast.Call) and hasattr(child_node, 'func'):
                if hasattr(child_node.func, 'value') and hasattr(child_node.func.value, 'id') and child_node.func.value.id == 're':
                    expensive_ops.append({
                        "line": child_node.lineno,
                        "code": ast.unparse(child_node),
                        "operation": "regular_expression"
                    })
        
        return expensive_ops
    
    def _find_repeated_calculations(self, node: ast.AST) -> List[Dict]:
        """
        Find repeated calculations in a function.
        
        Args:
            node: AST node of the function
            
        Returns:
            List of dictionaries containing repeated calculation information
        """
        repeated_calcs = []
        expressions = {}
        
        for child_node in ast.walk(node):
            if isinstance(child_node, (ast.BinOp, ast.Call)):
                expr_str = ast.unparse(child_node)
                if expr_str in expressions:
                    expressions[expr_str].append(child_node.lineno)
                else:
                    expressions[expr_str] = [child_node.lineno]
        
        for expr, lines in expressions.items():
            if len(lines) > 1 and len(expr) > 10:  # Only consider non-trivial expressions
                repeated_calcs.append({
                    "expression": expr,
                    "lines": lines,
                    "count": len(lines)
                })
        
        return repeated_calcs
    
    def analyze_directory(self, directory: str) -> List[Dict]:
        """
        Analyze all Python files in a directory for computational hotspots.
        
        Args:
            directory: Directory to analyze
            
        Returns:
            List of dictionaries containing hotspot information
        """
        for root, _, files in os.walk(directory):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    self.analyze_file(file_path)
        
        return self.hotspots
    
    def generate_report(self, output_file: str = "computational_performance_report.md") -> None:
        """
        Generate a Markdown report of the analysis results.
        
        Args:
            output_file: Path to the output file
        """
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# Computational Performance Analysis Report\n\n")
            f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## Summary\n\n")
            f.write(f"- Files analyzed: {self.stats['files_analyzed']}\n")
            f.write(f"- Functions analyzed: {self.stats['functions_analyzed']}\n")
            f.write(f"- Total computational hotspots: {self.stats['total_hotspots']}\n\n")
            
            f.write("## Hotspots by Type\n\n")
            f.write(f"- Time complexity issues: {self.stats['time_complexity_issues']}\n")
            f.write(f"- Memory usage issues: {self.stats['memory_usage_issues']}\n")
            f.write(f"- CPU intensive operations: {self.stats['cpu_intensive_operations']}\n\n")
            
            # Group hotspots by file
            hotspots_by_file = {}
            for hotspot in self.hotspots:
                file_path = hotspot["file"]
                if file_path not in hotspots_by_file:
                    hotspots_by_file[file_path] = []
                hotspots_by_file[file_path].append(hotspot)
            
            f.write("## Hotspots by File\n\n")
            for file_path, hotspots in hotspots_by_file.items():
                f.write(f"### {file_path}\n\n")
                
                for hotspot in hotspots:
                    severity_marker = "🔴" if hotspot["severity"] == "high" else "🟠" if hotspot["severity"] == "medium" else "🟡"
                    f.write(f"#### {severity_marker} {hotspot['function']} (Line {hotspot['line']})\n\n")
                    f.write(f"**Type:** {hotspot['type']}\n\n")
                    f.write(f"**Issue:** {hotspot['issue']}\n\n")
                    f.write(f"**Description:** {hotspot['description']}\n\n")
                    f.write("**Code:**\n\n")
                    f.write("```python\n")
                    f.write(hotspot['code'])
                    f.write("\n```\n\n")
                    f.write(f"**Recommendation:** {hotspot['recommendation']}\n\n")
            
            f.write("## Optimization Recommendations\n\n")
            f.write("### Time Complexity Optimization\n\n")
            f.write("1. **Avoid nested loops** - Use more efficient algorithms or data structures\n")
            f.write("2. **Use appropriate data structures** - Use sets or dictionaries for lookups instead of lists\n")
            f.write("3. **Implement memoization** - Cache results of expensive calculations\n")
            f.write("4. **Use more efficient algorithms** - Research and implement more efficient algorithms for your specific problem\n\n")
            
            f.write("### Memory Usage Optimization\n\n")
            f.write("1. **Use generators** - Use generators instead of creating large lists\n")
            f.write("2. **Properly close resources** - Use context managers (with statements) for files and other resources\n")
            f.write("3. **Avoid circular references** - Break circular references to prevent memory leaks\n")
            f.write("4. **Use memory-efficient data structures** - Consider using arrays or other memory-efficient data structures\n\n")
            
            f.write("### CPU Intensive Operation Optimization\n\n")
            f.write("1. **Cache results** - Cache results of expensive operations\n")
            f.write("2. **Optimize regular expressions** - Compile regular expressions and use more efficient patterns\n")
            f.write("3. **Use built-in functions** - Use built-in functions and methods when possible\n")
            f.write("4. **Consider parallel processing** - Use multiprocessing or threading for CPU-bound tasks\n")
    
    def save_json_report(self, output_file: str = "computational_performance_report.json") -> None:
        """
        Save the analysis results as JSON.
        
        Args:
            output_file: Path to the output file
        """
        report = {
            "timestamp": datetime.now().isoformat(),
            "stats": self.stats,
            "hotspots": self.hotspots,
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2)


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Profile computational performance of the codebase")
    parser.add_argument("--path", help="Path to file or directory to analyze")
    parser.add_argument("--output", default="computational_performance_report.md", help="Output report file")
    parser.add_argument("--json", action="store_true", help="Generate JSON report")
    
    args = parser.parse_args()
    
    # Install dependencies if needed
    install_dependencies()
    
    profiler = ComputationalProfiler()
    
    if args.path:
        path = args.path
    else:
        path = "api"  # Default to the api directory
        
    if os.path.isfile(path):
        profiler.analyze_file(path)
    elif os.path.isdir(path):
        profiler.analyze_directory(path)
    else:
        print(f"Path not found: {path}")
        return 1
    
    profiler.generate_report(args.output)
    print(f"Report saved to {args.output}")
    
    if args.json:
        json_output = os.path.splitext(args.output)[0] + ".json"
        profiler.save_json_report(json_output)
        print(f"JSON report saved to {json_output}")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
