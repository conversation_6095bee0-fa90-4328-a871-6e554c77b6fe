#!/usr/bin/env python3
"""
Script to identify caching opportunities in the codebase.

This script analyzes the codebase to identify functions and methods that
could benefit from caching, such as expensive computations, repeated database
queries, and external API calls.
"""

import os
import sys
import re
import ast
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple
import json
from datetime import datetime


class CachingOpportunityAnalyzer:
    """Class to analyze code for caching opportunities."""

    def __init__(self):
        """Initialize the CachingOpportunityAnalyzer."""
        self.opportunities = []
        self.stats = {
            "files_analyzed": 0,
            "functions_analyzed": 0,
            "database_query_opportunities": 0,
            "computation_opportunities": 0,
            "api_call_opportunities": 0,
            "total_opportunities": 0,
        }
        
    def analyze_file(self, file_path: str) -> List[Dict]:
        """
        Analyze a Python file for caching opportunities.
        
        Args:
            file_path: Path to the Python file
            
        Returns:
            List of dictionaries containing caching opportunities
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            self.stats["files_analyzed"] += 1
            
            # Parse the file with AST
            tree = ast.parse(content)
            
            # Find functions and methods
            opportunities = []
            
            for node in ast.walk(tree):
                if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    self.stats["functions_analyzed"] += 1
                    
                    # Check for database queries
                    db_queries = self._find_database_queries(node)
                    if db_queries:
                        opportunity = {
                            "file": file_path,
                            "function": node.name,
                            "line": node.lineno,
                            "type": "database_query",
                            "details": {
                                "query_count": len(db_queries),
                                "queries": db_queries
                            },
                            "recommendation": "Consider caching the results of database queries",
                            "priority": "high" if len(db_queries) > 1 else "medium"
                        }
                        opportunities.append(opportunity)
                        self.stats["database_query_opportunities"] += 1
                    
                    # Check for expensive computations
                    computations = self._find_expensive_computations(node)
                    if computations:
                        opportunity = {
                            "file": file_path,
                            "function": node.name,
                            "line": node.lineno,
                            "type": "computation",
                            "details": {
                                "computation_count": len(computations),
                                "computations": computations
                            },
                            "recommendation": "Consider caching the results of expensive computations",
                            "priority": "high" if len(computations) > 1 else "medium"
                        }
                        opportunities.append(opportunity)
                        self.stats["computation_opportunities"] += 1
                    
                    # Check for API calls
                    api_calls = self._find_api_calls(node)
                    if api_calls:
                        opportunity = {
                            "file": file_path,
                            "function": node.name,
                            "line": node.lineno,
                            "type": "api_call",
                            "details": {
                                "api_call_count": len(api_calls),
                                "api_calls": api_calls
                            },
                            "recommendation": "Consider caching the results of external API calls",
                            "priority": "high"
                        }
                        opportunities.append(opportunity)
                        self.stats["api_call_opportunities"] += 1
            
            self.opportunities.extend(opportunities)
            self.stats["total_opportunities"] += len(opportunities)
            
            return opportunities
            
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
            return []
    
    def _find_database_queries(self, node: ast.AST) -> List[Dict]:
        """
        Find database queries in a function.
        
        Args:
            node: AST node of the function
            
        Returns:
            List of dictionaries containing query information
        """
        queries = []
        
        for child_node in ast.walk(node):
            # Look for common ORM patterns
            if isinstance(child_node, ast.Call):
                # SQLAlchemy query patterns
                if self._is_sqlalchemy_query(child_node):
                    queries.append({
                        "line": child_node.lineno,
                        "query": ast.unparse(child_node)
                    })
                
                # Django ORM patterns
                elif self._is_django_query(child_node):
                    queries.append({
                        "line": child_node.lineno,
                        "query": ast.unparse(child_node)
                    })
                
                # Raw SQL execution
                elif self._is_raw_sql(child_node):
                    queries.append({
                        "line": child_node.lineno,
                        "query": ast.unparse(child_node)
                    })
        
        return queries
    
    def _is_sqlalchemy_query(self, node: ast.Call) -> bool:
        """
        Check if a call node is a SQLAlchemy query.
        
        Args:
            node: AST call node
            
        Returns:
            True if the node is a SQLAlchemy query
        """
        # Check for Model.query.filter, session.query, etc.
        if hasattr(node, 'func') and hasattr(node.func, 'value'):
            if hasattr(node.func.value, 'attr'):
                return node.func.value.attr == 'query' and node.func.attr in [
                    'all', 'filter', 'filter_by', 'get', 'first', 'one'
                ]
            elif hasattr(node.func.value, 'id') and node.func.value.id == 'session':
                return node.func.attr == 'query'
        return False
    
    def _is_django_query(self, node: ast.Call) -> bool:
        """
        Check if a call node is a Django ORM query.
        
        Args:
            node: AST call node
            
        Returns:
            True if the node is a Django ORM query
        """
        # Check for Model.objects.filter, Model.objects.all, etc.
        if hasattr(node, 'func') and hasattr(node.func, 'value'):
            if hasattr(node.func.value, 'attr'):
                return node.func.value.attr == 'objects' and node.func.attr in [
                    'all', 'filter', 'get', 'first', 'last', 'count'
                ]
        return False
    
    def _is_raw_sql(self, node: ast.Call) -> bool:
        """
        Check if a call node is a raw SQL execution.
        
        Args:
            node: AST call node
            
        Returns:
            True if the node is a raw SQL execution
        """
        # Check for cursor.execute, connection.execute, etc.
        if hasattr(node, 'func') and hasattr(node.func, 'attr'):
            return node.func.attr == 'execute' and len(node.args) > 0
        return False
    
    def _find_expensive_computations(self, node: ast.AST) -> List[Dict]:
        """
        Find expensive computations in a function.
        
        Args:
            node: AST node of the function
            
        Returns:
            List of dictionaries containing computation information
        """
        computations = []
        
        # Check for nested loops
        nested_loops = self._find_nested_loops(node)
        for loop in nested_loops:
            computations.append({
                "line": loop["line"],
                "type": "nested_loop",
                "code": loop["code"]
            })
        
        # Check for recursive calls
        recursive_calls = self._find_recursive_calls(node)
        for call in recursive_calls:
            computations.append({
                "line": call["line"],
                "type": "recursive_call",
                "code": call["code"]
            })
        
        # Check for complex list/dict comprehensions
        comprehensions = self._find_complex_comprehensions(node)
        for comp in comprehensions:
            computations.append({
                "line": comp["line"],
                "type": "complex_comprehension",
                "code": comp["code"]
            })
        
        return computations
    
    def _find_nested_loops(self, node: ast.AST) -> List[Dict]:
        """
        Find nested loops in a function.
        
        Args:
            node: AST node of the function
            
        Returns:
            List of dictionaries containing nested loop information
        """
        nested_loops = []
        
        for child_node in ast.walk(node):
            if isinstance(child_node, (ast.For, ast.While)):
                # Check if this loop contains another loop
                inner_loops = []
                for inner_node in ast.walk(child_node):
                    if inner_node != child_node and isinstance(inner_node, (ast.For, ast.While)):
                        inner_loops.append(inner_node)
                
                if inner_loops:
                    nested_loops.append({
                        "line": child_node.lineno,
                        "code": ast.unparse(child_node)
                    })
        
        return nested_loops
    
    def _find_recursive_calls(self, node: ast.FunctionDef) -> List[Dict]:
        """
        Find recursive calls in a function.
        
        Args:
            node: AST node of the function
            
        Returns:
            List of dictionaries containing recursive call information
        """
        recursive_calls = []
        function_name = node.name
        
        for child_node in ast.walk(node):
            if isinstance(child_node, ast.Call) and hasattr(child_node, 'func'):
                if hasattr(child_node.func, 'id') and child_node.func.id == function_name:
                    recursive_calls.append({
                        "line": child_node.lineno,
                        "code": ast.unparse(child_node)
                    })
        
        return recursive_calls
    
    def _find_complex_comprehensions(self, node: ast.AST) -> List[Dict]:
        """
        Find complex list/dict comprehensions in a function.
        
        Args:
            node: AST node of the function
            
        Returns:
            List of dictionaries containing comprehension information
        """
        comprehensions = []
        
        for child_node in ast.walk(node):
            if isinstance(child_node, (ast.ListComp, ast.DictComp, ast.SetComp)):
                # Check if the comprehension has multiple generators or conditions
                if len(child_node.generators) > 1 or any(len(gen.ifs) > 0 for gen in child_node.generators):
                    comprehensions.append({
                        "line": child_node.lineno,
                        "code": ast.unparse(child_node)
                    })
        
        return comprehensions
    
    def _find_api_calls(self, node: ast.AST) -> List[Dict]:
        """
        Find external API calls in a function.
        
        Args:
            node: AST node of the function
            
        Returns:
            List of dictionaries containing API call information
        """
        api_calls = []
        
        for child_node in ast.walk(node):
            if isinstance(child_node, ast.Call):
                # Check for common HTTP client libraries
                if self._is_http_request(child_node):
                    api_calls.append({
                        "line": child_node.lineno,
                        "code": ast.unparse(child_node)
                    })
        
        return api_calls
    
    def _is_http_request(self, node: ast.Call) -> bool:
        """
        Check if a call node is an HTTP request.
        
        Args:
            node: AST call node
            
        Returns:
            True if the node is an HTTP request
        """
        # Check for requests.get, requests.post, etc.
        if hasattr(node, 'func') and hasattr(node.func, 'value'):
            if hasattr(node.func.value, 'id') and node.func.value.id == 'requests':
                return node.func.attr in ['get', 'post', 'put', 'delete', 'patch']
        
        # Check for urllib.request.urlopen
        if hasattr(node, 'func') and hasattr(node.func, 'value'):
            if hasattr(node.func.value, 'attr') and node.func.value.attr == 'request':
                return node.func.attr == 'urlopen'
        
        # Check for http.client methods
        if hasattr(node, 'func') and hasattr(node.func, 'value'):
            if hasattr(node.func.value, 'id') and node.func.value.id in ['conn', 'connection', 'client']:
                return node.func.attr in ['request', 'getresponse', 'connect']
        
        return False
    
    def analyze_directory(self, directory: str) -> List[Dict]:
        """
        Analyze all Python files in a directory for caching opportunities.
        
        Args:
            directory: Directory to analyze
            
        Returns:
            List of dictionaries containing caching opportunities
        """
        for root, _, files in os.walk(directory):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    self.analyze_file(file_path)
        
        return self.opportunities
    
    def generate_report(self, output_file: str = "caching_opportunities_report.md") -> None:
        """
        Generate a Markdown report of the analysis results.
        
        Args:
            output_file: Path to the output file
        """
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# Caching Opportunities Analysis Report\n\n")
            f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## Summary\n\n")
            f.write(f"- Files analyzed: {self.stats['files_analyzed']}\n")
            f.write(f"- Functions analyzed: {self.stats['functions_analyzed']}\n")
            f.write(f"- Total caching opportunities: {self.stats['total_opportunities']}\n\n")
            
            f.write("## Opportunities by Type\n\n")
            f.write(f"- Database query opportunities: {self.stats['database_query_opportunities']}\n")
            f.write(f"- Computation opportunities: {self.stats['computation_opportunities']}\n")
            f.write(f"- API call opportunities: {self.stats['api_call_opportunities']}\n\n")
            
            # Group opportunities by file
            opportunities_by_file = {}
            for opportunity in self.opportunities:
                file_path = opportunity["file"]
                if file_path not in opportunities_by_file:
                    opportunities_by_file[file_path] = []
                opportunities_by_file[file_path].append(opportunity)
            
            f.write("## Opportunities by File\n\n")
            for file_path, opportunities in opportunities_by_file.items():
                f.write(f"### {file_path}\n\n")
                
                for opportunity in opportunities:
                    priority_marker = "🔴" if opportunity["priority"] == "high" else "🟠"
                    f.write(f"#### {priority_marker} {opportunity['function']} (Line {opportunity['line']})\n\n")
                    f.write(f"**Type:** {opportunity['type']}\n\n")
                    f.write(f"**Recommendation:** {opportunity['recommendation']}\n\n")
                    
                    if opportunity["type"] == "database_query":
                        f.write(f"**Query Count:** {opportunity['details']['query_count']}\n\n")
                        f.write("**Queries:**\n\n")
                        for query in opportunity["details"]["queries"]:
                            f.write(f"- Line {query['line']}: `{query['query']}`\n")
                    
                    elif opportunity["type"] == "computation":
                        f.write(f"**Computation Count:** {opportunity['details']['computation_count']}\n\n")
                        f.write("**Computations:**\n\n")
                        for computation in opportunity["details"]["computations"]:
                            f.write(f"- Line {computation['line']} ({computation['type']}):\n")
                            f.write("```python\n")
                            f.write(computation["code"])
                            f.write("\n```\n")
                    
                    elif opportunity["type"] == "api_call":
                        f.write(f"**API Call Count:** {opportunity['details']['api_call_count']}\n\n")
                        f.write("**API Calls:**\n\n")
                        for api_call in opportunity["details"]["api_calls"]:
                            f.write(f"- Line {api_call['line']}: `{api_call['code']}`\n")
                    
                    f.write("\n")
            
            f.write("## Implementation Recommendations\n\n")
            f.write("### Database Query Caching\n\n")
            f.write("1. Use Redis or Memcached for caching database query results\n")
            f.write("2. Implement a caching decorator for database access functions\n")
            f.write("3. Set appropriate TTL (Time To Live) based on data volatility\n")
            f.write("4. Implement cache invalidation when data is modified\n\n")
            
            f.write("### Computation Caching\n\n")
            f.write("1. Use memoization for pure functions\n")
            f.write("2. Implement LRU (Least Recently Used) caching for functions with limited input domains\n")
            f.write("3. Consider using functools.lru_cache for simple cases\n\n")
            
            f.write("### API Call Caching\n\n")
            f.write("1. Cache external API responses with appropriate TTL\n")
            f.write("2. Implement conditional requests using ETags or Last-Modified headers\n")
            f.write("3. Consider implementing a circuit breaker pattern for API calls\n")
    
    def save_json_report(self, output_file: str = "caching_opportunities_report.json") -> None:
        """
        Save the analysis results as JSON.
        
        Args:
            output_file: Path to the output file
        """
        report = {
            "timestamp": datetime.now().isoformat(),
            "stats": self.stats,
            "opportunities": self.opportunities,
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2)


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Identify caching opportunities in the codebase")
    parser.add_argument("--path", help="Path to file or directory to analyze")
    parser.add_argument("--output", default="caching_opportunities_report.md", help="Output report file")
    parser.add_argument("--json", action="store_true", help="Generate JSON report")
    
    args = parser.parse_args()
    
    analyzer = CachingOpportunityAnalyzer()
    
    if args.path:
        path = args.path
    else:
        path = "api"  # Default to the api directory
        
    if os.path.isfile(path):
        analyzer.analyze_file(path)
    elif os.path.isdir(path):
        analyzer.analyze_directory(path)
    else:
        print(f"Path not found: {path}")
        return 1
    
    analyzer.generate_report(args.output)
    print(f"Report saved to {args.output}")
    
    if args.json:
        json_output = os.path.splitext(args.output)[0] + ".json"
        analyzer.save_json_report(json_output)
        print(f"JSON report saved to {json_output}")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
