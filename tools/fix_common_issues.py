#!/usr/bin/env python3
"""
<PERSON>ript to fix common issues identified in the code structure analysis.

This script addresses the most common issues found in the codebase, such as:
1. Missing docstrings for classes and functions
2. Inconsistent naming conventions
3. Duplicate imports
"""

import os
import sys
import re
import ast
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
import json
import astor


class CodeFixer:
    """Class to fix common code issues."""

    def __init__(self, dry_run: bool = False):
        """
        Initialize the CodeFixer.
        
        Args:
            dry_run: If True, don't modify files
        """
        self.dry_run = dry_run
        self.fixes_applied = {
            "missing_docstrings": 0,
            "inconsistent_naming": 0,
            "duplicate_imports": 0,
            "total_files_modified": 0,
        }
        
    def fix_file(self, file_path: str) -> bool:
        """
        Fix issues in a Python file.
        
        Args:
            file_path: Path to the Python file
            
        Returns:
            bool: True if any fixes were applied
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Parse the file with AST
            tree = ast.parse(content)
            
            # Track if any fixes were applied
            fixes_applied = False
            
            # Fix duplicate imports
            fixed_imports, imports_fixed = self.fix_duplicate_imports(tree)
            if imports_fixed:
                fixes_applied = True
                self.fixes_applied["duplicate_imports"] += imports_fixed
                tree = ast.parse(fixed_imports)
            
            # Fix missing docstrings
            fixed_docstrings, docstrings_fixed = self.fix_missing_docstrings(tree)
            if docstrings_fixed:
                fixes_applied = True
                self.fixes_applied["missing_docstrings"] += docstrings_fixed
                tree = ast.parse(fixed_docstrings)
            
            # Fix inconsistent naming
            # Note: This is more complex and might require manual review
            # We'll just identify the issues for now
            
            # Write the fixed content back to the file
            if fixes_applied and not self.dry_run:
                fixed_content = astor.to_source(tree)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(fixed_content)
                self.fixes_applied["total_files_modified"] += 1
                
            return fixes_applied
            
        except Exception as e:
            print(f"Error fixing {file_path}: {e}")
            return False
    
    def fix_duplicate_imports(self, tree: ast.Module) -> Tuple[str, int]:
        """
        Fix duplicate imports in an AST tree.
        
        Args:
            tree: AST tree of the Python file
            
        Returns:
            Tuple of (fixed content, number of fixes applied)
        """
        # Track imports
        imports = {}
        import_nodes = []
        
        # Find all import nodes
        for i, node in enumerate(tree.body):
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                import_nodes.append((i, node))
                
                if isinstance(node, ast.Import):
                    for name in node.names:
                        full_name = name.name
                        if name.asname:
                            full_name = f"{name.name} as {name.asname}"
                        if full_name in imports:
                            imports[full_name].append(i)
                        else:
                            imports[full_name] = [i]
                else:  # ImportFrom
                    if node.module:
                        for name in node.names:
                            full_name = f"{node.module}.{name.name}"
                            if name.asname:
                                full_name = f"{full_name} as {name.asname}"
                            if full_name in imports:
                                imports[full_name].append(i)
                            else:
                                imports[full_name] = [i]
        
        # Find duplicate imports
        duplicates = {name: indices for name, indices in imports.items() if len(indices) > 1}
        
        if not duplicates:
            return astor.to_source(tree), 0
        
        # Remove duplicate imports
        indices_to_remove = []
        for name, indices in duplicates.items():
            # Keep the first occurrence, remove the rest
            indices_to_remove.extend(indices[1:])
        
        # Sort indices in reverse order to avoid index shifting
        indices_to_remove.sort(reverse=True)
        
        # Create a new tree without duplicate imports
        new_body = list(tree.body)
        for index in indices_to_remove:
            new_body.pop(index)
        
        new_tree = ast.Module(body=new_body, type_ignores=[])
        
        return astor.to_source(new_tree), len(indices_to_remove)
    
    def fix_missing_docstrings(self, tree: ast.Module) -> Tuple[str, int]:
        """
        Add docstrings to functions and classes that are missing them.
        
        Args:
            tree: AST tree of the Python file
            
        Returns:
            Tuple of (fixed content, number of fixes applied)
        """
        class DocstringTransformer(ast.NodeTransformer):
            def __init__(self):
                self.fixes = 0
                
            def visit_FunctionDef(self, node):
                # Check if function has a docstring
                has_docstring = (
                    node.body and 
                    isinstance(node.body[0], ast.Expr) and 
                    isinstance(node.body[0].value, ast.Str)
                )
                
                if not has_docstring:
                    # Add a simple docstring
                    docstring = f'"""\n{node.name} function.\n\n"""'
                    docstring_node = ast.Expr(value=ast.Str(s=docstring))
                    node.body.insert(0, docstring_node)
                    self.fixes += 1
                
                return self.generic_visit(node)
                
            def visit_ClassDef(self, node):
                # Check if class has a docstring
                has_docstring = (
                    node.body and 
                    isinstance(node.body[0], ast.Expr) and 
                    isinstance(node.body[0].value, ast.Str)
                )
                
                if not has_docstring:
                    # Add a simple docstring
                    docstring = f'"""\n{node.name} class.\n\n"""'
                    docstring_node = ast.Expr(value=ast.Str(s=docstring))
                    node.body.insert(0, docstring_node)
                    self.fixes += 1
                
                return self.generic_visit(node)
        
        transformer = DocstringTransformer()
        new_tree = transformer.visit(tree)
        
        return astor.to_source(new_tree), transformer.fixes
    
    def fix_directory(self, directory: str) -> int:
        """
        Fix issues in all Python files in a directory.
        
        Args:
            directory: Directory to process
            
        Returns:
            Number of files modified
        """
        files_modified = 0
        
        for root, _, files in os.walk(directory):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    if self.fix_file(file_path):
                        files_modified += 1
        
        return files_modified
    
    def generate_report(self, output_file: str = "code_fixes_report.md") -> None:
        """
        Generate a Markdown report of the fixes applied.
        
        Args:
            output_file: Path to the output file
        """
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# Code Fixes Report\n\n")
            
            if self.dry_run:
                f.write("**Dry run mode - no files were modified**\n\n")
            
            f.write("## Summary\n\n")
            f.write(f"- Total files modified: {self.fixes_applied['total_files_modified']}\n")
            f.write(f"- Missing docstrings fixed: {self.fixes_applied['missing_docstrings']}\n")
            f.write(f"- Duplicate imports fixed: {self.fixes_applied['duplicate_imports']}\n")
            f.write(f"- Inconsistent naming identified: {self.fixes_applied['inconsistent_naming']}\n\n")
            
            f.write("## Notes\n\n")
            f.write("1. **Inconsistent naming** issues require manual review and were not automatically fixed.\n")
            f.write("2. **Missing docstrings** were fixed with placeholder docstrings that should be reviewed and expanded.\n")
            f.write("3. **Duplicate imports** were fixed by removing redundant import statements.\n\n")
            
            f.write("## Next Steps\n\n")
            f.write("1. Review the placeholder docstrings and replace them with meaningful documentation.\n")
            f.write("2. Manually address inconsistent naming issues.\n")
            f.write("3. Run the code structure analysis again to verify the fixes.\n")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Fix common code issues")
    parser.add_argument("--path", help="Path to file or directory to process")
    parser.add_argument("--dry-run", action="store_true", help="Don't modify files, just report what would be changed")
    parser.add_argument("--output", default="code_fixes_report.md", help="Output report file")
    
    args = parser.parse_args()
    
    try:
        import astor
    except ImportError:
        print("Installing required dependencies...")
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "astor"])
        import astor
    
    fixer = CodeFixer(dry_run=args.dry_run)
    
    if args.path:
        path = args.path
    else:
        path = "api"  # Default to the api directory
        
    if os.path.isfile(path):
        fixer.fix_file(path)
    elif os.path.isdir(path):
        fixer.fix_directory(path)
    else:
        print(f"Path not found: {path}")
        return 1
    
    fixer.generate_report(args.output)
    print(f"Report saved to {args.output}")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
