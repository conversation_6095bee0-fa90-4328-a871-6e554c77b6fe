#!/usr/bin/env python3
"""
<PERSON>ript to analyze test coverage in the codebase.

This script runs pytest with coverage and generates a report of test coverage
for the codebase, highlighting areas that need improvement.
"""

import os
import sys
import subprocess
import argparse
import json
from datetime import datetime
from pathlib import Path


def install_dependencies():
    """Install required dependencies if not already installed."""
    try:
        import pytest
        import coverage
    except ImportError:
        print("Installing required dependencies...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pytest", "pytest-cov", "coverage"])


def run_coverage(module_paths=None, output_dir="coverage_reports", html_report=True, xml_report=True, threshold=80):
    """
    Run test coverage analysis for the specified modules.
    
    Args:
        module_paths: List of module paths to analyze (None for all)
        output_dir: Directory to save output files
        html_report: Whether to generate HTML report
        xml_report: Whether to generate XML report
        threshold: Coverage threshold percentage
        
    Returns:
        dict: Coverage results
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate timestamp for report files
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Build pytest command
    cmd = [
        "python", "-m", "pytest",
        "--cov=api",
        f"--cov-report=term",
    ]
    
    if html_report:
        cmd.append(f"--cov-report=html:{output_dir}/html_{timestamp}")
    
    if xml_report:
        cmd.append(f"--cov-report=xml:{output_dir}/coverage_{timestamp}.xml")
    
    # Add specific modules if provided
    if module_paths:
        cmd.extend(module_paths)
    
    # Run pytest with coverage
    print("Running test coverage analysis...")
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        # Save output to text file
        output_file = f"{output_dir}/coverage_{timestamp}.txt"
        with open(output_file, 'w') as f:
            f.write(result.stdout)
            f.write("\n\n")
            f.write(result.stderr)
        
        print(f"Coverage output saved to {output_file}")
        
        # Parse coverage results
        coverage_data = parse_coverage_output(result.stdout)
        
        # Check if coverage meets threshold
        total_coverage = coverage_data.get("total_coverage", 0)
        if total_coverage < threshold:
            print(f"WARNING: Coverage ({total_coverage}%) is below threshold ({threshold}%)")
        else:
            print(f"Coverage ({total_coverage}%) meets or exceeds threshold ({threshold}%)")
        
        # Save coverage data to JSON
        json_file = f"{output_dir}/coverage_{timestamp}.json"
        with open(json_file, 'w') as f:
            json.dump(coverage_data, f, indent=2)
        
        print(f"Coverage data saved to {json_file}")
        
        return {
            "success": True,
            "coverage_data": coverage_data,
            "output_file": output_file,
            "json_file": json_file,
            "html_dir": f"{output_dir}/html_{timestamp}" if html_report else None,
            "xml_file": f"{output_dir}/coverage_{timestamp}.xml" if xml_report else None
        }
        
    except subprocess.CalledProcessError as e:
        print(f"Error running coverage: {e}")
        return {
            "success": False,
            "error": str(e)
        }


def parse_coverage_output(output):
    """
    Parse the coverage output to extract coverage data.
    
    Args:
        output: Coverage command output
        
    Returns:
        dict: Parsed coverage data
    """
    lines = output.split('\n')
    coverage_data = {
        "modules": [],
        "total_coverage": 0,
        "missing_coverage": []
    }
    
    # Find the coverage summary table
    table_start = False
    for line in lines:
        if "---------- coverage:" in line:
            table_start = True
            continue
        
        if table_start and line.strip():
            if "TOTAL" in line:
                # Extract total coverage
                parts = line.split()
                try:
                    coverage_data["total_coverage"] = float(parts[-1].strip('%'))
                except (ValueError, IndexError):
                    pass
            elif "------------------" not in line and "Name" not in line:
                # Extract module coverage
                parts = line.split()
                if len(parts) >= 4:
                    try:
                        module_name = parts[0]
                        statements = int(parts[1])
                        missing = int(parts[2])
                        coverage_pct = float(parts[3].strip('%'))
                        
                        module_data = {
                            "name": module_name,
                            "statements": statements,
                            "missing": missing,
                            "coverage": coverage_pct
                        }
                        
                        coverage_data["modules"].append(module_data)
                        
                        # Add to missing coverage if below 80%
                        if coverage_pct < 80:
                            coverage_data["missing_coverage"].append({
                                "name": module_name,
                                "coverage": coverage_pct
                            })
                    except (ValueError, IndexError):
                        pass
    
    # Sort modules by coverage (ascending)
    coverage_data["modules"].sort(key=lambda x: x["coverage"])
    
    return coverage_data


def generate_report(results, output_file="coverage_summary.md"):
    """Generate a Markdown report of the coverage analysis."""
    with open(output_file, 'w') as f:
        f.write("# Test Coverage Report\n\n")
        f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        if not results["success"]:
            f.write(f"## Error\n\n")
            f.write(f"Error running coverage: {results['error']}\n\n")
            return
        
        coverage_data = results["coverage_data"]
        
        f.write(f"## Summary\n\n")
        f.write(f"Total coverage: **{coverage_data['total_coverage']}%**\n\n")
        
        f.write(f"## Modules with Low Coverage (< 80%)\n\n")
        if coverage_data["missing_coverage"]:
            f.write("| Module | Coverage |\n")
            f.write("|--------|----------|\n")
            for module in coverage_data["missing_coverage"]:
                f.write(f"| {module['name']} | {module['coverage']}% |\n")
        else:
            f.write("No modules with coverage below 80%.\n")
        
        f.write("\n## All Modules\n\n")
        f.write("| Module | Statements | Missing | Coverage |\n")
        f.write("|--------|------------|---------|----------|\n")
        for module in coverage_data["modules"]:
            f.write(f"| {module['name']} | {module['statements']} | {module['missing']} | {module['coverage']}% |\n")
        
        f.write("\n## Next Steps\n\n")
        if coverage_data["missing_coverage"]:
            f.write("1. Focus on improving test coverage for the following modules:\n")
            for module in coverage_data["missing_coverage"][:5]:  # Top 5 modules needing improvement
                f.write(f"   - {module['name']} ({module['coverage']}%)\n")
        else:
            f.write("Maintain current test coverage and add tests for new code.\n")
    
    print(f"Report saved to {output_file}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Analyze test coverage in Python code")
    parser.add_argument("--modules", nargs="+", help="Specific modules to analyze")
    parser.add_argument("--output-dir", default="coverage_reports", help="Directory to save output files")
    parser.add_argument("--no-html", action="store_true", help="Skip HTML report generation")
    parser.add_argument("--no-xml", action="store_true", help="Skip XML report generation")
    parser.add_argument("--threshold", type=float, default=80, help="Coverage threshold percentage")
    parser.add_argument("--report", default="coverage_summary.md", help="Output report file")
    
    args = parser.parse_args()
    
    # Install dependencies if needed
    install_dependencies()
    
    # Run coverage analysis
    results = run_coverage(
        module_paths=args.modules,
        output_dir=args.output_dir,
        html_report=not args.no_html,
        xml_report=not args.no_xml,
        threshold=args.threshold
    )
    
    # Generate report
    generate_report(results, args.report)
    
    print("\nAnalysis complete!")


if __name__ == "__main__":
    main()
