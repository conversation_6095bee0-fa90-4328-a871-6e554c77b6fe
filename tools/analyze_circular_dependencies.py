#!/usr/bin/env python3
"""
<PERSON>ript to analyze circular dependencies in the codebase.

This script uses the pydeps library to generate a dependency graph
and identify circular dependencies in the codebase.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path
import json
from datetime import datetime


def install_dependencies():
    """Install required dependencies if not already installed."""
    try:
        import pydeps
    except ImportError:
        print("Installing required dependencies...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pydeps"])


def analyze_dependencies(module_path, output_dir="dependency_analysis", max_depth=None):
    """
    Analyze dependencies for the specified module.
    
    Args:
        module_path: Path to the module to analyze
        output_dir: Directory to save output files
        max_depth: Maximum depth for dependency analysis
        
    Returns:
        dict: Analysis results including cycles found
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate base filename from module path
    base_name = os.path.basename(module_path)
    if os.path.isdir(module_path):
        output_base = f"{output_dir}/{base_name}"
    else:
        output_base = f"{output_dir}/{os.path.splitext(base_name)[0]}"
    
    # Build pydeps command
    cmd = [
        "pydeps",
        module_path,
        "--show-deps",
        "--show-cycles",
        f"--output={output_base}.svg",
    ]
    
    if max_depth:
        cmd.append(f"--max-bacon={max_depth}")
    
    # Run pydeps
    print(f"Analyzing dependencies for {module_path}...")
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        # Save output to text file
        with open(f"{output_base}_cycles.txt", "w") as f:
            f.write(result.stdout)
            f.write("\n\n")
            f.write(result.stderr)
        
        # Check for cycles in the output
        cycles_found = "Cycles found" in result.stdout
        if cycles_found:
            print(f"Circular dependencies found in {module_path}!")
            print(result.stdout)
        else:
            print(f"No circular dependencies found in {module_path}.")
        
        print(f"Dependency graph saved to {output_base}.svg")
        print(f"Analysis details saved to {output_base}_cycles.txt")
        
        # Extract cycles from output
        cycles = []
        if cycles_found:
            # Parse the output to extract cycles
            lines = result.stdout.split('\n')
            cycle_start = False
            current_cycle = []
            
            for line in lines:
                if line.startswith("Cycles found:"):
                    cycle_start = True
                elif cycle_start and line.strip():
                    if line.startswith("  "):
                        # This is part of a cycle
                        module = line.strip()
                        current_cycle.append(module)
                    else:
                        # End of a cycle
                        if current_cycle:
                            cycles.append(current_cycle)
                            current_cycle = []
                        cycle_start = False
            
            # Add the last cycle if there is one
            if current_cycle:
                cycles.append(current_cycle)
        
        return {
            "module": module_path,
            "cycles_found": cycles_found,
            "cycles": cycles,
            "output_svg": f"{output_base}.svg",
            "output_txt": f"{output_base}_cycles.txt"
        }
        
    except subprocess.CalledProcessError as e:
        print(f"Error analyzing dependencies: {e}")
        return {
            "module": module_path,
            "error": str(e),
            "cycles_found": False,
            "cycles": []
        }


def find_python_modules(base_dir):
    """Find all Python modules in the specified directory."""
    modules = []
    for root, dirs, files in os.walk(base_dir):
        # Skip __pycache__ directories and virtual environments
        dirs[:] = [d for d in dirs if d != "__pycache__" and d != "venv" and not d.startswith(".")]
        
        # Check if this directory is a Python package
        if "__init__.py" in files:
            modules.append(root)
    
    return modules


def generate_report(results, output_file="dependency_report.json"):
    """Generate a JSON report of the dependency analysis."""
    report = {
        "timestamp": datetime.now().isoformat(),
        "total_modules_analyzed": len(results),
        "modules_with_cycles": sum(1 for r in results if r["cycles_found"]),
        "results": results
    }
    
    with open(output_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"Report saved to {output_file}")
    
    # Print summary
    print("\nSummary:")
    print(f"Total modules analyzed: {report['total_modules_analyzed']}")
    print(f"Modules with circular dependencies: {report['modules_with_cycles']}")
    
    if report['modules_with_cycles'] > 0:
        print("\nModules with circular dependencies:")
        for result in results:
            if result["cycles_found"]:
                print(f"- {result['module']}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Analyze circular dependencies in Python code")
    parser.add_argument("--module", help="Specific module to analyze")
    parser.add_argument("--all", action="store_true", help="Analyze all modules")
    parser.add_argument("--depth", type=int, help="Maximum depth for dependency analysis")
    parser.add_argument("--output-dir", default="dependency_analysis", help="Directory to save output files")
    parser.add_argument("--report", default="dependency_report.json", help="Output report file")
    
    args = parser.parse_args()
    
    # Install dependencies if needed
    install_dependencies()
    
    results = []
    
    if args.all:
        # Analyze all modules
        modules = find_python_modules("api")
        for module in modules:
            results.append(analyze_dependencies(module, args.output_dir, args.depth))
    elif args.module:
        # Analyze specific module
        results.append(analyze_dependencies(args.module, args.output_dir, args.depth))
    else:
        # Default: analyze the main api module
        results.append(analyze_dependencies("api", args.output_dir, args.depth))
    
    # Generate report
    generate_report(results, args.report)
    
    print("\nAnalysis complete!")


if __name__ == "__main__":
    main()
