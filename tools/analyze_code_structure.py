#!/usr/bin/env python3
"""
<PERSON>ript to analyze code structure and identify common issues.

This script analyzes Python files in the codebase to identify common issues
such as inconsistent naming, long functions, and high complexity.
"""

import os
import sys
import re
import ast
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
import json
from datetime import datetime


class CodeAnalyzer:
    """Class to analyze code structure and identify issues."""

    def __init__(self):
        """Initialize the CodeAnalyzer."""
        self.issues = []
        self.stats = {
            "files_analyzed": 0,
            "lines_of_code": 0,
            "function_count": 0,
            "class_count": 0,
            "import_count": 0,
            "long_functions": 0,
            "complex_functions": 0,
            "undocumented_functions": 0,
            "undocumented_classes": 0,
            "inconsistent_naming": 0,
        }
        
    def analyze_file(self, file_path: str) -> Dict:
        """
        Analyze a Python file for issues.
        
        Args:
            file_path: Path to the Python file
            
        Returns:
            Dict containing analysis results
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            self.stats["files_analyzed"] += 1
            self.stats["lines_of_code"] += len(content.splitlines())
            
            file_issues = []
            
            # Parse the file with AST
            tree = ast.parse(content)
            
            # Analyze imports
            imports = []
            for node in ast.walk(tree):
                if isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for name in node.names:
                            imports.append(name.name)
                    else:  # ImportFrom
                        if node.module:
                            for name in node.names:
                                imports.append(f"{node.module}.{name.name}")
            
            self.stats["import_count"] += len(imports)
            
            # Check for duplicate imports
            duplicate_imports = [imp for imp in imports if imports.count(imp) > 1]
            if duplicate_imports:
                file_issues.append({
                    "type": "duplicate_imports",
                    "message": f"Duplicate imports: {', '.join(set(duplicate_imports))}",
                    "line": 0,
                })
            
            # Analyze functions and classes
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    self.stats["function_count"] += 1
                    
                    # Check function length
                    func_lines = len(ast.unparse(node).splitlines())
                    if func_lines > 50:
                        self.stats["long_functions"] += 1
                        file_issues.append({
                            "type": "long_function",
                            "message": f"Function '{node.name}' is {func_lines} lines long (> 50)",
                            "line": node.lineno,
                        })
                    
                    # Check function complexity (number of branches)
                    branches = sum(1 for n in ast.walk(node) if isinstance(n, (ast.If, ast.For, ast.While, ast.Try)))
                    if branches > 10:
                        self.stats["complex_functions"] += 1
                        file_issues.append({
                            "type": "complex_function",
                            "message": f"Function '{node.name}' has {branches} branches (> 10)",
                            "line": node.lineno,
                        })
                    
                    # Check for docstring
                    if not (node.body and isinstance(node.body[0], ast.Expr) and isinstance(node.body[0].value, ast.Str)):
                        self.stats["undocumented_functions"] += 1
                        file_issues.append({
                            "type": "undocumented_function",
                            "message": f"Function '{node.name}' is missing a docstring",
                            "line": node.lineno,
                        })
                    
                    # Check naming convention (snake_case)
                    if not re.match(r'^[a-z][a-z0-9_]*$', node.name):
                        self.stats["inconsistent_naming"] += 1
                        file_issues.append({
                            "type": "inconsistent_naming",
                            "message": f"Function '{node.name}' does not follow snake_case naming convention",
                            "line": node.lineno,
                        })
                
                elif isinstance(node, ast.ClassDef):
                    self.stats["class_count"] += 1
                    
                    # Check for docstring
                    if not (node.body and isinstance(node.body[0], ast.Expr) and isinstance(node.body[0].value, ast.Str)):
                        self.stats["undocumented_classes"] += 1
                        file_issues.append({
                            "type": "undocumented_class",
                            "message": f"Class '{node.name}' is missing a docstring",
                            "line": node.lineno,
                        })
                    
                    # Check naming convention (PascalCase)
                    if not re.match(r'^[A-Z][a-zA-Z0-9]*$', node.name):
                        self.stats["inconsistent_naming"] += 1
                        file_issues.append({
                            "type": "inconsistent_naming",
                            "message": f"Class '{node.name}' does not follow PascalCase naming convention",
                            "line": node.lineno,
                        })
            
            # Add file issues to overall issues
            if file_issues:
                self.issues.append({
                    "file": file_path,
                    "issues": file_issues,
                })
            
            return {
                "file": file_path,
                "issues": file_issues,
            }
            
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
            return {
                "file": file_path,
                "error": str(e),
            }
    
    def analyze_directory(self, directory: str) -> List[Dict]:
        """
        Analyze all Python files in a directory.
        
        Args:
            directory: Directory to analyze
            
        Returns:
            List of analysis results for each file
        """
        results = []
        
        for root, _, files in os.walk(directory):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    results.append(self.analyze_file(file_path))
        
        return results
    
    def generate_report(self, output_file: str = "code_structure_report.md") -> None:
        """
        Generate a Markdown report of the analysis results.
        
        Args:
            output_file: Path to the output file
        """
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# Code Structure Analysis Report\n\n")
            f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## Summary\n\n")
            f.write(f"- Files analyzed: {self.stats['files_analyzed']}\n")
            f.write(f"- Total lines of code: {self.stats['lines_of_code']}\n")
            f.write(f"- Functions: {self.stats['function_count']}\n")
            f.write(f"- Classes: {self.stats['class_count']}\n")
            f.write(f"- Imports: {self.stats['import_count']}\n\n")
            
            f.write("## Issues\n\n")
            f.write(f"- Long functions (> 50 lines): {self.stats['long_functions']}\n")
            f.write(f"- Complex functions (> 10 branches): {self.stats['complex_functions']}\n")
            f.write(f"- Undocumented functions: {self.stats['undocumented_functions']}\n")
            f.write(f"- Undocumented classes: {self.stats['undocumented_classes']}\n")
            f.write(f"- Inconsistent naming: {self.stats['inconsistent_naming']}\n\n")
            
            f.write("## Files with Issues\n\n")
            for file_issues in self.issues:
                f.write(f"### {file_issues['file']}\n\n")
                for issue in file_issues['issues']:
                    f.write(f"- Line {issue['line']}: {issue['message']}\n")
                f.write("\n")
            
            f.write("## Recommendations\n\n")
            f.write("1. **Refactor long functions** to improve readability and maintainability\n")
            f.write("2. **Reduce complexity** by breaking down complex functions into smaller, focused functions\n")
            f.write("3. **Add docstrings** to all functions and classes\n")
            f.write("4. **Standardize naming conventions** across the codebase\n")
            f.write("5. **Remove duplicate imports** to improve code clarity\n")
    
    def save_json_report(self, output_file: str = "code_structure_report.json") -> None:
        """
        Save the analysis results as JSON.
        
        Args:
            output_file: Path to the output file
        """
        report = {
            "timestamp": datetime.now().isoformat(),
            "stats": self.stats,
            "issues": self.issues,
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2)


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Analyze code structure and identify common issues")
    parser.add_argument("--path", help="Path to file or directory to analyze")
    parser.add_argument("--output", default="code_structure_report.md", help="Output report file")
    parser.add_argument("--json", action="store_true", help="Generate JSON report")
    
    args = parser.parse_args()
    
    analyzer = CodeAnalyzer()
    
    if args.path:
        path = args.path
    else:
        path = "api"  # Default to the api directory
        
    if os.path.isfile(path):
        analyzer.analyze_file(path)
    elif os.path.isdir(path):
        analyzer.analyze_directory(path)
    else:
        print(f"Path not found: {path}")
        return 1
    
    analyzer.generate_report(args.output)
    print(f"Report saved to {args.output}")
    
    if args.json:
        json_output = os.path.splitext(args.output)[0] + ".json"
        analyzer.save_json_report(json_output)
        print(f"JSON report saved to {json_output}")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
