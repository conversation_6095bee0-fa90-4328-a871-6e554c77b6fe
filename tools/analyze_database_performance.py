#!/usr/bin/env python3
"""
Script to analyze database query performance.

This script analyzes SQL queries in the codebase and identifies potential
performance issues such as missing indexes, N+1 queries, and inefficient joins.
"""

import os
import sys
import re
import ast
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple
import json
from datetime import datetime


def install_dependencies():
    """Install required dependencies if not already installed."""
    try:
        import sqlparse
    except ImportError:
        print("Installing required dependencies...")
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "sqlparse"])


class QueryAnalyzer:
    """Class to analyze SQL queries for performance issues."""

    def __init__(self):
        """Initialize the QueryAnalyzer."""
        self.queries = []
        self.issues = []
        self.stats = {
            "files_analyzed": 0,
            "queries_found": 0,
            "missing_indexes": 0,
            "n_plus_one": 0,
            "inefficient_joins": 0,
            "cartesian_products": 0,
            "select_star": 0,
            "no_limit": 0,
        }
        
    def extract_queries_from_file(self, file_path: str) -> List[Dict]:
        """
        Extract SQL queries from a Python file.
        
        Args:
            file_path: Path to the Python file
            
        Returns:
            List of dictionaries containing query information
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            self.stats["files_analyzed"] += 1
            
            # Parse the file with AST
            tree = ast.parse(content)
            
            # Extract string literals that might contain SQL
            queries = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Str) and self._is_sql_query(node.s):
                    queries.append({
                        "file": file_path,
                        "line": node.lineno,
                        "query": node.s,
                        "context": self._get_context(content, node.lineno)
                    })
                elif isinstance(node, ast.Call) and hasattr(node, 'func') and hasattr(node.func, 'attr'):
                    # Look for execute, query, or similar method calls
                    if node.func.attr in ['execute', 'query', 'raw', 'text', 'sql']:
                        for arg in node.args:
                            if isinstance(arg, ast.Str) and self._is_sql_query(arg.s):
                                queries.append({
                                    "file": file_path,
                                    "line": arg.lineno,
                                    "query": arg.s,
                                    "context": self._get_context(content, arg.lineno)
                                })
            
            self.stats["queries_found"] += len(queries)
            self.queries.extend(queries)
            
            return queries
            
        except Exception as e:
            print(f"Error extracting queries from {file_path}: {e}")
            return []
    
    def _is_sql_query(self, text: str) -> bool:
        """
        Check if a string is likely an SQL query.
        
        Args:
            text: String to check
            
        Returns:
            True if the string is likely an SQL query
        """
        # Simple heuristic: contains SQL keywords
        sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'FROM', 'WHERE', 'JOIN']
        text_upper = text.upper()
        return any(keyword in text_upper for keyword in sql_keywords)
    
    def _get_context(self, content: str, line_number: int, context_lines: int = 2) -> str:
        """
        Get the context around a line in a file.
        
        Args:
            content: File content
            line_number: Line number to get context for
            context_lines: Number of lines of context before and after
            
        Returns:
            String containing the context
        """
        lines = content.splitlines()
        start = max(0, line_number - context_lines - 1)
        end = min(len(lines), line_number + context_lines)
        
        context = []
        for i in range(start, end):
            prefix = '> ' if i == line_number - 1 else '  '
            context.append(f"{prefix}{i+1}: {lines[i]}")
        
        return '\n'.join(context)
    
    def analyze_query(self, query_info: Dict) -> List[Dict]:
        """
        Analyze a query for performance issues.
        
        Args:
            query_info: Dictionary containing query information
            
        Returns:
            List of dictionaries containing issue information
        """
        import sqlparse
        
        query = query_info["query"]
        issues = []
        
        # Parse the query
        parsed = sqlparse.parse(query)
        if not parsed:
            return issues
        
        stmt = parsed[0]
        
        # Check for SELECT *
        if stmt.get_type() == 'SELECT':
            select_tokens = [token for token in stmt.tokens if isinstance(token, sqlparse.sql.IdentifierList)]
            for token_list in select_tokens:
                for token in token_list.tokens:
                    if token.value == '*':
                        self.stats["select_star"] += 1
                        issues.append({
                            "type": "select_star",
                            "message": "Using SELECT * can retrieve unnecessary columns and impact performance",
                            "severity": "medium"
                        })
        
        # Check for missing WHERE clause in SELECT
        if stmt.get_type() == 'SELECT':
            where_clause = any(token.value.upper() == 'WHERE' for token in stmt.tokens)
            if not where_clause:
                issues.append({
                    "type": "no_where_clause",
                    "message": "SELECT without WHERE clause may retrieve too many rows",
                    "severity": "high"
                })
        
        # Check for missing LIMIT clause in SELECT
        if stmt.get_type() == 'SELECT':
            limit_clause = any(token.value.upper() == 'LIMIT' for token in stmt.tokens)
            if not limit_clause:
                self.stats["no_limit"] += 1
                issues.append({
                    "type": "no_limit",
                    "message": "SELECT without LIMIT clause may retrieve too many rows",
                    "severity": "medium"
                })
        
        # Check for inefficient JOINs
        join_count = query.upper().count('JOIN')
        if join_count > 3:
            self.stats["inefficient_joins"] += 1
            issues.append({
                "type": "many_joins",
                "message": f"Query has {join_count} JOINs which may impact performance",
                "severity": "medium"
            })
        
        # Check for potential cartesian products
        if 'JOIN' in query.upper() and 'ON' not in query.upper() and 'USING' not in query.upper():
            self.stats["cartesian_products"] += 1
            issues.append({
                "type": "cartesian_product",
                "message": "JOIN without ON or USING clause may result in a cartesian product",
                "severity": "high"
            })
        
        # Check for potential N+1 query issues (heuristic)
        if query_info["context"] and re.search(r'for\s+.*\s+in\s+.*:', query_info["context"]):
            self.stats["n_plus_one"] += 1
            issues.append({
                "type": "n_plus_one",
                "message": "Query inside a loop may cause N+1 query problem",
                "severity": "high"
            })
        
        # Add issues to the query info
        query_info["issues"] = issues
        
        # Add to overall issues if there are any
        if issues:
            self.issues.append(query_info)
        
        return issues
    
    def analyze_directory(self, directory: str) -> List[Dict]:
        """
        Analyze all Python files in a directory for SQL queries.
        
        Args:
            directory: Directory to analyze
            
        Returns:
            List of dictionaries containing query information with issues
        """
        for root, _, files in os.walk(directory):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    queries = self.extract_queries_from_file(file_path)
                    for query in queries:
                        self.analyze_query(query)
        
        return self.issues
    
    def generate_report(self, output_file: str = "database_performance_report.md") -> None:
        """
        Generate a Markdown report of the analysis results.
        
        Args:
            output_file: Path to the output file
        """
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# Database Query Performance Analysis Report\n\n")
            f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## Summary\n\n")
            f.write(f"- Files analyzed: {self.stats['files_analyzed']}\n")
            f.write(f"- Queries found: {self.stats['queries_found']}\n")
            f.write(f"- Queries with issues: {len(self.issues)}\n\n")
            
            f.write("## Issues by Type\n\n")
            f.write(f"- SELECT * queries: {self.stats['select_star']}\n")
            f.write(f"- Queries without LIMIT: {self.stats['no_limit']}\n")
            f.write(f"- Potential N+1 queries: {self.stats['n_plus_one']}\n")
            f.write(f"- Inefficient joins: {self.stats['inefficient_joins']}\n")
            f.write(f"- Potential cartesian products: {self.stats['cartesian_products']}\n\n")
            
            f.write("## Queries with Issues\n\n")
            for query_info in self.issues:
                f.write(f"### {query_info['file']}:{query_info['line']}\n\n")
                f.write("```sql\n")
                f.write(query_info['query'])
                f.write("\n```\n\n")
                
                f.write("**Context:**\n\n")
                f.write("```python\n")
                f.write(query_info['context'])
                f.write("\n```\n\n")
                
                f.write("**Issues:**\n\n")
                for issue in query_info['issues']:
                    severity_marker = "🔴" if issue['severity'] == 'high' else "🟠" if issue['severity'] == 'medium' else "🟡"
                    f.write(f"- {severity_marker} **{issue['type']}**: {issue['message']}\n")
                f.write("\n")
            
            f.write("## Recommendations\n\n")
            f.write("1. **Avoid SELECT * queries** - Specify only the columns you need\n")
            f.write("2. **Use LIMIT clauses** - Always limit the number of rows returned\n")
            f.write("3. **Fix N+1 queries** - Use JOIN or subqueries instead of querying in loops\n")
            f.write("4. **Optimize complex joins** - Consider denormalizing or using views\n")
            f.write("5. **Add indexes** - Ensure columns used in WHERE, JOIN, and ORDER BY are indexed\n")
    
    def save_json_report(self, output_file: str = "database_performance_report.json") -> None:
        """
        Save the analysis results as JSON.
        
        Args:
            output_file: Path to the output file
        """
        report = {
            "timestamp": datetime.now().isoformat(),
            "stats": self.stats,
            "issues": self.issues,
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2)


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Analyze database query performance")
    parser.add_argument("--path", help="Path to file or directory to analyze")
    parser.add_argument("--output", default="database_performance_report.md", help="Output report file")
    parser.add_argument("--json", action="store_true", help="Generate JSON report")
    
    args = parser.parse_args()
    
    # Install dependencies if needed
    install_dependencies()
    
    analyzer = QueryAnalyzer()
    
    if args.path:
        path = args.path
    else:
        path = "api"  # Default to the api directory
        
    if os.path.isfile(path):
        analyzer.extract_queries_from_file(path)
    elif os.path.isdir(path):
        analyzer.analyze_directory(path)
    else:
        print(f"Path not found: {path}")
        return 1
    
    analyzer.generate_report(args.output)
    print(f"Report saved to {args.output}")
    
    if args.json:
        json_output = os.path.splitext(args.output)[0] + ".json"
        analyzer.save_json_report(json_output)
        print(f"JSON report saved to {json_output}")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
