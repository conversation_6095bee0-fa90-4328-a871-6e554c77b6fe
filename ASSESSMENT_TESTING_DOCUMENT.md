# Assessment Testing Framework

## Overview

This document outlines the testing approach for the Assessment Management feature of the Regression Rigor platform. The testing framework includes both API tests using pytest and UI tests using <PERSON><PERSON> to ensure comprehensive test coverage of the assessment functionality.

## Test Structure

### API Tests

API tests are organized into four main files:

1. **test_assessment_api.py**: Basic CRUD operations for assessments and test executions
2. **test_assessment_api_extended.py**: Advanced tests covering edge cases, validation, and error handling
3. **test_execution_api.py**: Dedicated tests for test execution functionality
4. **test_bulk_assessment_operations.py**: Tests for bulk operations on assessments

### UI Tests

UI tests using <PERSON>wright are organized into four files in the `ui/tests/assessments/` directory:

1. **assessment-list.spec.js**: Tests for the assessment list page functionality
2. **assessment-form.spec.js**: Tests for assessment creation and editing forms
3. **assessment-detail.spec.js**: Tests for the assessment detail view and its tabs
4. **assessment-report.spec.js**: Tests for assessment report generation and display

## Key Testing Features

### API Testing Highlights

1. **Comprehensive CRUD Coverage**:
   - Testing all CRUD operations for assessments and test executions
   - Testing endpoint behavior for various HTTP methods
   - Validating response structures and status codes

2. **Advanced Query Parameter Testing**:
   - Filtering by status, type, date ranges, etc.
   - Sorting by different fields (ascending and descending)
   - Pagination with different page sizes and offsets

3. **Validation Testing**:
   - Required field validation
   - Data type validation
   - Date range validation (start date before end date)
   - Status and type enum validation

4. **Authorization Testing**:
   - Role-based access control for admin, operator, and viewer roles
   - Testing access restrictions for different HTTP methods by role

5. **Bulk Operation Testing**:
   - Bulk create, update, and delete operations
   - Partial success handling for bulk operations
   - Bulk relationship management (e.g., assigning campaigns)

### UI Testing Highlights

1. **Page Navigation Flow Testing**:
   - Testing navigation between assessment pages
   - Verifying URL changes and routing

2. **Form Interaction Testing**:
   - Form field validation
   - Submission and cancellation handling
   - Error display verification

3. **Data Display Testing**:
   - Verifying that assessment data is correctly displayed
   - Testing data filtering and sorting in the UI
   - Checking data refreshing after operations

4. **User Interaction Testing**:
   - Button clicks, form fills, and dialog interactions
   - Testing modals and confirmation dialogs
   - Navigation between tabs and sections

5. **Visual Verification**:
   - Testing the display of UI components like status chips
   - Verifying that critical information is visible
   - Checking that reports display charts and data correctly

## Running the Tests

A helper script `scripts/run_assessment_tests.sh` has been created to run the tests:

```bash
# Run all tests (API and UI)
./scripts/run_assessment_tests.sh

# Run only API tests
./scripts/run_assessment_tests.sh --api

# Run only UI tests
./scripts/run_assessment_tests.sh --ui

# Run a specific test file
./scripts/run_assessment_tests.sh --file=test_execution_api.py

# Run tests with a specific pattern
./scripts/run_assessment_tests.sh --testcase=validation

# Show help
./scripts/run_assessment_tests.sh --help
```

## Test Data Management

The test suite is designed to be self-contained, creating necessary test data at the beginning of tests and cleaning up after completion. This approach ensures:

1. **Test Isolation**: Each test operates independently without relying on data from other tests
2. **Repeatability**: Tests can be run multiple times with the same results
3. **Clean Environment**: The system state after testing is similar to before testing

## Extending the Test Suite

To add new tests to the Assessment Testing Framework:

1. **API Tests**:
   - Add new test functions to the appropriate test file based on functionality
   - Follow the naming convention `test_<functionality>_<scenario>`
   - Use existing fixtures for common setup (e.g., test tokens, database session)
   - Document the purpose of each test in its docstring

2. **UI Tests**:
   - Add new test functions to the appropriate spec file
   - Use the existing authentication setup
   - Follow the pattern of navigating to the relevant page, performing actions, and asserting results
   - Use descriptive test names that clearly indicate what is being tested

## Future Enhancements

1. **Performance Testing**: Add tests for API response times and UI rendering performance
2. **Visual Regression Testing**: Implement visual snapshot testing for UI components
3. **Cross-browser Testing**: Expand Playwright tests to run on multiple browsers
4. **Test Coverage Analysis**: Implement coverage reporting to identify untested code paths
5. **Accessibility Testing**: Add tests to verify accessibility compliance

## Conclusion

The Assessment Testing Framework provides comprehensive coverage of both API and UI functionality for the Assessment Management feature. By combining API tests with UI tests, we ensure that both the backend services and the user interface work correctly and consistently. 