from flask import Flask, render_template, flash, redirect, url_for, request, jsonify
from flask_login import Login<PERSON><PERSON><PERSON>, UserMixin, login_user, logout_user, current_user, login_required
from werkzeug.security import generate_password_hash, check_password_hash
import os
import logging
from datetime import datetime, timezone
import psycopg2
from psycopg2.extras import RealDictCursor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.secret_key = os.environ.get("SESSION_SECRET", "default_secret_key")

# Configure database connection
DATABASE_URL = os.environ.get("DATABASE_URL", "postgresql://regrigor:regrigor_password@localhost:5440/regrigor_db")

def get_db_connection():
    conn = psycopg2.connect(DATABASE_URL)
    conn.autocommit = True
    return conn

# User model for Flask-Login
class User(UserMixin):
    def __init__(self, id, username, email, password_hash):
        self.id = id
        self.username = username
        self.email = email
        self.password_hash = password_hash
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    @staticmethod
    def get_by_id(user_id):
        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            cursor.execute('SELECT * FROM flask_users WHERE id = %s', (user_id,))
            user_data = cursor.fetchone()
            cursor.close()
            conn.close()
            
            if user_data:
                return User(
                    id=user_data['id'],
                    username=user_data['username'],
                    email=user_data['email'],
                    password_hash=user_data['password_hash']
                )
            return None
        except Exception as e:
            logger.error(f"Error retrieving user: {e}")
            return None
    
    @staticmethod
    def get_by_username(username):
        try:
            conn = get_db_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            cursor.execute('SELECT * FROM flask_users WHERE username = %s', (username,))
            user_data = cursor.fetchone()
            cursor.close()
            conn.close()
            
            if user_data:
                return User(
                    id=user_data['id'],
                    username=user_data['username'],
                    email=user_data['email'],
                    password_hash=user_data['password_hash']
                )
            return None
        except Exception as e:
            logger.error(f"Error retrieving user: {e}")
            return None

# Setup Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

@login_manager.user_loader
def load_user(user_id):
    return User.get_by_id(user_id)

# Create tables if they don't exist
def init_db():
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Create users table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS flask_users (
            id SERIAL PRIMARY KEY,
            username VARCHAR(64) UNIQUE NOT NULL,
            email VARCHAR(120) UNIQUE NOT NULL,
            password_hash VARCHAR(256),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Check if we need to create a test user
        cursor.execute('SELECT COUNT(*) FROM flask_users')
        count = cursor.fetchone()[0]
        
        if count == 0:
            # Create test user
            password_hash = generate_password_hash('password')
            cursor.execute(
                'INSERT INTO flask_users (username, email, password_hash) VALUES (%s, %s, %s)',
                ('admin', '<EMAIL>', password_hash)
            )
            logger.info("Created test user: admin/password")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {e}")

# Routes
@app.route('/')
def index():
    """Render the main dashboard page."""
    return render_template('dashboard.html')

@app.route('/documentation')
def documentation_index():
    """Render the documentation page."""
    return render_template('documentation.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Handle user login."""
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        user = User.get_by_username(username)
        
        if user and user.check_password(password):
            login_user(user)
            next_page = request.args.get('next')
            return redirect(next_page or url_for('index'))
        
        flash('Invalid username or password', 'danger')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """Handle user logout."""
    logout_user()
    return redirect(url_for('index'))

@app.route('/register', methods=['GET', 'POST'])
def register():
    """Handle user registration."""
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        
        # Check if username already exists
        existing_user = User.get_by_username(username)
        if existing_user:
            flash('Username already exists. Please choose a different one.', 'danger')
            return render_template('registration.html')
        
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Hash the password
            password_hash = generate_password_hash(password)
            
            # Insert the new user
            cursor.execute(
                'INSERT INTO flask_users (username, email, password_hash) VALUES (%s, %s, %s)',
                (username, email, password_hash)
            )
            
            conn.commit()
            cursor.close()
            conn.close()
            
            flash('Registration successful! Please login.', 'success')
            return redirect(url_for('login'))
        except Exception as e:
            logger.error(f"Error registering user: {e}")
            flash('An error occurred during registration. Please try again.', 'danger')
    
    return render_template('registration.html')

@app.route('/settings')
@login_required
def settings():
    """User settings page."""
    return render_template('settings.html')

# API routes for testing
@app.route('/api/status')
def api_status():
    """API status endpoint."""
    return jsonify({
        "status": "online",
        "timestamp": datetime.now(timezone.utc).isoformat()
    })

@app.route('/api/db-status')
def db_status():
    """Database status check endpoint."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute('SELECT version();')
        version = cursor.fetchone()['version']
        cursor.close()
        conn.close()
        return jsonify({
            "status": "connected",
            "db_version": version
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

if __name__ == '__main__':
    # Initialize the database
    init_db()
    app.run(debug=True, host='0.0.0.0', port=5000) 