"""
Threat-Defense Mapping System for RegressionRigor API.

This module implements bidirectional mapping between MITRE ATT&CK techniques
and D3FEND countermeasures using STIX 2.1 as the underlying representation.
"""
from typing import List, Dict, Optional, Union, Set, Tuple
import logging
import stix2
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from api.models.mitre import MitreTechnique, MitreTactic, MitreVersion
from api.models.d3fend import D3FENDConcept, D3FENDVersion
from api.models.stix import StixDB
from api.models.stix_d3fend import D3fendMapping, D3fendMappingDB
from api.utils.stix_d3fend_utils import map_stix_bundle, import_stix_mappings

logger = logging.getLogger(__name__)

class ThreatDefenseMapper:
    """Implements bidirectional mapping between attack techniques and defensive countermeasures."""

    def __init__(self, db: Session):
        """Initialize the mapper with a database session.
        
        Args:
            db: The database session to use for queries
        """
        self.db = db
        
    def generate_attack_stix_bundle(self, techniques: List[str]) -> stix2.Bundle:
        """Generate a STIX bundle for ATT&CK techniques.
        
        Args:
            techniques: List of technique IDs (e.g., T1234)
            
        Returns:
            stix2.Bundle: A STIX bundle with attack patterns
        """
        # Get current MITRE version
        mitre_version = self.db.query(MitreVersion).filter(MitreVersion.is_current == True).first()
        if not mitre_version:
            raise ValueError("No current MITRE version found")
            
        # Get technique data
        technique_objects = self.db.query(MitreTechnique).filter(
            and_(
                MitreTechnique.technique_id.in_(techniques),
                MitreTechnique.version_id == mitre_version.id
            )
        ).all()
        
        if not technique_objects:
            raise ValueError(f"No techniques found for IDs: {techniques}")
            
        # Create STIX objects
        stix_objects = []
        for technique in technique_objects:
            # Create attack pattern
            attack_pattern = stix2.AttackPattern(
                id=f"attack-pattern--{technique.technique_id.lower()}",
                name=technique.name,
                description=technique.description or "",
                created=datetime.utcnow(),
                modified=datetime.utcnow(),
                external_references=[
                    {
                        "source_name": "mitre-attack",
                        "external_id": technique.technique_id
                    }
                ]
            )
            stix_objects.append(attack_pattern)
            
            # Add relationships to tactics
            for tactic in technique.tactics:
                relationship = stix2.Relationship(
                    id=f"relationship--{technique.technique_id.lower()}-{tactic.tactic_id.lower()}",
                    relationship_type="uses",
                    source_ref=attack_pattern.id,
                    target_ref=f"x-mitre-tactic--{tactic.tactic_id.lower()}",
                    created=datetime.utcnow(),
                    modified=datetime.utcnow()
                )
                stix_objects.append(relationship)
                
        return stix2.Bundle(objects=stix_objects)
        
    def find_d3fend_countermeasures(self, technique_ids: List[str]) -> Dict[str, List[Dict]]:
        """Find D3FEND countermeasures for ATT&CK techniques.
        
        Args:
            technique_ids: List of ATT&CK technique IDs (e.g., T1234)
            
        Returns:
            Dict mapping technique IDs to lists of D3FEND countermeasures
        """
        # Get mappings from database
        results = {}
        
        for technique_id in technique_ids:
            stix_id = f"attack-pattern--{technique_id.lower()}"
            
            # Query mappings
            mappings = self.db.query(D3fendMappingDB).filter(
                D3fendMappingDB.stix_id == stix_id
            ).all()
            
            if not mappings:
                results[technique_id] = []
                continue
                
            # Get D3FEND concepts
            d3fend_ids = [m.d3fend_id for m in mappings]
            concepts = self.db.query(D3FENDConcept).filter(
                D3FENDConcept.uri.in_(d3fend_ids)
            ).all()
            
            # Map concepts to technique
            concept_map = {c.uri: c for c in concepts}
            
            results[technique_id] = [
                {
                    "d3fend_id": m.d3fend_id,
                    "name": concept_map.get(m.d3fend_id, D3FENDConcept()).name if m.d3fend_id in concept_map else "Unknown",
                    "type": m.mapping_type,
                    "confidence": m.confidence
                }
                for m in mappings
            ]
            
        return results
        
    def generate_defense_stix_bundle(self, d3fend_ids: List[str]) -> stix2.Bundle:
        """Generate a STIX bundle for D3FEND countermeasures.
        
        Args:
            d3fend_ids: List of D3FEND concept URIs
            
        Returns:
            stix2.Bundle: A STIX bundle with course-of-action objects
        """
        # Get current D3FEND version
        d3fend_version = self.db.query(D3FENDVersion).filter(D3FENDVersion.is_current == True).first()
        if not d3fend_version:
            raise ValueError("No current D3FEND version found")
            
        # Get concept data
        concepts = self.db.query(D3FENDConcept).filter(
            and_(
                D3FENDConcept.uri.in_(d3fend_ids),
                D3FENDConcept.version_id == d3fend_version.id
            )
        ).all()
        
        if not concepts:
            raise ValueError(f"No D3FEND concepts found for URIs: {d3fend_ids}")
            
        # Create STIX objects
        stix_objects = []
        for concept in concepts:
            # Create course of action
            coa_id = concept.uri.split('#')[-1].lower()
            course_of_action = stix2.CourseOfAction(
                id=f"course-of-action--{coa_id}",
                name=concept.name,
                description=concept.definition or "",
                created=datetime.utcnow(),
                modified=datetime.utcnow(),
                external_references=[
                    {
                        "source_name": "d3fend",
                        "external_id": concept.uri,
                        "url": concept.uri
                    }
                ]
            )
            stix_objects.append(course_of_action)
            
            # Find and add mitigates relationships
            mappings = self.db.query(D3fendMappingDB).filter(
                D3fendMappingDB.d3fend_id == concept.uri
            ).all()
            
            for mapping in mappings:
                relationship = stix2.Relationship(
                    id=f"relationship--{coa_id}-{mapping.stix_id.split('--')[-1]}",
                    relationship_type="mitigates",
                    source_ref=course_of_action.id,
                    target_ref=mapping.stix_id,
                    created=datetime.utcnow(),
                    modified=datetime.utcnow()
                )
                stix_objects.append(relationship)
                
        return stix2.Bundle(objects=stix_objects)
        
    def calculate_defense_coverage(self, technique_ids: List[str]) -> Dict[str, float]:
        """Calculate defensive coverage for ATT&CK techniques.
        
        Args:
            technique_ids: List of ATT&CK technique IDs
            
        Returns:
            Dict mapping technique IDs to coverage scores (0-1)
        """
        countermeasures = self.find_d3fend_countermeasures(technique_ids)
        coverage = {}
        
        for technique_id, measures in countermeasures.items():
            # Calculate weighted coverage based on confidence scores
            if not measures:
                coverage[technique_id] = 0.0
                continue
                
            total_confidence = sum(m.get("confidence", 0.0) for m in measures)
            # Normalize to 0-1 scale with diminishing returns for multiple controls
            coverage[technique_id] = min(1.0, total_confidence / (1.0 + 0.5 * (len(measures) - 1)))
            
        return coverage
        
    def get_attack_path_coverage(self, technique_sequence: List[str]) -> Dict[str, Dict]:
        """Calculate coverage for an attack path (sequence of techniques).
        
        Args:
            technique_sequence: Ordered list of technique IDs representing an attack path
            
        Returns:
            Dict with overall coverage and per-technique coverage details
        """
        if not technique_sequence:
            return {"overall_coverage": 0.0, "techniques": {}}
            
        # Get individual coverage scores
        coverage = self.calculate_defense_coverage(technique_sequence)
        
        # Calculate overall path coverage (weakest link)
        min_coverage = min(coverage.values()) if coverage else 0.0
        
        # Calculate average coverage
        avg_coverage = sum(coverage.values()) / len(coverage) if coverage else 0.0
        
        return {
            "overall_coverage": min_coverage,
            "average_coverage": avg_coverage,
            "techniques": coverage
        }
        
    def map_new_d3fend_control(self, d3fend_uri: str, technique_ids: List[str], 
                               confidence: float = 0.8) -> List[D3fendMappingDB]:
        """Create new mappings between a D3FEND control and ATT&CK techniques.
        
        Args:
            d3fend_uri: URI of the D3FEND concept
            technique_ids: List of ATT&CK technique IDs to map to
            confidence: Confidence score for the mapping (0-1)
            
        Returns:
            List of created mapping records
        """
        # Verify D3FEND concept exists
        concept = self.db.query(D3FENDConcept).filter(D3FENDConcept.uri == d3fend_uri).first()
        if not concept:
            raise ValueError(f"D3FEND concept not found: {d3fend_uri}")
            
        # Create mappings
        mappings = []
        for technique_id in technique_ids:
            stix_id = f"attack-pattern--{technique_id.lower()}"
            
            # Check if mapping already exists
            existing = self.db.query(D3fendMappingDB).filter(
                and_(
                    D3fendMappingDB.stix_id == stix_id,
                    D3fendMappingDB.d3fend_id == d3fend_uri
                )
            ).first()
            
            if existing:
                # Update confidence if needed
                if existing.confidence != confidence:
                    existing.confidence = confidence
                    self.db.add(existing)
                mappings.append(existing)
                continue
                
            # Create new mapping
            mapping_db = D3fendMappingDB(
                id=f"mapping--{stix_id}-{d3fend_uri.split('#')[-1]}",
                stix_id=stix_id,
                d3fend_id=d3fend_uri,
                mapping_type="mitigates",
                confidence=confidence,
                mapping_metadata={
                    "created": datetime.utcnow().isoformat(),
                    "created_by": "ThreatDefenseMapper",
                    "technique_id": technique_id,
                    "d3fend_name": concept.name
                }
            )
            self.db.add(mapping_db)
            mappings.append(mapping_db)
            
        self.db.commit()
        return mappings
        
    def generate_comprehensive_stix_bundle(self, technique_ids: List[str]) -> stix2.Bundle:
        """Generate a comprehensive STIX bundle with both ATT&CK and D3FEND objects.
        
        Args:
            technique_ids: List of ATT&CK technique IDs
            
        Returns:
            stix2.Bundle: A STIX bundle with attack patterns, courses of action, and relationships
        """
        # Generate attack bundle
        attack_bundle = self.generate_attack_stix_bundle(technique_ids)
        
        # Find relevant D3FEND countermeasures
        countermeasures = self.find_d3fend_countermeasures(technique_ids)
        d3fend_ids = [
            measure["d3fend_id"]
            for measures in countermeasures.values()
            for measure in measures
        ]
        
        # Generate defense bundle
        defense_bundle = self.generate_defense_stix_bundle(d3fend_ids)
        
        # Combine objects
        all_objects = list(attack_bundle.objects) + list(defense_bundle.objects)
        
        # Remove duplicates (by ID)
        unique_objects = {}
        for obj in all_objects:
            unique_objects[obj.id] = obj
            
        return stix2.Bundle(objects=list(unique_objects.values()))
        
    def recommend_defense_improvements(self, technique_ids: List[str], 
                                      min_coverage: float = 0.7) -> Dict[str, List[Dict]]:
        """Recommend D3FEND controls to improve coverage.
        
        Args:
            technique_ids: List of ATT&CK technique IDs
            min_coverage: Minimum desired coverage threshold
            
        Returns:
            Dict mapping technique IDs to recommended D3FEND controls
        """
        # Get current coverage
        current_coverage = self.calculate_defense_coverage(technique_ids)
        
        # Find techniques below threshold
        below_threshold = [
            tid for tid, score in current_coverage.items()
            if score < min_coverage
        ]
        
        if not below_threshold:
            return {}
            
        # Get current D3FEND version
        d3fend_version = self.db.query(D3FENDVersion).filter(D3FENDVersion.is_current == True).first()
        if not d3fend_version:
            raise ValueError("No current D3FEND version found")
        
        # Find potential controls for each technique
        recommendations = {}
        for technique_id in below_threshold:
            # Get technique details
            technique = self.db.query(MitreTechnique).filter(
                MitreTechnique.technique_id == technique_id
            ).first()
            
            if not technique:
                continue
                
            # Find tactics for this technique
            tactic_ids = [t.tactic_id for t in technique.tactics]
            
            # Find D3FEND controls that mitigate techniques in these tactics
            stix_pattern = f"attack-pattern--{technique_id.lower()}"
            
            # Find existing mappings for similar techniques
            similar_mappings = self.db.query(D3fendMappingDB).join(
                StixDB, D3fendMappingDB.stix_id == StixDB.stix_id
            ).filter(
                StixDB.type == "attack-pattern"
            ).all()
            
            # Count occurrences of each D3FEND control
            control_counts = {}
            for mapping in similar_mappings:
                if mapping.d3fend_id not in control_counts:
                    control_counts[mapping.d3fend_id] = 0
                control_counts[mapping.d3fend_id] += 1
                
            # Get top controls
            top_controls = sorted(
                control_counts.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:5]
            
            # Get control details
            control_ids = [c[0] for c in top_controls]
            controls = self.db.query(D3FENDConcept).filter(
                and_(
                    D3FENDConcept.uri.in_(control_ids),
                    D3FENDConcept.version_id == d3fend_version.id
                )
            ).all()
            
            # Format recommendations
            recommendations[technique_id] = [
                {
                    "d3fend_id": c.uri,
                    "name": c.name,
                    "type": "mitigates",
                    "definition": c.definition,
                    "frequency": control_counts.get(c.uri, 0),
                    "confidence": 0.7  # Default recommendation confidence
                }
                for c in controls
            ]
            
        return recommendations