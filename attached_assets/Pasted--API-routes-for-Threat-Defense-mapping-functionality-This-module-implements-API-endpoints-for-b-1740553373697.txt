"""
API routes for Threat-Defense mapping functionality.

This module implements API endpoints for bidirectional mapping between
MITRE ATT&CK techniques and D3FEND countermeasures.
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Optional, Union
from pydantic import BaseModel, Field, validator
import json
import logging
from datetime import datetime

from api.database import get_db
from api.models.stix import StixDB
from api.models.stix_d3fend import D3fendMappingDB
from api.models.d3fend import D3FENDConcept
from api.models.mitre import MitreTechnique
from api.utils.i18n import _
from api.threat_defense.mapper import ThreatDefenseMapper

router = APIRouter()
logger = logging.getLogger(__name__)

class TechniqueList(BaseModel):
    """List of ATT&CK technique IDs."""
    techniques: List[str] = Field(..., description="List of ATT&CK technique IDs")
    
    @validator('techniques')
    def validate_techniques(cls, v):
        """Validate technique IDs follow the correct format."""
        for technique_id in v:
            if not technique_id.startswith('T'):
                raise ValueError(f"Invalid technique ID format: {technique_id}")
        return v

class D3FendControlList(BaseModel):
    """List of D3FEND control URIs."""
    controls: List[str] = Field(..., description="List of D3FEND control URIs")

class MappingRequest(BaseModel):
    """Request to create mappings between D3FEND controls and ATT&CK techniques."""
    d3fend_uri: str = Field(..., description="URI of the D3FEND concept")
    technique_ids: List[str] = Field(..., description="List of ATT&CK technique IDs")
    confidence: float = Field(0.8, description="Confidence score for the mapping (0-1)", ge=0.0, le=1.0)

class CoverageResponse(BaseModel):
    """Response containing defensive coverage information."""
    overall_coverage: float = Field(..., description="Overall defensive coverage (0-1)")
    average_coverage: float = Field(..., description="Average defensive coverage across techniques (0-1)")
    techniques: Dict[str, float] = Field(..., description="Coverage scores for individual techniques")
    
class CountermeasureResponse(BaseModel):
    """Response containing D3FEND countermeasures for techniques."""
    countermeasures: Dict[str, List[Dict]] = Field(
        ..., 
        description="Dict mapping technique IDs to lists of D3FEND countermeasures"
    )

class RecommendationsResponse(BaseModel):
    """Response containing recommended D3FEND controls to improve coverage."""
    recommendations: Dict[str, List[Dict]] = Field(
        ..., 
        description="Dict mapping technique IDs to recommended D3FEND controls"
    )

@router.post("/controls", response_model=CountermeasureResponse)
async def get_d3fend_countermeasures(
    request: TechniqueList,
    db: Session = Depends(get_db)
):
    """Get D3FEND countermeasures for ATT&CK techniques.
    
    Args:
        request: Request containing a list of ATT&CK technique IDs
        db: Database session
        
    Returns:
        Dict mapping technique IDs to lists of D3FEND countermeasures
    """
    mapper = ThreatDefenseMapper(db)
    try:
        countermeasures = mapper.find_d3fend_countermeasures(request.techniques)
        return CountermeasureResponse(countermeasures=countermeasures)
    except Exception as e:
        logger.error(f"Error finding countermeasures: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=_("Error finding countermeasures: {}").format(str(e))
        )

@router.post("/coverage", response_model=CoverageResponse)
async def calculate_attack_path_coverage(
    request: TechniqueList,
    db: Session = Depends(get_db)
):
    """Calculate defensive coverage for an attack path.
    
    Args:
        request: Request containing an ordered list of technique IDs representing an attack path
        db: Database session
        
    Returns:
        Dict with overall coverage and per-technique coverage details
    """
    mapper = ThreatDefenseMapper(db)
    try:
        coverage = mapper.get_attack_path_coverage(request.techniques)
        return CoverageResponse(
            overall_coverage=coverage["overall_coverage"],
            average_coverage=coverage["average_coverage"],
            techniques=coverage["techniques"]
        )
    except Exception as e:
        logger.error(f"Error calculating coverage: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=_("Error calculating coverage: {}").format(str(e))
        )

@router.post("/recommendations", response_model=RecommendationsResponse)
async def recommend_defense_improvements(
    request: TechniqueList,
    min_coverage: float = Query(0.7, ge=0.0, le=1.0),
    db: Session = Depends(get_db)
):
    """Recommend D3FEND controls to improve coverage.
    
    Args:
        request: Request containing a list of ATT&CK technique IDs
        min_coverage: Minimum desired coverage threshold
        db: Database session
        
    Returns:
        Dict mapping technique IDs to recommended D3FEND controls
    """
    mapper = ThreatDefenseMapper(db)
    try:
        recommendations = mapper.recommend_defense_improvements(
            request.techniques, 
            min_coverage=min_coverage
        )
        return RecommendationsResponse(recommendations=recommendations)
    except Exception as e:
        logger.error(f"Error generating recommendations: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=_("Error generating recommendations: {}").format(str(e))
        )

@router.post("/map", response_model=List[Dict])
async def create_d3fend_mapping(
    request: MappingRequest,
    db: Session = Depends(get_db)
):
    """Create new mappings between a D3FEND control and ATT&CK techniques.
    
    Args:
        request: Request containing mapping information
        db: Database session
        
    Returns:
        List of created mapping records
    """
    mapper = ThreatDefenseMapper(db)
    try:
        mappings = mapper.map_new_d3fend_control(
            request.d3fend_uri,
            request.technique_ids,
            confidence=request.confidence
        )
        return [
            {
                "id": m.id,
                "stix_id": m.stix_id,
                "d3fend_id": m.d3fend_id,
                "mapping_type": m.mapping_type,
                "confidence": m.confidence
            }
            for m in mappings
        ]
    except ValueError as e:
        raise HTTPException(
            status_code=404,
            detail=_("Error creating mapping: {}").format(str(e))
        )
    except Exception as e:
        logger.error(f"Error creating mapping: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=_("Error creating mapping: {}").format(str(e))
        )

@router.post("/stix-bundle", response_model=Dict)
async def generate_stix_bundle(
    request: TechniqueList,
    db: Session = Depends(get_db)
):
    """Generate a comprehensive STIX bundle with both ATT&CK and D3FEND objects.
    
    Args:
        request: Request containing a list of ATT&CK technique IDs
        db: Database session
        
    Returns:
        STIX bundle as JSON
    """
    mapper = ThreatDefenseMapper(db)
    try:
        bundle = mapper.generate_comprehensive_stix_bundle(request.techniques)
        # Convert to dictionary for JSON serialization
        return json.loads(bundle.serialize())
    except Exception as e:
        logger.error(f"Error generating STIX bundle: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=_("Error generating STIX bundle: {}").format(str(e))
        )