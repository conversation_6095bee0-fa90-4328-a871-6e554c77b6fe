
Skip to content
Navigation Menu
Sign in

SecurityRiskAdvisors /
VECTR
Public

VECTR is a tool that facilitates tracking of your red and blue team testing activities to measure detection and prevention capabilities across different attack scenarios
1.4k stars 166 forks Branches Tags
Activity

Code
Issues 51

SecurityRiskAdvisors/VECTR
Name	Last commit date
doodleincode
doodleincode
Feb 7, 2025
.github/ISSUE_TEMPLATE
	
Feb 6, 2024
cti
	
May 18, 2020
media
	
Jan 27, 2025
README.md
	
Jan 27, 2025
VECTR End User License Agreement.pdf
	
Aug 18, 2023
Repository files navigation

README

    Security

VECTR documentation: https://docs.vectr.io

VECTR Community Discord Channel: https://discord.gg/2FRd8zf728

VECTR is a tool that facilitates tracking of your red and blue team testing activities to measure detection and prevention capabilities across different attack scenarios. VECTR provides the ability to create assessment groups, which consist of a collection of Campaigns and supporting Test Cases to simulate adversary threats. Campaigns can be broad and span activity across the kill chain, from initial compromise to privilege escalation and lateral movement and so on, or can be a narrow in scope to focus on specific detection layers, tools, and infrastructure. VECTR is designed to promote full transparency between offense and defense, encourage training between team members, and improve detection & prevention success rate across the environment.

VECTR is focused on common indicators of attack and behaviors that may be carried out by any number of threat actor groups, with varying objectives and levels of sophistication. VECTR can also be used to replicate the step-by-step TTPs associated with specific groups and malware campaigns, however its primary purpose is to replicate attacker behaviors that span multiple threat actor groups and malware campaigns, past, present and future. VECTR is meant to be used over time with targeted campaigns, iteration, and measurable enhancements to both red team skills and blue team detection capabilities. Ultimately the goal of VECTR is to make a network resilient to all but the most sophisticated adversaries and insider attacks.
VECTR Heatmap
Resilience Trending
Data Import
Attack Escalation Graph
Attack Automation
Getting Started

See the Install Guide.
Supported Platforms
Server Operating Systems

    Current Ubuntu LTS (22.04)

We limit community support to this runtime environment. If you encounter issues feel free to open an issue on GitHub or join us on Discord.

VECTR should run in most containerization environments. If in enterprise we recommend consulting your Infrastructure/Containerization team for guidance. Or reach out directly via the contact form for our SaaS offerings.

Please read the instructions carefully for Upgrading a VECTR instance.
Browsers

Most modern browsers, such as Chrome, Firefox, Edge (Chromium) up to two prior releases are supported. We recommend using the latest of either:

    Chrome
    Firefox

General

    Presentation layer built on Vue 3 with some legacy AngularJS components.
    GraphQL API
    Support for TLS endpoints (VECTR Community Edition will auto-generate an untrusted self-signed cert or can be supplied with certs).

Security Risk Advisors
License

Please see the EULA

Atomic Red LICENSE
Releases 92
ce-9.6.5 Latest
Feb 7, 2025
+ 91 releases
Packages
No packages published
Contributors 6

    @carlvonderheid
    @thebleucheese
    @core-dump-19700101
    @doodleincode
    @SRAPSpencer
    @p-b--

Footer
© 2025 GitHub, Inc.
Footer navigation

    Terms
    Privacy
    Security
    Status
    Docs
    Contact

