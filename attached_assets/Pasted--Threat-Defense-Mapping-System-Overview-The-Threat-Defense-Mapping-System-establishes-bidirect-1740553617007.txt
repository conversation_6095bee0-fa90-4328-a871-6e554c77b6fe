# Threat-Defense Mapping System

## Overview

The Threat-Defense Mapping System establishes bidirectional relationships between MITRE ATT&CK techniques and D3FEND countermeasures, enabling comprehensive security planning and threat response capabilities. This integration leverages STIX 2.1 as the underlying representation format, providing a standardized approach to security intelligence exchange.

## Core Capabilities

### 1. Unified Threat-Defense Knowledge Graph

The system creates a cohesive knowledge graph connecting attack patterns to their corresponding defensive countermeasures. This enables security teams to:

- Visualize attack paths alongside applicable defensive controls
- Identify coverage gaps in existing security infrastructure
- Prioritize security investments based on threat intelligence

### 2. Defensive Coverage Analysis

The system calculates coverage metrics that quantify how well protected your systems are against specific attack techniques:

- **Overall Path Coverage**: Measures the weakest link in your defensive posture across an attack sequence
- **Average Coverage**: Provides a holistic view of general defensive capabilities
- **Per-Technique Coverage**: Offers granular insights into specific defensive strengths and weaknesses

### 3. Defensive Control Recommendations

When coverage gaps are identified, the system intelligently recommends D3FEND countermeasures based on:

- Effectiveness against similar attack techniques
- Implementation complexity and compatibility
- Industry best practices and proven countermeasures

### 4. STIX Intelligence Exchange

The system supports the generation of comprehensive STIX bundles that:

- Package ATT&CK techniques with corresponding D3FEND countermeasures
- Include relationship metadata and confidence scores
- Enable seamless intelligence sharing between security platforms

## API Endpoints

### Find D3FEND Countermeasures

```http
POST /api/v1/threat-defense/controls
```

Returns D3FEND countermeasures for specified ATT&CK techniques.

**Request Body:**
```json
{
  "techniques": ["T1566", "T1078", "T1021"]
}
```

**Response:**
```json
{
  "countermeasures": {
    "T1566": [
      {
        "d3fend_id": "http://d3fend.mitre.org/ontologies/d3fend.owl#HTTPTrafficAnalysis",
        "name": "HTTP Traffic Analysis",
        "type": "mitigates",
        "confidence": 0.9
      },
      ...
    ],
    "T1078": [
      ...
    ]
  }
}
```

### Calculate Attack Path Coverage

```http
POST /api/v1/threat-defense/coverage
```

Calculates defensive coverage for an attack path (sequence of techniques).

**Request Body:**
```json
{
  "techniques": ["T1566", "T1078", "T1021", "T1059", "T1486"]
}
```

**Response:**
```json
{
  "overall_coverage": 0.4,
  "average_coverage": 0.65,
  "techniques": {
    "T1566": 0.85,
    "T1078": 0.6,
    "T1021": 0.75,
    "T1059": 0.65,
    "T1486": 0.4
  }
}
```

### Get Defense Recommendations

```http
POST /api/v1/threat-defense/recommendations?min_coverage=0.7
```

Recommends D3FEND controls to improve coverage for techniques below the specified threshold.

**Request Body:**
```json
{
  "techniques": ["T1566", "T1078", "T1021", "T1059", "T1486"]
}
```

**Response:**
```json
{
  "recommendations": {
    "T1078": [
      {
        "d3fend_id": "http://d3fend.mitre.org/ontologies/d3fend.owl#AuthenticationCacheInvalidation",
        "name": "Authentication Cache Invalidation",
        "type": "mitigates",
        "definition": "...",
        "frequency": 12,
        "confidence": 0.7
      },
      ...
    ],
    "T1486": [
      ...
    ]
  }
}
```

### Create D3FEND Mapping

```http
POST /api/v1/threat-defense/map
```

Creates new mappings between a D3FEND control and ATT&CK techniques.

**Request Body:**
```json
{
  "d3fend_uri": "http://d3fend.mitre.org/ontologies/d3fend.owl#ProcessSpawningAnalysis",
  "technique_ids": ["T1059", "T1569"],
  "confidence": 0.8
}
```

**Response:**
```json
[
  {
    "id": "mapping--process-spawning-analysis-t1059",
    "stix_id": "attack-pattern--t1059",
    "d3fend_id": "http://d3fend.mitre.org/ontologies/d3fend.owl#ProcessSpawningAnalysis",
    "mapping_type": "mitigates",
    "confidence": 0.8
  },
  ...
]
```

### Generate STIX Bundle

```http
POST /api/v1/threat-defense/stix-bundle
```

Generates a comprehensive STIX bundle with both ATT&CK and D3FEND objects.

**Request Body:**
```json
{
  "techniques": ["T1566", "T1078", "T1486"]
}
```

**Response:**
A complete STIX 2.1 bundle containing:
- AttackPattern objects for each technique
- CourseOfAction objects for related D3FEND countermeasures
- Relationship objects connecting techniques and countermeasures

## Visual Analytics

The system includes a visual analytics component that provides:

1. **Attack Path Visualization**: Sequential representation of attack techniques
2. **Control Mapping Display**: Visual mapping between techniques and controls
3. **Coverage Indicators**: Color-coded indicators showing defensive coverage levels
4. **Recommendations View**: Interactive display of recommended defensive controls

## Implementation Considerations

### Data Requirements

For optimal functionality, the system requires:

1. An up-to-date MITRE ATT&CK dataset (imported via `/api/v1/mitre/import`)
2. Current D3FEND ontology data (typically in OWL format)
3. Initial mappings between ATT&CK techniques and D3FEND countermeasures

### Database Integration

The system leverages existing database models:
- MitreTechnique and MitreTactic from MITRE ATT&CK
- D3FENDConcept from D3FEND
- StixDB and D3fendMappingDB for STIX and mapping records

### Performance Considerations

For large-scale deployment, consider:
- Precomputing common coverage metrics
- Implementing caching for frequently accessed mappings
- Using pagination for large technique/control sets

## Usage Examples

### Generate Defensive Coverage Report

```python
import requests

# Define attack path
attack_path = ["T1566", "T1078", "T1021", "T1059", "T1486"]

# Get coverage metrics
response = requests.post(
    "http://localhost:5000/api/v1/threat-defense/coverage",
    json={"techniques": attack_path}
)

coverage_data = response.json()

# Get recommendations for improving coverage
recommendations = requests.post(
    "http://localhost:5000/api/v1/threat-defense/recommendations",
    json={"techniques": attack_path},
    params={"min_coverage": 0.7}
)

# Generate a STIX bundle for sharing
stix_bundle = requests.post(
    "http://localhost:5000/api/v1/threat-defense/stix-bundle",
    json={"techniques": attack_path}
)

# Save the STIX bundle for sharing
with open("attack_path_bundle.json", "w") as f:
    f.write(stix_bundle.text)
```

### Creating Custom Mappings

```python
import requests

# Define a new mapping between a D3FEND control and ATT&CK techniques
mapping = {
    "d3fend_uri": "http://d3fend.mitre.org/ontologies/d3fend.owl#DNSTrafficAnalysis",
    "technique_ids": ["T1071.004", "T1568.002"],
    "confidence": 0.85
}

# Create the mapping
response = requests.post(
    "http://localhost:5000/api/v1/threat-defense/map",
    json=mapping
)

print(f"Created {len(response.json())} mappings")
```

## Integration Benefits

1. **Comprehensive Security Planning**: Connect offensive and defensive security viewpoints
2. **Data-Driven Prioritization**: Make security investments based on quantitative coverage metrics
3. **Standardized Intelligence**: Leverage STIX for interoperability with other security platforms
4. **Visual Analytics**: Communicate security posture effectively with stakeholders
5. **Proactive Defense**: Move from reactive to proactive security with defense-in-depth planning