import React, { useState, useEffect } from 'react';
import { AlertCircle, Check, Shield, ShieldAlert, ShieldX } from 'lucide-react';

// Define color scales for coverage visualization
const getCoverageColor = (coverage) => {
  if (coverage >= 0.8) return 'bg-green-100 border-green-500';
  if (coverage >= 0.5) return 'bg-yellow-100 border-yellow-500';
  return 'bg-red-100 border-red-500';
};

const getCoverageIcon = (coverage) => {
  if (coverage >= 0.8) return <Shield className="text-green-600" />;
  if (coverage >= 0.5) return <ShieldAlert className="text-yellow-600" />;
  return <ShieldX className="text-red-600" />;
};

const AttackPathNode = ({ technique, countermeasures, coverage }) => {
  const [expanded, setExpanded] = useState(false);
  const colorClass = getCoverageColor(coverage);
  const icon = getCoverageIcon(coverage);

  return (
    <div className={`rounded-lg border-2 p-4 mb-4 ${colorClass}`}>
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          {icon}
          <h3 className="font-bold">{technique.id}: {technique.name}</h3>
        </div>
        <div className="text-right">
          <span className="text-lg font-semibold">{Math.round(coverage * 100)}%</span>
          <button 
            onClick={() => setExpanded(!expanded)}
            className="ml-2 text-blue-600 hover:text-blue-800"
          >
            {expanded ? 'Hide' : 'Show'} Countermeasures
          </button>
        </div>
      </div>
      
      {expanded && (
        <div className="mt-4 pl-8 border-l-2 border-gray-300">
          <h4 className="font-medium mb-2">D3FEND Countermeasures:</h4>
          {countermeasures.length === 0 ? (
            <p className="text-red-600 flex items-center gap-1">
              <AlertCircle size={16} />
              No countermeasures available
            </p>
          ) : (
            <ul className="space-y-2">
              {countermeasures.map(cm => (
                <li key={cm.d3fend_id} className="flex items-start gap-2">
                  <Check size={16} className="text-green-600 mt-1 flex-shrink-0" />
                  <div>
                    <span className="font-medium">{cm.name}</span>
                    <span className="text-sm text-gray-600 ml-2">
                      (Confidence: {Math.round(cm.confidence * 100)}%)
                    </span>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  );
};

const AttackPathVisualizer = () => {
  const [techniques, setTechniques] = useState([]);
  const [coverage, setCoverage] = useState({});
  const [countermeasures, setCountermeasures] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedTechniques, setSelectedTechniques] = useState([
    'T1566', 'T1078', 'T1021', 'T1059', 'T1486'
  ]);

  // Simulated data loading
  useEffect(() => {
    // In a real application, this would fetch from your API:
    // fetch('/api/v1/threat-defense/techniques')
    //   .then(res => res.json())
    //   .then(data => setTechniques(data))
    
    // Using sample data for demonstration
    setTechniques([
      { id: 'T1566', name: 'Phishing', description: 'Adversaries may send phishing messages to gain access to victim systems.' },
      { id: 'T1078', name: 'Valid Accounts', description: 'Adversaries may obtain and abuse credentials of existing accounts.' },
      { id: 'T1021', name: 'Remote Services', description: 'Adversaries may use valid accounts to log into remote services.' },
      { id: 'T1059', name: 'Command and Script Interpreter', description: 'Adversaries may abuse command and script interpreters to execute commands.' },
      { id: 'T1486', name: 'Data Encryption for Impact', description: 'Adversaries may encrypt data on target systems to disrupt availability.' }
    ]);
    
    setCoverage({
      'T1566': 0.85,
      'T1078': 0.60,
      'T1021': 0.75,
      'T1059': 0.65,
      'T1486': 0.40,
    });
    
    setCountermeasures({
      'T1566': [
        { d3fend_id: 'D3-HTTP', name: 'HTTP Traffic Analysis', confidence: 0.9 },
        { d3fend_id: 'D3-DECOY', name: 'Decoy Environment', confidence: 0.8 }
      ],
      'T1078': [
        { d3fend_id: 'D3-UAM', name: 'User Account Monitoring', confidence: 0.75 },
        { d3fend_id: 'D3-ACI', name: 'Authentication Cache Invalidation', confidence: 0.65 }
      ],
      'T1021': [
        { d3fend_id: 'D3-RSM', name: 'Remote Service Monitoring', confidence: 0.8 },
        { d3fend_id: 'D3-NTA', name: 'Network Traffic Analysis', confidence: 0.7 }
      ],
      'T1059': [
        { d3fend_id: 'D3-PM', name: 'Process Monitoring', confidence: 0.65 }
      ],
      'T1486': [
        { d3fend_id: 'D3-FA', name: 'File Analysis', confidence: 0.4 }
      ]
    });
    
    setLoading(false);
  }, []);

  if (loading) return <div className="text-center p-8">Loading attack path data...</div>;
  if (error) return <div className="text-red-600 p-4 border border-red-300 rounded">Error: {error}</div>;

  // Calculate overall coverage
  const techniquesCoverage = selectedTechniques.map(id => coverage[id] || 0);
  const overallCoverage = techniquesCoverage.length ? 
    Math.min(...techniquesCoverage) : 0;
  const averageCoverage = techniquesCoverage.length ? 
    techniquesCoverage.reduce((sum, val) => sum + val, 0) / techniquesCoverage.length : 0;

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        <h2 className="text-2xl font-bold mb-4">Attack Path Coverage Analysis</h2>
        
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="border rounded-lg p-4 bg-gray-50">
            <h3 className="text-lg font-medium mb-2">Overall Path Coverage</h3>
            <div className="flex items-center">
              {getCoverageIcon(overallCoverage)}
              <div className="ml-2">
                <div className="text-3xl font-bold">{Math.round(overallCoverage * 100)}%</div>
                <div className="text-sm text-gray-600">Weakest link approach</div>
              </div>
            </div>
          </div>
          
          <div className="border rounded-lg p-4 bg-gray-50">
            <h3 className="text-lg font-medium mb-2">Average Coverage</h3>
            <div className="flex items-center">
              {getCoverageIcon(averageCoverage)}
              <div className="ml-2">
                <div className="text-3xl font-bold">{Math.round(averageCoverage * 100)}%</div>
                <div className="text-sm text-gray-600">Across all techniques</div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex items-center mb-4">
          <div className="flex-grow h-1 bg-gray-200 relative">
            <div className="absolute inset-y-0 left-0 bg-green-500" style={{width: '33.3%'}}></div>
            <div className="absolute inset-y-0 left-1/3 bg-yellow-500" style={{width: '33.3%'}}></div>
            <div className="absolute inset-y-0 left-2/3 bg-red-500" style={{width: '33.4%'}}></div>
          </div>
          <div className="grid grid-cols-3 text-xs text-center w-44 ml-2">
            <div>Low</div>
            <div>Medium</div>
            <div>High</div>
          </div>
        </div>
      </div>
      
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-xl font-bold mb-4">Attack Sequence</h3>
        
        <div className="space-y-2">
          {selectedTechniques.map((id, index) => {
            const technique = techniques.find(t => t.id === id);
            if (!technique) return null;
            
            return (
              <div key={id} className="relative">
                {index > 0 && (
                  <div className="absolute top-0 left-6 h-6 w-0.5 bg-gray-400 -mt-6"></div>
                )}
                <AttackPathNode 
                  technique={technique}
                  countermeasures={countermeasures[id] || []}
                  coverage={coverage[id] || 0}
                />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default AttackPathVisualizer;