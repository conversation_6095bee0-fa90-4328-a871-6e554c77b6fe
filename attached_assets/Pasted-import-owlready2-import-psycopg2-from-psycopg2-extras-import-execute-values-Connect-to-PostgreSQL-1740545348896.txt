import owlready2
import psycopg2
from psycopg2.extras import execute_values

# Connect to PostgreSQL
conn = psycopg2.connect(
    host="localhost",
    database="d3fend_db",
    user="username",
    password="password"
)
cursor = conn.cursor()

# Load OWL ontology
onto = owlready2.get_ontology("d3fend.owl").load()

# Helper function to insert and return ID
def insert_and_get_id(cursor, table, columns, values):
    placeholders = ', '.join(['%s'] * len(values))
    columns_str = ', '.join(columns)
    query = f"INSERT INTO {table} ({columns_str}) VALUES ({placeholders}) RETURNING id"
    cursor.execute(query, values)
    return cursor.fetchone()[0]

# Import classes
class_id_map = {}  # To store URI -> id mapping
for cls in onto.classes():
    uri = cls.iri
    name = cls.name if hasattr(cls, "name") else uri.split("#")[-1]
    description = cls.comment.first() if hasattr(cls, "comment") and cls.comment else None
    
    try:
        class_id = insert_and_get_id(
            cursor, 
            "d3f_classes", 
            ["uri", "name", "description"], 
            [uri, name, description]
        )
        class_id_map[uri] = class_id
    except Exception as e:
        print(f"Error inserting class {name}: {e}")
        conn.rollback()
        continue

# Import class hierarchy
for cls in onto.classes():
    if cls.iri in class_id_map:
        subclass_id = class_id_map[cls.iri]
        
        for parent in cls.is_a:
            if hasattr(parent, "iri") and parent.iri in class_id_map:
                superclass_id = class_id_map[parent.iri]
                
                try:
                    cursor.execute(
                        "INSERT INTO d3f_class_hierarchy (subclass_id, superclass_id) VALUES (%s, %s)",
                        (subclass_id, superclass_id)
                    )
                except Exception as e:
                    print(f"Error inserting hierarchy for {cls.name}: {e}")
                    conn.rollback()

# Import properties
property_id_map = {}
for prop in list(onto.object_properties()) + list(onto.data_properties()):
    uri = prop.iri
    name = prop.name if hasattr(prop, "name") else uri.split("#")[-1]
    prop_type = "object" if prop in onto.object_properties() else "datatype"
    description = prop.comment.first() if hasattr(prop, "comment") and prop.comment else None
    
    try:
        property_id = insert_and_get_id(
            cursor, 
            "d3f_properties", 
            ["uri", "name", "property_type", "description"], 
            [uri, name, prop_type, description]
        )
        property_id_map[uri] = property_id
    except Exception as e:
        print(f"Error inserting property {name}: {e}")
        conn.rollback()
        continue

# Special handling for D3FEND-specific concepts
# For example, identifying countermeasures
countermeasure_cls = onto.search_one(iri="*#Countermeasure")
if countermeasure_cls:
    for cm in countermeasure_cls.subclasses():
        if cm.iri in class_id_map:
            class_id = class_id_map[cm.iri]
            name = cm.name if hasattr(cm, "name") else cm.iri.split("#")[-1]
            
            # You would need to extract implementation_level based on your ontology structure
            implementation_level = None
            
            try:
                cursor.execute(
                    "INSERT INTO d3f_countermeasures (class_id, countermeasure_name, implementation_level) VALUES (%s, %s, %s)",
                    (class_id, name, implementation_level)
                )
            except Exception as e:
                print(f"Error inserting countermeasure {name}: {e}")
                conn.rollback()

# Commit changes
conn.commit()
cursor.close()
conn.close()
print("Import completed successfully")