"""
Security Dashboard Application

A robust cybersecurity data platform providing advanced security analysis,
system monitoring, and comprehensive user management with enhanced logging
and interactive capabilities.

Features:
- User registration and authentication system
- Real-time security monitoring and analysis
- Integration with MITRE ATT&CK and D3FEND frameworks
- Comprehensive logging and auditing capabilities
"""

import os
import logging
import uuid
import markdown
import re
from flask import Flask, render_template, flash, redirect, url_for, request, jsonify, Markup
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON>ginManager
from werkzeug.security import generate_password_hash
from sqlalchemy.exc import SQLAlchemyError

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__, 
           static_folder='static',  # Set the static folder explicitly
           static_url_path='/static')  # Set the static URL path
app.secret_key = os.environ.get("SESSION_SECRET", "default_secret_key")

# Configure database
database_url = os.environ.get("DATABASE_URL")
if not database_url:
    raise ValueError("DATABASE_URL environment variable is not set")

app.config["SQLALCHEMY_DATABASE_URI"] = database_url
app.config["SQLALCHEMY_ENGINE_OPTIONS"] = {
    "pool_recycle": 300,
    "pool_pre_ping": True,
}
app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False

# Import database and models after app configuration
from api.database import db
from api.models.user import User, UserRole
from api.forms import RegistrationForm
from api.routes.auth import auth_bp, login_manager
from api.routes.admin import admin_bp
from api.utils.error_handler import log_error, handle_error_response

# Initialize database with app
db.init_app(app)

# Initialize Flask-Login
login_manager.init_app(app)

# Create all tables
with app.app_context():
    try:
        # Import all models to ensure they're registered
        import api.models  # noqa: F401
        from api.models.error_log import ErrorLog  # Import error log model
        from api.models.audit_log import AuditLog  # Import audit log model
        db.create_all()
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error creating database tables: {str(e)}")
        raise

# Register blueprints
app.register_blueprint(auth_bp)
app.register_blueprint(admin_bp)

# Register error handlers
@app.errorhandler(SQLAlchemyError)
def handle_sqlalchemy_error(error):
    """Handle SQLAlchemy errors globally."""
    error_id = str(uuid.uuid4())
    log_error(error)
    return render_template('error.html', 
                          title="Database Error",
                          message="A database error occurred.",
                          description="Our team has been notified and is working to fix the issue.",
                          error_id=error_id), 500

@app.errorhandler(404)
def page_not_found(error):
    """Handle 404 errors."""
    return render_template('error.html',
                          title="Page Not Found",
                          message="The page you're looking for doesn't exist.",
                          description="Please check the URL or navigate using the menu."), 404

@app.errorhandler(403)
def forbidden(error):
    """Handle 403 errors."""
    return render_template('error.html',
                          title="Access Denied",
                          message="You don't have permission to access this resource.",
                          description="Please contact your administrator if you believe this is an error."), 403

@app.errorhandler(500)
def internal_server_error(error):
    """Handle 500 errors."""
    error_id = str(uuid.uuid4())
    log_error(error)
    return render_template('error.html',
                          title="Server Error",
                          message="An unexpected error occurred.",
                          description="Our team has been notified and is working to fix the issue.",
                          error_id=error_id), 500

@app.errorhandler(Exception)
def handle_exception(error):
    """Handle all other exceptions globally."""
    error_id = str(uuid.uuid4())
    log_error(error)
    return render_template('error.html',
                          title="Unexpected Error",
                          message="Something went wrong.",
                          description="Our team has been notified and is working to fix the issue.",
                          error_id=error_id), 500

# API error handlers - return JSON for API routes
@app.errorhandler(SQLAlchemyError)
def handle_api_sqlalchemy_error(error):
    """Handle SQLAlchemy errors for API routes."""
    if request.path.startswith('/api/'):
        return handle_error_response(error, 500)
    return handle_sqlalchemy_error(error)

@app.errorhandler(Exception)
def handle_api_exception(error):
    """Handle all other exceptions for API routes."""
    if request.path.startswith('/api/'):
        return handle_error_response(error, 500)
    return handle_exception(error)

@app.route('/')
def index():
    """Render the main dashboard page."""
    logger.debug("Rendering dashboard page")
    return render_template('dashboard.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    """Handle user registration."""
    form = RegistrationForm()

    if form.validate_on_submit():
        try:
            # Create new user
            user = User(
                username=form.username.data,
                email=form.email.data,
                role=UserRole.VIEWER  # Default role for new registrations
            )
            user.set_password(form.password.data)

            # Add to database
            db.session.add(user)
            db.session.commit()

            flash('Registration successful! Please log in.', 'success')
            return redirect(url_for('auth.login'))

        except Exception as e:
            logger.error(f"Error during registration: {str(e)}")
            db.session.rollback()
            # Log the error with our error handler
            log_error(e, endpoint='/register', request_data=form.data)
            flash('An error occurred during registration. Please try again.', 'danger')

    return render_template('registration.html', form=form)

@app.route('/health')
def health():
    """Health check endpoint."""
    logger.debug("Health check endpoint called")
    return {"status": "healthy"}

@app.route('/docs/all')
def documentation_index():
    """Render the documentation index page."""
    logger.debug("Rendering documentation index page")
    try:
        # Read the Markdown file
        with open('docs/all.md', 'r') as f:
            content = f.read()
        
        # Convert Markdown to HTML with support for Mermaid diagrams
        # First, identify Mermaid code blocks
        mermaid_blocks = re.findall(r'```mermaid\n(.*?)```', content, re.DOTALL)
        
        # Replace Mermaid code blocks with placeholders
        for i, block in enumerate(mermaid_blocks):
            placeholder = f'MERMAID_PLACEHOLDER_{i}'
            content = content.replace(f'```mermaid\n{block}```', placeholder)
        
        # Convert Markdown to HTML
        html_content = markdown.markdown(content, extensions=['extra', 'codehilite', 'toc'])
        
        # Replace placeholders with Mermaid div elements
        for i, block in enumerate(mermaid_blocks):
            placeholder = f'MERMAID_PLACEHOLDER_{i}'
            mermaid_div = f'<div class="mermaid">{block}</div>'
            html_content = html_content.replace(placeholder, mermaid_div)
        
        # Create a template with Mermaid.js included
        return render_template('documentation.html', 
                              title="Documentation Index",
                              content=Markup(html_content))
    except Exception as e:
        logger.error(f"Error rendering documentation: {str(e)}")
        return render_template('error.html',
                              title="Documentation Error",
                              message="Could not load documentation.",
                              description=f"Error: {str(e)}"), 500

if __name__ == "__main__":
    # Always run on port 5000 with host 0.0.0.0 to make it externally accessible
    app.run(host='0.0.0.0', port=5000, debug=True)