import os
import subprocess
import unittest

class TestScripts(unittest.TestCase):
    def test_create_prd_on_merge_exists(self):
        self.assertTrue(os.path.exists('create_prd_on_merge.sh'))
        self.assertTrue(os.access('create_prd_on_merge.sh', os.X_OK))
        
    def test_create_refactor_branch_exists(self):
        self.assertTrue(os.path.exists('create_refactor_branch.sh'))
        self.assertTrue(os.access('create_refactor_branch.sh', os.X_OK))
        
    def test_cleanup_branches_exists(self):
        self.assertTrue(os.path.exists('cleanup_branches.sh'))
        self.assertTrue(os.access('cleanup_branches.sh', os.X_OK))
        
    def test_merge_features_modified(self):
        with open('merge_features.sh', 'r') as f:
            content = f.read()
        self.assertIn('create_prd_on_merge.sh', content)
        
    def test_prd_directory_exists(self):
        self.assertTrue(os.path.exists('.claude/feature_roadmap'))
        
    def test_prds_generated(self):
        prd_files = [f for f in os.listdir('.claude/feature_roadmap') if f.endswith('_prd.md')]
        self.assertGreater(len(prd_files), 0)

if __name__ == '__main__':
    unittest.main()
