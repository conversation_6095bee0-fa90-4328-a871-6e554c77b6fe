#!/bin/bash
set -e

# Print environment info
echo "Starting RegrigorRigor application..."
echo "Python version: $(python --version)"
echo "Working directory: $(pwd)"

# Wait for PostgreSQL
until PGPASSWORD=regrigor_password psql -h postgres -U regrigor -d regrigor_db -c '\q'; do
  >&2 echo "PostgreSQL is unavailable - sleeping"
  sleep 1
done

>&2 echo "PostgreSQL is up - executing command"

# Wait for the PostgreSQL database and initialize
if [ -f /app/.dockerwrapper/init_db.py ]; then
  python /app/.dockerwrapper/init_db.py
elif [ -f /app/init_db.py ]; then
  python /app/init_db.py
else
  echo "Warning: init_db.py not found, skipping database initialization"
fi

# Run migrations if we have alembic
if [ -f /app/alembic.ini ]; then
  echo "Running database migrations..."
  # Use alembic stamp to mark the current migration as applied without running it
  # This helps when tables already exist but alemb<PERSON> doesn't know about them
  alembic stamp head || echo "Alembic stamp failed, continuing anyway..."
  
  # Now try to run migrations, but don't fail if they already exist
  alembic upgrade head || echo "Alembic upgrade failed, continuing anyway..."
fi

# Execute the command passed to docker-entrypoint
exec "$@" 