[project]
name = "regression-rigor"
version = "0.1.0"
description = "A comprehensive regression testing and monitoring platform"
authors = [
    {name = "Your Name", email = "<EMAIL>"},
]
dependencies = [
    "fastapi>=0.100.0",
    "uvicorn>=0.22.0",
    "sqlalchemy>=2.0.0",
    "psycopg2-binary>=2.9.9",
    "alembic>=1.11.1",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.6",
    "redis>=4.6.0",
    "aioredis>=2.0.1",
    "python-dotenv>=1.0.0",
    "requests>=2.31.0",
    "httpx>=0.24.1",
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "black>=23.7.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.4.1",
]
requires-python = ">=3.11"
readme = "README.md"
license = {text = "MIT"}

[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"

[tool.pdm]
distribution = true

[tool.pdm.dev-dependencies]
dev = [
    "black>=23.7.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.4.1",
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
]

[tool.black]
line-length = 100
target-version = ["py311"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 100

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
check_untyped_defs = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --cov=api --cov-report=term-missing"
testpaths = [
    "tests",
]

[tool.interrogate]
ignore-init-method = true
ignore-init-module = false
ignore-magic = false
ignore-semiprivate = false
ignore-private = false
ignore-property-decorators = false
ignore-module = false
ignore-nested-functions = false
ignore-nested-classes = false
ignore-setters = false
fail-under = 95
exclude = ["setup.py", "docs", "build", ".git", "tests"]
ignore-regex = ["^get$", "^mock_.*", ".*BaseClass.*"]
verbose = 2
quiet = false
whitelist-regex = []
color = true
omit-covered-files = false
generate-badge = "docs/interrogate_badge.svg"
badge-format = "svg"
badge-style = "flat"
