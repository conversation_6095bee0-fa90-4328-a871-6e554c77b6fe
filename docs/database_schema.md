# Database Schema Documentation

This document provides a comprehensive overview of the database schema used in the Regression Rigor project.

## User Management Tables

### Users Table (`users`)
Stores user account information and authentication details.

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | INTEGER | Primary key |
| username | VARCHAR | Unique username for login |
| email | VARCHAR | User's email address |
| hashed_password | VARCHAR(256) | Securely hashed password |
| is_active | BOOLEAN | Whether the account is active |
| created_at | TIMESTAMP | Account creation timestamp |
| email_verified | BOOLEAN | Whether the email has been verified |
| role | VARCHAR(7) | User role (admin, analyst, viewer) |
| full_name | VARCHAR(100) | User's full name |
| bio | TEXT | User's biography or description |
| last_login | TIMESTAMP | Last login timestamp |
| failed_login_attempts | INTEGER | Count of failed login attempts |
| password_changed_at | TIMESTAMP | Last password change timestamp |
| account_locked | BOOLEAN | Whether the account is locked |
| account_locked_at | TIMESTAMP | When the account was locked |
| last_failed_login | TIMESTAMP | Last failed login attempt timestamp |
| password_reset_token | VARCHAR(256) | Token for password reset |
| password_reset_expires | TIMESTAMP | Expiration time for password reset token |
| two_factor_enabled | BOOLEAN | Whether 2FA is enabled |
| two_factor_secret | VARCHAR(32) | Secret key for TOTP generation |
| backup_codes | VARCHAR(512) | Comma-separated list of hashed backup codes |

### User Preferences Table (`user_preferences`)
Stores user-specific settings and preferences.

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | INTEGER | Primary key |
| user_id | INTEGER | Foreign key to users table |
| theme | VARCHAR(20) | UI theme preference |
| notification_settings | JSON | User notification preferences |
| dashboard_layout | JSON | Custom dashboard layout settings |
| timezone | VARCHAR(50) | User's preferred timezone |
| language | VARCHAR(10) | User's preferred language |
| email_notifications | BOOLEAN | Whether email notifications are enabled |
| two_factor_enabled | BOOLEAN | Whether 2FA is enabled (duplicate of users table) |

### User Sessions Table (`user_sessions`)
Tracks user login sessions across devices.

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | UUID | Primary key |
| user_id | INTEGER | Foreign key to users table |
| device_id | UUID | Foreign key to device_info table |
| session_token | VARCHAR | Unique session token |
| is_active | BOOLEAN | Whether the session is active |
| login_at | TIMESTAMP | Session start timestamp |
| last_activity | TIMESTAMP | Last activity timestamp |
| expires_at | TIMESTAMP | Session expiration timestamp |
| logout_at | TIMESTAMP | Session end timestamp |

### Device Info Table (`device_info`)
Stores information about devices used to access the system.

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | UUID | Primary key |
| user_agent | VARCHAR | Browser user agent string |
| ip_address | VARCHAR | IP address |
| device_type | VARCHAR | Type of device (mobile, desktop, tablet) |
| os_info | VARCHAR | Operating system information |
| browser_info | VARCHAR | Browser information |
| created_at | TIMESTAMP | When the device was first seen |
| last_seen | TIMESTAMP | When the device was last seen |

## Campaign Management Tables

### Campaign Table (`cl_campaign`)
Stores information about security testing campaigns.

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | INTEGER | Primary key |
| name | VARCHAR | Campaign name |
| description | VARCHAR | Campaign description |
| status | VARCHAR | Campaign status (active, inactive, completed) |
| created_time | TIMESTAMP | Creation timestamp |
| updated_time | TIMESTAMP | Last update timestamp |
| deleted_time | TIMESTAMP | Soft delete timestamp |

### Organization Table (`cl_organization`)
Stores information about organizations.

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | INTEGER | Primary key |
| name | VARCHAR | Organization name |
| description | VARCHAR | Organization description |
| created_time | TIMESTAMP | Creation timestamp |
| updated_time | TIMESTAMP | Last update timestamp |
| deleted_time | TIMESTAMP | Soft delete timestamp |

### Test Cases Table (`test_cases`)
Stores test cases associated with campaigns.

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | INTEGER | Primary key |
| name | VARCHAR | Test case name |
| description | VARCHAR | Test case description |
| campaign_id | INTEGER | Foreign key to cl_campaign table |
| expected_result | VARCHAR | Expected test result |
| actual_result | VARCHAR | Actual test result |
| status | VARCHAR | Test status (pending, running, passed, failed) |
| created_time | TIMESTAMP | Creation timestamp |
| updated_time | TIMESTAMP | Last update timestamp |
| deleted_time | TIMESTAMP | Soft delete timestamp |

### Campaign to Organization Association (`cl_campaign_to_organization`)
Maps the many-to-many relationship between campaigns and organizations.

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| campaign_id | INTEGER | Foreign key to cl_campaign table |
| organization_id | INTEGER | Foreign key to cl_organization table |

## MITRE ATT&CK Framework Tables

### MITRE Techniques Table (`mitre_techniques`)
Stores information about MITRE ATT&CK techniques.

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | INTEGER | Primary key |
| technique_id | VARCHAR(50) | MITRE technique ID (e.g., T1234) |
| name | VARCHAR(255) | Technique name |
| description | VARCHAR(2000) | Technique description |
| version_id | INTEGER | Foreign key to mitre_versions table |
| created_at | TIMESTAMP | Creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |
| extra_data | JSON | Additional technique data |
| detection | VARCHAR(2000) | Detection guidance |
| platforms | JSON | Applicable platforms |
| created_on | TIMESTAMP | Original creation timestamp |
| deleted_at | TIMESTAMP | Soft delete timestamp |
| is_deprecated | BOOLEAN | Whether the technique is deprecated |
| is_revoked | BOOLEAN | Whether the technique is revoked |
| revoked_by_id | INTEGER | ID of the technique that revoked this one |

### MITRE Tactics Table (`mitre_tactics`)
Stores information about MITRE ATT&CK tactics.

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | INTEGER | Primary key |
| tactic_id | VARCHAR | MITRE tactic ID |
| name | VARCHAR | Tactic name |
| description | VARCHAR | Tactic description |
| version_id | INTEGER | Foreign key to mitre_versions table |
| data | JSON | Additional tactic data |
| created_at | TIMESTAMP | Creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |
| created_on | TIMESTAMP | Original creation timestamp |
| deleted_at | TIMESTAMP | Soft delete timestamp |
| is_deprecated | BOOLEAN | Whether the tactic is deprecated |
| is_revoked | BOOLEAN | Whether the tactic is revoked |
| revoked_by_id | INTEGER | ID of the tactic that revoked this one |

### MITRE Groups Table (`mitre_groups`)
Stores information about threat groups in the MITRE ATT&CK framework.

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| id | INTEGER | Primary key |
| group_id | VARCHAR(50) | MITRE group ID |
| name | VARCHAR(255) | Group name |
| description | VARCHAR(2000) | Group description |
| version_id | INTEGER | Foreign key to mitre_versions table |
| aliases | JSON | Alternative names for the group |
| created_at | TIMESTAMP | Creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |
| extra_data | JSON | Additional group data |
| created_on | TIMESTAMP | Original creation timestamp |
| deleted_at | TIMESTAMP | Soft delete timestamp |
| is_deprecated | BOOLEAN | Whether the group is deprecated |
| is_revoked | BOOLEAN | Whether the group is revoked |
| revoked_by_id | INTEGER | ID of the group that revoked this one |

### Technique-Tactic Association (`technique_tactic_association`)
Maps the many-to-many relationship between techniques and tactics.

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| technique_id | INTEGER | Foreign key to mitre_techniques table |
| tactic_id | INTEGER | Foreign key to mitre_tactics table |

### Group-Technique Association (`group_technique_association`)
Maps the many-to-many relationship between groups and techniques.

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| group_id | INTEGER | Foreign key to mitre_groups table |
| technique_id | INTEGER | Foreign key to mitre_techniques table |

## Relationships

The database schema includes the following key relationships:

1. **Users to User Preferences**: One-to-one relationship
   - A user can have one set of preferences
   - Foreign key: `user_preferences.user_id` references `users.id`

2. **Users to User Sessions**: One-to-many relationship
   - A user can have multiple sessions
   - Foreign key: `user_sessions.user_id` references `users.id`

3. **User Sessions to Device Info**: Many-to-one relationship
   - Multiple sessions can be associated with a device
   - Foreign key: `user_sessions.device_id` references `device_info.id`

4. **Campaigns to Test Cases**: One-to-many relationship
   - A campaign can have multiple test cases
   - Foreign key: `test_cases.campaign_id` references `cl_campaign.id`

5. **Campaigns to Organizations**: Many-to-many relationship
   - A campaign can be associated with multiple organizations
   - An organization can be associated with multiple campaigns
   - Junction table: `cl_campaign_to_organization`

6. **Techniques to Tactics**: Many-to-many relationship
   - A technique can be associated with multiple tactics
   - A tactic can be associated with multiple techniques
   - Junction table: `technique_tactic_association`

7. **Groups to Techniques**: Many-to-many relationship
   - A group can use multiple techniques
   - A technique can be used by multiple groups
   - Junction table: `group_technique_association`

## Database Design Principles

1. **Soft Delete**: Most tables include a `deleted_time` or `deleted_at` column to support soft deletion.
2. **Versioning**: MITRE-related tables include version tracking to maintain historical data.
3. **Timestamps**: Creation and update timestamps are included for auditing purposes.
4. **JSON Fields**: Complex data structures are stored in JSON fields for flexibility.
5. **Normalization**: The schema follows normalization principles to reduce redundancy.

## Schema Evolution

The database schema is managed using Alembic migrations, allowing for controlled schema evolution over time. All changes to the schema should be made through migrations to ensure consistency across environments. 