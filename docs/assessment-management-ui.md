# Assessment Management UI Documentation

This document provides visual documentation of the Assessment Management UI components.

## How to Generate Screenshots

Screenshots can be generated using the screenshot generation script:

```bash
node scripts/generate_ui_screenshots.js
```

For more information on using the script, see [Screenshot Generation Documentation](../scripts/README.md).

## UI Components

### Assessments List View

The main entry point for assessment management. This view displays all assessments with filtering, sorting, and pagination capabilities.

![Assessments List](screenshots/01-assessments-list.png)

#### Filter Panel

The filter panel allows users to narrow down the list of assessments by various criteria.

![Assessment Filters](screenshots/02-assessments-filters.png)

### Assessment Detail View

#### Overview Tab

Provides a summary of the assessment with key details and a visualization of test results.

![Assessment Detail Overview](screenshots/03-assessment-detail-overview.png)

#### Test Executions Tab

Lists all test executions associated with the assessment, including their results and metadata.

![Assessment Test Executions](screenshots/04-assessment-test-executions.png)

#### Campaigns Tab

Shows campaigns that are associated with the assessment.

![Assessment Campaigns](screenshots/05-assessment-campaigns.png)

#### Report Tab

Displays a summary report with metrics and visualizations within the assessment detail view.

![Assessment Report Tab](screenshots/06-assessment-report-tab.png)

### Full Report View

A comprehensive view dedicated to displaying the complete assessment report.

![Full Assessment Report](screenshots/07-full-report.png)

#### Expanded Findings

Findings can be expanded to show detailed information.

![Expanded Findings](screenshots/08-findings-expanded.png)

### Assessment Form

The form for creating new assessments or editing existing ones.

![Create Assessment Form](screenshots/09-create-assessment-form.png)

#### Form Validation

The form includes validation to ensure data integrity.

![Form Validation](screenshots/10-form-validation.png)

## User Workflows

### Creating a New Assessment

1. Navigate to the Assessments List
2. Click the "New Assessment" button
3. Fill out the required fields in the form
4. Click "Create Assessment"
5. View the newly created assessment in the detail view

### Viewing Assessment Results

1. Navigate to the Assessments List
2. Click on an assessment name to go to its detail view
3. View the overview or switch to the Test Executions tab
4. For a comprehensive report, click the "View Full Report" button or navigate to the Report tab

### Editing an Assessment

1. Navigate to the assessment detail view
2. Click the "Edit" button
3. Modify the assessment details in the form
4. Click "Update Assessment" to save changes

### Generating a Report

1. Navigate to the assessment detail view
2. Click "Generate Report" button
3. View the report in the Report tab or click "View Full Report" for the dedicated report view
4. Use the "Download Report" button to export the report if needed 