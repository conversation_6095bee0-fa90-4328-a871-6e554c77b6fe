# Documentation Index {#documentation-index}

This page provides a comprehensive index of all documentation files in the Regression Rigor project, sorted by most recently modified.

## Recently Updated Documentation {#recently-updated-documentation}

1. [Feature Development Guide](feature_development_guide.md) - Standard process for developing new features
2. [Comprehensive Tagging System](features/comprehensive-tagging-system.md) - Feature documentation for the tagging system
3. [Development Roadmap](development_roadmap.md) - Planned features and enhancements
4. [Roadmap](roadmap.md) - High-level project roadmap
5. [Development Plan](development_plan.md) - Detailed development plan
6. [Docker Setup](docker_setup.md) - Instructions for setting up Docker
7. [Index](index.md) - Main documentation index
8. [Rate Limiting](rate_limiting.md) - API rate limiting implementation
9. [Project Report (2025-03-10)](project_report_2025-03-10.md) - Latest project status report
10. [Implementation Summary](implementation_summary.md) - Summary of implemented features

## Feature Documentation {#feature-documentation}

- [Comprehensive Tagging System](features/comprehensive-tagging-system.md) - Tag management for entities

## Technical Documentation {#technical-documentation}

- [API Documentation](api_documentation.md) - API endpoints and usage
- [Architecture](architecture.md) - System architecture overview
- [Database Schema](database_schema.md) - Database structure and relationships
- [Input Validation](input_validation.md) - Input validation mechanisms
- [Migrations](migrations.md) - Database migration procedures
- [Rate Limiting](rate_limiting.md) - API rate limiting implementation
- [Schema](schema.md) - Data schemas
- [User Management](user_management.md) - User authentication and authorization

## Planning and Roadmap {#planning-and-roadmap}

- [Development Plan](development_plan.md) - Detailed development plan
- [Development Roadmap](development_roadmap.md) - Planned features and enhancements
- [Roadmap](roadmap.md) - High-level project roadmap
- [TODO](TODO.md) - Pending tasks and improvements
- [Red Team Regression Plan](red_team_regression_plan.md) - Plan for regression testing

## Integration Documentation {#integration-documentation}

- [ATLAS Integration](atlas_integration.md) - Integration with ATLAS
- [Milestones: ATLAS Integration](milestones/ATLAS_INTEGRATION.md) - ATLAS integration milestones

## Development Guides {#development-guides}

- [Feature Development Guide](feature_development_guide.md) - Standard process for developing new features
- [Docker Setup](docker_setup.md) - Instructions for setting up Docker

## Example Mermaid Diagrams {#example-mermaid-diagrams}

### System Architecture {#system-architecture}

```mermaid
flowchart TD
    User[User] --> |HTTP Requests| FE[Frontend]
    FE --> |API Calls| BE[Backend API]
    BE --> |Queries| DB[(Database)]
    BE --> |Authentication| Auth[Auth Service]
    BE --> |Rate Limiting| RL[Rate Limiter]
    BE --> |Validation| Val[Input Validator]
    BE --> |MITRE Integration| MITRE[MITRE ATT&CK]
```

### Feature Development Workflow {#feature-development-workflow}

```mermaid
stateDiagram-v2
    [*] --> Planning
    Planning --> Setup
    Setup --> Implementation
    Implementation --> Testing
    Testing --> Documentation
    Documentation --> Completion
    Completion --> Review
    Review --> Merge
    Merge --> [*]
```

### Database Relationships {#database-relationships}

```mermaid
erDiagram
    USER ||--o{ USER_SESSION : has
    USER ||--o{ TAG : creates
    TAG ||--o{ TAG_ASSOCIATION : has
    TAG_ASSOCIATION }o--|| TEST_CASE : tags
    TAG_ASSOCIATION }o--|| CAMPAIGN : tags
    TAG_ASSOCIATION }o--|| ASSESSMENT : tags
```

## How to Use This Documentation {#how-to-use}

1. Start with the [Feature Development Guide](feature_development_guide.md) to understand our development process
2. Review the [Architecture](architecture.md) to understand the system structure
3. Explore specific feature documentation as needed
4. Refer to technical documentation for implementation details

## Contributing to Documentation {#contributing}

When adding new documentation:

1. Place it in the appropriate directory
2. Follow the established Markdown formatting
3. Include diagrams where helpful (we support Mermaid graphs)
4. Update this index page to include your new documentation

## Markdown and Mermaid Support {#markdown-support}

This documentation uses standard Markdown with additional support for Mermaid diagrams. To create a Mermaid diagram, use the following syntax:

```
```mermaid
graph TD
    A[Start] --> B[Process]
    B --> C[End]
```
```

For more information on Mermaid syntax, visit the [Mermaid documentation](https://mermaid-js.github.io/mermaid/). 