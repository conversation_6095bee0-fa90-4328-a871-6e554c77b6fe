# API Documentation Guide

This document provides information on how to use the Regression Rigor API documentation.

## Overview

The Regression Rigor API provides comprehensive documentation through both Swagger UI and ReDoc interfaces. These interactive documentation tools allow you to:

- Browse all available API endpoints
- Understand request and response formats
- Test API endpoints directly from the browser
- View authentication requirements
- Understand rate limiting policies

## Accessing the Documentation

The API documentation is available at the following URLs:

- **Swagger UI**: `/api/docs`
- **ReDoc**: `/api/redoc`

Both interfaces provide the same information but with different user experiences:

- **Swagger UI** is interactive and allows you to test endpoints directly.
- **ReDoc** provides a cleaner reading experience for browsing the documentation.

## Authentication in the Documentation

Most endpoints require authentication. To use these endpoints in the Swagger UI:

1. Click the "Authorize" button at the top of the page
2. Enter your credentials or JWT token
3. Click "Authorize" to authenticate your session

Once authenticated, you can test protected endpoints directly from the Swagger UI.

## Understanding Endpoint Documentation

Each endpoint in the documentation includes:

- **HTTP Method**: GET, POST, PUT, DELETE, etc.
- **URL Path**: The path to access the endpoint
- **Description**: What the endpoint does
- **Parameters**: Required and optional parameters
- **Request Body**: Format of the request body (for POST/PUT)
- **Responses**: Possible response codes and formats
- **Rate Limiting**: Information about rate limits for the endpoint

## Rate Limiting Information

The documentation includes information about rate limiting for each endpoint. The API implements different tiers of rate limiting:

- **Standard endpoints**: 100 requests per minute
- **Strict endpoints**: 20 requests per minute
- **Authentication endpoints**: 5 requests per minute
- **User-specific endpoints**: 200 requests per minute per user

When a rate limit is exceeded, the API returns a `429 Too Many Requests` response.

## Examples

The documentation includes examples for request and response bodies. These examples can be used as templates for your own API calls.

## Schema Definitions

The "Schemas" section at the bottom of the documentation provides detailed information about the data structures used in the API. This includes:

- **User**: User account information
- **TwoFactorSetup**: Two-factor authentication setup data
- **TechniqueResponse**: MITRE ATT&CK technique information
- And many more...

## Using the API with Code

Here are examples of how to call the API using different programming languages:

### Python

```python
import requests

# Authentication
auth_response = requests.post(
    "https://api.example.com/api/v1/auth/token",
    data={"username": "user", "password": "pass"}
)
token = auth_response.json()["access_token"]

# Making an authenticated request
headers = {"Authorization": f"Bearer {token}"}
response = requests.get(
    "https://api.example.com/api/v1/users/me/preferences",
    headers=headers
)
print(response.json())
```

### JavaScript

```javascript
// Authentication
fetch("https://api.example.com/api/v1/auth/token", {
  method: "POST",
  headers: {
    "Content-Type": "application/x-www-form-urlencoded"
  },
  body: "username=user&password=pass"
})
.then(response => response.json())
.then(data => {
  const token = data.access_token;
  
  // Making an authenticated request
  return fetch("https://api.example.com/api/v1/users/me/preferences", {
    headers: {
      "Authorization": `Bearer ${token}`
    }
  });
})
.then(response => response.json())
.then(data => console.log(data));
```

## Troubleshooting

If you encounter issues with the API documentation:

1. **Authentication errors**: Ensure your token is valid and properly formatted
2. **Rate limiting**: If you receive a 429 response, wait before making more requests
3. **Schema validation errors**: Check your request format against the schema definitions
4. **Browser issues**: Try using a different browser or clearing your cache

## Feedback and Support

If you find issues with the API documentation or have suggestions for improvement, please contact <NAME_EMAIL>. 