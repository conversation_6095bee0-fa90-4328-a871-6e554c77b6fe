# Environment Management API

This document provides detailed information about the Environment Management API endpoints in the RegressionRigor platform.

## Overview

The Environment Management API allows users to create, manage, and track testing environments. Environments serve as top-level containers for organizing security assessments, providing a structured approach to security testing activities across different systems or deployment stages.

## API Endpoints

All Environment Management API endpoints are available under the base path `/api/v1/environments`.

### List Environments

```
GET /api/v1/environments/
```

Retrieves a list of environments with optional filtering.

**Query Parameters:**
- `skip` (integer, optional): Number of records to skip for pagination. Default: 0
- `limit` (integer, optional): Maximum number of records to return. Default: 100
- `status` (string, optional): Filter by environment status (active, inactive, deprecated)
- `search` (string, optional): Search term to filter environments by name or description
- `include_deleted` (boolean, optional): Whether to include soft-deleted environments. Default: false

**Response:**
```json
[
  {
    "id": 1,
    "name": "Production Environment",
    "description": "Production systems environment",
    "type": "production",
    "status": "active",
    "created_by": 1,
    "created_at": "2024-03-16T10:30:00",
    "updated_at": "2024-03-16T10:30:00",
    "deleted_at": null,
    "is_deprecated": false,
    "is_revoked": false,
    "revoked_by_id": null,
    "version": 1
  },
  // Additional environments...
]
```

**Permissions:**
- Regular users can only see environments they created
- Admin users can see all environments

### Get Environment

```
GET /api/v1/environments/{environment_id}
```

Retrieves a specific environment by ID.

**Path Parameters:**
- `environment_id` (integer, required): The ID of the environment to retrieve

**Response:**
```json
{
  "id": 1,
  "name": "Production Environment",
  "description": "Production systems environment",
  "type": "production",
  "status": "active",
  "created_by": 1,
  "created_at": "2024-03-16T10:30:00",
  "updated_at": "2024-03-16T10:30:00",
  "deleted_at": null,
  "is_deprecated": false,
  "is_revoked": false,
  "revoked_by_id": null,
  "version": 1
}
```

**Permissions:**
- Users can only access environments they created
- Admin users can access any environment

### Create Environment

```
POST /api/v1/environments/
```

Creates a new environment.

**Request Body:**
```json
{
  "name": "Staging Environment",
  "description": "Staging systems environment",
  "type": "staging",
  "status": "active"
}
```

**Response:**
```json
{
  "id": 2,
  "name": "Staging Environment",
  "description": "Staging systems environment",
  "type": "staging",
  "status": "active",
  "created_by": 1,
  "created_at": "2024-03-16T11:45:00",
  "updated_at": "2024-03-16T11:45:00",
  "deleted_at": null,
  "is_deprecated": false,
  "is_revoked": false,
  "revoked_by_id": null,
  "version": 1
}
```

**Permissions:**
- Users must have the "admin" or "analyst" role

### Update Environment

```
PUT /api/v1/environments/{environment_id}
```

Updates an existing environment.

**Path Parameters:**
- `environment_id` (integer, required): The ID of the environment to update

**Request Body:**
```json
{
  "name": "Updated Staging Environment",
  "status": "inactive"
}
```

**Response:**
```json
{
  "id": 2,
  "name": "Updated Staging Environment",
  "description": "Staging systems environment",
  "type": "staging",
  "status": "inactive",
  "created_by": 1,
  "created_at": "2024-03-16T11:45:00",
  "updated_at": "2024-03-16T12:30:00",
  "deleted_at": null,
  "is_deprecated": false,
  "is_revoked": false,
  "revoked_by_id": null,
  "version": 2
}
```

**Permissions:**
- Users can only update environments they created
- Admin users can update any environment

### Delete Environment

```
DELETE /api/v1/environments/{environment_id}
```

Soft-deletes an environment.

**Path Parameters:**
- `environment_id` (integer, required): The ID of the environment to delete

**Response:**
- Status code 204 (No Content) on success

**Permissions:**
- Users can only delete environments they created
- Admin users can delete any environment

### Restore Environment

```
POST /api/v1/environments/{environment_id}/restore
```

Restores a soft-deleted environment.

**Path Parameters:**
- `environment_id` (integer, required): The ID of the environment to restore

**Response:**
```json
{
  "id": 2,
  "name": "Updated Staging Environment",
  "description": "Staging systems environment",
  "type": "staging",
  "status": "inactive",
  "created_by": 1,
  "created_at": "2024-03-16T11:45:00",
  "updated_at": "2024-03-16T12:30:00",
  "deleted_at": null,
  "is_deprecated": false,
  "is_revoked": false,
  "revoked_by_id": null,
  "version": 2
}
```

**Permissions:**
- Only admin users can restore environments

### Get Environment Assessments

```
GET /api/v1/environments/{environment_id}/assessments
```

Retrieves assessments associated with an environment.

**Path Parameters:**
- `environment_id` (integer, required): The ID of the environment

**Response:**
```json
[
  {
    "id": 1,
    "name": "Q2 Security Assessment",
    "description": "Comprehensive security assessment for Q2",
    "target_system": "Payment Processing System",
    "assessment_type": "penetration",
    "status": "in_progress",
    "start_date": "2024-04-01T00:00:00",
    "end_date": "2024-06-30T23:59:59",
    "environment_id": 1,
    "campaign_id": null
  },
  // Additional assessments...
]
```

**Permissions:**
- Users can only access assessments for environments they created
- Admin users can access assessments for any environment

### Deprecate Environment

```
POST /api/v1/environments/{environment_id}/deprecate
```

Marks an environment as deprecated.

**Path Parameters:**
- `environment_id` (integer, required): The ID of the environment to deprecate

**Response:**
```json
{
  "id": 2,
  "name": "Updated Staging Environment",
  "description": "Staging systems environment",
  "type": "staging",
  "status": "inactive",
  "created_by": 1,
  "created_at": "2024-03-16T11:45:00",
  "updated_at": "2024-03-16T14:15:00",
  "deleted_at": null,
  "is_deprecated": true,
  "is_revoked": false,
  "revoked_by_id": null,
  "version": 3
}
```

**Permissions:**
- Only admin users can deprecate environments

### Revoke Environment

```
POST /api/v1/environments/{environment_id}/revoke
```

Revokes an environment.

**Path Parameters:**
- `environment_id` (integer, required): The ID of the environment to revoke

**Response:**
```json
{
  "id": 2,
  "name": "Updated Staging Environment",
  "description": "Staging systems environment",
  "type": "staging",
  "status": "inactive",
  "created_by": 1,
  "created_at": "2024-03-16T11:45:00",
  "updated_at": "2024-03-16T14:30:00",
  "deleted_at": null,
  "is_deprecated": true,
  "is_revoked": true,
  "revoked_by_id": 1,
  "version": 4
}
```

**Permissions:**
- Only admin users can revoke environments

## Data Models

### Environment

The Environment model represents a testing environment.

**Fields:**
- `id` (integer): Unique identifier for the environment
- `name` (string): Name of the environment
- `description` (string): Detailed description of the environment
- `type` (string): Type of environment (production, staging, development, test, other)
- `status` (string): Status of the environment (active, inactive, deprecated)
- `created_by` (integer): ID of the user who created the environment
- `created_at` (datetime): Timestamp when the environment was created
- `updated_at` (datetime): Timestamp when the environment was last updated
- `deleted_at` (datetime, nullable): Timestamp when the environment was soft-deleted, or null if not deleted
- `is_deprecated` (boolean): Flag indicating if the environment is deprecated
- `is_revoked` (boolean): Flag indicating if the environment is revoked
- `revoked_by_id` (integer, nullable): ID of the user who revoked the environment, or null if not revoked
- `version` (integer): Version number for optimistic locking

## Error Handling

The API returns appropriate HTTP status codes and error messages for different scenarios:

- `400 Bad Request`: Invalid input data
- `401 Unauthorized`: Missing or invalid authentication
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server-side error

Error responses include a JSON body with details about the error:

```json
{
  "detail": "Error message describing the issue"
}
```

## Authentication and Authorization

All Environment Management API endpoints require authentication using JWT tokens. Include the token in the Authorization header:

```
Authorization: Bearer <token>
```

Access to endpoints is controlled by role-based permissions:
- Regular users can only access and modify their own environments
- Admin users have full access to all environments and operations 