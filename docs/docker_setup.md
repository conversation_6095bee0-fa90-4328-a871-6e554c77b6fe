# Docker Setup for RegressionRigor

This document provides instructions for setting up and running the RegressionRigor application using Docker.

## Prerequisites

- Docker
- Docker Compose

## Docker Configuration

All Docker-related files are located in the `.dockerwrapper` directory:

- `Dockerfile` - Docker configuration for the FastAPI backend
- `Dockerfile.flask` - Docker configuration for the Flask frontend
- `docker-compose.yml` - Docker Compose configuration for orchestrating all services
- `.dockerignore` - Files to exclude from Docker builds
- `docker-entrypoint.sh` - Entrypoint script for Docker containers
- `init_db.py` - Script for initializing the database in Docker containers

## Quick Start

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd regression-rigor
   ```

2. Start the application:
   ```bash
   docker-compose -f .dockerwrapper/docker-compose.yml up -d
   ```

3. Access the application:
   - FastAPI Backend: http://localhost:8010
   - FastAPI Swagger UI: http://localhost:8010/docs
   - Flask Frontend: http://localhost:3010

## Services

The application consists of three Docker services:

1. **postgres** - PostgreSQL database
   - Host Port: 5440 (maps to container port 5432)
   - User: regrigor
   - Password: regrigor_password
   - Database: regrigor_db

2. **api** - FastAPI backend
   - Host Port: 8010 (maps to container port 5001)
   - Environment variables:
     - DATABASE_URL=*****************************************************/regrigor_db
     - SESSION_SECRET=dev_session_secret_key_12345
     - JWT_SECRET_KEY=dev_jwt_secret_key_67890
     - JWT_ALGORITHM=HS256
     - REDIS_URL=redis://redis:6379/0

3. **web** - Flask frontend
   - Host Port: 3010 (maps to container port 5000)
   - Environment variables:
     - DATABASE_URL=*****************************************************/regrigor_db
     - SESSION_SECRET=dev_session_secret_key_12345
     - JWT_SECRET_KEY=dev_jwt_secret_key_67890
     - JWT_ALGORITHM=HS256
     - FLASK_APP=flask_app.py
     - FLASK_DEBUG=1
     - REDIS_URL=redis://redis:6379/0
     - API_URL=http://api:5001

## Development Workflow

The Docker setup is configured for development with these features:

1. **Volume Mounting**: The project directory is mounted into the containers, so code changes are reflected immediately.
2. **Auto-reload**: Both FastAPI and Flask are configured to auto-reload when code changes.
3. **Persistent Database**: PostgreSQL data is stored in a Docker volume for persistence between restarts.

## Common Commands

- Start all services:
  ```bash
  docker-compose -f .dockerwrapper/docker-compose.yml up -d
  ```

- Stop all services:
  ```bash
  docker-compose -f .dockerwrapper/docker-compose.yml down
  ```

- View logs:
  ```bash
  docker-compose -f .dockerwrapper/docker-compose.yml logs -f
  ```

- View logs for a specific service:
  ```bash
  docker-compose -f .dockerwrapper/docker-compose.yml logs -f api
  ```

- Restart a specific service:
  ```bash
  docker-compose -f .dockerwrapper/docker-compose.yml restart api
  ```

- Rebuild services (after Dockerfile changes):
  ```bash
  docker-compose -f .dockerwrapper/docker-compose.yml up -d --build
  ```

- Stop and remove all services and volumes (data will be lost):
  ```bash
  docker-compose -f .dockerwrapper/docker-compose.yml down -v
  ```

## Database Management

- Access the PostgreSQL shell:
  ```bash
  docker-compose -f .dockerwrapper/docker-compose.yml exec postgres psql -U regrigor -d regrigor_db
  ```

- Run database migrations:
  ```bash
  docker-compose -f .dockerwrapper/docker-compose.yml exec api alembic upgrade head
  ```

- Create a new migration:
  ```bash
  docker-compose -f .dockerwrapper/docker-compose.yml exec api alembic revision --autogenerate -m "description"
  ```

## Troubleshooting

- If services fail to start, check logs:
  ```bash
  docker-compose -f .dockerwrapper/docker-compose.yml logs
  ```

- Reset the database if needed:
  ```bash
  docker-compose -f .dockerwrapper/docker-compose.yml down -v
  docker-compose -f .dockerwrapper/docker-compose.yml up -d
  ```

- If you encounter dependency issues, rebuild the containers:
  ```bash
  docker-compose -f .dockerwrapper/docker-compose.yml up -d --build
  ```

## Accessing Services

Once the Docker containers are running, you can access the following services:

- FastAPI Backend: http://localhost:8010
- FastAPI Swagger UI: http://localhost:8010/docs
- Flask Frontend: http://localhost:3010

## Service Details

### PostgreSQL

- Container Name: regrigor-postgres
- Host Port: 5440 (maps to container port 5432)
- Credentials: regrigor/regrigor_password
- Database: regrigor_db

### FastAPI Backend

- Container Name: regrigor-api
- Host Port: 8010 (maps to container port 5001)
- Environment Variables:
  - DATABASE_URL: *****************************************************/regrigor_db
  - SESSION_SECRET: dev_session_secret_key_12345
  - JWT_SECRET_KEY: dev_jwt_secret_key_67890
  - JWT_ALGORITHM: HS256
  - REDIS_URL: redis://redis:6379/0

### Flask Frontend

- Container Name: regrigor-web
- Host Port: 3010 (maps to container port 5000)
- Environment Variables:
  - DATABASE_URL: *****************************************************/regrigor_db
  - SESSION_SECRET: dev_session_secret_key_12345
  - JWT_SECRET_KEY: dev_jwt_secret_key_67890
  - JWT_ALGORITHM: HS256
  - FLASK_APP: flask_app.py
  - FLASK_DEBUG: 1
  - REDIS_URL: redis://redis:6379/0
  - API_URL: http://api:5001 