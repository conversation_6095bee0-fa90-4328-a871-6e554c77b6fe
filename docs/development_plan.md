# RegressionRigor Development Plan

This document outlines the development plan for the RegressionRigor project, based on the comprehensive plan we created earlier.

## Current Status

We have successfully set up the Docker environment for the RegressionRigor project:

- PostgreSQL database running on port 5440
- FastAPI backend running on port 8010
- Flask frontend running on port 3010
- Redis cache running on port 9010

All services are accessible and working properly.

## Next Steps

### Phase 1: Foundation Setup (Remaining Tasks)

1. **Database Schema Verification**
   - Review existing database schema
   - Ensure all required tables are created
   - Verify relationships between tables
   - Add any missing tables or fields

2. **Authentication System Enhancement**
   - Implement JWT token-based authentication
   - Set up user roles and permissions
   - Create user management endpoints
   - Implement password reset functionality

3. **API Documentation**
   - Document all existing API endpoints
   - Add examples and usage instructions
   - Ensure all endpoints have proper validation
   - Implement API versioning

### Phase 2: MITRE Framework Integration

1. **MITRE ATT&CK Integration**
   - Verify existing MITRE ATT&CK models
   - Implement import/export functionality
   - Create version management system
   - Build relationship tracking between techniques, tactics, and groups

2. **MITRE ATLAS Integration**
   - Set up ATLAS data models
   - Create import process from the submodule
   - Implement version control for ATLAS data
   - Build relationship mapping between ATLAS components

3. **Framework Visualization**
   - Develop ATT&CK heat map visualization
   - Create ATLAS visualization components
   - Implement technique relationship mapping
   - Build framework navigation interface

### Phase 3: Core Platform Features

1. **Assessment Framework**
   - Implement campaign management system
   - Build test case structure with CRUD operations
   - Create assessment workflows
   - Develop template-based assessment capabilities

2. **Red Team Features**
   - Build operator guidance systems
   - Implement attack planning tools
   - Create test execution tracking
   - Develop attacker tools integration

3. **Blue Team Features**
   - Implement outcome tracking
   - Build detection time monitoring
   - Create defense mechanism management
   - Develop evidence file handling

## Development Workflow

1. **Feature Development**
   - Create a new branch for each feature
   - Implement the feature with tests
   - Submit a pull request for review
   - Merge after approval

2. **Testing**
   - Write unit tests for all new code
   - Ensure test coverage is maintained
   - Run integration tests for API endpoints
   - Perform manual testing for UI components

3. **Documentation**
   - Update API documentation for new endpoints
   - Document database schema changes
   - Update user guides for new features
   - Keep architecture diagrams current

## Immediate Tasks

1. **Database Schema Verification**
   - Run `docker-compose exec postgres psql -U regrigor -d regrigor_db -c "\dt"` to list all tables
   - Compare with the schema documentation
   - Identify any missing tables or fields

2. **API Endpoint Documentation**
   - Access the Swagger UI at http://localhost:8010/docs
   - Review existing endpoints
   - Identify missing endpoints or functionality

3. **User Authentication Implementation**
   - Review existing authentication code
   - Implement JWT token generation and validation
   - Create user registration and login endpoints
   - Set up role-based access control

## Resources

- Docker setup: See DOCKER_SETUP.md
- API documentation: http://localhost:8010/docs
- Flask frontend: http://localhost:3010
- Database schema: See docs/schema.md
- Architecture: See docs/architecture.md

## Development Environment

The development environment consists of:

- PostgreSQL database running on port 5440
- FastAPI backend running on port 8010
- Flask frontend running on port 3010
- Redis cache running on port 9010

## Testing

To test the application:

1. Start the Docker containers using `docker-compose -f .dockerwrapper/docker-compose.yml up -d`
2. Access the Swagger UI at http://localhost:8010/docs
3. Run the test suite using `pytest`

## Documentation

- API documentation: http://localhost:8010/docs
- Flask frontend: http://localhost:3010
- Database schema: See docs/schema.md
- Architecture: See docs/architecture.md 