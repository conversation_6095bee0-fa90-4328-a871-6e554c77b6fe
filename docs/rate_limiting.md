# Rate Limiting Implementation

This document describes the rate limiting implementation for the Regression Rigor API.

## Overview

Rate limiting is implemented using two complementary approaches:

1. **Global Middleware**: A path-based rate limiting middleware that applies to all requests
2. **Endpoint-specific Dependencies**: Fine-grained rate limiting applied to specific endpoints

This dual approach provides comprehensive protection against API abuse while allowing for flexible configuration.

## Configuration

### Global Middleware

The global rate limiting middleware is defined in `api/middleware/rate_limit.py` and configured in `api/main.py`. It provides:

- **Default Rate Limit**: 100 requests per minute for most endpoints
- **Path-based Rate Limits**: Different limits for different endpoint patterns:
  - Authentication endpoints: 5 requests per minute
  - Admin endpoints: 20 requests per minute
  - Data modification endpoints: 20 requests per minute
- **IP Whitelisting/Blacklisting**: Support for exempting or blocking specific IP addresses
- **Proper Error Responses**: 429 responses with Retry-After headers

### Endpoint-specific Dependencies

Endpoint-specific rate limiting is implemented using the `fastapi-limiter` library with <PERSON><PERSON> as the backend. The following rate limiters are available in `api/utils/rate_limiter.py`:

- **Standard Rate Limit**: 100 requests per minute
- **Strict Rate Limit**: 20 requests per minute
- **Authentication Rate Limit**: 5 requests per minute
- **User-specific Rate Limit**: 200 requests per minute per user

## Implementation Details

### Global Middleware Implementation

The `RateLimiter` class in `api/middleware/rate_limit.py` implements a sliding window algorithm to track and limit requests. Key features include:

- **Path Pattern Matching**: Uses regex to match request paths and apply appropriate limits
- **Client IP Extraction**: Properly handles X-Forwarded-For headers for clients behind proxies
- **Sliding Window**: Tracks requests within a configurable time window
- **Detailed Logging**: Logs rate limit violations with client and path information

### Redis Integration

For endpoint-specific rate limiting, Redis is used as the backend for storing rate limiting data. The Redis connection is configured in the `docker-compose.yml` file and initialized in the `api/main.py` file during application startup.

### Application to Endpoints

Rate limiting is applied to endpoints using FastAPI's dependency injection system. Each endpoint includes a rate limiter dependency appropriate for its sensitivity and expected usage pattern:

- **Authentication Endpoints**: Use the strict authentication rate limiter (5 requests/minute)
- **User Management Endpoints**: Use a mix of standard and strict rate limiters
- **Two-Factor Authentication Endpoints**: Use strict rate limiters for sensitive operations
- **MITRE ATT&CK Endpoints**: Use standard rate limiters for read operations and strict rate limiters for write operations

## Error Handling

When a client exceeds the rate limit, the API returns a `429 Too Many Requests` HTTP status code with a message indicating the rate limit has been exceeded. The response includes a `Retry-After` header indicating when the client can retry the request.

Example response:
```json
{
  "detail": "Too many requests",
  "retry_after": 30
}
```

## Monitoring and Maintenance

Rate limiting statistics can be monitored through:

1. **Application Logs**: Rate limit violations are logged with client IP and path information
2. **Redis Monitoring**: For endpoint-specific rate limiting, Redis commands can be used:

```
# Get all rate limiting keys
KEYS *fastapi-limiter:*

# Get information about a specific rate limit
GET fastapi-limiter:endpoint:127.0.0.1
```

## Configuration Options

The global rate limiter can be configured with the following options:

```python
RateLimiter(
    times=100,              # Default requests per window
    minutes=1,              # Default window size in minutes
    path_limits={           # Path-specific limits
        "/api/v1/auth/.*": {"times": 5, "minutes": 1}
    },
    whitelist_ips=["***********"],  # IPs exempt from rate limiting
    blacklist_ips=["********"]      # IPs always blocked
)
```

## Future Enhancements

Planned enhancements to the rate limiting system include:

- **Dynamic Rate Limiting**: Adjust rate limits based on server load or time of day
- **Rate Limit Headers**: Add informational headers about current rate limit usage
- **Enhanced Monitoring**: Add more detailed monitoring and alerting for rate limit violations
- **User Tier-based Limits**: Different rate limits for different user subscription tiers 