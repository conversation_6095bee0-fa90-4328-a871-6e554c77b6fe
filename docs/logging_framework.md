# Unified Logging and Testing Framework

## Overview

The Unified Logging and Testing Framework is a comprehensive solution for standardized logging and testing across both API and UI layers of the Regression Rigor application. It provides a consistent approach to debugging, error tracking, and performance monitoring throughout the entire application stack.

## Core Features

- **Unified Logging**: Consistent logging across API and UI with correlation IDs
- **Structured Log Entries**: All logs follow a standardized format with contextual information
- **Performance Metrics**: Collect and analyze performance data
- **Test Scenario Management**: Create, store, and execute test scenarios
- **Test Result Tracking**: Record and analyze test results
- **Cross-Layer Correlation**: Track requests across UI and API boundaries
- **Configurable Verbosity**: Adjust logging levels based on environment needs

## Architecture

### API Components

- **Logger Class**: Core logging functionality for API
- **Database Models**: Store logs, metrics, test scenarios, and results
- **API Endpoints**: Access and manage logs, tests, and configuration
- **Middleware**: Automatic request/response logging

### UI Components

- **Logger Utility**: Client-side logging with API integration
- **Test Runner**: Execute and record UI test scenarios
- **Dashboard**: Visualize logs, metrics, and test results

## Implementation Details

### Database Schema

The framework uses four primary tables:

1. **log_entries**: Store log messages from API and UI
2. **performance_metrics**: Store performance measurements
3. **test_scenarios**: Store test definitions
4. **test_results**: Store test execution results

### Logger Configuration

The logger can be configured with different verbosity levels:

- **debug**: Detailed information for debugging
- **info**: General information about system operation
- **warning**: Potential issues that aren't errors
- **error**: Error conditions that affect operation
- **critical**: Critical conditions requiring immediate action

## Usage Guide

### API Logging

```python
from api.services.logger import Logger

# Get logger instance
logger = Logger()

# Basic logging
logger.info("User logged in successfully", "auth_service")

# Logging with metadata
logger.info("Order created", "order_service", {
    "order_id": "12345",
    "total": 99.95,
    "items": 3
})

# Error logging
try:
    result = some_operation()
except Exception as e:
    logger.log_exception(e, "order_service")

# Performance tracking
@logger.track_time(metric_type="database_query", component="order_service")
def get_user_orders(user_id):
    # Function implementation
    return orders
```

### UI Logging

```javascript
import logger from '../utils/logger';

// Basic logging
logger.info("Page loaded", "UserDashboard");

// Logging with metadata
logger.info("User clicked checkout", "ShoppingCart", {
    itemCount: 5,
    cartTotal: 125.50
});

// Error logging
try {
    const result = processUserInput(data);
} catch (error) {
    logger.exception(error, "input_validation");
}

// Performance measurement
async function loadDashboardData() {
    return await logger.timeFunction(
        async () => {
            return await api.getDashboardData();
        },
        "api_call",
        "dashboard"
    );
}
```

### Creating Test Scenarios

```javascript
import testRunner from '../utils/testRunner';

// Create a test scenario
async function createLoginTest() {
    const steps = [
        testRunner.buildNavigateStep('/login', 'Navigate to login page'),
        testRunner.buildInputStep('#username', 'testuser', 'Enter username'),
        testRunner.buildInputStep('#password', 'password123', 'Enter password'),
        testRunner.buildClickStep('#login-button', 'Click login button'),
        testRunner.buildWaitStep(1000, 'Wait for redirect'),
        testRunner.buildAssertStep('contains(#welcome-message, "Welcome")', 'Verify login success')
    ];
    
    await testRunner.createUITestScenario(
        'Login Test',
        'Tests the user login functionality',
        steps
    );
}

// Run a test
async function runTest(scenarioId) {
    await testRunner.runTest(scenarioId, (status) => {
        console.log(`Test status: ${status.status}`);
    });
}
```

## Logging Dashboard

The logging dashboard provides a visual interface for exploring logs, metrics, and test results. Access it at `/logging-dashboard`.

### Dashboard Features

- **Logs Tab**: View, filter, and search logs
- **Tests Tab**: Create, manage, and run test scenarios
- **Metrics Tab**: View performance metrics

## API Reference

### Logging Endpoints

- `POST /api/logs`: Create a new log entry
- `GET /api/logs`: Query logs with filtering
- `GET /api/logs/{id}`: Get specific log entry
- `GET /api/logs/correlation/{correlation_id}`: Get logs by correlation ID

### Test Endpoints

- `POST /api/testing/scenarios`: Create a test scenario
- `GET /api/testing/scenarios`: List test scenarios
- `PUT /api/testing/scenarios/{id}`: Update a test scenario
- `DELETE /api/testing/scenarios/{id}`: Delete a test scenario
- `POST /api/testing/results`: Record test results
- `GET /api/testing/results`: Query test results

## Best Practices

1. **Use Structured Logging**: Include relevant context in logs
2. **Use Correlation IDs**: For tracking requests across components
3. **Choose Appropriate Log Levels**: Don't overuse high-severity levels
4. **Sanitize Sensitive Data**: Never log passwords, tokens, or PII
5. **Create Focused Tests**: Each test scenario should verify a specific flow
6. **Keep Tests Independent**: Tests shouldn't depend on the results of other tests
7. **Record Performance Metrics**: For identifying bottlenecks

## Troubleshooting

### Common Issues

**Issue**: Logs aren't appearing in the dashboard
- Check that the logger is properly initialized
- Verify that the log level is appropriate for the message
- Check API connectivity from the UI

**Issue**: Tests are failing intermittently
- Add wait steps for asynchronous operations
- Make assertions more resilient to timing issues
- Check for dependencies on external services

**Issue**: High logging overhead
- Reduce log verbosity in production
- Enable batch logging for UI components
- Implement log sampling for high-volume components

## Contributing

When enhancing the logging framework:

1. Follow the established patterns for consistency
2. Add appropriate unit tests
3. Update documentation with new features
4. Consider performance impact of changes 