# Test Case Management API

## Overview

The Test Case Management API provides endpoints for managing security test cases within the RegressionRigor platform. Test cases are the building blocks of security testing campaigns and assessments, representing individual security tests that can be executed to verify the security posture of a system.

This API allows users to create, retrieve, update, and delete test cases, as well as perform operations such as deprecation, revocation, and restoration. It also provides endpoints for bulk operations and statistics.

## API Endpoints

### List Test Cases

```
GET /api/v1/test-cases
```

Retrieves a list of test cases with optional filtering.

#### Query Parameters

- `skip` (integer, optional): Number of records to skip (for pagination). Default: 0.
- `limit` (integer, optional): Maximum number of records to return. Default: 100, Max: 1000.
- `status` (string, optional): Filter by test case status (draft, active, deprecated, archived).
- `type` (string, optional): Filter by test case type (manual, automated, hybrid).
- `priority` (string, optional): Filter by test case priority (low, medium, high, critical).
- `complexity` (string, optional): Filter by test case complexity (simple, moderate, complex).
- `tags` (array of strings, optional): Filter by tags.
- `mitre_techniques` (array of strings, optional): Filter by MITRE ATT&CK techniques.
- `created_by` (integer, optional): Filter by creator ID.
- `search` (string, optional): Search term for name and description.
- `include_deleted` (boolean, optional): Include soft-deleted test cases (admin only). Default: false.

#### Response

```json
[
  {
    "id": 1,
    "name": "Test SQL Injection",
    "description": "Test for SQL injection vulnerabilities",
    "type": "manual",
    "status": "active",
    "priority": "high",
    "complexity": "moderate",
    "prerequisites": "Access to the application",
    "steps": ["Step 1: Navigate to login page", "Step 2: Enter SQL injection payload"],
    "expected_result": "Application should sanitize input and prevent SQL injection",
    "tags": ["security", "injection", "sql"],
    "mitre_techniques": ["T1190", "T1212"],
    "created_by": 1,
    "created_at": "2024-03-20T10:00:00Z",
    "updated_at": "2024-03-20T10:00:00Z",
    "deleted_at": null,
    "is_deprecated": false,
    "is_revoked": false,
    "revoked_by_id": null,
    "version": "1.0.0"
  },
  // ...
]
```

### Get Test Case Statistics

```
GET /api/v1/test-cases/stats
```

Retrieves statistics about test cases.

#### Response

```json
{
  "total": 100,
  "by_status": {
    "draft": 20,
    "active": 60,
    "deprecated": 15,
    "archived": 5
  },
  "by_type": {
    "manual": 50,
    "automated": 30,
    "hybrid": 20
  },
  "by_priority": {
    "low": 10,
    "medium": 40,
    "high": 30,
    "critical": 20
  },
  "by_complexity": {
    "simple": 30,
    "moderate": 50,
    "complex": 20
  }
}
```

### Get Test Case by ID

```
GET /api/v1/test-cases/{test_case_id}
```

Retrieves a specific test case by its ID.

#### Path Parameters

- `test_case_id` (integer, required): ID of the test case to retrieve.

#### Query Parameters

- `include_deleted` (boolean, optional): Include soft-deleted test cases (admin only). Default: false.

#### Response

```json
{
  "id": 1,
  "name": "Test SQL Injection",
  "description": "Test for SQL injection vulnerabilities",
  "type": "manual",
  "status": "active",
  "priority": "high",
  "complexity": "moderate",
  "prerequisites": "Access to the application",
  "steps": ["Step 1: Navigate to login page", "Step 2: Enter SQL injection payload"],
  "expected_result": "Application should sanitize input and prevent SQL injection",
  "tags": ["security", "injection", "sql"],
  "mitre_techniques": ["T1190", "T1212"],
  "created_by": 1,
  "created_at": "2024-03-20T10:00:00Z",
  "updated_at": "2024-03-20T10:00:00Z",
  "deleted_at": null,
  "is_deprecated": false,
  "is_revoked": false,
  "revoked_by_id": null,
  "version": "1.0.0"
}
```

### Create Test Case

```
POST /api/v1/test-cases
```

Creates a new test case.

#### Request Body

```json
{
  "name": "Test XSS Vulnerability",
  "description": "Test for cross-site scripting vulnerabilities",
  "type": "manual",
  "status": "draft",
  "priority": "high",
  "complexity": "moderate",
  "prerequisites": "Access to the application",
  "steps": ["Step 1: Navigate to input form", "Step 2: Enter XSS payload"],
  "expected_result": "Application should sanitize input and prevent XSS",
  "tags": ["security", "injection", "xss"],
  "mitre_techniques": ["T1059", "T1189"]
}
```

#### Response

```json
{
  "id": 2,
  "name": "Test XSS Vulnerability",
  "description": "Test for cross-site scripting vulnerabilities",
  "type": "manual",
  "status": "draft",
  "priority": "high",
  "complexity": "moderate",
  "prerequisites": "Access to the application",
  "steps": ["Step 1: Navigate to input form", "Step 2: Enter XSS payload"],
  "expected_result": "Application should sanitize input and prevent XSS",
  "tags": ["security", "injection", "xss"],
  "mitre_techniques": ["T1059", "T1189"],
  "created_by": 1,
  "created_at": "2024-03-20T10:00:00Z",
  "updated_at": "2024-03-20T10:00:00Z",
  "deleted_at": null,
  "is_deprecated": false,
  "is_revoked": false,
  "revoked_by_id": null,
  "version": "1.0.0"
}
```

### Bulk Create Test Cases

```
POST /api/v1/test-cases/bulk
```

Creates multiple test cases in bulk.

#### Request Body

```json
{
  "test_cases": [
    {
      "name": "Bulk Test Case 1",
      "description": "First bulk test case",
      "expected_result": "Expected result 1"
    },
    {
      "name": "Bulk Test Case 2",
      "description": "Second bulk test case",
      "expected_result": "Expected result 2"
    },
    {
      "name": "Bulk Test Case 3",
      "description": "Third bulk test case",
      "expected_result": "Expected result 3"
    }
  ]
}
```

#### Response

```json
{
  "test_cases": [
    {
      "id": 3,
      "name": "Bulk Test Case 1",
      "description": "First bulk test case",
      "type": "manual",
      "status": "draft",
      "priority": "medium",
      "complexity": "moderate",
      "prerequisites": null,
      "steps": [],
      "expected_result": "Expected result 1",
      "tags": [],
      "mitre_techniques": [],
      "created_by": 1,
      "created_at": "2024-03-20T10:00:00Z",
      "updated_at": "2024-03-20T10:00:00Z",
      "deleted_at": null,
      "is_deprecated": false,
      "is_revoked": false,
      "revoked_by_id": null,
      "version": "1.0.0"
    },
    // ... other test cases
  ],
  "total_created": 3,
  "failed_entries": []
}
```

### Update Test Case

```
PUT /api/v1/test-cases/{test_case_id}
```

Updates an existing test case.

#### Path Parameters

- `test_case_id` (integer, required): ID of the test case to update.

#### Request Body

```json
{
  "name": "Updated SQL Injection Test",
  "status": "active"
}
```

#### Response

```json
{
  "id": 1,
  "name": "Updated SQL Injection Test",
  "description": "Test for SQL injection vulnerabilities",
  "type": "manual",
  "status": "active",
  "priority": "high",
  "complexity": "moderate",
  "prerequisites": "Access to the application",
  "steps": ["Step 1: Navigate to login page", "Step 2: Enter SQL injection payload"],
  "expected_result": "Application should sanitize input and prevent SQL injection",
  "tags": ["security", "injection", "sql"],
  "mitre_techniques": ["T1190", "T1212"],
  "created_by": 1,
  "created_at": "2024-03-20T10:00:00Z",
  "updated_at": "2024-03-20T10:05:00Z",
  "deleted_at": null,
  "is_deprecated": false,
  "is_revoked": false,
  "revoked_by_id": null,
  "version": "1.0.1"
}
```

### Delete Test Case

```
DELETE /api/v1/test-cases/{test_case_id}
```

Soft-deletes a test case.

#### Path Parameters

- `test_case_id` (integer, required): ID of the test case to delete.

#### Response

No content (204)

### Restore Test Case

```
POST /api/v1/test-cases/{test_case_id}/restore
```

Restores a soft-deleted test case.

#### Path Parameters

- `test_case_id` (integer, required): ID of the test case to restore.

#### Response

```json
{
  "id": 1,
  "name": "Updated SQL Injection Test",
  "description": "Test for SQL injection vulnerabilities",
  "type": "manual",
  "status": "active",
  "priority": "high",
  "complexity": "moderate",
  "prerequisites": "Access to the application",
  "steps": ["Step 1: Navigate to login page", "Step 2: Enter SQL injection payload"],
  "expected_result": "Application should sanitize input and prevent SQL injection",
  "tags": ["security", "injection", "sql"],
  "mitre_techniques": ["T1190", "T1212"],
  "created_by": 1,
  "created_at": "2024-03-20T10:00:00Z",
  "updated_at": "2024-03-20T10:05:00Z",
  "deleted_at": null,
  "is_deprecated": false,
  "is_revoked": false,
  "revoked_by_id": null,
  "version": "1.0.1"
}
```

### Deprecate Test Case

```
POST /api/v1/test-cases/{test_case_id}/deprecate
```

Marks a test case as deprecated.

#### Path Parameters

- `test_case_id` (integer, required): ID of the test case to deprecate.

#### Request Body

```json
{
  "reason": "Replaced by a newer test case"
}
```

#### Response

```json
{
  "id": 1,
  "name": "Updated SQL Injection Test",
  "description": "Test for SQL injection vulnerabilities",
  "type": "manual",
  "status": "deprecated",
  "priority": "high",
  "complexity": "moderate",
  "prerequisites": "Access to the application",
  "steps": ["Step 1: Navigate to login page", "Step 2: Enter SQL injection payload"],
  "expected_result": "Application should sanitize input and prevent SQL injection",
  "tags": ["security", "injection", "sql"],
  "mitre_techniques": ["T1190", "T1212"],
  "created_by": 1,
  "created_at": "2024-03-20T10:00:00Z",
  "updated_at": "2024-03-20T10:10:00Z",
  "deleted_at": null,
  "is_deprecated": true,
  "is_revoked": false,
  "revoked_by_id": null,
  "version": "1.0.2"
}
```

### Revoke Test Case

```
POST /api/v1/test-cases/{test_case_id}/revoke
```

Revokes a test case.

#### Path Parameters

- `test_case_id` (integer, required): ID of the test case to revoke.

#### Request Body

```json
{
  "reason": "Test case contains incorrect information"
}
```

#### Response

```json
{
  "id": 1,
  "name": "Updated SQL Injection Test",
  "description": "Test for SQL injection vulnerabilities",
  "type": "manual",
  "status": "deprecated",
  "priority": "high",
  "complexity": "moderate",
  "prerequisites": "Access to the application",
  "steps": ["Step 1: Navigate to login page", "Step 2: Enter SQL injection payload"],
  "expected_result": "Application should sanitize input and prevent SQL injection",
  "tags": ["security", "injection", "sql"],
  "mitre_techniques": ["T1190", "T1212"],
  "created_by": 1,
  "created_at": "2024-03-20T10:00:00Z",
  "updated_at": "2024-03-20T10:15:00Z",
  "deleted_at": null,
  "is_deprecated": true,
  "is_revoked": true,
  "revoked_by_id": 1,
  "version": "1.0.3"
}
```

## Permissions

The Test Case Management API enforces the following permissions:

- **Regular Users**:
  - Can create test cases
  - Can view, update, and delete their own test cases
  - Can deprecate their own test cases
  - Cannot restore or revoke test cases
  - Cannot see soft-deleted test cases

- **Admin Users**:
  - Can create, view, update, and delete any test case
  - Can deprecate any test case
  - Can restore and revoke test cases
  - Can see soft-deleted test cases

## Data Model

### Test Case

| Field            | Type      | Description                                      |
|------------------|-----------|--------------------------------------------------|
| id               | Integer   | Unique identifier for the test case              |
| name             | String    | Name of the test case                            |
| description      | String    | Detailed description of the test case            |
| type             | Enum      | Type of test case (manual, automated, hybrid)    |
| status           | Enum      | Status of the test case (draft, active, deprecated, archived) |
| priority         | Enum      | Priority of the test case (low, medium, high, critical) |
| complexity       | Enum      | Complexity of the test case (simple, moderate, complex) |
| prerequisites    | String    | Prerequisites for running the test case          |
| steps            | JSON      | Steps to execute the test case                   |
| expected_result  | String    | Expected result of the test case                 |
| tags             | JSON      | Tags associated with the test case               |
| mitre_techniques | JSON      | MITRE ATT&CK techniques associated with the test case |
| created_by       | Integer   | ID of the user who created the test case         |
| created_at       | DateTime  | Timestamp when the test case was created         |
| updated_at       | DateTime  | Timestamp of the last test case update           |
| deleted_at       | DateTime  | Timestamp when the test case was deleted (null if not deleted) |
| is_deprecated    | Boolean   | Whether the test case is deprecated              |
| is_revoked       | Boolean   | Whether the test case is revoked                 |
| revoked_by_id    | Integer   | ID of the user who revoked the test case         |
| version          | String    | Version of the test case                         |

## Error Handling

The API returns appropriate HTTP status codes and error messages for different scenarios:

- `400 Bad Request`: Invalid request parameters or body
- `401 Unauthorized`: Missing or invalid authentication
- `403 Forbidden`: Insufficient permissions to perform the operation
- `404 Not Found`: Test case not found
- `500 Internal Server Error`: Server-side error

## Authentication and Authorization

All endpoints require authentication using JWT tokens. Include the token in the Authorization header:

```
Authorization: Bearer <token>
```

The API enforces role-based access control (RBAC) to ensure users can only access resources they have permission for. 