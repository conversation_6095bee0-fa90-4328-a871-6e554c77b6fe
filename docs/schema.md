# Database Schema Documentation

## Overview
The database schema is designed to support the cybersecurity data platform, incorporating test case management, MITRE ATT&CK Framework integration, and MITRE ATLAS integration. Following MITRE's design philosophy, we implement versioning, deprecation tracking, and relationship mapping while preserving historical data.

## Entity Relationship Diagram

```mermaid
erDiagram
    mitre_versions ||--o{ mitre_techniques : "contains"
    mitre_versions ||--o{ mitre_tactics : "contains"
    mitre_versions ||--o{ mitre_groups : "contains"
    mitre_versions ||--o{ mitre_software : "contains"
    mitre_versions ||--o{ mitre_mitigations : "contains"
    mitre_techniques }|--o{ technique_tactic_association : "has"
    mitre_tactics }|--o{ technique_tactic_association : "has"
    mitre_groups }|--o{ group_technique_association : "uses"
    mitre_techniques }|--o{ group_technique_association : "used_by"
    mitre_software }|--o{ software_technique_association : "implements"
    mitre_techniques }|--o{ software_technique_association : "implemented_by"
    mitre_groups }|--o{ group_to_software : "uses"
    mitre_software }|--o{ group_to_software : "used_by"
    mitre_techniques }|--o{ mitigation_technique_association : "mitigated_by"
    mitre_mitigations }|--o{ mitigation_technique_association : "mitigates"

    mitre_versions {
        int id PK
        string version UK
        datetime import_date
        boolean is_current
        enum technology_domain
        datetime created_on
        datetime updated_at
        datetime deleted_at
        boolean is_deprecated
        boolean is_revoked
        int revoked_by_id
    }

    mitre_mitigations {
        int id PK
        string mitigation_id
        string name
        string description
        int version_id FK
        json extra_data
        datetime created_on
        datetime updated_at
        datetime deleted_at
        boolean is_deprecated
        boolean is_revoked
        int revoked_by_id
    }

    mitre_groups {
        int id PK
        string group_id
        string name
        string description
        int version_id FK
        json aliases
        json extra_data
        datetime created_on
        datetime updated_at
        datetime deleted_at
        boolean is_deprecated
        boolean is_revoked
        int revoked_by_id
    }

    mitre_software {
        int id PK
        string software_id
        string name
        string description
        int version_id FK
        string software_type
        json platforms
        json extra_data
        datetime created_on
        datetime updated_at
        datetime deleted_at
        boolean is_deprecated
        boolean is_revoked
        int revoked_by_id
    }
```

## ATLAS Integration Tables

### ATLAS Versions
Tracks different versions of imported ATLAS data:

```sql
CREATE TABLE atlas_versions (
    id INTEGER PRIMARY KEY,
    version VARCHAR NOT NULL UNIQUE,
    name VARCHAR NOT NULL,
    description TEXT,
    import_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_current BOOLEAN DEFAULT FALSE,
    created_on TIMESTAMP NOT NULL,
    updated_at TIMESTAMP
);
```

### ATLAS Tactics
Stores ATLAS tactics with version control:

```sql
CREATE TABLE atlas_tactics (
    id INTEGER PRIMARY KEY,
    external_id VARCHAR NOT NULL,
    name VARCHAR NOT NULL,
    description TEXT,
    version_id INTEGER REFERENCES atlas_versions(id),
    created_on TIMESTAMP NOT NULL,
    updated_at TIMESTAMP,
    UNIQUE(external_id, version_id)
);
```

### ATLAS Techniques
Stores ATLAS techniques including support for sub-techniques:

```sql
CREATE TABLE atlas_techniques (
    id INTEGER PRIMARY KEY,
    external_id VARCHAR NOT NULL,
    name VARCHAR NOT NULL,
    description TEXT,
    version_id INTEGER REFERENCES atlas_versions(id),
    tactic_id INTEGER REFERENCES atlas_tactics(id),
    is_subtechnique BOOLEAN DEFAULT FALSE,
    parent_technique_id INTEGER REFERENCES atlas_techniques(id),
    created_on TIMESTAMP NOT NULL,
    updated_at TIMESTAMP,
    revoked BOOLEAN DEFAULT FALSE,
    deprecated BOOLEAN DEFAULT FALSE,
    UNIQUE(external_id, version_id)
);
```

### ATLAS Matrix
Represents the ATLAS matrix structure:

```sql
CREATE TABLE atlas_matrix (
    id INTEGER PRIMARY KEY,
    name VARCHAR NOT NULL,
    description TEXT,
    version_id INTEGER REFERENCES atlas_versions(id),
    created_on TIMESTAMP NOT NULL,
    updated_at TIMESTAMP
);
```

### ATLAS Matrix Items
Maps techniques to tactics within the matrix:

```sql
CREATE TABLE atlas_matrix_items (
    id INTEGER PRIMARY KEY,
    matrix_id INTEGER REFERENCES atlas_matrix(id),
    technique_id INTEGER REFERENCES atlas_techniques(id),
    tactic_id INTEGER REFERENCES atlas_tactics(id),
    color VARCHAR,
    show_subtechniques BOOLEAN DEFAULT TRUE,
    UNIQUE(matrix_id, technique_id, tactic_id)
);
```

## Core Tables
### Campaigns
Represents test campaigns that group related test cases.

```sql
CREATE TABLE campaigns (
    id INTEGER PRIMARY KEY,
    name VARCHAR NOT NULL,
    description TEXT,
    status VARCHAR DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Fields:**
- `id`: Primary key
- `name`: Campaign name (required)
- `description`: Optional campaign description
- `status`: Campaign status (active/inactive/completed)
- `created_at`: Timestamp of creation
- `updated_at`: Timestamp of last update

### Test Cases
Represents individual test cases within campaigns.

```sql
CREATE TABLE test_cases (
    id INTEGER PRIMARY KEY,
    name VARCHAR NOT NULL,
    description TEXT,
    campaign_id INTEGER REFERENCES campaigns(id),
    expected_result TEXT NOT NULL,
    actual_result TEXT,
    status VARCHAR DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Fields:**
- `id`: Primary key
- `name`: Test case name (required)
- `description`: Optional test case description
- `campaign_id`: Foreign key to campaigns table
- `expected_result`: Expected test outcome (required)
- `actual_result`: Actual test outcome
- `status`: Test status (pending/running/passed/failed)
- `created_at`: Timestamp of creation
- `updated_at`: Timestamp of last update

## MITRE ATT&CK Framework Tables

### MITRE Versions
Tracks different versions of imported MITRE data, following MITRE's versioning approach:

```sql
CREATE TABLE mitre_versions (
    id INTEGER PRIMARY KEY,
    version VARCHAR NOT NULL UNIQUE,
    import_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_current BOOLEAN DEFAULT FALSE,
    created_on TIMESTAMP NOT NULL,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP,
    is_deprecated BOOLEAN DEFAULT FALSE,
    is_revoked BOOLEAN DEFAULT FALSE,
    revoked_by_id INTEGER
);
```

**Fields:**
- `version`: MITRE ATT&CK version string
- `is_current`: Flag for current version
- State tracking fields inherited from SoftDeleteMixin

### MITRE Techniques
Stores MITRE ATT&CK techniques with version control:

```sql
CREATE TABLE mitre_techniques (
    id INTEGER PRIMARY KEY,
    technique_id VARCHAR NOT NULL,
    name VARCHAR NOT NULL,
    description TEXT,
    version_id INTEGER REFERENCES mitre_versions(id) ON DELETE CASCADE,
    extra_data JSONB,
    created_on TIMESTAMP NOT NULL,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP,
    is_deprecated BOOLEAN DEFAULT FALSE,
    is_revoked BOOLEAN DEFAULT FALSE,
    revoked_by_id INTEGER,
    UNIQUE(technique_id, version_id)
);
```

**Fields:**
- `technique_id`: MITRE technique ID (e.g., T1234)
- `version_id`: Links to specific MITRE version
- State tracking fields for historical preservation

### MITRE Tactics
Following MITRE's tactic categorization:

```sql
CREATE TABLE mitre_tactics (
    id INTEGER PRIMARY KEY,
    tactic_id VARCHAR NOT NULL,
    name VARCHAR NOT NULL,
    description TEXT,
    version_id INTEGER REFERENCES mitre_versions(id) ON DELETE CASCADE,
    data JSONB,
    created_on TIMESTAMP NOT NULL,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP,
    is_deprecated BOOLEAN DEFAULT FALSE,
    is_revoked BOOLEAN DEFAULT FALSE,
    revoked_by_id INTEGER,
    UNIQUE(tactic_id, version_id)
);
```

### MITRE Groups
Following MITRE's group/threat actor categorization:

```sql
CREATE TABLE mitre_groups (
    id INTEGER PRIMARY KEY,
    group_id VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    version_id INTEGER REFERENCES mitre_versions(id) ON DELETE CASCADE,
    aliases JSONB,
    extra_data JSONB,
    created_on TIMESTAMP NOT NULL,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP,
    is_deprecated BOOLEAN DEFAULT FALSE,
    is_revoked BOOLEAN DEFAULT FALSE,
    revoked_by_id INTEGER,
    UNIQUE(group_id, version_id)
);
```

### MITRE Software
Representing tools and malware used by threat actors:

```sql
CREATE TABLE mitre_software (
    id INTEGER PRIMARY KEY,
    software_id VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    version_id INTEGER REFERENCES mitre_versions(id) ON DELETE CASCADE,
    software_type VARCHAR(50),
    platforms JSONB,
    extra_data JSONB,
    created_on TIMESTAMP NOT NULL,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP,
    is_deprecated BOOLEAN DEFAULT FALSE,
    is_revoked BOOLEAN DEFAULT FALSE,
    revoked_by_id INTEGER,
    UNIQUE(software_id, version_id)
);
```

## Relationship Mapping

### Technique-Tactic Association
Maps techniques to tactics in a many-to-many relationship, similar to MITRE's matrix structure:

```sql
CREATE TABLE technique_tactic_association (
    technique_id INTEGER REFERENCES mitre_techniques(id) ON DELETE CASCADE,
    tactic_id INTEGER REFERENCES mitre_tactics(id) ON DELETE CASCADE,
    PRIMARY KEY (technique_id, tactic_id)
);
```

### Group-Software Association
Maps groups to their associated software:

```sql
CREATE TABLE mitre_group_to_software (
    group_id INTEGER REFERENCES mitre_groups(id) ON DELETE CASCADE,
    software_id INTEGER REFERENCES mitre_software(id) ON DELETE CASCADE,
    PRIMARY KEY (group_id, software_id)
);
```

### Software-Technique Association
Maps software to the techniques it implements:

```sql
CREATE TABLE mitre_software_to_technique (
    software_id INTEGER REFERENCES mitre_software(id) ON DELETE CASCADE,
    technique_id INTEGER REFERENCES mitre_techniques(id) ON DELETE CASCADE,
    PRIMARY KEY (software_id, technique_id)
);
```

## Mitigation Model
Following MITRE's mitigation design:

```sql
CREATE TABLE mitre_mitigations (
    id INTEGER PRIMARY KEY,
    mitigation_id VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    version_id INTEGER REFERENCES mitre_versions(id) ON DELETE CASCADE,
    extra_data JSONB,
    created_on TIMESTAMP NOT NULL,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP,
    is_deprecated BOOLEAN DEFAULT FALSE,
    is_revoked BOOLEAN DEFAULT FALSE,
    revoked_by_id INTEGER,
    UNIQUE(mitigation_id, version_id)
);
```

### Mitigation-Technique Association
Maps mitigations to the techniques they address:

```sql
CREATE TABLE mitigation_technique_association (
    mitigation_id INTEGER REFERENCES mitre_mitigations(id) ON DELETE CASCADE,
    technique_id INTEGER REFERENCES mitre_techniques(id) ON DELETE CASCADE,
    PRIMARY KEY (mitigation_id, technique_id)
);
```

## Data Migration Guidelines
### Soft Delete Implementation
- Records are never physically deleted
- `deleted_at` timestamp marks logical deletion
- Queries should filter out soft-deleted records by default
- Historical data remains accessible for auditing

### Version Management
- Each MITRE import creates a new version record
- Only one version can be marked as current
- Old versions are preserved but marked as non-current
- Objects reference their version via foreign keys

### Deprecation vs. Revocation
Following MITRE's approach:
- Deprecation: Object is no longer recommended but remains valid
- Revocation: Object has been replaced by a newer version
- Both states preserve the object while indicating its status

## Best Practices
1. Always use soft deletes instead of physical deletion
2. Track object history through timestamps
3. Maintain version relationships
4. Preserve deprecated/revoked objects
5. Use proper foreign key constraints

## Schema Evolution
- Use Alembic migrations for schema changes
- Test migrations on development before production
- Include both upgrade() and downgrade() implementations
- Document migration rationale and impact

This schema design ensures data integrity while maintaining historical records and relationships, aligning with MITRE ATT&CK's philosophy of preserving and tracking data changes over time.

## Relationships
1. Campaign → Test Cases: One-to-many
   - A campaign can have multiple test cases
   - Each test case belongs to one campaign

2. MITRE Version → Techniques/Tactics: One-to-many
   - Each version has multiple techniques and tactics
   - Each technique/tactic belongs to one version

3. Techniques ↔ Tactics: Many-to-many
   - Techniques can be associated with multiple tactics
   - Tactics can be associated with multiple techniques
   - Relationship managed through technique_tactic_association table

3. Group ↔ Software: Many-to-many
   - Groups can use multiple software tools
   - Software can be used by multiple groups
   - Relationship managed through mitre_group_to_software table

4. Software ↔ Techniques: Many-to-many
   - Software can implement multiple techniques
   - Techniques can be implemented by multiple software
   - Relationship managed through mitre_software_to_technique table

5. Version → Technology Domain: One-to-one
   - Each version belongs to one technology domain (Enterprise, Mobile, ICS)
   - Helps organize techniques and tactics by environment


## Constraints
1. Unique Constraints:
   - MITRE version strings must be unique
   - Technique IDs must be unique within a version
   - Tactic IDs must be unique within a version
   - Group IDs must be unique within a version
   - Software IDs must be unique within a version

2. Foreign Key Constraints:
   - test_cases.campaign_id → campaigns.id
   - mitre_techniques.version_id → mitre_versions.id
   - mitre_tactics.version_id → mitre_versions.id
   - mitre_groups.version_id → mitre_versions.id
   - mitre_software.version_id → mitre_versions.id
   - mitre_mitigations.version_id → mitre_versions.id

3. Delete Cascades:
   - Deleting a MITRE version cascades to its techniques, tactics, groups, and software.
   - Deleting a technique/tactic cascades to association table entries.
   - Deleting a group cascades to group-software association entries.
   - Deleting software cascades to software-technique association entries.


## Indexes
Important indexes for performance:
1. campaigns(id)
2. test_cases(campaign_id)
3. mitre_versions(version)
4. mitre_techniques(technique_id, version_id)
5. mitre_tactics(tactic_id, version_id)
6. mitre_groups(group_id, version_id)
7. mitre_software(software_id, version_id)
8. mitre_mitigations(mitigation_id, version_id)

## Model Inheritance and CRUD Operations

### Model Hierarchy
```mermaid
classDiagram
    class SoftDeleteMixin {
        +created_on: DateTime
        +updated_at: DateTime
        +deleted_at: DateTime
        +is_deprecated: Boolean
        +is_revoked: Boolean
        +revoked_by_id: Integer
        +version: String
        +get_all(db, skip_deleted)
        +get_by_id(db, id, skip_deleted)
        +soft_delete(db)
        +restore(db)
        +update(db, **kwargs)
        +create(db, **kwargs)
        +mark_deprecated(db, replacement_id)
    }

    class BaseModel {
        <<Abstract>>
        +id: Integer
        +metadata: MetaData
    }

    class User {
        +username: String
        +email: String
        +hashed_password: String
        +is_active: Boolean
    }

    class Campaign {
        +name: String
        +description: String
        +start_date: DateTime
        +end_date: DateTime
        +status: String
    }

    class TestCase {
        +name: String
        +description: String
        +technique_id: String
        +phase: String
        +outcome: String
    }

    BaseModel <|-- User
    BaseModel <|-- Campaign
    BaseModel <|-- TestCase
    SoftDeleteMixin <|-- User
    SoftDeleteMixin <|-- Campaign
    SoftDeleteMixin <|-- TestCase
```

### CRUD Operations Flow
```mermaid
sequenceDiagram
    participant Client
    participant CRUDRouter
    participant Model
    participant Database

    Client->>CRUDRouter: HTTP Request
    CRUDRouter->>Model: Call Model Method
    Model->>Database: Execute Query
    Database-->>Model: Return Result
    Model-->>CRUDRouter: Process Result
    CRUDRouter-->>Client: HTTP Response

    note over CRUDRouter: Generic CRUD Router<br>supports all models with<br>SoftDeleteMixin
```

## ATLAS Integration Guidelines

### Data Import Process
1. Each ATLAS import creates a new version record
2. Only one version can be marked as current
3. Previous versions are preserved but marked as non-current
4. Objects reference their version via foreign keys
5. Support for sub-techniques through parent-child relationships
6. Matrix structure preserves technique-tactic mappings
7. Color and display preferences stored in matrix items

### Best Practices
1. Use AtlasImporter service for data imports
2. Maintain version relationships
3. Handle sub-techniques appropriately
4. Preserve matrix structure
5. Track revoked and deprecated items
6. Use proper foreign key constraints

### Schema Evolution
- Use Alembic migrations for schema changes
- Test migrations on development before production
- Include both upgrade() and downgrade() implementations
- Document migration rationale and impact

This schema design ensures data integrity while maintaining historical records and relationships, aligning with MITRE ATT&CK's philosophy of preserving and tracking data changes over time.