# Enhanced Testcase Chaining & Sequencing

## Overview

The Enhanced Testcase Chaining & Sequencing feature provides comprehensive workflow management for RegressionRigor, enabling complex attack scenarios through interconnected testcase execution. This system supports sequential, parallel, and conditional execution patterns with full dependency management and visual representation.

## Features

### 1. Testcase Chain Management

- **Chain Creation**: Define named chains with configurable execution parameters
- **Chain Types**: Sequential, parallel, and conditional execution patterns
- **Chain Validation**: Structural integrity checks including cycle detection
- **Chain Versioning**: Track changes and maintain chain history

### 2. Node-Based Architecture

- **Node Types**: Start, standard, conditional, parallel, and end nodes
- **Visual Positioning**: UI coordinates for graphical chain representation
- **Execution Control**: Timeout, retry, and failure handling per node
- **Conditional Logic**: Expression-based conditional execution

### 3. Edge-Based Flow Control

- **Edge Types**: Standard, success path, failure path, conditional, and parallel
- **Flow Conditions**: Expression-based edge traversal conditions
- **Edge Weighting**: Priority-based edge selection
- **Visual Labels**: Human-readable edge descriptions

### 4. Execution Engine

- **Chain Execution**: Full chain orchestration with status tracking
- **Node Execution**: Individual testcase execution within chains
- **Dependency Resolution**: Automatic dependency satisfaction checking
- **Execution Monitoring**: Real-time status and progress tracking

### 5. Condition System

- **Preconditions**: Requirements that must be met before testcase execution
- **Postconditions**: Validations performed after testcase completion
- **Cleanup Conditions**: Cleanup operations for environment restoration
- **Validation Scripts**: Custom validation logic for condition checking

## Database Schema

### Core Tables

#### `testcase_chains`
Stores testcase chain definitions and metadata.

```sql
CREATE TABLE testcase_chains (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status chain_status_enum NOT NULL DEFAULT 'draft',
    chain_type VARCHAR(50) NOT NULL DEFAULT 'sequential',
    max_execution_time_minutes INTEGER NOT NULL DEFAULT 60 CHECK (max_execution_time_minutes > 0),
    retry_on_failure BOOLEAN NOT NULL DEFAULT FALSE,
    auto_cleanup BOOLEAN NOT NULL DEFAULT TRUE,
    created_by INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 1
);
```

#### `testcase_chain_nodes`
Stores individual nodes within testcase chains.

```sql
CREATE TABLE testcase_chain_nodes (
    id SERIAL PRIMARY KEY,
    chain_id INTEGER NOT NULL REFERENCES testcase_chains(id) ON DELETE CASCADE,
    testcase_id INTEGER NOT NULL REFERENCES test_cases(id) ON DELETE CASCADE,
    node_type node_type_enum NOT NULL DEFAULT 'standard',
    execution_order INTEGER NOT NULL DEFAULT 0,
    position_x FLOAT NOT NULL DEFAULT 0.0,
    position_y FLOAT NOT NULL DEFAULT 0.0,
    condition_expression TEXT,
    timeout_minutes INTEGER NOT NULL DEFAULT 30 CHECK (timeout_minutes > 0),
    retry_count INTEGER NOT NULL DEFAULT 0 CHECK (retry_count >= 0),
    continue_on_failure BOOLEAN NOT NULL DEFAULT FALSE,
    required_for_completion BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 1,
    UNIQUE(chain_id, testcase_id)
);
```

#### `testcase_chain_edges`
Stores connections between chain nodes.

```sql
CREATE TABLE testcase_chain_edges (
    id SERIAL PRIMARY KEY,
    source_node_id INTEGER NOT NULL REFERENCES testcase_chain_nodes(id) ON DELETE CASCADE,
    target_node_id INTEGER NOT NULL REFERENCES testcase_chain_nodes(id) ON DELETE CASCADE,
    edge_type edge_type_enum NOT NULL DEFAULT 'standard',
    condition TEXT,
    weight INTEGER NOT NULL DEFAULT 1 CHECK (weight > 0),
    label VARCHAR(100),
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 1,
    CHECK (source_node_id != target_node_id),
    UNIQUE(source_node_id, target_node_id)
);
```

#### `chain_executions`
Tracks execution of entire testcase chains.

```sql
CREATE TABLE chain_executions (
    id SERIAL PRIMARY KEY,
    chain_id INTEGER NOT NULL REFERENCES testcase_chains(id) ON DELETE CASCADE,
    started_by INTEGER NOT NULL REFERENCES users(id),
    start_time TIMESTAMP NOT NULL DEFAULT NOW(),
    end_time TIMESTAMP,
    status execution_status_enum NOT NULL DEFAULT 'pending',
    execution_context JSON,
    result_summary JSON,
    error_message TEXT,
    total_nodes INTEGER NOT NULL DEFAULT 0 CHECK (total_nodes >= 0),
    completed_nodes INTEGER NOT NULL DEFAULT 0 CHECK (completed_nodes >= 0),
    failed_nodes INTEGER NOT NULL DEFAULT 0 CHECK (failed_nodes >= 0),
    skipped_nodes INTEGER NOT NULL DEFAULT 0 CHECK (skipped_nodes >= 0),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 1
);
```

#### `node_executions`
Tracks execution of individual nodes within chains.

```sql
CREATE TABLE node_executions (
    id SERIAL PRIMARY KEY,
    chain_execution_id INTEGER NOT NULL REFERENCES chain_executions(id) ON DELETE CASCADE,
    node_id INTEGER NOT NULL REFERENCES testcase_chain_nodes(id) ON DELETE CASCADE,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    status node_execution_status_enum NOT NULL DEFAULT 'pending',
    result_data JSON,
    error_message TEXT,
    output_logs TEXT,
    attempt_number INTEGER NOT NULL DEFAULT 1 CHECK (attempt_number > 0),
    max_attempts INTEGER NOT NULL DEFAULT 1 CHECK (max_attempts > 0),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 1,
    CHECK (attempt_number <= max_attempts)
);
```

#### `testcase_conditions`
Stores preconditions, postconditions, and cleanup conditions for testcases.

```sql
CREATE TABLE testcase_conditions (
    id SERIAL PRIMARY KEY,
    testcase_id INTEGER NOT NULL REFERENCES test_cases(id) ON DELETE CASCADE,
    condition_type condition_type_enum NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    validation_script TEXT,
    validation_type VARCHAR(50) NOT NULL DEFAULT 'script',
    required BOOLEAN NOT NULL DEFAULT TRUE,
    execution_order INTEGER NOT NULL DEFAULT 0,
    timeout_seconds INTEGER NOT NULL DEFAULT 30 CHECK (timeout_seconds > 0),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP,
    version INTEGER NOT NULL DEFAULT 1
);
```

## API Endpoints

### Chain Management

#### `GET /api/v1/testcase-chains/chains`
List testcase chains with optional filtering.

**Query Parameters:**
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum records to return (default: 100)
- `status`: Filter by chain status
- `chain_type`: Filter by chain type
- `created_by`: Filter by creator

#### `POST /api/v1/testcase-chains/chains`
Create a new testcase chain.

**Request Body:**
```json
{
    "name": "Advanced Persistence Chain",
    "description": "Multi-stage persistence attack scenario",
    "chain_type": "sequential",
    "max_execution_time_minutes": 120,
    "retry_on_failure": true,
    "auto_cleanup": true
}
```

#### `GET /api/v1/testcase-chains/chains/{chain_id}`
Get a specific chain with nodes and edges.

#### `PUT /api/v1/testcase-chains/chains/{chain_id}`
Update an existing chain.

#### `DELETE /api/v1/testcase-chains/chains/{chain_id}`
Soft delete a chain and all associated components.

### Chain Validation

#### `GET /api/v1/testcase-chains/chains/{chain_id}/validate`
Validate chain structural integrity.

**Response:**
```json
{
    "is_valid": true,
    "errors": [],
    "warnings": ["Chain has no end node"],
    "cycle_detected": false,
    "unreachable_nodes": []
}
```

### Node Management

#### `POST /api/v1/testcase-chains/chains/{chain_id}/nodes`
Add a node to a chain.

**Request Body:**
```json
{
    "testcase_id": 101,
    "node_type": "conditional",
    "execution_order": 2,
    "position_x": 200.0,
    "position_y": 150.0,
    "condition_expression": "previous_result.status == 'success'",
    "timeout_minutes": 45,
    "retry_count": 2,
    "continue_on_failure": false,
    "required_for_completion": true
}
```

#### `PUT /api/v1/testcase-chains/nodes/{node_id}`
Update a chain node.

#### `DELETE /api/v1/testcase-chains/nodes/{node_id}`
Remove a node from a chain.

### Edge Management

#### `POST /api/v1/testcase-chains/edges`
Create an edge between two nodes.

**Request Body:**
```json
{
    "source_node_id": 1,
    "target_node_id": 2,
    "edge_type": "success_path",
    "condition": "result.exit_code == 0",
    "weight": 1,
    "label": "Success Path",
    "description": "Path taken when testcase succeeds"
}
```

#### `PUT /api/v1/testcase-chains/edges/{edge_id}`
Update an edge.

#### `DELETE /api/v1/testcase-chains/edges/{edge_id}`
Remove an edge.

### Execution Management

#### `POST /api/v1/testcase-chains/chains/{chain_id}/execute`
Start chain execution.

**Request Body:**
```json
{
    "execution_context": {
        "environment": "staging",
        "target_host": "*************",
        "parameters": {
            "timeout": 300,
            "verbose": true
        }
    }
}
```

#### `GET /api/v1/testcase-chains/executions`
List chain executions with filtering.

#### `GET /api/v1/testcase-chains/executions/{execution_id}`
Get execution details with node execution status.

#### `GET /api/v1/testcase-chains/executions/{execution_id}/next-nodes`
Get next executable nodes in a chain.

## Usage Examples

### Creating a Chain

```python
from api.services.testcase_chain_service import TestcaseChainService

service = TestcaseChainService(db)
chain = service.create_chain(
    name="Advanced Persistence Attack",
    description="Multi-stage persistence scenario",
    chain_type="sequential",
    max_execution_time_minutes=120,
    retry_on_failure=True,
    auto_cleanup=True,
    created_by=user_id
)
```

### Adding Nodes to Chain

```python
# Add start node
start_node = service.add_node_to_chain(
    chain_id=chain.id,
    testcase_id=101,  # Initial access testcase
    node_type="start",
    execution_order=1,
    position_x=100.0,
    position_y=100.0
)

# Add conditional node
conditional_node = service.add_node_to_chain(
    chain_id=chain.id,
    testcase_id=102,  # Privilege escalation testcase
    node_type="conditional",
    execution_order=2,
    position_x=200.0,
    position_y=100.0,
    condition_expression="previous_result.privileges == 'admin'",
    continue_on_failure=False
)

# Add end node
end_node = service.add_node_to_chain(
    chain_id=chain.id,
    testcase_id=103,  # Persistence testcase
    node_type="end",
    execution_order=3,
    position_x=300.0,
    position_y=100.0
)
```

### Creating Edges

```python
# Success path from start to conditional
success_edge = service.add_edge_to_chain(
    source_node_id=start_node.id,
    target_node_id=conditional_node.id,
    edge_type="success_path",
    condition="result.status == 'success'",
    label="Initial Access Successful"
)

# Conditional path to end
conditional_edge = service.add_edge_to_chain(
    source_node_id=conditional_node.id,
    target_node_id=end_node.id,
    edge_type="conditional",
    condition="result.privileges == 'admin'",
    label="Admin Privileges Obtained"
)
```

### Validating Chain

```python
validation_result = service.validate_chain(chain.id)
if not validation_result.is_valid:
    print(f"Validation errors: {validation_result.errors}")
    print(f"Warnings: {validation_result.warnings}")
```

### Executing Chain

```python
execution = service.start_chain_execution(
    chain_id=chain.id,
    started_by=user_id,
    execution_context={
        "environment": "test",
        "target": "*************",
        "timeout": 300
    }
)

# Get next executable nodes
next_nodes = service.get_next_executable_nodes(execution.id)
for node in next_nodes:
    print(f"Ready to execute: {node.testcase_id}")
```

## Chain Types

### Sequential Chains
- Nodes execute in strict order
- Each node waits for previous to complete
- Failure stops execution unless `continue_on_failure` is set

### Parallel Chains
- Multiple nodes can execute simultaneously
- Synchronization points control flow
- Resource contention management

### Conditional Chains
- Execution path determined by conditions
- Dynamic flow based on results
- Complex branching scenarios

## Condition System

### Preconditions
```python
# Example: Check if target is reachable
precondition = TestcaseCondition(
    testcase_id=testcase.id,
    condition_type="precondition",
    name="Target Reachability",
    description="Verify target host is reachable",
    validation_script="ping -c 1 $TARGET_HOST",
    validation_type="script",
    required=True,
    timeout_seconds=30
)
```

### Postconditions
```python
# Example: Verify persistence mechanism
postcondition = TestcaseCondition(
    testcase_id=testcase.id,
    condition_type="postcondition",
    name="Persistence Verification",
    description="Verify persistence mechanism is active",
    validation_script="check_persistence.sh",
    validation_type="script",
    required=True,
    timeout_seconds=60
)
```

### Cleanup Conditions
```python
# Example: Remove artifacts
cleanup = TestcaseCondition(
    testcase_id=testcase.id,
    condition_type="cleanup",
    name="Artifact Cleanup",
    description="Remove test artifacts from target",
    validation_script="cleanup_artifacts.sh",
    validation_type="script",
    required=False,
    timeout_seconds=120
)
```

## Best Practices

### Chain Design
1. **Start with Simple Chains**: Begin with sequential chains before adding complexity
2. **Clear Node Naming**: Use descriptive names for nodes and edges
3. **Proper Error Handling**: Configure appropriate failure handling strategies
4. **Timeout Management**: Set realistic timeouts for each node
5. **Validation First**: Always validate chains before execution

### Performance Optimization
1. **Parallel Execution**: Use parallel nodes where possible
2. **Resource Management**: Consider resource constraints in parallel chains
3. **Cleanup Automation**: Enable auto-cleanup for resource management
4. **Monitoring**: Implement comprehensive execution monitoring

### Security Considerations
1. **Access Control**: Restrict chain creation and execution permissions
2. **Audit Logging**: Track all chain operations for compliance
3. **Environment Isolation**: Use isolated environments for chain execution
4. **Credential Management**: Secure handling of execution credentials

## Troubleshooting

### Common Issues

1. **Cycle Detection**: Use validation to identify and resolve cycles
2. **Unreachable Nodes**: Check edge connectivity and start nodes
3. **Execution Timeouts**: Adjust node timeouts based on testcase complexity
4. **Dependency Failures**: Review edge conditions and node requirements

### Debug Commands

```python
# Validate chain structure
validation = service.validate_chain(chain_id)

# Check execution status
execution = db.query(ChainExecution).filter(ChainExecution.id == execution_id).first()
print(f"Status: {execution.status}")
print(f"Progress: {execution.completed_nodes}/{execution.total_nodes}")

# Get next executable nodes
next_nodes = service.get_next_executable_nodes(execution_id)
print(f"Next nodes: {[n.id for n in next_nodes]}")
```

## Integration Points

- **Testcase Management**: Seamless integration with existing testcase system
- **MITRE ATT&CK**: Support for MITRE technique-based chains
- **Execution Engine**: Integration with testcase execution infrastructure
- **Reporting**: Chain execution results in assessment reports
- **Notifications**: Integration with notification system for execution updates

This Enhanced Testcase Chaining & Sequencing system provides RegressionRigor with enterprise-grade workflow management capabilities, enabling complex attack scenario modeling and execution that rivals industry-leading platforms.
