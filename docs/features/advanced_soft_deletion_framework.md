# Advanced Soft Deletion Framework

## Overview

The Advanced Soft Deletion Framework provides comprehensive soft deletion capabilities for RegressionRigor, including configurable retention policies, audit trails, scheduled purging, and cascade control. This framework ensures data integrity while providing flexible data lifecycle management.

## Features

### 1. Configurable Retention Policies

- **Entity-specific policies**: Different retention periods for different entity types
- **Automatic purging**: Scheduled permanent deletion of expired soft-deleted records
- **Cascade control**: Configure whether deletions cascade to related entities
- **Notification system**: Warn users before permanent deletion

### 2. Comprehensive Audit Trail

- **Operation tracking**: Log all soft deletion, restoration, and purge operations
- **User attribution**: Track which user performed each operation
- **Reason logging**: Store reasons for deletions and restorations
- **Metadata storage**: Additional context for operations

### 3. Scheduled Operations

- **Automatic purging**: Background jobs to permanently delete expired records
- **Notification delivery**: Scheduled warnings before permanent deletion
- **Policy enforcement**: Regular checks to ensure compliance with policies
- **Retry mechanism**: Automatic retry of failed operations with exponential backoff

### 4. Enhanced Soft Delete Capabilities

- **User tracking**: Track who deleted each record
- **Deletion reasons**: Store reasons for deletions
- **Cascade strategies**: Control how deletions affect related entities
- **Recovery workflows**: Structured process for restoring deleted data

## Database Schema

### Core Tables

#### `soft_deletion_policies`
Stores retention policies for different entity types.

```sql
CREATE TABLE soft_deletion_policies (
    id SERIAL PRIMARY KEY,
    entity_type VARCHAR(100) NOT NULL UNIQUE,
    retention_period_days INTEGER NOT NULL CHECK (retention_period_days > 0),
    auto_purge_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    cascade_deletion BOOLEAN NOT NULL DEFAULT TRUE,
    notification_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    notification_days_before INTEGER NOT NULL DEFAULT 7 CHECK (notification_days_before >= 0),
    description TEXT,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

#### `soft_deletion_audits`
Tracks all soft deletion operations for compliance and debugging.

```sql
CREATE TABLE soft_deletion_audits (
    id SERIAL PRIMARY KEY,
    entity_type VARCHAR(100) NOT NULL,
    entity_id INTEGER NOT NULL,
    operation_type operation_type_enum NOT NULL,
    performed_by INTEGER REFERENCES users(id),
    reason TEXT,
    cascade_triggered BOOLEAN NOT NULL DEFAULT FALSE,
    affected_entities_count INTEGER NOT NULL DEFAULT 1 CHECK (affected_entities_count > 0),
    policy_id INTEGER REFERENCES soft_deletion_policies(id),
    retention_period_used INTEGER,
    metadata JSON,
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    operation_time TIMESTAMP NOT NULL DEFAULT NOW()
);
```

#### `soft_deletion_schedules`
Manages scheduled operations like purging and notifications.

```sql
CREATE TABLE soft_deletion_schedules (
    id SERIAL PRIMARY KEY,
    entity_type VARCHAR(100) NOT NULL,
    entity_id INTEGER NOT NULL,
    operation_type schedule_operation_type_enum NOT NULL,
    scheduled_for TIMESTAMP NOT NULL,
    policy_id INTEGER NOT NULL REFERENCES soft_deletion_policies(id),
    status schedule_status_enum NOT NULL DEFAULT 'pending',
    executed_at TIMESTAMP,
    execution_result TEXT,
    retry_count INTEGER NOT NULL DEFAULT 0 CHECK (retry_count >= 0),
    max_retries INTEGER NOT NULL DEFAULT 3 CHECK (max_retries >= 0),
    created_by INTEGER REFERENCES users(id),
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

#### `soft_deletion_notifications`
Tracks notifications sent to users about upcoming purges.

```sql
CREATE TABLE soft_deletion_notifications (
    id SERIAL PRIMARY KEY,
    entity_type VARCHAR(100) NOT NULL,
    entity_id INTEGER NOT NULL,
    recipient_id INTEGER NOT NULL REFERENCES users(id),
    notification_type notification_type_enum NOT NULL,
    scheduled_purge_date TIMESTAMP NOT NULL,
    days_until_purge INTEGER NOT NULL CHECK (days_until_purge >= 0),
    sent_at TIMESTAMP,
    delivery_status delivery_status_enum NOT NULL DEFAULT 'pending',
    delivery_method VARCHAR(50),
    subject VARCHAR(255),
    message TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

## API Endpoints

### Policy Management

#### `GET /api/v1/soft-deletion/policies`
List all soft deletion policies with optional filtering.

**Query Parameters:**
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum records to return (default: 100)
- `entity_type`: Filter by entity type

#### `POST /api/v1/soft-deletion/policies`
Create a new soft deletion policy.

**Request Body:**
```json
{
    "entity_type": "testcase",
    "retention_period_days": 30,
    "auto_purge_enabled": true,
    "cascade_deletion": true,
    "notification_enabled": true,
    "notification_days_before": 7,
    "description": "Policy for test cases"
}
```

#### `GET /api/v1/soft-deletion/policies/{policy_id}`
Get a specific policy by ID.

#### `PUT /api/v1/soft-deletion/policies/{policy_id}`
Update an existing policy.

#### `DELETE /api/v1/soft-deletion/policies/{policy_id}`
Delete a policy and cancel associated scheduled operations.

### Audit Logs

#### `GET /api/v1/soft-deletion/audit`
List audit logs with optional filtering.

**Query Parameters:**
- `entity_type`: Filter by entity type
- `entity_id`: Filter by entity ID
- `operation_type`: Filter by operation type
- `performed_by`: Filter by user ID

#### `GET /api/v1/soft-deletion/audit/{audit_id}`
Get a specific audit log by ID.

### Scheduled Operations

#### `GET /api/v1/soft-deletion/schedules`
List scheduled operations with optional filtering.

**Query Parameters:**
- `entity_type`: Filter by entity type
- `operation_type`: Filter by operation type
- `status`: Filter by status

### Notifications

#### `GET /api/v1/soft-deletion/notifications`
List notifications. Regular users see only their own notifications.

**Query Parameters:**
- `recipient_id`: Filter by recipient (admin only)
- `notification_type`: Filter by notification type
- `delivery_status`: Filter by delivery status

## Usage Examples

### Creating a Policy

```python
from api.services.soft_deletion_service import SoftDeletionService

service = SoftDeletionService(db)
policy = service.create_policy(
    entity_type="campaign",
    retention_period_days=90,
    auto_purge_enabled=True,
    cascade_deletion=True,
    notification_enabled=True,
    notification_days_before=14,
    description="Campaign retention policy",
    created_by=user_id
)
```

### Soft Deleting an Entity

```python
# Using the enhanced mixin
entity.advanced_soft_delete(
    user_id=current_user.id,
    reason="Campaign completed",
    cascade=True
)

# Using the service
audit = service.soft_delete_entity(
    entity=entity,
    entity_type="campaign",
    user_id=current_user.id,
    reason="Campaign completed",
    cascade=True,
    ip_address=request.client.host,
    user_agent=request.headers.get("user-agent")
)
```

### Restoring an Entity

```python
audit = service.restore_entity(
    entity=entity,
    entity_type="campaign",
    user_id=current_user.id,
    reason="Restoration requested by user",
    ip_address=request.client.host,
    user_agent=request.headers.get("user-agent")
)
```

## Background Processing

### Scheduled Operations

The framework includes a scheduler service that processes scheduled operations:

```python
from api.services.soft_deletion_scheduler import run_scheduled_operations

# Run this via cron job or task scheduler
stats = run_scheduled_operations()
print(f"Processed {stats['processed']} operations")
```

### Cron Job Setup

Add to your crontab to run every hour:

```bash
0 * * * * /path/to/python -c "from api.services.soft_deletion_scheduler import run_scheduled_operations; run_scheduled_operations()"
```

## Configuration

### Default Policies

The migration creates default policies for common entity types:

- **Campaigns**: 90 days retention, auto-purge enabled
- **Test Cases**: 60 days retention, no cascade
- **Assessments**: 180 days retention, cascade enabled
- **Environments**: 30 days retention, no cascade
- **User Sessions**: 7 days retention, no notifications
- **Audit Logs**: 365 days retention, no auto-purge
- **Error Logs**: 90 days retention, no notifications

### Environment Variables

```bash
# Soft deletion settings
SOFT_DELETE_DEFAULT_RETENTION_DAYS=30
SOFT_DELETE_NOTIFICATION_DAYS=7
SOFT_DELETE_MAX_RETRIES=3
SOFT_DELETE_BATCH_SIZE=100
```

## Security Considerations

1. **Access Control**: Only authorized users can create/modify policies
2. **Audit Trail**: All operations are logged with user attribution
3. **Data Protection**: Soft deletion provides recovery window for accidental deletions
4. **Compliance**: Configurable retention periods support regulatory requirements

## Monitoring and Alerting

### Key Metrics

- Number of pending scheduled operations
- Failed operation count and retry rates
- Policy compliance across entity types
- Notification delivery success rates

### Health Checks

```python
from api.services.soft_deletion_scheduler import SoftDeletionScheduler

scheduler = SoftDeletionScheduler(db)
stats = scheduler.get_operation_statistics()

# Alert if too many operations are overdue
if stats["overdue_count"] > threshold:
    send_alert("Soft deletion operations are falling behind")
```

## Migration Guide

### Existing Models

To add advanced soft deletion to existing models:

1. Add the required fields to your model
2. Inherit from `AdvancedSoftDeleteMixin`
3. Create a migration to add the fields
4. Create a policy for the entity type

```python
# Add to existing model
class ExistingModel(Base, AdvancedSoftDeleteMixin):
    # ... existing fields ...
    
    # Enhanced soft deletion fields
    deleted_at = Column(DateTime, nullable=True)
    deleted_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    deletion_reason = Column(Text, nullable=True)
    cascade_strategy = Column(String(20), nullable=True)
```

## Best Practices

1. **Policy Design**: Set appropriate retention periods based on business needs
2. **Cascade Control**: Carefully consider cascade relationships to avoid unintended deletions
3. **Monitoring**: Regularly monitor scheduled operations for failures
4. **Testing**: Test restoration workflows to ensure data can be recovered
5. **Documentation**: Document entity relationships and cascade behavior
6. **Performance**: Index soft deletion fields for query performance

## Troubleshooting

### Common Issues

1. **Operations Not Processing**: Check scheduler service is running
2. **High Retry Rates**: Investigate underlying database or network issues
3. **Missing Notifications**: Verify notification service configuration
4. **Policy Conflicts**: Ensure entity types have unique policies

### Debug Commands

```python
# Check operation status
scheduler = SoftDeletionScheduler(db)
stats = scheduler.get_operation_statistics()

# Process operations manually
stats = scheduler.process_scheduled_operations(batch_size=10)

# Cancel operations for entity
count = scheduler.cancel_operations_for_entity("testcase", 123)
```
