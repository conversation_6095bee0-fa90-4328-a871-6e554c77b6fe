# Campaign Management API

This document provides detailed information about the Campaign Management API endpoints in the RegressionRigor platform.

## Overview

The Campaign Management API allows users to create, manage, and track security testing campaigns. Campaigns serve as containers for organizing related security tests and assessments, providing a structured approach to security testing activities.

## API Endpoints

All Campaign Management API endpoints are available under the base path `/api/v1/campaigns`.

### List Campaigns

```
GET /api/v1/campaigns/
```

Retrieves a list of campaigns with optional filtering.

**Query Parameters:**
- `skip` (integer, optional): Number of records to skip for pagination. Default: 0
- `limit` (integer, optional): Maximum number of records to return. Default: 100
- `status` (string, optional): Filter by campaign status (draft, active, completed, archived)
- `search` (string, optional): Search term to filter campaigns by name or description
- `include_deleted` (boolean, optional): Whether to include soft-deleted campaigns. Default: false

**Response:**
```json
[
  {
    "id": 1,
    "name": "Q2 Security Assessment",
    "description": "Comprehensive security assessment for Q2",
    "status": "active",
    "start_date": "2024-04-01T00:00:00",
    "end_date": "2024-06-30T23:59:59",
    "created_by": 1,
    "created_at": "2024-03-15T10:30:00",
    "updated_at": "2024-03-15T10:30:00",
    "deleted_at": null
  },
  // Additional campaigns...
]
```

**Permissions:**
- Regular users can only see campaigns they created
- Admin users can see all campaigns

### Get Campaign

```
GET /api/v1/campaigns/{campaign_id}
```

Retrieves a specific campaign by ID.

**Path Parameters:**
- `campaign_id` (integer, required): The ID of the campaign to retrieve

**Response:**
```json
{
  "id": 1,
  "name": "Q2 Security Assessment",
  "description": "Comprehensive security assessment for Q2",
  "status": "active",
  "start_date": "2024-04-01T00:00:00",
  "end_date": "2024-06-30T23:59:59",
  "created_by": 1,
  "created_at": "2024-03-15T10:30:00",
  "updated_at": "2024-03-15T10:30:00",
  "deleted_at": null
}
```

**Permissions:**
- Users can only access campaigns they created
- Admin users can access any campaign

### Create Campaign

```
POST /api/v1/campaigns/
```

Creates a new campaign.

**Request Body:**
```json
{
  "name": "Q3 Security Assessment",
  "description": "Comprehensive security assessment for Q3",
  "status": "draft",
  "start_date": "2024-07-01T00:00:00",
  "end_date": "2024-09-30T23:59:59"
}
```

**Response:**
```json
{
  "id": 2,
  "name": "Q3 Security Assessment",
  "description": "Comprehensive security assessment for Q3",
  "status": "draft",
  "start_date": "2024-07-01T00:00:00",
  "end_date": "2024-09-30T23:59:59",
  "created_by": 1,
  "created_at": "2024-03-15T11:45:00",
  "updated_at": "2024-03-15T11:45:00",
  "deleted_at": null
}
```

**Permissions:**
- Users must have the "create_campaign" permission

### Update Campaign

```
PUT /api/v1/campaigns/{campaign_id}
```

Updates an existing campaign.

**Path Parameters:**
- `campaign_id` (integer, required): The ID of the campaign to update

**Request Body:**
```json
{
  "name": "Updated Q3 Security Assessment",
  "status": "active"
}
```

**Response:**
```json
{
  "id": 2,
  "name": "Updated Q3 Security Assessment",
  "description": "Comprehensive security assessment for Q3",
  "status": "active",
  "start_date": "2024-07-01T00:00:00",
  "end_date": "2024-09-30T23:59:59",
  "created_by": 1,
  "created_at": "2024-03-15T11:45:00",
  "updated_at": "2024-03-15T12:30:00",
  "deleted_at": null
}
```

**Permissions:**
- Users can only update campaigns they created
- Admin users can update any campaign

### Delete Campaign

```
DELETE /api/v1/campaigns/{campaign_id}
```

Soft-deletes a campaign.

**Path Parameters:**
- `campaign_id` (integer, required): The ID of the campaign to delete

**Response:**
- Status code 204 (No Content) on success

**Permissions:**
- Users can only delete campaigns they created
- Admin users can delete any campaign

### Restore Campaign

```
POST /api/v1/campaigns/{campaign_id}/restore
```

Restores a soft-deleted campaign.

**Path Parameters:**
- `campaign_id` (integer, required): The ID of the campaign to restore

**Response:**
```json
{
  "id": 2,
  "name": "Updated Q3 Security Assessment",
  "description": "Comprehensive security assessment for Q3",
  "status": "active",
  "start_date": "2024-07-01T00:00:00",
  "end_date": "2024-09-30T23:59:59",
  "created_by": 1,
  "created_at": "2024-03-15T11:45:00",
  "updated_at": "2024-03-15T12:30:00",
  "deleted_at": null
}
```

**Permissions:**
- Only admin users can restore campaigns

### Get Campaign Test Cases

```
GET /api/v1/campaigns/{campaign_id}/test-cases
```

Retrieves test cases associated with a campaign.

**Path Parameters:**
- `campaign_id` (integer, required): The ID of the campaign

**Response:**
```json
[
  {
    "id": 1,
    "name": "SQL Injection Test",
    "description": "Test for SQL injection vulnerabilities",
    "type": "security",
    "priority": "high",
    "status": "active",
    "created_by": 1,
    "created_at": "2024-03-10T09:15:00",
    "updated_at": "2024-03-10T09:15:00"
  },
  // Additional test cases...
]
```

**Permissions:**
- Users can only access test cases for campaigns they created
- Admin users can access test cases for any campaign

### Assign Test Cases

```
POST /api/v1/campaigns/{campaign_id}/test-cases
```

Assigns test cases to a campaign.

**Path Parameters:**
- `campaign_id` (integer, required): The ID of the campaign

**Request Body:**
```json
{
  "test_case_ids": [1, 2, 3]
}
```

**Response:**
- Status code 204 (No Content) on success

**Permissions:**
- Users can only assign test cases to campaigns they created
- Admin users can assign test cases to any campaign

### Remove Test Case

```
DELETE /api/v1/campaigns/{campaign_id}/test-cases/{test_case_id}
```

Removes a test case from a campaign.

**Path Parameters:**
- `campaign_id` (integer, required): The ID of the campaign
- `test_case_id` (integer, required): The ID of the test case to remove

**Response:**
- Status code 204 (No Content) on success

**Permissions:**
- Users can only remove test cases from campaigns they created
- Admin users can remove test cases from any campaign

### Get Campaign Summary

```
GET /api/v1/campaigns/{campaign_id}/summary
```

Provides a summary of a campaign's test cases and assessments.

**Path Parameters:**
- `campaign_id` (integer, required): The ID of the campaign

**Response:**
```json
{
  "total_test_cases": 10,
  "total_assessments": 5,
  "test_case_status": {
    "not_started": 2,
    "in_progress": 5,
    "completed": 3
  },
  "assessment_status": {
    "draft": 1,
    "active": 3,
    "completed": 1
  },
  "completion_percentage": 30
}
```

**Permissions:**
- Users can only access summaries for campaigns they created
- Admin users can access summaries for any campaign

## Data Models

### Campaign

The Campaign model represents a security testing campaign.

**Fields:**
- `id` (integer): Unique identifier for the campaign
- `name` (string): Name of the campaign
- `description` (string): Detailed description of the campaign
- `status` (string): Status of the campaign (draft, active, completed, archived)
- `start_date` (datetime): Planned start date for the campaign
- `end_date` (datetime): Planned end date for the campaign
- `created_by` (integer): ID of the user who created the campaign
- `created_at` (datetime): Timestamp when the campaign was created
- `updated_at` (datetime): Timestamp when the campaign was last updated
- `deleted_at` (datetime, nullable): Timestamp when the campaign was soft-deleted, or null if not deleted

## Error Handling

The API returns appropriate HTTP status codes and error messages for different scenarios:

- `400 Bad Request`: Invalid input data
- `401 Unauthorized`: Missing or invalid authentication
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server-side error

Error responses include a JSON body with details about the error:

```json
{
  "detail": "Error message describing the issue"
}
```

## Authentication and Authorization

All Campaign Management API endpoints require authentication using JWT tokens. Include the token in the Authorization header:

```
Authorization: Bearer <token>
```

Access to endpoints is controlled by role-based permissions:
- Regular users can only access and modify their own campaigns
- Admin users have full access to all campaigns and operations 