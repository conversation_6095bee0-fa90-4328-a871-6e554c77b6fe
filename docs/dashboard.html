<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feature Development Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .dashboard {
            margin-top: 30px;
        }
        .feature-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            background-color: #fff;
        }
        .feature-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .feature-name {
            font-size: 1.4em;
            font-weight: bold;
            margin: 0;
        }
        .feature-date {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        .progress-container {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }
        .progress-item {
            flex: 1;
            text-align: center;
            padding: 10px;
            border-radius: 4px;
            margin: 0 5px;
        }
        .not-started {
            background-color: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }
        .in-progress {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }
        .complete {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .summary {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            text-align: center;
        }
        .summary-item {
            padding: 15px;
            border-radius: 8px;
            min-width: 150px;
        }
        .summary-number {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }
        .progress-bar-container {
            height: 20px;
            background-color: #f5f5f5;
            border-radius: 10px;
            margin: 10px 0;
            overflow: hidden;
        }
        .progress-bar {
            height: 100%;
            background-color: #4caf50;
            border-radius: 10px;
            transition: width 0.5s ease-in-out;
        }
        .filters {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }
        .filter-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            background-color: #f8f9fa;
            cursor: pointer;
        }
        .filter-btn.active {
            background-color: #007bff;
            color: white;
        }
        .search-container {
            margin-bottom: 20px;
        }
        #searchInput {
            padding: 8px;
            width: 100%;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        @media (max-width: 768px) {
            .progress-container {
                flex-direction: column;
            }
            .progress-item {
                margin: 5px 0;
            }
            .summary {
                flex-direction: column;
            }
            .summary-item {
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <h1>Feature Development Dashboard</h1>
    <p>Last updated: <span id="lastUpdated"></span></p>
    
    <div class="search-container">
        <input type="text" id="searchInput" placeholder="Search features...">
    </div>
    
    <div class="filters">
        <button class="filter-btn active" data-filter="all">All</button>
        <button class="filter-btn" data-filter="complete">Completed</button>
        <button class="filter-btn" data-filter="in-progress">In Progress</button>
        <button class="filter-btn" data-filter="not-started">Not Started</button>
    </div>
    
    <div class="summary">
        <div class="summary-item" style="background-color: #e8f4f8;">
            <h3>Total Features</h3>
            <div class="summary-number" id="totalFeatures">0</div>
        </div>
        <div class="summary-item" style="background-color: #d4edda;">
            <h3>Completed</h3>
            <div class="summary-number" id="completedFeatures">0</div>
        </div>
        <div class="summary-item" style="background-color: #fff3cd;">
            <h3>In Progress</h3>
            <div class="summary-number" id="inProgressFeatures">0</div>
        </div>
        <div class="summary-item" style="background-color: #f8f9fa;">
            <h3>Not Started</h3>
            <div class="summary-number" id="notStartedFeatures">0</div>
        </div>
    </div>
    
    <h2>Overall Progress</h2>
    <div class="progress-bar-container">
        <div class="progress-bar" id="overallProgress" style="width: 0%"></div>
    </div>
    
    <div class="dashboard" id="featureDashboard">
        <!-- Feature cards will be inserted here -->
    </div>
    
    <script>
        // Feature data
        const features = [
            {
                name: "---------",
                api: "-----",
                tests: "-------",
                ui: "----",
                completedDate: "----",
                overallStatus: "Not Started",
                progress: 0
            },
            {
                name: "Admin-Interface",
                api: "Complete",
                tests: "Complete",
                ui: "In Progress",
                completedDate: "-",
                overallStatus: "In Progress",
                progress: 83
            },
            {
                name: "Dashboard",
                api: "In Progress",
                tests: "Not Started",
                ui: "Not Started",
                completedDate: "-",
                overallStatus: "In Progress",
                progress: 16
            },
            {
                name: "Rate Limiting",
                api: "Complete",
                tests: "Not Started",
                ui: "Not Started",
                completedDate: "-",
                overallStatus: "Not Started",
                progress: 33
            },
            {
                name: "Input Validation",
                api: "Complete",
                tests: "Not Started",
                ui: "Not Started",
                completedDate: "-",
                overallStatus: "Not Started",
                progress: 33
            },
            {
                name: "API Documentation",
                api: "Complete",
                tests: "Not Started",
                ui: "Not Started",
                completedDate: "-",
                overallStatus: "Not Started",
                progress: 33
            },
            {
                name: "Error-Handling",
                api: "Complete",
                tests: "In Progress",
                ui: "In Progress",
                completedDate: "-",
                overallStatus: "In Progress",
                progress: 66
            },
            {
                name: "Two-Factor Authentication",
                api: "Complete",
                tests: "Not Started",
                ui: "Not Started",
                completedDate: "-",
                overallStatus: "Not Started",
                progress: 33
            },
        ];
        
        // Set last updated date
        document.getElementById('lastUpdated').textContent = new Date().toLocaleString();
        
        // Calculate summary statistics
        const totalFeatures = features.length;
        const completedFeatures = features.filter(f => f.overallStatus === 'Complete').length;
        const inProgressFeatures = features.filter(f => f.overallStatus === 'In Progress').length;
        const notStartedFeatures = features.filter(f => f.overallStatus === 'Not Started').length;
        
        // Update summary numbers
        document.getElementById('totalFeatures').textContent = totalFeatures;
        document.getElementById('completedFeatures').textContent = completedFeatures;
        document.getElementById('inProgressFeatures').textContent = inProgressFeatures;
        document.getElementById('notStartedFeatures').textContent = notStartedFeatures;
        
        // Calculate overall progress
        const overallProgress = Math.round((completedFeatures / totalFeatures) * 100);
        document.getElementById('overallProgress').style.width = `${overallProgress}%`;
        
        // Function to render feature cards
        function renderFeatures(featuresToRender) {
            const dashboard = document.getElementById('featureDashboard');
            dashboard.innerHTML = '';
            
            featuresToRender.forEach(feature => {
                const card = document.createElement('div');
                card.className = `feature-card ${feature.overallStatus.toLowerCase().replace(' ', '-')}`;
                
                const header = document.createElement('div');
                header.className = 'feature-header';
                
                const nameElement = document.createElement('h3');
                nameElement.className = 'feature-name';
                nameElement.textContent = feature.name;
                
                const dateElement = document.createElement('div');
                dateElement.className = 'feature-date';
                dateElement.textContent = feature.completedDate !== '-' ? `Completed: ${feature.completedDate}` : '';
                
                header.appendChild(nameElement);
                header.appendChild(dateElement);
                
                const progressBar = document.createElement('div');
                progressBar.className = 'progress-bar-container';
                const progressFill = document.createElement('div');
                progressFill.className = 'progress-bar';
                progressFill.style.width = `${feature.progress}%`;
                progressBar.appendChild(progressFill);
                
                const progressContainer = document.createElement('div');
                progressContainer.className = 'progress-container';
                
                const apiStatus = document.createElement('div');
                apiStatus.className = `progress-item ${feature.api.toLowerCase().replace(' ', '-')}`;
                apiStatus.textContent = `API: ${feature.api}`;
                
                const testsStatus = document.createElement('div');
                testsStatus.className = `progress-item ${feature.tests.toLowerCase().replace(' ', '-')}`;
                testsStatus.textContent = `Tests: ${feature.tests}`;
                
                const uiStatus = document.createElement('div');
                uiStatus.className = `progress-item ${feature.ui.toLowerCase().replace(' ', '-')}`;
                uiStatus.textContent = `UI: ${feature.ui}`;
                
                progressContainer.appendChild(apiStatus);
                progressContainer.appendChild(testsStatus);
                progressContainer.appendChild(uiStatus);
                
                card.appendChild(header);
                card.appendChild(progressBar);
                card.appendChild(progressContainer);
                
                dashboard.appendChild(card);
            });
        }
        
        // Initial render
        renderFeatures(features);
        
        // Filter functionality
        const filterButtons = document.querySelectorAll('.filter-btn');
        filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Update active button
                filterButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                
                const filter = button.getAttribute('data-filter');
                let filteredFeatures = features;
                
                if (filter !== 'all') {
                    filteredFeatures = features.filter(feature => 
                        feature.overallStatus.toLowerCase().replace(' ', '-') === filter
                    );
                }
                
                // Apply search filter as well
                const searchTerm = document.getElementById('searchInput').value.toLowerCase();
                if (searchTerm) {
                    filteredFeatures = filteredFeatures.filter(feature => 
                        feature.name.toLowerCase().includes(searchTerm)
                    );
                }
                
                renderFeatures(filteredFeatures);
            });
        });
        
        // Search functionality
        document.getElementById('searchInput').addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase();
            
            // Get current filter
            const activeFilter = document.querySelector('.filter-btn.active').getAttribute('data-filter');
            
            let filteredFeatures = features;
            if (activeFilter !== 'all') {
                filteredFeatures = features.filter(feature => 
                    feature.overallStatus.toLowerCase().replace(' ', '-') === activeFilter
                );
            }
            
            // Apply search filter
            if (searchTerm) {
                filteredFeatures = filteredFeatures.filter(feature => 
                    feature.name.toLowerCase().includes(searchTerm)
                );
            }
            
            renderFeatures(filteredFeatures);
        });
    </script>
</body>
</html>
