# Assessment Management API

The Assessment Management API provides endpoints for managing security assessments and test executions. This API allows users to create, read, update, and delete assessments, as well as track test executions and generate reports.

## Models

### Assessment

An assessment represents a security testing activity against a target system.

**Fields:**
- `id`: Unique identifier for the assessment
- `name`: Name of the assessment
- `description`: Detailed description of the assessment
- `target_system`: The system being assessed
- `assessment_type`: Type of assessment (e.g., vulnerability, penetration, compliance)
- `status`: Current status of the assessment (planned, in-progress, completed, cancelled)
- `start_date`: Planned start date for the assessment
- `end_date`: Planned end date for the assessment
- `campaign_id`: ID of the campaign this assessment belongs to
- `created_by`: ID of the user who created the assessment
- `created_on`: Timestamp when the assessment was created
- `updated_at`: Timestamp when the assessment was last updated
- `deleted_at`: Timestamp when the assessment was soft-deleted (if applicable)

### Test Execution

A test execution represents the result of executing a test case as part of an assessment.

**Fields:**
- `id`: Unique identifier for the test execution
- `test_case_id`: ID of the test case being executed
- `assessment_id`: ID of the assessment this execution belongs to
- `result`: Result of the test execution (pass, fail, partial, pending, blocked, not_applicable)
- `notes`: Additional notes about the test execution
- `evidence`: Evidence supporting the test result
- `executed_by`: ID of the user who executed the test
- `executed_at`: Timestamp when the test was executed

## Endpoints

### Assessments

#### GET /api/v1/assessments/

Get a list of assessments with optional filtering.

**Query Parameters:**
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum number of records to return (default: 100, max: 1000)
- `status`: Filter by assessment status
- `campaign_id`: Filter by campaign ID
- `search`: Search term for name or description
- `include_deleted`: Include soft-deleted assessments (admin only)

**Permissions:**
- Regular users can only see assessments they created
- Admins can see all assessments

#### GET /api/v1/assessments/{assessment_id}

Get an assessment by its ID.

**Path Parameters:**
- `assessment_id`: ID of the assessment to retrieve

**Query Parameters:**
- `include_deleted`: Include soft-deleted assessments (admin only)

**Permissions:**
- Regular users can only see assessments they created
- Admins can see all assessments

#### POST /api/v1/assessments/

Create a new assessment.

**Request Body:**
- `name`: Name of the assessment (required)
- `description`: Detailed description of the assessment
- `target_system`: The system being assessed (required)
- `assessment_type`: Type of assessment (required)
- `status`: Current status of the assessment (default: "planned")
- `start_date`: Planned start date for the assessment
- `end_date`: Planned end date for the assessment
- `campaign_id`: ID of the campaign this assessment belongs to (required)

**Permissions:**
- All authenticated users with "create_assessment" permission can create assessments

#### PUT /api/v1/assessments/{assessment_id}

Update an existing assessment.

**Path Parameters:**
- `assessment_id`: ID of the assessment to update

**Request Body:**
- `name`: Name of the assessment
- `description`: Detailed description of the assessment
- `target_system`: The system being assessed
- `assessment_type`: Type of assessment
- `status`: Current status of the assessment
- `start_date`: Planned start date for the assessment
- `end_date`: Planned end date for the assessment
- `campaign_id`: ID of the campaign this assessment belongs to

**Permissions:**
- Regular users can only update assessments they created
- Admins can update any assessment

#### DELETE /api/v1/assessments/{assessment_id}

Soft-delete an assessment.

**Path Parameters:**
- `assessment_id`: ID of the assessment to delete

**Permissions:**
- Regular users can only delete assessments they created
- Admins can delete any assessment

#### POST /api/v1/assessments/{assessment_id}/restore

Restore a soft-deleted assessment.

**Path Parameters:**
- `assessment_id`: ID of the assessment to restore

**Permissions:**
- Only admins can restore assessments

### Test Executions

#### GET /api/v1/assessments/{assessment_id}/executions

Get test executions for an assessment.

**Path Parameters:**
- `assessment_id`: ID of the assessment

**Query Parameters:**
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum number of records to return (default: 100, max: 1000)
- `result`: Filter by execution result

**Permissions:**
- Regular users can only see executions for assessments they created
- Admins can see all executions

#### GET /api/v1/assessments/executions/{execution_id}

Get a test execution by its ID.

**Path Parameters:**
- `execution_id`: ID of the test execution to retrieve

**Permissions:**
- Regular users can only see executions for assessments they created
- Admins can see all executions

#### POST /api/v1/assessments/executions

Create a new test execution.

**Request Body:**
- `test_case_id`: ID of the test case being executed (required)
- `assessment_id`: ID of the assessment this execution belongs to (required)
- `result`: Result of the test execution (required)
- `notes`: Additional notes about the test execution
- `evidence`: Evidence supporting the test result

**Permissions:**
- Regular users can only create executions for assessments they created
- Admins can create executions for any assessment

#### PUT /api/v1/assessments/executions/{execution_id}

Update an existing test execution.

**Path Parameters:**
- `execution_id`: ID of the test execution to update

**Request Body:**
- `result`: Result of the test execution
- `notes`: Additional notes about the test execution
- `evidence`: Evidence supporting the test result

**Permissions:**
- Regular users can only update executions for assessments they created
- Admins can update any execution

#### DELETE /api/v1/assessments/executions/{execution_id}

Delete a test execution.

**Path Parameters:**
- `execution_id`: ID of the test execution to delete

**Permissions:**
- Regular users can only delete executions for assessments they created
- Admins can delete any execution

### Reports and Summaries

#### GET /api/v1/assessments/{assessment_id}/summary

Get a summary of test execution results for an assessment.

**Path Parameters:**
- `assessment_id`: ID of the assessment

**Response:**
- `total_tests`: Total number of test executions
- `passed`: Number of passed tests
- `failed`: Number of failed tests
- `partial`: Number of partially passed tests
- `pending`: Number of pending tests
- `blocked`: Number of blocked tests
- `not_applicable`: Number of not applicable tests
- `completion_percentage`: Percentage of tests completed
- `pass_rate`: Percentage of tests passed (including partial passes)

**Permissions:**
- Regular users can only see summaries for assessments they created
- Admins can see all summaries

#### GET /api/v1/assessments/{assessment_id}/report

Generate a comprehensive report for an assessment.

**Path Parameters:**
- `assessment_id`: ID of the assessment

**Response:**
- `assessment`: Assessment details
- `summary`: Summary of test execution results
- `test_executions`: List of test executions
- `mitre_coverage`: MITRE ATT&CK coverage analysis
- `recommendations`: Recommendations based on test results

**Permissions:**
- Regular users can only generate reports for assessments they created
- Admins can generate reports for any assessment

## Error Handling

All endpoints return appropriate HTTP status codes:

- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `204 No Content`: Resource deleted successfully
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `422 Unprocessable Entity`: Validation error

Error responses include a detail message explaining the error.

## Authentication

All endpoints require authentication using JWT tokens. Include the token in the Authorization header:

```
Authorization: Bearer <token>
```

## Rate Limiting

All endpoints are subject to rate limiting. When a rate limit is exceeded, the API will return a 429 Too Many Requests response. 