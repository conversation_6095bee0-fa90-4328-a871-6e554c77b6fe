# RegressionRigor Documentation

This directory contains all documentation for the RegressionRigor project.

## Project Overview

RegressionRigor is a robust FastAPI-based API application designed for comprehensive API management with advanced database integration and scalable architecture. The platform focuses on cybersecurity testing and assessment, integrating with MITRE ATT&CK and MITRE ATLAS frameworks to provide a complete environment for security teams.

## Documentation Index

### Project Planning

- [Development Plan](development_plan.md) - Comprehensive development plan for the project
- [Roadmap](roadmap.md) - Future development roadmap
- [TODO List](TODO.md) - Current tasks and priorities
- [Red Team Regression Plan](red_team_regression_plan.md) - Plan for red team regression testing

### Architecture & Design

- [Architecture](architecture.md) - System architecture and design
- [Schema](schema.md) - Database schema documentation
- [Migrations](migrations.md) - Database migration guidelines

### API & Implementation

- [API Documentation](api_documentation.md) - API endpoints and usage
- [Rate Limiting](rate_limiting.md) - API rate limiting implementation
- [Input Validation](input_validation.md) - Input validation implementation
- [Implementation Summary](implementation_summary.md) - Summary of implementation details

### Integration

- [ATLAS Integration](atlas_integration.md) - MITRE ATLAS framework integration

### User Guides

- [User Management](user_management.md) - User management documentation
- [Docker Setup](docker_setup.md) - Docker environment setup instructions

### Reports

- [Project Report (2025-03-10)](project_report_2025-03-10.md) - Latest project status report
- [Dashboard](dashboard.html) - Project dashboard

## Directory Structure

- `3rdparty/` - Third-party documentation
- `milestones/` - Project milestone documentation 