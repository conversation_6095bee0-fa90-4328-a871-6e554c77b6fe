# Database Migrations Guide

## Overview
This project uses Alembic for database migrations to manage schema changes in a controlled, versioned manner.

## Migration Architecture
- Soft Delete Implementation: All models inherit from `SoftDeleteMixin` which provides:
  - `deleted_at`: Timestamp when record was soft-deleted 
  - `is_deprecated`: Boolean flag for deprecated records
  - `is_revoked`: Boolean flag for revoked records
  - `revoked_by_id`: References replacement record
  - `created_on`: Creation timestamp
  - `updated_at`: Last update timestamp

## Running Migrations
1. Create new migration:
   ```bash
   alembic revision --autogenerate -m "description"
   ```

2. Apply migrations:
   ```bash
   alembic upgrade head
   ```

3. Rollback migrations:
   ```bash
   alembic downgrade -1
   ```

## Best Practices
1. Always review autogenerated migrations before applying
2. Test migrations on development before production 
3. Never delete migration files - they're part of project history
4. Include both upgrade() and downgrade() implementations
5. Add descriptive comments in migration files

## MITRE Data Handling
- Versions are tracked in `mitre_versions` table
- Each MITRE object (technique, tactic) is version-specific
- Soft deletion preserves historical data while marking records as inactive
- Use `is_deprecated` for officially deprecated MITRE entries
- Use `is_revoked` when an entry is replaced by a newer version