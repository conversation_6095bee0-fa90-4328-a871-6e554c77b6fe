# Input Validation Implementation

This document describes the input validation implementation for the Regression Rigor API.

## Overview

Input validation is a critical security measure that helps prevent malicious or malformed data from entering the application. The Regression Rigor API implements comprehensive input validation at multiple levels:

1. **Schema-level validation** using Pydantic models
2. **Request-level validation** using middleware
3. **Custom validators** for specific data types

## Validation Utilities

The `api/utils/validators.py` module provides a set of validation functions for common data types:

- **Password Validation**: Ensures passwords meet security requirements
- **Username Validation**: Validates usernames against format requirements
- **Email Validation**: Validates email addresses
- **MITRE Technique ID Validation**: Validates MITRE ATT&CK technique IDs
- **Pagination Parameter Validation**: Validates skip and limit parameters
- **Date Range Validation**: Validates date ranges
- **JSON Data Validation**: Validates JSON data against required fields

Each validation function returns a tuple of `(is_valid, message)`, where `is_valid` is a boolean indicating whether the validation passed, and `message` is a descriptive error message if validation failed.

## Validation Middleware

The `api/middleware/validation.py` module provides a middleware that validates incoming requests against predefined validators. The middleware:

1. Checks if the request path matches any of the configured validation paths
2. Applies the appropriate validators to query parameters and request body
3. Returns a 422 Unprocessable Entity response if validation fails

The middleware is configured with default validators for common paths, such as authentication endpoints and user management endpoints. Custom validators can be added for specific paths.

## Schema Validation

In addition to the middleware, the API uses Pydantic models for schema validation. These models define the expected structure and types of request and response data, and automatically validate incoming data against these definitions.

The `CommonValidators` class in `api/utils/validators.py` provides reusable validators that can be included in Pydantic models.

## Error Handling

When validation fails, the API returns a 422 Unprocessable Entity response with a detailed error message. The error response includes:

- A general error message
- Specific validation errors for each field
- A timestamp

Example error response:

```json
{
  "detail": "Validation error",
  "errors": [
    "Field 'username': Username must be 3-32 characters and contain only letters, numbers, underscores, and hyphens",
    "Field 'password': Password must contain at least one uppercase letter"
  ],
  "timestamp": "2025-03-10T12:34:56.789012"
}
```

## Validation Rules

The API enforces the following validation rules:

### Usernames
- 3-32 characters
- Alphanumeric characters, underscores, and hyphens only

### Passwords
- At least 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one digit
- At least one special character (@$!%*?&)

### Email Addresses
- Standard email format validation

### MITRE Technique IDs
- Format: TXXXX or TXXXX.XXX (e.g., T1234 or T1234.001)

### Pagination Parameters
- Skip: Non-negative integer
- Limit: Positive integer, not exceeding the maximum limit (default: 100)

## Adding Custom Validators

To add custom validators for specific endpoints:

1. Create a validation function in `api/utils/validators.py`
2. Add the validator to the `path_validators` dictionary when configuring the validation middleware

Example:

```python
# Create a custom validator
def validate_custom_field(value: str) -> Tuple[bool, str]:
    if not re.match(r"^[a-z]+$", value):
        return False, "Field must contain only lowercase letters"
    return True, "Field is valid"

# Add the validator to the middleware configuration
validation_middleware = get_validation_middleware(
    path_validators={
        r"^/api/v1/custom/endpoint": {
            "custom_field": validate_custom_field
        }
    }
)
```

## Best Practices

When working with the API, follow these best practices:

1. Always validate user input before sending it to the API
2. Check the API documentation for specific validation requirements
3. Handle validation errors gracefully in your client application
4. Use the provided examples as templates for your requests 