# D3FEND Data Integration

## Overview
This document describes how the D3FEND (Digital Defense Framework) ontology is integrated into our system. The D3FEND framework, developed by MITRE, provides a knowledge graph of cybersecurity countermeasures.

## Database Schema

### D3FEND Versions (`d3fend_versions`)
Tracks different versions of imported D3FEND ontology data:
- `id`: Primary key
- `version`: Version identifier (e.g., "2025.02.25")
- `import_date`: When this version was imported
- `is_current`: Boolean flag for the active version

### D3FEND Concepts (`d3fend_concepts`)
Stores the core entities from the D3FEND ontology:
- `id`: Primary key
- `uri`: Full URI of the concept (unique identifier from D3FEND)
- `name`: Local name part of the URI
- `type`: Entity type (e.g., "owl:Class", "owl:ObjectProperty")
- `definition`: Full description of the concept
- `version_id`: Foreign key to d3fend_versions
- `created`: Creation timestamp
- `modified`: Last modification timestamp
- `is_deprecated`: Deprecation status
- `external_references`: JSON field for external references (e.g., DBpedia)
- `notes`: Additional documentation

### Concept Relationships (`d3fend_relationships`)
Represents relationships between D3FEND concepts:
- `source_id`: ID of the source concept
- `target_id`: ID of the target concept
- `relationship_type`: Type of relationship (e.g., "rdfs:subClassOf", "d3f:mitigates")

## API Endpoints

### Version Management
```http
GET /api/v1/d3fend/versions
```
Lists all imported D3FEND ontology versions.

Response:
```json
[
  {
    "id": 1,
    "version": "2025.02.25",
    "import_date": "2025-02-25T10:00:00Z",
    "is_current": true
  }
]
```

### Concept Access
```http
GET /api/v1/d3fend/concepts
```
Lists concepts with pagination and optional filtering.

Query Parameters:
- `version_id` (optional): Filter by specific version
- `type` (optional): Filter by concept type
- `page` (default: 1): Page number
- `size` (default: 10, max: 100): Items per page

Response:
```json
{
  "items": [
    {
      "uri": "http://d3fend.mitre.org/ontologies/d3fend.owl#HTTPSOnly",
      "name": "HTTPSOnly",
      "type": "owl:Class",
      "definition": "..."
    }
  ],
  "total": 150,
  "page": 1,
  "size": 10
}
```

```http
GET /api/v1/d3fend/concepts/{uri}
```
Get a specific concept by URI (URL-encoded).

Response:
```json
{
  "uri": "http://d3fend.mitre.org/ontologies/d3fend.owl#HTTPSOnly",
  "name": "HTTPSOnly",
  "type": "owl:Class",
  "definition": "...",
  "external_references": {"@id": "http://example.com/ref"}
}
```

```http
GET /api/v1/d3fend/relationships/{uri}
```
Get relationships for a specific concept.

Query Parameters:
- `relationship_type` (optional): Filter by relationship type

Response:
```json
[
  {
    "source_uri": "http://d3fend.mitre.org/ontologies/d3fend.owl#HTTPSOnly",
    "target_uri": "http://d3fend.mitre.org/ontologies/d3fend.owl#NetworkSecurity",
    "target_name": "NetworkSecurity",
    "relationship_type": "rdfs:subClassOf"
  }
]
```

## Error Handling

The API uses standard HTTP status codes:
- 200: Success
- 400: Bad Request (invalid parameters)
- 404: Not Found (concept/version not found)
- 500: Internal Server Error

Error responses include a detail message:
```json
{
  "detail": "Concept not found"
}
```

## Logging

The system uses Python's logging framework with the following levels:
- DEBUG: Detailed information for debugging
- INFO: General operational events
- WARNING: Unexpected but handled situations
- ERROR: Serious issues that need attention

Log files are stored in the `logs` directory:
- `d3fend_import.log`: Import process logs
- Application logs contain request details and relationship processing

## Test Coverage

The test suite (`tests/test_api/test_d3fend.py`) covers:
- Version management
- Concept retrieval and filtering
- Relationship querying
- Error handling
- Import functionality
- Edge cases (URI encoding, relationship types)

## Import Process
The import utility (`api/utils/d3fend_import.py`) handles:
1. Reading the D3FEND JSON-LD file
2. Creating a new version record
3. Importing concepts and their metadata
4. Building relationship mappings
5. Transaction management and error handling

## Usage Examples

### List All Defensive Techniques
```http
GET /api/v1/d3fend/concepts?type=d3f:DefensiveTechnique
```

### Find Mitigations for a Technique
```http
GET /api/v1/d3fend/relationships/HTTPSOnly?relationship_type=d3f:mitigates
```

### Get Current Version's Concepts
```http
GET /api/v1/d3fend/concepts?page=1&size=50
```

## Troubleshooting

### Common Issues
1. Invalid URI encoding
   - Ensure URIs are properly URL-encoded when making requests
   - Use urllib.parse.quote() in Python clients

2. Relationship queries returning 404
   - Verify the source concept exists
   - Check that the URI is correctly encoded
   - Ensure relationship type matches exactly

3. Import process failures
   - Check JSON-LD file format
   - Verify database connection
   - Review import logs in logs/d3fend_import.log

### Best Practices
1. Always use the latest version unless specific version needed
2. Use pagination for large result sets
3. Include error handling for failed requests
4. Monitor import logs when updating ontology data