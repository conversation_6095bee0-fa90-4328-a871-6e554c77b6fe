# MITRE ATLAS Integration

## Overview
The project integrates the MITRE ATLAS (Adversarial Threat Landscape for Artificial-Intelligence Systems) framework to provide AI-specific threat modeling capabilities. This document covers the implementation details and usage guidelines.

## Implementation

### Database Schema
The ATLAS data is stored in the following tables:
- `atlas_versions`: Tracks ATLAS framework versions
- `atlas_tactics`: Stores ATLAS tactics 
- `atlas_techniques`: Stores ATLAS techniques and sub-techniques
- `atlas_matrices`: Stores ATLAS matrices
- `atlas_matrix_items`: Maps techniques to tactics within matrices

### Key Components

1. Data Import Service (`api/services/atlas_importer.py`)
   - Imports ATLAS data from JSON format
   - Handles version management 
   - Maintains technique-tactic relationships
   - Supports incremental updates
   - Handles subtechniques
   - Tracks import timestamps

2. Models (`api/models/atlas.py`)
   - `AtlasVersion`: Version tracking
   - `AtlasTactic`: Tactics definition
   - `AtlasTechnique`: Techniques with sub-technique support
   - `AtlasMatrix`: Matrix definitions
   - `AtlasMatrixItem`: Matrix mappings

3. CLI Commands (`api/cli/atlas_commands.py`)
   - Import ATLAS data
   - Force update existing data
   - Manage versions

## Usage

### Importing ATLAS Data
```bash
# Import new ATLAS data
python -m api.cli.atlas_commands import-data --matrix-file path/to/atlas.json

# Force update existing data
python -m api.cli.atlas_commands import-data --matrix-file path/to/atlas.json --force-update
```

### Working with ATLAS Data
1. Access via SQLAlchemy models:
```python
from api.models.atlas import AtlasTechnique, AtlasTactic

# Get all techniques for a tactic
techniques = db.query(AtlasTechnique).join(AtlasTactic).filter(
    AtlasTactic.name == "reconnaissance"
).all()
```

2. Version Management:
```python
from api.models.atlas import AtlasVersion

# Get current version
current = db.query(AtlasVersion).filter(AtlasVersion.is_current == True).first()
```

## Data Structure

### Techniques 
- Unique external IDs (e.g., "AML.T0001")
- Support for sub-techniques (e.g., "AML.T0001.001")
- Link to parent techniques
- Associated tactics
- Metadata (name, description, etc.)
- Version tracking
- Creation and modification timestamps

### Tactics
- Unique external IDs
- Associated techniques
- Matrix mappings
- Version tracking

### Matrix Items
- Links techniques to tactics
- Supports visualization
- Maintains ordering

## Future Enhancements
1. ATLAS visualization using D3.js
2. Advanced search and filtering
3. AI threat intelligence dashboard
4. ATLAS-ATT&CK mapping
5. Automated threat detection

## References
- [MITRE ATLAS](https://atlas.mitre.org/)
- [ATLAS on GitHub](https://github.com/mitre-atlas)
- [ATLAS Navigator](https://mitre-atlas.github.io/atlas-navigator/)