# Architecture Documentation

# Dual-Service Architecture

## Service Configuration
The application runs as two separate services:

1. Flask UI (Port 5000)
   - <PERSON><PERSON> user interface and web pages
   - Integrates with FastAPI backend for data
   - Uses shared database configuration

2. FastAPI Backend (Port 5001)
   - Provides REST API endpoints
   - Handles MITRE ATT&CK data processing
   - Manages authentication and authorization
   - Implements threat defense mapping

## Database Configuration
- Shared PostgreSQL database between both services
- Connection pooling configured for both Flask and FastAPI
- Uses SQLAlchemy with version-specific configurations:
  - Flask: Flask-SQLAlchemy integration
  - FastAPI: SQLAlchemy with async session management

## Authentication Flow
1. User authentication handled by Flask UI
2. API authentication managed by FastAPI
3. Shared user model and database schema

## Route Structure
```
/                 -> Flask UI routes
/api/v1/*         -> FastAPI routes
  ├── /auth       -> Authentication endpoints
  ├── /mitre      -> MITRE ATT&CK data
  └── /users      -> User management
```

## Model Inheritance and CRUD Operations
### Model Hierarchy
```mermaid
classDiagram
    class SoftDeleteMixin {
        +created_on: DateTime
        +updated_at: DateTime
        +deleted_at: DateTime
        +is_deprecated: Boolean
        +is_revoked: Boolean
        +revoked_by_id: Integer
        +version: String
        +get_all(db, skip_deleted)
        +get_by_id(db, id, skip_deleted)
        +soft_delete(db)
        +restore(db)
        +update(db, **kwargs)
        +create(db, **kwargs)
        +mark_deprecated(db, replacement_id)
    }

    class BaseModel {
        <<Abstract>>
        +id: Integer
        +metadata: MetaData
    }

    class User {
        +username: String
        +email: String
        +hashed_password: String
        +is_active: Boolean
    }

    class Campaign {
        +name: String
        +description: String
        +start_date: DateTime
        +end_date: DateTime
        +status: String
    }

    class TestCase {
        +name: String
        +description: String
        +technique_id: String
        +phase: String
        +outcome: String
    }

    BaseModel <|-- User
    BaseModel <|-- Campaign
    BaseModel <|-- TestCase
    SoftDeleteMixin <|-- User
    SoftDeleteMixin <|-- Campaign
    SoftDeleteMixin <|-- TestCase
```

### CRUD Operations Flow
```mermaid
sequenceDiagram
    participant Client
    participant CRUDRouter
    participant Model
    participant Database

    Client->>CRUDRouter: HTTP Request
    CRUDRouter->>Model: Call Model Method
    Model->>Database: Execute Query
    Database-->>Model: Return Result
    Model-->>CRUDRouter: Process Result
    CRUDRouter-->>Client: HTTP Response

    note over CRUDRouter: Generic CRUD Router<br>supports all models with<br>SoftDeleteMixin
```

### API Architecture
```mermaid
graph TD
    A[Client] --> B[FastAPI Application]
    B --> C[Authentication Middleware]
    C --> D[CRUD Router]
    D --> E[Model Operations]
    E --> F[Database]

    subgraph "CRUD Operations"
    D --> G[Create]
    D --> H[Read]
    D --> I[Update]
    D --> J[Soft Delete]
    D --> K[Restore]
    end

    subgraph "Model Layer"
    E --> L[User Model]
    E --> M[Campaign Model]
    E --> N[TestCase Model]
    end
```

## Implementation Details

### Generic CRUD Router
The `CRUDRouter` class provides a reusable set of CRUD operations that work with any model inheriting from `SoftDeleteMixin`:

```python
class CRUDRouter(Generic[ModelType]):
    """Generic CRUD router for models with soft delete."""

    def __init__(self, model: Type[ModelType], prefix: str, tags: List[str]):
        self.model = model
        self.router = APIRouter(prefix=prefix, tags=tags)
        self._register_routes()
```

### Model Integration
Models inherit from both `BaseModel` and `SoftDeleteMixin` to gain CRUD capabilities:

```python
class User(BaseModel, SoftDeleteMixin):
    __tablename__ = "users"
    # Model fields...
```

### API Endpoints
The generic CRUD router automatically creates the following endpoints for each model:

- `GET /`: List all records (with pagination)
- `GET /{id}`: Get a single record
- `POST /`: Create a new record
- `PATCH /{id}`: Update an existing record
- `DELETE /{id}`: Soft delete a record
- `POST /{id}/restore`: Restore a soft-deleted record

### Soft Delete Implementation
Soft delete is implemented at the mixin level:

```python
def soft_delete(self, db: Session) -> None:
    """Soft delete a record."""
    self.deleted_at = datetime.utcnow()
    db.add(self)
    db.commit()
    db.refresh(self)
```

## Future Considerations

1. **Pagination Enhancement**
   - Implement cursor-based pagination for better performance
   - Add sorting options

2. **Caching Layer**
   - Add Redis caching for frequently accessed records
   - Implement cache invalidation strategies

3. **Audit Trail**
   - Track all changes to records
   - Maintain version history

4. **Bulk Operations**
   - Add support for bulk create/update/delete operations
   - Implement batch processing for large datasets