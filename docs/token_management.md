# Token Management System

## Overview
The token management system provides a secure and flexible way to handle user authentication tokens in the application. It supports both access tokens (short-lived) and refresh tokens (long-lived), with features for token revocation, blacklisting, and automatic cleanup.

## Components

### 1. Token Configuration
Located in `api/config/token_config.py`, the configuration system uses Pydantic settings to manage token-related settings:

```python
class TokenSettings(BaseSettings):
    # JWT Settings
    SECRET_KEY: str = "your-secret-key-here"  # Load from environment
    ALGORITHM: str = "HS256"
    
    # Token Expiration
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 15
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # Token Blacklist
    BLACKLIST_ENABLED: bool = True
    BLACKLIST_CLEANUP_INTERVAL_HOURS: int = 24
```

All settings can be overridden using environment variables with the `TOKEN_` prefix.

### 2. Token Utilities
Located in `api/auth/token_utils.py`, provides core token management functions:

#### Token Creation
- `create_access_token()`: Creates a short-lived access token
- `create_refresh_token()`: Creates a long-lived refresh token

#### Token Validation
- `verify_token()`: Validates a token's signature and expiration
- `decode_token()`: Decodes a token and returns its payload

#### Token Management
- `refresh_access_token()`: Creates a new access token using a refresh token
- `revoke_token()`: Revokes a single token
- `revoke_all_tokens()`: Revokes all tokens for a user
- `cleanup_blacklist()`: Removes old entries from the token blacklist

### 3. Token Router
Located in `api/auth/token.py`, provides HTTP endpoints for token operations:

#### Endpoints
- `POST /refresh`: Refresh an access token using a refresh token
- `POST /revoke`: Revoke a specific token
- `POST /revoke-all`: Revoke all tokens for the current user

## Usage

### Creating Tokens
```python
# Create access token
access_token = create_access_token(
    user_id="user123",
    username="john_doe"
)

# Create refresh token
refresh_token = create_refresh_token(
    user_id="user123",
    username="john_doe"
)
```

### Refreshing Tokens
```python
# Refresh access token
new_access_token = refresh_access_token(refresh_token)
```

### Revoking Tokens
```python
# Revoke single token
revoke_token(db, token, user_id)

# Revoke all tokens
revoke_all_tokens(db, user_id)
```

## Security Features

### 1. Token Expiration
- Access tokens expire after 15 minutes (configurable)
- Refresh tokens expire after 7 days (configurable)

### 2. Token Blacklisting
- Revoked tokens are stored in the blacklist
- Blacklisted tokens cannot be used for authentication
- Automatic cleanup of old blacklist entries

### 3. Token Validation
- Verifies token signature
- Checks token expiration
- Validates required payload fields
- Ensures correct token type

## Best Practices

1. **Token Storage**
   - Store access tokens in memory (client-side)
   - Store refresh tokens securely (e.g., HTTP-only cookies)

2. **Token Rotation**
   - Use refresh tokens to obtain new access tokens
   - Implement token rotation for long-lived sessions

3. **Security**
   - Use HTTPS for all token-related requests
   - Implement rate limiting for token endpoints
   - Monitor token usage for suspicious activity

4. **Error Handling**
   - Handle token expiration gracefully
   - Implement proper error messages for invalid tokens
   - Log security-related events

## Testing
The token management system includes comprehensive tests in `tests/test_api/test_token_management.py` covering:
- Token creation and validation
- Token refresh functionality
- Token revocation
- Blacklist management
- Error cases and edge conditions

## Configuration
To customize the token management system, set the following environment variables:

```bash
# JWT Settings
TOKEN_SECRET_KEY=your-secret-key
TOKEN_ALGORITHM=HS256

# Token Expiration
TOKEN_ACCESS_TOKEN_EXPIRE_MINUTES=15
TOKEN_REFRESH_TOKEN_EXPIRE_DAYS=7

# Token Blacklist
TOKEN_BLACKLIST_ENABLED=true
TOKEN_BLACKLIST_CLEANUP_INTERVAL_HOURS=24
```

## Dependencies
- `python-jose[cryptography]`: JWT token handling
- `pydantic`: Settings management
- `sqlalchemy`: Database operations
- `fastapi`: API endpoints 