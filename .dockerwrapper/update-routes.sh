#!/bin/bash

# Navigate to the project root directory
cd "$(dirname "$0")/.."

# Function to display usage
function display_usage {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  --add-react-route PATH   Add a React route to Nginx configuration"
    echo "  --remove-route PATH      Remove a route from Nginx configuration"
    echo "  --list-routes            List all configured routes"
    echo "  --help                   Display this help message"
    echo ""
    echo "Example:"
    echo "  $0 --add-react-route /dashboard"
    echo "  $0 --remove-route /dashboard"
    echo "  $0 --list-routes"
}

# Function to add a React route to Nginx
function add_react_route {
    local route=$1
    
    # Ensure route starts with /
    if [[ $route != /* ]]; then
        route="/$route"
    fi
    
    # Check if Nginx config exists
    if [ ! -f ".dockerwrapper/nginx/conf.d/default.conf" ]; then
        echo "Error: Nginx configuration file not found"
        exit 1
    fi
    
    # Check if route already exists
    if grep -q "location $route {" .dockerwrapper/nginx/conf.d/default.conf; then
        echo "Route $route already exists in Nginx configuration"
        return
    fi
    
    # Add route to Nginx config before the last closing brace
    sed -i "/location \/ {/i \    location $route {\n        try_files \$uri \$uri\/ /index.html;\n    }\n" .dockerwrapper/nginx/conf.d/default.conf
    
    echo "Added React route $route to Nginx configuration"
}

# Function to remove a route from Nginx
function remove_route {
    local route=$1
    
    # Ensure route starts with /
    if [[ $route != /* ]]; then
        route="/$route"
    fi
    
    # Check if Nginx config exists
    if [ ! -f ".dockerwrapper/nginx/conf.d/default.conf" ]; then
        echo "Error: Nginx configuration file not found"
        exit 1
    fi
    
    # Check if route exists
    if ! grep -q "location $route {" .dockerwrapper/nginx/conf.d/default.conf; then
        echo "Route $route not found in Nginx configuration"
        return
    fi
    
    # Remove route from Nginx config (3 lines: location, try_files, closing brace)
    sed -i "/location $route {/,+2d" .dockerwrapper/nginx/conf.d/default.conf
    
    echo "Removed route $route from Nginx configuration"
}

# Function to list all routes
function list_routes {
    echo "Configured Routes:"
    echo "-----------------"
    grep -n "location .* {" .dockerwrapper/nginx/conf.d/default.conf | grep -v "location / {" | sed 's/location //' | sed 's/ {//'
}

# Parse command line arguments
if [ $# -eq 0 ]; then
    display_usage
    exit 1
fi

action=""
route=""

while [ $# -gt 0 ]; do
    case "$1" in
        --add-react-route)
            action="add-react-route"
            shift
            route=$1
            shift
            ;;
        --remove-route)
            action="remove-route"
            shift
            route=$1
            shift
            ;;
        --list-routes)
            list_routes
            exit 0
            ;;
        --help)
            display_usage
            exit 0
            ;;
        *)
            echo "Error: Unknown option $1"
            display_usage
            exit 1
            ;;
    esac
done

case "$action" in
    add-react-route)
        if [ -z "$route" ]; then
            echo "Error: No route specified"
            display_usage
            exit 1
        fi
        add_react_route "$route"
        ;;
    remove-route)
        if [ -z "$route" ]; then
            echo "Error: No route specified"
            display_usage
            exit 1
        fi
        remove_route "$route"
        ;;
    *)
        echo "Error: No action specified"
        display_usage
        exit 1
        ;;
esac
