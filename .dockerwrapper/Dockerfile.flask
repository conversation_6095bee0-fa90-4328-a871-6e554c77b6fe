FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV FLASK_APP=flask_app.py

# Copy requirements separately for better caching
COPY pyproject.toml /app/

# Install dependencies explicitly and avoid conflicting packages
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir flask flask-login flask-wtf sqlalchemy flask-sqlalchemy werkzeug psycopg2-binary email-validator jinja2

# Copy project files
COPY . /app/

# Make entrypoint script executable
RUN chmod +x /app/.dockerwrapper/docker-entrypoint.sh

# Expose port for Flask
EXPOSE 5000

# Set entrypoint
ENTRYPOINT ["/app/.dockerwrapper/docker-entrypoint.sh"]

# Command to run Flask
CMD ["flask", "run", "--host", "0.0.0.0", "--port", "5000"] 