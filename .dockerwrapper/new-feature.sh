#!/bin/bash

# Navigate to the project root directory
cd "$(dirname "$0")/.."

# Function to display usage
function display_usage {
    echo "Usage: $0 [options] FEATURE_NAME"
    echo "Options:"
    echo "  --create       Create a new feature with all components"
    echo "  --help         Display this help message"
    echo ""
    echo "Example:"
    echo "  $0 --create dashboard"
}

# Function to create a new feature
function create_feature {
    local feature=$1
    
    if [ -z "$feature" ]; then
        echo "Error: No feature name specified"
        display_usage
        exit 1
    fi
    
    echo "Creating new feature: $feature"
    
    # Start API development
    echo "Starting API development..."
    ./.dockerwrapper/feature.sh --start-api "$feature"
    
    # Start test development
    echo "Starting test development..."
    ./.dockerwrapper/feature.sh --start-test "$feature"
    
    # Start UI development
    echo "Starting UI development..."
    ./.dockerwrapper/feature.sh --start-ui "$feature"
    
    # Create database model file
    echo "Creating database model file..."
    create_db_model "$feature"
    
    # Create database migration file
    echo "Creating database migration file..."
    create_db_migration "$feature"
    
    # Update the development roadmap
    echo "Updating development roadmap..."
    ./.dockerwrapper/update-roadmap.sh --update
    
    # Generate dashboard
    echo "Generating dashboard..."
    ./.dockerwrapper/generate-dashboard.sh --generate
    
    echo "Feature '$feature' created successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Implement the database model in api/models/database/${feature}.py"
    echo "2. Update the database migration in api/migrations/versions/xxxx_add_${feature}.py"
    echo "3. Implement the API endpoints in api/endpoints/${feature}.py"
    echo "4. Implement the tests in tests/test_${feature}.py"
    echo "5. Implement the UI components in frontend/src/pages/${feature^}/index.tsx"
    echo "6. Mark each phase as complete when finished:"
    echo "   ./.dockerwrapper/feature.sh --complete-api ${feature}"
    echo "   ./.dockerwrapper/feature.sh --complete-test ${feature}"
    echo "   ./.dockerwrapper/feature.sh --complete-ui ${feature}"
}

# Function to create database model file
function create_db_model {
    local feature=$1
    
    # Create directory if it doesn't exist
    mkdir -p api/models/database
    
    if [ ! -f "api/models/database/${feature}.py" ]; then
        cat > "api/models/database/${feature}.py" << EOF
"""
Database model for ${feature} feature
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from api.database import Base

class ${feature^}(Base):
    """${feature^} database model"""
    __tablename__ = "${feature}s"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # TODO: Add additional fields and relationships as needed
    
    def __repr__(self):
        return f"<${feature^} id={self.id} name={self.name}>"
EOF
        echo "Created database model file: api/models/database/${feature}.py"
    else
        echo "Database model file already exists: api/models/database/${feature}.py"
    fi
}

# Function to create database migration file
function create_db_migration {
    local feature=$1
    
    # Create directory if it doesn't exist
    mkdir -p api/migrations/versions
    
    # Generate a timestamp for the migration filename
    timestamp=$(date +"%Y%m%d%H%M%S")
    
    if [ ! -f "api/migrations/versions/${timestamp}_add_${feature}.py" ]; then
        cat > "api/migrations/versions/${timestamp}_add_${feature}.py" << EOF
"""
Add ${feature} table

Revision ID: ${timestamp}
Revises: 
Create Date: $(date -u +"%Y-%m-%d %H:%M:%S.%3N")
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import func

# revision identifiers, used by Alembic
revision = '${timestamp}'
down_revision = None  # TODO: Update this to the previous migration
branch_labels = None
depends_on = None

def upgrade():
    """Upgrade database schema"""
    op.create_table(
        '${feature}s',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_${feature}s_id'), '${feature}s', ['id'], unique=False)
    
    # TODO: Add additional columns and indexes as needed

def downgrade():
    """Downgrade database schema"""
    op.drop_index(op.f('ix_${feature}s_id'), table_name='${feature}s')
    op.drop_table('${feature}s')
EOF
        echo "Created database migration file: api/migrations/versions/${timestamp}_add_${feature}.py"
    else
        echo "Database migration file already exists: api/migrations/versions/${timestamp}_add_${feature}.py"
    fi
}

# Parse command line arguments
if [ $# -lt 2 ]; then
    display_usage
    exit 1
fi

action=""
feature=""

while [ $# -gt 0 ]; do
    case "$1" in
        --create)
            action="create"
            shift
            ;;
        --help)
            display_usage
            exit 0
            ;;
        -*)
            echo "Error: Unknown option $1"
            display_usage
            exit 1
            ;;
        *)
            feature=$1
            shift
            ;;
    esac
done

case "$action" in
    create)
        create_feature "$feature"
        ;;
    *)
        echo "Error: No action specified"
        display_usage
        exit 1
        ;;
esac 