#!/usr/bin/env python3
"""Database initialization script for Docker startup."""
import time
import logging
import os
import sys
from contextlib import contextmanager
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from sqlalchemy import create_engine, text

# Add the app directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get database URL from environment
DATABASE_URL = os.environ.get("DATABASE_URL")
if not DATABASE_URL:
    logger.critical("DATABASE_URL environment variable is not set!")
    sys.exit(1)

def wait_for_db(max_retries=30, retry_interval=2):
    """Wait for the database to be available."""
    retry_count = 0
    
    # Extract connection parameters from DATABASE_URL
    # Format: postgresql://username:password@host:port/dbname
    # Split by ://
    parts = DATABASE_URL.split("://")[1]
    # Split credentials and host
    credentials, host_part = parts.split("@")
    # Extract username and password
    if ":" in credentials:
        username, password = credentials.split(":")
    else:
        username = credentials
        password = ""
    # Extract host, port, and database name
    if "/" in host_part:
        host_port, dbname = host_part.split("/")
    else:
        host_port = host_part
        dbname = ""
    # Extract host and port
    if ":" in host_port:
        host, port = host_port.split(":")
    else:
        host = host_port
        port = "5432"  # Default PostgreSQL port
    
    logger.info(f"Waiting for PostgreSQL server at {host}:{port}...")
    
    while retry_count < max_retries:
        try:
            conn = psycopg2.connect(
                host=host,
                port=port,
                user=username,
                password=password,
                dbname="postgres"  # Connect to default database initially
            )
            conn.close()
            logger.info("PostgreSQL server is available!")
            return True
        except psycopg2.OperationalError:
            retry_count += 1
            logger.info(f"Waiting for PostgreSQL to become available... (Attempt {retry_count}/{max_retries})")
            time.sleep(retry_interval)
    
    logger.critical(f"Failed to connect to PostgreSQL after {max_retries} attempts")
    return False

def init_database():
    """Initialize the database if it doesn't exist."""
    try:
        # Create a SQLAlchemy engine
        engine = create_engine(DATABASE_URL)
        
        # Test if we can connect to the database
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            logger.info("Database connection successful")
        
        try:
            # Import the database initialization function from the application
            from api.database import init_db
            
            # Initialize the database schema
            init_db()
            logger.info("Database initialized successfully")
        except Exception as e:
            # Check if this is a "table already exists" error
            if "already exists" in str(e):
                logger.warning("Tables already exist, skipping initialization")
                return True
            else:
                # Re-raise the exception if it's not a "table already exists" error
                logger.error(f"Database initialization error: {e}")
                logger.exception("Database initialization failed")
                return False
        
        return True
    except Exception as e:
        logger.error(f"Database initialization error: {e}")
        logger.exception("Database initialization failed")
        return False

def main():
    """Main entry point."""
    # Wait for the database to be available
    if not wait_for_db():
        sys.exit(1)
    
    # Initialize the database
    if not init_database():
        sys.exit(1)
    
    logger.info("Database setup completed successfully")

if __name__ == "__main__":
    main() 