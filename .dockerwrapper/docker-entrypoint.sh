#!/bin/bash
set -e

# Print environment info
echo "Starting RegrigorRigor application..."
echo "Python version: $(python --version)"
echo "Working directory: $(pwd)"

# Wait for the PostgreSQL database
echo "Waiting for PostgreSQL server..."
export PGPASSWORD=regrigor_password
until pg_isready -h postgres -U regrigor -d regrigor_db; do
  echo "PostgreSQL is unavailable - sleeping 2 seconds"
  sleep 2
done
echo "PostgreSQL is up - continuing"

# Check if database exists and is accessible
echo "Checking database connection..."
if psql -h postgres -U regrigor -d regrigor_db -c "SELECT 1" > /dev/null 2>&1; then
  echo "Database connection successful!"
else
  echo "Failed to connect to database."
  exit 1
fi

# Create tables directly if needed, instead of importing potentially problematic modules
if [ -n "$INIT_DB" ] && [ "$INIT_DB" = "true" ]; then
  echo "Initializing database tables..."
  python -c "
import os
import psycopg2
conn = psycopg2.connect('*****************************************************/regrigor_db')
conn.autocommit = True
cursor = conn.cursor()
cursor.execute('''
CREATE TABLE IF NOT EXISTS flask_users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(64) UNIQUE NOT NULL,
    email VARCHAR(120) UNIQUE NOT NULL,
    password_hash VARCHAR(256),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
)
''')
conn.close()
print('Basic tables created.')
"
fi

# Execute the command passed to docker-entrypoint
exec "$@" 