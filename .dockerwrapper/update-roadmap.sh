#!/bin/bash

# Navigate to the project root directory
cd "$(dirname "$0")/.."

# Function to display usage
function display_usage {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  --update       Update the development roadmap based on migration status"
    echo "  --help         Display this help message"
    echo ""
    echo "Example:"
    echo "  $0 --update"
}

# Function to update the development roadmap
function update_roadmap {
    echo "Updating development roadmap..."
    
    # Check if MIGRATION.md exists
    if [ ! -f "MIGRATION.md" ]; then
        echo "Error: MIGRATION.md not found"
        exit 1
    fi
    
    # Check if docs/development_roadmap.md exists
    if [ ! -f "docs/development_roadmap.md" ]; then
        echo "Error: docs/development_roadmap.md not found"
        exit 1
    fi
    
    # Create a temporary file for the updated roadmap
    temp_file=$(mktemp)
    
    # Copy the header section of the roadmap (up to "## Completed Tasks")
    sed -n '1,/## Completed Tasks/p' docs/development_roadmap.md > "$temp_file"
    
    # Add completed features from MIGRATION.md
    echo "" >> "$temp_file"
    echo "### Completed Features" >> "$temp_file"
    
    # Get completed features (where API, Tests, and UI are all Complete)
    grep "| .* | Complete | Complete | Complete |" MIGRATION.md | while read -r line; do
        feature=$(echo "$line" | awk -F'|' '{print $2}' | xargs)
        echo "- ✅ $feature" >> "$temp_file"
    done
    
    # Add features with partial completion
    echo "" >> "$temp_file"
    echo "### Partially Completed Features" >> "$temp_file"
    
    # API complete but not everything else
    grep "| .* | Complete | .* | .* |" MIGRATION.md | grep -v "| .* | Complete | Complete | Complete |" | while read -r line; do
        feature=$(echo "$line" | awk -F'|' '{print $2}' | xargs)
        api_status=$(echo "$line" | awk -F'|' '{print $3}' | xargs)
        test_status=$(echo "$line" | awk -F'|' '{print $4}' | xargs)
        ui_status=$(echo "$line" | awk -F'|' '{print $5}' | xargs)
        
        echo "- $feature" >> "$temp_file"
        echo "  - API: $api_status" >> "$temp_file"
        echo "  - Tests: $test_status" >> "$temp_file"
        echo "  - UI: $ui_status" >> "$temp_file"
    done
    
    # Add in-progress section
    echo "" >> "$temp_file"
    echo "## In Progress" >> "$temp_file"
    echo "" >> "$temp_file"
    
    # Get in-progress features
    grep "| .* | In Progress | .* | .* |" MIGRATION.md | while read -r line; do
        feature=$(echo "$line" | awk -F'|' '{print $2}' | xargs)
        echo "### $feature" >> "$temp_file"
        echo "- [ ] Complete API development" >> "$temp_file"
        echo "- [ ] Implement tests" >> "$temp_file"
        echo "- [ ] Develop UI components" >> "$temp_file"
        echo "" >> "$temp_file"
    done
    
    # Add upcoming features section
    echo "## Upcoming Features" >> "$temp_file"
    echo "" >> "$temp_file"
    
    # Get not started features
    grep "| .* | Not Started | .* | .* |" MIGRATION.md | while read -r line; do
        feature=$(echo "$line" | awk -F'|' '{print $2}' | xargs)
        echo "### $feature" >> "$temp_file"
        echo "- [ ] Start API development" >> "$temp_file"
        echo "- [ ] Implement tests" >> "$temp_file"
        echo "- [ ] Develop UI components" >> "$temp_file"
        echo "" >> "$temp_file"
    done
    
    # Add long-term goals section if it exists in the original roadmap
    if grep -q "## Long-term Goals" docs/development_roadmap.md; then
        echo "## Long-term Goals" >> "$temp_file"
        echo "" >> "$temp_file"
        sed -n '/## Long-term Goals/,/^$/p' docs/development_roadmap.md | tail -n +2 >> "$temp_file"
    fi
    
    # Replace the roadmap with the updated version
    mv "$temp_file" docs/development_roadmap.md
    
    echo "Development roadmap updated successfully"
}

# Parse command line arguments
if [ $# -eq 0 ]; then
    display_usage
    exit 1
fi

while [ $# -gt 0 ]; do
    case "$1" in
        --update)
            update_roadmap
            shift
            ;;
        --help)
            display_usage
            exit 0
            ;;
        *)
            echo "Error: Unknown option $1"
            display_usage
            exit 1
            ;;
    esac
done 