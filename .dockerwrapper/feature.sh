#!/bin/bash

# Navigate to the project root directory
cd "$(dirname "$0")/.."

# Function to display usage
function display_usage {
    echo "Usage: $0 [options] FEATURE_NAME"
    echo "Options:"
    echo "  --start-api      Start API development for the feature"
    echo "  --start-test     Start test development for the feature"
    echo "  --start-ui       Start UI development for the feature"
    echo "  --complete-api   Mark API development as complete"
    echo "  --complete-test  Mark test development as complete"
    echo "  --complete-ui    Mark UI development as complete"
    echo "  --status         Show migration status"
    echo "  --help           Display this help message"
    echo ""
    echo "Example:"
    echo "  $0 --start-api dashboard"
    echo "  $0 --complete-api dashboard"
    echo "  $0 --status"
}

# Function to update migration status
function update_status {
    local feature=$1
    local phase=$2
    local status=$3
    
    # Convert feature name to title case
    feature_title=$(echo $feature | sed 's/\b\(.\)/\u\1/g')
    
    # Check if MIGRATION.md exists, if not create it
    if [ ! -f "MIGRATION.md" ]; then
        cat > "MIGRATION.md" << EOF
# Feature Migration Tracking

This document tracks the development status of features across different phases.

## Migration Tracking

| Feature | API | Tests | UI | Completed Date |
|---------|-----|-------|----|----|
| Admin Interface | Not Started | Not Started | Not Started | - |

## Development Workflow

1. Start API development with \`./dockerwrapper/feature.sh --start-api FEATURE_NAME\`
2. Complete API development with \`./dockerwrapper/feature.sh --complete-api FEATURE_NAME\`
3. Start test development with \`./dockerwrapper/feature.sh --start-test FEATURE_NAME\`
4. Complete test development with \`./dockerwrapper/feature.sh --complete-test FEATURE_NAME\`
5. Start UI development with \`./dockerwrapper/feature.sh --start-ui FEATURE_NAME\`
6. Complete UI development with \`./dockerwrapper/feature.sh --complete-ui FEATURE_NAME\`

## Status Definitions

- **Not Started**: Development has not begun
- **In Progress**: Development is currently underway
- **Complete**: Development is finished and ready for review/deployment
EOF
        echo "Created MIGRATION.md file"
    fi
    
    # Check if feature exists in migration tracking table
    if ! grep -q "| $feature_title " MIGRATION.md; then
        # Add new feature to the table
        sed -i "/| Admin Interface .*/a | $feature_title | Not Started | Not Started | Not Started | - |" MIGRATION.md
    fi
    
    # Update status for the specified phase
    case $phase in
        api)
            sed -i "s/| $feature_title | [^|]* | /| $feature_title | $status | /" MIGRATION.md
            ;;
        test)
            sed -i "s/| $feature_title | [^|]* | [^|]* | /| $feature_title | [^|]* | $status | /" MIGRATION.md
            ;;
        ui)
            sed -i "s/| $feature_title | [^|]* | [^|]* | [^|]* | /| $feature_title | [^|]* | [^|]* | $status | /" MIGRATION.md
            ;;
    esac
    
    # If UI is complete, update migration date
    if [ "$phase" == "ui" ] && [ "$status" == "Complete" ]; then
        current_date=$(date +"%Y-%m-%d")
        sed -i "s/| $feature_title | [^|]* | [^|]* | [^|]* | [^|]* |/| $feature_title | [^|]* | [^|]* | [^|]* | $current_date |/" MIGRATION.md
    fi
    
    echo "Updated migration status for $feature_title $phase to $status"
}

# Function to show migration status
function show_status {
    echo "Migration Status:"
    echo "----------------"
    grep -A 100 "## Migration Tracking" MIGRATION.md | grep -B 100 "## Development Workflow" | grep "|"
}

# Function to create API files
function create_api_files {
    local feature=$1
    
    # Create API endpoint file
    mkdir -p api/endpoints
    
    if [ ! -f "api/endpoints/${feature}.py" ]; then
        cat > "api/endpoints/${feature}.py" << EOF
"""
API endpoints for ${feature} feature
"""
from fastapi import APIRouter, Depends, HTTPException
from typing import List, Optional
from models.${feature} import ${feature^}Model, ${feature^}Create, ${feature^}Update
from database import get_db
from sqlalchemy.orm import Session
from api.utils.rate_limiter import standard_rate_limit

router = APIRouter(
    prefix="/${feature}",
    tags=["${feature}"],
    responses={404: {"description": "${feature^} not found"}},
)

@router.get("/", response_model=List[${feature^}Model])
async def get_all_${feature}s(
    skip: int = 0, 
    limit: int = 100,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit)
):
    """
    Get all ${feature}s
    """
    # TODO: Implement ${feature} retrieval logic
    return []

@router.get("/{${feature}_id}", response_model=${feature^}Model)
async def get_${feature}(
    ${feature}_id: int,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit)
):
    """
    Get a specific ${feature} by ID
    """
    # TODO: Implement ${feature} retrieval logic
    return None

@router.post("/", response_model=${feature^}Model)
async def create_${feature}(
    ${feature}: ${feature^}Create,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit)
):
    """
    Create a new ${feature}
    """
    # TODO: Implement ${feature} creation logic
    return None

@router.put("/{${feature}_id}", response_model=${feature^}Model)
async def update_${feature}(
    ${feature}_id: int,
    ${feature}: ${feature^}Update,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit)
):
    """
    Update a ${feature}
    """
    # TODO: Implement ${feature} update logic
    return None

@router.delete("/{${feature}_id}")
async def delete_${feature}(
    ${feature}_id: int,
    db: Session = Depends(get_db),
    _: None = Depends(standard_rate_limit)
):
    """
    Delete a ${feature}
    """
    # TODO: Implement ${feature} deletion logic
    return {"message": "${feature^} deleted"}
EOF
        echo "Created API endpoint file: api/endpoints/${feature}.py"
    else
        echo "API endpoint file already exists: api/endpoints/${feature}.py"
    fi
    
    # Create model file
    mkdir -p models
    
    if [ ! -f "models/${feature}.py" ]; then
        cat > "models/${feature}.py" << EOF
"""
Models for ${feature} feature
"""
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from api.utils.validators import CommonValidators

class ${feature^}Base(BaseModel):
    """Base ${feature} model"""
    name: str = Field(..., description="Name of the ${feature}")
    description: Optional[str] = Field(None, description="Description of the ${feature}")

class ${feature^}Create(${feature^}Base, CommonValidators):
    """${feature^} creation model"""
    pass

class ${feature^}Update(${feature^}Base):
    """${feature^} update model"""
    name: Optional[str] = Field(None, description="Name of the ${feature}")

class ${feature^}Model(${feature^}Base):
    """${feature^} response model"""
    id: int = Field(..., description="Unique identifier for the ${feature}")
    created_at: datetime = Field(..., description="Timestamp when the ${feature} was created")
    updated_at: Optional[datetime] = Field(None, description="Timestamp when the ${feature} was last updated")
    
    class Config:
        orm_mode = True
EOF
        echo "Created model file: models/${feature}.py"
    else
        echo "Model file already exists: models/${feature}.py"
    fi
}

# Function to create test files
function create_test_files {
    local feature=$1
    
    # Create test file
    mkdir -p tests
    
    if [ ! -f "tests/test_${feature}.py" ]; then
        cat > "tests/test_${feature}.py" << EOF
"""
Tests for ${feature} feature
"""
import pytest
from fastapi.testclient import TestClient
from api.main import app
from models.${feature} import ${feature^}Create, ${feature^}Update
from datetime import datetime
import json

client = TestClient(app)

@pytest.fixture
def sample_${feature}_data():
    """Fixture for sample ${feature} data"""
    return {
        "name": "Test ${feature^}",
        "description": "Test description"
    }

def test_get_all_${feature}s():
    """Test getting all ${feature}s"""
    response = client.get("/api/v1/${feature}/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)

def test_get_${feature}_not_found():
    """Test getting a non-existent ${feature}"""
    ${feature}_id = 9999  # Assuming this ID doesn't exist
    response = client.get(f"/api/v1/${feature}/{${feature}_id}")
    assert response.status_code == 404

def test_create_${feature}(sample_${feature}_data):
    """Test creating a ${feature}"""
    response = client.post("/api/v1/${feature}/", json=sample_${feature}_data)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == sample_${feature}_data["name"]
    assert data["description"] == sample_${feature}_data["description"]
    assert "id" in data
    assert "created_at" in data
    
    # Store the created ID for other tests
    return data["id"]

def test_update_${feature}(sample_${feature}_data):
    """Test updating a ${feature}"""
    # First create a ${feature}
    create_response = client.post("/api/v1/${feature}/", json=sample_${feature}_data)
    ${feature}_id = create_response.json()["id"]
    
    # Now update it
    update_data = {
        "name": "Updated ${feature^}",
        "description": "Updated description"
    }
    response = client.put(f"/api/v1/${feature}/{${feature}_id}", json=update_data)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == update_data["name"]
    assert data["description"] == update_data["description"]
    assert "updated_at" in data
    
    # Verify the update with a GET request
    get_response = client.get(f"/api/v1/${feature}/{${feature}_id}")
    assert get_response.status_code == 200
    assert get_response.json()["name"] == update_data["name"]

def test_delete_${feature}(sample_${feature}_data):
    """Test deleting a ${feature}"""
    # First create a ${feature}
    create_response = client.post("/api/v1/${feature}/", json=sample_${feature}_data)
    ${feature}_id = create_response.json()["id"]
    
    # Now delete it
    response = client.delete(f"/api/v1/${feature}/{${feature}_id}")
    assert response.status_code == 200
    assert response.json()["message"] == "${feature^} deleted"
    
    # Verify it's gone with a GET request
    get_response = client.get(f"/api/v1/${feature}/{${feature}_id}")
    assert get_response.status_code == 404

def test_validation_errors():
    """Test validation errors when creating a ${feature}"""
    # Missing required field
    invalid_data = {
        "description": "Missing name field"
    }
    response = client.post("/api/v1/${feature}/", json=invalid_data)
    assert response.status_code == 422
    
    # Empty name
    invalid_data = {
        "name": "",
        "description": "Empty name"
    }
    response = client.post("/api/v1/${feature}/", json=invalid_data)
    assert response.status_code == 422
EOF
        echo "Created test file: tests/test_${feature}.py"
    else
        echo "Test file already exists: tests/test_${feature}.py"
    fi
}

# Function to create React component files
function create_ui_files {
    local feature=$1
    
    # Create React component files
    mkdir -p frontend/src/pages/${feature^}
    
    if [ ! -f "frontend/src/pages/${feature^}/index.tsx" ]; then
        cat > "frontend/src/pages/${feature^}/index.tsx" << EOF
import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { useParams, useNavigate } from 'react-router-dom';
import { Button, Card, Container, Row, Col, Form, Alert, Spinner } from 'react-bootstrap';
import './styles.css';

interface ${feature^}Data {
  id: number;
  name: string;
  description: string;
  created_at: string;
  updated_at: string | null;
}

const ${feature^}: React.FC = () => {
  const [data, setData] = useState<${feature^}Data[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<{name: string, description: string}>({
    name: '',
    description: ''
  });
  const [isEditing, setIsEditing] = useState<number | null>(null);
  
  const navigate = useNavigate();
  
  useEffect(() => {
    fetch${feature^}Data();
  }, []);
  
  const fetch${feature^}Data = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/v1/${feature}');
      setData(response.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching ${feature} data:', err);
      setError('Failed to load ${feature} data. Please try again later.');
      // For development, use mock data when API is not available
      setData([
        { 
          id: 1, 
          name: 'Sample ${feature^}', 
          description: 'This is a sample ${feature}',
          created_at: new Date().toISOString(),
          updated_at: null
        }
      ]);
    } finally {
      setLoading(false);
    }
  };
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (isEditing) {
        // Update existing ${feature}
        await axios.put(\`/api/v1/${feature}/\${isEditing}\`, formData);
      } else {
        // Create new ${feature}
        await axios.post('/api/v1/${feature}/', formData);
      }
      
      // Reset form and refresh data
      setFormData({ name: '', description: '' });
      setIsEditing(null);
      fetch${feature^}Data();
    } catch (err) {
      console.error('Error saving ${feature}:', err);
      setError('Failed to save ${feature}. Please try again.');
    }
  };
  
  const handleEdit = (item: ${feature^}Data) => {
    setFormData({
      name: item.name,
      description: item.description || ''
    });
    setIsEditing(item.id);
  };
  
  const handleDelete = async (id: number) => {
    if (!window.confirm('Are you sure you want to delete this ${feature}?')) {
      return;
    }
    
    try {
      await axios.delete(\`/api/v1/${feature}/\${id}\`);
      fetch${feature^}Data();
    } catch (err) {
      console.error('Error deleting ${feature}:', err);
      setError('Failed to delete ${feature}. Please try again.');
    }
  };
  
  const handleCancel = () => {
    setFormData({ name: '', description: '' });
    setIsEditing(null);
  };

  if (loading && data.length === 0) {
    return (
      <Container className="mt-4">
        <div className="text-center">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p>Loading ${feature} data...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container className="${feature}-container mt-4">
      <h1>${feature^} Management</h1>
      <p>This is the ${feature} management interface.</p>
      
      {error && <Alert variant="danger">{error}</Alert>}
      
      <Card className="mb-4">
        <Card.Header>
          {isEditing ? 'Edit ${feature^}' : 'Create New ${feature^}'}
        </Card.Header>
        <Card.Body>
          <Form onSubmit={handleSubmit}>
            <Form.Group className="mb-3">
              <Form.Label>Name</Form.Label>
              <Form.Control 
                type="text" 
                name="name" 
                value={formData.name} 
                onChange={handleInputChange} 
                required 
              />
            </Form.Group>
            
            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control 
                as="textarea" 
                name="description" 
                value={formData.description} 
                onChange={handleInputChange} 
                rows={3} 
              />
            </Form.Group>
            
            <div className="d-flex gap-2">
              <Button variant="primary" type="submit">
                {isEditing ? 'Update' : 'Create'}
              </Button>
              {isEditing && (
                <Button variant="secondary" onClick={handleCancel}>
                  Cancel
                </Button>
              )}
            </div>
          </Form>
        </Card.Body>
      </Card>
      
      <h2>${feature^} List</h2>
      {data.length === 0 ? (
        <Alert variant="info">No ${feature} data found.</Alert>
      ) : (
        <Row xs={1} md={2} lg={3} className="g-4">
          {data.map(item => (
            <Col key={item.id}>
              <Card className="${feature}-item">
                <Card.Body>
                  <Card.Title>{item.name}</Card.Title>
                  <Card.Text>{item.description}</Card.Text>
                  <div className="${feature}-meta">
                    <small>Created: {new Date(item.created_at).toLocaleDateString()}</small>
                    {item.updated_at && (
                      <small>Updated: {new Date(item.updated_at).toLocaleDateString()}</small>
                    )}
                  </div>
                </Card.Body>
                <Card.Footer className="d-flex justify-content-between">
                  <Button variant="outline-primary" size="sm" onClick={() => handleEdit(item)}>
                    Edit
                  </Button>
                  <Button variant="outline-danger" size="sm" onClick={() => handleDelete(item.id)}>
                    Delete
                  </Button>
                </Card.Footer>
              </Card>
            </Col>
          ))}
        </Row>
      )}
    </Container>
  );
};

export default ${feature^};
EOF
        echo "Created React component file: frontend/src/pages/${feature^}/index.tsx"
    else
        echo "React component file already exists: frontend/src/pages/${feature^}/index.tsx"
    fi
    
    if [ ! -f "frontend/src/pages/${feature^}/styles.css" ]; then
        cat > "frontend/src/pages/${feature^}/styles.css" << EOF
.${feature}-container {
  padding-bottom: 2rem;
}

.${feature}-item {
  height: 100%;
  transition: transform 0.2s, box-shadow 0.2s;
}

.${feature}-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.${feature}-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #6c757d;
  margin-top: 1rem;
}

/* Form styling */
textarea.form-control {
  min-height: 100px;
}
EOF
        echo "Created CSS file: frontend/src/pages/${feature^}/styles.css"
    else
        echo "CSS file already exists: frontend/src/pages/${feature^}/styles.css"
    fi
    
    # Update routes file if it exists
    if [ -f "frontend/src/routes.tsx" ]; then
        # Check if route already exists
        if ! grep -q "${feature^}" frontend/src/routes.tsx; then
            # Add import statement
            sed -i "/import React from 'react';/a import ${feature^} from './pages/${feature^}';" frontend/src/routes.tsx
            
            # Add route to routes array
            sed -i "/const routes = \[/a \ \ { path: '/${feature}', element: <${feature^} /> }," frontend/src/routes.tsx
            
            echo "Updated routes file with ${feature} route"
        else
            echo "Route for ${feature} already exists in routes file"
        fi
    fi
}

# Function to create update-routes.sh if it doesn't exist
function ensure_update_routes_script {
    if [ ! -f ".dockerwrapper/update-routes.sh" ]; then
        cat > ".dockerwrapper/update-routes.sh" << 'EOF'
#!/bin/bash

# Navigate to the project root directory
cd "$(dirname "$0")/.."

# Function to display usage
function display_usage {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  --add-react-route PATH   Add a React route to Nginx configuration"
    echo "  --remove-route PATH      Remove a route from Nginx configuration"
    echo "  --list-routes            List all configured routes"
    echo "  --help                   Display this help message"
    echo ""
    echo "Example:"
    echo "  $0 --add-react-route /dashboard"
    echo "  $0 --remove-route /dashboard"
    echo "  $0 --list-routes"
}

# Function to add a React route to Nginx
function add_react_route {
    local route=$1
    
    # Ensure route starts with /
    if [[ $route != /* ]]; then
        route="/$route"
    fi
    
    # Check if Nginx config exists
    if [ ! -f ".dockerwrapper/nginx/conf.d/default.conf" ]; then
        echo "Error: Nginx configuration file not found"
        exit 1
    fi
    
    # Check if route already exists
    if grep -q "location $route {" .dockerwrapper/nginx/conf.d/default.conf; then
        echo "Route $route already exists in Nginx configuration"
        return
    fi
    
    # Add route to Nginx config before the last closing brace
    sed -i "/location \/ {/i \    location $route {\n        try_files \$uri \$uri\/ /index.html;\n    }\n" .dockerwrapper/nginx/conf.d/default.conf
    
    echo "Added React route $route to Nginx configuration"
}

# Function to remove a route from Nginx
function remove_route {
    local route=$1
    
    # Ensure route starts with /
    if [[ $route != /* ]]; then
        route="/$route"
    fi
    
    # Check if Nginx config exists
    if [ ! -f ".dockerwrapper/nginx/conf.d/default.conf" ]; then
        echo "Error: Nginx configuration file not found"
        exit 1
    fi
    
    # Check if route exists
    if ! grep -q "location $route {" .dockerwrapper/nginx/conf.d/default.conf; then
        echo "Route $route not found in Nginx configuration"
        return
    fi
    
    # Remove route from Nginx config (3 lines: location, try_files, closing brace)
    sed -i "/location $route {/,+2d" .dockerwrapper/nginx/conf.d/default.conf
    
    echo "Removed route $route from Nginx configuration"
}

# Function to list all routes
function list_routes {
    echo "Configured Routes:"
    echo "-----------------"
    grep -n "location .* {" .dockerwrapper/nginx/conf.d/default.conf | grep -v "location / {" | sed 's/location //' | sed 's/ {//'
}

# Parse command line arguments
if [ $# -eq 0 ]; then
    display_usage
    exit 1
fi

action=""
route=""

while [ $# -gt 0 ]; do
    case "$1" in
        --add-react-route)
            action="add-react-route"
            shift
            route=$1
            shift
            ;;
        --remove-route)
            action="remove-route"
            shift
            route=$1
            shift
            ;;
        --list-routes)
            list_routes
            exit 0
            ;;
        --help)
            display_usage
            exit 0
            ;;
        *)
            echo "Error: Unknown option $1"
            display_usage
            exit 1
            ;;
    esac
done

case "$action" in
    add-react-route)
        if [ -z "$route" ]; then
            echo "Error: No route specified"
            display_usage
            exit 1
        fi
        add_react_route "$route"
        ;;
    remove-route)
        if [ -z "$route" ]; then
            echo "Error: No route specified"
            display_usage
            exit 1
        fi
        remove_route "$route"
        ;;
    *)
        echo "Error: No action specified"
        display_usage
        exit 1
        ;;
esac
EOF
        chmod +x .dockerwrapper/update-routes.sh
        echo "Created update-routes.sh script"
    fi
}

# Parse command line arguments
if [ $# -eq 0 ]; then
    display_usage
    exit 1
fi

action=""
feature=""

while [ $# -gt 0 ]; do
    case "$1" in
        --start-api)
            action="start-api"
            shift
            ;;
        --start-test)
            action="start-test"
            shift
            ;;
        --start-ui)
            action="start-ui"
            shift
            ;;
        --complete-api)
            action="complete-api"
            shift
            ;;
        --complete-test)
            action="complete-test"
            shift
            ;;
        --complete-ui)
            action="complete-ui"
            shift
            ;;
        --status)
            show_status
            exit 0
            ;;
        --help)
            display_usage
            exit 0
            ;;
        -*)
            echo "Error: Unknown option $1"
            display_usage
            exit 1
            ;;
        *)
            feature=$1
            shift
            ;;
    esac
done

if [ "$action" != "" ] && [ "$feature" == "" ]; then
    echo "Error: No feature name specified"
    display_usage
    exit 1
fi

# Ensure update-routes.sh exists
ensure_update_routes_script

case "$action" in
    start-api)
        update_status "$feature" "api" "In Progress"
        create_api_files "$feature"
        ;;
    start-test)
        update_status "$feature" "test" "In Progress"
        create_test_files "$feature"
        ;;
    start-ui)
        update_status "$feature" "ui" "In Progress"
        create_ui_files "$feature"
        ;;
    complete-api)
        update_status "$feature" "api" "Complete"
        ;;
    complete-test)
        update_status "$feature" "test" "Complete"
        ;;
    complete-ui)
        update_status "$feature" "ui" "Complete"
        # Update Nginx routes to point to React implementation
        ./.dockerwrapper/update-routes.sh --add-react-route "/$feature"
        echo "Added React route for /$feature"
        echo "You need to restart Nginx for changes to take effect:"
        echo "./.dockerwrapper/run.sh restart nginx"
        ;;
    *)
        echo "Error: No action specified"
        display_usage
        exit 1
        ;;
esac 