# Docker Wrapper and Feature Management

This directory contains Docker configuration files and scripts for managing the development environment and feature development process.

## Feature Management System

The feature management system helps track the development status of features across different phases (API, Tests, UI) and automates the creation of boilerplate code.

### Master Management Script

The `manage.sh` script provides a unified interface to all feature management tools:

```bash
# Show help
./.dockerwrapper/manage.sh help

# Manage feature development
./.dockerwrapper/manage.sh feature --start-api dashboard
./.dockerwrapper/manage.sh feature --complete-api dashboard

# Create a new feature with all components
./.dockerwrapper/manage.sh new --create dashboard

# Show feature development status
./.dockerwrapper/manage.sh status

# Generate the feature development dashboard
./.dockerwrapper/manage.sh dashboard

# Generate a project report
./.dockerwrapper/manage.sh report

# Update the development roadmap
./.dockerwrapper/manage.sh update
```

### Individual Scripts

- **feature.sh**: Main script for managing feature development
- **new-feature.sh**: Script for creating a new feature with all components
- **generate-dashboard.sh**: Script for generating a visual dashboard of feature development
- **generate-report.sh**: Script for generating a project report
- **update-roadmap.sh**: Script for updating the development roadmap based on migration status
- **update-routes.sh**: Script for managing routes in the Nginx configuration

### Using the Feature Management System

#### Starting Development for a New Feature

```bash
# Start API development
./.dockerwrapper/feature.sh --start-api feature_name

# Start test development
./.dockerwrapper/feature.sh --start-test feature_name

# Start UI development
./.dockerwrapper/feature.sh --start-ui feature_name
```

#### Completing Development Phases

```bash
# Mark API development as complete
./.dockerwrapper/feature.sh --complete-api feature_name

# Mark test development as complete
./.dockerwrapper/feature.sh --complete-test feature_name

# Mark UI development as complete
./.dockerwrapper/feature.sh --complete-ui feature_name
```

#### Checking Status

```bash
# Show migration status
./.dockerwrapper/feature.sh --status
```

#### Creating a New Feature

```bash
# Create a new feature with all components
./.dockerwrapper/new-feature.sh --create feature_name
```

#### Generating Reports and Dashboards

```bash
# Generate a visual dashboard
./.dockerwrapper/generate-dashboard.sh --generate

# Generate a project report
./.dockerwrapper/generate-report.sh --generate
```

#### Updating the Development Roadmap

```bash
# Update the development roadmap based on migration status
./.dockerwrapper/update-roadmap.sh --update
```

### Generated Files

When starting development for a feature, the system generates:

- **API Development**: 
  - API endpoint file in `api/endpoints/FEATURE_NAME.py`
  - Model file in `models/FEATURE_NAME.py`
  - Database model in `api/models/database/FEATURE_NAME.py`
  - Database migration in `api/migrations/versions/TIMESTAMP_add_FEATURE_NAME.py`

- **Test Development**:
  - Test file in `tests/test_FEATURE_NAME.py`

- **UI Development**:
  - React component in `frontend/src/pages/FEATURE_NAME/index.tsx`
  - CSS file in `frontend/src/pages/FEATURE_NAME/styles.css`
  - Updates routes in `frontend/src/routes.tsx` (if it exists)
  - Adds route to Nginx configuration when completed

### Migration Tracking

The system maintains a `MIGRATION.md` file in the project root that tracks the development status of all features. The status is updated automatically when using the feature management scripts.

### Visualization and Reporting

- **Dashboard**: An HTML dashboard is generated at `docs/dashboard.html` that provides a visual overview of feature development status.
- **Project Reports**: Project reports are generated at `docs/project_report_DATE.md` with statistics and recommendations.

## Docker Configuration

This directory also contains Docker configuration files for the development environment:

- **docker-compose.yml**: Docker Compose configuration for the development environment
- **Dockerfile**: Dockerfile for the API container
- **Dockerfile.flask**: Dockerfile for the web container
- **nginx/**: Nginx configuration files

## Development Environment

To start the development environment:

```bash
docker-compose -f .dockerwrapper/docker-compose.yml up -d
```

To stop the development environment:

```bash
docker-compose -f .dockerwrapper/docker-compose.yml down
```

To rebuild containers:

```bash
docker-compose -f .dockerwrapper/docker-compose.yml up -d --build
```

## Files

- `Dockerfile` - Docker configuration for the FastAPI backend
- `Dockerfile.flask` - Docker configuration for the Flask frontend
- `docker-compose.yml` - Docker Compose configuration for orchestrating all services
- `.dockerignore` - Files to exclude from Docker builds
- `docker-entrypoint.sh` - Entrypoint script for Docker containers
- `init_db.py` - Script for initializing the database in Docker containers

## Usage

To use these Docker configurations, run the following commands from the project root:

```bash
# Build and start all services
docker-compose -f .dockerwrapper/docker-compose.yml up -d

# View logs
docker-compose -f .dockerwrapper/docker-compose.yml logs -f

# Stop all services
docker-compose -f .dockerwrapper/docker-compose.yml down
```

For more detailed instructions, see the [Docker Setup](../docs/docker_setup.md) documentation. 