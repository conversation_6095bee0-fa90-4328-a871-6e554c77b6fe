# Git
.git
.gitignore
.gitmodules

# Docker
Dockerfile
Dockerfile.flask
docker-compose.yml
.dockerignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
ENV/
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Tests
.pytest_cache/
.coverage
htmlcov/
coverage_reports/

# Environment
.env
.venv

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs/
*.log

# Temp files
.DS_Store
Thumbs.db 