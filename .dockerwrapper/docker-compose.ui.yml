version: '3.8'

services:
  postgres:
    image: postgres:16
    container_name: regrigor-postgres
    environment:
      POSTGRES_USER: regrigor
      POSTGRES_PASSWORD: regrigor_password
      POSTGRES_DB: regrigor_db
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - "3330:5432"
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "regrigor"]
      interval: 5s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: regrigor-redis
    ports:
      - "3331:6379"
    volumes:
      - redisdata:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  ui:
    image: python:3.11-slim
    container_name: regrigor-ui
    environment:
      - DATABASE_URL=*****************************************************/regrigor_db
      - FLASK_APP=simple_app.py
      - FLASK_DEBUG=1
      - REDIS_URL=redis://redis:6379/0
      - SESSION_SECRET=dev_session_secret_key_12345
      - JWT_SECRET_KEY=dev_jwt_secret_key_67890
      - JWT_ALGORITHM=HS256
      - PYTHONPATH=/app
    ports:
      - "3335:5000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ..:/app
    working_dir: /app
    command: >
      bash -c "apt-get update && apt-get install -y gcc && 
              pip install flask flask-login flask-wtf werkzeug psycopg2-binary email-validator && 
              python simple_app.py"
    restart: unless-stopped

volumes:
  pgdata:
  redisdata: 