#!/bin/bash

# Function to display help
function display_help {
    echo "Regression Rigor Management Script"
    echo ""
    echo "Usage: ./manage COMMAND [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  feature [OPTIONS]    Manage feature development phases"
    echo "  new [OPTIONS]        Create a new feature with all components"
    echo "  status               Show feature development status"
    echo "  dashboard            Generate the feature development dashboard"
    echo "  report               Generate a project report"
    echo "  update               Update the development roadmap"
    echo "  help                 Display this help message"
    echo ""
    echo "Examples:"
    echo "  ./manage feature --start-api dashboard    Start API development for dashboard feature"
    echo "  ./manage new --create user-management     Create a new feature named user-management"
    echo "  ./manage status                           Show current feature status"
    echo "  ./manage dashboard                        Generate the dashboard"
    echo "  ./manage report                           Generate a project report"
    echo ""
    echo "For more detailed help on specific commands, run:"
    echo "  ./manage feature --help"
    echo "  ./manage new --help"
}

# Check if we have no arguments
if [ $# -eq 0 ]; then
    display_help
    exit 1
fi

command=$1
shift

case "$command" in
    feature)
        ./.dockerwrapper/feature.sh "$@"
        ;;
    new)
        ./.dockerwrapper/new-feature.sh "$@"
        ;;
    status)
        ./.dockerwrapper/feature.sh --status
        ;;
    dashboard)
        if [ $# -eq 0 ]; then
            ./.dockerwrapper/generate-dashboard.sh --generate
        else
            ./.dockerwrapper/generate-dashboard.sh "$@"
        fi
        ;;
    report)
        if [ $# -eq 0 ]; then
            ./.dockerwrapper/generate-report.sh --generate
        else
            ./.dockerwrapper/generate-report.sh "$@"
        fi
        ;;
    update)
        if [ $# -eq 0 ]; then
            ./.dockerwrapper/update-roadmap.sh --update
        else
            ./.dockerwrapper/update-roadmap.sh "$@"
        fi
        ;;
    help)
        display_help
        ;;
    *)
        echo "Error: Unknown command '$command'"
        display_help
        exit 1
        ;;
esac 