#!/bin/bash

# Navigate to the project root directory
cd "$(dirname "$0")/.."

# Function to display usage
function display_usage {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  --generate    Generate the project report"
    echo "  --help        Display this help message"
    echo ""
    echo "Example:"
    echo "  $0 --generate"
}

# Function to generate the project report
function generate_report {
    echo "Generating project report..."
    
    # Check if MIGRATION.md exists
    if [ ! -f "MIGRATION.md" ]; then
        echo "Error: MIGRATION.md not found"
        exit 1
    fi
    
    # Create docs directory if it doesn't exist
    mkdir -p docs
    
    # Get current date
    current_date=$(date +"%Y-%m-%d")
    
    # Create the report file
    cat > "docs/project_report_${current_date}.md" << EOF
# Project Report - ${current_date}

## Overview

This report provides an overview of the current state of the Regression Rigor project.

## Feature Status

EOF
    
    # Extract feature data from MIGRATION.md
    echo "### Feature Development Status" >> "docs/project_report_${current_date}.md"
    echo "" >> "docs/project_report_${current_date}.md"
    echo "| Feature | API | Tests | UI | Completed Date |" >> "docs/project_report_${current_date}.md"
    echo "|---------|-----|-------|----|----|" >> "docs/project_report_${current_date}.md"
    
    grep -A 100 "## Migration Tracking" MIGRATION.md | grep -B 100 "## Development Workflow" | grep "|" | grep -v "Feature |" | while read -r line; do
        feature=$(echo "$line" | awk -F'|' '{print $2}' | xargs)
        api_status=$(echo "$line" | awk -F'|' '{print $3}' | xargs)
        test_status=$(echo "$line" | awk -F'|' '{print $4}' | xargs)
        ui_status=$(echo "$line" | awk -F'|' '{print $5}' | xargs)
        completed_date=$(echo "$line" | awk -F'|' '{print $6}' | xargs)
        
        # Skip the header row
        if [ "$feature" == "Feature" ]; then
            continue
        fi
        
        echo "| $feature | $api_status | $test_status | $ui_status | $completed_date |" >> "docs/project_report_${current_date}.md"
    done
    
    # Add statistics section
    echo "" >> "docs/project_report_${current_date}.md"
    echo "## Statistics" >> "docs/project_report_${current_date}.md"
    echo "" >> "docs/project_report_${current_date}.md"
    
    # Calculate statistics
    total_features=$(grep -A 100 "## Migration Tracking" MIGRATION.md | grep -B 100 "## Development Workflow" | grep "|" | grep -v "Feature |" | wc -l)
    completed_features=$(grep -A 100 "## Migration Tracking" MIGRATION.md | grep -B 100 "## Development Workflow" | grep "| Complete | Complete | Complete |" | wc -l)
    in_progress_features=$(grep -A 100 "## Migration Tracking" MIGRATION.md | grep -B 100 "## Development Workflow" | grep "| In Progress" | wc -l)
    not_started_features=$((total_features - completed_features - in_progress_features))
    
    # Calculate API, Test, and UI completion percentages
    api_complete=$(grep -A 100 "## Migration Tracking" MIGRATION.md | grep -B 100 "## Development Workflow" | grep "| Complete |" | wc -l)
    test_complete=$(grep -A 100 "## Migration Tracking" MIGRATION.md | grep -B 100 "## Development Workflow" | grep "| .* | Complete |" | wc -l)
    ui_complete=$(grep -A 100 "## Migration Tracking" MIGRATION.md | grep -B 100 "## Development Workflow" | grep "| .* | .* | Complete |" | wc -l)
    
    api_percent=$((api_complete * 100 / total_features))
    test_percent=$((test_complete * 100 / total_features))
    ui_percent=$((ui_complete * 100 / total_features))
    overall_percent=$(( (api_percent + test_percent + ui_percent) / 3 ))
    
    echo "### Feature Counts" >> "docs/project_report_${current_date}.md"
    echo "" >> "docs/project_report_${current_date}.md"
    echo "- **Total Features**: $total_features" >> "docs/project_report_${current_date}.md"
    echo "- **Completed Features**: $completed_features" >> "docs/project_report_${current_date}.md"
    echo "- **In Progress Features**: $in_progress_features" >> "docs/project_report_${current_date}.md"
    echo "- **Not Started Features**: $not_started_features" >> "docs/project_report_${current_date}.md"
    
    echo "" >> "docs/project_report_${current_date}.md"
    echo "### Completion Percentages" >> "docs/project_report_${current_date}.md"
    echo "" >> "docs/project_report_${current_date}.md"
    echo "- **API**: $api_percent%" >> "docs/project_report_${current_date}.md"
    echo "- **Tests**: $test_percent%" >> "docs/project_report_${current_date}.md"
    echo "- **UI**: $ui_percent%" >> "docs/project_report_${current_date}.md"
    echo "- **Overall**: $overall_percent%" >> "docs/project_report_${current_date}.md"
    
    # Add code statistics section
    echo "" >> "docs/project_report_${current_date}.md"
    echo "### Code Statistics" >> "docs/project_report_${current_date}.md"
    echo "" >> "docs/project_report_${current_date}.md"
    
    # Count Python files and lines
    python_files=$(find . -name "*.py" | wc -l)
    python_lines=$(find . -name "*.py" | xargs cat | wc -l)
    
    # Count TypeScript/JavaScript files and lines
    ts_files=$(find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | wc -l)
    ts_lines=$(find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | xargs cat 2>/dev/null | wc -l)
    
    # Count HTML/CSS files and lines
    html_files=$(find . -name "*.html" -o -name "*.css" | wc -l)
    html_lines=$(find . -name "*.html" -o -name "*.css" | xargs cat 2>/dev/null | wc -l)
    
    # Count shell script files and lines
    shell_files=$(find . -name "*.sh" | wc -l)
    shell_lines=$(find . -name "*.sh" | xargs cat | wc -l)
    
    # Count total files and lines
    total_files=$((python_files + ts_files + html_files + shell_files))
    total_lines=$((python_lines + ts_lines + html_lines + shell_lines))
    
    echo "#### File Counts" >> "docs/project_report_${current_date}.md"
    echo "" >> "docs/project_report_${current_date}.md"
    echo "- **Python Files**: $python_files" >> "docs/project_report_${current_date}.md"
    echo "- **TypeScript/JavaScript Files**: $ts_files" >> "docs/project_report_${current_date}.md"
    echo "- **HTML/CSS Files**: $html_files" >> "docs/project_report_${current_date}.md"
    echo "- **Shell Script Files**: $shell_files" >> "docs/project_report_${current_date}.md"
    echo "- **Total Files**: $total_files" >> "docs/project_report_${current_date}.md"
    
    echo "" >> "docs/project_report_${current_date}.md"
    echo "#### Line Counts" >> "docs/project_report_${current_date}.md"
    echo "" >> "docs/project_report_${current_date}.md"
    echo "- **Python Lines**: $python_lines" >> "docs/project_report_${current_date}.md"
    echo "- **TypeScript/JavaScript Lines**: $ts_lines" >> "docs/project_report_${current_date}.md"
    echo "- **HTML/CSS Lines**: $html_lines" >> "docs/project_report_${current_date}.md"
    echo "- **Shell Script Lines**: $shell_lines" >> "docs/project_report_${current_date}.md"
    echo "- **Total Lines**: $total_lines" >> "docs/project_report_${current_date}.md"
    
    # Add recent activity section
    echo "" >> "docs/project_report_${current_date}.md"
    echo "## Recent Activity" >> "docs/project_report_${current_date}.md"
    echo "" >> "docs/project_report_${current_date}.md"
    
    # Get recent file changes (last 7 days)
    echo "### Recent File Changes (Last 7 Days)" >> "docs/project_report_${current_date}.md"
    echo "" >> "docs/project_report_${current_date}.md"
    echo '```' >> "docs/project_report_${current_date}.md"
    find . -type f -mtime -7 | grep -v "node_modules\|venv\|__pycache__\|.git" | sort >> "docs/project_report_${current_date}.md"
    echo '```' >> "docs/project_report_${current_date}.md"
    
    # Add next steps section
    echo "" >> "docs/project_report_${current_date}.md"
    echo "## Next Steps" >> "docs/project_report_${current_date}.md"
    echo "" >> "docs/project_report_${current_date}.md"
    
    # Extract in-progress features
    echo "### In-Progress Features to Complete" >> "docs/project_report_${current_date}.md"
    echo "" >> "docs/project_report_${current_date}.md"
    
    grep -A 100 "## Migration Tracking" MIGRATION.md | grep -B 100 "## Development Workflow" | grep "| In Progress" | while read -r line; do
        feature=$(echo "$line" | awk -F'|' '{print $2}' | xargs)
        api_status=$(echo "$line" | awk -F'|' '{print $3}' | xargs)
        test_status=$(echo "$line" | awk -F'|' '{print $4}' | xargs)
        ui_status=$(echo "$line" | awk -F'|' '{print $5}' | xargs)
        
        echo "- **$feature**" >> "docs/project_report_${current_date}.md"
        
        if [ "$api_status" == "In Progress" ]; then
            echo "  - Complete API development" >> "docs/project_report_${current_date}.md"
        fi
        
        if [ "$test_status" == "In Progress" ]; then
            echo "  - Complete test implementation" >> "docs/project_report_${current_date}.md"
        fi
        
        if [ "$ui_status" == "In Progress" ]; then
            echo "  - Complete UI development" >> "docs/project_report_${current_date}.md"
        fi
    done
    
    # Add recommendations section
    echo "" >> "docs/project_report_${current_date}.md"
    echo "### Recommendations" >> "docs/project_report_${current_date}.md"
    echo "" >> "docs/project_report_${current_date}.md"
    
    # Recommend starting new features if less than 3 in progress
    if [ $in_progress_features -lt 3 ] && [ $not_started_features -gt 0 ]; then
        echo "- Start development on new features (less than 3 features currently in progress)" >> "docs/project_report_${current_date}.md"
    fi
    
    # Recommend focusing on tests if API is ahead
    if [ $api_percent -gt $((test_percent + 20)) ]; then
        echo "- Focus on implementing tests (API development is significantly ahead)" >> "docs/project_report_${current_date}.md"
    fi
    
    # Recommend focusing on UI if tests are ahead
    if [ $test_percent -gt $((ui_percent + 20)) ]; then
        echo "- Focus on UI development (test implementation is significantly ahead)" >> "docs/project_report_${current_date}.md"
    fi
    
    # Always recommend completing in-progress features
    if [ $in_progress_features -gt 0 ]; then
        echo "- Complete the in-progress features before starting too many new ones" >> "docs/project_report_${current_date}.md"
    fi
    
    echo "Project report generated at docs/project_report_${current_date}.md"
}

# Parse command line arguments
if [ $# -eq 0 ]; then
    display_usage
    exit 1
fi

while [ $# -gt 0 ]; do
    case "$1" in
        --generate)
            generate_report
            shift
            ;;
        --help)
            display_usage
            exit 0
            ;;
        *)
            echo "Error: Unknown option $1"
            display_usage
            exit 1
            ;;
    esac
done 