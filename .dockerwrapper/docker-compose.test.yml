version: '3.8'

services:
  postgres:
    image: postgres:16
    container_name: regrigor-postgres
    environment:
      POSTGRES_USER: regrigor
      POSTGRES_PASSWORD: regrigor_password
      POSTGRES_DB: regrigor_db
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - "3330:5432"
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "regrigor"]
      interval: 5s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: regrigor-redis
    ports:
      - "3331:6379"
    volumes:
      - redisdata:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  test-web:
    image: python:3.11-slim
    container_name: regrigor-test-web
    environment:
      - DATABASE_URL=postgresql://regrigor:regrigor_password@localhost:3330/regrigor_db
      - FLASK_APP=test_app.py
      - FLASK_DEBUG=1
      - REDIS_URL=redis://localhost:3331/0
      - PORT=3334
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ..:/app
    working_dir: /app
    command: >
      bash -c "pip install flask psycopg2-binary && 
              python -m flask run --host 0.0.0.0 --port 3334"
    restart: unless-stopped
    network_mode: "host"

volumes:
  pgdata:
  redisdata: 