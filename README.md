# RegressionRigor

A comprehensive regression testing and monitoring platform.

## Features

- User Management and Authentication
- Rate Limiting
- Database Integration
- API and Web Interface

## Development

### Prerequisites

- Python 3.11+
- Docker and Docker Compose
- PostgreSQL
- Redis

### Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   pip install -e .
   ```
3. Set up environment variables
4. Run with Docker Compose:
   ```bash
   docker-compose up -d
   ```

## License

MIT