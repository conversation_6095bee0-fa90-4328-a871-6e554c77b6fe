{% extends "base.html" %}

{% block title %}Edit {{ testcase.name }} | Test Cases{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Edit Test Case</h1>
        <a href="{{ url_for('view_testcase', id=testcase.id) }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Test Case
        </a>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-body">
            <form method="POST" action="{{ url_for('edit_testcase', id=testcase.id) }}" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control {% if form.name.errors %}is-invalid{% endif %}" id="name" name="name" value="{{ form.name.data or testcase.name }}" required>
                    {% if form.name.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.name.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="form-text">Enter a descriptive name for the test case.</div>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                    <textarea class="form-control {% if form.description.errors %}is-invalid{% endif %}" id="description" name="description" rows="3" required>{{ form.description.data or testcase.description }}</textarea>
                    {% if form.description.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.description.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="form-text">Provide a detailed description of what this test case is designed to verify.</div>
                </div>

                <div class="mb-3">
                    <label for="campaign_id" class="form-label">Campaign <span class="text-danger">*</span></label>
                    <select class="form-select {% if form.campaign_id.errors %}is-invalid{% endif %}" id="campaign_id" name="campaign_id" required>
                        <option value="" disabled>Select a campaign</option>
                        {% for campaign in campaigns %}
                            <option value="{{ campaign.id }}" {% if form.campaign_id.data == campaign.id|string or (not form.campaign_id.data and testcase.campaign_id == campaign.id) %}selected{% endif %}>{{ campaign.name }}</option>
                        {% endfor %}
                    </select>
                    {% if form.campaign_id.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.campaign_id.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="form-text">Select the campaign this test case belongs to.</div>
                </div>

                <div class="mb-3">
                    <label for="expected_result" class="form-label">Expected Result <span class="text-danger">*</span></label>
                    <textarea class="form-control {% if form.expected_result.errors %}is-invalid{% endif %}" id="expected_result" name="expected_result" rows="3" required>{{ form.expected_result.data or testcase.expected_result }}</textarea>
                    {% if form.expected_result.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.expected_result.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="form-text">Describe what the expected outcome of this test case should be.</div>
                </div>

                <div class="mb-3">
                    <label for="actual_result" class="form-label">Actual Result</label>
                    <textarea class="form-control {% if form.actual_result.errors %}is-invalid{% endif %}" id="actual_result" name="actual_result" rows="3">{{ form.actual_result.data or testcase.actual_result }}</textarea>
                    {% if form.actual_result.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.actual_result.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="form-text">Record the actual outcome of the test case execution.</div>
                </div>

                <div class="mb-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select {% if form.status.errors %}is-invalid{% endif %}" id="status" name="status">
                        <option value="pending" {% if form.status.data == 'pending' or (not form.status.data and testcase.status == 'pending') %}selected{% endif %}>Pending</option>
                        <option value="running" {% if form.status.data == 'running' or (not form.status.data and testcase.status == 'running') %}selected{% endif %}>Running</option>
                        <option value="passed" {% if form.status.data == 'passed' or (not form.status.data and testcase.status == 'passed') %}selected{% endif %}>Passed</option>
                        <option value="failed" {% if form.status.data == 'failed' or (not form.status.data and testcase.status == 'failed') %}selected{% endif %}>Failed</option>
                    </select>
                    {% if form.status.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.status.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="form-text">Select the current status of the test case.</div>
                </div>

                <div class="mb-3">
                    <label for="mitre_techniques" class="form-label">MITRE ATT&CK Techniques</label>
                    <select class="form-select {% if form.mitre_techniques.errors %}is-invalid{% endif %}" id="mitre_techniques" name="mitre_techniques" multiple>
                        {% for technique in mitre_techniques %}
                            <option value="{{ technique.technique_id }}" 
                                {% if form.mitre_techniques.data and technique.technique_id in form.mitre_techniques.data %}
                                    selected
                                {% elif not form.mitre_techniques.data and testcase.mitre_techniques and technique.technique_id in testcase.mitre_techniques %}
                                    selected
                                {% endif %}>
                                {{ technique.technique_id }} - {{ technique.name }}
                            </option>
                        {% endfor %}
                    </select>
                    {% if form.mitre_techniques.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.mitre_techniques.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="form-text">Select the MITRE ATT&CK techniques that apply to this test case. Hold Ctrl/Cmd to select multiple.</div>
                </div>

                <div class="mb-3">
                    <label for="attachments" class="form-label">Add Attachments</label>
                    <input type="file" class="form-control {% if form.attachments.errors %}is-invalid{% endif %}" id="attachments" name="attachments" multiple>
                    {% if form.attachments.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.attachments.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="form-text">Upload additional files for this test case (scripts, screenshots, etc.).</div>
                </div>

                {% if testcase.attachments %}
                    <div class="mb-4">
                        <label class="form-label">Current Attachments</label>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead>
                                    <tr>
                                        <th>Filename</th>
                                        <th>Size</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for attachment in testcase.attachments %}
                                        <tr>
                                            <td>{{ attachment.filename }}</td>
                                            <td>{{ attachment.size|filesizeformat }}</td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="{{ url_for('download_attachment', id=attachment.id) }}" class="btn btn-outline-primary">
                                                        <i class="bi bi-download"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteAttachmentModal{{ attachment.id }}">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                {% endif %}

                <div class="mb-4">
                    <label class="form-label">Test Steps</label>
                    <div id="steps-container">
                        {% if form.steps.data %}
                            {% for step in form.steps.data %}
                                <div class="card mb-2 step-card">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-5 mb-2">
                                                <label class="form-label">Step Title</label>
                                                <input type="text" class="form-control" name="steps[{{ loop.index0 }}][title]" value="{{ step.title }}" required>
                                            </div>
                                            <div class="col-md-5 mb-2">
                                                <label class="form-label">Status</label>
                                                <select class="form-select" name="steps[{{ loop.index0 }}][status]">
                                                    <option value="pending" {% if step.status == 'pending' %}selected{% endif %}>Pending</option>
                                                    <option value="in_progress" {% if step.status == 'in_progress' %}selected{% endif %}>In Progress</option>
                                                    <option value="completed" {% if step.status == 'completed' %}selected{% endif %}>Completed</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2 mb-2 d-flex align-items-end">
                                                <button type="button" class="btn btn-outline-danger remove-step w-100">Remove</button>
                                            </div>
                                            <div class="col-12">
                                                <label class="form-label">Step Description</label>
                                                <textarea class="form-control" name="steps[{{ loop.index0 }}][description]" rows="2" required>{{ step.description }}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        {% elif testcase.steps %}
                            {% for step in testcase.steps %}
                                <div class="card mb-2 step-card">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-5 mb-2">
                                                <label class="form-label">Step Title</label>
                                                <input type="text" class="form-control" name="steps[{{ loop.index0 }}][title]" value="{{ step.title }}" required>
                                            </div>
                                            <div class="col-md-5 mb-2">
                                                <label class="form-label">Status</label>
                                                <select class="form-select" name="steps[{{ loop.index0 }}][status]">
                                                    <option value="pending" {% if step.status == 'pending' %}selected{% endif %}>Pending</option>
                                                    <option value="in_progress" {% if step.status == 'in_progress' %}selected{% endif %}>In Progress</option>
                                                    <option value="completed" {% if step.status == 'completed' %}selected{% endif %}>Completed</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2 mb-2 d-flex align-items-end">
                                                <button type="button" class="btn btn-outline-danger remove-step w-100">Remove</button>
                                            </div>
                                            <div class="col-12">
                                                <label class="form-label">Step Description</label>
                                                <textarea class="form-control" name="steps[{{ loop.index0 }}][description]" rows="2" required>{{ step.description }}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        {% endif %}
                    </div>
                    <button type="button" id="add-step" class="btn btn-outline-primary mt-2">
                        <i class="bi bi-plus-circle"></i> Add Step
                    </button>
                    <div class="form-text">Add steps to guide testers through the test case execution.</div>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('view_testcase', id=testcase.id) }}" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn btn-primary">Update Test Case</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% for attachment in testcase.attachments %}
    <!-- Delete Attachment Modal -->
    <div class="modal fade" id="deleteAttachmentModal{{ attachment.id }}" tabindex="-1" aria-labelledby="deleteAttachmentModalLabel{{ attachment.id }}" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteAttachmentModalLabel{{ attachment.id }}">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    Are you sure you want to delete the attachment <strong>{{ attachment.filename }}</strong>? This action cannot be undone.
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form action="{{ url_for('delete_attachment', id=attachment.id, testcase_id=testcase.id) }}" method="POST" class="d-inline">
                        <button type="submit" class="btn btn-danger">Delete</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endfor %}
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
<style>
    .step-card {
        border-left: 4px solid #6c757d;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const stepsContainer = document.getElementById('steps-container');
        const addStepButton = document.getElementById('add-step');
        
        // Add step button click handler
        addStepButton.addEventListener('click', function() {
            const stepIndex = document.querySelectorAll('.step-card').length;
            const stepCard = document.createElement('div');
            stepCard.className = 'card mb-2 step-card';
            stepCard.innerHTML = `
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-5 mb-2">
                            <label class="form-label">Step Title</label>
                            <input type="text" class="form-control" name="steps[${stepIndex}][title]" required>
                        </div>
                        <div class="col-md-5 mb-2">
                            <label class="form-label">Status</label>
                            <select class="form-select" name="steps[${stepIndex}][status]">
                                <option value="pending" selected>Pending</option>
                                <option value="in_progress">In Progress</option>
                                <option value="completed">Completed</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-2 d-flex align-items-end">
                            <button type="button" class="btn btn-outline-danger remove-step w-100">Remove</button>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Step Description</label>
                            <textarea class="form-control" name="steps[${stepIndex}][description]" rows="2" required></textarea>
                        </div>
                    </div>
                </div>
            `;
            stepsContainer.appendChild(stepCard);
            
            // Add event listener to the new remove button
            stepCard.querySelector('.remove-step').addEventListener('click', function() {
                stepCard.remove();
                // Reindex the remaining steps
                reindexSteps();
            });
        });
        
        // Add event listeners to existing remove buttons
        document.querySelectorAll('.remove-step').forEach(button => {
            button.addEventListener('click', function() {
                this.closest('.step-card').remove();
                // Reindex the remaining steps
                reindexSteps();
            });
        });
        
        // Function to reindex steps after removal
        function reindexSteps() {
            const stepCards = document.querySelectorAll('.step-card');
            stepCards.forEach((card, index) => {
                card.querySelectorAll('input, select, textarea').forEach(input => {
                    const name = input.getAttribute('name');
                    if (name) {
                        const newName = name.replace(/steps\[\d+\]/, `steps[${index}]`);
                        input.setAttribute('name', newName);
                    }
                });
            });
        }
    });
</script>
{% endblock %} 