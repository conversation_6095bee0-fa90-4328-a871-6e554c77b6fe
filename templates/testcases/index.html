{% extends "base.html" %}

{% block title %}Test Cases | Security Dashboard{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Test Cases</h1>
        <a href="{{ url_for('new_testcase') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> New Test Case
        </a>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    {% if testcases %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Campaign</th>
                        <th>Status</th>
                        <th>Expected Result</th>
                        <th>Actual Result</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for testcase in testcases %}
                        <tr>
                            <td>{{ testcase.name }}</td>
                            <td>
                                <a href="{{ url_for('view_campaign', id=testcase.campaign_id) }}">
                                    {{ testcase.campaign.name }}
                                </a>
                            </td>
                            <td>
                                {% if testcase.status == 'pending' %}
                                    <span class="badge bg-secondary">Pending</span>
                                {% elif testcase.status == 'running' %}
                                    <span class="badge bg-primary">Running</span>
                                {% elif testcase.status == 'passed' %}
                                    <span class="badge bg-success">Passed</span>
                                {% elif testcase.status == 'failed' %}
                                    <span class="badge bg-danger">Failed</span>
                                {% else %}
                                    <span class="badge bg-dark">{{ testcase.status }}</span>
                                {% endif %}
                            </td>
                            <td>{{ testcase.expected_result|truncate(30) }}</td>
                            <td>{{ testcase.actual_result|truncate(30) if testcase.actual_result else 'N/A' }}</td>
                            <td>{{ testcase.created_time.strftime('%Y-%m-%d') }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('view_testcase', id=testcase.id) }}" class="btn btn-sm btn-outline-primary">View</a>
                                    <a href="{{ url_for('edit_testcase', id=testcase.id) }}" class="btn btn-sm btn-outline-secondary">Edit</a>
                                    <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ testcase.id }}">Delete</button>
                                </div>
                            </td>
                        </tr>

                        <!-- Delete Modal -->
                        <div class="modal fade" id="deleteModal{{ testcase.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ testcase.id }}" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="deleteModalLabel{{ testcase.id }}">Confirm Delete</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        Are you sure you want to delete the test case <strong>{{ testcase.name }}</strong>? This action cannot be undone.
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                        <form action="{{ url_for('delete_testcase', id=testcase.id) }}" method="POST" class="d-inline">
                                            <button type="submit" class="btn btn-danger">Delete</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="alert alert-info">
            <p>No test cases found. <a href="{{ url_for('new_testcase') }}">Create your first test case</a>.</p>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
{% endblock %} 