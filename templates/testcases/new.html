{% extends "base.html" %}

{% block title %}New Test Case | Security Dashboard{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>New Test Case</h1>
        <a href="{{ url_for('testcases') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Test Cases
        </a>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-body">
            <form method="POST" action="{{ url_for('new_testcase') }}" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control {% if form.name.errors %}is-invalid{% endif %}" id="name" name="name" value="{{ form.name.data or '' }}" required>
                    {% if form.name.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.name.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="form-text">Enter a descriptive name for the test case.</div>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                    <textarea class="form-control {% if form.description.errors %}is-invalid{% endif %}" id="description" name="description" rows="3" required>{{ form.description.data or '' }}</textarea>
                    {% if form.description.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.description.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="form-text">Provide a detailed description of what this test case is designed to verify.</div>
                </div>

                <div class="mb-3">
                    <label for="campaign_id" class="form-label">Campaign <span class="text-danger">*</span></label>
                    <select class="form-select {% if form.campaign_id.errors %}is-invalid{% endif %}" id="campaign_id" name="campaign_id" required>
                        <option value="" selected disabled>Select a campaign</option>
                        {% for campaign in campaigns %}
                            <option value="{{ campaign.id }}" {% if form.campaign_id.data == campaign.id|string %}selected{% endif %}>{{ campaign.name }}</option>
                        {% endfor %}
                    </select>
                    {% if form.campaign_id.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.campaign_id.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="form-text">Select the campaign this test case belongs to.</div>
                </div>

                <div class="mb-3">
                    <label for="expected_result" class="form-label">Expected Result <span class="text-danger">*</span></label>
                    <textarea class="form-control {% if form.expected_result.errors %}is-invalid{% endif %}" id="expected_result" name="expected_result" rows="3" required>{{ form.expected_result.data or '' }}</textarea>
                    {% if form.expected_result.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.expected_result.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="form-text">Describe what the expected outcome of this test case should be.</div>
                </div>

                <div class="mb-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select {% if form.status.errors %}is-invalid{% endif %}" id="status" name="status">
                        <option value="pending" {% if form.status.data == 'pending' or not form.status.data %}selected{% endif %}>Pending</option>
                        <option value="running" {% if form.status.data == 'running' %}selected{% endif %}>Running</option>
                        <option value="passed" {% if form.status.data == 'passed' %}selected{% endif %}>Passed</option>
                        <option value="failed" {% if form.status.data == 'failed' %}selected{% endif %}>Failed</option>
                    </select>
                    {% if form.status.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.status.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="form-text">Select the initial status of the test case.</div>
                </div>

                <div class="mb-3">
                    <label for="mitre_techniques" class="form-label">MITRE ATT&CK Techniques</label>
                    <select class="form-select {% if form.mitre_techniques.errors %}is-invalid{% endif %}" id="mitre_techniques" name="mitre_techniques" multiple>
                        {% for technique in mitre_techniques %}
                            <option value="{{ technique.technique_id }}" {% if form.mitre_techniques.data and technique.technique_id in form.mitre_techniques.data %}selected{% endif %}>{{ technique.technique_id }} - {{ technique.name }}</option>
                        {% endfor %}
                    </select>
                    {% if form.mitre_techniques.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.mitre_techniques.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="form-text">Select the MITRE ATT&CK techniques that apply to this test case. Hold Ctrl/Cmd to select multiple.</div>
                </div>

                <div class="mb-3">
                    <label for="attachments" class="form-label">Attachments</label>
                    <input type="file" class="form-control {% if form.attachments.errors %}is-invalid{% endif %}" id="attachments" name="attachments" multiple>
                    {% if form.attachments.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.attachments.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="form-text">Upload any relevant files for this test case (scripts, screenshots, etc.).</div>
                </div>

                <div class="mb-4">
                    <label class="form-label">Test Steps</label>
                    <div id="steps-container">
                        {% if form.steps.data %}
                            {% for step in form.steps.data %}
                                <div class="card mb-2 step-card">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-5 mb-2">
                                                <label class="form-label">Step Title</label>
                                                <input type="text" class="form-control" name="steps[{{ loop.index0 }}][title]" value="{{ step.title }}" required>
                                            </div>
                                            <div class="col-md-5 mb-2">
                                                <label class="form-label">Status</label>
                                                <select class="form-select" name="steps[{{ loop.index0 }}][status]">
                                                    <option value="pending" {% if step.status == 'pending' or not step.status %}selected{% endif %}>Pending</option>
                                                    <option value="in_progress" {% if step.status == 'in_progress' %}selected{% endif %}>In Progress</option>
                                                    <option value="completed" {% if step.status == 'completed' %}selected{% endif %}>Completed</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2 mb-2 d-flex align-items-end">
                                                <button type="button" class="btn btn-outline-danger remove-step w-100">Remove</button>
                                            </div>
                                            <div class="col-12">
                                                <label class="form-label">Step Description</label>
                                                <textarea class="form-control" name="steps[{{ loop.index0 }}][description]" rows="2" required>{{ step.description }}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        {% endif %}
                    </div>
                    <button type="button" id="add-step" class="btn btn-outline-primary mt-2">
                        <i class="bi bi-plus-circle"></i> Add Step
                    </button>
                    <div class="form-text">Add steps to guide testers through the test case execution.</div>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('testcases') }}" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn btn-primary">Create Test Case</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
<style>
    .step-card {
        border-left: 4px solid #6c757d;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const stepsContainer = document.getElementById('steps-container');
        const addStepButton = document.getElementById('add-step');
        
        // Add step button click handler
        addStepButton.addEventListener('click', function() {
            const stepIndex = document.querySelectorAll('.step-card').length;
            const stepCard = document.createElement('div');
            stepCard.className = 'card mb-2 step-card';
            stepCard.innerHTML = `
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-5 mb-2">
                            <label class="form-label">Step Title</label>
                            <input type="text" class="form-control" name="steps[${stepIndex}][title]" required>
                        </div>
                        <div class="col-md-5 mb-2">
                            <label class="form-label">Status</label>
                            <select class="form-select" name="steps[${stepIndex}][status]">
                                <option value="pending" selected>Pending</option>
                                <option value="in_progress">In Progress</option>
                                <option value="completed">Completed</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-2 d-flex align-items-end">
                            <button type="button" class="btn btn-outline-danger remove-step w-100">Remove</button>
                        </div>
                        <div class="col-12">
                            <label class="form-label">Step Description</label>
                            <textarea class="form-control" name="steps[${stepIndex}][description]" rows="2" required></textarea>
                        </div>
                    </div>
                </div>
            `;
            stepsContainer.appendChild(stepCard);
            
            // Add event listener to the new remove button
            stepCard.querySelector('.remove-step').addEventListener('click', function() {
                stepCard.remove();
                // Reindex the remaining steps
                reindexSteps();
            });
        });
        
        // Add event listeners to existing remove buttons
        document.querySelectorAll('.remove-step').forEach(button => {
            button.addEventListener('click', function() {
                this.closest('.step-card').remove();
                // Reindex the remaining steps
                reindexSteps();
            });
        });
        
        // Function to reindex steps after removal
        function reindexSteps() {
            const stepCards = document.querySelectorAll('.step-card');
            stepCards.forEach((card, index) => {
                card.querySelectorAll('input, select, textarea').forEach(input => {
                    const name = input.getAttribute('name');
                    if (name) {
                        const newName = name.replace(/steps\[\d+\]/, `steps[${index}]`);
                        input.setAttribute('name', newName);
                    }
                });
            });
        }
    });
</script>
{% endblock %} 