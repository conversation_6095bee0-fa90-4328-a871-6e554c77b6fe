{% extends "base.html" %}

{% block title %}{{ testcase.name }} | Test Cases{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ testcase.name }}</h1>
        <div>
            <a href="{{ url_for('edit_testcase', id=testcase.id) }}" class="btn btn-secondary">
                <i class="bi bi-pencil"></i> Edit
            </a>
            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                <i class="bi bi-trash"></i> Delete
            </button>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Test Case Details</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>Description</h6>
                        <p>{{ testcase.description }}</p>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6>Status</h6>
                            {% if testcase.status == 'pending' %}
                                <span class="badge bg-secondary">Pending</span>
                            {% elif testcase.status == 'running' %}
                                <span class="badge bg-primary">Running</span>
                            {% elif testcase.status == 'passed' %}
                                <span class="badge bg-success">Passed</span>
                            {% elif testcase.status == 'failed' %}
                                <span class="badge bg-danger">Failed</span>
                            {% else %}
                                <span class="badge bg-dark">{{ testcase.status }}</span>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <h6>Campaign</h6>
                            <a href="{{ url_for('view_campaign', id=testcase.campaign_id) }}">
                                {{ testcase.campaign.name }}
                            </a>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6>Created</h6>
                            <p>{{ testcase.created_time.strftime('%Y-%m-%d %H:%M') }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>Last Updated</h6>
                            <p>{{ testcase.updated_time.strftime('%Y-%m-%d %H:%M') if testcase.updated_time else 'N/A' }}</p>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6>Expected Result</h6>
                        <div class="p-3 bg-light rounded">
                            <pre class="mb-0">{{ testcase.expected_result }}</pre>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6>Actual Result</h6>
                        <div class="p-3 bg-light rounded">
                            <pre class="mb-0">{{ testcase.actual_result if testcase.actual_result else 'No results recorded yet' }}</pre>
                        </div>
                    </div>

                    {% if testcase.mitre_techniques %}
                        <div class="mb-3">
                            <h6>MITRE ATT&CK Techniques</h6>
                            <div>
                                {% for technique in testcase.mitre_techniques %}
                                    <span class="badge bg-info text-dark me-1 mb-1">{{ technique }}</span>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>

            {% if testcase.steps %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Test Steps</h5>
                    </div>
                    <div class="card-body">
                        <ol class="list-group list-group-numbered">
                            {% for step in testcase.steps %}
                                <li class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold">{{ step.title }}</div>
                                        {{ step.description }}
                                    </div>
                                    {% if step.status %}
                                        {% if step.status == 'completed' %}
                                            <span class="badge bg-success rounded-pill">Completed</span>
                                        {% elif step.status == 'in_progress' %}
                                            <span class="badge bg-primary rounded-pill">In Progress</span>
                                        {% else %}
                                            <span class="badge bg-secondary rounded-pill">Pending</span>
                                        {% endif %}
                                    {% endif %}
                                </li>
                            {% endfor %}
                        </ol>
                    </div>
                </div>
            {% endif %}
        </div>

        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if testcase.status == 'pending' %}
                            <form action="{{ url_for('run_testcase', id=testcase.id) }}" method="POST">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="bi bi-play-fill"></i> Run Test Case
                                </button>
                            </form>
                        {% elif testcase.status == 'running' %}
                            <form action="{{ url_for('stop_testcase', id=testcase.id) }}" method="POST">
                                <button type="submit" class="btn btn-warning w-100">
                                    <i class="bi bi-stop-fill"></i> Stop Test Case
                                </button>
                            </form>
                        {% else %}
                            <form action="{{ url_for('rerun_testcase', id=testcase.id) }}" method="POST">
                                <button type="submit" class="btn btn-outline-primary w-100">
                                    <i class="bi bi-arrow-repeat"></i> Re-run Test Case
                                </button>
                            </form>
                        {% endif %}
                        
                        <a href="{{ url_for('testcases') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Test Cases
                        </a>
                    </div>
                </div>
            </div>

            {% if testcase.attachments %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Attachments</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-group">
                            {% for attachment in testcase.attachments %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-file-earmark"></i>
                                        {{ attachment.filename }}
                                    </div>
                                    <a href="{{ url_for('download_attachment', id=attachment.id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-download"></i>
                                    </a>
                                </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete the test case <strong>{{ testcase.name }}</strong>? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{{ url_for('delete_testcase', id=testcase.id) }}" method="POST" class="d-inline">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
{% endblock %} 