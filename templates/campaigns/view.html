{% extends "base.html" %}

{% block title %}{{ campaign.name }} | Security Dashboard{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ campaign.name }}</h1>
        <div>
            <a href="{{ url_for('edit_campaign', id=campaign.id) }}" class="btn btn-primary">
                <i class="bi bi-pencil"></i> Edit
            </a>
            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                <i class="bi bi-trash"></i> Delete
            </button>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h3>Campaign Details</h3>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h5>Description</h5>
                        <p>{{ campaign.description or 'No description provided.' }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <h5>Status</h5>
                        {% if campaign.status == 'active' %}
                            <span class="badge bg-success">Active</span>
                        {% elif campaign.status == 'inactive' %}
                            <span class="badge bg-secondary">Inactive</span>
                        {% elif campaign.status == 'completed' %}
                            <span class="badge bg-primary">Completed</span>
                        {% else %}
                            <span class="badge bg-dark">{{ campaign.status }}</span>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Created</h5>
                            <p>{{ campaign.created_time.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>Last Updated</h5>
                            <p>{{ campaign.updated_time.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h3>Associated Assessments</h3>
                </div>
                <div class="card-body">
                    {% if campaign.assessments %}
                        <ul class="list-group">
                            {% for assessment in campaign.assessments %}
                                <li class="list-group-item">
                                    <a href="{{ url_for('view_assessment', id=assessment.id) }}">{{ assessment.name }}</a>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p>No assessments associated with this campaign.</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3>Test Cases</h3>
                    <a href="{{ url_for('new_testcase') }}" class="btn btn-sm btn-primary">
                        <i class="bi bi-plus-circle"></i> Add Test Case
                    </a>
                </div>
                <div class="card-body">
                    {% if campaign.test_cases %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Status</th>
                                        <th>Expected Result</th>
                                        <th>Actual Result</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for testcase in campaign.test_cases %}
                                        <tr>
                                            <td>{{ testcase.name }}</td>
                                            <td>
                                                {% if testcase.status == 'pending' %}
                                                    <span class="badge bg-secondary">Pending</span>
                                                {% elif testcase.status == 'running' %}
                                                    <span class="badge bg-primary">Running</span>
                                                {% elif testcase.status == 'passed' %}
                                                    <span class="badge bg-success">Passed</span>
                                                {% elif testcase.status == 'failed' %}
                                                    <span class="badge bg-danger">Failed</span>
                                                {% else %}
                                                    <span class="badge bg-dark">{{ testcase.status }}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ testcase.expected_result|truncate(30) }}</td>
                                            <td>{{ testcase.actual_result|truncate(30) if testcase.actual_result else 'N/A' }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ url_for('view_testcase', id=testcase.id) }}" class="btn btn-sm btn-outline-primary">View</a>
                                                    <a href="{{ url_for('edit_testcase', id=testcase.id) }}" class="btn btn-sm btn-outline-secondary">Edit</a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p>No test cases found for this campaign. <a href="{{ url_for('new_testcase') }}">Create your first test case</a>.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete the campaign <strong>{{ campaign.name }}</strong>? This will also delete all associated test cases. This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{{ url_for('delete_campaign', id=campaign.id) }}" method="POST" class="d-inline">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
{% endblock %} 