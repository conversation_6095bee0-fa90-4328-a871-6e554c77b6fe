{% extends "base.html" %}

{% block title %}Campaigns | Security Dashboard{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Security Campaigns</h1>
        <a href="{{ url_for('new_campaign') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> New Campaign
        </a>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    {% if campaigns %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Description</th>
                        <th>Status</th>
                        <th>Test Cases</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for campaign in campaigns %}
                        <tr>
                            <td>{{ campaign.name }}</td>
                            <td>{{ campaign.description|truncate(50) if campaign.description else 'No description' }}</td>
                            <td>
                                {% if campaign.status == 'active' %}
                                    <span class="badge bg-success">Active</span>
                                {% elif campaign.status == 'inactive' %}
                                    <span class="badge bg-secondary">Inactive</span>
                                {% elif campaign.status == 'completed' %}
                                    <span class="badge bg-primary">Completed</span>
                                {% else %}
                                    <span class="badge bg-dark">{{ campaign.status }}</span>
                                {% endif %}
                            </td>
                            <td>{{ campaign.test_cases|length }}</td>
                            <td>{{ campaign.created_time.strftime('%Y-%m-%d') }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('view_campaign', id=campaign.id) }}" class="btn btn-sm btn-outline-primary">View</a>
                                    <a href="{{ url_for('edit_campaign', id=campaign.id) }}" class="btn btn-sm btn-outline-secondary">Edit</a>
                                    <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ campaign.id }}">Delete</button>
                                </div>
                            </td>
                        </tr>

                        <!-- Delete Modal -->
                        <div class="modal fade" id="deleteModal{{ campaign.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ campaign.id }}" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="deleteModalLabel{{ campaign.id }}">Confirm Delete</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        Are you sure you want to delete the campaign <strong>{{ campaign.name }}</strong>? This will also delete all associated test cases. This action cannot be undone.
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                        <form action="{{ url_for('delete_campaign', id=campaign.id) }}" method="POST" class="d-inline">
                                            <button type="submit" class="btn btn-danger">Delete</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="alert alert-info">
            <p>No campaigns found. <a href="{{ url_for('new_campaign') }}">Create your first campaign</a>.</p>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
{% endblock %} 