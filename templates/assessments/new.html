{% extends "base.html" %}

{% block title %}New Assessment | Security Dashboard{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">Create New Assessment</h2>
                </div>
                <div class="card-body">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <form method="POST" action="{{ url_for('new_assessment') }}">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.name.label(class="form-label") }}
                            {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows=4) }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.description.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Provide a detailed description of the assessment scope and objectives.</div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.target_system.label(class="form-label") }}
                            {{ form.target_system(class="form-control" + (" is-invalid" if form.target_system.errors else "")) }}
                            {% if form.target_system.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.target_system.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Specify the system, application, or infrastructure being assessed.</div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.assessment_type.label(class="form-label") }}
                                {{ form.assessment_type(class="form-select" + (" is-invalid" if form.assessment_type.errors else "")) }}
                                {% if form.assessment_type.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.assessment_type.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                {{ form.status.label(class="form-label") }}
                                {{ form.status(class="form-select" + (" is-invalid" if form.status.errors else "")) }}
                                {% if form.status.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.status.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.start_date.label(class="form-label") }}
                                {{ form.start_date(class="form-control" + (" is-invalid" if form.start_date.errors else ""), type="date") }}
                                {% if form.start_date.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.start_date.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                {{ form.end_date.label(class="form-label") }}
                                {{ form.end_date(class="form-control" + (" is-invalid" if form.end_date.errors else ""), type="date") }}
                                {% if form.end_date.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.end_date.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">Optional. Leave blank if end date is not yet determined.</div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ url_for('assessments') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Create Assessment</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 