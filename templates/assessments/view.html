{% extends "base.html" %}

{% block title %}{{ assessment.name }} | Security Dashboard{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ assessment.name }}</h1>
        <div>
            <a href="{{ url_for('edit_assessment', id=assessment.id) }}" class="btn btn-primary">
                <i class="bi bi-pencil"></i> Edit
            </a>
            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                <i class="bi bi-trash"></i> Delete
            </button>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h3>Assessment Details</h3>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h5>Description</h5>
                        <p>{{ assessment.description or 'No description provided.' }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <h5>Target System</h5>
                        <p>{{ assessment.target_system }}</p>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h5>Assessment Type</h5>
                            {% if assessment.assessment_type == 'vulnerability' %}
                                <span class="badge bg-info">Vulnerability Assessment</span>
                            {% elif assessment.assessment_type == 'penetration' %}
                                <span class="badge bg-warning">Penetration Test</span>
                            {% elif assessment.assessment_type == 'code_review' %}
                                <span class="badge bg-secondary">Code Review</span>
                            {% elif assessment.assessment_type == 'compliance' %}
                                <span class="badge bg-primary">Compliance Audit</span>
                            {% else %}
                                <span class="badge bg-dark">{{ assessment.assessment_type }}</span>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6">
                            <h5>Status</h5>
                            {% if assessment.status == 'planned' %}
                                <span class="badge bg-secondary">Planned</span>
                            {% elif assessment.status == 'in-progress' %}
                                <span class="badge bg-primary">In Progress</span>
                            {% elif assessment.status == 'completed' %}
                                <span class="badge bg-success">Completed</span>
                            {% elif assessment.status == 'cancelled' %}
                                <span class="badge bg-danger">Cancelled</span>
                            {% else %}
                                <span class="badge bg-dark">{{ assessment.status }}</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h5>Start Date</h5>
                            <p>{{ assessment.start_date.strftime('%Y-%m-%d') }}</p>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>End Date</h5>
                            <p>{{ assessment.end_date.strftime('%Y-%m-%d') if assessment.end_date else 'Not specified' }}</p>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h5>Created By</h5>
                        <p>{{ assessment.creator.username }}</p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Created</h5>
                            <p>{{ assessment.created_time.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>Last Updated</h5>
                            <p>{{ assessment.updated_time.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h3>Associated Campaigns</h3>
                </div>
                <div class="card-body">
                    {% if assessment.campaigns %}
                        <ul class="list-group">
                            {% for campaign in assessment.campaigns %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <a href="{{ url_for('view_campaign', id=campaign.id) }}">{{ campaign.name }}</a>
                                    <span class="badge bg-primary rounded-pill">{{ campaign.test_cases|length }} tests</span>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p>No campaigns associated with this assessment.</p>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('new_campaign') }}" class="btn btn-sm btn-outline-primary">Add Campaign</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete the assessment <strong>{{ assessment.name }}</strong>? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{{ url_for('delete_assessment', id=assessment.id) }}" method="POST" class="d-inline">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
{% endblock %} 