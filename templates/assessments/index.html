{% extends "base.html" %}

{% block title %}Assessments | Security Dashboard{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Security Assessments</h1>
        <a href="{{ url_for('new_assessment') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> New Assessment
        </a>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    {% if assessments %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Target System</th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>Start Date</th>
                        <th>End Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for assessment in assessments %}
                        <tr>
                            <td>{{ assessment.name }}</td>
                            <td>{{ assessment.target_system }}</td>
                            <td>
                                {% if assessment.assessment_type == 'vulnerability' %}
                                    <span class="badge bg-info">Vulnerability Assessment</span>
                                {% elif assessment.assessment_type == 'penetration' %}
                                    <span class="badge bg-warning">Penetration Test</span>
                                {% elif assessment.assessment_type == 'code_review' %}
                                    <span class="badge bg-secondary">Code Review</span>
                                {% elif assessment.assessment_type == 'compliance' %}
                                    <span class="badge bg-primary">Compliance Audit</span>
                                {% else %}
                                    <span class="badge bg-dark">{{ assessment.assessment_type }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if assessment.status == 'planned' %}
                                    <span class="badge bg-secondary">Planned</span>
                                {% elif assessment.status == 'in-progress' %}
                                    <span class="badge bg-primary">In Progress</span>
                                {% elif assessment.status == 'completed' %}
                                    <span class="badge bg-success">Completed</span>
                                {% elif assessment.status == 'cancelled' %}
                                    <span class="badge bg-danger">Cancelled</span>
                                {% else %}
                                    <span class="badge bg-dark">{{ assessment.status }}</span>
                                {% endif %}
                            </td>
                            <td>{{ assessment.start_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ assessment.end_date.strftime('%Y-%m-%d') if assessment.end_date else 'N/A' }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('view_assessment', id=assessment.id) }}" class="btn btn-sm btn-outline-primary">View</a>
                                    <a href="{{ url_for('edit_assessment', id=assessment.id) }}" class="btn btn-sm btn-outline-secondary">Edit</a>
                                    <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ assessment.id }}">Delete</button>
                                </div>
                            </td>
                        </tr>

                        <!-- Delete Modal -->
                        <div class="modal fade" id="deleteModal{{ assessment.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ assessment.id }}" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="deleteModalLabel{{ assessment.id }}">Confirm Delete</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        Are you sure you want to delete the assessment <strong>{{ assessment.name }}</strong>? This action cannot be undone.
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                        <form action="{{ url_for('delete_assessment', id=assessment.id) }}" method="POST" class="d-inline">
                                            <button type="submit" class="btn btn-danger">Delete</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="alert alert-info">
            <p>No assessments found. <a href="{{ url_for('new_assessment') }}">Create your first assessment</a>.</p>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
{% endblock %} 