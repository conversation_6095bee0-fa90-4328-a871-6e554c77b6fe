{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .documentation-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        display: flex;
        flex-wrap: wrap;
    }
    
    .documentation-sidebar {
        width: 250px;
        position: sticky;
        top: 20px;
        align-self: flex-start;
        margin-right: 30px;
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 20px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        max-height: calc(100vh - 40px);
        overflow-y: auto;
    }
    
    .documentation-content {
        flex: 1;
        min-width: 0;
        background-color: #fff;
        padding: 30px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    
    .documentation-content h1 {
        margin-top: 0;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }
    
    .documentation-content h2 {
        margin-top: 30px;
        padding-bottom: 5px;
        border-bottom: 1px solid #eee;
        scroll-margin-top: 20px;
    }
    
    .documentation-content h3 {
        margin-top: 25px;
        scroll-margin-top: 20px;
    }
    
    .documentation-content pre {
        background-color: #f5f5f5;
        padding: 15px;
        border-radius: 5px;
        overflow-x: auto;
    }
    
    .documentation-content code {
        background-color: #f5f5f5;
        padding: 2px 5px;
        border-radius: 3px;
    }
    
    .documentation-content pre code {
        padding: 0;
    }
    
    .documentation-content ul, .documentation-content ol {
        padding-left: 25px;
    }
    
    .documentation-content blockquote {
        border-left: 4px solid #ddd;
        padding-left: 15px;
        color: #777;
    }
    
    .documentation-content img {
        max-width: 100%;
    }
    
    .documentation-content table {
        border-collapse: collapse;
        width: 100%;
        margin: 20px 0;
    }
    
    .documentation-content table, .documentation-content th, .documentation-content td {
        border: 1px solid #ddd;
    }
    
    .documentation-content th, .documentation-content td {
        padding: 10px;
        text-align: left;
    }
    
    .documentation-content th {
        background-color: #f5f5f5;
    }
    
    .documentation-content tr:nth-child(even) {
        background-color: #f9f9f9;
    }
    
    .mermaid {
        margin: 20px 0;
    }
    
    .toc-list {
        list-style-type: none;
        padding-left: 0;
    }
    
    .toc-list li {
        margin-bottom: 8px;
    }
    
    .toc-list ul {
        list-style-type: none;
        padding-left: 15px;
    }
    
    .toc-list a {
        text-decoration: none;
        color: #495057;
        display: block;
        padding: 3px 0;
        border-left: 2px solid transparent;
        padding-left: 10px;
    }
    
    .toc-list a:hover {
        color: #007bff;
        border-left: 2px solid #007bff;
    }
    
    .toc-list a.active {
        color: #007bff;
        font-weight: bold;
        border-left: 2px solid #007bff;
    }
    
    .toc-header {
        font-weight: bold;
        margin-bottom: 10px;
        padding-bottom: 5px;
        border-bottom: 1px solid #ddd;
    }
    
    .back-to-top {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background-color: #007bff;
        color: white;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        text-decoration: none;
        opacity: 0;
        transition: opacity 0.3s;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }
    
    .back-to-top.visible {
        opacity: 1;
    }
    
    .back-to-top:hover {
        background-color: #0056b3;
        color: white;
    }
    
    @media (max-width: 768px) {
        .documentation-container {
            flex-direction: column;
        }
        
        .documentation-sidebar {
            width: 100%;
            position: relative;
            margin-right: 0;
            margin-bottom: 20px;
            max-height: none;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="documentation-container">
    <aside class="documentation-sidebar">
        <div class="toc-header">Table of Contents</div>
        <ul class="toc-list" id="toc">
            <!-- Table of contents will be generated by JavaScript -->
        </ul>
    </aside>
    <div class="documentation-content">
        {{ content }}
    </div>
</div>
<a href="#" class="back-to-top" id="back-to-top" title="Back to top">↑</a>
{% endblock %}

{% block scripts %}
{{ super() }}
<!-- Include Mermaid.js for diagram rendering -->
<script src="https://cdn.jsdelivr.net/npm/mermaid@10.0.0/dist/mermaid.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
        
        // Generate table of contents
        const toc = document.getElementById('toc');
        const headings = document.querySelectorAll('.documentation-content h2, .documentation-content h3');
        const tocItems = {};
        
        headings.forEach(heading => {
            // Create an ID for the heading if it doesn't have one
            if (!heading.id) {
                heading.id = heading.textContent.toLowerCase().replace(/[^\w]+/g, '-');
            }
            
            const listItem = document.createElement('li');
            const link = document.createElement('a');
            link.href = `#${heading.id}`;
            link.textContent = heading.textContent;
            link.dataset.target = heading.id;
            
            if (heading.tagName === 'H2') {
                listItem.appendChild(link);
                toc.appendChild(listItem);
                tocItems[heading.id] = {
                    element: listItem,
                    link: link,
                    children: document.createElement('ul')
                };
            } else if (heading.tagName === 'H3') {
                // Find the parent H2
                let parentId = null;
                let currentElement = heading.previousElementSibling;
                
                while (currentElement && !parentId) {
                    if (currentElement.tagName === 'H2') {
                        parentId = currentElement.id;
                    }
                    currentElement = currentElement.previousElementSibling;
                }
                
                if (parentId && tocItems[parentId]) {
                    const childItem = document.createElement('li');
                    childItem.appendChild(link);
                    
                    if (!tocItems[parentId].element.contains(tocItems[parentId].children)) {
                        tocItems[parentId].element.appendChild(tocItems[parentId].children);
                    }
                    
                    tocItems[parentId].children.appendChild(childItem);
                    tocItems[heading.id] = {
                        element: childItem,
                        link: link
                    };
                }
            }
        });
        
        // Highlight active section on scroll
        const observerOptions = {
            root: null,
            rootMargin: '0px 0px -70% 0px',
            threshold: 0
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                const id = entry.target.id;
                const tocLink = document.querySelector(`.toc-list a[data-target="${id}"]`);
                
                if (tocLink) {
                    if (entry.isIntersecting) {
                        // Remove active class from all links
                        document.querySelectorAll('.toc-list a').forEach(link => {
                            link.classList.remove('active');
                        });
                        
                        // Add active class to current link
                        tocLink.classList.add('active');
                    }
                }
            });
        }, observerOptions);
        
        // Observe all headings
        headings.forEach(heading => {
            observer.observe(heading);
        });
        
        // Back to top button
        const backToTopButton = document.getElementById('back-to-top');
        
        window.addEventListener('scroll', () => {
            if (window.scrollY > 300) {
                backToTopButton.classList.add('visible');
            } else {
                backToTopButton.classList.remove('visible');
            }
        });
        
        backToTopButton.addEventListener('click', (e) => {
            e.preventDefault();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 20,
                        behavior: 'smooth'
                    });
                    
                    // Update URL without reloading the page
                    history.pushState(null, null, `#${targetId}`);
                }
            });
        });
        
        // Handle initial hash in URL
        if (window.location.hash) {
            const targetId = window.location.hash.substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                setTimeout(() => {
                    window.scrollTo({
                        top: targetElement.offsetTop - 20,
                        behavior: 'smooth'
                    });
                    
                    // Highlight the active section in TOC
                    const tocLink = document.querySelector(`.toc-list a[data-target="${targetId}"]`);
                    if (tocLink) {
                        tocLink.classList.add('active');
                    }
                }, 300);
            }
        }
    });
</script>
{% endblock %} 