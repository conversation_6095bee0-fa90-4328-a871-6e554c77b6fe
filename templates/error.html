{% extends "base.html" %}

{% block title %}Error | Security Dashboard{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h2 class="mb-0">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        {{ title|default('An Error Occurred') }}
                    </h2>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <img src="{{ url_for('static', filename='images/error.svg') }}" alt="Error" class="img-fluid mb-4" style="max-width: 200px;">
                        <h3>{{ message|default('We encountered an unexpected error.') }}</h3>
                        <p class="text-muted">{{ description|default('Our team has been notified and is working to fix the issue.') }}</p>
                    </div>
                    
                    <div class="alert alert-secondary">
                        <h5>What you can do:</h5>
                        <ul>
                            <li>Try refreshing the page</li>
                            <li>Try again in a few minutes</li>
                            <li>Return to the <a href="{{ url_for('index') }}">dashboard</a></li>
                            <li>Contact support if the problem persists</li>
                        </ul>
                    </div>
                    
                    {% if error_id %}
                    <div class="mt-3 text-muted">
                        <small>Error reference: {{ error_id }}</small>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-house-door"></i> Return to Dashboard
                        </a>
                        <a href="javascript:history.back()" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-left"></i> Go Back
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
<style>
    .card-header {
        border-radius: calc(0.375rem - 1px) calc(0.375rem - 1px) 0 0;
    }
</style>
{% endblock %} 