{% extends "base.html" %}

{% block title %}Error Logs | Admin Dashboard{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Error Logs</h1>
        <div>
            <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#clearLogsModal">
                <i class="bi bi-trash"></i> Clear Logs
            </button>
            <a href="{{ url_for('admin.export_error_logs') }}" class="btn btn-outline-primary">
                <i class="bi bi-download"></i> Export Logs
            </a>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">Filter Options</h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ url_for('admin.error_logs') }}" class="row g-3">
                <div class="col-md-3">
                    <label for="error_type" class="form-label">Error Type</label>
                    <select class="form-select" id="error_type" name="error_type">
                        <option value="">All Types</option>
                        {% for type in error_types %}
                            <option value="{{ type }}" {% if request.args.get('error_type') == type %}selected{% endif %}>{{ type }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="endpoint" class="form-label">Endpoint</label>
                    <select class="form-select" id="endpoint" name="endpoint">
                        <option value="">All Endpoints</option>
                        {% for endpoint in endpoints %}
                            <option value="{{ endpoint }}" {% if request.args.get('endpoint') == endpoint %}selected{% endif %}>{{ endpoint }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date_from" class="form-label">Date From</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request.args.get('date_from', '') }}">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">Date To</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request.args.get('date_to', '') }}">
                </div>
                <div class="col-12 d-flex justify-content-end">
                    <a href="{{ url_for('admin.error_logs') }}" class="btn btn-outline-secondary me-2">Reset</a>
                    <button type="submit" class="btn btn-primary">Apply Filters</button>
                </div>
            </form>
        </div>
    </div>

    {% if error_logs %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Error Type</th>
                        <th>Error Message</th>
                        <th>Endpoint</th>
                        <th>User</th>
                        <th>Date/Time</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in error_logs %}
                        <tr>
                            <td>{{ log.id }}</td>
                            <td>
                                <span class="badge bg-danger">{{ log.error_type }}</span>
                            </td>
                            <td>{{ log.error_message|truncate(50) }}</td>
                            <td>{{ log.endpoint or 'N/A' }}</td>
                            <td>
                                {% if log.user %}
                                    <a href="{{ url_for('admin.view_user', id=log.user.id) }}">{{ log.user.username }}</a>
                                {% else %}
                                    <span class="text-muted">Anonymous</span>
                                {% endif %}
                            </td>
                            <td>{{ log.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#viewLogModal{{ log.id }}">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                    <form action="{{ url_for('admin.delete_error_log', id=log.id) }}" method="POST" class="d-inline">
                                        <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this log?')">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>

                        <!-- View Log Modal -->
                        <div class="modal fade" id="viewLogModal{{ log.id }}" tabindex="-1" aria-labelledby="viewLogModalLabel{{ log.id }}" aria-hidden="true">
                            <div class="modal-dialog modal-lg modal-dialog-scrollable">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="viewLogModalLabel{{ log.id }}">Error Log Details</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="mb-3">
                                            <h6>Error Type</h6>
                                            <div class="p-2 bg-light rounded">{{ log.error_type }}</div>
                                        </div>
                                        <div class="mb-3">
                                            <h6>Error Message</h6>
                                            <div class="p-2 bg-light rounded">{{ log.error_message }}</div>
                                        </div>
                                        <div class="mb-3">
                                            <h6>Stack Trace</h6>
                                            <pre class="p-2 bg-light rounded" style="max-height: 300px; overflow-y: auto;"><code>{{ log.stack_trace }}</code></pre>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <h6>Endpoint</h6>
                                                <div class="p-2 bg-light rounded">{{ log.endpoint or 'N/A' }}</div>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>Request Method</h6>
                                                <div class="p-2 bg-light rounded">{{ log.request_method or 'N/A' }}</div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <h6>Request Data</h6>
                                            <pre class="p-2 bg-light rounded" style="max-height: 200px; overflow-y: auto;"><code>{{ log.request_data|tojson(indent=2) if log.request_data else 'No request data' }}</code></pre>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <h6>User</h6>
                                                <div class="p-2 bg-light rounded">
                                                    {% if log.user %}
                                                        <a href="{{ url_for('admin.view_user', id=log.user.id) }}">{{ log.user.username }}</a>
                                                    {% else %}
                                                        Anonymous
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>Date/Time</h6>
                                                <div class="p-2 bg-light rounded">{{ log.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <nav aria-label="Error logs pagination">
            <ul class="pagination justify-content-center">
                {% if page > 1 %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin.error_logs', page=page-1, **request.args) }}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                {% endif %}

                {% for p in range(1, total_pages + 1) %}
                    <li class="page-item {% if p == page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for('admin.error_logs', page=p, **request.args) }}">{{ p }}</a>
                    </li>
                {% endfor %}

                {% if page < total_pages %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin.error_logs', page=page+1, **request.args) }}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    {% else %}
        <div class="alert alert-info">
            <p>No error logs found.</p>
        </div>
    {% endif %}
</div>

<!-- Clear Logs Modal -->
<div class="modal fade" id="clearLogsModal" tabindex="-1" aria-labelledby="clearLogsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="clearLogsModalLabel">Confirm Clear Logs</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to clear all error logs? This action cannot be undone.</p>
                <form id="clearLogsForm" method="POST" action="{{ url_for('admin.clear_error_logs') }}">
                    <div class="mb-3">
                        <label for="clear_days" class="form-label">Clear logs older than:</label>
                        <select class="form-select" id="clear_days" name="days">
                            <option value="0">All logs</option>
                            <option value="1">1 day</option>
                            <option value="7" selected>7 days</option>
                            <option value="30">30 days</option>
                            <option value="90">90 days</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="clearLogsForm" class="btn btn-danger">Clear Logs</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add syntax highlighting for code blocks if needed
        // This is just a placeholder - you might want to add a library like highlight.js
    });
</script>
{% endblock %} 