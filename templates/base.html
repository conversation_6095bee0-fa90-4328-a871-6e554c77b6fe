<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Security Dashboard{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Intro.js CSS -->
    <link href="https://cdn.jsdelivr.net/npm/intro.js@7.2.0/minified/introjs.min.css" rel="stylesheet">

    <!-- Custom styles -->
    <style>
        .tooltip-helper {
            cursor: pointer;
        }
        #start-tour {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        .highlight-tour-element {
            z-index: 999999 !important;
            position: relative;
        }
        .navbar .nav-item {
            position: relative;
        }
        #theme-toggle {
            cursor: pointer;
        }
        /* Dark mode specific styles */
        [data-bs-theme="dark"] {
            --bs-body-color: #dee2e6;
            --bs-body-bg: #212529;
        }
        [data-bs-theme="dark"] .card {
            background-color: #2c3034;
            border-color: #373b3e;
        }
        /* Tour customization for dark theme */
        .introjs-tooltip.dark-theme {
            background-color: #2c3034;
            color: #dee2e6;
        }
        .introjs-tooltip.dark-theme .introjs-tooltiptext {
            color: #dee2e6;
        }
        .introjs-tooltip.dark-theme .introjs-button {
            background-color: #373b3e;
            color: #dee2e6;
            border-color: #495057;
        }
        .introjs-tooltip.dark-theme .introjs-button:hover {
            background-color: #495057;
        }
        .introjs-tooltip.dark-theme .introjs-skipbutton {
            color: #dee2e6;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/" id="nav-home">Security Dashboard</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/threats" id="nav-threats">Threat Analysis</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/defense" id="nav-defense">Defense Mapping</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/monitoring" id="nav-monitoring">System Monitoring</a>
                    </li>
                    {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Security Testing
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('assessments') }}" id="nav-assessments">Assessments</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('campaigns') }}" id="nav-campaigns">Campaigns</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('testcases') }}" id="nav-testcases">Test Cases</a></li>
                        </ul>
                    </li>
                    {% endif %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="docsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Documentation
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="docsDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('documentation_index') }}" id="nav-docs-all">All Documentation</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('documentation_index') }}#feature-documentation" id="nav-docs-features">Feature Documentation</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('documentation_index') }}#technical-documentation" id="nav-docs-technical">Technical Documentation</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('documentation_index') }}#planning-and-roadmap" id="nav-docs-roadmap">Planning & Roadmap</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('documentation_index') }}#development-guides" id="nav-docs-guides">Development Guides</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/settings" id="nav-settings">Settings</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item">
                        <span class="nav-link">Welcome, {{ current_user.username }}</span>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('logout') }}">Logout</a>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('login') }}">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('register') }}">Register</a>
                    </li>
                    {% endif %}
                    <li class="nav-item">
                        <button id="theme-toggle" class="btn btn-outline-light" aria-label="Toggle theme">
                            <i class="theme-icon">🌙</i>
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mt-4" id="main-content">
        {% block content %}{% endblock %}
    </main>

    <!-- Tour Button -->
    <button id="start-tour" class="btn btn-primary">Start Tour</button>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/intro.js@7.2.0/minified/intro.min.js"></script>
    <script src="{{ url_for('static', filename='js/onboarding.js') }}"></script>
    <script src="{{ url_for('static', filename='js/theme.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>