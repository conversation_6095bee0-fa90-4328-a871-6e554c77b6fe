{% extends "base.html" %}
{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-center">Account Settings</h3>
                </div>
                <div class="card-body">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }}">{{ message }}</div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    <form method="POST" action="{{ url_for('settings') }}">
                        {{ form.hidden_tag() }}
                        <div class="form-group mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                            {% if form.username.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="form-group mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="form-group mb-3">
                            {{ form.current_password.label(class="form-label") }}
                            {{ form.current_password(class="form-control" + (" is-invalid" if form.current_password.errors else "")) }}
                            {% if form.current_password.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.current_password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="form-group mb-3">
                            {{ form.new_password.label(class="form-label") }}
                            {{ form.new_password(class="form-control" + (" is-invalid" if form.new_password.errors else "")) }}
                            {% if form.new_password.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.new_password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">Leave blank if you don't want to change password</small>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">Update Settings</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
