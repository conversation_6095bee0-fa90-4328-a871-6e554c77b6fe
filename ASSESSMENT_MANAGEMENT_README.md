# Assessment Management Feature

This document provides an overview of the Assessment Management feature implementation.

## Components Implemented

### API
- Core CRUD operations for Assessment Management
- Two additional endpoints:
  - `GET /api/v1/assessments/{id}/results` - Fetches assessment test execution results
  - `GET /api/v1/assessments/{id}/report` - Generates comprehensive assessment reports

### UI Components
1. **Assessment Service (`assessmentService.js`)**
   - Handles all API calls related to assessments
   - Includes methods for fetching, creating, updating, and deleting assessments
   - Provides methods for retrieving test results and reports

2. **Assessments List Page (`Assessments.js`)**
   - Displays a table of all assessments with pagination, sorting, and filtering
   - Allows creating new assessments, editing, and deleting existing ones
   - Includes filtering options by name, status, type, and target system

3. **Assessment Detail Page (`AssessmentDetail.js`)**
   - Shows detailed information about a specific assessment
   - Features a tabbed interface with:
     - Overview tab: Basic assessment information
     - Test Executions tab: Lists all test executions for the assessment
     - Campaigns tab: Associated campaigns
     - Report tab: Summary report with metrics and MITRE coverage
   - Provides buttons to edit, delete, and view full report

4. **Assessment Form (`AssessmentForm.js`)**
   - Form for creating new assessments or editing existing ones
   - Validates user input and displays error messages
   - Includes fields for all assessment properties
   - Handles both create and edit use cases with the same component

5. **Assessment Report (`AssessmentReport.js`)**
   - Comprehensive view of the assessment report
   - Includes executive summary with charts
   - Displays findings by severity with detailed information
   - Shows recommendations and test results
   - Provides a downloadable report option

## Routes
- `/assessments` - List all assessments
- `/assessments/new` - Create a new assessment
- `/assessments/:id` - View assessment details
- `/assessments/:id/edit` - Edit an existing assessment
- `/assessments/:id/report` - View the full assessment report

## Visual Interface Guide

For detailed screenshots and visual documentation of the UI, see [Assessment Management UI Documentation](docs/assessment-management-ui.md).

Screenshots can be generated using the screenshot generation script:

```bash
node scripts/generate_ui_screenshots.js
```

### Assessments List View
The main assessments page presents a clean tabular view with the following elements:
- **Header**: Contains the page title "Assessments" and action buttons for filters, refresh, and creating new assessments
- **Filter Panel**: Expandable section with fields to filter by name, status, type, and target system
- **Data Table**: Displays assessment information with sortable columns for:
  - Name (clickable to navigate to details)
  - Status (shown as colored chips)
  - Type
  - Target System
  - Start/End Dates
- **Action Buttons**: Each row contains icons for viewing, editing, viewing reports, and deleting
- **Pagination**: Controls at the bottom to navigate between pages of results

### Assessment Detail View
A comprehensive view of a single assessment with a tabbed interface:
- **Header**: Shows assessment name, status chip, and action buttons
- **Tab Navigation**: Four tabs for different aspects of the assessment
- **Overview Tab**: 
  - Left panel: Assessment details including description, target system, type, and dates
  - Right panel: Pie chart visualization of test results
- **Test Executions Tab**: Table of test cases with their execution results, notes, and metadata
- **Campaigns Tab**: Lists associated campaigns with their status and timeline
- **Report Tab**: Summary view of the assessment report with metrics and visualizations

### Assessment Form
A structured form for creating or editing assessments:
- **Header**: Context-aware title ("Create New Assessment" or "Edit Assessment")
- **Assessment Details Section**: 
  - Name and description fields
  - Type selection dropdown
  - Target system field
  - Status selection dropdown
  - Environment selection dropdown
- **Schedule Section**: Date pickers for start and end dates
- **Campaigns Section**: Multi-select dropdown for associating campaigns
- **Form Actions**: Buttons to cancel or submit the form

### Assessment Report View
A detailed report interface with several sections:
- **Header**: Report title with assessment name and timeline
- **Executive Summary**: Overview text with a pie chart showing findings by severity
- **Assessment Details**: Two cards showing assessment information and target system details
- **Findings Section**: Accordion-style expandable items for each finding, including:
  - Severity level with color coding
  - Description, impact, and remediation details
  - Affected components and references
- **Recommendations Section**: List of suggested actions
- **Test Results Summary**: Table of test execution results with status indicators

## Features
- **Data Visualization**: Charts and graphs for test results and findings
- **Interactive Elements**: Tabs, accordions, and dialogs for better user experience
- **Responsive Design**: Layouts work on both desktop and mobile devices
- **Form Validation**: Client-side validation for user inputs
- **Error Handling**: Proper error messages and loading states

## How to Access the UI
To view the Assessment Management UI:
1. Log in to the application
2. Navigate to the Assessments section from the main navigation menu
3. Interact with the list of assessments or create a new one
4. Click on any assessment to view its details
5. Use the action buttons to edit, delete, or view reports

## Next Steps
1. Complete testing for all aspects of the Assessment Management API
2. Verify UI components work correctly with real data
3. Implement report export functionality
4. Add user feedback mechanisms and notifications

## Status
The Assessment Management feature is now complete for the UI portion, with all necessary components implemented and integrated with the API. 