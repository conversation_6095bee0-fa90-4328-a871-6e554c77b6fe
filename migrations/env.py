import os
import sys
from logging.config import fileConfig
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse

from sqlalchemy import engine_from_config
from sqlalchemy import pool
from sqlalchemy import text

from alembic import context

# Add the project root directory to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from api.core.config import settings
from api.db.base_model import Base

# Import all models to ensure they are registered with Base.metadata
from api.db.models.item import Item
from api.db.models.user import User
# Import additional models here as needed

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Set the database URL from environment variable
url = os.environ.get("DATABASE_URL")
if url:
    config.set_main_option("sqlalchemy.url", url)

# Set target_metadata to None initially
target_metadata = None

# Try to dynamically find and load the database module and models
try:
    import importlib.util
    import pathlib
    
    # Try to find and import the database module
    database_paths = list(pathlib.Path('/app').rglob('database.py'))
    if database_paths:
        database_path = str(database_paths[0])
        print(f"Found database module at: {database_path}")
        
        # Import the module dynamically
        spec = importlib.util.spec_from_file_location("database_module", database_path)
        database_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(database_module)
        
        # Get the Base class
        Base = database_module.Base
        target_metadata = Base.metadata
        print("Successfully loaded SQLAlchemy models metadata")
    else:
        print("Could not find database.py file!")
except Exception as e:
    print(f"Error importing database models: {e}")

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.
    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.
    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, 
            target_metadata=target_metadata,
            compare_type=True,  # Compare column types
            compare_server_default=True,  # Compare default values
            include_schemas=True,  # Include database schema
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()