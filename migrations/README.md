
# Database Migrations

This directory contains database migration scripts managed by Alembic.

## Contents

- `versions/`: Individual migration script versions
- `env.py`: Alembic environment configuration
- `script.py.mako`: Template for generating new migrations

## Usage

### Creating a New Migration

```bash
alembic revision --autogenerate -m "description of changes"
```

### Running Migrations

```bash
# Apply all pending migrations
alembic upgrade head

# Apply specific number of migrations
alembic upgrade +1

# Rollback migrations
alembic downgrade -1
```

### Using the Helper Script

```bash
python scripts/manage_migrations.py create "description of changes"
python scripts/manage_migrations.py upgrade
python scripts/manage_migrations.py downgrade
```

## Best Practices

1. Review auto-generated migrations before applying them
2. Test migrations on development before applying to production
3. Include meaningful descriptions for each migration
4. Keep migrations small and focused on specific changes
