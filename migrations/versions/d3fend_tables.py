"""D3FEND ontology tables

Revision ID: d3fend_tables
Revises: initial_mitre_defense
Create Date: 2025-02-25 12:15:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'd3fend_tables'
down_revision = 'initial_mitre_defense'
branch_labels = None
depends_on = None

def upgrade() -> None:
    # Create d3fend_versions table
    op.create_table(
        'd3fend_versions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('version', sa.String(length=50), nullable=False),
        sa.Column('import_date', sa.DateTime(), nullable=True),
        sa.Column('is_current', sa.<PERSON>(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('version')
    )

    # Create d3fend_concepts table
    op.create_table(
        'd3fend_concepts',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('uri', sa.String(length=255), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('type', sa.String(length=50), nullable=False),
        sa.Column('definition', sa.Text(), nullable=True),
        sa.Column('version_id', sa.Integer(), nullable=False),
        sa.Column('created', sa.DateTime(), nullable=True),
        sa.Column('modified', sa.DateTime(), nullable=True),
        sa.Column('is_deprecated', sa.Boolean(), nullable=True),
        sa.Column('external_references', sa.Text(), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['version_id'], ['d3fend_versions.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('uri')
    )

    # Create d3fend_relationships association table
    op.create_table(
        'd3fend_relationships',
        sa.Column('source_id', sa.Integer(), nullable=False),
        sa.Column('target_id', sa.Integer(), nullable=False),
        sa.Column('relationship_type', sa.String(length=50), nullable=False),
        sa.ForeignKeyConstraint(['source_id'], ['d3fend_concepts.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['target_id'], ['d3fend_concepts.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('source_id', 'target_id')
    )

def downgrade() -> None:
    op.drop_table('d3fend_relationships')
    op.drop_table('d3fend_concepts')
    op.drop_table('d3fend_versions')
