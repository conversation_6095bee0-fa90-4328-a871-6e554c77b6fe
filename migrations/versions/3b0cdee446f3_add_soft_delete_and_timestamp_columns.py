"""Add soft delete and timestamp columns

Revision ID: 3b0cdee446f3
Revises: d3fend_tables
Create Date: 2025-03-04 09:21:06.030281

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '3b0cdee446f3'
down_revision = 'd3fend_tables'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Use conditional drops with CASCADE to handle dependencies
    op.execute("DROP TABLE IF EXISTS d3f_class_hierarchy CASCADE")
    op.execute("DROP TABLE IF EXISTS stix_d3fend_mappings CASCADE")
    op.execute("DROP TABLE IF EXISTS d3f_countermeasures CASCADE")
    op.execute("DROP TABLE IF EXISTS d3f_digital_artifacts CASCADE")
    op.execute("DROP TABLE IF EXISTS d3f_class_property_relationships CASCADE")
    op.execute("DROP TABLE IF EXISTS d3f_properties CASCADE")
    op.execute("DROP TABLE IF EXISTS d3f_classes CASCADE")


def downgrade() -> None:
    # Recreate tables in reverse order of dependencies
    op.create_table('d3f_classes',
        sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column('uri', sa.VARCHAR(length=255), nullable=False),
        sa.Column('name', sa.VARCHAR(length=255), nullable=False),
        sa.Column('description', sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint('id', name='d3f_classes_pkey'),
        sa.UniqueConstraint('uri', name='d3f_classes_uri_key')
    )

    op.create_table('d3f_properties',
        sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column('uri', sa.VARCHAR(length=255), nullable=False),
        sa.Column('name', sa.VARCHAR(length=255), nullable=False),
        sa.Column('property_type', sa.VARCHAR(length=50), nullable=False),
        sa.Column('description', sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint('id', name='d3f_properties_pkey'),
        sa.UniqueConstraint('uri', name='d3f_properties_uri_key')
    )

    op.create_table('d3f_class_property_relationships',
        sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column('source_class_id', sa.INTEGER(), nullable=True),
        sa.Column('property_id', sa.INTEGER(), nullable=True),
        sa.Column('target_class_id', sa.INTEGER(), nullable=True),
        sa.ForeignKeyConstraint(['property_id'], ['d3f_properties.id'], name='d3f_class_property_relationships_property_id_fkey', ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['source_class_id'], ['d3f_classes.id'], name='d3f_class_property_relationships_source_class_id_fkey', ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['target_class_id'], ['d3f_classes.id'], name='d3f_class_property_relationships_target_class_id_fkey', ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id', name='d3f_class_property_relationships_pkey')
    )

    op.create_table('d3f_digital_artifacts',
        sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column('class_id', sa.INTEGER(), nullable=False),
        sa.Column('artifact_name', sa.VARCHAR(length=255), nullable=False),
        sa.ForeignKeyConstraint(['class_id'], ['d3f_classes.id'], name='d3f_digital_artifacts_class_id_fkey', ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id', name='d3f_digital_artifacts_pkey')
    )

    op.create_table('d3f_countermeasures',
        sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column('class_id', sa.INTEGER(), nullable=False),
        sa.Column('countermeasure_name', sa.VARCHAR(length=255), nullable=False),
        sa.Column('implementation_level', sa.VARCHAR(length=50), nullable=True),
        sa.ForeignKeyConstraint(['class_id'], ['d3f_classes.id'], name='d3f_countermeasures_class_id_fkey', ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id', name='d3f_countermeasures_pkey')
    )

    op.create_table('stix_d3fend_mappings',
        sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column('stix_id', sa.VARCHAR(length=50), nullable=False),
        sa.Column('d3fend_id', sa.VARCHAR(length=50), nullable=False),
        sa.Column('technique_id', sa.VARCHAR(length=50), nullable=False),
        sa.Column('mapping_type', sa.VARCHAR(length=50), nullable=False),
        sa.Column('confidence', sa.DOUBLE_PRECISION(precision=53), nullable=True),
        sa.Column('countermeasure_name', sa.VARCHAR(length=255), nullable=False),
        sa.Column('description', sa.VARCHAR(length=1000), nullable=True),
        sa.Column('effectiveness_score', sa.DOUBLE_PRECISION(precision=53), nullable=True),
        sa.Column('mapping_metadata', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('created_time', postgresql.TIMESTAMP(), nullable=True),
        sa.Column('updated_time', postgresql.TIMESTAMP(), nullable=True),
        sa.Column('deleted_time', postgresql.TIMESTAMP(), nullable=True),
        sa.PrimaryKeyConstraint('id', name='stix_d3fend_mappings_pkey')
    )

    op.create_table('d3f_class_hierarchy',
        sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column('subclass_id', sa.INTEGER(), nullable=True),
        sa.Column('superclass_id', sa.INTEGER(), nullable=True),
        sa.ForeignKeyConstraint(['subclass_id'], ['d3f_classes.id'], name='d3f_class_hierarchy_subclass_id_fkey', ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['superclass_id'], ['d3f_classes.id'], name='d3f_class_hierarchy_superclass_id_fkey', ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id', name='d3f_class_hierarchy_pkey')
    )