"""
Hierarchical data model migration.

Revision ID: 20240316_hierarchical
Revises: 20240315_add_campaign_tables
Create Date: 2024-03-16 10:00:00.000000
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic
revision = '20240316_hierarchical'
down_revision = '20240315_add_campaign_tables'
branch_labels = None
depends_on = None


def upgrade():
    # Create environments table
    op.create_table(
        'environments',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('type', sa.Enum('production', 'staging', 'development', 'test', 'other', name='environmenttype'), nullable=False),
        sa.Column('status', sa.Enum('active', 'inactive', 'deprecated', name='environmentstatus'), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('deleted_at', sa.DateTime(), nullable=True),
        sa.Column('is_deprecated', sa.Boolean(), nullable=False),
        sa.Column('is_revoked', sa.Boolean(), nullable=False),
        sa.Column('revoked_by_id', sa.Integer(), nullable=True),
        sa.Column('version', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['revoked_by_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_environments_id'), 'environments', ['id'], unique=False)
    
    # Add environment_id to assessments table
    op.add_column('assessments', sa.Column('environment_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'assessments', 'environments', ['environment_id'], ['id'])
    
    # Add assessment_id to campaigns table
    op.add_column('campaigns', sa.Column('assessment_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'campaigns', 'assessments', ['assessment_id'], ['id'])
    
    # Create a default environment for existing assessments
    op.execute("""
    INSERT INTO environments (
        name, 
        description, 
        type, 
        status, 
        created_by, 
        created_at, 
        updated_at, 
        is_deprecated, 
        is_revoked, 
        version
    ) 
    VALUES (
        'Default Environment', 
        'Default environment created during migration', 
        'test', 
        'active', 
        (SELECT id FROM users WHERE role = 'admin' LIMIT 1), 
        NOW(), 
        NOW(), 
        FALSE, 
        FALSE, 
        1
    )
    """)
    
    # Update existing assessments to use the default environment
    op.execute("""
    UPDATE assessments 
    SET environment_id = (SELECT id FROM environments WHERE name = 'Default Environment')
    """)
    
    # Update existing campaigns to link to their assessments
    op.execute("""
    UPDATE campaigns c
    SET assessment_id = a.id
    FROM assessments a
    WHERE a.campaign_id = c.id
    """)


def downgrade():
    # Remove foreign key constraints
    op.drop_constraint(None, 'campaigns', type_='foreignkey')
    op.drop_constraint(None, 'assessments', type_='foreignkey')
    
    # Drop columns
    op.drop_column('campaigns', 'assessment_id')
    op.drop_column('assessments', 'environment_id')
    
    # Drop environments table
    op.drop_index(op.f('ix_environments_id'), table_name='environments')
    op.drop_table('environments') 