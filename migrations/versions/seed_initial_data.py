"""Seed initial data

Revision ID: seed_initial_data
Revises: create_user_tables
Create Date: 2025-03-06 12:00:00.000000

"""
from alembic import op
from sqlalchemy import text
from datetime import datetime

# revision identifiers, used by Alembic.
revision = 'seed_initial_data'
down_revision = 'create_user_tables'
branch_labels = None
depends_on = None

def upgrade() -> None:
    # Create default admin user
    conn = op.get_bind()

    # Check if admin user exists
    result = conn.execute(
        text("SELECT id FROM users WHERE username = 'admin'")
    ).fetchone()

    if not result:
        conn.execute(
            text("""
                INSERT INTO users (username, email, hashed_password, role, is_active, email_verified, created_at)
                VALUES (:username, :email, :hashed_password, :role, :is_active, :email_verified, :created_at)
            """),
            {
                "username": "admin",
                "email": "<EMAIL>",
                "hashed_password": "$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewYpwBAHHKQM.Vj.", # "admin123"
                "role": "admin",
                "is_active": True,
                "email_verified": True,
                "created_at": datetime.utcnow()
            }
        )

        # Create default user preferences for admin
        conn.execute(
            text("""
                INSERT INTO user_preferences (user_id, theme, timezone, language, email_notifications)
                VALUES ((SELECT id FROM users WHERE username = 'admin'), :theme, :timezone, :language, :email_notifications)
            """),
            {
                "theme": "light",
                "timezone": "UTC",
                "language": "en",
                "email_notifications": True
            }
        )

def downgrade() -> None:
    # Remove seeded data
    conn = op.get_bind()
    conn.execute(text("DELETE FROM user_preferences WHERE user_id = (SELECT id FROM users WHERE username = 'admin')"))
    conn.execute(text("DELETE FROM users WHERE username = 'admin'"))