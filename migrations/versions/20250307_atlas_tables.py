"""Add ATLAS tables

Revision ID: 20250307_atlas_tables
Revises: 9f04fd77b717
Create Date: 2025-03-07 09:45:00.000000

"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime

# revision identifiers, used by Alembic.
revision = '20250307_atlas_tables'
down_revision = '9f04fd77b717'
branch_labels = None
depends_on = None

def upgrade() -> None:
    # Create AtlasVersion table
    op.create_table(
        'atlas_versions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('version', sa.String(length=50), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('import_date', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('is_current', sa.<PERSON>(), nullable=False, default=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('version', name='uq_atlas_versions_version')
    )

    # Create AtlasTactic table
    op.create_table(
        'atlas_tactics',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('external_id', sa.String(length=50), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('version_id', sa.Integer(), nullable=False),
        sa.Column('created', sa.DateTime(), nullable=True),
        sa.Column('modified', sa.DateTime(), nullable=True),
        sa.Column('revoked', sa.Boolean(), nullable=False, default=False),
        sa.Column('deprecated', sa.Boolean(), nullable=False, default=False),
        sa.ForeignKeyConstraint(['version_id'], ['atlas_versions.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('external_id', 'version_id', name='uq_atlas_tactics_external_id_version')
    )

    # Create AtlasTechnique table
    op.create_table(
        'atlas_techniques',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('external_id', sa.String(length=50), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('version_id', sa.Integer(), nullable=False),
        sa.Column('tactic_id', sa.Integer(), nullable=True),
        sa.Column('created', sa.DateTime(), nullable=True),
        sa.Column('modified', sa.DateTime(), nullable=True),
        sa.Column('revoked', sa.Boolean(), nullable=False, default=False),
        sa.Column('deprecated', sa.Boolean(), nullable=False, default=False),
        sa.Column('is_subtechnique', sa.Boolean(), nullable=False, default=False),
        sa.Column('parent_technique_id', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['parent_technique_id'], ['atlas_techniques.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['tactic_id'], ['atlas_tactics.id'], ondelete='SET NULL'),
        sa.ForeignKeyConstraint(['version_id'], ['atlas_versions.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('external_id', 'version_id', name='uq_atlas_techniques_external_id_version')
    )

    # Create AtlasMatrix table
    op.create_table(
        'atlas_matrices',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('version_id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['version_id'], ['atlas_versions.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )

    # Create AtlasMatrixItem table
    op.create_table(
        'atlas_matrix_items',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('matrix_id', sa.Integer(), nullable=False),
        sa.Column('technique_id', sa.Integer(), nullable=False),
        sa.Column('tactic_id', sa.Integer(), nullable=False),
        sa.Column('color', sa.String(length=50), nullable=True),
        sa.Column('show_subtechniques', sa.Boolean(), nullable=False, default=True),
        sa.ForeignKeyConstraint(['matrix_id'], ['atlas_matrices.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['technique_id'], ['atlas_techniques.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['tactic_id'], ['atlas_tactics.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('matrix_id', 'technique_id', 'tactic_id', name='uq_atlas_matrix_items_matrix_technique_tactic')
    )

def downgrade() -> None:
    op.drop_table('atlas_matrix_items')
    op.drop_table('atlas_matrices')
    op.drop_table('atlas_techniques')
    op.drop_table('atlas_tactics')
    op.drop_table('atlas_versions')