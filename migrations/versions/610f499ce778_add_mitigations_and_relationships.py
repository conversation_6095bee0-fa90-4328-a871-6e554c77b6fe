"""Add mitigations and relationships

Revision ID: 610f499ce778
Revises: 3b0cdee446f3
Create Date: 2025-03-04 09:33:15.413582

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '610f499ce778'
down_revision = '3b0cdee446f3'
branch_labels = None
depends_on = None

def upgrade() -> None:
    # Create enum type first
    op.execute("CREATE TYPE technologydomain AS ENUM ('enterprise', 'mobile', 'ics')")

    # Add new columns to existing tables
    op.add_column('mitre_techniques', sa.Column('detection', sa.String(length=2000), nullable=True))
    op.add_column('mitre_techniques', sa.Column('platforms', sa.JSON(), nullable=True))
    op.add_column('mitre_versions', sa.Column('technology_domain', 
                                            sa.Enum('enterprise', 'mobile', 'ics', 
                                                   name='technologydomain', 
                                                   create_type=False),
                                            nullable=False, 
                                            server_default='enterprise'))

def downgrade() -> None:
    # Drop added columns
    op.drop_column('mitre_versions', 'technology_domain')
    op.drop_column('mitre_techniques', 'platforms')
    op.drop_column('mitre_techniques', 'detection')

    # Drop the enum type
    op.execute("DROP TYPE technologydomain")