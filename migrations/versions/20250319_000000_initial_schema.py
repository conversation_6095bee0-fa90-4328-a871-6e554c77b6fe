"""Initial schema

Revision ID: 0001_initial_schema
Revises: 
Create Date: 2025-03-19 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '0001_initial_schema'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Drop problematic index if it exists
    try:
        op.execute("DROP INDEX IF EXISTS ix_test_cases_id;")
    except Exception as e:
        print(f"Warning: Could not drop index ix_test_cases_id: {e}")
    
    # Create device_info table if it doesn't exist
    op.create_table('device_info',
        sa.Column('id', postgresql.UUID(), nullable=False),
        sa.Column('device_type', sa.String(255), nullable=True),
        sa.Column('device_name', sa.String(255), nullable=True),
        sa.Column('os_name', sa.String(255), nullable=True),
        sa.Column('os_version', sa.String(255), nullable=True),
        sa.Column('browser_name', sa.String(255), nullable=True),
        sa.Column('browser_version', sa.String(255), nullable=True),
        sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create remaining tables in the proper order...
    # For now we'll just create the device_info table to fix the immediate issue


def downgrade() -> None:
    # Drop tables in reverse order
    op.drop_table('device_info') 