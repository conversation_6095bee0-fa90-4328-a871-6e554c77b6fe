"""
Add heat map tables for MITRE ATT&CK visualization.

Revision ID: 004_add_heatmap_tables
Revises: 003_add_user_management
Create Date: 2024-12-19 10:00:00.000000
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic
revision = '004_add_heatmap_tables'
down_revision = '003_add_user_management'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Create heat map related tables.
    
    This migration adds tables for storing attack technique results
    and heat map snapshots to support MITRE ATT&CK visualization.
    """
    # Create attack_technique_results table
    op.create_table(
        'attack_technique_results',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('technique_id', sa.String(), nullable=False),
        sa.Column('campaign_id', sa.Integer(), nullable=False),
        sa.Column('assessment_id', sa.Integer(), nullable=True),
        
        # Effectiveness metrics (0-1 scale)
        sa.Column('effectiveness_score', sa.Float(), nullable=True, default=0.0),
        sa.Column('detection_rate', sa.Float(), nullable=True, default=0.0),
        sa.Column('prevention_rate', sa.Float(), nullable=True, default=0.0),
        
        # Execution statistics
        sa.Column('execution_count', sa.Integer(), nullable=True, default=0),
        sa.Column('success_count', sa.Integer(), nullable=True, default=0),
        sa.Column('detection_count', sa.Integer(), nullable=True, default=0),
        sa.Column('prevention_count', sa.Integer(), nullable=True, default=0),
        
        # Metadata
        sa.Column('last_tested', sa.DateTime(), nullable=True),
        sa.Column('test_environment', sa.String(), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        
        # Timestamps
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('deleted_at', sa.DateTime(), nullable=True),
        
        # Primary key
        sa.PrimaryKeyConstraint('id'),
        
        # Foreign keys
        sa.ForeignKeyConstraint(
            ['campaign_id'], 
            ['campaigns.id'], 
            ondelete='CASCADE'
        ),
        sa.ForeignKeyConstraint(
            ['assessment_id'], 
            ['assessments.id'], 
            ondelete='CASCADE'
        ),
        
        # Unique constraint for technique and campaign
        sa.UniqueConstraint(
            'technique_id', 
            'campaign_id', 
            name='uix_technique_campaign_result'
        )
    )
    
    # Create indexes for attack_technique_results
    op.create_index(
        'ix_attack_technique_results_technique_id',
        'attack_technique_results',
        ['technique_id']
    )
    op.create_index(
        'ix_attack_technique_results_campaign_id',
        'attack_technique_results',
        ['campaign_id']
    )
    op.create_index(
        'ix_attack_technique_results_assessment_id',
        'attack_technique_results',
        ['assessment_id']
    )
    op.create_index(
        'ix_attack_technique_results_deleted_at',
        'attack_technique_results',
        ['deleted_at']
    )
    op.create_index(
        'ix_attack_technique_results_last_tested',
        'attack_technique_results',
        ['last_tested']
    )
    
    # Create heatmap_snapshots table
    op.create_table(
        'heatmap_snapshots',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('campaign_id', sa.Integer(), nullable=True),
        sa.Column('assessment_id', sa.Integer(), nullable=True),
        
        # Snapshot metadata
        sa.Column('snapshot_date', sa.DateTime(), nullable=True),
        sa.Column('snapshot_type', sa.String(), nullable=True, default='campaign'),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        
        # Aggregated data (stored as JSON for flexibility)
        sa.Column('technique_coverage', sa.JSON(), nullable=True),
        sa.Column('tactic_effectiveness', sa.JSON(), nullable=True),
        sa.Column('overall_metrics', sa.JSON(), nullable=True),
        
        # Comparison data
        sa.Column('baseline_snapshot_id', sa.Integer(), nullable=True),
        
        # Timestamps
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('deleted_at', sa.DateTime(), nullable=True),
        
        # Primary key
        sa.PrimaryKeyConstraint('id'),
        
        # Foreign keys
        sa.ForeignKeyConstraint(
            ['campaign_id'], 
            ['campaigns.id'], 
            ondelete='CASCADE'
        ),
        sa.ForeignKeyConstraint(
            ['assessment_id'], 
            ['assessments.id'], 
            ondelete='CASCADE'
        ),
        sa.ForeignKeyConstraint(
            ['baseline_snapshot_id'], 
            ['heatmap_snapshots.id']
        )
    )
    
    # Create indexes for heatmap_snapshots
    op.create_index(
        'ix_heatmap_snapshots_campaign_id',
        'heatmap_snapshots',
        ['campaign_id']
    )
    op.create_index(
        'ix_heatmap_snapshots_assessment_id',
        'heatmap_snapshots',
        ['assessment_id']
    )
    op.create_index(
        'ix_heatmap_snapshots_snapshot_date',
        'heatmap_snapshots',
        ['snapshot_date']
    )
    op.create_index(
        'ix_heatmap_snapshots_snapshot_type',
        'heatmap_snapshots',
        ['snapshot_type']
    )
    op.create_index(
        'ix_heatmap_snapshots_deleted_at',
        'heatmap_snapshots',
        ['deleted_at']
    )
    op.create_index(
        'ix_heatmap_snapshots_name',
        'heatmap_snapshots',
        ['name']
    )
    
    # Add check constraints for data validation
    op.create_check_constraint(
        'ck_attack_technique_results_effectiveness_score',
        'attack_technique_results',
        'effectiveness_score >= 0 AND effectiveness_score <= 1'
    )
    op.create_check_constraint(
        'ck_attack_technique_results_detection_rate',
        'attack_technique_results',
        'detection_rate >= 0 AND detection_rate <= 1'
    )
    op.create_check_constraint(
        'ck_attack_technique_results_prevention_rate',
        'attack_technique_results',
        'prevention_rate >= 0 AND prevention_rate <= 1'
    )
    op.create_check_constraint(
        'ck_attack_technique_results_execution_count',
        'attack_technique_results',
        'execution_count >= 0'
    )
    op.create_check_constraint(
        'ck_attack_technique_results_success_count',
        'attack_technique_results',
        'success_count >= 0 AND success_count <= execution_count'
    )
    op.create_check_constraint(
        'ck_attack_technique_results_detection_count',
        'attack_technique_results',
        'detection_count >= 0 AND detection_count <= execution_count'
    )
    op.create_check_constraint(
        'ck_attack_technique_results_prevention_count',
        'attack_technique_results',
        'prevention_count >= 0 AND prevention_count <= execution_count'
    )
    
    # Add check constraint for snapshot type
    op.create_check_constraint(
        'ck_heatmap_snapshots_snapshot_type',
        'heatmap_snapshots',
        "snapshot_type IN ('campaign', 'assessment', 'organization', 'comparison')"
    )
    
    # Add check constraint to ensure either campaign_id or assessment_id is provided
    op.create_check_constraint(
        'ck_heatmap_snapshots_campaign_or_assessment',
        'heatmap_snapshots',
        'campaign_id IS NOT NULL OR assessment_id IS NOT NULL'
    )


def downgrade() -> None:
    """
    Drop heat map related tables.
    
    This migration removes all heat map related tables and their
    associated indexes and constraints.
    """
    # Drop check constraints
    op.drop_constraint(
        'ck_heatmap_snapshots_campaign_or_assessment',
        'heatmap_snapshots',
        type_='check'
    )
    op.drop_constraint(
        'ck_heatmap_snapshots_snapshot_type',
        'heatmap_snapshots',
        type_='check'
    )
    op.drop_constraint(
        'ck_attack_technique_results_prevention_count',
        'attack_technique_results',
        type_='check'
    )
    op.drop_constraint(
        'ck_attack_technique_results_detection_count',
        'attack_technique_results',
        type_='check'
    )
    op.drop_constraint(
        'ck_attack_technique_results_success_count',
        'attack_technique_results',
        type_='check'
    )
    op.drop_constraint(
        'ck_attack_technique_results_execution_count',
        'attack_technique_results',
        type_='check'
    )
    op.drop_constraint(
        'ck_attack_technique_results_prevention_rate',
        'attack_technique_results',
        type_='check'
    )
    op.drop_constraint(
        'ck_attack_technique_results_detection_rate',
        'attack_technique_results',
        type_='check'
    )
    op.drop_constraint(
        'ck_attack_technique_results_effectiveness_score',
        'attack_technique_results',
        type_='check'
    )
    
    # Drop indexes for heatmap_snapshots
    op.drop_index('ix_heatmap_snapshots_name', table_name='heatmap_snapshots')
    op.drop_index('ix_heatmap_snapshots_deleted_at', table_name='heatmap_snapshots')
    op.drop_index('ix_heatmap_snapshots_snapshot_type', table_name='heatmap_snapshots')
    op.drop_index('ix_heatmap_snapshots_snapshot_date', table_name='heatmap_snapshots')
    op.drop_index('ix_heatmap_snapshots_assessment_id', table_name='heatmap_snapshots')
    op.drop_index('ix_heatmap_snapshots_campaign_id', table_name='heatmap_snapshots')
    
    # Drop indexes for attack_technique_results
    op.drop_index('ix_attack_technique_results_last_tested', table_name='attack_technique_results')
    op.drop_index('ix_attack_technique_results_deleted_at', table_name='attack_technique_results')
    op.drop_index('ix_attack_technique_results_assessment_id', table_name='attack_technique_results')
    op.drop_index('ix_attack_technique_results_campaign_id', table_name='attack_technique_results')
    op.drop_index('ix_attack_technique_results_technique_id', table_name='attack_technique_results')
    
    # Drop tables
    op.drop_table('heatmap_snapshots')
    op.drop_table('attack_technique_results')
