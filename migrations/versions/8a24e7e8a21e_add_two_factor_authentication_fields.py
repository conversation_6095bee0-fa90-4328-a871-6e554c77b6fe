"""Add two-factor authentication fields

Revision ID: 8a24e7e8a21e
Revises: 2a585d6cac47
Create Date: 2025-03-10 11:32:54.858851

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '8a24e7e8a21e'
down_revision = '2a585d6cac47'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_stix_d3fend_mappings_technique_id', table_name='stix_d3fend_mappings')
    op.drop_table('stix_d3fend_mappings')
    op.drop_index('ix_stix_objects_type', table_name='stix_objects')
    op.drop_table('stix_objects')
    op.drop_table('flask_users')
    op.add_column('users', sa.Column('two_factor_enabled', sa.<PERSON>(), nullable=True))
    op.add_column('users', sa.Column('two_factor_secret', sa.String(length=32), nullable=True))
    op.add_column('users', sa.Column('backup_codes', sa.String(length=512), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'backup_codes')
    op.drop_column('users', 'two_factor_secret')
    op.drop_column('users', 'two_factor_enabled')
    op.create_table('flask_users',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('username', sa.VARCHAR(length=64), autoincrement=False, nullable=False),
    sa.Column('email', sa.VARCHAR(length=120), autoincrement=False, nullable=False),
    sa.Column('password_hash', sa.VARCHAR(length=256), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='flask_users_pkey'),
    sa.UniqueConstraint('email', name='flask_users_email_key'),
    sa.UniqueConstraint('username', name='flask_users_username_key')
    )
    op.create_table('stix_objects',
    sa.Column('stix_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('type', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('data', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=False),
    sa.Column('created_time', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=False),
    sa.Column('updated_time', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=False),
    sa.Column('deleted_time', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('stix_id', name='pk_stix_objects')
    )
    op.create_index('ix_stix_objects_type', 'stix_objects', ['type'], unique=False)
    op.create_table('stix_d3fend_mappings',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('stix_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('d3fend_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('technique_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('mapping_type', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('confidence', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('countermeasure_name', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('description', sa.VARCHAR(length=1000), autoincrement=False, nullable=True),
    sa.Column('effectiveness_score', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('mapping_metadata', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('created_time', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('updated_time', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('deleted_time', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='pk_stix_d3fend_mappings')
    )
    op.create_index('ix_stix_d3fend_mappings_technique_id', 'stix_d3fend_mappings', ['technique_id'], unique=False)
    # ### end Alembic commands ###
