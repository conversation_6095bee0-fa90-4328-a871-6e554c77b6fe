"""merge migration heads

Revision ID: 9f04fd77b717
Revises: 610f499ce778, seed_initial_data
Create Date: 2025-03-06 11:31:06.758605

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9f04fd77b717'
down_revision = ('610f499ce778', 'seed_initial_data')
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Just a merge migration, no schema changes needed
    pass


def downgrade() -> None:
    pass