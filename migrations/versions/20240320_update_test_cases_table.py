"""Update test_cases table with new fields

Revision ID: 20240320_update_test_cases_table
Revises: 20240316_hierarchical
Create Date: 2024-03-20 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20240320_update_test_cases_table'
down_revision = '20240316_hierarchical'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create enum types
    op.execute("CREATE TYPE testcasestatus AS ENUM ('draft', 'active', 'deprecated', 'archived')")
    op.execute("CREATE TYPE testcasetype AS ENUM ('manual', 'automated', 'hybrid')")
    op.execute("CREATE TYPE testcasepriority AS ENUM ('low', 'medium', 'high', 'critical')")
    op.execute("CREATE TYPE testcasecomplexity AS ENUM ('simple', 'moderate', 'complex')")
    
    # Add new columns to test_cases table
    op.add_column('test_cases', sa.Column('type', sa.Enum('manual', 'automated', 'hybrid', name='testcasetype', create_type=False), 
                                         nullable=False, server_default='manual'))
    op.add_column('test_cases', sa.Column('priority', sa.Enum('low', 'medium', 'high', 'critical', name='testcasepriority', create_type=False), 
                                         nullable=False, server_default='medium'))
    op.add_column('test_cases', sa.Column('complexity', sa.Enum('simple', 'moderate', 'complex', name='testcasecomplexity', create_type=False), 
                                         nullable=False, server_default='moderate'))
    op.add_column('test_cases', sa.Column('prerequisites', sa.Text(), nullable=True))
    op.add_column('test_cases', sa.Column('steps', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('test_cases', sa.Column('tags', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('test_cases', sa.Column('mitre_techniques', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('test_cases', sa.Column('created_by', sa.Integer(), nullable=True))
    op.add_column('test_cases', sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False))
    op.add_column('test_cases', sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False))
    op.add_column('test_cases', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column('test_cases', sa.Column('is_deprecated', sa.Boolean(), server_default='false', nullable=False))
    op.add_column('test_cases', sa.Column('is_revoked', sa.Boolean(), server_default='false', nullable=False))
    op.add_column('test_cases', sa.Column('revoked_by_id', sa.Integer(), nullable=True))
    op.add_column('test_cases', sa.Column('version', sa.String(length=10), nullable=True))
    
    # Add foreign key constraints
    op.create_foreign_key(None, 'test_cases', 'users', ['created_by'], ['id'])
    op.create_foreign_key(None, 'test_cases', 'users', ['revoked_by_id'], ['id'])
    
    # Update status column to use the new enum type
    # First, rename the old status column
    op.alter_column('test_cases', 'status', new_column_name='old_status')
    
    # Add the new status column with the enum type
    op.add_column('test_cases', sa.Column('status', sa.Enum('draft', 'active', 'deprecated', 'archived', name='testcasestatus', create_type=False), 
                                         nullable=False, server_default='draft'))
    
    # Update the new status column based on the old status
    op.execute("""
    UPDATE test_cases 
    SET status = CASE 
        WHEN old_status = 'pending' THEN 'draft'::testcasestatus
        WHEN old_status = 'running' THEN 'active'::testcasestatus
        WHEN old_status = 'passed' THEN 'active'::testcasestatus
        WHEN old_status = 'failed' THEN 'active'::testcasestatus
        ELSE 'draft'::testcasestatus
    END
    """)
    
    # Drop the old status column
    op.drop_column('test_cases', 'old_status')


def downgrade() -> None:
    # Add back the old status column
    op.add_column('test_cases', sa.Column('old_status', sa.String(), server_default='pending', nullable=False))
    
    # Update the old status column based on the new status
    op.execute("""
    UPDATE test_cases 
    SET old_status = CASE 
        WHEN status = 'draft' THEN 'pending'
        WHEN status = 'active' THEN 'running'
        WHEN status = 'deprecated' THEN 'pending'
        WHEN status = 'archived' THEN 'pending'
        ELSE 'pending'
    END
    """)
    
    # Drop the new status column
    op.drop_column('test_cases', 'status')
    
    # Rename the old status column back to status
    op.alter_column('test_cases', 'old_status', new_column_name='status')
    
    # Drop foreign key constraints
    op.drop_constraint(None, 'test_cases', type_='foreignkey')
    op.drop_constraint(None, 'test_cases', type_='foreignkey')
    
    # Drop added columns
    op.drop_column('test_cases', 'version')
    op.drop_column('test_cases', 'revoked_by_id')
    op.drop_column('test_cases', 'is_revoked')
    op.drop_column('test_cases', 'is_deprecated')
    op.drop_column('test_cases', 'deleted_at')
    op.drop_column('test_cases', 'updated_at')
    op.drop_column('test_cases', 'created_at')
    op.drop_column('test_cases', 'created_by')
    op.drop_column('test_cases', 'mitre_techniques')
    op.drop_column('test_cases', 'tags')
    op.drop_column('test_cases', 'steps')
    op.drop_column('test_cases', 'prerequisites')
    op.drop_column('test_cases', 'complexity')
    op.drop_column('test_cases', 'priority')
    op.drop_column('test_cases', 'type')
    
    # Drop enum types
    op.execute("DROP TYPE testcasecomplexity")
    op.execute("DROP TYPE testcasepriority")
    op.execute("DROP TYPE testcasetype")
    op.execute("DROP TYPE testcasestatus") 