"""Create user tables

Revision ID: create_user_tables
Revises: 3b0cdee446f3
Create Date: 2025-03-06 12:30:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'create_user_tables'
down_revision = '3b0cdee446f3'
branch_labels = None
depends_on = None

def upgrade() -> None:
    # Create users table
    op.create_table(
        'users',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('username', sa.String(64), nullable=False),
        sa.Column('email', sa.String(120), nullable=False),
        sa.Column('hashed_password', sa.String(256), nullable=False),
        sa.Column('is_active', sa.<PERSON>(), default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()')),
        sa.Column('email_verified', sa.<PERSON>(), default=False),
        sa.Column('role', sa.String(20), nullable=False),
        sa.Column('full_name', sa.String(100)),
        sa.Column('bio', sa.Text),
        sa.Column('last_login', sa.DateTime(timezone=True)),
        sa.Column('failed_login_attempts', sa.Integer, default=0),
        sa.Column('password_changed_at', sa.DateTime(timezone=True)),
        sa.Column('account_locked', sa.Boolean(), default=False),
        sa.Column('account_locked_at', sa.DateTime(timezone=True)),
        sa.Column('last_failed_login', sa.DateTime(timezone=True)),
        sa.Column('password_reset_token', sa.String(256)),
        sa.Column('password_reset_expires', sa.DateTime(timezone=True)),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('username'),
        sa.UniqueConstraint('email')
    )

    # Create user_preferences table
    op.create_table(
        'user_preferences',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('theme', sa.String(20), default='light'),
        sa.Column('notification_settings', sa.JSON, default={}),
        sa.Column('dashboard_layout', sa.JSON, default={}),
        sa.Column('timezone', sa.String(50), default='UTC'),
        sa.Column('language', sa.String(10), default='en'),
        sa.Column('email_notifications', sa.Boolean(), default=True),
        sa.Column('two_factor_enabled', sa.Boolean(), default=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )

def downgrade() -> None:
    op.drop_table('user_preferences')
    op.drop_table('users')