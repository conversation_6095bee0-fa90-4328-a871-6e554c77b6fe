"""Add advanced soft deletion framework tables

Revision ID: 005_add_advanced_soft_deletion_tables
Revises: 004_add_heatmap_tables
Create Date: 2025-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '005_add_advanced_soft_deletion_tables'
down_revision = '004_add_heatmap_tables'
branch_labels = None
depends_on = None


def upgrade():
    """Create advanced soft deletion framework tables."""
    
    # Create operation type enums
    operation_type_enum = postgresql.ENUM(
        'soft_delete', 'restore', 'permanent_delete', 'policy_change', 
        'cascade_delete', 'cascade_restore',
        name='operation_type_enum'
    )
    operation_type_enum.create(op.get_bind())
    
    schedule_operation_type_enum = postgresql.ENUM(
        'purge', 'notification', 'policy_check',
        name='schedule_operation_type_enum'
    )
    schedule_operation_type_enum.create(op.get_bind())
    
    schedule_status_enum = postgresql.ENUM(
        'pending', 'in_progress', 'completed', 'failed', 'cancelled',
        name='schedule_status_enum'
    )
    schedule_status_enum.create(op.get_bind())
    
    notification_type_enum = postgresql.ENUM(
        'purge_warning', 'purge_reminder', 'purge_final_notice', 'purge_completed',
        name='notification_type_enum'
    )
    notification_type_enum.create(op.get_bind())
    
    delivery_status_enum = postgresql.ENUM(
        'pending', 'sent', 'delivered', 'failed', 'cancelled',
        name='delivery_status_enum'
    )
    delivery_status_enum.create(op.get_bind())
    
    # Create soft_deletion_policies table
    op.create_table(
        'soft_deletion_policies',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('entity_type', sa.String(length=100), nullable=False),
        sa.Column('retention_period_days', sa.Integer(), nullable=False),
        sa.Column('auto_purge_enabled', sa.Boolean(), nullable=False, default=True),
        sa.Column('cascade_deletion', sa.Boolean(), nullable=False, default=True),
        sa.Column('notification_enabled', sa.Boolean(), nullable=False, default=True),
        sa.Column('notification_days_before', sa.Integer(), nullable=False, default=7),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.CheckConstraint('retention_period_days > 0', name='ck_retention_period_positive'),
        sa.CheckConstraint('notification_days_before >= 0', name='ck_notification_days_non_negative'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('entity_type', name='uq_entity_type_policy')
    )
    
    # Create soft_deletion_audits table
    op.create_table(
        'soft_deletion_audits',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('entity_type', sa.String(length=100), nullable=False),
        sa.Column('entity_id', sa.Integer(), nullable=False),
        sa.Column('operation_type', operation_type_enum, nullable=False),
        sa.Column('performed_by', sa.Integer(), nullable=True),
        sa.Column('reason', sa.Text(), nullable=True),
        sa.Column('cascade_triggered', sa.Boolean(), nullable=False, default=False),
        sa.Column('affected_entities_count', sa.Integer(), nullable=False, default=1),
        sa.Column('policy_id', sa.Integer(), nullable=True),
        sa.Column('retention_period_used', sa.Integer(), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.String(length=500), nullable=True),
        sa.Column('operation_time', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.CheckConstraint('affected_entities_count > 0', name='ck_affected_entities_positive'),
        sa.ForeignKeyConstraint(['performed_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['policy_id'], ['soft_deletion_policies.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create soft_deletion_schedules table
    op.create_table(
        'soft_deletion_schedules',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('entity_type', sa.String(length=100), nullable=False),
        sa.Column('entity_id', sa.Integer(), nullable=False),
        sa.Column('operation_type', schedule_operation_type_enum, nullable=False),
        sa.Column('scheduled_for', sa.DateTime(), nullable=False),
        sa.Column('policy_id', sa.Integer(), nullable=False),
        sa.Column('status', schedule_status_enum, nullable=False, default='pending'),
        sa.Column('executed_at', sa.DateTime(), nullable=True),
        sa.Column('execution_result', sa.Text(), nullable=True),
        sa.Column('retry_count', sa.Integer(), nullable=False, default=0),
        sa.Column('max_retries', sa.Integer(), nullable=False, default=3),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.CheckConstraint('retry_count >= 0', name='ck_retry_count_non_negative'),
        sa.CheckConstraint('max_retries >= 0', name='ck_max_retries_non_negative'),
        sa.CheckConstraint('retry_count <= max_retries', name='ck_retry_count_within_max'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['policy_id'], ['soft_deletion_policies.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create soft_deletion_notifications table
    op.create_table(
        'soft_deletion_notifications',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('entity_type', sa.String(length=100), nullable=False),
        sa.Column('entity_id', sa.Integer(), nullable=False),
        sa.Column('recipient_id', sa.Integer(), nullable=False),
        sa.Column('notification_type', notification_type_enum, nullable=False),
        sa.Column('scheduled_purge_date', sa.DateTime(), nullable=False),
        sa.Column('days_until_purge', sa.Integer(), nullable=False),
        sa.Column('sent_at', sa.DateTime(), nullable=True),
        sa.Column('delivery_status', delivery_status_enum, nullable=False, default='pending'),
        sa.Column('delivery_method', sa.String(length=50), nullable=True),
        sa.Column('subject', sa.String(length=255), nullable=True),
        sa.Column('message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.CheckConstraint('days_until_purge >= 0', name='ck_days_until_purge_non_negative'),
        sa.ForeignKeyConstraint(['recipient_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for performance
    op.create_index('idx_sdp_entity_type', 'soft_deletion_policies', ['entity_type'])
    
    op.create_index('idx_sda_entity', 'soft_deletion_audits', ['entity_type', 'entity_id'])
    op.create_index('idx_sda_operation', 'soft_deletion_audits', ['operation_type'])
    op.create_index('idx_sda_performed_by', 'soft_deletion_audits', ['performed_by'])
    op.create_index('idx_sda_operation_time', 'soft_deletion_audits', ['operation_time'])
    op.create_index('idx_sda_policy', 'soft_deletion_audits', ['policy_id'])
    
    op.create_index('idx_sds_entity', 'soft_deletion_schedules', ['entity_type', 'entity_id'])
    op.create_index('idx_sds_scheduled_for', 'soft_deletion_schedules', ['scheduled_for'])
    op.create_index('idx_sds_status', 'soft_deletion_schedules', ['status'])
    op.create_index('idx_sds_policy', 'soft_deletion_schedules', ['policy_id'])
    
    op.create_index('idx_sdn_recipient', 'soft_deletion_notifications', ['recipient_id'])
    op.create_index('idx_sdn_entity', 'soft_deletion_notifications', ['entity_type', 'entity_id'])
    op.create_index('idx_sdn_delivery_status', 'soft_deletion_notifications', ['delivery_status'])
    op.create_index('idx_sdn_purge_date', 'soft_deletion_notifications', ['scheduled_purge_date'])
    
    # Insert default policies for common entity types
    op.execute("""
        INSERT INTO soft_deletion_policies (entity_type, retention_period_days, auto_purge_enabled, cascade_deletion, notification_enabled, notification_days_before, description)
        VALUES
        ('campaign', 90, true, true, true, 7, 'Default policy for campaigns - 90 day retention'),
        ('testcase', 60, true, false, true, 7, 'Default policy for test cases - 60 day retention'),
        ('assessment', 180, true, true, true, 14, 'Default policy for assessments - 180 day retention'),
        ('environment', 30, true, false, true, 3, 'Default policy for environments - 30 day retention'),
        ('user_session', 7, true, false, false, 0, 'Default policy for user sessions - 7 day retention'),
        ('audit_log', 365, false, false, false, 0, 'Default policy for audit logs - 365 day retention, no auto-purge'),
        ('error_log', 90, true, false, false, 0, 'Default policy for error logs - 90 day retention')
    """)


def downgrade():
    """Drop advanced soft deletion framework tables."""
    
    # Drop indexes
    op.drop_index('idx_sdn_purge_date', 'soft_deletion_notifications')
    op.drop_index('idx_sdn_delivery_status', 'soft_deletion_notifications')
    op.drop_index('idx_sdn_entity', 'soft_deletion_notifications')
    op.drop_index('idx_sdn_recipient', 'soft_deletion_notifications')
    
    op.drop_index('idx_sds_policy', 'soft_deletion_schedules')
    op.drop_index('idx_sds_status', 'soft_deletion_schedules')
    op.drop_index('idx_sds_scheduled_for', 'soft_deletion_schedules')
    op.drop_index('idx_sds_entity', 'soft_deletion_schedules')
    
    op.drop_index('idx_sda_policy', 'soft_deletion_audits')
    op.drop_index('idx_sda_operation_time', 'soft_deletion_audits')
    op.drop_index('idx_sda_performed_by', 'soft_deletion_audits')
    op.drop_index('idx_sda_operation', 'soft_deletion_audits')
    op.drop_index('idx_sda_entity', 'soft_deletion_audits')
    
    op.drop_index('idx_sdp_entity_type', 'soft_deletion_policies')
    
    # Drop tables
    op.drop_table('soft_deletion_notifications')
    op.drop_table('soft_deletion_schedules')
    op.drop_table('soft_deletion_audits')
    op.drop_table('soft_deletion_policies')
    
    # Drop enums
    delivery_status_enum = postgresql.ENUM(name='delivery_status_enum')
    delivery_status_enum.drop(op.get_bind())
    
    notification_type_enum = postgresql.ENUM(name='notification_type_enum')
    notification_type_enum.drop(op.get_bind())
    
    schedule_status_enum = postgresql.ENUM(name='schedule_status_enum')
    schedule_status_enum.drop(op.get_bind())
    
    schedule_operation_type_enum = postgresql.ENUM(name='schedule_operation_type_enum')
    schedule_operation_type_enum.drop(op.get_bind())
    
    operation_type_enum = postgresql.ENUM(name='operation_type_enum')
    operation_type_enum.drop(op.get_bind())
