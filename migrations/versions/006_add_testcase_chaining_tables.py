"""Add enhanced testcase chaining and sequencing tables

Revision ID: 006_add_testcase_chaining_tables
Revises: 005_add_advanced_soft_deletion_tables
Create Date: 2025-01-15 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '006_add_testcase_chaining_tables'
down_revision = '005_add_advanced_soft_deletion_tables'
branch_labels = None
depends_on = None


def upgrade():
    """Create enhanced testcase chaining and sequencing tables."""
    
    # Create enums
    chain_status_enum = postgresql.ENUM(
        'draft', 'active', 'completed', 'archived', 'paused',
        name='chain_status_enum'
    )
    chain_status_enum.create(op.get_bind())
    
    node_type_enum = postgresql.ENUM(
        'start', 'standard', 'conditional', 'parallel', 'end',
        name='node_type_enum'
    )
    node_type_enum.create(op.get_bind())
    
    edge_type_enum = postgresql.ENUM(
        'standard', 'success_path', 'failure_path', 'conditional', 'parallel',
        name='edge_type_enum'
    )
    edge_type_enum.create(op.get_bind())
    
    execution_status_enum = postgresql.ENUM(
        'pending', 'running', 'completed', 'failed', 'aborted', 'timeout',
        name='execution_status_enum'
    )
    execution_status_enum.create(op.get_bind())
    
    node_execution_status_enum = postgresql.ENUM(
        'pending', 'running', 'completed', 'failed', 'skipped', 'timeout', 'aborted',
        name='node_execution_status_enum'
    )
    node_execution_status_enum.create(op.get_bind())
    
    condition_type_enum = postgresql.ENUM(
        'precondition', 'postcondition', 'cleanup',
        name='condition_type_enum'
    )
    condition_type_enum.create(op.get_bind())
    
    # Create testcase_chains table
    op.create_table(
        'testcase_chains',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('status', chain_status_enum, nullable=False, default='draft'),
        sa.Column('chain_type', sa.String(length=50), nullable=False, default='sequential'),
        sa.Column('max_execution_time_minutes', sa.Integer(), nullable=False, default=60),
        sa.Column('retry_on_failure', sa.Boolean(), nullable=False, default=False),
        sa.Column('auto_cleanup', sa.Boolean(), nullable=False, default=True),
        sa.Column('created_by', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('deleted_at', sa.DateTime(), nullable=True),
        sa.Column('version', sa.Integer(), nullable=False, default=1),
        sa.CheckConstraint('max_execution_time_minutes > 0', name='ck_max_execution_time_positive'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create testcase_chain_nodes table
    op.create_table(
        'testcase_chain_nodes',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('chain_id', sa.Integer(), nullable=False),
        sa.Column('testcase_id', sa.Integer(), nullable=False),
        sa.Column('node_type', node_type_enum, nullable=False, default='standard'),
        sa.Column('execution_order', sa.Integer(), nullable=False, default=0),
        sa.Column('position_x', sa.Float(), nullable=False, default=0.0),
        sa.Column('position_y', sa.Float(), nullable=False, default=0.0),
        sa.Column('condition_expression', sa.Text(), nullable=True),
        sa.Column('timeout_minutes', sa.Integer(), nullable=False, default=30),
        sa.Column('retry_count', sa.Integer(), nullable=False, default=0),
        sa.Column('continue_on_failure', sa.Boolean(), nullable=False, default=False),
        sa.Column('required_for_completion', sa.Boolean(), nullable=False, default=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('deleted_at', sa.DateTime(), nullable=True),
        sa.Column('version', sa.Integer(), nullable=False, default=1),
        sa.CheckConstraint('timeout_minutes > 0', name='ck_timeout_minutes_positive'),
        sa.CheckConstraint('retry_count >= 0', name='ck_retry_count_non_negative'),
        sa.ForeignKeyConstraint(['chain_id'], ['testcase_chains.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['testcase_id'], ['test_cases.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('chain_id', 'testcase_id', name='uq_chain_testcase')
    )
    
    # Create testcase_chain_edges table
    op.create_table(
        'testcase_chain_edges',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('source_node_id', sa.Integer(), nullable=False),
        sa.Column('target_node_id', sa.Integer(), nullable=False),
        sa.Column('edge_type', edge_type_enum, nullable=False, default='standard'),
        sa.Column('condition', sa.Text(), nullable=True),
        sa.Column('weight', sa.Integer(), nullable=False, default=1),
        sa.Column('label', sa.String(length=100), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('deleted_at', sa.DateTime(), nullable=True),
        sa.Column('version', sa.Integer(), nullable=False, default=1),
        sa.CheckConstraint('source_node_id != target_node_id', name='ck_no_self_loops'),
        sa.CheckConstraint('weight > 0', name='ck_weight_positive'),
        sa.ForeignKeyConstraint(['source_node_id'], ['testcase_chain_nodes.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['target_node_id'], ['testcase_chain_nodes.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('source_node_id', 'target_node_id', name='uq_source_target_edge')
    )
    
    # Create chain_executions table
    op.create_table(
        'chain_executions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('chain_id', sa.Integer(), nullable=False),
        sa.Column('started_by', sa.Integer(), nullable=False),
        sa.Column('start_time', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('end_time', sa.DateTime(), nullable=True),
        sa.Column('status', execution_status_enum, nullable=False, default='pending'),
        sa.Column('execution_context', sa.JSON(), nullable=True),
        sa.Column('result_summary', sa.JSON(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('total_nodes', sa.Integer(), nullable=False, default=0),
        sa.Column('completed_nodes', sa.Integer(), nullable=False, default=0),
        sa.Column('failed_nodes', sa.Integer(), nullable=False, default=0),
        sa.Column('skipped_nodes', sa.Integer(), nullable=False, default=0),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('deleted_at', sa.DateTime(), nullable=True),
        sa.Column('version', sa.Integer(), nullable=False, default=1),
        sa.CheckConstraint('total_nodes >= 0', name='ck_total_nodes_non_negative'),
        sa.CheckConstraint('completed_nodes >= 0', name='ck_completed_nodes_non_negative'),
        sa.CheckConstraint('failed_nodes >= 0', name='ck_failed_nodes_non_negative'),
        sa.CheckConstraint('skipped_nodes >= 0', name='ck_skipped_nodes_non_negative'),
        sa.ForeignKeyConstraint(['chain_id'], ['testcase_chains.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['started_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create node_executions table
    op.create_table(
        'node_executions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('chain_execution_id', sa.Integer(), nullable=False),
        sa.Column('node_id', sa.Integer(), nullable=False),
        sa.Column('start_time', sa.DateTime(), nullable=True),
        sa.Column('end_time', sa.DateTime(), nullable=True),
        sa.Column('status', node_execution_status_enum, nullable=False, default='pending'),
        sa.Column('result_data', sa.JSON(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('output_logs', sa.Text(), nullable=True),
        sa.Column('attempt_number', sa.Integer(), nullable=False, default=1),
        sa.Column('max_attempts', sa.Integer(), nullable=False, default=1),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('deleted_at', sa.DateTime(), nullable=True),
        sa.Column('version', sa.Integer(), nullable=False, default=1),
        sa.CheckConstraint('attempt_number > 0', name='ck_attempt_number_positive'),
        sa.CheckConstraint('max_attempts > 0', name='ck_max_attempts_positive'),
        sa.CheckConstraint('attempt_number <= max_attempts', name='ck_attempt_within_max'),
        sa.ForeignKeyConstraint(['chain_execution_id'], ['chain_executions.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['node_id'], ['testcase_chain_nodes.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create testcase_conditions table
    op.create_table(
        'testcase_conditions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('testcase_id', sa.Integer(), nullable=False),
        sa.Column('condition_type', condition_type_enum, nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('validation_script', sa.Text(), nullable=True),
        sa.Column('validation_type', sa.String(length=50), nullable=False, default='script'),
        sa.Column('required', sa.Boolean(), nullable=False, default=True),
        sa.Column('execution_order', sa.Integer(), nullable=False, default=0),
        sa.Column('timeout_seconds', sa.Integer(), nullable=False, default=30),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('deleted_at', sa.DateTime(), nullable=True),
        sa.Column('version', sa.Integer(), nullable=False, default=1),
        sa.CheckConstraint('timeout_seconds > 0', name='ck_timeout_seconds_positive'),
        sa.ForeignKeyConstraint(['testcase_id'], ['test_cases.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for performance
    op.create_index('idx_tc_name', 'testcase_chains', ['name'])
    op.create_index('idx_tc_status', 'testcase_chains', ['status'])
    op.create_index('idx_tc_created_by', 'testcase_chains', ['created_by'])
    op.create_index('idx_tc_created_at', 'testcase_chains', ['created_at'])
    
    op.create_index('idx_tcn_chain', 'testcase_chain_nodes', ['chain_id'])
    op.create_index('idx_tcn_testcase', 'testcase_chain_nodes', ['testcase_id'])
    op.create_index('idx_tcn_execution_order', 'testcase_chain_nodes', ['execution_order'])
    op.create_index('idx_tcn_node_type', 'testcase_chain_nodes', ['node_type'])
    
    op.create_index('idx_tce_source', 'testcase_chain_edges', ['source_node_id'])
    op.create_index('idx_tce_target', 'testcase_chain_edges', ['target_node_id'])
    op.create_index('idx_tce_edge_type', 'testcase_chain_edges', ['edge_type'])
    
    op.create_index('idx_ce_chain', 'chain_executions', ['chain_id'])
    op.create_index('idx_ce_started_by', 'chain_executions', ['started_by'])
    op.create_index('idx_ce_status', 'chain_executions', ['status'])
    op.create_index('idx_ce_start_time', 'chain_executions', ['start_time'])
    
    op.create_index('idx_ne_chain_execution', 'node_executions', ['chain_execution_id'])
    op.create_index('idx_ne_node', 'node_executions', ['node_id'])
    op.create_index('idx_ne_status', 'node_executions', ['status'])
    
    op.create_index('idx_tco_testcase', 'testcase_conditions', ['testcase_id'])
    op.create_index('idx_tco_condition_type', 'testcase_conditions', ['condition_type'])
    op.create_index('idx_tco_execution_order', 'testcase_conditions', ['execution_order'])


def downgrade():
    """Drop enhanced testcase chaining and sequencing tables."""
    
    # Drop indexes
    op.drop_index('idx_tco_execution_order', 'testcase_conditions')
    op.drop_index('idx_tco_condition_type', 'testcase_conditions')
    op.drop_index('idx_tco_testcase', 'testcase_conditions')
    
    op.drop_index('idx_ne_status', 'node_executions')
    op.drop_index('idx_ne_node', 'node_executions')
    op.drop_index('idx_ne_chain_execution', 'node_executions')
    
    op.drop_index('idx_ce_start_time', 'chain_executions')
    op.drop_index('idx_ce_status', 'chain_executions')
    op.drop_index('idx_ce_started_by', 'chain_executions')
    op.drop_index('idx_ce_chain', 'chain_executions')
    
    op.drop_index('idx_tce_edge_type', 'testcase_chain_edges')
    op.drop_index('idx_tce_target', 'testcase_chain_edges')
    op.drop_index('idx_tce_source', 'testcase_chain_edges')
    
    op.drop_index('idx_tcn_node_type', 'testcase_chain_nodes')
    op.drop_index('idx_tcn_execution_order', 'testcase_chain_nodes')
    op.drop_index('idx_tcn_testcase', 'testcase_chain_nodes')
    op.drop_index('idx_tcn_chain', 'testcase_chain_nodes')
    
    op.drop_index('idx_tc_created_at', 'testcase_chains')
    op.drop_index('idx_tc_created_by', 'testcase_chains')
    op.drop_index('idx_tc_status', 'testcase_chains')
    op.drop_index('idx_tc_name', 'testcase_chains')
    
    # Drop tables
    op.drop_table('testcase_conditions')
    op.drop_table('node_executions')
    op.drop_table('chain_executions')
    op.drop_table('testcase_chain_edges')
    op.drop_table('testcase_chain_nodes')
    op.drop_table('testcase_chains')
    
    # Drop enums
    condition_type_enum = postgresql.ENUM(name='condition_type_enum')
    condition_type_enum.drop(op.get_bind())
    
    node_execution_status_enum = postgresql.ENUM(name='node_execution_status_enum')
    node_execution_status_enum.drop(op.get_bind())
    
    execution_status_enum = postgresql.ENUM(name='execution_status_enum')
    execution_status_enum.drop(op.get_bind())
    
    edge_type_enum = postgresql.ENUM(name='edge_type_enum')
    edge_type_enum.drop(op.get_bind())
    
    node_type_enum = postgresql.ENUM(name='node_type_enum')
    node_type_enum.drop(op.get_bind())
    
    chain_status_enum = postgresql.ENUM(name='chain_status_enum')
    chain_status_enum.drop(op.get_bind())
