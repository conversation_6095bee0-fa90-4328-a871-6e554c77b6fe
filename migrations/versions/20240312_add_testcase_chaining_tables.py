"""Add testcase chaining tables

Revision ID: a7c5e9b2d4f3
Revises: previous_revision_id
Create Date: 2024-03-12 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB


# revision identifiers, used by Alembic.
revision = 'a7c5e9b2d4f3'
down_revision = 'previous_revision_id'  # Update this with the actual previous revision ID
branch_labels = None
depends_on = None


def upgrade():
    # Create testcase_chains table
    op.create_table(
        'testcase_chains',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_by', sa.Integer(), sa.<PERSON>ey('flask_users.id'), nullable=True),
        sa.Column('status', sa.String(20), server_default='draft', nullable=False),
        sa.Column('created_time', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_time', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), 
                  onupdate=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('deleted_time', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_tc_created_by', 'testcase_chains', ['created_by'])
    op.create_index('idx_tc_status', 'testcase_chains', ['status'])
    op.create_index('idx_tc_deleted_time', 'testcase_chains', ['deleted_time'])

    # Create testcase_chain_nodes table
    op.create_table(
        'testcase_chain_nodes',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('chain_id', sa.Integer(), sa.ForeignKey('testcase_chains.id', ondelete='CASCADE'), nullable=False),
        sa.Column('testcase_id', sa.Integer(), sa.ForeignKey('test_cases.id', ondelete='CASCADE'), nullable=False),
        sa.Column('node_type', sa.String(20), server_default='standard', nullable=False),
        sa.Column('position_x', sa.Float(), default=0, nullable=False),
        sa.Column('position_y', sa.Float(), default=0, nullable=False),
        sa.Column('execution_order', sa.Integer(), default=0, nullable=False),
        sa.Column('condition_expression', sa.Text(), nullable=True),
        sa.Column('created_time', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_time', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), 
                  onupdate=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('deleted_time', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_tcn_chain_id', 'testcase_chain_nodes', ['chain_id'])
    op.create_index('idx_tcn_testcase_id', 'testcase_chain_nodes', ['testcase_id'])
    op.create_index('idx_tcn_node_type', 'testcase_chain_nodes', ['node_type'])
    op.create_index('idx_tcn_deleted_time', 'testcase_chain_nodes', ['deleted_time'])

    # Create testcase_chain_edges table
    op.create_table(
        'testcase_chain_edges',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('source_node_id', sa.Integer(), sa.ForeignKey('testcase_chain_nodes.id', ondelete='CASCADE'), nullable=False),
        sa.Column('target_node_id', sa.Integer(), sa.ForeignKey('testcase_chain_nodes.id', ondelete='CASCADE'), nullable=False),
        sa.Column('edge_type', sa.String(20), server_default='standard', nullable=False),
        sa.Column('condition', sa.Text(), nullable=True),
        sa.Column('created_time', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_time', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), 
                  onupdate=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('deleted_time', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_tce_source_node_id', 'testcase_chain_edges', ['source_node_id'])
    op.create_index('idx_tce_target_node_id', 'testcase_chain_edges', ['target_node_id'])
    op.create_index('idx_tce_edge_type', 'testcase_chain_edges', ['edge_type'])
    op.create_index('idx_tce_deleted_time', 'testcase_chain_edges', ['deleted_time'])

    # Create chain_executions table
    op.create_table(
        'chain_executions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('chain_id', sa.Integer(), sa.ForeignKey('testcase_chains.id', ondelete='CASCADE'), nullable=False),
        sa.Column('started_by', sa.Integer(), sa.ForeignKey('flask_users.id'), nullable=True),
        sa.Column('start_time', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('end_time', sa.DateTime(), nullable=True),
        sa.Column('status', sa.String(20), server_default='running', nullable=False),
        sa.Column('created_time', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_time', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), 
                  onupdate=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('deleted_time', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_ce_chain_id', 'chain_executions', ['chain_id'])
    op.create_index('idx_ce_started_by', 'chain_executions', ['started_by'])
    op.create_index('idx_ce_status', 'chain_executions', ['status'])
    op.create_index('idx_ce_start_time', 'chain_executions', ['start_time'])
    op.create_index('idx_ce_deleted_time', 'chain_executions', ['deleted_time'])

    # Create node_executions table
    op.create_table(
        'node_executions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('chain_execution_id', sa.Integer(), sa.ForeignKey('chain_executions.id', ondelete='CASCADE'), nullable=False),
        sa.Column('node_id', sa.Integer(), sa.ForeignKey('testcase_chain_nodes.id', ondelete='CASCADE'), nullable=False),
        sa.Column('start_time', sa.DateTime(), nullable=True),
        sa.Column('end_time', sa.DateTime(), nullable=True),
        sa.Column('status', sa.String(20), server_default='pending', nullable=False),
        sa.Column('result_data', JSONB, nullable=True),
        sa.Column('created_time', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_time', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), 
                  onupdate=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('deleted_time', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_ne_chain_execution_id', 'node_executions', ['chain_execution_id'])
    op.create_index('idx_ne_node_id', 'node_executions', ['node_id'])
    op.create_index('idx_ne_status', 'node_executions', ['status'])
    op.create_index('idx_ne_deleted_time', 'node_executions', ['deleted_time'])

    # Create testcase_conditions table
    op.create_table(
        'testcase_conditions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('testcase_id', sa.Integer(), sa.ForeignKey('test_cases.id', ondelete='CASCADE'), nullable=False),
        sa.Column('condition_type', sa.String(20), nullable=False),
        sa.Column('name', sa.String(100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('validation_script', sa.Text(), nullable=True),
        sa.Column('required', sa.Boolean(), default=True, nullable=False),
        sa.Column('created_time', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_time', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), 
                  onupdate=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('deleted_time', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_tc_testcase_id', 'testcase_conditions', ['testcase_id'])
    op.create_index('idx_tc_condition_type', 'testcase_conditions', ['condition_type'])
    op.create_index('idx_tc_deleted_time', 'testcase_conditions', ['deleted_time'])


def downgrade():
    # Drop tables in reverse order of creation to respect foreign key constraints
    op.drop_table('testcase_conditions')
    op.drop_table('node_executions')
    op.drop_table('chain_executions')
    op.drop_table('testcase_chain_edges')
    op.drop_table('testcase_chain_nodes')
    op.drop_table('testcase_chains') 