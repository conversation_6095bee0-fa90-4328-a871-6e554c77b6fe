
"""
Add MFA fields to User model.

Revision ID: add_mfa_fields
Revises: 
Create Date: 2023-10-30 12:00:00.000000
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_mfa_fields'
down_revision = None  # Update this to the previous migration ID
branch_labels = None
depends_on = None


def upgrade():
    """Add MFA columns to users table."""
    op.add_column('users', sa.Column('mfa_secret', sa.String(), nullable=True))
    op.add_column('users', sa.Column('mfa_enabled', sa.<PERSON>(), nullable=True, server_default='false'))


def downgrade():
    """Remove MFA columns from users table."""
    op.drop_column('users', 'mfa_enabled')
    op.drop_column('users', 'mfa_secret')
