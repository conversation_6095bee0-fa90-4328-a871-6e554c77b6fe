"""
Remove circular dependencies between Assessment and Campaign models.

Revision ID: 20240319_remove_circular
Revises: 20240316_hierarchical
Create Date: 2024-03-19 10:00:00.000000
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic
revision = '20240319_remove_circular'
down_revision = '20240316_hierarchical'
branch_labels = None
depends_on = None


def upgrade():
    # Step 1: Create a temporary table to store assessment-campaign relationships
    op.execute("""
    CREATE TEMPORARY TABLE assessment_campaign_temp AS
    SELECT 
        a.id AS assessment_id, 
        a.campaign_id 
    FROM 
        assessments a 
    WHERE 
        a.campaign_id IS NOT NULL;
    """)
    
    # Step 2: Update campaigns to link to their assessments based on the current relationships
    op.execute("""
    UPDATE campaigns c
    SET assessment_id = t.assessment_id
    FROM assessment_campaign_temp t
    WHERE c.id = t.campaign_id
    AND c.assessment_id IS NULL;
    """)
    
    # Step 3: Drop the foreign key constraint from assessments to campaigns if it exists
    try:
        op.drop_constraint("assessments_campaign_id_fkey", "assessments", type_="foreignkey")
    except:
        # The constraint might have a different name or might not exist
        pass
    
    # Step 4: Drop the campaign_id column from assessments
    op.drop_column("assessments", "campaign_id")
    
    # Step 5: Drop the temporary table
    op.execute("DROP TABLE IF EXISTS assessment_campaign_temp;")


def downgrade():
    # Add campaign_id back to assessments table
    op.add_column('assessments', sa.Column('campaign_id', sa.Integer(), nullable=True))
    
    # Add foreign key constraint
    op.create_foreign_key(
        "assessments_campaign_id_fkey",
        "assessments",
        "campaigns",
        ["campaign_id"],
        ["id"]
    )
    
    # Update campaign_id in assessments based on assessment_id in campaigns
    op.execute("""
    UPDATE assessments a
    SET campaign_id = c.id
    FROM campaigns c
    WHERE c.assessment_id = a.id
    AND a.campaign_id IS NULL;
    """) 