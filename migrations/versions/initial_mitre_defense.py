"""Initial MITRE Defense tables

Revision ID: initial_mitre_defense
Create Date: 2024-02-25 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'initial_mitre_defense'
down_revision = None
branch_labels = None
depends_on = None

def upgrade() -> None:
    # Create mitre_versions table for version tracking
    op.create_table(
        'mitre_versions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('version', sa.String(50), nullable=False),
        sa.Column('name', sa.String(100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('import_date', sa.DateTime(), nullable=True),
        sa.Column('is_current', sa.<PERSON>(), default=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('version')
    )

    # Create mitre_tactics table
    op.create_table(
        'mitre_tactics',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('external_id', sa.String(50), nullable=False),
        sa.Column('name', sa.String(100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('version_id', sa.Integer(), nullable=False),
        sa.Column('created', sa.DateTime(), nullable=True),
        sa.Column('modified', sa.DateTime(), nullable=True),
        sa.Column('revoked', sa.Boolean(), default=False),
        sa.Column('deprecated', sa.Boolean(), default=False),
        sa.ForeignKeyConstraint(['version_id'], ['mitre_versions.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('external_id', 'version_id')
    )

    # Create mitre_techniques table
    op.create_table(
        'mitre_techniques',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('external_id', sa.String(50), nullable=False),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('version_id', sa.Integer(), nullable=False),
        sa.Column('tactic_id', sa.Integer(), nullable=True),
        sa.Column('created', sa.DateTime(), nullable=True),
        sa.Column('modified', sa.DateTime(), nullable=True),
        sa.Column('revoked', sa.Boolean(), default=False),
        sa.Column('deprecated', sa.Boolean(), default=False),
        sa.ForeignKeyConstraint(['version_id'], ['mitre_versions.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['tactic_id'], ['mitre_tactics.id'], ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('external_id', 'version_id')
    )

    # Create mitre_technique_relationships table
    op.create_table(
        'mitre_technique_relationships',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('source_id', sa.Integer(), nullable=False),
        sa.Column('target_id', sa.Integer(), nullable=False),
        sa.Column('relationship_type', sa.String(50), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['source_id'], ['mitre_techniques.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['target_id'], ['mitre_techniques.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )

def downgrade() -> None:
    op.drop_table('mitre_technique_relationships')
    op.drop_table('mitre_techniques')
    op.drop_table('mitre_tactics')
    op.drop_table('mitre_versions')