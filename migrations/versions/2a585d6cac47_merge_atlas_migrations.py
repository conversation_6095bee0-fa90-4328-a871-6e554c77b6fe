"""merge_atlas_migrations

Revision ID: 2a585d6cac47
Revises: 20250307_atlas_tables, add_session_management
Create Date: 2025-03-07 09:46:34.627762

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2a585d6cac47'
down_revision = ('20250307_atlas_tables', 'add_session_management')
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Just a merge migration, no schema changes needed
    pass


def downgrade() -> None:
    pass